# Syrix DAO

This module contains the Data Access Object (DAO) interfaces for the Syrix security platform.

## Design Principles

This DAO module follows these principles:

1. **Interface-based Design**: All DAOs are defined as interfaces, with implementations in separate projects/modules.
2. **Separation of Concerns**: DAO interfaces are separate from database-specific implementations.
3. **Framework Independence**: The DAO layer is not dependent on any specific framework.
4. **Custom Exception Handling**: All exceptions are part of the Syrix codebase for better control and independence.
5. **Factory Pattern**: DAO instances are created through a DaoFactory to abstract creation details.

## Module Structure

- **Base DAO Interface**: Generic interface for common CRUD operations
- **Entity-specific DAO Interfaces**: Specialized interfaces for each entity type
- **Custom Exception Package**: Domain-specific exceptions for data access operations

## Usage

The DAO layer is designed to be used through the DaoFactory:

```java
// Get a DAO factory (implementation specific)
DaoFactory daoFactory = /* obtain a factory implementation */;

// Get a specific DAO
CompanyDao clientDao = daoFactory.getCompanyDao();

// Use the DAO
Optional<Company> client = clientDao.findById(companyId);
List<Company> activeCompanies = clientDao.findAllActive();

// Close resources when done
daoFactory.close();
```

## Available DAOs

The following DAOs are available:

- `CompanyDao`: CRUD operations for Company entities
- `AlertDao`: CRUD operations for Alert entities
- `ConnectionInfoDao`: CRUD operations for ConnectionInfo entities
- `UserProfileDao`: CRUD operations for UserProfile entities
- `AuditLogDao`: CRUD operations for AuditLog entities
- `SystemLogEntryDao`: CRUD operations for SystemLogEntry entities
- `SecurityMetricsDao`: CRUD operations for SecurityMetrics entities

## Custom Exceptions

The DAO layer uses custom exceptions for specific error scenarios:

- `DataAccessException`: Base exception for all data access errors
- `EntityNotFoundException`: Thrown when an entity is not found
- `ConstraintViolationException`: For constraint violations
- `DatabaseConnectionException`: For connection issues
- `UpdateFailedException`: For update failures
- `TransactionException`: For transaction-related errors

## Implementation Options

Implementations for different database backends should be created in separate modules. Common options might include:

- **JDBC**: SQL-based implementation using plain JDBC
- **JPA**: SQL-based implementation using JPA (Hibernate)
- **MongoDB**: NoSQL-based implementation for MongoDB
- **Redis**: In-memory database implementation
- **DynamoDB**: AWS-specific NoSQL implementation

## Transaction Management

Each implementation should handle transactions appropriately for its database technology.
