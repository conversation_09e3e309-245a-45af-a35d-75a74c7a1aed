package io.syrix.exceptions;

import java.time.Duration;

/**
 * Error backoff sleep strategy
 *
 */
public interface BackoffFunction {
	/**
	 * Returns next sleep interval or null if backoff should be terminated
	 *
	 * @param attempt zero based retry attempt number
	 * @param e exception thrown on this attempt
	 * @return sleep duration or null if backoff should be terminated
	 */
	Duration delay(int attempt, Exception e);
}
