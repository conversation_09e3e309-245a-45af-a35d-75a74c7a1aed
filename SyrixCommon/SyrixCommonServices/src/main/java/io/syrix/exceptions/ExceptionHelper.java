package io.syrix.exceptions;


import io.syrix.Constants;
import io.syrix.function.RunnableWithException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.helpers.MessageFormatter;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;

import static java.util.Objects.requireNonNull;

public class ExceptionHelper {
	private static final Logger logger = LoggerFactory.getLogger(ExceptionHelper.class);
	public static final String FAILED_TO_PROCESS_ROUTINE_ATTEMPT = "Failed to process routine, attempt #{}: {}";

	private final int retries;
	private int counter;
	private final ErrorType errorType;
	private final boolean toSleep;
	private final Set<String> messages = new HashSet<>();

	/**
	 * {@code ExceptionHelper} with default retries count with logging and backoff sleeping
	 */
	public ExceptionHelper() {
		this(Constants.RETRIES, ErrorType.ERROR, true);
	}

	public ExceptionHelper(final int retries, final ErrorType errorType, final boolean toSleep) {
		this.retries = retries;
		this.errorType = errorType;
		this.toSleep = toSleep;
	}

	/**
	 * Creates processor that runs payload in a loop until success.
	 * Call {@link ProcessBuilder#catchAll(Object, String, Object...)} or {@link ProcessBuilder#throwAll()}
	 * to start processing.
	 * Usage example:
	 * <pre>
	 * String result = new ExceptionHelper()
	 *     .process(() -> apiClient.loadCurrentUser())
	 *     .catchOne(UnauthorizedException.class, e -> {
	 *         refreshToken();
	 *         return Optional.empty();
	 *     })
	 *     .throwAll();
	 * </pre>
	 *
	 * This is nearly identical to:
	 * <pre>
	 * String result;
	 * boolean isDone = false;
ExceptionHelper exHelper = new ExceptionHelper(Constants.RETRIES, ErrorType.ERROR, true);
	 * while (!isDone) {
	 *     try {
	 *         result = apiClient.loadCurrentUser();
	 *         isDone = true;
	 *     } catch (UnauthorizedException e) {
	 *         refreshToken();
	 *         exHelper.throwIfZero(e.getMessage(), e);
	 *     } catch (Exception e) {
	 *         exHelper.throwIfZero(e.getMessage(), e);
	 *     }
	 * }
	 * </pre>
	 *
	 * @param payload retryable task
	 * @param <V> return type
	 * @return builder to set up processor
	 */
	public <V> ProcessBuilder<V> process(Callable<V> payload) {
		return new ProcessBuilder<>(payload);
	}

	public ProcessBuilder<Void> process(RunnableWithException payload) {
		return new ProcessBuilder<>(() -> {
			payload.run();
			return null;
		});
	}

	public void throwIfZero(final String msg) {
		throwIfZero(msg, null);
	}

	public void throwIfZero(final String msg, final Throwable exception) {
		incCounterInternal();
		if (exception != null) {
			messages.add(exception.getMessage());
		}
		if (lastTry()) {
			log(msg, exception);
			throw new SyrixException(msg, exception);
		}
		if (toSleep) {
			errorSleep();
		}
	}

	private void log(final String msg, final Throwable e, final Object[] params) {
		String tmpMsg = addAggregatedMessages(msg);
		switch (errorType) {
			case DEBUG -> {
				if (e != null && params != null) logger.debug(tmpMsg, params, e);
				else if (params != null) logger.debug(tmpMsg, params);
				else if (e != null)  logger.debug(tmpMsg, e);
				else logger.debug(tmpMsg);
			}
			case INFO -> {
				if (params != null) logger.info(tmpMsg, params);
				else logger.info(tmpMsg);
			}
			case WARNING -> {
				if (e != null && params != null) logger.warn(tmpMsg, params, e);
				else if (params != null) logger.warn(tmpMsg, params);
				else if (e != null)  logger.warn(tmpMsg, e);
				else logger.warn(tmpMsg);
			}
			default -> {
				if (e != null && params != null) logger.error(tmpMsg, params, e);
				else if (params != null) logger.error(tmpMsg, params);
				else if (e != null)  logger.error(tmpMsg, e);
				else logger.error(tmpMsg);
			}
		}
	}

	// Convenience methods
	private void log(final String msg, final Throwable e) {
		log(msg, e, null);
	}

	private void log(final String msg, final Object[] params) {
		log(msg, null, params);
	}

	private void log(final String msg) {
		log(msg, null, null);
	}

	private String addAggregatedMessages(String msg) {
		StringBuilder builder = new StringBuilder(msg);
		if (messages.size() > 1) {
			builder.append(Constants.NEW_LINE).append("Aggregated errors:").append(Constants.NEW_LINE);
			for (String agrMessage : messages) {
				builder.append(agrMessage).append(Constants.NEW_LINE);
			}

		}
		return builder.toString();
	}

	public void errorSleep() {
		errorSleep(0L);
	}

	public void errorSleep(long additionalSleepTime) {
		final double sleepPower = Math.min(counter, 5);
		final long delay = (long) (ThreadLocalRandom.current().nextDouble() * (Math.pow(4, sleepPower) * 100L))
			+ additionalSleepTime;
		customErrorSleep(delay);
	}

	/** IMPORTANT!!! recommended to use when you are confident that sleep time enough
	 * @param delay time to sleep
	 */
	public void customErrorSleep(long delay) {
		try {
			Thread.sleep(delay);
		} catch (final InterruptedException e) {
			Thread.currentThread().interrupt();
			logger.warn("Sleep interrupted", e);
		}
	}

	public boolean lastTry() {
		return counter >= retries;
	}

	public void resetRetriesCounter() {
		messages.clear();
		counter = 0;
	}

	public boolean logIfZero(final String msg, final Throwable e) {
		incCounterInternal();
		if (e != null) {
			messages.add(e.getMessage());
		}
		if (lastTry()) {
			log(msg, e);
			return true;
		}
		if (toSleep) {
			errorSleep();
		}
		return false;
	}

	public boolean logIfZero(final String msg) {
		return logIfZero(msg, (Object[]) null);
	}

	public boolean logIfZero(final String msg, Object... params) {
		incCounterInternal();
		if (lastTry()) {
			log(msg, params);
			return true;
		}
		if (toSleep) {
			errorSleep();
		}
		return false;
	}

	public void warnLogIfZero(final String msg) {
		incCounterInternal();
		if (retries == counter) {
			logger.warn(msg);
		}
	}

	public int getCounter() {
		return counter;
	}

	public int getRetries() {
		return retries;
	}

	public void incCounter() {
		incCounterInternal();
		if (toSleep) {
			errorSleep();
		}
	}

	protected void incCounterInternal() {
		++counter;
	}

	public void throwIfZero(SyrixException e) {
		incCounter();
		if (lastTry()) {
			throw e;
		}
	}

	/**
	 * Builder that construct and run payload task in a loop
	 *
	 * @param <V> task result type
	 */
	public class ProcessBuilder<V> {
		private final Callable<V> payload;
		private final List<CatchClause<V, ?>> catches = new ArrayList<>();
		private BiConsumer<V, Exception> whenComplete = (v, ex) -> {
		};

		private BackoffFunction backoff;
		private Logger logger = ExceptionHelper.logger; // NOSONAR
		private Runnable preAttemptExecution = () -> {
		};

		private ProcessBuilder(Callable<V> payload) {
			this.payload = payload;
			this.backoff = toSleep
				? new DefaultBackoff(retries)
				: new SleeplessBackoff(retries);
		}

		/**
		 * Use specific logger instead of default one
		 *
		 * @return this object
		 */
		public ProcessBuilder<V> withLogger(Logger logger) {
			this.logger = requireNonNull(logger);
			return this;
		}

		/**
		 * Use function to process some behaviour after completion
		 *
		 * @return this object
		 */
		public ProcessBuilder<V> whenComplete(BiConsumer<V, Exception> func) {
			this.whenComplete = requireNonNull(func);
			return this;
		}

		public ProcessBuilder<V> withBackoff(BackoffFunction backoff) {
			this.backoff = requireNonNull(backoff);
			return this;
		}

		/**
		 * Add one catch clause that process one specific type of exception.
		 * Clauses match one by one in the order of addition. Lookup stops after first match.
		 * @param exception type of exception that the clause should process
		 * @param handler exception handler, return {@link Optional#empty()} if processing should be retried
		 * @param <E> type of exception to process
		 * @return this object
		 */
		public <E extends Exception> ProcessBuilder<V> catchOne(Class<E> exception, Function<E, Optional<V>> handler) {
			catches.add(new CatchClause<>(requireNonNull(exception), requireNonNull(handler)));
			return this;
		}

		/**
		 * Catches all exceptions if retry didn't help and log error (like {@code ExceptionHelper.logIfZero()}
		 *
		 * @param defaultResult result returned in case of error (can be null)
		 * @param message message logged in case of error ({@code Logger.error} style)
		 * @param parameters message parameters
		 * @return result of payload execution
		 */
		public V catchAll(V defaultResult, String message, Object... parameters) {
			return catchAllApply(() -> defaultResult, message, parameters);
		}

		public V catchAllVoid(String message, Object... parameters) {
			return catchAllApply(() -> null, message, parameters);
		}

		/**
		 * Catches all exceptions if retry didn't help and log error (like {@code ExceptionHelper.logIfZero()}
		 *
		 * @param defaultResultFunction result returned in case of error
		 * @param message               message logged in case of error ({@code Logger.error} style)
		 * @param parameters            message parameters
		 * @return result of payload execution
		 */
		public V catchAllApply(Supplier<V> defaultResultFunction, String message, Object... parameters) {
			requireNonNull(message);
			requireNonNull(parameters);
			requireNonNull(defaultResultFunction);
			
			String errorMessage = MessageFormatter.arrayFormat(message, parameters).getMessage();
			
			return executeWithRetry(e -> {
				V result = defaultResultFunction.get();
				logFail(errorMessage, e);
				return result;
			});
		}

		/**
		 * Throws all exceptions if retry didn't help (like {@code ExceptionHelper.throwIfZero()})
		 * Wraps non-runtime exceptions with {@link RuntimeException}.
		 * Logs errors before throw.
		 * @return result of payload execution
		 */
		public V throwAll() {
			return throwAll(e -> {
				if (e instanceof RuntimeException) return (RuntimeException) e;
				return new RuntimeException(e.getMessage(), e);
			});
		}

		/**
		 * Throws all exceptions if retry didn't help (like {@code ExceptionHelper.throwIfZero()})
		 *
		 * Wraps all exceptions with {@link RuntimeException} with provided message
		 *
		 * Logs errors before throw.
		 *
		 * @param message message logged in case of error ({@code Logger.error} style)
		 * @param parameters message parameters
		 * @return result of payload execution
		 */
		public V throwAll(String message, Object... parameters) {
			requireNonNull(message);
			requireNonNull(parameters);
			return throwAll(e -> new RuntimeException(MessageFormatter.arrayFormat(message, parameters).getMessage(), e));
		}

		/**
		 * Throws all exceptions if retry didn't help (like {@code ExceptionHelper.throwIfZero()})
		 *
		 * Use custom exception processing to wrap payload errors.
		 *
		 * Logs errors before throw.
		 *
		 * @param exceptionProcessor custom exception processor
		 * @return result of payload execution
		 */
		public V throwAll(Function<Exception, RuntimeException> exceptionProcessor) {
			requireNonNull(exceptionProcessor);
			
			return executeWithRetry(e -> {
				RuntimeException ex = exceptionProcessor.apply(e);
				logFail(ex.getMessage(), e);
				throw ex;
			});
		}

		/**
		 * Core retry logic shared between catchAllApply and throwAll methods.
		 * Executes the payload with retry logic and handles exceptions using the provided failure handler.
		 *
		 * @param failureHandler handles the exception when retries are exhausted
		 * @return result of payload execution or failure handler result
		 */
		private V executeWithRetry(Function<Exception, V> failureHandler) {
			V result = null;
			
			for (int attempt = 0; ; attempt++) {
				preAttemptExecution.run();
				
				try {
					result = payload.call();
					whenComplete.accept(result, null);
					return result;
				} catch (Exception e) {
					// Check if any specific catch clauses handle this exception
					Optional<V> catchResult = processCatches(e);
					if (catchResult.isPresent()) {
						result = catchResult.get();
						whenComplete.accept(result, e);
						return result;
					}

					// Check if we should retry or fail
					Duration delay = backoff.delay(attempt, e);
					if (delay == null) {
						// No more retries - handle failure
						result = failureHandler.apply(e);
						whenComplete.accept(result, e);
						return result;
					}

					// Log attempt and sleep before retry
					logAttempts(attempt, e.getMessage());
					sleep(delay);
				}
			}
		}

		private void sleep(Duration delay) {
			try {
				Thread.sleep(delay.toMillis());
			} catch (final InterruptedException e) {
				Thread.currentThread().interrupt();
				logger.warn("Sleep interrupted", e);
			}
		}

		private Optional<V> processCatches(Exception e) {
			for (CatchClause<V, ?> clause : catches) {
				if (clause.match(e)) return clause.process(e);
			}
			return Optional.empty();
		}

		private void logFail(String msg, Throwable e) {
			switch (errorType) {
				case DEBUG -> logger.debug(msg, e);
				case INFO -> logger.info(msg);
				case WARNING -> logger.warn(msg, e);
				default -> logger.error(msg, e);
			}
		}

		private void logAttempts(int attempt, String msg) {
			switch (errorType) {
				case DEBUG -> logger.debug(FAILED_TO_PROCESS_ROUTINE_ATTEMPT, attempt,msg);
				case INFO -> logger.info(FAILED_TO_PROCESS_ROUTINE_ATTEMPT, attempt,msg);
				default -> logger.warn(FAILED_TO_PROCESS_ROUTINE_ATTEMPT, attempt,msg);
			}
		}

		/**
		 *
		 * @param preProcessExecution void function to process pre execution process before every attempt, for instance
		 *                            api exec count
		 * @return this object
		 */
		public ProcessBuilder<V> attemptPreProcess(Runnable preProcessExecution) {
			 this.preAttemptExecution = preProcessExecution;
			 return this;
		}
	}

	private static class CatchClause<V, E extends Exception> {
		private final Class<E> clazz;
		private final Function<E, Optional<V>> handler;

		private CatchClause(Class<E> clazz, Function<E, Optional<V>> handler) {
			this.clazz = clazz;
			this.handler = handler;
		}

		public boolean match(Exception e) {
			return clazz.isInstance(e);
		}

		public Optional<V> process(Exception e) {
			return handler.apply(clazz.cast(e));
		}

		@Override
		public String toString() {
			return "CatchClause{%s}".formatted(clazz.getSimpleName());
		}
	}

	private static class DefaultBackoff implements BackoffFunction {
		private final int maxAttempts;

		DefaultBackoff(int maxAttempts) {
			this.maxAttempts = maxAttempts;
		}

		@Override
		public Duration delay(int attempt, Exception e) {
			if (attempt + 1 < maxAttempts) {
				int sleepPower = Math.min(attempt + 1, 5);
				int delay = (int) (Math.pow(4, sleepPower) * 100);
				int jitter = ThreadLocalRandom.current().nextInt(delay / 2 + 1) - delay / 4; // ±25% jitter
				return Duration.ofMillis(delay + jitter);
			}
			return null;
		}

		@Override
		public String toString() {
			return "DefaultBackoff{maxAttempts=%d}".formatted(maxAttempts);
		}
	}

	private static class SleeplessBackoff implements BackoffFunction {
		private final int maxAttempts;

		SleeplessBackoff(int maxAttempts) {
			this.maxAttempts = maxAttempts;
		}

		@Override
		public Duration delay(int attempt, Exception e) {
			return attempt + 1 < maxAttempts ? Duration.ZERO: null;
		}

		@Override
		public String toString() {
			return "SleeplessBackoff{maxAttempts=%d}".formatted(maxAttempts);
		}
	}
}
