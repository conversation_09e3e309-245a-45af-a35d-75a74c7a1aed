package io.syrix.security;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;

public final class EncryptedString implements Serializable {

    private static final long serialVersionUID = -988225485036435137L;

    private final String value;
    private final byte[] iv;
    private final int ver;

    @BsonCreator
    public EncryptedString(@BsonProperty("value") final String encryptedString,
                           @BsonProperty("iv") final byte[] iv,
                           @BsonProperty("ver") final int ver) {
        this.value = encryptedString;
        this.iv = Arrays.copyOf(iv, iv.length);
        this.ver = ver;
    }

    public String getValue() {
        return value;
    }

    public byte[] getIv() {
        return Arrays.copyOf(iv, iv.length);
    }

    public int getVer() {
        return ver;
    }


    public String toString() {
        return String.format("EncryptedString[value = %s, algVer = %s]",
                value, ver);
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            EncryptedString that = (EncryptedString)o;
            return this.value.equals(that.value);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(this.value);
    }
}

