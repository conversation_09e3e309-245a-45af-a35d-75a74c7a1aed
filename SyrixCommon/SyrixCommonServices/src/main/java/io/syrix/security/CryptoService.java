package io.syrix.security;

import io.syrix.Context;
import io.syrix.aws.KmsService;
import io.syrix.exceptions.SyrixException;
import io.syrix.utils.CommonUtils;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static java.util.Objects.requireNonNull;
import static javax.crypto.Cipher.DECRYPT_MODE;
import static javax.crypto.Cipher.ENCRYPT_MODE;

public class CryptoService {

	public static final int IV_SIZE = 12;
	private static final String AES_ALGORITHM = "AES";
	private static final String CHACHA20_ALGORITHM = "ChaCha20";
	private static final String BLOCK_ALGORITHM = "AES/GCM/NoPadding";
	private static final String STREAM_ALGORITHM = "ChaCha20-Poly1305";
	private static final Logger log = LoggerFactory.getLogger(CryptoService.class);
	private static final byte MK_DELIMITER = ',';
	private static final String MK_PROPERTIES_FILE_NAME = "mk.properties";
	private static final String SYRIX_MK_PATH_PROP = "Syrix.MKPropPath";
	private static final SecureRandom SECURE_RANDOM = new SecureRandom();

	private static volatile CryptoService instance;

	private final MasterKey currentMK;
	private final List<MasterKey> mkVersions;

	public static CryptoService getInstance() {
		if (instance == null) {
			synchronized (CryptoService.class) {
				if (instance == null) {
					instance = new CryptoService();
				}
			}
		}
		return instance;
	}

	private CryptoService() {
		Properties mk = getMKProperties();
		String keyId = requireNonNull(mk.getProperty("keyId"), "Property missed from mk: keyId");
		String encryptedKeyList = requireNonNull(mk.getProperty("keys"), "Property missed from mk: keys");
		List<byte[]> masterKeys = KmsService.getInstance().decryptAndSplit(keyId, encryptedKeyList, MK_DELIMITER);
		AtomicInteger indexCounter = new AtomicInteger(0);
		mkVersions = masterKeys.stream().map(key -> {
			int currentIndex = indexCounter.getAndIncrement();
			return new MasterKey(key, keyId, currentIndex);
		}).toList();
        if (mkVersions.isEmpty()) {
            throw new SyrixException("No master keys available for encryption.");
        }
        currentMK = mkVersions.get(mkVersions.size() - 1);
	}

	private Properties getMKProperties() {
		Properties mk = new Properties();
		String mkPropPath = getMKPropPathFromSysVar();
		if (mkPropPath != null) {
			try (InputStream in = new FileInputStream(mkPropPath)) {
				mk.load(in);
				return mk;
			} catch (IOException e) {
				throw new SyrixException("Cannot load keys from the local file: " + mkPropPath, e);
			}
		}
		try (InputStream in = Context.class.getResourceAsStream(MK_PROPERTIES_FILE_NAME)) {
			if (in == null) {
				throw new SyrixException("Class-path resource 'mk.properties' not found, use --master-keys param");
			}
			mk.load(in);
			return mk;
		} catch (IOException e) {
			throw new SyrixException("Failed to load mk.properties",e);
		}
	}

	private String getMKPropPathFromSysVar() {
		return CommonUtils.readConfigProperty(SYRIX_MK_PATH_PROP);
	}

	public EncryptedString encryptBuffer( final byte[] plainBuffer) {
		try {
			byte[] iv = generateIv();
			Cipher cipher = initAES(currentMK, AES_ALGORITHM, ENCRYPT_MODE, BLOCK_ALGORITHM, iv);
			return new EncryptedString(Hex.encodeHexString(cipher.doFinal(plainBuffer)), iv, currentMK.getVer());
		} catch (final Exception e) {
			throw new SyrixException("Error During AES Encryption", e);
		}
	}


	public EncryptedString encrypt( final String plainString) {
		return encryptBuffer(plainString.getBytes());
	}

	public byte[] decryptToBuffer(EncryptedString cipherString) {
		try {

			int ver = cipherString.getVer();
            if (ver < 0 || ver >= mkVersions.size()) {
				throw new SyrixException("failed to decrypt data as version is not valid");
			}
			MasterKey key = mkVersions.get(ver);
			byte[] cipherBuffer = Hex.decodeHex(cipherString.getValue());

			Cipher cipher = initAES(key, AES_ALGORITHM, DECRYPT_MODE, BLOCK_ALGORITHM,
					cipherString.getIv());

			return cipher.doFinal(cipherBuffer);
		} catch (Exception e) {
			throw new SyrixException("Error During AES Decryption", e);
		}
	}

	public String decrypt(EncryptedString cipherString) {
		return new String(decryptToBuffer(cipherString), StandardCharsets.UTF_8);
	}

	public OutputStream getDefaultCipherOutputStream(final OutputStream dest) throws IOException {

		final byte[] iv = generateIv();

		//write a key version
		final ByteBuffer buffer = ByteBuffer.allocate(4);
		buffer.order(ByteOrder.BIG_ENDIAN); // BIG_ENDIAN or LITTLE_ENDIAN
		buffer.putInt(currentMK.getVer());
		dest.write(buffer.array());

		//write iv
		dest.write(iv);

		Cipher cipher = initAES(currentMK, CHACHA20_ALGORITHM, ENCRYPT_MODE, STREAM_ALGORITHM, iv);
		OutputStream out = new CipherOutputStream(dest, cipher);

		return out;
	}

	public static OutputStream getCipherOutputStream(final byte[] key, final byte[] iv, final OutputStream dest) {
		MasterKey mk = new MasterKey(key, "", 0);
		Cipher cipher = initAES(mk, CHACHA20_ALGORITHM, ENCRYPT_MODE, STREAM_ALGORITHM, iv);
		OutputStream out = new CipherOutputStream(dest, cipher);
		return out;
	}

	public  InputStream getDefaultCipherInputStream(final InputStream in) throws IOException {

		//Read a key version
		byte[] bytes = new byte[4];
		int bytesRead = in.read(bytes);
		if (bytesRead != 4) {
			throw new IOException("Expected 4 bytes, got " + bytesRead);
		}

		ByteBuffer buffer = ByteBuffer.wrap(bytes);
		buffer.order(ByteOrder.BIG_ENDIAN);

		int ver = buffer.getInt();

		if (ver < 0 || ver >= mkVersions.size()) {
			throw new SyrixException("failed to decrypt data as version is not valid");
		}
		MasterKey key = mkVersions.get(ver);
		// Read iv from beginning of stream
		byte[] iv = new byte[IV_SIZE];
		bytesRead = in.read(iv);

		if (bytesRead != IV_SIZE) {
			throw new IOException( String.format("Expected bytes %d, got %d", IV_SIZE,  bytesRead));
		}

		Cipher cipher = initAES(key, CHACHA20_ALGORITHM, DECRYPT_MODE, STREAM_ALGORITHM, iv);
		return new CipherInputStream(in, cipher);
	}

	public static InputStream getAESInputStream(final byte[] key, final byte[] iv, final InputStream in) {
		MasterKey mk = new MasterKey(key, "", 0);
		Cipher cipher = initAES(mk, CHACHA20_ALGORITHM, DECRYPT_MODE, STREAM_ALGORITHM, iv);
		return new CipherInputStream(in, cipher);
	}

	private static Cipher initAES(final MasterKey masterKey, final String algorithm,
								  final int mode, final String transformation, final byte[] iv) {
		try {
			final SecretKeySpec skeySpec = new SecretKeySpec(masterKey.getKey(), algorithm);
			Cipher cipher = Cipher.getInstance(transformation);
			GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            cipher.init(mode, skeySpec, gcmSpec);
			return cipher;
		} catch (final Exception e) {
			throw new SyrixException("Cannot initialize AES encryption", e);
		}
	}

	private byte[] generateIv() {
		byte[] iv = new byte[IV_SIZE];
		SECURE_RANDOM.nextBytes(iv);
		return iv;
	}
}
