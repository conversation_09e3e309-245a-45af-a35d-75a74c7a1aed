package io.syrix.security;

import java.util.Arrays;

public class <PERSON><PERSON><PERSON> {

    private final byte[] key;
    private final String keyId;
    private final int ver;

    public MasterKey(final byte[] key, final String keyId, final int ver) {
        this.key = Arrays.copyOf(key, key.length);
        this.keyId = keyId;
        this.ver = ver;
    }

    public byte[] getKey() {
        return Arrays.copyOf(key, key.length);
    }

    public String getKeyId() {
        return keyId;
    }

    public int getVer() {
        return ver;
    }

    public void clear() {
        Arrays.fill(key, (byte)0);
    }
}
