package io.syrix.aws;

import io.syrix.exceptions.ExceptionHelper;
import io.syrix.exceptions.SyrixException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.kms.model.DecryptRequest;
import software.amazon.awssdk.services.kms.model.DecryptResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class KmsService {

    private static volatile KmsService instance = null;
    private static final Logger log = LoggerFactory.getLogger(KmsService.class);
    private final KmsClient kmsClient;

    private KmsService() {
        kmsClient = KmsClient.builder()
                .region(Region.US_EAST_1)
                .credentialsProvider(DefaultCredentialsProvider.builder().build())
                .httpClient(ApacheHttpClient.builder()
                    .maxConnections(50)
                    .connectionTimeout(Duration.ofSeconds(5))
                    .socketTimeout(Duration.ofSeconds(30))
                    .build())
                .build();
    }

    public static KmsService getInstance() {
        if (instance == null) {
            synchronized (KmsService.class) {
                if (instance == null) {
                    instance = new KmsService();
                }
            }
        }
        return instance;
    }

    public byte[] decrypt(final String keyId, final String encryptedData) {
        DecryptResponse resp = new ExceptionHelper()
                .process(() -> kmsClient.decrypt(DecryptRequest.builder()
                        .keyId(keyId)
                        .ciphertextBlob(SdkBytes.fromString(encryptedData, StandardCharsets.UTF_8)).build()))
                .throwAll();
        if (resp != null ) {
            return resp.plaintext().asByteArray();
        }
        log.error("decrypt - response is empty for key : {}", keyId);
        throw new SyrixException("Failed to decrypt data: KMS response was empty for keyId " + keyId);
    }

    public List<byte[]> decryptAndSplit(final String keyId, final String encryptedData, byte delimiter) {
        DecryptResponse resp = new ExceptionHelper()
                .process(() -> kmsClient.decrypt(DecryptRequest.builder()
                        .keyId(keyId)
                        .ciphertextBlob(SdkBytes.fromString(encryptedData, StandardCharsets.UTF_8)).build()))
                .throwAll();
        if (resp != null ) {
            return splitByDelimiter(resp.plaintext(), delimiter);
        }
        throw new SyrixException("Failed to decrypt data: KMS response was empty for keyId " + keyId);

    }

    private List<byte[]> splitByDelimiter(final SdkBytes plaintext, final byte delimiter) {
        List<byte[]> result = new ArrayList<>();
        byte[] array = plaintext.asByteArray();
        int start = 0;
        for (int i = 0; i < array.length; i++) {
            if (array[i] == delimiter) {
                result.add(Arrays.copyOfRange(array, start, i));
                start = i + 1;
            }
        }
        // Add the remaining part
        result.add(Arrays.copyOfRange(array, start, array.length));
        return result;
    }

    public static void shutdown() {
        if (instance.kmsClient != null) {
            instance.kmsClient.close();
        }
    }
}
