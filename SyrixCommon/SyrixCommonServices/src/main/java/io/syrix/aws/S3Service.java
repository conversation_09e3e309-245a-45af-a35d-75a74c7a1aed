package io.syrix.aws;

import io.syrix.Context;
import io.syrix.exceptions.SyrixException;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.Properties;

public class S3Service {

    //TODO need to add S3 factory to be able to re-use S3 client (S3Service should be singleton?)


    private static S3Client createAmazonS3Client(final Region region) {
        return S3Client.builder()
                .region(region)
                .credentialsProvider(DefaultCredentialsProvider.create())
                .httpClient(ApacheHttpClient.builder()
                        .maxConnections(50)
                        .connectionTimeout(Duration.ofSeconds(5))
                        .socketTimeout(Duration.ofSeconds(30))
                        .build())
                .build();
    }

    public static Properties getPropFile(final String bucket, final String path) {
        final Properties credProp = new Properties();

        try (S3Client client = createAmazonS3Client(Region.US_EAST_1);
             InputStream inputStream = client.getObject(GetObjectRequest.builder()
                             .bucket(bucket)
                             .key(path)
                             .build())) {
            credProp.load(inputStream);
            return credProp;
        } catch (final SdkClientException  | IOException e)  {
            throw new SyrixException(String.format("failed to get properties file from bucket: %s, path: %s", bucket, path), e);
        }
    }
}
