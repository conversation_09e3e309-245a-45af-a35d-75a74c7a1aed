package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ServiceType;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object interface for ConnectionInfo entities.
 */
public interface ConnectionInfoDao extends BaseDao<ConnectionInfo, UUID> {

    /**
     * Find connection information by Customer ID.
     *
     * @param customerId The Customer ID
     * @return A list of connection information for the given Customer
     * @throws DataAccessException If a data access error occurs
     */
    List<ConnectionInfo> findByCustomerId(String customerId) throws DataAccessException;

    /**
     * Find connection information by service type.
     *
     * @param service The ServiceType type
     * @return A list of connection information for the given service type
     * @throws DataAccessException If a data access error occurs
     */
    List<ConnectionInfo> findByService(ServiceType service) throws DataAccessException;

    /**
     * Find connection information by service type and customer ID.
     *
     * @param service The ServiceType type
     * @param customerId The Customer ID
     * @return Optional connection information for the given service type and customer ID
     * @throws DataAccessException If a data access error occurs
     */
    Optional<ConnectionInfo> findByServiceAndCustomerId(ServiceType service, UUID customerId) throws DataAccessException;
}
