package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ServiceType;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object interface for OAuth2 tokens.
 * This interface defines operations for accessing and manipulating OAuth2 token data.
 */
public interface OAuth2TokenDao {
    
    /**
     * Get all connection information.
     *
     * @return A list of connection information objects
     * @throws DataAccessException If a data access error occurs
     */
    List<ConnectionInfo> getAllConnections() throws DataAccessException;
    
    /**
     * Get connection information by ID.
     *
     * @param id The connection ID
     * @return An optional containing the connection information, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<ConnectionInfo> getConnectionById(String id) throws DataAccessException;
    
    /**
     * Get connection information by client ID and service type.
     *
     * @param clientId The client ID
     * @param serviceType The service type (e.g., "office365", "google")
     * @return An optional containing the connection information, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<ConnectionInfo> getConnectionByClientAndService(String clientId, ServiceType serviceType)
            throws DataAccessException;
    
    /**
     * Save or update OAuth2 tokens.
     *
     * @param clientId The client ID
     * @param serviceType The service type
     * @param accessToken The access token
     * @param refreshToken The refresh token
     * @param tokenExpires The token expiration timestamp
     * @param scopes The OAuth2 scopes
     * @param domain The domain associated with the tokens
     * @return true if saved successfully, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean saveTokens(String clientId, ServiceType serviceType, String accessToken,
                      String refreshToken, String tennantId, Timestamp tokenExpires, String scopes, String domain)
                      throws DataAccessException;
    
    /**
     * Delete a connection by ID.
     *
     * @param id The connection ID
     * @return true if deleted successfully, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean deleteConnection(UUID id) throws DataAccessException;
    
    /**
     * Update the last connected timestamp for a connection.
     *
     * @param id The connection ID
     * @param lastConnected The last connected timestamp
     * @return true if updated successfully, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean updateLastConnected(String id, LocalDateTime lastConnected) throws DataAccessException;
}
