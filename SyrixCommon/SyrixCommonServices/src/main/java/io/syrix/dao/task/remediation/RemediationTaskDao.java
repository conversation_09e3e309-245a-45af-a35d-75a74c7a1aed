package io.syrix.dao.task.remediation;

import io.syrix.dao.BaseDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.task.remediation.RemediationTask;

import java.util.Optional;
import java.util.UUID;

public interface RemediationTaskDao extends BaseDao<RemediationTask, UUID> {

	Optional<RemediationTask> findTaskByIdAndCustomer(
			UUID taskId,
			UUID customerId) throws DataAccessException;
}
