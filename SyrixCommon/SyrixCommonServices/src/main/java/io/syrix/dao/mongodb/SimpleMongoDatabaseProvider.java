package io.syrix.dao.mongodb;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

/**
 * A simple standalone implementation of MongoDatabaseProvider that creates and manages 
 * a MongoDB client and database connection without requiring Spring or other frameworks.
 * This provider supports configuration through environment variables, properties file, or direct parameters.
 */
public class SimpleMongoDatabaseProvider implements MongoDatabaseProvider {

    private static final Logger logger = LoggerFactory.getLogger(SimpleMongoDatabaseProvider.class);

    // Environment variable names for configuration
    private static final String ENV_CONNECTION_STRING = "SYRIX_MONGODB_CONNECTION_STRING";
    private static final String ENV_DATABASE = "SYRIX_MONGODB_DATABASE";

    // Property names for configuration
    private static final String PROP_CONNECTION_STRING = "mongodb.connectionString";
    private static final String PROP_DATABASE = "mongodb.database";

    // Default values
    private static final String DEFAULT_CONNECTION_STRING = "mongodb://localhost:27017";
    private static final String DEFAULT_DATABASE = "syrix";

    private final MongoClient mongoClient;
    private final MongoDatabase database;

    /**
     * Create a new SimpleMongoDatabaseProvider with configuration from environment variables.
     */
    public SimpleMongoDatabaseProvider() {
        this(getConnectionStringFromEnv(), getDatabaseFromEnv());
    }

    /**
     * Create a new SimpleMongoDatabaseProvider with configuration from the specified properties file.
     *
     * @param propertiesFilePath The path to the properties file containing MongoDB configuration
     */
    public SimpleMongoDatabaseProvider(String propertiesFilePath) {
        Properties properties = loadProperties(propertiesFilePath);
        String connectionString = properties.getProperty(PROP_CONNECTION_STRING, DEFAULT_CONNECTION_STRING);
        String databaseName = properties.getProperty(PROP_DATABASE, DEFAULT_DATABASE);
        
        this.mongoClient = createMongoClient(connectionString);
        this.database = createMongoDatabase(databaseName);
        
        logger.info("MongoDB provider initialized with database '{}' from properties file", databaseName);
    }

    /**
     * Create a new SimpleMongoDatabaseProvider with the specified connection string and database name.
     *
     * @param connectionString The MongoDB connection string
     * @param databaseName The MongoDB database name
     */
    public SimpleMongoDatabaseProvider(String connectionString, String databaseName) {
        this.mongoClient = createMongoClient(connectionString);
        this.database = createMongoDatabase(databaseName);
        
        logger.info("MongoDB provider initialized with database '{}'", databaseName);
    }

    /**
     * Create a new SimpleMongoDatabaseProvider with explicit MongoClient and database name.
     * This constructor is useful for testing with mocked MongoDB client.
     *
     * @param mongoClient The MongoDB client
     * @param databaseName The MongoDB database name
     */
    public SimpleMongoDatabaseProvider(MongoClient mongoClient, String databaseName) {
        this.mongoClient = mongoClient;
        this.database = mongoClient.getDatabase(databaseName);
        
        logger.info("MongoDB provider initialized with custom client and database '{}'", databaseName);
    }

    @Override
    public MongoDatabase getDatabase() {
        return database;
    }

    @Override
    public MongoClient getClient() {
        return mongoClient;
    }

    @Override
    public void close() {
        if (mongoClient != null) {
            try {
                mongoClient.close();
                logger.info("MongoDB client closed");
            } catch (Exception e) {
                logger.error("Error closing MongoDB client: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Create a MongoDB client with the specified connection string.
     *
     * @param connectionString The MongoDB connection string
     * @return The MongoDB client
     */
    private MongoClient createMongoClient(String connectionString) {
        try {
            logger.debug("Creating MongoDB client with connection string: {}", connectionString);
            
            // Create codec registry for POJO support
            CodecRegistry pojoCodecRegistry = fromRegistries(
                    MongoClientSettings.getDefaultCodecRegistry(),
                    fromProviders(PojoCodecProvider.builder().automatic(true).build())
            );

            // Configure MongoDB client settings
            MongoClientSettings settings = MongoClientSettings.builder()
                    .applyConnectionString(new ConnectionString(connectionString))
                    .codecRegistry(pojoCodecRegistry)
                    .build();

            // Create and return the MongoDB client
            return MongoClients.create(settings);
        } catch (Exception e) {
            logger.error("Error creating MongoDB client: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create MongoDB client", e);
        }
    }

    /**
     * Create a MongoDB database with the specified name.
     *
     * @param databaseName The MongoDB database name
     * @return The MongoDB database
     */
    private MongoDatabase createMongoDatabase(String databaseName) {
        try {
            logger.debug("Creating MongoDB database: {}", databaseName);
            return mongoClient.getDatabase(databaseName);
        } catch (Exception e) {
            logger.error("Error creating MongoDB database: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create MongoDB database", e);
        }
    }

    /**
     * Load MongoDB configuration from a properties file.
     *
     * @param propertiesFilePath The path to the properties file
     * @return The loaded properties
     */
    private Properties loadProperties(String propertiesFilePath) {
        Properties properties = new Properties();
        
        try (InputStream inputStream = new FileInputStream(propertiesFilePath)) {
            properties.load(inputStream);
            logger.debug("Loaded MongoDB configuration from properties file: {}", propertiesFilePath);
        } catch (IOException e) {
            logger.warn("Failed to load MongoDB configuration from properties file: {}", propertiesFilePath, e);
            logger.warn("Using default configuration");
        }
        
        return properties;
    }

    /**
     * Get the MongoDB connection string from environment variables.
     *
     * @return The MongoDB connection string
     */
    private static String getConnectionStringFromEnv() {
        String connectionString = System.getenv(ENV_CONNECTION_STRING);
        return connectionString != null ? connectionString : DEFAULT_CONNECTION_STRING;
    }

    /**
     * Get the MongoDB database name from environment variables.
     *
     * @return The MongoDB database name
     */
    private static String getDatabaseFromEnv() {
        String database = System.getenv(ENV_DATABASE);
        return database != null ? database : DEFAULT_DATABASE;
    }
}