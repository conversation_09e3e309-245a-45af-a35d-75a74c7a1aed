    package io.syrix.dao.impl.mongodb;

    import com.mongodb.client.MongoCollection;
    import com.mongodb.client.MongoDatabase;
    import com.mongodb.client.model.Filters;
    import com.mongodb.client.model.Updates;
    import com.mongodb.client.result.UpdateResult;
    import io.syrix.dao.OAuthConfigurationDao;
    import io.syrix.dao.exception.DataAccessException;
    import io.syrix.dao.mongodb.AbstractMongoDao;
    import io.syrix.datamodel.ConnectionStatus;
    import io.syrix.datamodel.ServiceType;
    import io.syrix.datamodel.oauth.MSALOAuthConfig;
    import io.syrix.datamodel.oauth.OAuthConfig;
    import org.bson.Document;
    import org.bson.conversions.Bson;
    import org.slf4j.Logger;
    import org.slf4j.LoggerFactory;

    import java.util.ArrayList;
    import java.util.Date;
    import java.util.List;
    import java.util.Optional;
    import java.util.UUID;

    /**
     * MongoDB implementation of the OAuth2ConfigurationDao interface.
     * This implementation reads from and writes to the SyrixConfig collection where
     * OAuth2 configuration is stored as a nested document "MSOAuthConfig" within a document
     * with name "config".
     */
    public class MongoOAuthConfigurationDao extends AbstractMongoDao implements OAuthConfigurationDao {

        private static final Logger logger = LoggerFactory.getLogger(MongoOAuthConfigurationDao.class);
        private static final String COLLECTION_NAME = "SyrixConfig";
        private static final String CONFIG_NAME = "config";
        private static final String MS_OAUTH_CONFIG = "MSOAuthConfig";

        /**
         * Create a new MongoOAuth2ConfigurationDao with the specified database connection.
         *
         * @param database The MongoDB database
         */
        public MongoOAuthConfigurationDao(MongoDatabase database) {
            super(database);
            ensureConfigDocumentExists();
        }

        /**
         * Ensure that the config document exists in the SyrixConfig collection.
         */
        private void ensureConfigDocumentExists() {
            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                if (configDoc == null) {
                    // Create the config document if it doesn't exist
                    Document newConfig = new Document("name", CONFIG_NAME);
                    collection.insertOne(newConfig);
                    logger.info("Created config document in SyrixConfig collection");
                }
            } catch (Exception e) {
                logger.warn("Error ensuring config document exists: {}", e.getMessage());
                // Don't throw exception here - we'll handle it if needed in other methods
            }
        }

        @Override
        public List<OAuthConfig> getAllConfigurations() throws DataAccessException {
            logger.debug("Getting all OAuth2 configurations");

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                List<OAuthConfig> result = new ArrayList<>();
                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);
                    if (msOAuthConfig != null) {
                        result.add(documentToOAuthConfig(msOAuthConfig));
                    }
                }

                return result;
            } catch (Exception e) {
                logger.error("Error getting all OAuth2 configurations: {}", e.getMessage(), e);
                throw new DataAccessException("Failed to get all OAuth2 configurations", e);
            }
        }

        @Override
        public Optional<OAuthConfig> getConfigurationByService(ServiceType serviceType) throws DataAccessException {
            logger.debug("Getting OAuth2 configuration for service type: {}", serviceType);

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);

                    if (msOAuthConfig != null && serviceType.getValue().equals(msOAuthConfig.getString("service_type"))) {
                        return Optional.of(documentToOAuthConfig(msOAuthConfig));
                    }
                }

                return Optional.empty();
            } catch (Exception e) {
                logger.error("Error getting OAuth2 configuration for service type {}: {}", serviceType, e.getMessage(), e);
                throw new DataAccessException("Failed to get OAuth2 configuration for service type: " + serviceType, e);
            }
        }

        @Override
        public boolean hasConfiguration(ServiceType serviceType) throws DataAccessException {
            logger.debug("Checking if OAuth2 configuration exists for service type: {}", serviceType);

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);

                    return msOAuthConfig != null &&
                           serviceType.getValue().equals(msOAuthConfig.getString("service_type"));
                }

                return false;
            } catch (Exception e) {
                logger.error("Error checking if OAuth2 configuration exists for service type {}: {}",
                        serviceType, e.getMessage(), e);
                throw new DataAccessException("Failed to check if OAuth2 configuration exists for service type: "
                        + serviceType, e);
            }
        }

        @Override
        public OAuthConfig save(OAuthConfig config) throws DataAccessException {
            logger.debug("Saving OAuth2 configuration for service type: {}", config.getServiceType());

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

                // Create the OAuth2 config document
                Document msOAuthConfigDoc = oAuthConfigToDocument(config);

                // Update the config document
                Bson filter = Filters.eq("name", CONFIG_NAME);
                Bson update = Updates.set(MS_OAUTH_CONFIG, msOAuthConfigDoc);

                UpdateResult result = collection.updateOne(filter, update);

                return config;
            } catch (Exception e) {
                logger.error("Error saving OAuth2 configuration for service type {}: {}",
                        config.getServiceType(), e.getMessage(), e);
                throw new DataAccessException("Failed to save OAuth2 configuration for service type: "
                        + config.getServiceType(), e);
            }
        }

        @Override
        public boolean deleteConfiguration(ServiceType serviceType) throws DataAccessException {
            logger.debug("Deleting OAuth2 configuration for service type: {}", serviceType);

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

                // Remove the MS OAuth config
                Bson filter = Filters.eq("name", CONFIG_NAME);
                Bson update = Updates.unset(MS_OAUTH_CONFIG);

                UpdateResult result = collection.updateOne(filter, update);

                return result.getModifiedCount() > 0;
            } catch (Exception e) {
                logger.error("Error deleting OAuth2 configuration for service type {}: {}",
                        serviceType, e.getMessage(), e);
                throw new DataAccessException("Failed to delete OAuth2 configuration for service type: "
                        + serviceType, e);
            }
        }

        @Override
        public Optional<OAuthConfig> findById(UUID id) {
            logger.debug("Finding OAuth2 configuration for id: {}", id);
            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);
                    if (msOAuthConfig != null && id.toString().equals(msOAuthConfig.getString("id"))) {
                        return Optional.of(documentToOAuthConfig(msOAuthConfig));
                    }
                }
                return Optional.empty();
            } catch (Exception e) {
                logger.error("Error finding configuration by id {}: {}", id, e.getMessage(), e);
                throw new DataAccessException("Failed to find OAuth2 configuration for id: " + id, e);
            }
        }

        @Override
        public List<OAuthConfig> findByStatus(ConnectionStatus connectionStatus) {
            logger.debug("Finding OAuth2 configurations with status: {}", connectionStatus);
            try {
                List<OAuthConfig> result = new ArrayList<>();
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);
                    if (msOAuthConfig != null) {
                        boolean isActive = msOAuthConfig.getBoolean("is_active", false);
                        boolean statusMatch = (connectionStatus == ConnectionStatus.ACTIVE && isActive)
                                || (connectionStatus != ConnectionStatus.ACTIVE && !isActive);
                        if (statusMatch) {
                            result.add(documentToOAuthConfig(msOAuthConfig));
                        }
                    }
                }
                return result;
            } catch (Exception e) {
                logger.error("Error finding configurations by status {}: {}", connectionStatus, e.getMessage(), e);
                throw new DataAccessException("Failed to find OAuth2 configurations by status: " + connectionStatus, e);
            }
        }

        @Override
        public Optional<OAuthConfig> findByService(ServiceType service) {
            logger.debug("Getting OAuth2 configuration document for service type: {}", service);

            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();

                // Check if the configuration document exists and contains the MS OAuth config.
                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);

                    // Verify that the document exists and matches the provided service type.
                    if (msOAuthConfig != null &&
                            service.getValue().equals(msOAuthConfig.getString("service_type"))) {
                        return Optional.of(documentToOAuthConfig(msOAuthConfig));
                    }
                }
                return Optional.empty();
            } catch (Exception e) {
                logger.error("Error getting document for service type {}: {}", service, e.getMessage(), e);
                throw new DataAccessException("Failed to get document for service type: " + service, e);
            }
        }

        @Override
        public void deleteById(UUID id) {
            logger.debug("Deleting OAuth2 configuration for id: {}", id);
            try {
                MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
                Document configDoc = collection.find(Filters.eq("name", CONFIG_NAME)).first();
                if (configDoc != null && configDoc.containsKey(MS_OAUTH_CONFIG)) {
                    Document msOAuthConfig = configDoc.get(MS_OAUTH_CONFIG, Document.class);
                    if (msOAuthConfig != null && id.toString().equals(msOAuthConfig.getString("id"))) {
                        Bson filter = Filters.eq("name", CONFIG_NAME);
                        Bson update = Updates.unset(MS_OAUTH_CONFIG);
                        collection.updateOne(filter, update);
                        logger.debug("Deleted OAuth2 configuration for id: {}", id);
                    }
                }
            } catch (Exception e) {
                logger.error("Error deleting OAuth2 configuration with id {}: {}", id, e.getMessage(), e);
                throw new DataAccessException("Failed to delete OAuth2 configuration for id: " + id, e);
            }
        }

        /**
         * Convert an OAuthConfig object to a MongoDB Document.
         *
         * @param config The OAuthConfig to convert
         * @return The MongoDB Document
         */
        private Document oAuthConfigToDocument(OAuthConfig config) {
           return new Document()
                    .append("id", UUID.randomUUID().toString())
                    .append("service_type", config.getServiceType().getValue())
                    .append("client_id", config.getClientId())
                    .append("client_secret", config.getClientSecret())
                    .append("redirect_uri", config.getRedirectUri())
                    .append("scope", config.getScopes())
                    .append("auth_url", config.getAuthUrl())
                    .append("token_url", config.getTokenUrl())
                    .append("is_active", true)
                    .append("created_at", new Date())
                    .append("updated_at", new Date());
        }

        /**
         * Convert a MongoDB Document to an OAuthConfig object.
         *
         * @param doc The MongoDB Document
         * @return The OAuthConfig object
         */
        private OAuthConfig documentToOAuthConfig(Document doc) {
            String serviceTypeStr = doc.getString("service_type");

            ServiceType serviceType = null;
            for (ServiceType st : ServiceType.values()) {
                if (st.getValue().equals(serviceTypeStr)) {
                    serviceType = st;
                    break;
                }
            }

            if (serviceType == null) {
                // Default to OFFICE365 if not found
                serviceType = ServiceType.OFFICE365;
            }

            String clientId = doc.getString("client_id");
            String clientSecret = doc.getString("client_secret");
            String redirectUri = doc.getString("redirect_uri");
            String scopes = doc.getString("scope"); // Note: field name is 'scope' in the document
            String authUrl = doc.getString("auth_url");
            String tokenUrl = doc.getString("token_url");

            // Use the builder pattern to create the config
            return MSALOAuthConfig.builder()
                    .serviceType(serviceType)
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .redirectUri(redirectUri)
                    .scopes(scopes)
                    .authUrl(authUrl)
                    .tokenUrl(tokenUrl)
                    .build();
        }
    }
