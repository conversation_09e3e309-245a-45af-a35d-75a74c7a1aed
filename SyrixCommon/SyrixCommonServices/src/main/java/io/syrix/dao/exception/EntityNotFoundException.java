package io.syrix.dao.exception;

/**
 * Exception thrown when a requested entity cannot be found.
 */
public class EntityNotFoundException extends DataAccessException {

    /**
     * Constructs a new EntityNotFoundException with the specified message.
     *
     * @param message the detail message
     */
    public EntityNotFoundException(String message) {
        super(message);
    }

    /**
     * Constructs a new EntityNotFoundException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public EntityNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new EntityNotFoundException for an entity with the specified ID.
     *
     * @param entityName the name of the entity type
     * @param id the ID of the entity
     */
    public EntityNotFoundException(String entityName, Object id) {
        super(entityName + " with id " + id + " not found");
    }
}
