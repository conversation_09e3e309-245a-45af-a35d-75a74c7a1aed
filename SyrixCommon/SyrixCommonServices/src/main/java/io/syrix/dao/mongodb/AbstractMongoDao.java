package io.syrix.dao.mongodb;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

/**
 * Abstract MongoDB DAO class that provides basic functionality for MongoDB operations.
 * This is a base class for all MongoDB-based DAO implementations without framework dependencies.
 */
public abstract class AbstractMongoDao {
    
    protected final MongoDatabase database;
    protected final CodecRegistry pojoCodecRegistry;
    
    /**
     * Constructs a new AbstractMongoDao with the given MongoDatabase.
     * 
     * @param database The MongoDB database to use for operations
     */
    public AbstractMongoDao(MongoDatabase database) {
        this.database = database;
        this.pojoCodecRegistry = fromRegistries(
                database.getCodecRegistry(),
                fromProviders(PojoCodecProvider.builder().automatic(true).build())
        );
    }
    
    /**
     * Gets a MongoDB collection with the given name and document class.
     * 
     * @param collectionName The name of the collection
     * @param documentClass The class of documents in the collection
     * @param <T> The type of documents in the collection
     * @return The MongoDB collection
     */
    protected <T> MongoCollection<T> getCollection(String collectionName, Class<T> documentClass) {
        return database.getCollection(collectionName, documentClass).withCodecRegistry(pojoCodecRegistry);
    }
    
    /**
     * Gets a MongoDB collection with the given name for raw Document operations.
     * 
     * @param collectionName The name of the collection
     * @return The MongoDB collection of Documents
     */
    protected MongoCollection<Document> getCollection(String collectionName) {
        return database.getCollection(collectionName);
    }
}
