package io.syrix.dao.impl.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import io.syrix.dao.OAuth2TokenDao;
import io.syrix.dao.exception.ConstraintViolationException;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoDao;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * MongoDB implementation of the OAuth2TokenDao interface without framework dependencies.
 */
public class MongoOAuth2TokenDao extends AbstractMongoDao implements OAuth2TokenDao {

	private static final Logger logger = LoggerFactory.getLogger(MongoOAuth2TokenDao.class);
	private static final String COLLECTION_NAME = "OauthTokens";
	private static final String CONNECTION_INFO_COLLECTION = "ConnectionInfo";
	public static final String CLIENT_ID = "clientId";
	public static final String SERVICE = "service";
	public static final String TENANT_ID = "tenantId";
	public static final String LAST_CONNECTED = "lastConnected";
	public static final String DOMAIN = "domain";
	public static final String CONNECTED = "connected";
	public static final String SERVICE_TYPE = "serviceType";

	/**
	 * Create a new MongoOAuth2TokenDao with the specified database connection.
	 *
	 * @param database The MongoDB database
	 */
	public MongoOAuth2TokenDao(MongoDatabase database) {
		super(database);
	}

	@Override
	public List<ConnectionInfo> getAllConnections() throws DataAccessException {
		logger.debug("Getting all connections");

		try {
			MongoCollection<Document> collection = getCollection(CONNECTION_INFO_COLLECTION);
			FindIterable<Document> docs = collection.find();

			List<ConnectionInfo> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToConnectionInfo(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error getting all connections: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to get all connections", e);
		}
	}

	@Override
	public Optional<ConnectionInfo> getConnectionById(String id) throws DataAccessException {
		logger.debug("Getting connection by ID: {}", id);

		try {
			MongoCollection<Document> collection = getCollection(CONNECTION_INFO_COLLECTION);
			Document doc = collection.find(Filters.eq("_id", id)).first();

			if (doc == null) {
				return Optional.empty();
			}

			return Optional.of(documentToConnectionInfo(doc));
		} catch (Exception e) {
			logger.error("Error getting connection by ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to get connection by ID: " + id, e);
		}
	}

	@Override
	public Optional<ConnectionInfo> getConnectionByClientAndService(String clientId, ServiceType serviceType)
			throws DataAccessException {
		logger.debug("Getting connection by client ID: {} and service type: {}", clientId, serviceType);

		try {
			MongoCollection<Document> collection = getCollection(CONNECTION_INFO_COLLECTION);
			Document doc = collection.find(
					Filters.and(
							Filters.eq(CLIENT_ID, clientId),
							Filters.eq(SERVICE, serviceType.getValue())
					)
			).first();

			if (doc == null) {
				return Optional.empty();
			}

			return Optional.of(documentToConnectionInfo(doc));
		} catch (Exception e) {
			logger.error("Error getting connection by client ID {} and service type {}: {}",
					clientId, serviceType, e.getMessage(), e);
			throw new DataAccessException("Failed to get connection by client ID and service type", e);
		}
	}

	@Override
	public boolean saveTokens(String clientId, ServiceType serviceType, String accessToken,
							  String refreshToken, String tenantId, Timestamp tokenExpires, String scopes, String domain)
			throws DataAccessException {
		logger.debug("Saving tokens for client ID: {} and service type: {}", clientId, serviceType);

		try {
			MongoCollection<Document> tokensCollection = getCollection(COLLECTION_NAME);
			MongoCollection<Document> connectionCollection = getCollection(CONNECTION_INFO_COLLECTION);

			// Check if connection exists
			Bson connectionFilter = Filters.and(
					Filters.eq(CLIENT_ID, clientId),
					Filters.eq(SERVICE, serviceType)
			);

			Document connectionDoc = connectionCollection.find(connectionFilter).first();
			UUID connectionId;

			if (connectionDoc == null) {
				// Create a new connection
				connectionId = UUID.randomUUID();

				if (StringUtils.isEmpty(tenantId)) {
					// If tenantId is empty in the document, check if the domain can be used to derive it
					if (!StringUtils.isEmpty(domain) && domain.contains(".onmicrosoft.com")) {
						tenantId = domain.replace(".onmicrosoft.com", "");
						logger.info("Derived tenant ID from domain: {}", tenantId);
					} else {
						// Use a default tenantId based on client ID
						tenantId = clientId;
						logger.info("Using client ID as tenant ID: {}", tenantId);
					}
				}

				if (StringUtils.isEmpty(tenantId)) {
					throw new ConstraintViolationException("Tenant ID cannot be empty");
				}

				// Create connection info
				ConnectionInfo connectionInfo = new ConnectionInfo(
						connectionId,
						serviceType,
						ConnectionStatus.ACTIVE,
						serviceType + " Connection",
						LocalDateTime.now(),
						tenantId,
						UUID.fromString(clientId),
						domain
				);

				// Insert connection info
				connectionCollection.insertOne(connectionInfoToDocument(connectionInfo));
			} else {
				connectionId = UUID.fromString(connectionDoc.getString("_id"));

				// Update connection info
				connectionCollection.updateOne(
						connectionFilter,
						Updates.combine(
								Updates.set(CONNECTED, true),
								Updates.set(LAST_CONNECTED, LocalDateTime.now()),
								Updates.set(DOMAIN, domain)
						)
				);
			}

			// Create or update tokens
			Bson tokenFilter = Filters.and(
					Filters.eq(CLIENT_ID, clientId),
					Filters.eq(SERVICE_TYPE, serviceType.getValue())
			);

			Document tokenDoc = new Document()
					.append(CLIENT_ID, clientId)
					.append(SERVICE_TYPE, serviceType.getValue())
					.append("accessToken", accessToken)
					.append("refreshToken", refreshToken)
					.append("tokenExpires", tokenExpires != null ? tokenExpires.toLocalDateTime() : null)
					.append("scopes", scopes)
					.append(DOMAIN, domain)
					.append("connectionId", connectionId)
					.append(TENANT_ID, connectionDoc != null ? connectionDoc.getString(TENANT_ID) : clientId) // Ensure tenant ID is saved in token document too
					.append("lastUpdated", LocalDateTime.now());

			// Update or insert the document
			tokensCollection.replaceOne(tokenFilter, tokenDoc, new com.mongodb.client.model.ReplaceOptions().upsert(true));

			return true;
		} catch (Exception e) {
			logger.error("Error saving tokens for client ID {} and service type {}: {}",
					clientId, serviceType, e.getMessage(), e);
			throw new DataAccessException("Failed to save tokens", e);
		}
	}

	@Override
	public boolean deleteConnection(UUID id) throws DataAccessException {
		logger.debug("Deleting connection by ID: {}", id);

		try {
			MongoCollection<Document> connectionCollection = getCollection(CONNECTION_INFO_COLLECTION);
			MongoCollection<Document> tokensCollection = getCollection(COLLECTION_NAME);

			// Get the connection first
			Document connectionDoc = connectionCollection.find(Filters.eq("_id", id)).first();
			if (connectionDoc == null) {
				return false;
			}

			String clientId = connectionDoc.getString(CLIENT_ID);
			String serviceType = connectionDoc.getString(SERVICE);

			// Delete tokens
			tokensCollection.deleteOne(
					Filters.and(
							Filters.eq(CLIENT_ID, clientId),
							Filters.eq(SERVICE_TYPE, serviceType)
					)
			);

			// Delete connection
			DeleteResult result = connectionCollection.deleteOne(Filters.eq("_id", id));

			return result.getDeletedCount() > 0;
		} catch (Exception e) {
			logger.error("Error deleting connection by ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to delete connection by ID: " + id, e);
		}
	}

	@Override
	public boolean updateLastConnected(String id, LocalDateTime lastConnected) throws DataAccessException {
		logger.debug("Updating last connected time for connection ID: {}", id);

		try {
			MongoCollection<Document> collection = getCollection(CONNECTION_INFO_COLLECTION);
			UpdateResult result = collection.updateOne(Filters.eq("_id", id), Updates.set(LAST_CONNECTED, lastConnected));
			return result.getModifiedCount() > 0;
		} catch (Exception e) {
			logger.error("Error updating last connected time for connection ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to update last connected time for connection ID: " + id, e);
		}
	}

	/**
	 * Convert a ConnectionInfo object to a MongoDB Document.
	 *
	 * @param connectionInfo The ConnectionInfo to convert
	 * @return The MongoDB Document
	 */
	private Document connectionInfoToDocument(ConnectionInfo connectionInfo) {
		return new Document("_id", connectionInfo.getId())
				.append(SERVICE, connectionInfo.getService())
				.append("connectionStatus", connectionInfo.getStatus())
				.append("displayName", connectionInfo.getDisplayName())
				.append(LAST_CONNECTED, connectionInfo.getLastConnected())
				.append(TENANT_ID, connectionInfo.getExternalTenantId())
				.append(CLIENT_ID, connectionInfo.getCustomerId())
				.append(DOMAIN, connectionInfo.getDomain());
	}

	/**
	 * Convert a MongoDB Document to a ConnectionInfo object.
	 *
	 * @param doc The MongoDB Document
	 * @return The ConnectionInfo object
	 */
	private ConnectionInfo documentToConnectionInfo(Document doc) {
		UUID id = UUID.fromString(doc.getString("_id"));
		ServiceType service = ServiceType.valueOf(doc.getString(SERVICE));
		ConnectionStatus connected = ConnectionStatus.valueOf(doc.getString("connectionStatus"));
		String displayName = doc.getString("displayName");
		LocalDateTime lastConnected = doc.get(LAST_CONNECTED, Date.class).toInstant().atZone(java.time.ZoneId.of("UTC")).toLocalDateTime();
		String tenantId = doc.getString(TENANT_ID);
		UUID clientId = UUID.fromString(doc.getString(CLIENT_ID));
		String domain = doc.getString(DOMAIN);

		return new ConnectionInfo(id, service, connected, displayName, lastConnected, tenantId, clientId, domain);
	}
}
