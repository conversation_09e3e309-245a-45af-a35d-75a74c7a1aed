package io.syrix.dao;

import io.syrix.dao.task.remediation.RemediationTaskDao;

/**
 * Factory interface for creating DAO instances.
 */
public interface DaoFactory {

    /**
     * Get the CustomerDao instance.
     *
     * @return The CustomerDao
     */
    CustomerDao getCustomerDao();

    ConnectionInfoDao getConnectionInfoDao();

    OAuthConfigurationDao getOAuthConfigurationDao();
    
    /**
     * Get the OAuth2TokenDao instance.
     *
     * @return The OAuth2TokenDao
     */
    OAuth2TokenDao getOAuth2TokenDao();

    /**
     * Get the SysConfigDao instance.
     *
     * @return The SysConfigDao
     */
    SysConfigDao getSysConfigDao();

    /**
     * Get the CustomerReportDao instance.
     *
     * @return The CustomerReportDao
     */
    CustomerReportDao getCustomerReportDao();

    /**
     * Get the TaskDao instance.
     *
     * @return The TaskDao
     */
    TaskDao getTaskDao();

    RemediationTaskDao getRemediationTaskDao();

    /**
     * Close resources held by this factory.
     */
    void close();
}
