package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.report.CustomerReport;

import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object for working with customer reports.
 * Provides methods for saving and finding reports by companies.
 */
public interface CustomerReportDao extends BaseDao<CustomerReport, UUID> {

    /**
     * Find the latest report for a specific customer.
     *
     * @param customerId Customer ID
     * @return Optional with report if found
     * @throws DataAccessException if a data access error occurs
     */
    Optional<CustomerReport> findLatestByCustomerId(UUID customerId) throws DataAccessException;
    
}
