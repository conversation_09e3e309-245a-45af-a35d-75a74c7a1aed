package io.syrix.dao.impl.mongodb;

import com.mongodb.client.MongoDatabase;
import io.syrix.dao.ConnectionInfoDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoBaseDao;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.token.Token;
import io.syrix.datamodel.token.TokenType;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.mongodb.client.model.Filters.eq;

/**
 * MongoDB implementation of the ConnectionInfoDao interface.
 * Extends AbstractMongoBaseDao to provide standardized CRUD operations with MongoDB.
 */
public class MongoConnectionInfoDao extends AbstractMongoBaseDao<ConnectionInfo, UUID> implements ConnectionInfoDao {

	private static final Logger logger = LoggerFactory.getLogger(MongoConnectionInfoDao.class);
	private static final String COLLECTION_NAME = "connections";
	public static final String LAST_CONNECTED_AT = "lastConnectedAt";

	/**
	 * Constructor for MongoConnectionInfoDao.
	 *
	 * @param database MongoDB database instance
	 */
	public MongoConnectionInfoDao(MongoDatabase database) {
		super(database, COLLECTION_NAME);
	}

	@Override
	public List<ConnectionInfo> findByCustomerId(String customerId) throws DataAccessException {
		try {
			var docs = getCollection(COLLECTION_NAME).find(eq("customerId", customerId));
			List<ConnectionInfo> result = new java.util.ArrayList<>();
			for (var doc : docs) {
				result.add(documentToEntity(doc));
			}
			return result;
		} catch (Exception e) {
			logger.error("Error finding ConnectionInfo by customerId", e);
			throw new DataAccessException("Error finding ConnectionInfo by customerId", e);
		}
	}

	@Override
	public List<ConnectionInfo> findByService(ServiceType service) throws DataAccessException {
		try {
			var docs = getCollection(COLLECTION_NAME).find(eq("serviceType", service.name()));
			List<ConnectionInfo> result = new java.util.ArrayList<>();
			for (var doc : docs) {
				result.add(documentToEntity(doc));
			}
			return result;
		} catch (Exception e) {
			logger.error("Error finding ConnectionInfo by serviceType", e);
			throw new DataAccessException("Error finding ConnectionInfo by serviceType", e);
		}
	}

	@Override
	public Optional<ConnectionInfo> findByServiceAndCustomerId(ServiceType service, UUID customerId) throws DataAccessException {
		try {
			var filter = com.mongodb.client.model.Filters.and(
					eq("serviceType", service.name()),
					eq("customerId", customerId.toString())
			);
			var doc = getCollection(COLLECTION_NAME).find(filter).first();
			return doc != null ? Optional.of(documentToEntity(doc)) : Optional.empty();
		} catch (Exception e) {
			logger.error("Error finding ConnectionInfo by serviceType and customerId", e);
			throw new DataAccessException("Error finding ConnectionInfo by serviceType and customerId", e);
		}
	}

	@Override
	protected ConnectionInfo prepareForCreate(ConnectionInfo entity) {
		if (entity.getId() == null) {
			entity.setId(UUID.randomUUID());
		}
		if (entity.getCreatedAt() == null) {
			entity.setCreatedAt(LocalDateTime.now());
		}
		if (entity.getUpdatedAt() == null) {
			entity.setUpdatedAt(LocalDateTime.now());
		}
		return entity;
	}

	@Override
	protected ConnectionInfo prepareForUpdate(ConnectionInfo entity) {
		entity.setUpdatedAt(LocalDateTime.now());
		return entity;
	}

	@Override
	protected UUID getEntityId(ConnectionInfo entity) {
		return entity.getId();
	}

	@Override
	protected ConnectionInfo documentToEntity(Document document) {
		if (document == null) {
			return null;
		}

		ConnectionInfo connectionInfo = new ConnectionInfo();

		// Map basic fields from the document to the ConnectionInfo object
		connectionInfo.setId(UUID.fromString(document.getString("_id")));
		connectionInfo.setCustomerId(UUID.fromString(document.getString("customerId")));

		// Convert service_type to ServiceType enum
		String serviceTypeStr = document.getString("serviceType");
		if (serviceTypeStr != null) {
			try {
				ServiceType serviceType = ServiceType.valueOf(serviceTypeStr);
				connectionInfo.setService(serviceType);
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown service type: {}", serviceTypeStr);
			}
		}

		// Set externalTenantId if available in document
		if (document.containsKey("externalTenantId")) {
			connectionInfo.setExternalTenantId(document.getString("externalTenantId"));
		}

		// Set domain if available
		if (document.containsKey("domain")) {
			connectionInfo.setDomain(document.getString("domain"));
		}

		// Set lastConnected if available
		if (document.containsKey(LAST_CONNECTED_AT)) {
			Date lastConnectedDate = document.getDate(LAST_CONNECTED_AT);
			if (lastConnectedDate != null) {
				connectionInfo.setLastConnected(
						lastConnectedDate.toInstant()
								.atZone(ZoneId.systemDefault())
								.toLocalDateTime());
			}
		}

		// Set displayName if available
		if (document.containsKey("displayName")) {
			connectionInfo.setDisplayName(document.getString("displayName"));
		}

		// Map Token subdocument
		if (document.containsKey("token")) {
			Document tokenDoc = document.get("token", Document.class);
			Token token = mapTokenFromSubdocument(tokenDoc);
			if (token != null) {
				connectionInfo.setToken(token);
			}
		}

		// Map timestamps
		if (document.containsKey("createdAt")) {
			Date createdDate = document.getDate("createdAt");
			if (createdDate != null) {
				connectionInfo.setCreatedAt(
						createdDate.toInstant()
								.atZone(ZoneId.systemDefault())
								.toLocalDateTime());
			}
		}

		if (document.containsKey("updatedAt")) {
			Date updatedDate = document.getDate("updatedAt");
			if (updatedDate != null) {
				connectionInfo.setUpdatedAt(
						updatedDate.toInstant()
								.atZone(ZoneId.systemDefault())
								.toLocalDateTime());
			}
		}

		// Set connection status based on token availability and lastConnectedAt presence
		boolean hasValidToken = connectionInfo.getToken() != null && connectionInfo.getToken().getToken() != null;
		boolean hasRecentConnection = document.containsKey(LAST_CONNECTED_AT) && document.getDate(LAST_CONNECTED_AT) != null;
		connectionInfo.setStatus(hasValidToken && hasRecentConnection ? ConnectionStatus.ACTIVE : ConnectionStatus.INACTIVE);

		return connectionInfo;
	}

	@Override
	protected Document entityToDocument(ConnectionInfo connectionInfo) {
		Document document = new Document();

		// Set the MongoDB document _id field
		document.append("_id", connectionInfo.getId().toString());
		document.append("customerId", connectionInfo.getCustomerId().toString());

		// Map the service to serviceType
		if (connectionInfo.getService() != null) {
			document.append("serviceType", connectionInfo.getService().name());
		}

		// Map externalTenantId if available
		if (connectionInfo.getExternalTenantId() != null) {
			document.append("externalTenantId", connectionInfo.getExternalTenantId());
		}

		// Map domain if available
		if (connectionInfo.getDomain() != null) {
			document.append("domain", connectionInfo.getDomain());
		}

		// Map displayName if available
		if (connectionInfo.getDisplayName() != null) {
			document.append("displayName", connectionInfo.getDisplayName());
		}

		// Map lastConnected to lastConnectedAt if available
		if (connectionInfo.getLastConnected() != null) {
			document.append(LAST_CONNECTED_AT, Date.from(
					connectionInfo.getLastConnected().atZone(ZoneId.systemDefault()).toInstant()
			));
		}

		// Map Token as subdocument
		if (connectionInfo.getToken() != null) {
			Document tokenSubdoc = mapTokenToSubdocument(connectionInfo.getToken());
			document.append("token", tokenSubdoc);
		}

		if (connectionInfo.getCreatedAt() != null) {
			document.append("createdAt", Date.from(connectionInfo.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant()));
		}

		// Set update timestamp
		if (connectionInfo.getUpdatedAt() != null) {
			document.append("updatedAt", Date.from(connectionInfo.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant()));
		}
		return document;
	}

	/**
	 * Maps Token from MongoDB subdocument.
	 *
	 * @param tokenDoc the token subdocument
	 * @return Token object or null if subdocument is null or invalid
	 */
	private Token mapTokenFromSubdocument(Document tokenDoc) {
		if (tokenDoc == null) {
			return null;
		}

		Token token = new Token();

		// Map token type
		if (tokenDoc.containsKey("tokenType")) {
			String tokenTypeStr = tokenDoc.getString("tokenType");
			try {
				token.setTokenType(TokenType.valueOf(tokenTypeStr));
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown token type: {}, defaulting to ACCESS_TOKEN", tokenTypeStr);
				token.setTokenType(TokenType.ACCESS_TOKEN);
			}
		} else {
			token.setTokenType(TokenType.ACCESS_TOKEN);
		}

		if (tokenDoc.containsKey("token")) {
			token.setToken(tokenDoc.getString("token"));
		}

		if (tokenDoc.containsKey("tokenExpiresAt")) {
			Date expiresAtDate = tokenDoc.getDate("tokenExpiresAt");
			if (expiresAtDate != null) {
				token.setTokenExpiresAt(expiresAtDate.toInstant());
			}
		}

		if (tokenDoc.containsKey("scopes")) {
			token.setScopes(tokenDoc.getString("scopes"));
		}

		if (tokenDoc.containsKey("createdAt")) {
			Date createdAtDate = tokenDoc.getDate("createdAt");
			if (createdAtDate != null) {
				token.setCreatedAt(createdAtDate.toInstant());
			}
		}

		return token;
	}

	/**
	 * Maps Token object to MongoDB subdocument.
	 *
	 * @param token the Token object to convert
	 * @return MongoDB Document representing the token
	 */
	private Document mapTokenToSubdocument(Token token) {
		if (token == null) {
			return null;
		}

		Document tokenDoc = new Document();

		// Map token type
		if (token.getTokenType() != null) {
			tokenDoc.append("tokenType", token.getTokenType().name());
		}

		// Map token value
		if (token.getToken() != null) {
			tokenDoc.append("token", token.getToken());
		}

		// Map token expiration
		if (token.getTokenExpiresAt() != null) {
			tokenDoc.append("tokenExpiresAt", Date.from(token.getTokenExpiresAt()));
		}

		// Map scopes
		if (token.getScopes() != null) {
			tokenDoc.append("scopes", token.getScopes());
		}

		// Map token creation time
		if (token.getCreatedAt() != null) {
			tokenDoc.append("createdAt", Date.from(token.getCreatedAt()));
		}

		return tokenDoc;
	}
}
