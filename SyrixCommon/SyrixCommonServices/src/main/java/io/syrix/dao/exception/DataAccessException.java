package io.syrix.dao.exception;

/**
 * Base exception for all data access exceptions in the Syrix DAO layer.
 * This is a runtime exception, so it does not need to be declared in method signatures.
 */
public class DataAccessException extends RuntimeException {

    /**
     * Constructs a new DataAccessException with the specified message.
     *
     * @param message the detail message
     */
    public DataAccessException(String message) {
        super(message);
    }

    /**
     * Constructs a new DataAccessException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public DataAccessException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new DataAccessException with the specified cause.
     *
     * @param cause the cause
     */
    public DataAccessException(Throwable cause) {
        super(cause);
    }
}
