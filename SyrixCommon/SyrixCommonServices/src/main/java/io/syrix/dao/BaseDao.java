package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.exception.EntityNotFoundException;
import io.syrix.dao.exception.UpdateFailedException;

import java.util.List;
import java.util.Optional;

/**
 * Generic Data Access Object (DAO) interface providing common CRUD operations.
 *
 * @param <T> Type of entity this DAO works with
 * @param <ID> Type of the entity's primary key/identifier
 */
public interface BaseDao<T, ID> {

    /**
     * Find an entity by its identifier.
     *
     * @param id The entity identifier
     * @return An Optional containing the entity if found, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<T> findById(ID id) throws DataAccessException;

    /**
     * Find all entities.
     *
     * @return A list of all entities
     * @throws DataAccessException If a data access error occurs
     */
    List<T> findAll() throws DataAccessException;

    /**
     * Save or update an entity.
     *
     * @param entity The entity to save or update
     * @return The saved entity (may include updated information, e.g., generated ID)
     * @throws UpdateFailedException If the entity could not be saved or updated
     * @throws DataAccessException If a data access error occurs
     */
    T save(T entity) throws DataAccessException;

    /**
     * Delete an entity by its identifier.
     *
     * @param id The entity identifier
     * @throws EntityNotFoundException If the entity does not exist
     * @throws DataAccessException If a data access error occurs
     */
    void deleteById(ID id) throws DataAccessException;
    
    /**
     * Delete an entity.
     *
     * @param entity The entity to delete
     * @throws EntityNotFoundException If the entity does not exist
     * @throws DataAccessException If a data access error occurs
     */
    void delete(T entity) throws DataAccessException;
    
    /**
     * Check if an entity with the given ID exists.
     *
     * @param id The entity identifier
     * @return true if an entity exists with the given ID, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean existsById(ID id) throws DataAccessException;
    
    /**
     * Count the number of entities.
     *
     * @return The number of entities
     * @throws DataAccessException If a data access error occurs
     */
    long count() throws DataAccessException;
}
