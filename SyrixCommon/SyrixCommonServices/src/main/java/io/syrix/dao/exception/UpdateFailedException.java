package io.syrix.dao.exception;

/**
 * Exception thrown when a data update operation cannot be performed.
 */
public class UpdateFailedException extends DataAccessException {

    /**
     * Constructs a new UpdateFailedException with the specified message.
     *
     * @param message the detail message
     */
    public UpdateFailedException(String message) {
        super(message);
    }

    /**
     * Constructs a new UpdateFailedException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public UpdateFailedException(String message, Throwable cause) {
        super(message, cause);
    }
}
