package io.syrix.dao.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.ReplaceOptions;
import com.mongodb.client.result.DeleteResult;
import io.syrix.dao.BaseDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.exception.EntityNotFoundException;
import io.syrix.dao.exception.UpdateFailedException;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Abstract base implementation of the BaseDao interface for MongoDB.
 * This provides a generic implementation that can be extended for specific entity types.
 *
 * @param <T> The entity type
 * @param <ID> The ID type of the entity
 */
public abstract class AbstractMongoBaseDao<T, ID> extends AbstractMongoDao implements BaseDao<T, ID> {

    private static final Logger logger = LoggerFactory.getLogger(AbstractMongoBaseDao.class);

    private final String collectionName;

    /**
     * Create a new AbstractMongoBaseDao for the specified entity type.
     *
     * @param database The MongoDB database
     * @param collectionName The name of the MongoDB collection
     */
    protected AbstractMongoBaseDao(MongoDatabase database, String collectionName) {
        super(database);
        this.collectionName = collectionName;
    }

    @Override
    public Optional<T> findById(ID id) throws DataAccessException {
        logger.debug("Finding entity by ID: {}", id);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);
            Document doc = collection.find(createIdFilter(id)).first();

            if (doc == null) {
                return Optional.empty();
            }

            return Optional.of(documentToEntity(doc));
        } catch (Exception e) {
            logger.error("Error finding entity by ID {}: {}", id, e.getMessage(), e);
            throw new DataAccessException("Failed to find entity by ID: " + id, e);
        }
    }

    @Override
    public List<T> findAll() throws DataAccessException {
        logger.debug("Finding all entities in collection: {}", collectionName);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);
            FindIterable<Document> docs = collection.find();

            List<T> result = new ArrayList<>();
            for (Document doc : docs) {
                result.add(documentToEntity(doc));
            }

            return result;
        } catch (Exception e) {
            logger.error("Error finding all entities: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to find all entities", e);
        }
    }

    @Override
    public T save(T entity) throws DataAccessException {
        ID id = getEntityId(entity);
        if (id == null) {
            return create(entity);
        } else {
            return update(entity);
        }
    }

    @Override
    public void deleteById(ID id) throws DataAccessException {
        logger.debug("Deleting entity by ID: {}", id);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);

            // Delete the document
            Bson filter = createIdFilter(id);
            DeleteResult result = collection.deleteOne(filter);

            if (result.getDeletedCount() == 0) {
                if (existsById(id)) {
                    logger.warn("Entity found but not deleted. ID: {}", id);
                    throw new UpdateFailedException("Entity found but not deleted: " + id);
                } else {
                    logger.warn("Entity not found with ID: {}", id);
                    throw new EntityNotFoundException("Entity not found with ID: " + id);
                }
            }
        } catch (EntityNotFoundException | UpdateFailedException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting entity by ID {}: {}", id, e.getMessage(), e);
            throw new DataAccessException("Failed to delete entity by ID: " + id, e);
        }
    }

    @Override
    public void delete(T entity) throws DataAccessException {
        ID id = getEntityId(entity);
        if (id == null) {
            throw new DataAccessException("Cannot delete entity without ID");
        }
        deleteById(id);
    }

    @Override
    public boolean existsById(ID id) throws DataAccessException {
        logger.debug("Checking if entity exists by ID: {}", id);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);
            long count = collection.countDocuments(createIdFilter(id));
            return count > 0;
        } catch (Exception e) {
            logger.error("Error checking if entity exists by ID {}: {}", id, e.getMessage(), e);
            throw new DataAccessException("Failed to check if entity exists by ID: " + id, e);
        }
    }

    @Override
    public long count() throws DataAccessException {
        logger.debug("Counting all entities in collection: {}", collectionName);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);
            return collection.countDocuments();
        } catch (Exception e) {
            logger.error("Error counting entities: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to count entities", e);
        }
    }

    /**
     * Create a new entity in the database.
     *
     * @param entity The entity to create
     * @return The created entity
     * @throws DataAccessException If a data access error occurs
     */
    protected T create(T entity) throws DataAccessException {
        logger.debug("Creating new entity");

        try {
            MongoCollection<Document> collection = getCollection(collectionName);

            // Prepare entity for creation (e.g., generate ID)
            T preparedEntity = prepareForCreate(entity);

            // Convert entity to document
            Document doc = entityToDocument(preparedEntity);

            // Insert the document
            collection.insertOne(doc);

            return preparedEntity;
        } catch (Exception e) {
            logger.error("Error creating entity: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to create entity", e);
        }
    }

    /**
     * Update an existing entity in the database.
     *
     * @param entity The entity to update
     * @return The updated entity
     * @throws DataAccessException If a data access error occurs
     */
    protected T update(T entity) throws DataAccessException {
        ID id = getEntityId(entity);
        logger.debug("Updating entity by ID: {}", id);

        try {
            MongoCollection<Document> collection = getCollection(collectionName);

            // Prepare entity for update (e.g., set updated timestamp)
            T preparedEntity = prepareForUpdate(entity);

            // Convert entity to document
            Document doc = entityToDocument(preparedEntity);

            // Create filter and replacement document
            Bson filter = createIdFilter(id);

            // Replace the document
            ReplaceOptions options = new ReplaceOptions().upsert(false);
            collection.replaceOne(filter, doc, options);

            // Get the updated document
            Document updatedDoc = collection.find(filter).first();
            if (updatedDoc == null) {
                throw new EntityNotFoundException("Entity not found with ID: " + id);
            }

            return documentToEntity(updatedDoc);
        } catch (EntityNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating entity: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to update entity", e);
        }
    }

    /**
     * Create a MongoDB filter for the entity ID.
     *
     * @param id The entity ID
     * @return The MongoDB filter
     */
    protected Bson createIdFilter(ID id) {
        return Filters.eq("_id", convertIdToString(id));
    }

    /**
     * Convert an entity ID to a string representation for MongoDB.
     * By default, this will call the ID's toString() method.
     * Override this method for types that require special handling.
     *
     * @param id The entity ID
     * @return The string representation of the ID
     */
    protected String convertIdToString(ID id) {
        return id.toString();
    }

    /**
     * Prepare an entity for creation.
     * This method should set any default or system-managed values (e.g., ID, creation timestamp).
     *
     * @param entity The entity to prepare
     * @return The prepared entity
     */
    protected abstract T prepareForCreate(T entity);

    /**
     * Prepare an entity for update.
     * This method should set any system-managed values (e.g., updated timestamp).
     *
     * @param entity The entity to prepare
     * @return The prepared entity
     */
    protected abstract T prepareForUpdate(T entity);

    /**
     * Get the ID from an entity.
     *
     * @param entity The entity
     * @return The entity ID
     */
    protected abstract ID getEntityId(T entity);

    /**
     * Convert a MongoDB document to an entity.
     *
     * @param document The MongoDB document
     * @return The entity
     */
    protected abstract T documentToEntity(Document document);

    /**
     * Convert an entity to a MongoDB document.
     *
     * @param entity The entity
     * @return The MongoDB document
     */
    protected abstract Document entityToDocument(T entity);
}