package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.OAuthConfig;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object interface for OAuth2 configurations.
 * This interface defines operations for accessing and manipulating OAuth2 configuration data.
 */
public interface OAuthConfigurationDao {
    
    /**
     * Get all OAuth2 configurations.
     *
     * @return A list of OAuthConfig objects
     * @throws DataAccessException If a data access error occurs
     */
    List<OAuthConfig> getAllConfigurations() throws DataAccessException;
    
    /**
     * Get OAuth2 configuration for a specific service.
     *
     * @param serviceType The service type (e.g., "office365", "google")
     * @return An optional containing the OAuthConfig object, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<OAuthConfig> getConfigurationByService(ServiceType serviceType) throws DataAccessException;
    
    /**
     * Check if a configuration exists for a specific service.
     *
     * @param serviceType The service type
     * @return true if configuration exists, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean hasConfiguration(ServiceType serviceType) throws DataAccessException;

    /**
     * Delete an OAuth2 configuration.
     *
     * @param serviceType The service type to delete configuration for
     * @return true if deleted successfully, false otherwise
     * @throws DataAccessException If a data access error occurs
     */
    boolean deleteConfiguration(ServiceType serviceType) throws DataAccessException;
/**
     * Find an OAuth2 configuration by its unique identifier.
     *
     * @param id The unique identifier of the OAuth configuration.
     * @return An optional containing the OAuthConfig object if found, or empty if not found.
     */
    Optional<OAuthConfig> findById(UUID id);

    /**
     * Find all OAuth2 configurations with a specific connection status.
     *
     * @param connectionStatus The connection status to filter configurations by.
     * @return A list of OAuthConfig objects matching the specified connection status.
     */
    List<OAuthConfig> findByStatus(ConnectionStatus connectionStatus);

    /**
     * Find an OAuth2 configuration for a specific service type.
     *
     * @param service The service type to search for (e.g., "office365", "google").
     * @return An optional containing the OAuthConfig object if found, or empty if not found.
     */
    Optional<OAuthConfig> findByService(ServiceType service);

    /**
     * Save or update an OAuth2 configuration.
     *
     * @param config The OAuth2 configuration to save or update.
     * @return The saved or updated OAuthConfig object.
     */
    OAuthConfig save(OAuthConfig config);

    /**
     * Delete an OAuth2 configuration by its unique identifier.
     *
     * @param id The unique identifier of the OAuth2 configuration to delete.
     */
    void deleteById(UUID id);
}
