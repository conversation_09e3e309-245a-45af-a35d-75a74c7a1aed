package io.syrix.dao.impl.mongodb.task.remediation;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoBaseDao;
import io.syrix.dao.task.remediation.RemediationTaskDao;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.datamodel.task.TaskType;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.datamodel.task.remediation.exchange.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationTask;
import io.syrix.datamodel.task.remediation.sharepoint.SharingCapabilityEntity;
import io.syrix.datamodel.task.remediation.teams.TeamsBroadcastRecordingStrategy;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationStrategy;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationTask;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * MongoDB implementation of RemediationTaskDao.
 * Manages RemediationTask entities in MongoDB using polymorphic storage pattern.
 */
public class MongoRemediationTaskDao extends AbstractMongoBaseDao<RemediationTask, UUID> implements RemediationTaskDao {
	
	private static final Logger logger = LoggerFactory.getLogger(MongoRemediationTaskDao.class);
	private static final String COLLECTION_NAME = "tasks";
	private static final String TYPE_FIELD = "taskType";

	/**
	 * Constructs MongoRemediationTaskDao with the specified MongoDB database.
	 *
	 * @param database The MongoDB database instance
	 */
	public MongoRemediationTaskDao(MongoDatabase database) {
		super(database, COLLECTION_NAME);
	}

	@Override
	public Optional<RemediationTask> findTaskByIdAndCustomer(UUID taskId, UUID customerId) throws DataAccessException {
		logger.debug("Finding RemediationTask by taskId: {}, customerId: {}", taskId, customerId);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			
			Bson filter = Filters.and(
				Filters.eq("_id", taskId.toString()),
				Filters.eq("customerId", customerId.toString()),
				Filters.eq(TYPE_FIELD, TaskType.REMEDIATE_CONFIGURATION.name())
			);
			
			Document doc = collection.find(filter).first();
			
			if (doc == null) {
				logger.debug("No RemediationTask found for taskId: {}, customerId: {}", taskId, customerId);
				return Optional.empty();
			}
			
			RemediationTask task = documentToEntity(doc);
			logger.debug("Successfully found RemediationTask with id: {}", task.getId());
			return Optional.of(task);
			
		} catch (Exception e) {
			logger.error("Error finding RemediationTask by id and customer: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to find RemediationTask by taskId: " + taskId + ", customerId: " + customerId, e);
		}
	}

	@Override
	protected RemediationTask prepareForCreate(RemediationTask entity) {
		if (entity.getId() == null) {
			entity.setId(UUID.randomUUID());
		}
		
		// Set task type if not already set
		if (entity.getType() == null) {
			entity.setType(TaskType.REMEDIATE_CONFIGURATION);
		}
		
		// Set creation timestamp if not set
		if (entity.getCreatedAt() == null) {
			entity.setCreatedAt(Instant.now());
		}
		
		return entity;
	}

	@Override
	protected RemediationTask prepareForUpdate(RemediationTask entity) {
		// No specific update preparation needed for RemediationTask
		return entity;
	}

	@Override
	protected UUID getEntityId(RemediationTask entity) {
		return entity.getId();
	}

	@Override
	protected RemediationTask documentToEntity(Document document) {
		RemediationTask task = new RemediationTask();
		
		// Set base entity fields
		task.setId(UUID.fromString(document.getString("_id")));
		
		String customerIdStr = document.getString("customerId");
		if (customerIdStr != null) {
			task.setCustomerId(UUID.fromString(customerIdStr));
		}
		
		String typeStr = document.getString(TYPE_FIELD);
		if (typeStr != null) {
			task.setType(TaskType.valueOf(typeStr));
		}
		
		String statusStr = document.getString("status");
		if (statusStr != null) {
			task.setStatus(io.syrix.datamodel.task.TaskStatus.valueOf(statusStr));
		}
		
		Date createdAtDate = document.getDate("createdAt");
		if (createdAtDate != null) {
			task.setCreatedAt(createdAtDate.toInstant());
		}
		
		// Set RemediationTask specific fields
		task.setRetrieveTaskId(document.getString("retrieveTaskId"));
		
		String connectionIdStr = document.getString("connectionId");
		if (connectionIdStr != null) {
			task.setConnectionId(UUID.fromString(connectionIdStr));
		}
		
		Date retrieveDateDate = document.getDate("retrieveDate");
		if (retrieveDateDate != null) {
			task.setRetrieveDate(retrieveDateDate.toInstant());
		}
		
		List<String> serviceTypeNames = document.getList("serviceType", String.class);
		if (serviceTypeNames != null) {
			List<ConfigurationServiceType> serviceTypes = new ArrayList<>();
			for (String name : serviceTypeNames) {
				try {
					serviceTypes.add(ConfigurationServiceType.valueOf(name));
				} catch (IllegalArgumentException e) {
					logger.warn("Unknown ConfigurationServiceType: {}", name);
				}
			}
			task.setServiceType(serviceTypes);
		}
		
		// Handle SharePoint remediation task
		Document spDoc = document.get("spRemediationTask", Document.class);
		if (spDoc != null) {
			SharepointRemediationTask spTask = deserializeSharepointTask(spDoc);
			task.setSpRemediationTask(spTask);
		}
		
		// Handle Teams remediation task
		Document teamsDoc = document.get("teamsRemediationTask", Document.class);
		if (teamsDoc != null) {
			TeamsRemediationTask teamsTask = deserializeTeamsTask(teamsDoc);
			task.setTeamsRemediationTask(teamsTask);
		}
		
		// Handle Exchange remediation task
		Document exchangeDoc = document.get("exchangeRemediationTask", Document.class);
		if (exchangeDoc != null) {
			ExchangeRemediationTask exchangeTask = deserializeExchangeTask(exchangeDoc);
			task.setExchangeRemediationTask(exchangeTask);
		}
		
		return task;
	}

	@Override
	protected Document entityToDocument(RemediationTask entity) {
		Document doc = new Document("_id", entity.getId().toString())
			.append("customerId", entity.getCustomerId() != null ? entity.getCustomerId().toString() : null)
			.append(TYPE_FIELD, entity.getType() != null ? entity.getType().name() : null)
			.append("status", entity.getStatus() != null ? entity.getStatus().name() : null)
			.append("createdAt", entity.getCreatedAt())
			.append("retrieveTaskId", entity.getRetrieveTaskId())
			.append("connectionId", entity.getConnectionId() != null ? entity.getConnectionId().toString() : null)
			.append("retrieveDate", entity.getRetrieveDate());
		
		// Handle service types
		if (entity.getServiceType() != null) {
			List<String> serviceTypeNames = new ArrayList<>();
			for (ConfigurationServiceType serviceType : entity.getServiceType()) {
				serviceTypeNames.add(serviceType.name());
			}
			doc.append("serviceType", serviceTypeNames);
		}
		
		// Handle SharePoint remediation task
		if (entity.getSpRemediationTask() != null) {
			Document spDoc = serializeSharepointTask(entity.getSpRemediationTask());
			doc.append("spRemediationTask", spDoc);
		}
		
		// Handle Teams remediation task
		if (entity.getTeamsRemediationTask() != null) {
			Document teamsDoc = serializeTeamsTask(entity.getTeamsRemediationTask());
			doc.append("teamsRemediationTask", teamsDoc);
		}
		
		// Handle Exchange remediation task
		if (entity.getExchangeRemediationTask() != null) {
			Document exchangeDoc = serializeExchangeTask(entity.getExchangeRemediationTask());
			doc.append("exchangeRemediationTask", exchangeDoc);
		}
		
		return doc;
	}

	// SharePoint serialization/deserialization methods
	
	/**
	 * Serializes SharePoint remediation task to MongoDB document
	 * @param task SharePoint remediation task
	 * @return MongoDB document
	 */
	private Document serializeSharepointTask(SharepointRemediationTask task) {
		Document doc = new Document();
		
		if (task.getPolicyIds() != null) {
			doc.append("policyIds", task.getPolicyIds());
		}
		
		if (task.getRemediationConfig() != null) {
			Document configDoc = serializeSharepointConfig(task.getRemediationConfig());
			doc.append("remediationConfig", configDoc);
		}
		
		return doc;
	}
	
	/**
	 * Deserializes SharePoint remediation task from MongoDB document
	 * @param doc MongoDB document
	 * @return SharePoint remediation task
	 */
	private SharepointRemediationTask deserializeSharepointTask(Document doc) {
		SharepointRemediationTask task = new SharepointRemediationTask();

		List<String> policyIds = doc.getList("policyIds", String.class);
		if (policyIds != null) {
			task.setPolicyIds(policyIds);
		}
		
		Document configDoc = doc.get("remediationConfig", Document.class);
		if (configDoc != null) {
			SharepointRemediationConfig config = deserializeSharepointConfig(configDoc);
			task.setRemediationConfig(config);
		}
		
		return task;
	}
	
	/**
	 * Serializes SharePoint configuration to MongoDB document
	 */
	private Document serializeSharepointConfig(SharepointRemediationConfig config) {
		Document doc = new Document();
		
		if (config.sharingCapability != null) {
			doc.append("sharingCapability", config.sharingCapability.name());
		}
		
		if (config.odbSharingCapability != null) {
			doc.append("odbSharingCapability", config.odbSharingCapability.name());
		}
		
		if (config.allowedDomains != null) {
			doc.append("allowedDomains", config.allowedDomains);
		}
		
		return doc;
	}
	
	/**
	 * Deserializes SharePoint configuration from MongoDB document
	 */
	private SharepointRemediationConfig deserializeSharepointConfig(Document doc) {
		SharepointRemediationConfig config = new SharepointRemediationConfig();
		
		String sharingCapabilityStr = doc.getString("sharingCapability");
		if (sharingCapabilityStr != null) {
			try {
				config.sharingCapability = SharingCapabilityEntity.valueOf(sharingCapabilityStr);
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown SharingCapabilityEntity: {}", sharingCapabilityStr);
			}
		}
		
		String odbSharingCapabilityStr = doc.getString("odbSharingCapability");
		if (odbSharingCapabilityStr != null) {
			try {
				config.odbSharingCapability = SharingCapabilityEntity.valueOf(odbSharingCapabilityStr);
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown ODB SharingCapabilityEntity: {}", odbSharingCapabilityStr);
			}
		}

		List<String> allowedDomains = doc.getList("allowedDomains", String.class);
		if (allowedDomains != null) {
			config.allowedDomains = allowedDomains;
		}
		
		return config;
	}
	
	// Teams serialization/deserialization methods
	
	/**
	 * Serializes Teams remediation task to MongoDB document
	 * @param task Teams remediation task
	 * @return MongoDB document
	 */
	private Document serializeTeamsTask(TeamsRemediationTask task) {
		Document doc = new Document();
		
		if (task.getPolicyIds() != null) {
			doc.append("policyIds", task.getPolicyIds());
		}
		
		if (task.getRemediationConfig() != null) {
			Document configDoc = serializeTeamsConfig(task.getRemediationConfig());
			doc.append("remediationConfig", configDoc);
		}
		
		return doc;
	}
	
	/**
	 * Deserializes Teams remediation task from MongoDB document
	 * @param doc MongoDB document
	 * @return Teams remediation task
	 */
	private TeamsRemediationTask deserializeTeamsTask(Document doc) {
		TeamsRemediationTask task = new TeamsRemediationTask();

		List<String> policyIds = doc.getList("policyIds", String.class);
		if (policyIds != null) {
			task.setPolicyIds(policyIds);
		}
		
		Document configDoc = doc.get("remediationConfig", Document.class);
		if (configDoc != null) {
			TeamsRemediationConfig config = deserializeTeamsConfig(configDoc);
			task.setRemediationConfig(config);
		}
		
		return task;
	}
	
	/**
	 * Serializes Teams configuration to MongoDB document
	 */
	private Document serializeTeamsConfig(TeamsRemediationConfig config) {
		Document doc = new Document();
		
		if (config.getAutoAdmittedUsersStrategy() != null) {
			doc.append("autoAdmittedUsersStrategy", config.getAutoAdmittedUsersStrategy().name());
		}
		
		if (config.getBroadcastRecordingModeStrategy() != null) {
			doc.append("broadcastRecordingModeStrategy", config.getBroadcastRecordingModeStrategy().name());
		}
		
		return doc;
	}
	
	/**
	 * Deserializes Teams configuration from MongoDB document
	 */
	private TeamsRemediationConfig deserializeTeamsConfig(Document doc) {
		TeamsRemediationConfig config = new TeamsRemediationConfig();
		
		String autoAdmittedUsersStrategyStr = doc.getString("autoAdmittedUsersStrategy");
		if (autoAdmittedUsersStrategyStr != null) {
			try {
				config.setAutoAdmittedUsersStrategy(TeamsRemediationStrategy.valueOf(autoAdmittedUsersStrategyStr));
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown TeamsRemediationStrategy: {}", autoAdmittedUsersStrategyStr);
			}
		}
		
		String broadcastRecordingModeStrategyStr = doc.getString("broadcastRecordingModeStrategy");
		if (broadcastRecordingModeStrategyStr != null) {
			try {
				config.setBroadcastRecordingModeStrategy(TeamsBroadcastRecordingStrategy.valueOf(broadcastRecordingModeStrategyStr));
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown TeamsBroadcastRecordingStrategy: {}", broadcastRecordingModeStrategyStr);
			}
		}
		
		return config;
	}
	
	// Exchange serialization/deserialization methods
	
	/**
	 * Serializes Exchange remediation task to MongoDB document
	 * @param task Exchange remediation task
	 * @return MongoDB document
	 */
	private Document serializeExchangeTask(ExchangeRemediationTask task) {
		Document doc = new Document();
		
		if (task.getPolicyIds() != null) {
			doc.append("policyIds", task.getPolicyIds());
		}
		
		if (task.getRemediationConfig() != null) {
			Document configDoc = serializeExchangeConfig(task.getRemediationConfig());
			doc.append("remediationConfig", configDoc);
		}
		
		return doc;
	}
	
	/**
	 * Deserializes Exchange remediation task from MongoDB document
	 * @param doc MongoDB document
	 * @return Exchange remediation task
	 */
	private ExchangeRemediationTask deserializeExchangeTask(Document doc) {
		ExchangeRemediationTask task = new ExchangeRemediationTask();

		List<String> policyIds = doc.getList("policyIds", String.class);
		if (policyIds != null) {
			task.setPolicyIds(policyIds);
		}
		
		Document configDoc = doc.get("remediationConfig", Document.class);
		if (configDoc != null) {
			ExchangeRemediationConfig config = deserializeExchangeConfig(configDoc);
			task.setRemediationConfig(config);
		}
		
		return task;
	}
	
	/**
	 * Serializes Exchange configuration to MongoDB document
	 */
	private Document serializeExchangeConfig(ExchangeRemediationConfig config) {
		Document doc = new Document();
		
		if (config.getDefaultDomain() != null) {
			doc.append("defaultDomain", config.getDefaultDomain());
		}
		
		if (config.getAttachmentFilterConfig() != null) {
			Document attachmentDoc = serializeAttachmentFilterConfig(config.getAttachmentFilterConfig());
			doc.append("attachmentFilterConfig", attachmentDoc);
		}
		
		if (config.getAntiPhishingConfig() != null) {
			Document antiPhishingDoc = serializeAntiPhishingConfig(config.getAntiPhishingConfig());
			doc.append("antiPhishingConfig", antiPhishingDoc);
		}
		
		return doc;
	}
	
	/**
	 * Deserializes Exchange configuration from MongoDB document
	 */
	private ExchangeRemediationConfig deserializeExchangeConfig(Document doc) {
		ExchangeRemediationConfig config = new ExchangeRemediationConfig();
		
		config.setDefaultDomain(doc.getString("defaultDomain"));
		
		Document attachmentDoc = doc.get("attachmentFilterConfig", Document.class);
		if (attachmentDoc != null) {
			AttachmentFilterConfig attachmentConfig = deserializeAttachmentFilterConfig(attachmentDoc);
			config.setAttachmentFilterConfig(attachmentConfig);
		}
		
		Document antiPhishingDoc = doc.get("antiPhishingConfig", Document.class);
		if (antiPhishingDoc != null) {
			AntiPhishingRemediationConfig antiPhishingConfig = deserializeAntiPhishingConfig(antiPhishingDoc);
			config.setAntiPhishingConfig(antiPhishingConfig);
		}
		
		return config;
	}
	
	/**
	 * Serializes attachment filter configuration to MongoDB document
	 */
	private Document serializeAttachmentFilterConfig(AttachmentFilterConfig config) {
		Document doc = new Document();
		
		doc.append("policyName", config.getPolicyName())
			.append("ruleName", config.getRuleName())
			.append("adminDisplayName", config.getAdminDisplayName())
			.append("zapEnabled", config.getZapEnabled());
		
		if (config.getFilesTypeList() != null) {
			doc.append("filesTypeList", config.getFilesTypeList().name());
		}
		
		if (config.getAction() != null) {
			doc.append("action", config.getAction().name());
		}
		
		if (config.getRecipientDomainIs() != null) {
			doc.append("recipientDomainIs", config.getRecipientDomainIs());
		}
		
		if (config.getSentTo() != null) {
			doc.append("sentTo", config.getSentTo());
		}
		
		if (config.getSentToMemberOf() != null) {
			doc.append("sentToMemberOf", config.getSentToMemberOf());
		}
		
		return doc;
	}
	
	/**
	 * Deserializes attachment filter configuration from MongoDB document
	 */
	private AttachmentFilterConfig deserializeAttachmentFilterConfig(Document doc) {
		AttachmentFilterConfig config = new AttachmentFilterConfig();
		
		config.setPolicyName(doc.getString("policyName"));
		config.setRuleName(doc.getString("ruleName"));
		config.setAdminDisplayName(doc.getString("adminDisplayName"));
		config.setZapEnabled(doc.getBoolean("zapEnabled"));
		
		String filesTypeListStr = doc.getString("filesTypeList");
		if (filesTypeListStr != null) {
			try {
				config.setFilesTypeList(AttachmentFileTypeList.valueOf(filesTypeListStr));
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown AttachmentFileTypeList: {}", filesTypeListStr);
			}
		}
		
		String actionStr = doc.getString("action");
		if (actionStr != null) {
			try {
				config.setAction(AttachmentFilterAction.valueOf(actionStr));
			} catch (IllegalArgumentException e) {
				logger.warn("Unknown AttachmentFilterAction: {}", actionStr);
			}
		}

		List<String> recipientDomainIs = doc.getList("recipientDomainIs", String.class);
		if (recipientDomainIs != null) {
			config.setRecipientDomainIs(recipientDomainIs);
		}

		List<String> sentTo = doc.getList("sentTo", String.class);
		if (sentTo != null) {
			config.setSentTo(sentTo);
		}

		List<String> sentToMemberOf = doc.getList("sentToMemberOf", String.class);
		if (sentToMemberOf != null) {
			config.setSentToMemberOf(sentToMemberOf);
		}
		
		return config;
	}
	
	/**
	 * Serializes anti-phishing configuration to MongoDB document
	 */
	private Document serializeAntiPhishingConfig(AntiPhishingRemediationConfig config) {
		Document doc = new Document();
		
		doc.append("policyName", config.getPolicyName())
			.append("enableFirstContactSafetyTips", config.getEnableFirstContactSafetyTips())
			.append("enableSimilarUsersSafetyTips", config.getEnableSimilarUsersSafetyTips())
			.append("enableSimilarDomainsSafetyTips", config.getEnableSimilarDomainsSafetyTips())
			.append("enableUnusualCharactersSafetyTips", config.getEnableUnusualCharactersSafetyTips())
			.append("enableMailboxIntelligence", config.getEnableMailboxIntelligence())
			.append("mailboxIntelligenceAction", config.getMailboxIntelligenceAction())
			.append("phishThresholdLevel", config.getPhishThresholdLevel());

		
		if (config.getProtectedUsers() != null) {
			doc.append("protectedUsers", config.getProtectedUsers());
		}
		
		if (config.getProtectedDomains() != null) {
			doc.append("protectedDomains", config.getProtectedDomains());
		}
		
		if (config.getRecipientDomain() != null) {
			doc.append("recipientDomain", config.getRecipientDomain());
		}
		
		return doc;
	}
	
	/**
	 * Deserializes anti-phishing configuration from MongoDB document
	 */
	private AntiPhishingRemediationConfig deserializeAntiPhishingConfig(Document doc) {
		AntiPhishingRemediationConfig config = new AntiPhishingRemediationConfig();
		
		config.setPolicyName(doc.getString("policyName"));
		config.setEnableFirstContactSafetyTips(doc.getBoolean("enableFirstContactSafetyTips"));
		config.setEnableSimilarUsersSafetyTips(doc.getBoolean("enableSimilarUsersSafetyTips"));
		config.setEnableSimilarDomainsSafetyTips(doc.getBoolean("enableSimilarDomainsSafetyTips"));
		config.setEnableUnusualCharactersSafetyTips(doc.getBoolean("enableUnusualCharactersSafetyTips"));
		config.setEnableMailboxIntelligence(doc.getBoolean("enableMailboxIntelligence"));
		config.setMailboxIntelligenceAction(doc.getString("mailboxIntelligenceAction"));
		config.setPhishThresholdLevel(doc.getInteger("phishThresholdLevel"));

		List<String> protectedUsers = doc.getList("protectedUsers", String.class);
		if (protectedUsers != null) {
			config.setProtectedUsers(protectedUsers);
		}

		List<String> protectedDomains = doc.getList("protectedDomains", String.class);
		if (protectedDomains != null) {
			config.setProtectedDomains(protectedDomains);
		}

		List<String> recipientDomain = doc.getList("recipientDomain", String.class);
		if (recipientDomain != null) {
			config.setRecipientDomain(recipientDomain);
		}
		
		return config;
	}
}
