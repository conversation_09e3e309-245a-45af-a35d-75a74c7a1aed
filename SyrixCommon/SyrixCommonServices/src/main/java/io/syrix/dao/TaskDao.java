package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.RetrieveTask;

import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object for Task entities.
 * Provides operations for storing and retrieving tasks with polymorphic support.
 */
public interface TaskDao extends BaseDao<Task, UUID> {

    /**
     * Save a task subclass (polymorphic save).
     * Handles storage of RetrieveTask and other Task subclasses.
     *
     * @param task The task to save (can be any Task subclass)
     * @param <T> The specific type of the task
     * @return The saved task with the same type
     * @throws DataAccessException If a data access error occurs
     */
    <T extends Task> T saveTaskSubclass(T task) throws DataAccessException;

    /**
     * Find RetrieveTask by taskId, customerId and status.
     * 
     * @param taskId The task identifier
     * @param customerId The customer identifier
     * @return Optional containing the RetrieveTask if found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<RetrieveTask> findRetrieveTaskByIdAndCustomer(
            UUID taskId,
            UUID customerId) throws DataAccessException;

    /**
     * Find Task by taskId and customerId.
     * 
     * @param taskId The task identifier
     * @param customerId The customer identifier
     * @return Optional containing the Task if found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<Task> findByIdAndCustomerId(UUID taskId, UUID customerId) throws DataAccessException;
}
