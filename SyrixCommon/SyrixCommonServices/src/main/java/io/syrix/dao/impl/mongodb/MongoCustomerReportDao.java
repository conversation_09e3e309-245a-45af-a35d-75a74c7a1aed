package io.syrix.dao.impl.mongodb;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import io.syrix.dao.CustomerReportDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoBaseDao;
import io.syrix.datamodel.report.CustomerReport;
import io.syrix.datamodel.report.PolicyResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;


/**
 * MongoDB implementation of CustomerReportDao.
 */
public class MongoCustomerReportDao extends AbstractMongoBaseDao<CustomerReport, UUID> implements CustomerReportDao {

    private static final Logger logger = LoggerFactory.getLogger(MongoCustomerReportDao.class);
    private static final String COLLECTION_NAME = "customer_reports";

    public MongoCustomerReportDao(MongoDatabase database) {
        super(database, COLLECTION_NAME);
    }

    @Override
    public Optional<CustomerReport> findLatestByCustomerId(UUID customerId) throws DataAccessException {
        logger.debug("Finding latest report for customer ID: {}", customerId);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

            Document doc = collection.find(Filters.eq("customerId", customerId.toString()))
                    .sort(Sorts.descending("createDate"))
                    .first();

            if (doc == null) {
                return Optional.empty();
            }

            return Optional.of(documentToEntity(doc));
        } catch (Exception e) {
            logger.error("Error finding latest report for customer ID {}: {}", customerId, e.getMessage(), e);
            throw new DataAccessException("Failed to find latest report for customer: " + customerId, e);
        }
    }

    @Override
    protected CustomerReport prepareForCreate(CustomerReport customerReport) {
        // Generate ID if not set
        if (customerReport.getId() == null) {
            customerReport.setId(UUID.randomUUID());
        }

        // Set timestamps
        LocalDateTime now = LocalDateTime.now();
        if (customerReport.getCreatedAt() == null) {
            customerReport.setCreatedAt(now);
        }

        return customerReport;
    }

    @Override
    protected CustomerReport prepareForUpdate(CustomerReport entity) {
        // For reports, we usually don't update the creation date
        return entity;
    }

    @Override
    protected UUID getEntityId(CustomerReport entity) {
        return entity.getId();
    }

    @Override
    protected CustomerReport documentToEntity(Document document) {
        CustomerReport report = new CustomerReport();

        // Basic fields
        String customerIdStr = document.getString("customerId");
        if (customerIdStr != null) {
            report.setCustomerId(UUID.fromString(customerIdStr));
        }

        Date createDate = document.getDate("createdAt");
        if (createDate != null) {
            report.setCreatedAt(LocalDateTime.ofInstant(createDate.toInstant(), ZoneOffset.UTC));
        }

        // Service results
        Document resultsDoc = document.get("results", Document.class);
        if (resultsDoc != null) {
            Map<ConfigurationServiceType, List<PolicyResult>> results = new HashMap<>();

            for (String serviceTypeKey : resultsDoc.keySet()) {
                try {
                    ConfigurationServiceType serviceType = ConfigurationServiceType.valueOf(serviceTypeKey);
                    List<Document> policyResultsDocs = resultsDoc.getList(serviceTypeKey, Document.class);

                    List<PolicyResult> policyResults = new ArrayList<>();
                    if (policyResultsDocs != null) {
                        for (Document policyResultDoc : policyResultsDocs) {
                            PolicyResult policyResult = documentToPolicyResult(policyResultDoc);
                            policyResults.add(policyResult);
                        }
                    }

                    results.put(serviceType, policyResults);
                } catch (IllegalArgumentException e) {
                    logger.warn("Unknown service type: {}", serviceTypeKey);
                }
            }

            report.setResults(results);
        }

        return report;
    }

    @Override
    protected Document entityToDocument(CustomerReport entity) {
        Document document = new Document();

        // Basic fields
        if (entity.getCustomerId() != null) {
            document.append("customerId", entity.getCustomerId().toString());
        }

        if (entity.getCreatedAt() != null) {
            document.append("createDate", Date.from(entity.getCreatedAt().toInstant(ZoneOffset.UTC)));
        }

        // Unique document ID
        UUID entityId = getEntityId(entity);
        if (entityId != null) {
            document.append("_id", entityId.toString());
        }

        // Service results
        if (entity.getResults() != null) {
            Document resultsDoc = new Document();

            for (Map.Entry<ConfigurationServiceType, List<PolicyResult>> entry : entity.getResults().entrySet()) {
                String serviceTypeKey = entry.getKey().name();
                List<PolicyResult> policyResults = entry.getValue();

                List<Document> policyResultsDocs = new ArrayList<>();
                if (policyResults != null) {
                    for (PolicyResult policyResult : policyResults) {
                        Document policyResultDoc = policyResultToDocument(policyResult);
                        policyResultsDocs.add(policyResultDoc);
                    }
                }

                resultsDoc.append(serviceTypeKey, policyResultsDocs);
            }

            document.append("results", resultsDoc);
        }

        return document;
    }

    /**
     * Converts Document to PolicyResult.
     */
    private PolicyResult documentToPolicyResult(Document document) {
        PolicyResult policyResult = new PolicyResult();

        policyResult.setPolicyId(document.getString("policyId"));

        Boolean requirementMet = document.getBoolean("requirementMet");
        if (requirementMet != null) {
            policyResult.setRequirementMet(requirementMet);
        }

        policyResult.setCriticality(document.getString("criticality"));
        policyResult.setReportDetails(document.getString("reportDetails"));

        return policyResult;
    }

    /**
     * Converts PolicyResult to Document.
     */
    private Document policyResultToDocument(PolicyResult policyResult) {
        Document document = new Document();

        if (policyResult.getPolicyId() != null) {
            document.append("policyId", policyResult.getPolicyId());
        }

        document.append("requirementMet", policyResult.isRequirementMet());

        if (policyResult.getCriticality() != null) {
            document.append("criticality", policyResult.getCriticality());
        }

        if (policyResult.getReportDetails() != null) {
            document.append("reportDetails", policyResult.getReportDetails());
        }

        return document;
    }
}