package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.Customer;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object for Customer entities.
 */
public interface CustomerDao extends BaseDao<Customer, UUID> {

    /**
     * Find a Customer by its name.
     *
     * @param name The Customer name
     * @return An Optional containing the Customer if found, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<Customer> findByName(String name) throws DataAccessException;
    
    /**
     * Find a Customer by its Microsoft tenant ID.
     *
     * @param microsoftTenantId The Microsoft tenant ID
     * @return An Optional containing the Customer if found, or empty if not found
     * @throws DataAccessException If a data access error occurs
     */
    Optional<Customer> findByMicrosoftTenantId(String microsoftTenantId) throws DataAccessException;

    /**
     * Find companies by status.
     *
     * @param status The status to filter by
     * @return A list of companies with the given status
     * @throws DataAccessException If a data access error occurs
     */
    List<Customer> findByStatus(String status) throws DataAccessException;
}
