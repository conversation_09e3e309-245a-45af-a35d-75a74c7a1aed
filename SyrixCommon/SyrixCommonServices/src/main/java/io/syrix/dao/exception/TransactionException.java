package io.syrix.dao.exception;

/**
 * Exception thrown when a database transaction fails.
 */
public class TransactionException extends DataAccessException {

    /**
     * Constructs a new TransactionException with the specified message.
     *
     * @param message the detail message
     */
    public TransactionException(String message) {
        super(message);
    }

    /**
     * Constructs a new TransactionException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public TransactionException(String message, Throwable cause) {
        super(message, cause);
    }
}
