package io.syrix.dao.exception;

/**
 * Exception thrown when a database operation fails due to a connectivity issue.
 */
public class DatabaseConnectionException extends DataAccessException {

    /**
     * Constructs a new DatabaseConnectionException with the specified message.
     *
     * @param message the detail message
     */
    public DatabaseConnectionException(String message) {
        super(message);
    }

    /**
     * Constructs a new DatabaseConnectionException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public DatabaseConnectionException(String message, Throwable cause) {
        super(message, cause);
    }
}
