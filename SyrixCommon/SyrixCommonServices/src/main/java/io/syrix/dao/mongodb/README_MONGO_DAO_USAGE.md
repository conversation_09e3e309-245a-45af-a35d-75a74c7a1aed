# MongoDB DAO Usage Guide

This guide explains how to use the MongoDB DAO implementation provided in the Syrix DAO module.

## Overview

The MongoDB DAO implementation provides a framework for accessing MongoDB collections through the Syrix DAO interfaces. The implementation includes:

- `MongoDatabaseProvider`: Interface for providing MongoDB database connections
- `SimpleMongoDatabaseProvider`: Standalone implementation of MongoDatabaseProvider
- `AbstractMongoDao`: Base class for MongoDB DAO implementations
- `AbstractMongoBaseDao`: Generic implementation of BaseDao for MongoDB

## Getting Started

### 1. Creating a Database Provider

First, create a `MongoDatabaseProvider` instance to manage the MongoDB connection:

```java
// Option 1: Using environment variables (SYRIX_MONGODB_CONNECTION_STRING, SYRIX_MONGODB_DATABASE)
MongoDatabaseProvider provider = new SimpleMongoDatabaseProvider();

// Option 2: Using a properties file
MongoDatabaseProvider provider = new SimpleMongoDatabaseProvider("/path/to/mongodb.properties");

// Option 3: Using explicit connection string and database name
MongoDatabaseProvider provider = new SimpleMongoDatabaseProvider(
    "mongodb://localhost:27017", "syrix");
```

### 2. Creating a DAO Factory

Create a `MongoDaoFactory` to manage your DAO instances:

```java
DaoFactory daoFactory = new MongoDaoFactory(provider);
```

### 3. Getting DAO Instances

Get the DAO instances from the factory:

```java
CompanyDao clientDao = daoFactory.getCompanyDao();
ConnectionInfoDao connectionInfoDao = daoFactory.getConnectionInfoDao();
OAuthConfigurationDao oauthConfigDao = daoFactory.getOAuthConfigurationDao();
OAuth2TokenDao oauth2TokenDao = daoFactory.getOAuth2TokenDao();
```

### 4. Using the DAOs

Use the DAOs to perform CRUD operations:

```java
// Create a new entity
Company client = new Company();
client.setName("Acme Corp");
client.setDisplayName("Acme Corporation");
client.setStatus("active");
client = clientDao.save(client);

// Find an entity by ID
Optional<Company> foundCompany = clientDao.findById(client.getId());

// Find all entities
List<Company> allCompanies = clientDao.findAll();

// Update an entity
client.setDisplayName("Updated Display Name");
client = clientDao.save(client);

// Delete an entity
clientDao.deleteById(client.getId());
```

### 5. Closing Resources

When you're done, close the DAO factory to release resources:

```java
daoFactory.close();
```

## Implementing Custom DAOs

To implement a custom DAO for a new entity type, extend `AbstractMongoBaseDao`:

```java
public class MyEntityDao extends AbstractMongoBaseDao<MyEntity, UUID> {
    
    private static final String COLLECTION_NAME = "my_entities";
    
    public MyEntityDao(MongoDatabase database) {
        super(database, COLLECTION_NAME);
    }
    
    @Override
    protected MyEntity prepareForCreate(MyEntity entity) {
        if (entity.getId() == null) {
            entity.setId(UUID.randomUUID());
        }
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        return entity;
    }
    
    @Override
    protected MyEntity prepareForUpdate(MyEntity entity) {
        entity.setUpdatedAt(LocalDateTime.now());
        return entity;
    }
    
    @Override
    protected UUID getEntityId(MyEntity entity) {
        return entity.getId();
    }
    
    @Override
    protected MyEntity documentToEntity(Document document) {
        // Convert Document to MyEntity
        MyEntity entity = new MyEntity();
        entity.setId(UUID.fromString(document.getString("_id")));
        entity.setName(document.getString("name"));
        // ... set other fields
        return entity;
    }
    
    @Override
    protected Document entityToDocument(MyEntity entity) {
        // Convert MyEntity to Document
        Document doc = new Document("_id", entity.getId().toString());
        doc.append("name", entity.getName());
        // ... add other fields
        return doc;
    }
    
    // Custom finder methods
    public List<MyEntity> findByName(String name) {
        // Implementation
    }
}
```

Then add it to your DAO factory:

```java
public class MyDaoFactory extends MongoDaoFactory {
    
    private final MyEntityDao myEntityDao;
    
    public MyDaoFactory(MongoDatabaseProvider databaseProvider) {
        super(databaseProvider);
        this.myEntityDao = new MyEntityDao(getDatabase());
    }
    
    public MyEntityDao getMyEntityDao() {
        return myEntityDao;
    }
}
```

## Configuration Properties

### Environment Variables

- `SYRIX_MONGODB_CONNECTION_STRING`: MongoDB connection string
- `SYRIX_MONGODB_DATABASE`: MongoDB database name

### Properties File

- `mongodb.connectionString`: MongoDB connection string
- `mongodb.database`: MongoDB database name

## Best Practices

1. **Use DAO Factory Pattern**: Always use a DAO factory to create and manage DAO instances.
2. **Close Resources**: Always close the DAO factory when you're done to release resources.
3. **Handle Exceptions**: All DAO methods throw `DataAccessException` or its subclasses. Implement proper exception handling.
4. **Use Transactions When Appropriate**: For operations that require atomicity, use MongoDB transactions.
5. **Follow MongoDB Best Practices**: Follow MongoDB best practices for document design and indexing.

## Example Usage with Spring Boot

When using with Spring Boot, you can create a bean for the MongoDB provider and DAO factory:

```java
@Configuration
public class MongoConfig {
    
    @Bean
    public MongoDatabaseProvider mongoDatabaseProvider() {
        return new SimpleMongoDatabaseProvider();
    }
    
    @Bean
    public DaoFactory daoFactory(MongoDatabaseProvider mongoDatabaseProvider) {
        return new MongoDaoFactory(mongoDatabaseProvider);
    }
    
    @Bean
    public CompanyDao clientDao(DaoFactory daoFactory) {
        return daoFactory.getCompanyDao();
    }
    
    // Other DAO beans as needed
}
```

Then autowire the DAOs in your services:

```java
@Service
public class CompanyService {
    
    private final CompanyDao clientDao;
    
    @Autowired
    public CompanyService(CompanyDao clientDao) {
        this.clientDao = clientDao;
    }
    
    // Service methods
}
```