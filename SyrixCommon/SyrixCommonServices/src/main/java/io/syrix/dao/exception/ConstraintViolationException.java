package io.syrix.dao.exception;

/**
 * Exception thrown when a data access operation fails due to a database constraint violation.
 */
public class ConstraintViolationException extends DataAccessException {

    /**
     * Constructs a new ConstraintViolationException with the specified message.
     *
     * @param message the detail message
     */
    public ConstraintViolationException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConstraintViolationException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConstraintViolationException(String message, Throwable cause) {
        super(message, cause);
    }
}
