package io.syrix.dao.impl.mongodb;

import com.mongodb.client.MongoDatabase;
import io.syrix.dao.*;
import io.syrix.dao.impl.mongodb.task.remediation.MongoRemediationTaskDao;
import io.syrix.dao.mongodb.MongoDatabaseProvider;
import io.syrix.dao.task.remediation.RemediationTaskDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MongoDB implementation of the DaoFactory interface without framework dependencies.
 */
public class MongoDaoFactory implements DaoFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(MongoDaoFactory.class);
    
    private final MongoDatabaseProvider databaseProvider;
    private final MongoDatabase database;
    
    // DAO instances
    private final CustomerDao customerDao;
    private final OAuthConfigurationDao oauthConfigurationDao;
    private final OAuth2TokenDao oauth2TokenDao;
    private final MongoConnectionInfoDao connectionInfoDao;
    private final SysConfigDao sysConfigDao;
    private final CustomerReportDao customerReportDao;
    private final TaskDao taskDao;
    private final RemediationTaskDao remediationTaskDao;

    /**
     * Constructs a new MongoDaoFactory with the given database provider.
     * 
     * @param databaseProvider The MongoDB database provider
     */
    public MongoDaoFactory(MongoDatabaseProvider databaseProvider) {
        this.databaseProvider = databaseProvider;
        this.database = databaseProvider.getDatabase();
        
        // Initialize DAOs
        this.customerDao = new MongoCustomerDao(database);
        this.oauthConfigurationDao = new MongoOAuthConfigurationDao(database);
        this.oauth2TokenDao = new MongoOAuth2TokenDao(database);
        this.connectionInfoDao = new MongoConnectionInfoDao(database);
        this.sysConfigDao = new MongoSysConfigDao(database);
        this.customerReportDao = new MongoCustomerReportDao(database);
        this.taskDao = new MongoTaskDao(database);
        this.remediationTaskDao = new MongoRemediationTaskDao(database);
        logger.info("MongoDB DAO Factory initialized");
    }
    
    @Override
    public CustomerDao getCustomerDao() {
        return customerDao;
    }

    @Override
    public MongoConnectionInfoDao getConnectionInfoDao() { return this.connectionInfoDao; }

    /**
     * Get the OAuth2ConfigurationDao instance.
     * 
     * @return The OAuth2ConfigurationDao
     */
    public OAuthConfigurationDao getOAuthConfigurationDao() {
        return oauthConfigurationDao;
    }
    
    /**
     * Get the OAuth2TokenDao instance.
     * 
     * @return The OAuth2TokenDao
     */
    public OAuth2TokenDao getOAuth2TokenDao() {
        return oauth2TokenDao;
    }

    /**
     * Get the SysConfigDao instance.
     *
     * @return The SysConfigDao
     */
    @Override
    public SysConfigDao getSysConfigDao() {
        return sysConfigDao;
    }

    @Override
    public CustomerReportDao getCustomerReportDao() {
        return customerReportDao;
    }

    @Override
    public TaskDao getTaskDao() {
        return taskDao;
    }

    @Override
    public RemediationTaskDao getRemediationTaskDao() {
        return remediationTaskDao;
    }

    @Override
    public void close() {
        logger.info("Closing MongoDB DAO Factory");
        databaseProvider.close();
    }
}
