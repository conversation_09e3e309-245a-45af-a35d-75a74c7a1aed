package io.syrix.dao.impl.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import io.syrix.dao.CustomerDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoBaseDao;
import io.syrix.datamodel.Customer;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * MongoDB implementation of the CustomerDao interface without framework dependencies.
 */
public class MongoCustomerDao extends AbstractMongoBaseDao<Customer, UUID> implements CustomerDao {

    private static final Logger logger = LoggerFactory.getLogger(MongoCustomerDao.class);
    private static final String COLLECTION_NAME = "Customers";

    /**
     * Create a new MongoCustomerDao with the specified database connection.
     *
     * @param database The MongoDB database
     */
    public MongoCustomerDao(MongoDatabase database) {
        super(database, COLLECTION_NAME);
    }

    @Override
    public Optional<Customer> findByName(String name) throws DataAccessException {
        logger.debug("Finding customer by name: {}", name);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
            Document doc = collection.find(Filters.eq("name", name)).first();

            if (doc == null) {
                return Optional.empty();
            }

            return Optional.of(documentToEntity(doc));
        } catch (Exception e) {
            logger.error("Error finding customer by name {}: {}", name, e.getMessage(), e);
            throw new DataAccessException("Failed to find customer by name: " + name, e);
        }
    }

    @Override
    public Optional<Customer> findByMicrosoftTenantId(String microsoftTenantId) throws DataAccessException {
        logger.debug("Finding customer by Microsoft tenant ID: {}", microsoftTenantId);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
            Document doc = collection.find(Filters.eq("microsoftTenantId", microsoftTenantId)).first();

            if (doc == null) {
                return Optional.empty();
            }

            return Optional.of(documentToEntity(doc));
        } catch (Exception e) {
            logger.error("Error finding customer by Microsoft tenant ID {}: {}", microsoftTenantId, e.getMessage(), e);
            throw new DataAccessException("Failed to find customer by Microsoft tenant ID: " + microsoftTenantId, e);
        }
    }

    @Override
    public List<Customer> findByStatus(String status) throws DataAccessException {
        logger.debug("Finding companies by status: {}", status);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
            FindIterable<Document> docs = collection.find(Filters.eq("status", status));

            List<Customer> result = new ArrayList<>();
            for (Document doc : docs) {
                result.add(documentToEntity(doc));
            }

            return result;
        } catch (Exception e) {
            logger.error("Error finding companies by status {}: {}", status, e.getMessage(), e);
            throw new DataAccessException("Failed to find companies by status: " + status, e);
        }
    }

    @Override
    protected Customer prepareForCreate(Customer customer) {
        // Generate ID if not present
        if (customer.getId() == null) {
            customer.setId(UUID.randomUUID());
        }

        // Set timestamps
        LocalDateTime now = LocalDateTime.now();
        if (customer.getCreatedAt() == null) {
            customer.setCreatedAt(now);
        }
        if (customer.getUpdatedAt() == null) {
            customer.setUpdatedAt(now);
        }

        // Set default status if not present
        if (customer.getStatus() == null) {
            customer.setStatus("active");
        }

        return customer;
    }

    @Override
    protected Customer prepareForUpdate(Customer customer) {
        // Update the timestamp
        customer.setUpdatedAt(LocalDateTime.now());
        return customer;
    }

    @Override
    protected UUID getEntityId(Customer customer) {
        return customer.getId();
    }

    @Override
    protected Document entityToDocument(Customer customer) {
        // Store LocalDateTime as string in MongoDB to avoid type conversion issues
        String createdAtStr = customer.getCreatedAt() != null ? customer.getCreatedAt().toString() : null;
        String updatedAtStr = customer.getUpdatedAt() != null ? customer.getUpdatedAt().toString() : null;

        return new Document("_id", customer.getId().toString())
                .append("name", customer.getName())
                .append("displayName", customer.getDisplayName())
                .append("microsoftTenantId", customer.getMicrosoftTenantId())
                .append("contactEmail", customer.getContactEmail())
                .append("contactPhone", customer.getContactPhone())
                .append("status", customer.getStatus())
                .append("createdAt", createdAtStr)
                .append("updatedAt", updatedAtStr);
    }

    @Override
    protected Customer documentToEntity(Document doc) {
        UUID id = UUID.fromString(doc.getString("_id"));
        String name = doc.getString("name");
        String displayName = doc.getString("displayName");
        String microsoftTenantId = doc.getString("microsoftTenantId");
        String contactEmail = doc.getString("contactEmail");
        String contactPhone = doc.getString("contactPhone");
        String status = doc.getString("status");

        // Parse LocalDateTime from string representation
        LocalDateTime createdAt = null;
        Object createdAtObj = doc.get("createdAt");
        if (createdAtObj != null) {
            try {
                createdAt = LocalDateTime.parse(createdAtObj.toString());
            } catch (Exception e) {
                logger.warn("Could not parse createdAt: {} ({})", createdAtObj, 
                    createdAtObj.getClass().getName());
                createdAt = LocalDateTime.now(); // Fallback
            }
        }

        LocalDateTime updatedAt = null;
        Object updatedAtObj = doc.get("updatedAt");
        if (updatedAtObj != null) {
            try {
                updatedAt = LocalDateTime.parse(updatedAtObj.toString());
            } catch (Exception e) {
                logger.warn("Could not parse updatedAt: {} ({})", updatedAtObj,
                    updatedAtObj.getClass().getName());
                updatedAt = LocalDateTime.now(); // Fallback
            }
        }

        // Create Customer object
        Customer customer = new Customer();
        customer.setId(id);
        customer.setName(name);
        customer.setDisplayName(displayName);
        customer.setMicrosoftTenantId(microsoftTenantId);
        customer.setContactEmail(contactEmail);
        customer.setContactPhone(contactPhone);
        customer.setStatus(status);
        if (createdAt != null) {
            customer.setCreatedAt(createdAt);
        }
        if (updatedAt != null) {
            customer.setUpdatedAt(updatedAt);
        }
        return customer;
    }
}