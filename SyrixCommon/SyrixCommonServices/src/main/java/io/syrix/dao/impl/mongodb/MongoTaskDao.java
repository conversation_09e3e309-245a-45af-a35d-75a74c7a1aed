package io.syrix.dao.impl.mongodb;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import io.syrix.dao.TaskDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.mongodb.AbstractMongoBaseDao;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.TaskStatus;
import io.syrix.datamodel.task.TaskType;
import io.syrix.datamodel.task.RetrieveTask;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.*;

/**
 * MongoDB implementation of TaskDao supporting polymorphic Task storage.
 * Stores all Task subclasses in a single 'tasks' collection using discriminator pattern.
 */
public class MongoTaskDao extends AbstractMongoBaseDao<Task, UUID> implements TaskDao {

    private static final Logger logger = LoggerFactory.getLogger(MongoTaskDao.class);
    private static final String COLLECTION_NAME = "tasks";
    private static final String TYPE_FIELD = "taskType";

    public MongoTaskDao(MongoDatabase database) {
        super(database, COLLECTION_NAME);
    }

    @Override
    public Optional<Task> findByIdAndCustomerId(UUID taskId, UUID customerId) throws DataAccessException {
        logger.debug("Finding Task by taskId: {}, customerId: {}", taskId, customerId);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
            
            Bson filter = Filters.and(
                Filters.eq("_id", taskId.toString()),
                Filters.eq("customerId", customerId.toString())
            );
            
            Document doc = collection.find(filter).first();
            
            if (doc == null) {
                logger.debug("No Task found for taskId: {}, customerId: {}", taskId, customerId);
                return Optional.empty();
            }
            
            Task task = documentToEntity(doc);
            logger.debug("Successfully found Task with id: {}", task.getId());
            return Optional.of(task);
            
        } catch (Exception e) {
            logger.error("Error finding Task by id and customer: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to find Task by taskId: " + taskId + ", customerId: " + customerId, e);
        }
    }

    @Override
    public <T extends Task> T saveTaskSubclass(T task) throws DataAccessException {
        logger.debug("Saving task subclass: {}", task.getClass().getSimpleName());
        
        @SuppressWarnings("unchecked")
        T savedTask = (T) save(task);
        return savedTask;
    }

    @Override
    public Optional<RetrieveTask> findRetrieveTaskByIdAndCustomer(UUID taskId, UUID customerId) throws DataAccessException {
        logger.debug("Finding RetrieveTask by taskId: {}, customerId: {}", taskId, customerId);

        try {
            MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
            
            // Log the filter components
            logger.debug("Filter components:");
            logger.debug("  _id: '{}' (String)", taskId.toString());
            logger.debug("  customerId: '{}' (String)", customerId.toString());
            logger.debug("  taskType: '{}' (String)", TaskType.RETRIEVE_CONFIGURATION.name());
            
            Bson filter = Filters.and(
                Filters.eq("_id", taskId.toString()),
                Filters.eq("customerId", customerId.toString()),
                Filters.eq(TYPE_FIELD, TaskType.RETRIEVE_CONFIGURATION.name())
            );
            
            // Log the actual MongoDB filter
            logger.debug("MongoDB filter: {}", filter.toBsonDocument().toJson());
            
            // Count documents matching the filter
            long matchingCount = collection.countDocuments(filter);
            logger.debug("Documents matching filter: {}", matchingCount);
            
            Document doc = collection.find(filter).first();
            
            if (doc == null) {
                logger.debug("No RetrieveTask found for taskId: {}, customerId: {}", taskId, customerId);
                
                // Test individual filter components
                logger.debug("Testing individual filters:");
                logger.debug("  _id filter matches: {}", collection.countDocuments(Filters.eq("_id", taskId.toString())));
                logger.debug("  customerId filter matches: {}", collection.countDocuments(Filters.eq("customerId", customerId.toString())));
                logger.debug("  taskType filter matches: {}", collection.countDocuments(Filters.eq(TYPE_FIELD, TaskType.RETRIEVE_CONFIGURATION.name())));
                
                return Optional.empty();
            }
            
            logger.debug("Found document: {}", doc.toJson());
            
            Task task = documentToEntity(doc);
            if (task instanceof RetrieveTask) {
                logger.debug("Successfully found RetrieveTask with id: {}", task.getId());
                return Optional.of((RetrieveTask) task);
            } else {
                logger.warn("Found task with correct criteria but wrong type: {}", task.getClass().getSimpleName());
                return Optional.empty();
            }
            
        } catch (Exception e) {
            logger.error("Error finding RetrieveTask by id and customer: {}", e.getMessage(), e);
            throw new DataAccessException("Failed to find RetrieveTask by taskId: " + taskId + ", customerId: " + customerId, e);
        }
    }

    @Override
    protected Task prepareForCreate(Task task) {
        if (task.getId() == null) {
            task.setId(UUID.randomUUID());
        }
        
        // Set default status if not present
        if (task.getStatus() == null) {
            task.setStatus(TaskStatus.CREATED);
        }
        
        // Ensure type is set based on task class
        if (task.getType() == null) {
            if (task instanceof RetrieveTask) {
                task.setType(TaskType.RETRIEVE_CONFIGURATION);
            }
        }
        
        return task;
    }

    @Override
    protected Task prepareForUpdate(Task task) {
        // No special update preparation needed currently
        return task;
    }

    @Override
    protected UUID getEntityId(Task task) {
        return task.getId();
    }

    @Override
    protected Document entityToDocument(Task task) {
        Document doc = new Document("_id", task.getId().toString())
                .append("customerId", task.getCustomerId() != null ? task.getCustomerId().toString() : null)
                .append(TYPE_FIELD, task.getType() != null ? task.getType().name() : null)
                .append("status", task.getStatus() != null ? task.getStatus().name() : null)
                .append("createdAt", task.getCreatedAt());

        // Handle polymorphic serialization
        if (task instanceof RetrieveTask retrieveTask) {
			// Convert service type list to string array
            List<String> serviceTypeNames = new ArrayList<>();
            if (retrieveTask.getServiceTypeList() != null) {
                for (ConfigurationServiceType serviceType : retrieveTask.getServiceTypeList()) {
                    serviceTypeNames.add(serviceType.name());
                }
            }
            
            doc.append("serviceTypeList", serviceTypeNames)
                    .append("connectionId", retrieveTask.getConnectionId() != null ? retrieveTask.getConnectionId().toString() : null)
                    .append("externalTenantId", retrieveTask.getExternalTenantId());
        }

        return doc;
    }

    @Override
    protected Task documentToEntity(Document doc) {
        UUID id = UUID.fromString(doc.getString("_id"));
        String customerIdStr = doc.getString("customerId");
        UUID customerId = customerIdStr != null ? UUID.fromString(customerIdStr) : null;
        
        String typeStr = doc.getString(TYPE_FIELD);
        TaskType type = typeStr != null ? TaskType.valueOf(typeStr) : null;
        
        String statusStr = doc.getString("status");
        TaskStatus status = statusStr != null ? TaskStatus.valueOf(statusStr) : null;

        Instant createdAt = Optional.ofNullable(doc.getDate("createdAt")).map(Date::toInstant).orElse(null);

        // Create appropriate task subclass based on type
        Task task;
        if (type == TaskType.RETRIEVE_CONFIGURATION) {
            RetrieveTask retrieveTask = new RetrieveTask();
            retrieveTask.setExternalTenantId(doc.getString("externalTenantId"));
            
            // Handle service type list
            @SuppressWarnings("unchecked")
            List<String> serviceTypeNames = (List<String>) doc.get("serviceTypeList");
            if (serviceTypeNames != null) {
                List<ConfigurationServiceType> serviceTypes = new ArrayList<>();
                for (String name : serviceTypeNames) {
                    try {
                        serviceTypes.add(ConfigurationServiceType.valueOf(name));
                    } catch (IllegalArgumentException e) {
                        logger.warn("Unknown ConfigurationServiceType: {}", name);
                    }
                }
                retrieveTask.setServiceTypeList(serviceTypes);
            }
            
            // Handle connectionId
            String connectionIdStr = doc.getString("connectionId");
            if (connectionIdStr != null) {
                retrieveTask.setConnectionId(UUID.fromString(connectionIdStr));
            }
            
            task = retrieveTask;
        } else {
            // Fallback to base Task for unknown types
            task = new Task();
        }

        // Set common properties
        task.setId(id);
        task.setCustomerId(customerId);
        task.setType(type);
        task.setStatus(status);
        task.setCreatedAt(createdAt);

        return task;
    }
}
