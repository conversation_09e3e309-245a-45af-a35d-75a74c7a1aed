package io.syrix.dao;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.SysConfig;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data Access Object interface for SysConfig entities.
 * Provides operations for managing system configuration data including Microsoft 365 security policies.
 */
public interface SysConfigDao extends BaseDao<SysConfig, UUID> {

    /**
     * Find the most recent system configuration.
     * 
     * @return An Optional containing the latest configuration if found, or empty if no configurations exist
     * @throws DataAccessException If a data access error occurs
     */
    Optional<SysConfig> findLatest() throws DataAccessException;

    /**
     * Find all system configurations created after the specified date.
     * 
     * @param date The date threshold for filtering configurations
     * @return A list of configurations created after the specified date
     * @throws DataAccessException If a data access error occurs
     */
    List<SysConfig> findByCreateDateAfter(LocalDateTime date) throws DataAccessException;

    /**
     * Find all system configurations updated after the specified date.
     * 
     * @param date The date threshold for filtering configurations
     * @return A list of configurations updated after the specified date
     * @throws DataAccessException If a data access error occurs
     */
    List<SysConfig> findByUpdateDateAfter(LocalDateTime date) throws DataAccessException;

    /**
     * Find configurations that contain policies for a specific Microsoft service section.
     * 
     * @param sectionName The Microsoft service section name (e.g., "aad", "defender", "exo")
     * @return A list of configurations containing policies for the specified section
     * @throws DataAccessException If a data access error occurs
     */
    List<SysConfig> findBySectionName(String sectionName) throws DataAccessException;

    /**
     * Find configurations that contain a specific policy ID.
     * 
     * @param policyId The policy ID to search for
     * @return A list of configurations containing the specified policy ID
     * @throws DataAccessException If a data access error occurs
     */
    List<SysConfig> findByPolicyId(String policyId) throws DataAccessException;

    /**
     * Delete all configurations older than the specified date.
     * Useful for cleanup operations to maintain database size.
     * 
     * @param date The date threshold - configurations older than this will be deleted
     * @return The number of configurations deleted
     * @throws DataAccessException If a data access error occurs
     */
    long deleteOlderThan(LocalDateTime date) throws DataAccessException;

    /**
     * Count configurations that have policies (non-empty policy maps).
     * 
     * @return The number of configurations with policies
     * @throws DataAccessException If a data access error occurs
     */
    long countWithPolicies() throws DataAccessException;

    /**
     * Find configurations ordered by creation date (newest first).
     * 
     * @param limit Maximum number of configurations to return
     * @return A list of configurations ordered by creation date descending
     * @throws DataAccessException If a data access error occurs
     */
    List<SysConfig> findRecentConfigurations(int limit) throws DataAccessException;

    /**
     * Count total number of unique policy IDs across all configurations.
     * This method aggregates all policy IDs from all sections in all configurations
     * and returns the count of unique policy identifiers.
     * 
     * @return The total number of unique policy IDs available in the system
     * @throws DataAccessException If a data access error occurs
     */
    int countAvailablePolicyIds() throws DataAccessException;
}
