package io.syrix.dao.impl.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.ReplaceOptions;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.DeleteResult;
import io.syrix.dao.SysConfigDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.dao.exception.EntityNotFoundException;
import io.syrix.dao.mongodb.AbstractMongoDao;
import io.syrix.datamodel.SysConfig;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * MongoDB implementation of the SysConfigDao interface.
 * Handles persistence operations for Microsoft 365 security configuration data.
 */
public class MongoSysConfigDao extends AbstractMongoDao implements SysConfigDao {

	private static final Logger logger = LoggerFactory.getLogger(MongoSysConfigDao.class);
	private static final String COLLECTION_NAME = "SysConfig";

	/**
	 * Create a new MongoSysConfigDao with the specified database connection.
	 *
	 * @param database The MongoDB database
	 */
	public MongoSysConfigDao(MongoDatabase database) {
		super(database);
	}

	@Override
	public Optional<SysConfig> findById(UUID id) throws DataAccessException {
		logger.debug("Finding system configuration by ID: {}", id);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			Document doc = collection.find(Filters.eq("_id", id.toString())).first();

			if (doc == null) {
				return Optional.empty();
			}

			return Optional.of(documentToSysConfig(doc));
		} catch (Exception e) {
			logger.error("Error finding system configuration by ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to find system configuration by ID: " + id, e);
		}
	}

	@Override
	public List<SysConfig> findAll() throws DataAccessException {
		logger.debug("Finding all system configurations");

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			FindIterable<Document> docs = collection.find().sort(Sorts.descending("createDate"));

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToSysConfig(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding all system configurations: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to find all system configurations", e);
		}
	}

	@Override
	public SysConfig save(SysConfig sysConfig) throws DataAccessException {
		if (sysConfig.getId() == null) {
			return create(sysConfig);
		} else {
			return update(sysConfig);
		}
	}

	@Override
	public void deleteById(UUID id) throws DataAccessException {
		logger.debug("Deleting system configuration by ID: {}", id);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

			Bson filter = Filters.eq("_id", id.toString());
			DeleteResult result = collection.deleteOne(filter);

			if (result.getDeletedCount() == 0) {
				throw new EntityNotFoundException("System configuration not found with ID: " + id);
			}
		} catch (EntityNotFoundException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error deleting system configuration by ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to delete system configuration by ID: " + id, e);
		}
	}

	@Override
	public void delete(SysConfig entity) throws DataAccessException {
		deleteById(entity.getId());
	}

	@Override
	public boolean existsById(UUID id) throws DataAccessException {
		logger.debug("Checking if system configuration exists by ID: {}", id);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			long count = collection.countDocuments(Filters.eq("_id", id.toString()));
			return count > 0;
		} catch (Exception e) {
			logger.error("Error checking if system configuration exists by ID {}: {}", id, e.getMessage(), e);
			throw new DataAccessException("Failed to check if system configuration exists by ID: " + id, e);
		}
	}

	@Override
	public long count() throws DataAccessException {
		logger.debug("Counting all system configurations");

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			return collection.countDocuments();
		} catch (Exception e) {
			logger.error("Error counting system configurations: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to count system configurations", e);
		}
	}

	@Override
	public Optional<SysConfig> findLatest() throws DataAccessException {
		logger.debug("Finding latest system configuration");

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			Document doc = collection.find()
					.sort(Sorts.descending("createDate"))
					.limit(1)
					.first();

			if (doc == null) {
				return Optional.empty();
			}

			return Optional.of(documentToSysConfig(doc));
		} catch (Exception e) {
			logger.error("Error finding latest system configuration: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to find latest system configuration", e);
		}
	}

	@Override
	public List<SysConfig> findByCreateDateAfter(LocalDateTime date) throws DataAccessException {
		logger.debug("Finding system configurations created after: {}", date);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			String dateStr = date.toString();
			FindIterable<Document> docs = collection.find(Filters.gt("createDate", dateStr))
					.sort(Sorts.descending("createDate"));

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToSysConfig(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding system configurations created after {}: {}", date, e.getMessage(), e);
			throw new DataAccessException("Failed to find system configurations created after: " + date, e);
		}
	}

	@Override
	public List<SysConfig> findByUpdateDateAfter(LocalDateTime date) throws DataAccessException {
		logger.debug("Finding system configurations updated after: {}", date);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			String dateStr = date.toString();
			FindIterable<Document> docs = collection.find(Filters.gt("updateDate", dateStr))
					.sort(Sorts.descending("updateDate"));

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToSysConfig(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding system configurations updated after {}: {}", date, e.getMessage(), e);
			throw new DataAccessException("Failed to find system configurations updated after: " + date, e);
		}
	}

	@Override
	public List<SysConfig> findBySectionName(String sectionName) throws DataAccessException {
		logger.debug("Finding system configurations by section name: {}", sectionName);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			String fieldPath = "policyIdsBySection." + sectionName;
			FindIterable<Document> docs = collection.find(Filters.exists(fieldPath))
					.sort(Sorts.descending("createDate"));

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToSysConfig(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding system configurations by section name {}: {}", sectionName, e.getMessage(), e);
			throw new DataAccessException("Failed to find system configurations by section name: " + sectionName, e);
		}
	}

	@Override
	public List<SysConfig> findByPolicyId(String policyId) throws DataAccessException {
		logger.debug("Finding system configurations by policy ID: {}", policyId);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			FindIterable<Document> docs = collection.find()
					.sort(Sorts.descending("createDate"));

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				SysConfig config = documentToSysConfig(doc);
				// Check if any section contains the policy ID
				boolean containsPolicy = config.getPolicyIdsBySection().values().stream()
						.anyMatch(policies -> policies.contains(policyId));

				if (containsPolicy) {
					result.add(config);
				}
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding system configurations by policy ID {}: {}", policyId, e.getMessage(), e);
			throw new DataAccessException("Failed to find system configurations by policy ID: " + policyId, e);
		}
	}

	@Override
	public long deleteOlderThan(LocalDateTime date) throws DataAccessException {
		logger.debug("Deleting system configurations older than: {}", date);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			String dateStr = date.toString();
			DeleteResult result = collection.deleteMany(Filters.lt("createDate", dateStr));

			long deletedCount = result.getDeletedCount();
			logger.info("Deleted {} system configurations older than {}", deletedCount, date);
			return deletedCount;
		} catch (Exception e) {
			logger.error("Error deleting system configurations older than {}: {}", date, e.getMessage(), e);
			throw new DataAccessException("Failed to delete system configurations older than: " + date, e);
		}
	}

	@Override
	public long countWithPolicies() throws DataAccessException {
		logger.debug("Counting system configurations with policies");

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			// Count documents where policyIdsBySection exists and is not empty
			return collection.countDocuments(Filters.and(
					Filters.exists("policyIdsBySection"),
					Filters.ne("policyIdsBySection", new Document())
			));
		} catch (Exception e) {
			logger.error("Error counting system configurations with policies: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to count system configurations with policies", e);
		}
	}

	@Override
	public List<SysConfig> findRecentConfigurations(int limit) throws DataAccessException {
		logger.debug("Finding {} most recent system configurations", limit);

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);
			FindIterable<Document> docs = collection.find()
					.sort(Sorts.descending("createDate"))
					.limit(limit);

			List<SysConfig> result = new ArrayList<>();
			for (Document doc : docs) {
				result.add(documentToSysConfig(doc));
			}

			return result;
		} catch (Exception e) {
			logger.error("Error finding recent system configurations: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to find recent system configurations", e);
		}
	}

	@Override
	public int countAvailablePolicyIds() throws DataAccessException {
		logger.debug("Counting total number of unique policy IDs across all configurations");

		try {
			Optional<SysConfig> config = findLatest();
			if (config.isPresent()) {
				SysConfig latestConfig = config.get();
				if (latestConfig.getPolicyIdsBySection() != null) {
					long count = latestConfig.getPolicyIdsBySection().values().stream()
							.flatMap(List::stream)
							.distinct()
							.count();
					logger.debug("Found {} unique policy IDs in the latest configuration", count);
					return Math.toIntExact(count);
				}
			}
			logger.debug("No configurations found, returning 0 unique policy IDs");
			return 0;
		} catch (Exception e) {
			logger.error("Error counting available policy IDs: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to count available policy IDs", e);
		}
	}

	private SysConfig create(SysConfig sysConfig) throws DataAccessException {
		logger.debug("Creating new system configuration");

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

			// Generate ID if not present
			if (sysConfig.getId() == null) {
				sysConfig.setId(UUID.randomUUID());
			}

			// Set timestamps
			LocalDateTime now = LocalDateTime.now();
			if (sysConfig.getCreateDate() == null) {
				sysConfig.setCreateDate(now);
			}
			if (sysConfig.getUpdateDate() == null) {
				sysConfig.setUpdateDate(now);
			}

			Document doc = sysConfigToDocument(sysConfig);
			collection.insertOne(doc);

			return sysConfig;
		} catch (Exception e) {
			logger.error("Error creating system configuration: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to create system configuration", e);
		}
	}

	private SysConfig update(SysConfig sysConfig) throws DataAccessException {
		logger.debug("Updating system configuration: {}", sysConfig.getId());

		try {
			MongoCollection<Document> collection = getCollection(COLLECTION_NAME);

			// Update timestamp
			sysConfig.setUpdateDate(LocalDateTime.now());

			Bson filter = Filters.eq("_id", sysConfig.getId().toString());
			Document doc = sysConfigToDocument(sysConfig);

			ReplaceOptions options = new ReplaceOptions().upsert(false);
			collection.replaceOne(filter, doc, options);

			// Verify update
			Document updatedDoc = collection.find(filter).first();
			if (updatedDoc == null) {
				throw new EntityNotFoundException("System configuration not found with ID: " + sysConfig.getId());
			}

			return documentToSysConfig(updatedDoc);
		} catch (EntityNotFoundException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error updating system configuration: {}", e.getMessage(), e);
			throw new DataAccessException("Failed to update system configuration", e);
		}
	}

	/**
	 * Convert a SysConfig object to a MongoDB Document.
	 */
	private Document sysConfigToDocument(SysConfig sysConfig) {
		Document policyDoc = new Document();

		// Convert Map<String, List<String>> to MongoDB document
		if (sysConfig.getPolicyIdsBySection() != null) {
			for (Map.Entry<String, List<String>> entry : sysConfig.getPolicyIdsBySection().entrySet()) {
				policyDoc.append(entry.getKey(), entry.getValue());
			}
		}

		return new Document("_id", sysConfig.getId().toString())
				.append("createDate", sysConfig.getCreateDate() != null ? sysConfig.getCreateDate().toString() : null)
				.append("updateDate", sysConfig.getUpdateDate() != null ? sysConfig.getUpdateDate().toString() : null)
				.append("policyIdsBySection", policyDoc);
	}

	/**
	 * Convert a MongoDB Document to a SysConfig object.
	 */
	private SysConfig documentToSysConfig(Document doc) {
		UUID id = UUID.fromString(doc.getString("_id"));

		LocalDateTime createDate = null;
		String createDateStr = doc.getString("createDate");
		if (createDateStr != null) {
			try {
				createDate = LocalDateTime.parse(createDateStr);
			} catch (Exception e) {
				logger.warn("Could not parse createDate: {}", createDateStr);
				createDate = LocalDateTime.now();
			}
		}

		LocalDateTime updateDate = null;
		String updateDateStr = doc.getString("updateDate");
		if (updateDateStr != null) {
			try {
				updateDate = LocalDateTime.parse(updateDateStr);
			} catch (Exception e) {
				logger.warn("Could not parse updateDate: {}", updateDateStr);
				updateDate = LocalDateTime.now();
			}
		}

		// Convert MongoDB document back to Map<String, List<String>>
		Map<String, List<String>> policyIdsBySection = new HashMap<>();
		Document policyDoc = doc.get("policyIdsBySection", Document.class);
		if (policyDoc != null) {
			for (String key : policyDoc.keySet()) {
				Object value = policyDoc.get(key);
				if (value instanceof List) {
					@SuppressWarnings("unchecked")
					List<String> policies = (List<String>) value;
					policyIdsBySection.put(key, policies);
				}
			}
		}

		SysConfig sysConfig = new SysConfig();
		sysConfig.setId(id);
		sysConfig.setCreateDate(createDate);
		sysConfig.setUpdateDate(updateDate);
		sysConfig.setPolicyIdsBySection(policyIdsBySection);
		return sysConfig;
	}
}
