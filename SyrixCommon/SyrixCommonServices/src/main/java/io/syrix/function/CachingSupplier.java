package io.syrix.function;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;


public class CachingSupplier<T> implements Supplier<T> {

	private final Supplier<T> innerSupplier;
	private volatile T result;
	private final AtomicBoolean invoked = new AtomicBoolean(false);

	public CachingSupplier(Supplier<T> innerSupplier) {

		this.innerSupplier = innerSupplier;
	}

	@Override
	public T get() {
		if (!invoked.get()) {
			synchronized (invoked) {
				if (!invoked.get()) {
					result = innerSupplier.get();
					invoked.set(true);
				}
			}
		}
		return result;
	}
}
