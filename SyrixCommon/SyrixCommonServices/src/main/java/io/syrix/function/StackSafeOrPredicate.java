package io.syrix.function;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

public class StackSafeOrPredicate<T> {

	List<Predicate<? super T>> predicates = new ArrayList<>();

	public void or(Predicate<? super T> other) {
		Objects.requireNonNull(other);
		predicates.add(other);
	}

	public Predicate<T> convert() {
		return f -> predicates.stream().anyMatch(predicate -> predicate.test(f));
	}

}
