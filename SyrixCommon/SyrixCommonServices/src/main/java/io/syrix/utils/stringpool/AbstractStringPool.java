package io.syrix.utils.stringpool;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AbstractStringPool implements IStringPool {
	private static final Logger logger = LoggerFactory.getLogger(AbstractStringPool.class);
	private static final int MAX_SIZE = 100 * 1024 / 3; // ~100kb for UTF8
	private static final int MAX_POOL_SIZE = 500000;
	protected transient Map<String, String> map;

	protected AbstractStringPool(Map<String, String> map) {
		this.map = map;
	}

	@Override
	public String getCanonicalVersion(String str) {
		if (StringUtils.isEmpty(str)) {
			return str;
		}
		if (str.length() > MAX_SIZE) {
            logger.debug("[StringPool] very long string encountered. Length: {}", str.length());
			return str;
		}
		if (map.size() > MAX_POOL_SIZE) {
			logger.debug("[StringPool] Clear string pool");
			map.clear();
		}
		String canon = map.putIfAbsent(str, str);
		return (canon == null) ? str : canon;
	}
}
