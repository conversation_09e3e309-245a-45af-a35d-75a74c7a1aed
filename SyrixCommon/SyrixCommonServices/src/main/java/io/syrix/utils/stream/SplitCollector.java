package io.syrix.utils.stream;

import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Supplier;
import java.util.stream.Collector;

public abstract class SplitCollector<T, R> implements Collector<T, Map<Integer, Collection<T>>, R> {
    public static final int DEFAULT_BULK_WRITE_CHUNK_SIZE = 25;
    public static final int DEFAULT_BULK_READ_CHUNK_SIZE = 20;

    private final AtomicInteger counter = new AtomicInteger(0);
    private final int chunkSize;

    private Supplier<Collection<T>> collectionSupplier = new Supplier<Collection<T>>() {
        @Override
        public Collection<T> get() {
            return new ArrayList<>();
        }
    };

    public SplitCollector() {
        this(DEFAULT_BULK_WRITE_CHUNK_SIZE);
    }

    public SplitCollector(int chunkSize) {
        this.chunkSize = chunkSize;
    }

    public SplitCollector(Supplier<Collection<T>> supplier) {
        this(DEFAULT_BULK_WRITE_CHUNK_SIZE, supplier);
    }

    /**
     * Initialize split collection with chunk size and collection supplier.
     *
     * @param chunkSize - size of chunks
     * @param supplier  - provides collection which will be used as chunk holder
     */
    public SplitCollector(int chunkSize, Supplier<Collection<T>> supplier) {
        this(chunkSize);
        this.collectionSupplier = supplier;
    }

    @Override
    public Supplier<Map<Integer, Collection<T>>> supplier() {
        return () -> new HashMap<>();
    }

    @Override
    public BiConsumer<Map<Integer, Collection<T>>, T> accumulator() {
        return (c, v) -> {
            int key = counter.getAndIncrement() / chunkSize;
            Collection<T> collection = c.get(key);
            if (null == collection) {
                collection = collectionSupplier.get();
                c.put(key, collection);
            }
            collection.add(v);
        };
    }

    @Override
    public BinaryOperator<Map<Integer, Collection<T>>> combiner() {
        return (left, right) -> {
            Map<Integer, Collection<T>> smallerMap = left.size() < right.size() ? left : right;
            Map<Integer, Collection<T>> largerMap = left.size() < right.size() ? right : left;

            smallerMap.forEach((key, value) -> largerMap.merge(key, value, (v1, v2) -> {
                v1.addAll(v2);
                return v1;
            }));
            return largerMap;
        };
    }

    @Override
    public Set<Characteristics> characteristics() {
        return EnumSet.of(Characteristics.UNORDERED);
    }

}

