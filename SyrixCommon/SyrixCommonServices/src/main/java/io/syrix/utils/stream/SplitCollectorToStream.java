package io.syrix.utils.stream;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class SplitCollectorToStream<T> extends SplitCollector<T, Stream<Collection<T>>> {
    public SplitCollectorToStream() {
        super();
    }

    public SplitCollectorToStream(int chunkSize) {
        super(chunkSize);
    }

    public SplitCollectorToStream(Supplier<Collection<T>> supplier) {
        super(supplier);
    }

    public SplitCollectorToStream(int chunkSize, Supplier<Collection<T>> supplier) {
        super(chunkSize, supplier);
    }

    @Override
    public Function<Map<Integer, Collection<T>>, Stream<Collection<T>>> finisher() {
        return m -> m.values().stream();
    }

}
