package io.syrix.utils.stream;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;

public class SplitCollectorToCollection<T> extends SplitCollector<T, Collection<Collection<T>>> {
    public SplitCollectorToCollection() {
        super();
    }

    public SplitCollectorToCollection(int chunkSize) {
        super(chunkSize);
    }

    public SplitCollectorToCollection(Supplier<Collection<T>> supplier) {
        super(supplier);
    }

    public SplitCollectorToCollection(int chunkSize, Supplier<Collection<T>> supplier) {
        super(chunkSize, supplier);
    }

    @Override
    public Function<Map<Integer, Collection<T>>, Collection<Collection<T>>> finisher() {
        return m -> m.values();
    }
}
