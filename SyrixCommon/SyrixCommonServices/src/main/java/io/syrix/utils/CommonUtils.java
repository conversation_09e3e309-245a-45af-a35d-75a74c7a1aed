package io.syrix.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

public class CommonUtils {
    /**
     * Tries to get value first from system properties and then from system environment using "unix name style".
     * <p>Unix style is a capital letters delimited by underscores. Example: {@code syrix.Env -> SYRIX_ENV}</p>
     *
     * @param key system property name
     * @param defaultValue system property default value
     */
    public static String readConfigProperty(String key, String defaultValue) {
        String property = System.getProperty(key);
        if (property != null) {
            return property;
        }
        String envName = key.replaceAll("\\W", "_").toUpperCase();
        String variable = System.getenv(envName);
        if (variable != null) {
            return variable;
        }

        return defaultValue;
    }

    public static String readConfigProperty(String key) {
        return readConfigProperty(key, null);
    }

    /**
     * Tries to get value first from system properties and then from system environment using "unix name style".
     * <p>Unix style is a capital letters delimited by underscores. Example: {@code syrix.Env -> SYRIX_ENV}</p>
     *
     * @param key          system property name
     */
    public static int readIntConfigProperty(String key) {
        String stringProp = readConfigProperty(key, "0");
        return Integer.parseInt(stringProp);
    }

    public static int readIntConfigProperty(String key, int defaultValue) {
        String stringProp = readConfigProperty(key, String.valueOf(defaultValue));
        return Integer.parseInt(stringProp);
    }


    public static boolean isNullOrEmpty(Object o) {
        if (o == null) {
            return true;
        } else if (o instanceof String) {
            return StringUtils.isEmpty((String)o);
        } else if (o instanceof Map) {
            return ((Map<?, ?>) o).isEmpty();
        } else if (o instanceof Collection) {
            return ((Collection<?>) o).isEmpty();
        } else if (o.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(o) == 0;
        } else {
            return false;
        }
    }

}
