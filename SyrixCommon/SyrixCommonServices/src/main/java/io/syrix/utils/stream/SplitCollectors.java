package io.syrix.utils.stream;


import java.util.Collection;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Stream;

public class SplitCollectors {

    private SplitCollectors() {
    }

    public static <T> Collector<T, Map<Integer, Collection<T>>, Stream<Collection<T>>> toStream(int chunkSize) {
        return new SplitCollectorToStream<>(chunkSize);
    }

    public static <T> Collector<T, Map<Integer, Collection<T>>, Collection<Collection<T>>> toCollection(int chunkSize) {
        return new SplitCollectorToCollection<>(chunkSize);
    }
}

