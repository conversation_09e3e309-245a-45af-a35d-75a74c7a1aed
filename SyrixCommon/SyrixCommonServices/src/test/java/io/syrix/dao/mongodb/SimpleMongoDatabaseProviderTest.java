package io.syrix.dao.mongodb;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class SimpleMongoDatabaseProviderTest {

    @Mock
    private MongoClient mockMongoClient;

    @Mock
    private MongoDatabase mockMongoDatabase;

    private AutoCloseable closeable;
    private String tempPropertiesFilePath;

    @BeforeEach
    void setUp() throws IOException {
        closeable = MockitoAnnotations.openMocks(this);
        when(mockMongoClient.getDatabase(anyString())).thenReturn(mockMongoDatabase);
        
        // Create a temporary properties file for testing
        File tempFile = File.createTempFile("mongodb-test", ".properties");
        tempPropertiesFilePath = tempFile.getAbsolutePath();
        
        Properties props = new Properties();
        props.setProperty("mongodb.connectionString", "mongodb://localhost:27017");
        props.setProperty("mongodb.database", "testdb");
        
        try (FileWriter writer = new FileWriter(tempFile)) {
            props.store(writer, "Test MongoDB Configuration");
        }
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
        
        // Clean up the temporary properties file
        File tempFile = new File(tempPropertiesFilePath);
        if (tempFile.exists()) {
            tempFile.delete();
        }
    }

    @Test
    void testConstructorWithMockClient() {
        // Arrange
        String databaseName = "testdb";
        
        // Act
        SimpleMongoDatabaseProvider provider = new SimpleMongoDatabaseProvider(mockMongoClient, databaseName);
        
        // Assert
        assertNotNull(provider);
        assertEquals(mockMongoDatabase, provider.getDatabase());
        assertEquals(mockMongoClient, provider.getClient());
        
        // Verify
        verify(mockMongoClient).getDatabase(databaseName);
    }

    @Test
    void testConstructorWithPropertiesFile() {
        // This test would typically require an actual MongoDB server.
        // For unit testing, we'll just verify that the provider can be created without exception.
        
        // Act & Assert
        assertDoesNotThrow(() -> {
            SimpleMongoDatabaseProvider provider = new SimpleMongoDatabaseProvider(tempPropertiesFilePath);
            provider.close();
        });
    }

    @Test
    void testCloseMethod() {
        // Arrange
        SimpleMongoDatabaseProvider provider = new SimpleMongoDatabaseProvider(mockMongoClient, "testdb");
        
        // Act
        provider.close();
        
        // Verify
        verify(mockMongoClient).close();
    }
}