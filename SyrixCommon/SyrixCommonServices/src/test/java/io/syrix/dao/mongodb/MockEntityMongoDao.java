package io.syrix.dao.mongodb;

import com.mongodb.client.MongoDatabase;
import io.syrix.dao.exception.DataAccessException;
import org.bson.Document;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Example implementation of AbstractMongoBaseDao for testing purposes.
 */
public class MockEntityMongoDao extends AbstractMongoBaseDao<MockEntityMongoDao.TestEntity, UUID> {

    private static final String COLLECTION_NAME = "test_entities";

    public MockEntityMongoDao(MongoDatabase database) {
        super(database, COLLECTION_NAME);
    }

    @Override
    protected TestEntity prepareForCreate(TestEntity entity) {
        // Create a copy to avoid modifying the input object
        TestEntity copy = new TestEntity(entity);
        
        // Generate ID if not present
        if (copy.getId() == null) {
            copy.setId(UUID.randomUUID());
        }
        
        // Set timestamps
        LocalDateTime now = LocalDateTime.now();
        copy.setCreatedAt(now);
        copy.setUpdatedAt(now);
        
        return copy;
    }

    @Override
    protected TestEntity prepareForUpdate(TestEntity entity) {
        // Create a copy to avoid modifying the input object
        TestEntity copy = new TestEntity(entity);
        
        // Update timestamp
        copy.setUpdatedAt(LocalDateTime.now());
        
        return copy;
    }

    @Override
    protected UUID getEntityId(TestEntity entity) {
        return entity.getId();
    }

    @Override
    protected TestEntity documentToEntity(Document document) {
        if (document == null) {
            throw new DataAccessException("Cannot convert null document to TestEntity");
        }
        
        UUID id = UUID.fromString(document.getString("_id"));
        String name = document.getString("name");
        String description = document.getString("description");
        boolean active = document.getBoolean("active", false);
        LocalDateTime createdAt = document.get("createdAt", LocalDateTime.class);
        LocalDateTime updatedAt = document.get("updatedAt", LocalDateTime.class);
        
        TestEntity entity = new TestEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setDescription(description);
        entity.setActive(active);
        entity.setCreatedAt(createdAt);
        entity.setUpdatedAt(updatedAt);
        
        return entity;
    }

    @Override
    protected Document entityToDocument(TestEntity entity) {
        if (entity == null) {
            throw new DataAccessException("Cannot convert null TestEntity to document");
        }
        
        return new Document("_id", entity.getId().toString())
                .append("name", entity.getName())
                .append("description", entity.getDescription())
                .append("active", entity.isActive())
                .append("createdAt", entity.getCreatedAt())
                .append("updatedAt", entity.getUpdatedAt());
    }
    
    /**
     * Find a TestEntity by name.
     * 
     * @param name The name to search for
     * @return The TestEntity with the given name, or null if not found
     * @throws DataAccessException If a data access error occurs
     */
    public TestEntity findByName(String name) throws DataAccessException {
        try {
            Document doc = getCollection(COLLECTION_NAME)
                    .find(new Document("name", name))
                    .first();
            
            if (doc == null) {
                return null;
            }
            
            return documentToEntity(doc);
        } catch (Exception e) {
            throw new DataAccessException("Error finding TestEntity by name: " + name, e);
        }
    }

    /**
     * Simple entity class for testing DAO operations.
     */
    public static class TestEntity {
        private UUID id;
        private String name;
        private String description;
        private boolean active;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        public TestEntity() {
            // Default constructor
        }
        
        public TestEntity(TestEntity other) {
            this.id = other.id;
            this.name = other.name;
            this.description = other.description;
            this.active = other.active;
            this.createdAt = other.createdAt;
            this.updatedAt = other.updatedAt;
        }

        public UUID getId() {
            return id;
        }

        public void setId(UUID id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }

        public LocalDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
        }

        public LocalDateTime getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TestEntity that = (TestEntity) o;
            return active == that.active &&
                    Objects.equals(id, that.id) &&
                    Objects.equals(name, that.name) &&
                    Objects.equals(description, that.description);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, name, description, active);
        }

        @Override
        public String toString() {
            return "TestEntity{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", description='" + description + '\'' +
                    ", active=" + active +
                    ", createdAt=" + createdAt +
                    ", updatedAt=" + updatedAt +
                    '}';
        }
    }
}