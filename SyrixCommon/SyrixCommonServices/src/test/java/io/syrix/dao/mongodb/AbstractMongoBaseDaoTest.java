package io.syrix.dao.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.UpdateResult;
import io.syrix.dao.exception.EntityNotFoundException;
import io.syrix.dao.mongodb.MockEntityMongoDao.TestEntity;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class AbstractMongoBaseDaoTest {

    @Mock
    private MongoDatabase mockDatabase;

    @Mock
    private MongoCollection<Document> mockCollection;

    @Mock
    private FindIterable<Document> mockFindIterable;

    @Mock
    private MongoCursor<Document> mockCursor;
    
    @Mock
    private DeleteResult mockDeleteResult;
    
    @Mock
    private InsertOneResult mockInsertOneResult;
    
    @Mock
    private UpdateResult mockUpdateResult;

    private MockEntityMongoDao dao;
    private TestEntity testEntity;
    private Document testDocument;
    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        
        // Set up mock behavior
        when(mockDatabase.getCollection(anyString())).thenReturn(mockCollection);
        when(mockCollection.find(any(Bson.class))).thenReturn(mockFindIterable);
        when(mockCollection.find()).thenReturn(mockFindIterable);
        when(mockFindIterable.first()).thenReturn(testDocument);
        when(mockFindIterable.iterator()).thenReturn(mockCursor);
        when(mockCollection.deleteOne(any(Bson.class))).thenReturn(mockDeleteResult);
        
        // Create DAO
        dao = new MockEntityMongoDao(mockDatabase);
        
        // Set up test data
        UUID testId = UUID.randomUUID();
        testEntity = new TestEntity();
        testEntity.setId(testId);
        testEntity.setName("Test Entity");
        testEntity.setDescription("Test Description");
        testEntity.setActive(true);
        
        testDocument = new Document("_id", testId.toString())
                .append("name", "Test Entity")
                .append("description", "Test Description")
                .append("active", true);
    }

    @Test
    void findByIdWhenEntityExists() {
        // Arrange
        UUID id = testEntity.getId();
        when(mockFindIterable.first()).thenReturn(testDocument);
        
        // Act
        Optional<TestEntity> result = dao.findById(id);
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId());
        assertEquals("Test Entity", result.get().getName());
        
        // Verify
        verify(mockCollection).find(any(Bson.class));
    }

    @Test
    void findByIdWhenEntityDoesNotExist() {
        // Arrange
        UUID id = UUID.randomUUID();
        when(mockFindIterable.first()).thenReturn(null);
        
        // Act
        Optional<TestEntity> result = dao.findById(id);
        
        // Assert
        assertFalse(result.isPresent());
        
        // Verify
        verify(mockCollection).find(any(Bson.class));
    }

    @Test
    void findAll() {
        // Arrange
        List<Document> documents = new ArrayList<>();
        documents.add(testDocument);
        
        when(mockCursor.hasNext()).thenReturn(true, false);
        when(mockCursor.next()).thenReturn(testDocument);
        
        // Act
        List<TestEntity> results = dao.findAll();
        
        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        
        // Verify
        verify(mockCollection).find();
        verify(mockFindIterable).iterator();
    }

    @Test
    void saveNewEntity() {
        // Arrange
        TestEntity newEntity = new TestEntity();
        newEntity.setName("New Entity");
        newEntity.setDescription("New Description");
        
        // MongoDB's insertOne returns InsertOneResult
        when(mockCollection.insertOne(any(Document.class))).thenReturn(mockInsertOneResult);
        
        // Act
        TestEntity result = dao.save(newEntity);
        
        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("New Entity", result.getName());
        assertNotNull(result.getCreatedAt());
        assertNotNull(result.getUpdatedAt());
        
        // Verify
        verify(mockCollection).insertOne(any(Document.class));
    }

    @Test
    void saveExistingEntity() {
        // Arrange
        when(mockFindIterable.first()).thenReturn(testDocument);
        // MongoDB's replaceOne returns UpdateResult
        when(mockCollection.replaceOne(any(Bson.class), any(Document.class), any())).thenReturn(mockUpdateResult);
        
        // Act
        TestEntity result = dao.save(testEntity);
        
        // Assert
        assertNotNull(result);
        assertEquals(testEntity.getId(), result.getId());
        
        // Verify
        verify(mockCollection).replaceOne(any(Bson.class), any(Document.class), any());
    }

    @Test
    void deleteByIdWhenEntityExists() {
        // Arrange
        UUID id = testEntity.getId();
        when(mockDeleteResult.getDeletedCount()).thenReturn(1L);
        
        // Act & Assert
        assertDoesNotThrow(() -> dao.deleteById(id));
        
        // Verify
        verify(mockCollection).deleteOne(any(Bson.class));
    }

    @Test
    void deleteByIdWhenEntityDoesNotExist() {
        // Arrange
        UUID id = UUID.randomUUID();
        when(mockDeleteResult.getDeletedCount()).thenReturn(0L);
        when(mockCollection.countDocuments(any(Bson.class))).thenReturn(0L);
        
        // Act & Assert
        assertThrows(EntityNotFoundException.class, () -> dao.deleteById(id));
        
        // Verify
        verify(mockCollection).deleteOne(any(Bson.class));
        verify(mockCollection).countDocuments(any(Bson.class));
    }

    @Test
    void existsById() {
        // Arrange
        UUID id = testEntity.getId();
        when(mockCollection.countDocuments(any(Bson.class))).thenReturn(1L);
        
        // Act
        boolean result = dao.existsById(id);
        
        // Assert
        assertTrue(result);
        
        // Verify
        verify(mockCollection).countDocuments(any(Bson.class));
    }

    @Test
    void count() {
        // Arrange
        when(mockCollection.countDocuments()).thenReturn(5L);
        
        // Act
        long result = dao.count();
        
        // Assert
        assertEquals(5L, result);
        
        // Verify
        verify(mockCollection).countDocuments();
    }

    @Test
    void testCustomMethod() {
        // Arrange
        when(mockFindIterable.first()).thenReturn(testDocument);
        
        // Act
        TestEntity result = dao.findByName("Test Entity");
        
        // Assert
        assertNotNull(result);
        assertEquals("Test Entity", result.getName());
        
        // Verify
        verify(mockCollection).find(any(Document.class));
    }
}