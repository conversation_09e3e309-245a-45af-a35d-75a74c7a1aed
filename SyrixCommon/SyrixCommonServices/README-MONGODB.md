# Syrix MongoDB DAO Implementation

This module provides a MongoDB implementation of the Syrix DAO interfaces without any framework dependencies like Spring.

## Overview

The MongoDB DAO implementation provides a simple and efficient way to access MongoDB databases for the Syrix security platform. The implementation follows these principles:

- No dependencies on Spring or other frameworks
- Simple configuration via properties file or environment variables
- Thread-safe connection management
- Proper error handling and logging
- Intuitive API for common CRUD operations

## Getting Started

### 1. Configuration

The MongoDB connection can be configured in multiple ways:

#### Environment Variables

Set the following environment variables:

```bash
# Required
SYRIX_MONGODB_CONNECTION_STRING=mongodb://localhost:27017
SYRIX_MONGODB_DATABASE=syrix

# Optional
# Additional configuration as needed
```

#### Properties File

Create a properties file (e.g., `mongodb.properties`) with the following content:

```properties
mongodb.connectionString=mongodb://localhost:27017
mongodb.database=syrix
# Additional configuration as needed
```

### 2. Using the DAO Factory

```java
import io.syrix.dao.DaoFactory;
import io.syrix.dao.ClientDao;
import io.syrix.dao.ConnectionInfoDao;
import io.syrix.dao.mongodb.MongoDbDaoFactory;

// Create the DAO factory with default configuration (environment variables)
DaoFactory daoFactory = new MongoDbDaoFactory();

		// OR create with a configuration file
		DaoFactory daoFactory = new MongoDbDaoFactory("/path/to/mongodb.properties");

		// Get the DAOs
		ClientDao clientDao = daoFactory.getClientDao();
		ConnectionInfoDao connectionInfoDao = daoFactory.getConnectionInfoDao();
		OAuth2ConfigurationDao oauth2ConfigDao = ((MongoDbDaoFactory) daoFactory).getOAuth2ConfigurationDao();
		OAuth2TokenDao oauth2TokenDao = ((MongoDbDaoFactory) daoFactory).getOAuth2TokenDao();

// Use the DAOs
// ...

// Close the DAO factory when done
daoFactory.

		close();
```

### 3. Example Usage

#### Company DAO

```java
// Create a client
Company client = new Company();
client.setName("Test Company");
client.setDisplayName("Test Company Display Name");
client.setMicrosoftTenantId("test-tenant-id");
client.setContactEmail("<EMAIL>");

// Save the client
client = clientDao.save(client);
System.out.println("Created client: " + client);

// Find the client by ID
Optional<Company> foundCompany = clientDao.findById(client.getId());
foundCompany.ifPresent(c -> System.out.println("Found client: " + c));

// Find the client by name
foundCompany = clientDao.findByName("Test Company");
foundCompany.ifPresent(c -> System.out.println("Found client by name: " + c));

// Update the client
client.setDisplayName("Updated Display Name");
client = clientDao.save(client);
System.out.println("Updated client: " + client);

// Delete the client
clientDao.deleteById(client.getId());
System.out.println("Deleted client with ID: " + client.getId());
```

#### Connection Info DAO

```java
// Create a connection info
ConnectionInfo connectionInfo = new ConnectionInfo();
connectionInfo.setService(ServiceType.OFFICE365.getValue());
connectionInfo.setConnected(true);
connectionInfo.setDisplayName("Office 365");
connectionInfo.setTenantId("test-tenant-id");
connectionInfo.setCompanyId(UUID.randomUUID().toString());

// Save the connection info
connectionInfo = connectionInfoDao.save(connectionInfo);
System.out.println("Created connection info: " + connectionInfo);

// Find connection info by service
List<ConnectionInfo> serviceConnections = connectionInfoDao.findByService(ServiceType.OFFICE365.getValue());
System.out.println("Found " + serviceConnections.size() + " connections for service " + ServiceType.OFFICE365.getValue());
```

## Error Handling

The MongoDB DAO implementation provides comprehensive error handling. All DAO methods throw a `DataAccessException` or a subclass of it when an error occurs. This allows for consistent error handling across the application.

## Thread Safety

The MongoDB DAO implementation is thread-safe. The connection is managed by a singleton instance of the `SimpleMongoDatabaseProvider` class, which uses a connection pool to manage connections to the MongoDB server.

## Logging

The MongoDB DAO implementation uses SLF4J for logging. All classes log important events at appropriate log levels, which can be configured in the logging framework of your choice.

## Additional Information

- The MongoDB DAO implementation automatically handles MongoDB document-to-object mapping using the MongoDB POJO codec registry
- The DAOs are designed to be used with the Syrix data model classes
- The implementation supports all operations defined in the DAO interfaces

## License

This module is part of the Syrix security platform and is subject to the same license terms as the rest of the platform.
