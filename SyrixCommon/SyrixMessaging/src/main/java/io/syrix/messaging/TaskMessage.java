package io.syrix.messaging;

import io.syrix.datamodel.task.TaskType;

import java.util.UUID;

/**
 * Message model for task queue operations.
 * Contains information about tasks to be processed by workers.
 */
public class TaskMessage {
    private UUID customerId;
    private UUID taskId;
    private TaskType taskType;

    public TaskMessage() {}

    public UUID getTaskId() {
        return taskId;
    }

    public void setTaskId(UUID taskId) {
        this.taskId = taskId;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public UUID getCustomerId() {
        return customerId;
    }

    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }

    @Override
    public String toString() {
        return "TaskMessage{" +
               "taskId='" + taskId + '\'' +
               ", taskType=" + taskType +
               ", customerId='" + customerId + '\'' +
               '}';
    }
}
