package io.syrix.utils.service;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for BaselinePolicyService.
 * Tests the service with real baseline data from resources.
 */
class BaselinePolicyServiceIntegrationTest {

    private static BaselinePolicyService service;

    @BeforeAll
    static void setUpClass() throws IOException {
        service = BaselinePolicyService.getInstance();
        service.initialize();
    }

    @Test
    void testServiceLoadsRealBaselineData() {
        // Given & When
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        
        // Then
        assertNotNull(allPolicies);
        assertTrue(allPolicies.size() > 100, "Should load substantial number of policies from real baselines");
        
        // Verify we have policies from multiple services
        Set<String> services = service.getAvailableServices();
        assertTrue(services.size() >= 5, "Should have multiple Microsoft 365 services");
        assertTrue(services.contains("EXO"), "Should contain Exchange Online policies");
        assertTrue(services.contains("AAD"), "Should contain Azure AD policies");
        assertTrue(services.contains("TEAMS"), "Should contain Teams policies");
    }

    @Test
    void testServiceProvidesCorrectStatistics() {
        // Given & When
        Map<String, Object> stats = service.getStatistics();
        
        // Then
        assertNotNull(stats);
        
        Integer totalPolicies = (Integer) stats.get("totalPolicies");
        Integer totalServices = (Integer) stats.get("totalServices");
        
        assertTrue(totalPolicies > 100, "Should have substantial number of policies");
        assertTrue(totalServices >= 5, "Should have multiple services");
        
        @SuppressWarnings("unchecked")
        Map<String, Long> criticalityBreakdown = (Map<String, Long>) stats.get("criticalityBreakdown");
        
        // Should have SHALL and SHOULD policies from real baselines
        assertTrue(criticalityBreakdown.getOrDefault("SHALL", 0L) > 0, "Should have SHALL policies");
        assertTrue(criticalityBreakdown.getOrDefault("SHOULD", 0L) > 0, "Should have SHOULD policies");
    }

    @Test
    void testServiceProvidesExchangeOnlinePolicies() {
        // Given & When
        List<PolicyBaseline> exoPolicies = service.getPoliciesByService("EXO");
        
        // Then
        assertNotNull(exoPolicies);
        assertFalse(exoPolicies.isEmpty(), "Should have Exchange Online policies");
        
        // Verify all policies are EXO policies
        for (PolicyBaseline policy : exoPolicies) {
            assertTrue(policy.getPolicyId().contains(".EXO."), 
                "All policies should be EXO policies: " + policy.getPolicyId());
        }
        
        // Should have a substantial number of EXO policies
        assertTrue(exoPolicies.size() > 20, "Should have substantial number of EXO policies");
    }

    @Test
    void testServiceProvidesAzureADPolicies() {
        // Given & When
        List<PolicyBaseline> aadPolicies = service.getPoliciesByService("AAD");
        
        // Then
        assertNotNull(aadPolicies);
        assertFalse(aadPolicies.isEmpty(), "Should have Azure AD policies");
        
        // Verify all policies are AAD policies
        for (PolicyBaseline policy : aadPolicies) {
            assertTrue(policy.getPolicyId().contains(".AAD."), 
                "All policies should be AAD policies: " + policy.getPolicyId());
        }
    }

    @Test
    void testServiceProvidesPoliciesByCriticality() {
        // Given & When
        List<PolicyBaseline> shallPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHALL);
        List<PolicyBaseline> shouldPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHOULD);
        
        // Then
        assertNotNull(shallPolicies);
        assertNotNull(shouldPolicies);
        
        // Should have both SHALL and SHOULD policies from real baselines
        assertFalse(shallPolicies.isEmpty(), "Should have SHALL policies");
        assertFalse(shouldPolicies.isEmpty(), "Should have SHOULD policies");
        
        // Verify criticality filtering works correctly
        for (PolicyBaseline policy : shallPolicies) {
            assertEquals(PolicyCriticality.SHALL, policy.getCriticality());
        }
        for (PolicyBaseline policy : shouldPolicies) {
            assertEquals(PolicyCriticality.SHOULD, policy.getCriticality());
        }
    }

    @Test
    void testServiceProvidesSpecificPolicyDetails() {
        // Given - Get a known policy from the service
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        String firstPolicyId = allPolicies.keySet().iterator().next();
        
        // When
        PolicyBaseline policy = service.getPolicyById(firstPolicyId);
        
        // Then
        assertNotNull(policy, "Should find the policy");
        assertEquals(firstPolicyId, policy.getPolicyId());
        assertNotNull(policy.getTitle());
        assertNotNull(policy.getCriticality());
        
        // Verify source file information
        String sourceFile = service.getPolicySourceFile(firstPolicyId);
        assertNotNull(sourceFile, "Should have source file information");
        assertTrue(sourceFile.endsWith(".md") || sourceFile.contains("classpath"), 
            "Source file should be a markdown file or classpath resource");
    }

    @Test
    void testServiceHandlesCaseInsensitiveServiceLookup() {
        // Given
        List<PolicyBaseline> upperCasePolicies = service.getPoliciesByService("EXO");
        
        // When
        List<PolicyBaseline> lowerCasePolicies = service.getPoliciesByService("exo");
        List<PolicyBaseline> mixedCasePolicies = service.getPoliciesByService("Exo");
        
        // Then
        assertEquals(upperCasePolicies.size(), lowerCasePolicies.size(), 
            "Case insensitive lookup should return same results");
        assertEquals(upperCasePolicies.size(), mixedCasePolicies.size(), 
            "Case insensitive lookup should return same results");
    }

    @Test
    void testServiceReloadFunctionality() throws IOException {
        // Given
        int initialPolicyCount = service.getAllPolicies().size();
        
        // When
        service.reload();
        
        // Then
        assertTrue(service.isInitialized(), "Service should remain initialized after reload");
        int reloadedPolicyCount = service.getAllPolicies().size();
        assertEquals(initialPolicyCount, reloadedPolicyCount, 
            "Policy count should be same after reload");
    }

    @Test
    void testServiceSingletonBehavior() {
        // Given & When
        BaselinePolicyService instance1 = BaselinePolicyService.getInstance();
        BaselinePolicyService instance2 = BaselinePolicyService.getInstance();
        
        // Then
        assertSame(instance1, instance2, "Should return same singleton instance");
        assertSame(service, instance1, "Should be same as test service instance");
    }

    @Test
    void testServiceProvidesConsistentData() {
        // Given & When - Call service methods multiple times
        Map<String, PolicyBaseline> policies1 = service.getAllPolicies();
        Map<String, PolicyBaseline> policies2 = service.getAllPolicies();
        
        Set<String> services1 = service.getAvailableServices();
        Set<String> services2 = service.getAvailableServices();
        
        // Then
        assertEquals(policies1.size(), policies2.size(), "Should return consistent policy count");
        assertEquals(services1.size(), services2.size(), "Should return consistent service count");
        
        // Verify same policies are returned
        for (String policyId : policies1.keySet()) {
            assertTrue(policies2.containsKey(policyId), "Should contain same policies");
        }
    }
}
