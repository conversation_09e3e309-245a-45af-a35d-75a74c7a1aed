package io.syrix.utils.service;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for BaselinePolicyService.
 * Tests the singleton service for managing baseline policies.
 */
class BaselinePolicyServiceTest {

    private BaselinePolicyService service;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // Get the singleton instance
        service = BaselinePolicyService.getInstance();

        // Note: Since this is a singleton, we can't truly reset it between tests
        // Tests that require uninitialized state should be run first or handle the initialized state
    }

    @Test
    void testSingleton_ShouldReturnSameInstance() {
        // Given & When
        BaselinePolicyService instance1 = BaselinePolicyService.getInstance();
        BaselinePolicyService instance2 = BaselinePolicyService.getInstance();
        
        // Then
        assertSame(instance1, instance2);
    }

    @Test
    void testInitialize_ShouldLoadPoliciesFromResources() throws IOException {
        // Given - service may already be initialized due to singleton nature

        // When
        service.initialize(); // This should be safe to call multiple times

        // Then
        assertTrue(service.isInitialized());
        Map<String, PolicyBaseline> policies = service.getAllPolicies();
        assertNotNull(policies);
        assertTrue(policies.size() > 0, "Should load policies from resources");
    }

    @Test
    void testGetAllPolicies_WhenInitialized_ShouldReturnPolicies() throws IOException {
        // Given
        service.initialize();
        
        // When
        Map<String, PolicyBaseline> policies = service.getAllPolicies();
        
        // Then
        assertNotNull(policies);
        assertFalse(policies.isEmpty());
        
        // Verify policies have required fields
        for (PolicyBaseline policy : policies.values()) {
            assertNotNull(policy.getPolicyId());
            assertNotNull(policy.getCriticality());
            assertNotNull(policy.getTitle());
        }
    }

    @Test
    void testGetAllPolicies_WhenNotInitialized_ShouldThrowException() {
        // Given - Due to singleton nature, service might already be initialized
        // This test verifies the behavior when service is not initialized
        if (service.isInitialized()) {
            // Skip this test if service is already initialized
            return;
        }

        // When & Then
        assertThrows(IllegalStateException.class, () -> service.getAllPolicies());
    }

    @Test
    void testGetPolicyById_WithValidId_ShouldReturnPolicy() throws IOException {
        // Given
        service.initialize();
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        String firstPolicyId = allPolicies.keySet().iterator().next();
        
        // When
        PolicyBaseline policy = service.getPolicyById(firstPolicyId);
        
        // Then
        assertNotNull(policy);
        assertEquals(firstPolicyId, policy.getPolicyId());
    }

    @Test
    void testGetPolicyById_WithInvalidId_ShouldReturnNull() throws IOException {
        // Given
        service.initialize();
        
        // When
        PolicyBaseline policy = service.getPolicyById("INVALID.ID.1.1v1");
        
        // Then
        assertNull(policy);
    }

    @Test
    void testGetPolicyById_WhenNotInitialized_ShouldThrowException() {
        // Given - Due to singleton nature, service might already be initialized
        if (service.isInitialized()) {
            // Skip this test if service is already initialized
            return;
        }

        // When & Then
        assertThrows(IllegalStateException.class, () -> service.getPolicyById("MS.EXO.1.1v1"));
    }

    @Test
    void testGetPoliciesByService_WithValidService_ShouldReturnPolicies() throws IOException {
        // Given
        service.initialize();
        Set<String> availableServices = service.getAvailableServices();
        String firstService = availableServices.iterator().next();
        
        // When
        List<PolicyBaseline> policies = service.getPoliciesByService(firstService);
        
        // Then
        assertNotNull(policies);
        assertFalse(policies.isEmpty());
        
        // Verify all policies belong to the requested service
        for (PolicyBaseline policy : policies) {
            assertTrue(policy.getPolicyId().contains("." + firstService + "."));
        }
    }

    @Test
    void testGetPoliciesByService_WithInvalidService_ShouldReturnEmptyList() throws IOException {
        // Given
        service.initialize();
        
        // When
        List<PolicyBaseline> policies = service.getPoliciesByService("INVALID");
        
        // Then
        assertNotNull(policies);
        assertTrue(policies.isEmpty());
    }

    @Test
    void testGetAvailableServices_ShouldReturnServices() throws IOException {
        // Given
        service.initialize();
        
        // When
        Set<String> services = service.getAvailableServices();
        
        // Then
        assertNotNull(services);
        assertFalse(services.isEmpty());
        
        // Should contain common Microsoft 365 services
        // Note: We can't assert specific services since it depends on available baseline files
        assertTrue(services.size() > 0);
    }

    @Test
    void testGetPoliciesByCriticality_ShouldReturnFilteredPolicies() throws IOException {
        // Given
        service.initialize();
        
        // When
        List<PolicyBaseline> shallPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHALL);
        List<PolicyBaseline> shouldPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHOULD);
        List<PolicyBaseline> mayPolicies = service.getPoliciesByCriticality(PolicyCriticality.MAY);
        
        // Then
        assertNotNull(shallPolicies);
        assertNotNull(shouldPolicies);
        assertNotNull(mayPolicies);
        
        // Verify criticality filtering
        for (PolicyBaseline policy : shallPolicies) {
            assertEquals(PolicyCriticality.SHALL, policy.getCriticality());
        }
        for (PolicyBaseline policy : shouldPolicies) {
            assertEquals(PolicyCriticality.SHOULD, policy.getCriticality());
        }
        for (PolicyBaseline policy : mayPolicies) {
            assertEquals(PolicyCriticality.MAY, policy.getCriticality());
        }
    }

    @Test
    void testGetPolicySourceFile_ShouldReturnSourceFile() throws IOException {
        // Given
        service.initialize();
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        String firstPolicyId = allPolicies.keySet().iterator().next();
        
        // When
        String sourceFile = service.getPolicySourceFile(firstPolicyId);
        
        // Then
        assertNotNull(sourceFile);
        assertFalse(sourceFile.isEmpty());
        assertTrue(sourceFile.endsWith(".md") || sourceFile.contains("classpath"));
    }

    @Test
    void testGetStatistics_ShouldReturnValidStatistics() throws IOException {
        // Given
        service.initialize();
        
        // When
        Map<String, Object> stats = service.getStatistics();
        
        // Then
        assertNotNull(stats);
        assertTrue(stats.containsKey("totalPolicies"));
        assertTrue(stats.containsKey("totalServices"));
        assertTrue(stats.containsKey("criticalityBreakdown"));
        assertTrue(stats.containsKey("serviceBreakdown"));
        
        // Verify statistics values
        Integer totalPolicies = (Integer) stats.get("totalPolicies");
        Integer totalServices = (Integer) stats.get("totalServices");
        
        assertNotNull(totalPolicies);
        assertNotNull(totalServices);
        assertTrue(totalPolicies > 0);
        assertTrue(totalServices > 0);
        
        @SuppressWarnings("unchecked")
        Map<String, Long> criticalityBreakdown = (Map<String, Long>) stats.get("criticalityBreakdown");
        assertNotNull(criticalityBreakdown);
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> serviceBreakdown = (Map<String, Integer>) stats.get("serviceBreakdown");
        assertNotNull(serviceBreakdown);
    }

    @Test
    void testReload_ShouldReinitializeService() throws IOException {
        // Given
        service.initialize();
        assertTrue(service.isInitialized());
        int initialPolicyCount = service.getAllPolicies().size();
        
        // When
        service.reload();
        
        // Then
        assertTrue(service.isInitialized());
        int reloadedPolicyCount = service.getAllPolicies().size();
        assertEquals(initialPolicyCount, reloadedPolicyCount);
    }

    @Test
    void testCaseInsensitiveServiceLookup() throws IOException {
        // Given
        service.initialize();
        Set<String> availableServices = service.getAvailableServices();
        if (availableServices.isEmpty()) {
            return; // Skip if no services available
        }
        
        String firstService = availableServices.iterator().next();
        
        // When
        List<PolicyBaseline> upperCasePolicies = service.getPoliciesByService(firstService.toUpperCase());
        List<PolicyBaseline> lowerCasePolicies = service.getPoliciesByService(firstService.toLowerCase());
        
        // Then
        assertEquals(upperCasePolicies.size(), lowerCasePolicies.size());
    }

    @Test
    void testThreadSafety_MultipleConcurrentAccess() throws IOException, InterruptedException {
        // Given
        service.initialize();
        
        // When - simulate concurrent access
        Thread[] threads = new Thread[5];
        boolean[] results = new boolean[5];
        
        for (int i = 0; i < 5; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    Map<String, PolicyBaseline> policies = service.getAllPolicies();
                    results[index] = policies != null && !policies.isEmpty();
                } catch (Exception e) {
                    results[index] = false;
                }
            });
            threads[i].start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Then
        for (boolean result : results) {
            assertTrue(result, "All concurrent accesses should succeed");
        }
    }
}
