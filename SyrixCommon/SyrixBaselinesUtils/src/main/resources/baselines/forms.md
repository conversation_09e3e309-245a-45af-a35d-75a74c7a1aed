**TLP:CLEAR**

# CISA M365 Security Configuration Baseline for Microsoft Forms

Microsoft Forms is a web-based application that allows users to create surveys, quizzes, and polls. Forms is included with Microsoft 365 subscriptions and integrates with other Microsoft 365 services. This baseline provides security configuration guidance for Microsoft Forms to protect against phishing attacks and unauthorized data collection.

Microsoft's **Cybersecurity and Infrastructure Security Agency (CISA)** has published security configuration baselines for Microsoft 365. These baselines provide minimum security requirements and are updated periodically to address emerging threats.

This document, "CISA M365 Security Configuration Baseline for Microsoft Forms," will help organizations implement the security controls outlined in the CISA baseline for Microsoft Forms. The security controls are organized by policy areas and are mapped to the CISA M365 Security Configuration Baseline.

This baseline is based on the Microsoft 365 Security Configuration Baseline published by CISA, version 2.1.0, dated June 2023. The policy statements and implementation details in this document are derived from the CISA baseline and Microsoft's technical documentation.

This baseline will be updated as new versions of the CISA M365 Security Configuration Baseline are published or as Microsoft introduces new security features for Microsoft Forms.

## **IMPORTANT NOTICE**

This information is being provided "as is" for informational purposes only. CISA does not endorse any commercial product or service, including any subjects of analysis. Any reference to specific commercial products, processes, or services by service mark, trademark, manufacturer, or otherwise, does not constitute or imply endorsement, recommendation, or favoring by CISA.

This document is provided for educational and informational purposes only and is not intended to provide specific guidance for any particular organization's security posture. Organizations should conduct their own assessment of security risks and implement appropriate security controls based on their specific requirements and risk tolerance.

The information contained in this document is subject to change without notice. CISA makes no warranties, express or implied, with respect to the information provided herein. CISA shall not be liable for any direct, indirect, incidental, special, or consequential damages arising from the use of this information.

## **Traffic Light Protocol**

This document is classified as TLP:CLEAR. Recipients may share TLP:CLEAR information without restriction, subject to copyright controls. TLP:CLEAR information may be distributed freely, consistent with applicable legal and ethical considerations.

## **License Compliance**

This work includes material taken from documents published by the Center for Internet Security, Inc. ("CIS") and is used under license. CIS Controls and CIS Benchmarks are published by CIS under a Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International Public License. The link to the license terms can be found at https://creativecommons.org/licenses/by-nc-sa/4.0/. CIS and CIS Controls and CIS Benchmarks are registered trademarks of CIS, and no endorsement is implied.

This document is in the worldwide public domain. Organizations may copy, republish, redistribute, acquire, translate, transcribe, and use this information.

## **Assumptions**

This document assumes the organization is using Microsoft 365 with appropriate licensing that includes Microsoft Forms functionality. Organizations must ensure they have the necessary licenses and permissions to implement the security controls outlined in this baseline.

The technical implementation details assume the organization has appropriate administrative permissions to configure Microsoft Forms settings through the Microsoft 365 Admin Center, PowerShell, or Microsoft Graph API.

## **Key Terminology**

The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and "OPTIONAL" in this document are to be interpreted as described in RFC 2119.

**Microsoft Forms**: A web-based application that allows users to create surveys, quizzes, and polls. Forms is included with Microsoft 365 subscriptions.

**Phishing Protection**: A security feature that automatically scans form submissions for malicious content and blocks potentially harmful submissions.

**Microsoft Graph API**: A RESTful web API that enables access to Microsoft Cloud service resources, including Microsoft Forms settings.

## **Baseline Policies**

### MS.FORMS.1.1v1
**Title**: Ensure internal phishing protection for Forms is enabled

**Description**: Microsoft Forms phishing protection MUST be enabled to automatically scan form submissions for malicious content and protect against phishing attacks conducted through organizational forms.

**Policy Statement**: Organizations SHALL enable Microsoft Forms phishing protection to detect and block potentially malicious form submissions within the organization.

**CIS Reference**: CIS Microsoft 365 Foundations Benchmark v5.0.0 - Control 1.3.5

**Rationale**: Microsoft Forms can be exploited by threat actors to conduct phishing campaigns, harvest credentials, collect sensitive organizational data, and perform social engineering attacks. Enabling phishing protection provides automatic scanning of form submissions and helps protect users from form-based threats.

**Configuration Details**:
- **Setting**: `isInOrgFormsPhishingScanEnabled`
- **Required Value**: `true`
- **API Endpoint**: `/beta/admin/forms/settings`
- **Method**: PATCH request to Microsoft Graph API

**Verification**:
PowerShell verification:
```powershell
$uri = 'https://graph.microsoft.com/beta/admin/forms/settings'
Invoke-MgGraphRequest -Uri $uri | select isInOrgFormsPhishingScanEnabled
```

Expected result: `isInOrgFormsPhishingScanEnabled: True`

**Impact**: 
- **Positive**: Protects against form-based phishing attacks and malicious data collection
- **Negative**: Potential for false positives that may block legitimate form submissions

**Related Controls**: 
- MS.EXO.7.1v1 (Advanced Threat Protection)
- MS.DEF.2.1v1 (Safe Attachments)
- MS.AAD.3.1v1 (Conditional Access)