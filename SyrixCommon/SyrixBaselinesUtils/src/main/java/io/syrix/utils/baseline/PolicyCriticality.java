package io.syrix.utils.baseline;

/**
 * Enum representing the criticality levels for baseline policies.
 * Based on CISA M365 Security Configuration Baseline standards.
 */
public enum PolicyCriticality {
    
    /**
     * ⚠️ SHALL - Mandatory requirement whose omission causes non-compliance or system failure.
     * Maps to CRITICAL severity.
     */
    SHALL("SHALL", "Mandatory requirement", "⚠️", AlertSeverity.CRITICAL),
    
    /**
     * 📈 SHOULD - Strong recommendation; implementers are advised to comply unless there's clear justification to deviate.
     * Maps to HIGH severity.  
     */
    SHOULD("SHOULD", "Recommended requirement", "📈", AlertSeverity.HIGH),
    
    /**
     * ℹ️ MAY - Optional or discretionary action, offering flexibility without breaking compliance.
     * Maps to MEDIUM severity.
     */
    MAY("MAY", "Optional requirement", "ℹ️", AlertSeverity.MEDIUM);
    
    private final String code;
    private final String description;
    private final String icon;
    private final AlertSeverity severity;
    
    PolicyCriticality(String code, String description, String icon, AlertSeverity severity) {
        this.code = code;
        this.description = description;
        this.icon = icon;
        this.severity = severity;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public AlertSeverity getSeverity() {
        return severity;
    }
    
    /**
     * Parse criticality from string value (case-insensitive).
     * @param value The string value to parse
     * @return The corresponding PolicyCriticality
     * @throws IllegalArgumentException if value is not recognized
     */
    public static PolicyCriticality fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Criticality value cannot be null or empty");
        }
        
        String normalizedValue = value.trim().toUpperCase();
        
        for (PolicyCriticality criticality : values()) {
            if (criticality.code.equals(normalizedValue)) {
                return criticality;
            }
        }
        
        throw new IllegalArgumentException("Unknown criticality value: " + value);
    }
    
    @Override
    public String toString() {
        return icon + " " + code + " - " + description;
    }
}
