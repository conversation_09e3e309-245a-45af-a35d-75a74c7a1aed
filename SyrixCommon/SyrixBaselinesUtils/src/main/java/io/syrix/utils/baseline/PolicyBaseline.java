package io.syrix.utils.baseline;

import java.util.List;
import java.util.Map;

/**
 * Data model representing a parsed baseline policy from CISA M365 Security Configuration Baseline documents.
 * Contains all relevant information extracted from markdown baseline files.
 */
public class PolicyBaseline {
    
    private String policyId;              // e.g., "MS.EXO.8.2v2"
    private String title;                 // Policy title/name
    private String description;           // Full policy description (formerly rationale)
    private PolicyCriticality criticality; // SHALL/SHOULD/MAY
    private String lastModified;          // When policy was last updated
    private List<MitreAttackTtp> mitreAttackTtps; // MITRE ATT&CK TTP mappings
    private String implementation;        // Implementation instructions
    private List<String> resources;       // Related resources/links
    private String licenseRequirements;   // Required licenses
    private Map<String, String> additionalMetadata; // Any other metadata
    
    // Constructors
    public PolicyBaseline() {}
    
    public PolicyBaseline(String policyId, String title, String description, 
                         PolicyCriticality criticality) {
        this.policyId = policyId;
        this.title = title;
        this.description = description;
        this.criticality = criticality;
    }
    
    // Getters and Setters
    public String getPolicyId() {
        return policyId;
    }
    
    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    
    public PolicyCriticality getCriticality() {
        return criticality;
    }
    
    public void setCriticality(PolicyCriticality criticality) {
        this.criticality = criticality;
    }
    
    public String getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(String lastModified) {
        this.lastModified = lastModified;
    }
    
    public List<MitreAttackTtp> getMitreAttackTtps() {
        return mitreAttackTtps;
    }
    
    public void setMitreAttackTtps(List<MitreAttackTtp> mitreAttackTtps) {
        this.mitreAttackTtps = mitreAttackTtps;
    }
    
    public String getImplementation() {
        return implementation;
    }
    
    public void setImplementation(String implementation) {
        this.implementation = implementation;
    }
    
    public List<String> getResources() {
        return resources;
    }
    
    public void setResources(List<String> resources) {
        this.resources = resources;
    }
    
    public String getLicenseRequirements() {
        return licenseRequirements;
    }
    
    public void setLicenseRequirements(String licenseRequirements) {
        this.licenseRequirements = licenseRequirements;
    }
    
    public Map<String, String> getAdditionalMetadata() {
        return additionalMetadata;
    }
    
    public void setAdditionalMetadata(Map<String, String> additionalMetadata) {
        this.additionalMetadata = additionalMetadata;
    }
    
    /**
     * Get the corresponding alert severity for this policy's criticality.
     * @return AlertSeverity mapped from this policy's criticality
     */
    public AlertSeverity getAlertSeverity() {
        return criticality != null ? criticality.getSeverity() : AlertSeverity.LOW;
    }
    
    /**
     * Check if this policy is mandatory (SHALL).
     * @return true if criticality is SHALL, false otherwise
     */
    public boolean isMandatory() {
        return criticality == PolicyCriticality.SHALL;
    }
    
    /**
     * Check if this policy is recommended (SHOULD).
     * @return true if criticality is SHOULD, false otherwise
     */
    public boolean isRecommended() {
        return criticality == PolicyCriticality.SHOULD;
    }
    
    /**
     * Check if this policy is optional (MAY).
     * @return true if criticality is MAY, false otherwise
     */
    public boolean isOptional() {
        return criticality == PolicyCriticality.MAY;
    }
    
    @Override
    public String toString() {
        return String.format("PolicyBaseline{policyId='%s', title='%s', criticality=%s}", 
                           policyId, title, criticality);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PolicyBaseline that = (PolicyBaseline) o;
        return policyId != null ? policyId.equals(that.policyId) : that.policyId == null;
    }
    
    @Override
    public int hashCode() {
        return policyId != null ? policyId.hashCode() : 0;
    }
}
