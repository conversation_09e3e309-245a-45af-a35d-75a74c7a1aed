package io.syrix.utils.baseline;

public record Control(
        String id,
        String value,
        boolean deleted,
        boolean malformedDescription,
        String baselineName
) {

    public static ControlBuilder builder() {
        return new ControlBuilder();
    }

    public static class ControlBuilder {
        private String id;
        private String value;
        private boolean deleted;
        private boolean malformedDescription;
        private String baselineName;

        public ControlBuilder id(String id) {
            this.id = id;
            return this;
        }

        public ControlBuilder value(String value) {
            this.value = value;
            return this;
        }

        public ControlBuilder deleted(boolean deleted) {
            this.deleted = deleted;
            return this;
        }

        public ControlBuilder malformedDescription(boolean malformedDescription) {
            this.malformedDescription = malformedDescription;
            return this;
        }

        public ControlBuilder baselineName(String baselineName) {
            this.baselineName = baselineName;
            return this;
        }

        public Control build() {
            return new Control(this.id, this.value, this.deleted, this.malformedDescription, this.baselineName);
        }
    }
}
