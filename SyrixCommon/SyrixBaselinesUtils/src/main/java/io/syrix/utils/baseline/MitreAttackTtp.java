package io.syrix.utils.baseline;

/**
 * Record representing a MITRE ATT&amp;CK TTP (Tactics, Techniques, and Procedures) mapping.
 * Contains the technique name and its URL for reference.
 *
 * @param title The technique title (e.g., "T1087: Account Discovery")
 * @param url The URL to the technique page (e.g., "https://attack.mitre.org/techniques/T1087/")
 */
public record MitreAttackTtp(
    String title,   // e.g., "T1087: Account Discovery"
    String url      // e.g., "https://attack.mitre.org/techniques/T1087/"
) {
    
    /**
     * Parse a MITRE TTP from the enhanced format "Title|URL".
     * 
     * @param enhanced The enhanced format string
     * @return MitreAttackTtp instance or null if format is invalid
     */
    public static MitreAttackTtp fromEnhancedFormat(String enhanced) {
        if (enhanced == null || !enhanced.contains("|")) {
            return null;
        }
        
        String[] parts = enhanced.split("\\|", 2);
        if (parts.length != 2) {
            return null;
        }
        
        return new MitreAttackTtp(parts[0].trim(), parts[1].trim());
    }
    
    /**
     * Parse a MITRE TTP from the legacy format "Title (ID)".
     * 
     * @param legacy The legacy format string
     * @return MitreAttackTtp instance or null if format is invalid
     */
    public static MitreAttackTtp fromLegacyFormat(String legacy) {
        if (legacy == null || !legacy.contains("(") || !legacy.contains(")")) {
            return null;
        }
        
        int openParen = legacy.lastIndexOf("(");
        int closeParen = legacy.lastIndexOf(")");
        
        if (openParen == -1 || closeParen == -1 || openParen >= closeParen) {
            return null;
        }
        
        String title = legacy.substring(0, openParen).trim();
        String ttpId = legacy.substring(openParen + 1, closeParen).trim();
        String url = "https://attack.mitre.org/techniques/" + ttpId;
        
        return new MitreAttackTtp(title, url);
    }
    
    /**
     * Get the TTP ID from the URL.
     * 
     * @return The TTP ID (e.g., "T1087") or null if URL format is invalid
     */
    public String getTtpId() {
        if (url == null || !url.contains("/techniques/")) {
            return null;
        }
        
        String[] parts = url.split("/techniques/");
        if (parts.length != 2) {
            return null;
        }
        
        String idPart = parts[1];
        // Remove trailing slash if present
        if (idPart.endsWith("/")) {
            idPart = idPart.substring(0, idPart.length() - 1);
        }
        
        return idPart;
    }
    
    /**
     * Check if this TTP is clickable (has a valid URL).
     *
     * @return true if URL is valid and points to MITRE ATT&amp;CK
     */
    public boolean isClickable() {
        return url != null && url.startsWith("https://attack.mitre.org/techniques/");
    }
}