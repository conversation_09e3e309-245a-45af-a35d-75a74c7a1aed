package io.syrix.utils.baseline;

import io.syrix.utils.exceptions.BaselineImportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class SecureBaselineService {
	private static final Logger logger = LoggerFactory.getLogger(SecureBaselineService.class);
	private static final String GROUP_HEADER_PATTERN = "^## \\d+\\. .+$";
	private static final int MAX_LINE_SEARCH = 20;

	/**
	 * Imports secure baselines from markdown files for specified products.
	 *
	 * @param productNames List of product names to import baselines for
	 * @return Map of product names to their baseline configurations
	 */
	public Map<String, List<BaselineGroup>> importSecureBaseline(List<String> productNames) {
		Map<String, List<BaselineGroup>> output = new HashMap<>();

		for (String product : productNames) {
			try {
				logger.debug("Processing secure baseline markdown for {}", product);
				output.put(product, processProductBaseline(product));
			} catch (Exception e) {
				logger.error("Failed to parse {} secure baseline markdown from resources", product, e);
				throw new BaselineImportException(String.format(
						"Failed to parse %s secure baseline markdown.", product), e);
			}
		}

		return output;
	}

	private List<BaselineGroup> processProductBaseline(String product) throws IOException {
		List<BaselineGroup> groups = new ArrayList<>();

		// Load markdown file from resources
		String resourcePath = String.format("baselines/%s.md", product);
		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath);

		if (inputStream == null) {
			throw new IOException(String.format("Could not find baseline file %s in resources", resourcePath));
		}

		// Read all lines from the markdown file
		List<String> mdLines;
		try (BufferedReader reader = new BufferedReader(
				new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
			mdLines = reader.lines().toList();
		}

		List<Integer> groupHeaderLines = findGroupHeaderLines(mdLines);

		for (Integer lineNumber : groupHeaderLines) {
			String groupHeader = mdLines.get(lineNumber);
			BaselineGroup group = processGroup(product, groupHeader, mdLines, lineNumber);
			groups.add(group);
		}

		return groups;
	}


	private List<Integer> findGroupHeaderLines(List<String> mdLines) {
		List<Integer> lineNumbers = new ArrayList<>();
		for (int i = 0; i < mdLines.size(); i++) {
			String line = mdLines.get(i);
			if (line.matches(GROUP_HEADER_PATTERN)) {
				lineNumbers.add(i);
			}
		}
		return lineNumbers;
	}

	private BaselineGroup processGroup(String product, String groupHeader, List<String> mdLines, int startLine) {
		String[] headerParts = groupHeader.substring(3).split("\\.");  // Remove "## "
		String groupNumber = headerParts[0];
		String groupName = headerParts[1].trim();

		List<Control> controls = new ArrayList<>();
		Pattern idPattern = Pattern.compile(String.format("MS\\.%s\\.%s\\.\\d+v\\d+\\s*$",
				product.toUpperCase(), groupNumber));

		for (int i = startLine; i < mdLines.size(); i++) {
			String line = mdLines.get(i);
			if (line.startsWith("#### ") && idPattern.matcher(line).find()) {
				Control control = processControl(line, mdLines, i, product);
				controls.add(control);
			}
		}

		return BaselineGroup.builder()
				.groupNumber(groupNumber)
				.groupName(groupName)
				.controls(controls)
				.baselineName(product)
				.build();
	}

	private Control processControl(String idLine, List<String> mdLines, int lineNumber, String product) {
		String id = idLine.substring(5).trim(); // Remove "#### "
		boolean deleted = id.endsWith("X");
		if (deleted) {
			id = id.substring(0, id.length() - 1);
		}

		String value = processDescription(mdLines, lineNumber + 1);

		boolean malformedDescription = false;
		if (value.isBlank()) {
			malformedDescription = true;
			value = "Error - The baseline policy text is malformed. Description should start immediately after Policy Id.";
			logger.error("Expected description for {} to start on line {}", id, lineNumber + 1);
		}

		return Control.builder()
				.id(id)
				.value(applyMarkdownFormatting(value))
				.deleted(deleted)
				.malformedDescription(malformedDescription)
				.baselineName(product)
				.build();
	}

	private String processDescription(List<String> mdLines, int startLine) {
		StringBuilder value = new StringBuilder();
		int lineAdvance = 0;
		boolean isList = false;

		while (lineAdvance < MAX_LINE_SEARCH && startLine + lineAdvance < mdLines.size()) {
			String currentLine = mdLines.get(startLine + lineAdvance).trim();

			if (currentLine.isEmpty()) {
				lineAdvance++;
				continue;
			}

			if (currentLine.contains("<!--")) {
				break;
			}

			if (currentLine.endsWith(":")) {
				isList = true;
			}

			if (value.length() > 0) {
				value.append(isList ? "\n" : " ");
			}
			value.append(currentLine);

			if (currentLine.endsWith(".") && !isList) {
				break;
			}

			lineAdvance++;
		}

		return value.toString();
	}

	private String applyMarkdownFormatting(String text) {
		// Replace italics
		String ret_text = text.replaceAll("_([^_]+)_", "<i>$1</i>");

		// Replace bold
		ret_text = ret_text.replaceAll("\\*\\*([^*]+)\\*\\*", "<b>$1</b>");

		return ret_text;
	}
}
