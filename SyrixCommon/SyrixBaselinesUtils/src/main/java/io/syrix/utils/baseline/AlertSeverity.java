package io.syrix.utils.baseline;

/**
 * Enum representing alert severity levels.
 * Maps to PolicyCriticality for baseline policy compliance alerts.
 */
public enum AlertSeverity {
    
    /**
     * Critical severity - system failure imminent or security breach detected.
     * Maps to PolicyCriticality.SHALL
     */
    CRITICAL("critical", "Critical", 1, "🔴"),
    
    /**
     * High severity - significant risk or recommended action needed.
     * Maps to PolicyCriticality.SHOULD
     */
    HIGH("high", "High", 2, "🟠"),
    
    /**
     * Medium severity - moderate risk or optional improvement.
     * Maps to PolicyCriticality.MAY
     */
    MEDIUM("medium", "Medium", 3, "🟡"),
    
    /**
     * Low severity - minor issue or informational.
     * Used for general alerts not tied to policy criticality.
     */
    LOW("low", "Low", 4, "🟢");
    
    private final String code;
    private final String displayName;
    private final int priority;
    private final String icon;
    
    AlertSeverity(String code, String displayName, int priority, String icon) {
        this.code = code;
        this.displayName = displayName;
        this.priority = priority;
        this.icon = icon;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public String getIcon() {
        return icon;
    }
    
    /**
     * Maps PolicyCriticality to corresponding AlertSeverity.
     * 
     * @param criticality The policy criticality to map
     * @return The corresponding alert severity
     */
    public static AlertSeverity fromPolicyCriticality(PolicyCriticality criticality) {
        return switch (criticality) {
            case SHALL -> CRITICAL;
            case SHOULD -> HIGH;
            case MAY -> MEDIUM;
        };
    }
    
    /**
     * Parse severity from string value (case-insensitive).
     * @param value The string value to parse
     * @return The corresponding AlertSeverity
     * @throws IllegalArgumentException if value is not recognized
     */
    public static AlertSeverity fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Severity value cannot be null or empty");
        }
        
        String normalizedValue = value.trim().toLowerCase();
        
        for (AlertSeverity severity : values()) {
            if (severity.code.equals(normalizedValue) || 
                severity.displayName.toLowerCase().equals(normalizedValue)) {
                return severity;
            }
        }
        
        throw new IllegalArgumentException("Unknown severity value: " + value);
    }
    
    @Override
    public String toString() {
        return icon + " " + displayName;
    }
}
