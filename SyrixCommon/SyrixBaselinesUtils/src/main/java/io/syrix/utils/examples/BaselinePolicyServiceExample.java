package io.syrix.utils.examples;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import io.syrix.utils.service.BaselinePolicyService;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Example demonstrating how to use the BaselinePolicyService.
 * Shows common usage patterns for accessing CISA M365 Security Configuration Baseline policies.
 */
public class BaselinePolicyServiceExample {

    public static void main(String[] args) {
        try {
            // Get the singleton service instance
            BaselinePolicyService service = BaselinePolicyService.getInstance();
            
            // Initialize the service (loads all baseline policies)
            System.out.println("🚀 Initializing BaselinePolicyService...");
            service.initialize();
            System.out.println("✅ Service initialized successfully!");
            System.out.println();
            
            // Example 1: Get all policies
            demonstrateGetAllPolicies(service);
            
            // Example 2: Get policies by service
            demonstrateGetPoliciesByService(service);
            
            // Example 3: Get specific policy by ID
            demonstrateGetPolicyById(service);
            
            // Example 4: Get policies by criticality
            demonstrateGetPoliciesByCriticality(service);
            
            // Example 5: Get service statistics
            demonstrateGetStatistics(service);
            
            // Example 6: Search and filter policies
            demonstrateSearchAndFilter(service);
            
        } catch (IOException e) {
            System.err.println("❌ Error initializing service: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void demonstrateGetAllPolicies(BaselinePolicyService service) {
        System.out.println("📋 Example 1: Get All Policies");
        System.out.println("=" + "=".repeat(50));
        
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        System.out.println("Total policies loaded: " + allPolicies.size());
        
        // Show first 3 policies
        System.out.println("Sample policies:");
        allPolicies.values().stream()
                .limit(3)
                .forEach(policy -> 
                    System.out.printf("  %s - %s %s (%s)%n",
                        policy.getPolicyId(),
                        policy.getDescription(),
                        policy.getTitle(),
                        policy.getCriticality().getCode())
                );
        System.out.println();
    }
    
    private static void demonstrateGetPoliciesByService(BaselinePolicyService service) {
        System.out.println("🏢 Example 2: Get Policies by Service");
        System.out.println("=" + "=".repeat(50));
        
        // Get available services
        Set<String> services = service.getAvailableServices();
        System.out.println("Available services: " + services);
        
        // Get Exchange Online policies
        List<PolicyBaseline> exoPolicies = service.getPoliciesByService("EXO");
        System.out.println("Exchange Online policies: " + exoPolicies.size());
        
        // Show first 3 EXO policies
        System.out.println("Sample EXO policies:");
        exoPolicies.stream()
                .limit(3)
                .forEach(policy -> 
                    System.out.printf("  %s - %s (%s)%n", 
                        policy.getPolicyId(), 
                        policy.getTitle(), 
                        policy.getCriticality().getCode())
                );
        System.out.println();
    }
    
    private static void demonstrateGetPolicyById(BaselinePolicyService service) {
        System.out.println("🔍 Example 3: Get Specific Policy by ID");
        System.out.println("=" + "=".repeat(50));
        
        // Get a specific policy (use first available policy ID)
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        String firstPolicyId = allPolicies.keySet().iterator().next();
        
        PolicyBaseline policy = service.getPolicyById(firstPolicyId);
        if (policy != null) {
            System.out.println("Policy Details:");
            System.out.println("  ID: " + policy.getPolicyId());
            System.out.println("  Title: " + policy.getTitle());
            System.out.println("  Criticality: " + policy.getCriticality());
            System.out.println("  Source File: " + service.getPolicySourceFile(firstPolicyId));
            
            if (!policy.getDescription().isEmpty()) {
                System.out.println("  Description: " + policy.getDescription().substring(0, 
                    Math.min(100, policy.getDescription().length())) + "...");
            }
        }
        System.out.println();
    }
    
    private static void demonstrateGetPoliciesByCriticality(BaselinePolicyService service) {
        System.out.println("⚠️ Example 4: Get Policies by Criticality");
        System.out.println("=" + "=".repeat(50));
        
        List<PolicyBaseline> shallPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHALL);
        List<PolicyBaseline> shouldPolicies = service.getPoliciesByCriticality(PolicyCriticality.SHOULD);
        List<PolicyBaseline> mayPolicies = service.getPoliciesByCriticality(PolicyCriticality.MAY);
        
        System.out.println("SHALL policies (mandatory): " + shallPolicies.size());
        System.out.println("SHOULD policies (recommended): " + shouldPolicies.size());
        System.out.println("MAY policies (optional): " + mayPolicies.size());
        
        // Show sample SHALL policies
        if (!shallPolicies.isEmpty()) {
            System.out.println("Sample SHALL policies:");
            shallPolicies.stream()
                    .limit(3)
                    .forEach(policy -> 
                        System.out.printf("  %s - %s%n", 
                            policy.getPolicyId(), 
                            policy.getTitle())
                    );
        }
        System.out.println();
    }
    
    private static void demonstrateGetStatistics(BaselinePolicyService service) {
        System.out.println("📊 Example 5: Get Service Statistics");
        System.out.println("=" + "=".repeat(50));
        
        Map<String, Object> stats = service.getStatistics();
        
        System.out.println("Total Policies: " + stats.get("totalPolicies"));
        System.out.println("Total Services: " + stats.get("totalServices"));
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> serviceBreakdown = (Map<String, Integer>) stats.get("serviceBreakdown");
        System.out.println("Service Breakdown:");
        serviceBreakdown.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> 
                    System.out.printf("  %-15s: %2d policies%n", entry.getKey(), entry.getValue())
                );
        
        @SuppressWarnings("unchecked")
        Map<String, Long> criticalityBreakdown = (Map<String, Long>) stats.get("criticalityBreakdown");
        System.out.println("Criticality Breakdown:");
        criticalityBreakdown.forEach((criticality, count) -> 
            System.out.printf("  %-10s: %2d policies%n", criticality, count)
        );
        System.out.println();
    }
    
    private static void demonstrateSearchAndFilter(BaselinePolicyService service) {
        System.out.println("🔍 Example 6: Search and Filter Policies");
        System.out.println("=" + "=".repeat(50));
        
        // Example: Find all policies related to "external" (case-insensitive search)
        Map<String, PolicyBaseline> allPolicies = service.getAllPolicies();
        List<PolicyBaseline> externalPolicies = allPolicies.values().stream()
                .filter(policy -> 
                    policy.getPolicyId().toLowerCase().contains("external") ||
                    policy.getTitle().toLowerCase().contains("external") ||
                    policy.getDescription().toLowerCase().contains("external")
                )
                .toList();
        
        System.out.println("Policies related to 'external': " + externalPolicies.size());
        externalPolicies.forEach(policy -> 
            System.out.printf("  %s - %s (%s)%n", 
                policy.getPolicyId(), 
                policy.getTitle(), 
                policy.getCriticality().getCode())
        );
        
        // Example: Find all SHALL policies for Exchange Online
        List<PolicyBaseline> exoShallPolicies = service.getPoliciesByService("EXO").stream()
                .filter(policy -> policy.getCriticality() == PolicyCriticality.SHALL)
                .toList();
        
        System.out.println("Exchange Online SHALL policies: " + exoShallPolicies.size());
        System.out.println();
        
        // Example: Get policies by multiple services
        System.out.println("Policies for EXO and AAD services:");
        List<String> targetServices = List.of("EXO", "AAD");
        long totalPolicies = targetServices.stream()
                .mapToLong(serviceName -> service.getPoliciesByService(serviceName).size())
                .sum();
        System.out.println("Total policies for EXO and AAD: " + totalPolicies);
        System.out.println();
    }
}
