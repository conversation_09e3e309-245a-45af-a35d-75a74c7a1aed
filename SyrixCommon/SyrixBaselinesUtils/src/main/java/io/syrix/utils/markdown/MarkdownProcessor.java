package io.syrix.utils.markdown;

import org.apache.commons.text.StringEscapeUtils;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * Service for processing basic markdown syntax and converting it to HTML.
 * Handles simple formatting like bold, italic, and anchor creation.
 * 
 * Moved from SyrixBackend to SyrixCommon/SyrixUtils for shared usage.
 */
public class MarkdownProcessor {

	private static final Pattern ITALIC_PATTERN = Pattern.compile("_([^\\v][^_]*[^\\v])?_");
	private static final Pattern BOLD_PATTERN = Pattern.compile("\\*\\*(.*?)\\*\\*");

	/**
	 * Converts markdown formatting to HTML.
	 */
	public String processMarkdown(String markdown) {
		if (markdown == null || markdown.isEmpty()) {
			return markdown;
		}

		String processed = markdown;

		// Convert italics
		processed = replaceItalics(processed);

		// Convert bold
		processed = replaceBold(processed);

		// HTML encode the result
		return StringEscapeUtils.escapeHtml4(processed);
	}

	private String replaceItalics(String text) {
		Matcher matcher = ITALIC_PATTERN.matcher(text);
		StringBuffer result = new StringBuffer();

		while (matcher.find()) {
			String content = matcher.group(1);
			matcher.appendReplacement(result, "<i>" + content + "</i>");
		}
		matcher.appendTail(result);

		return result.toString();
	}

	private String replaceBold(String text) {
		Matcher matcher = BOLD_PATTERN.matcher(text);
		StringBuffer result = new StringBuffer();

		while (matcher.find()) {
			String content = matcher.group(1);
			matcher.appendReplacement(result, "<b>" + content + "</b>");
		}
		matcher.appendTail(result);

		return result.toString();
	}

	/**
	 * Creates an anchor link from group number and name.
	 */
	public String createMarkdownAnchor(String groupNumber, String groupName) {
		if (!groupNumber.matches("\\d+")) {
			throw new IllegalArgumentException("Invalid group number: " + groupNumber);
		}

		String mangledName = groupName.toLowerCase()
				.trim()
				.replace(' ', '-');

		return "#" + groupNumber.trim() + "-" + mangledName;
	}
}
