package io.syrix.utils.baseline;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


public class BaselineGroup {
	private final String groupNumber;
	private final String groupName;
	private final List<Control> controls;
	private final String baselineName;

	private BaselineGroup(Builder builder) {
		this.groupNumber = builder.groupNumber;
		this.groupName = builder.groupName;
		this.controls = Collections.unmodifiableList(new ArrayList<>(builder.controls));
		this.baselineName = builder.baselineName;
	}

	public String getGroupNumber() {
		return groupNumber;
	}

	public String getGroupName() {
		return groupName;
	}

	public List<Control> getControls() {
		return controls;
	}

	public String getBaselineName() {
		return baselineName;
	}

	public static class Builder {
		private String groupNumber;
		private String groupName;
		private List<Control> controls = new ArrayList<>();
		private String baselineName;

		public Builder groupNumber(String groupNumber) {
			this.groupNumber = groupNumber;
			return this;
		}

		public Builder groupName(String groupName) {
			this.groupName = groupName;
			return this;
		}

		public Builder baselineName(String baselineName) {
			this.baselineName = baselineName;
			return this;
		}

		public Builder addControl(Control control) {
			this.controls.add(control);
			return this;
		}

		public Builder controls(List<Control> controls) {
			this.controls = new ArrayList<>(controls);
			return this;
		}

		public BaselineGroup build() {
			validate();
			return new BaselineGroup(this);
		}

		private void validate() {
			List<String> missingFields = new ArrayList<>();

			if (groupNumber == null || groupNumber.trim().isEmpty()) {
				missingFields.add("groupNumber");
			}
			if (groupName == null || groupName.trim().isEmpty()) {
				missingFields.add("groupName");
			}
			if (baselineName == null || baselineName.trim().isEmpty()) {
				missingFields.add("baselineName");
			}
			if (controls == null || controls.isEmpty()) {
				missingFields.add("controls");
			}

			if (!missingFields.isEmpty()) {
				throw new IllegalStateException("Required fields missing: " + String.join(", ", missingFields));
			}

			// Validate group number format
			if (!groupNumber.matches("\\d+")) {
				throw new IllegalStateException("Group number must be numeric: " + groupNumber);
			}
		}
	}

	public static Builder builder() {
		return new Builder();
	}

	@Override
	public String toString() {
		return String.format("BaselineGroup{groupNumber='%s', groupName='%s', controls=%d}",
				groupNumber, groupName, controls.size());
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		BaselineGroup that = (BaselineGroup) o;
		return Objects.equals(groupNumber, that.groupNumber) &&
				Objects.equals(groupName, that.groupName) &&
				Objects.equals(baselineName, that.baselineName) &&
				Objects.equals(controls, that.controls);
	}

	@Override
	public int hashCode() {
		return Objects.hash(groupNumber, groupName, baselineName, controls);
	}
}