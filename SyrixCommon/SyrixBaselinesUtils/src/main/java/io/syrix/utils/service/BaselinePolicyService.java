package io.syrix.utils.service;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import io.syrix.utils.markdown.BaselinePolicyParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Singleton service for managing and accessing CISA M365 Security Configuration Baseline policies.
 * Provides centralized access to all baseline policies with caching and lookup capabilities.
 */
public class BaselinePolicyService {
    
    private static final Logger logger = LoggerFactory.getLogger(BaselinePolicyService.class);
    
    private static final String DEFAULT_BASELINES_PATH = "src/main/resources/baselines";
    private static final String CLASSPATH_BASELINES_PATH = "/baselines";
    
    // Singleton instance
    private static volatile BaselinePolicyService instance;
    private static final Object lock = new Object();
    
    // Internal state
    private final BaselinePolicyParser parser;
    private final Map<String, PolicyBaseline> allPolicies = new ConcurrentHashMap<>();
    private final Map<String, String> policyToFileMap = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> serviceToPolicy = new ConcurrentHashMap<>();
    private boolean initialized = false;
    
    /**
     * Private constructor for singleton pattern.
     */
    private BaselinePolicyService() {
        this.parser = new BaselinePolicyParser();
    }
    
    /**
     * Get the singleton instance of BaselinePolicyService.
     * 
     * @return The singleton instance
     */
    public static BaselinePolicyService getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new BaselinePolicyService();
                }
            }
        }
        return instance;
    }
    
    /**
     * Initialize the service by loading all baseline policies.
     * This method is thread-safe and will only load policies once.
     * 
     * @throws IOException if baseline files cannot be read
     */
    public synchronized void initialize() throws IOException {
        if (initialized) {
            logger.debug("BaselinePolicyService already initialized");
            return;
        }
        
        logger.info("Initializing BaselinePolicyService...");
        
        // Clear any existing data
        allPolicies.clear();
        policyToFileMap.clear();
        serviceToPolicy.clear();
        
        // Try to load from file system first, then classpath
        boolean loaded = loadFromFileSystem() || loadFromClasspath();
        
        if (!loaded) {
            throw new IOException("No baseline files found in file system or classpath");
        }
        
        // Build service index
        buildServiceIndex();
        
        initialized = true;
        logger.info("BaselinePolicyService initialized with {} policies from {} services", 
                   allPolicies.size(), serviceToPolicy.size());
    }
    
    /**
     * Get all baseline policies as a map.
     * 
     * @return Map of policy ID to PolicyBaseline
     * @throws IllegalStateException if service is not initialized
     */
    public Map<String, PolicyBaseline> getAllPolicies() {
        ensureInitialized();
        return new HashMap<>(allPolicies);
    }
    
    /**
     * Get a specific policy by its ID.
     * 
     * @param policyId The policy ID (e.g., "MS.EXO.1.1v1")
     * @return The PolicyBaseline or null if not found
     * @throws IllegalStateException if service is not initialized
     */
    public PolicyBaseline getPolicyById(String policyId) {
        ensureInitialized();
        return allPolicies.get(policyId);
    }
    
    /**
     * Get all policies for a specific service.
     * 
     * @param service The service name (e.g., "EXO", "AAD", "TEAMS")
     * @return List of policies for the service
     * @throws IllegalStateException if service is not initialized
     */
    public List<PolicyBaseline> getPoliciesByService(String service) {
        ensureInitialized();
        Set<String> policyIds = serviceToPolicy.get(service.toUpperCase());
        if (policyIds == null) {
            return new ArrayList<>();
        }
        
        return policyIds.stream()
                .map(allPolicies::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * Get all available service names.
     * 
     * @return Set of service names
     * @throws IllegalStateException if service is not initialized
     */
    public Set<String> getAvailableServices() {
        ensureInitialized();
        return new HashSet<>(serviceToPolicy.keySet());
    }
    
    /**
     * Get policies by criticality level.
     * 
     * @param criticality The criticality level
     * @return List of policies with the specified criticality
     * @throws IllegalStateException if service is not initialized
     */
    public List<PolicyBaseline> getPoliciesByCriticality(PolicyCriticality criticality) {
        ensureInitialized();
        return allPolicies.values().stream()
                .filter(policy -> policy.getCriticality() == criticality)
                .collect(Collectors.toList());
    }
    
    /**
     * Get the source file for a policy.
     * 
     * @param policyId The policy ID
     * @return The source file name or null if not found
     * @throws IllegalStateException if service is not initialized
     */
    public String getPolicySourceFile(String policyId) {
        ensureInitialized();
        return policyToFileMap.get(policyId);
    }
    
    /**
     * Check if the service is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Get statistics about loaded policies.
     * 
     * @return Map containing various statistics
     * @throws IllegalStateException if service is not initialized
     */
    public Map<String, Object> getStatistics() {
        ensureInitialized();
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPolicies", allPolicies.size());
        stats.put("totalServices", serviceToPolicy.size());
        
        // Criticality breakdown
        Map<String, Long> criticalityStats = allPolicies.values().stream()
                .collect(Collectors.groupingBy(
                    policy -> policy.getCriticality().getCode(),
                    Collectors.counting()
                ));
        stats.put("criticalityBreakdown", criticalityStats);
        
        // Service breakdown
        Map<String, Integer> serviceStats = new HashMap<>();
        serviceToPolicy.forEach((service, policies) -> 
            serviceStats.put(service, policies.size()));
        stats.put("serviceBreakdown", serviceStats);
        
        return stats;
    }
    
    /**
     * Force reload of all baseline policies.
     * 
     * @throws IOException if baseline files cannot be read
     */
    public synchronized void reload() throws IOException {
        logger.info("Reloading BaselinePolicyService...");
        initialized = false;
        initialize();
    }
    
    // Private helper methods
    
    private void ensureInitialized() {
        if (!initialized) {
            throw new IllegalStateException("BaselinePolicyService is not initialized. Call initialize() first.");
        }
    }
    
    private boolean loadFromFileSystem() {
        try {
            Path baselineDir = Paths.get(DEFAULT_BASELINES_PATH);
            if (!Files.exists(baselineDir)) {
                logger.debug("File system path not found: {}", baselineDir);
                return false;
            }
            
            logger.info("Loading baseline policies from file system: {}", baselineDir);
            List<Path> markdownFiles = findMarkdownFiles(baselineDir);
            
            if (markdownFiles.isEmpty()) {
                logger.warn("No .md files found in {}", baselineDir);
                return false;
            }
            
            for (Path file : markdownFiles) {
                loadPoliciesFromFile(file, file.getFileName().toString());
            }
            
            return !allPolicies.isEmpty();
            
        } catch (IOException e) {
            logger.error("Error loading from file system: {}", e.getMessage());
            return false;
        }
    }
    
    private boolean loadFromClasspath() {
        try {
            String[] knownFiles = {
                "aad.md", "defender.md", "exo.md", "powerbi.md",
                "powerplatform.md", "sharepoint.md", "teams.md"
            };
            
            List<String> foundFiles = new ArrayList<>();
            for (String filename : knownFiles) {
                if (getClass().getResource(CLASSPATH_BASELINES_PATH + "/" + filename) != null) {
                    foundFiles.add(filename);
                }
            }
            
            if (foundFiles.isEmpty()) {
                logger.warn("No baseline files found in classpath");
                return false;
            }
            
            logger.info("Loading baseline policies from classpath");
            for (String filename : foundFiles) {
                loadPoliciesFromClasspath(filename);
            }
            
            return !allPolicies.isEmpty();
            
        } catch (Exception e) {
            logger.error("Error loading from classpath: {}", e.getMessage());
            return false;
        }
    }
    
    private void loadPoliciesFromFile(Path file, String filename) {
        try {
            logger.debug("Loading policies from file: {}", filename);
            Map<String, PolicyBaseline> filePolicies = parser.parseBaselineFile(file);
            
            for (String policyId : filePolicies.keySet()) {
                policyToFileMap.put(policyId, filename);
            }
            allPolicies.putAll(filePolicies);
            
            logger.debug("Loaded {} policies from {}", filePolicies.size(), filename);
            
        } catch (Exception e) {
            logger.error("Error loading policies from {}: {}", filename, e.getMessage());
        }
    }
    
    private void loadPoliciesFromClasspath(String filename) throws IOException {
        URL resource = getClass().getResource(CLASSPATH_BASELINES_PATH + "/" + filename);
        if (resource == null) return;
        
        // Create temporary file for parser
        Path tempFile = Files.createTempFile("baseline-", "-" + filename);
        try (InputStream inputStream = resource.openStream()) {
            Files.copy(inputStream, tempFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        loadPoliciesFromFile(tempFile, filename + " (classpath)");
        
        // Clean up
        Files.deleteIfExists(tempFile);
    }
    
    private List<Path> findMarkdownFiles(Path directory) throws IOException {
        List<Path> files = new ArrayList<>();
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory, "*.md")) {
            for (Path file : stream) {
                files.add(file);
            }
        }
        return files;
    }
    
    private void buildServiceIndex() {
        for (String policyId : allPolicies.keySet()) {
            String service = extractServiceFromPolicyId(policyId);
            serviceToPolicy.computeIfAbsent(service, k -> new HashSet<>()).add(policyId);
        }
    }
    
    private String extractServiceFromPolicyId(String policyId) {
        if (policyId == null || !policyId.contains(".")) {
            return "UNKNOWN";
        }
        String[] parts = policyId.split("\\.");
        return parts.length >= 2 ? parts[1].toUpperCase() : "UNKNOWN";
    }
}
