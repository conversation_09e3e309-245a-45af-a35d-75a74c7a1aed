package io.syrix.datamodel.task.remediation.teams;
/**
 * Configuration for Teams remediation
 */
public class TeamsRemediationConfig {
    private TeamsRemediationStrategy autoAdmittedUsersStrategy;
    private TeamsBroadcastRecordingStrategy broadcastRecordingModeStrategy;

    public TeamsRemediationStrategy getAutoAdmittedUsersStrategy() {
        return autoAdmittedUsersStrategy;
    }

    public void setAutoAdmittedUsersStrategy(TeamsRemediationStrategy autoAdmittedUsersStrategy) {
        this.autoAdmittedUsersStrategy = autoAdmittedUsersStrategy;
    }
    
    public TeamsBroadcastRecordingStrategy getBroadcastRecordingModeStrategy() {
        return broadcastRecordingModeStrategy;
    }

    public void setBroadcastRecordingModeStrategy(TeamsBroadcastRecordingStrategy broadcastRecordingModeStrategy) {
        this.broadcastRecordingModeStrategy = broadcastRecordingModeStrategy;
    }
}
