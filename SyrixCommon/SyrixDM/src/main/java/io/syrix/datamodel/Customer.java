package io.syrix.datamodel;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Model class representing a customer/tenant in the Syrix security platform.
 * This entity stores information about registered companies including their
 * identification, contact details, and status information.
 */
public class Customer extends BaseEntity {
	private String name;
	private String displayName;
	private String microsoftTenantId;
	private String contactEmail;
	private String contactPhone;
	private String status;
	private LocalDateTime createdAt;
	private LocalDateTime updatedAt;

	/**
	 * Default constructor for Customer.
	 * Initializes a new customer with 'active' status.
	 */
	public Customer() {
		this.status = "active";
	}

	/**
	 * Parameterized constructor for Customer.
	 * 
	 * @param id The unique identifier of the Customer
	 * @param name The internal name of the Customer
	 * @param displayName The display name of the Customer
	 * @param microsoftTenantId The Microsoft tenant ID associated with this Customer
	 * @param contactEmail The contact email for the Customer
	 * @param contactPhone The contact phone for the Customer
	 * @param status The status of the Customer (e.g., "active", "inactive")
	 * @param createdAt The date and time when the Customer was created
	 * @param updatedAt The date and time when the Customer was last updated
	 */
	public Customer(UUID id, String name, String displayName, String microsoftTenantId,
					String contactEmail, String contactPhone, String status,
					LocalDateTime createdAt, LocalDateTime updatedAt) {
		this.id = id;
		this.name = name;
		this.displayName = displayName;
		this.microsoftTenantId = microsoftTenantId;
		this.contactEmail = contactEmail;
		this.contactPhone = contactPhone;
		this.status = status;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	/**
	 * Gets the unique identifier of the Customer.
	 * 
	 * @return The Customer's UUID
	 */
	public UUID getId() {
		return id;
	}

	/**
	 * Sets the unique identifier of the Customer.
	 * 
	 * @param id The UUID to set
	 */
	public void setId(UUID id) {
		this.id = id;
	}

	/**
	 * Gets the internal name of the Customer.
	 * 
	 * @return The Customer's name
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the internal name of the Customer.
	 * 
	 * @param name The name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * Gets the display name of the Customer.
	 * 
	 * @return The Customer's display name
	 */
	public String getDisplayName() {
		return displayName;
	}

	/**
	 * Sets the display name of the Customer.
	 * 
	 * @param displayName The display name to set
	 */
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	/**
	 * Gets the Microsoft tenant ID associated with this Customer.
	 * 
	 * @return The Microsoft tenant ID
	 */
	public String getMicrosoftTenantId() {
		return microsoftTenantId;
	}

	/**
	 * Sets the Microsoft tenant ID associated with this Customer.
	 * 
	 * @param microsoftTenantId The Microsoft tenant ID to set
	 */
	public void setMicrosoftTenantId(String microsoftTenantId) {
		this.microsoftTenantId = microsoftTenantId;
	}

	/**
	 * Gets the contact email for the Customer.
	 * 
	 * @return The contact email
	 */
	public String getContactEmail() {
		return contactEmail;
	}

	/**
	 * Sets the contact email for the Customer.
	 * 
	 * @param contactEmail The contact email to set
	 */
	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}

	/**
	 * Gets the contact phone number for the Customer.
	 * 
	 * @return The contact phone number
	 */
	public String getContactPhone() {
		return contactPhone;
	}

	/**
	 * Sets the contact phone number for the Customer.
	 * 
	 * @param contactPhone The contact phone number to set
	 */
	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	/**
	 * Gets the status of the Customer.
	 * 
	 * @return The Customer's status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the status of the Customer.
	 * 
	 * @param status The status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * Gets the creation date and time of the Customer.
	 * 
	 * @return The creation date and time
	 */
	public LocalDateTime getCreatedAt() {
		return createdAt;
	}

	/**
	 * Sets the creation date and time of the Customer.
	 * 
	 * @param createdAt The creation date and time to set
	 */
	public void setCreatedAt(LocalDateTime createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * Gets the last update date and time of the Customer.
	 * 
	 * @return The last update date and time
	 */
	public LocalDateTime getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * Sets the last update date and time of the Customer.
	 * 
	 * @param updatedAt The last update date and time to set
	 */
	public void setUpdatedAt(LocalDateTime updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "Customer{" +
				"id=" + id +
				", name='" + name + '\'' +
				", displayName='" + displayName + '\'' +
				", microsoftTenantId='" + microsoftTenantId + '\'' +
				", contactEmail='" + contactEmail + '\'' +
				", status='" + status + '\'' +
				'}';
	}
}
