package io.syrix.datamodel.task.remediation.exchange;

/**
 * Defines the strictness levels for attachment filtering policies
 * with associated policy names, actions, and file type lists.
 */
public enum AttachmentFilterAction {
	QUARANTINE("Quarantine Attachment Filter", "Quarantine"),
	REJECT("Reject Attachment Filter", "Reject"); // Special case - uses combined list

	private final String policyName;
	private final String action;

	AttachmentFilterAction(String policyName, String action) {
		this.policyName = policyName;
		this.action = action;
	}

	/**
	 * Gets the policy name for this attachment filter action.
	 * @return the policy name
	 */
	public String getPolicyName() {
		return policyName;
	}

	/**
	 * Gets the action to be taken for this attachment filter.
	 * @return the action (Quarantine or Reject)
	 */
	public String getAction() {
		return action;
	}

	/**
	 * Parse from string, with default to LESS_STRICT if invalid
	 */
	public static AttachmentFilterAction fromString(String value) {
		try {
			return valueOf(value.toUpperCase());
		} catch (IllegalArgumentException | NullPointerException e) {
			return QUARANTINE; // Default to less strict if invalid
		}
	}
}