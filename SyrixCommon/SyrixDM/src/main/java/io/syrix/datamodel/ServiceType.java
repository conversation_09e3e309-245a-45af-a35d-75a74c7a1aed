package io.syrix.datamodel;

/**
 * Enum representing types of services that can be connected to the platform.
 * This enum provides standardized identifiers for the different external
 * services supported by the Syrix security platform.
 */
public enum ServiceType {
    /**
     * Represents Microsoft Office 365 service.
     */
    OFFICE365("office365");

    private final String value;
    
    /**
     * Constructor for ServiceType enum.
     * 
     * @param value The string representation of the service type
     */
    ServiceType(String value) { 
        this.value = value; 
    }
    
    /**
     * Gets the string representation of the service type.
     * 
     * @return The value as a string
     */
    public String getValue() { 
        return value; 
    }
    
    @Override 
    public String toString() { 
        return value; 
    }
}
