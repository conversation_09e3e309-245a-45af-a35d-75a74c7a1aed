package io.syrix.datamodel.task.remediation;

import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationTask;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationTask;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationTask;
import io.syrix.datamodel.report.ConfigurationServiceType;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public class RemediationTask extends Task {
	private String retrieveTaskId;
	private UUID connectionId;
	private Instant retrieveDate;

	private List<ConfigurationServiceType> serviceType;

	private SharepointRemediationTask spRemediationTask;
	private TeamsRemediationTask teamsRemediationTask;
	private ExchangeRemediationTask exchangeRemediationTask;

	public String getRetrieveTaskId() {
		return retrieveTaskId;
	}

	public void setRetrieveTaskId(String retrieveTaskId) {
		this.retrieveTaskId = retrieveTaskId;
	}

	public List<ConfigurationServiceType> getServiceType() {
		return serviceType;
	}

	public void setServiceType(List<ConfigurationServiceType> serviceType) {
		this.serviceType = serviceType;
	}

	public SharepointRemediationTask getSpRemediationTask() {
		return spRemediationTask;
	}

	public void setSpRemediationTask(SharepointRemediationTask spRemediationTask) {
		this.spRemediationTask = spRemediationTask;
	}

	public TeamsRemediationTask getTeamsRemediationTask() {
		return teamsRemediationTask;
	}

	public void setTeamsRemediationTask(TeamsRemediationTask teamsRemediationTask) {
		this.teamsRemediationTask = teamsRemediationTask;
	}

	public ExchangeRemediationTask getExchangeRemediationTask() {
		return exchangeRemediationTask;
	}

	public void setExchangeRemediationTask(ExchangeRemediationTask exchangeRemediationTask) {
		this.exchangeRemediationTask = exchangeRemediationTask;
	}

	public UUID getConnectionId() {
		return connectionId;
	}

	public void setConnectionId(UUID connectionId) {
		this.connectionId = connectionId;
	}

	public Instant getRetrieveDate() {
		return retrieveDate;
	}

	public void setRetrieveDate(Instant retrieveDate) {
		this.retrieveDate = retrieveDate;
	}
}
