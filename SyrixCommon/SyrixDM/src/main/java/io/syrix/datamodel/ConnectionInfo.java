package io.syrix.datamodel;

import io.syrix.datamodel.token.Token;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Information about a connection to an external service.
 * This class maintains the state and metadata of connections to various 
 * external services (like Microsoft Office 365) within the Syrix platform.
 */
public class ConnectionInfo extends BaseEntity {
    private ServiceType service;
    private String displayName;
    private LocalDateTime lastConnected;
    private String externalTenantId;
    private UUID customerId;
    private String domain;
    private Token token;
    private LocalDateTime lastUpdatedAt;
    private LocalDateTime createddAt;
    private ConnectionStatus connectionStatus;

    /**
     * Default constructor for ConnectionInfo.
     * Creates an empty connection info object.
     */
    public ConnectionInfo() {
    }

    /**
     * Parameterized constructor for ConnectionInfo.
     * 
     * @param id The unique identifier for this connection
     * @param service The service type this connection is for
     * @param connectionStatus The status of this connection (active/inactive)
     * @param displayName The display name for this connection
     * @param lastConnected The timestamp of the last successful connection
     * @param externalTenantId The tenant ID associated with this connection
     * @param customerId The customer ID this connection belongs to
     */
    public ConnectionInfo(UUID id,
                          ServiceType service,
                          ConnectionStatus connectionStatus,
                          String displayName,
                          LocalDateTime lastConnected,
                          String externalTenantId,
                          UUID customerId) {
        this.id = id;
        this.service = service;
        this.displayName = displayName;
        this.lastConnected = lastConnected;
        this.externalTenantId = externalTenantId;
        this.customerId = customerId;
        this.connectionStatus = connectionStatus;
    }

    /**
     * Extended parameterized constructor for ConnectionInfo that includes domain.
     * 
     * @param id The unique identifier for this connection
     * @param serviceType The service type this connection is for
     * @param connectionStatus The status of this connection (active/inactive)
     * @param displayName The display name for this connection
     * @param lastConnected The timestamp of the last successful connection
     * @param externalTenantId The tenant ID associated with this connection
     * @param customerId The customer ID this connection belongs to
     * @param domain The domain associated with this connection
     */
    public ConnectionInfo(UUID id,
                          ServiceType serviceType,
                          ConnectionStatus connectionStatus,
                          String displayName,
                          LocalDateTime lastConnected,
                          String externalTenantId,
                          UUID customerId,
                          String domain) {
        this.id = id;
        this.service = serviceType;
        this.displayName = displayName;
        this.lastConnected = lastConnected;
        this.externalTenantId = externalTenantId;
        this.customerId = customerId;
        this.domain = domain;
        this.connectionStatus = connectionStatus;
    }

    /**
     * Gets the service type this connection is for.
     * 
     * @return The service type
     */
    public ServiceType getService() {
        return service;
    }

    /**
     * Sets the service type this connection is for.
     * 
     * @param service The service type to set
     */
    public void setService(ServiceType service) {
        this.service = service;
    }

    /**
     * Gets the service type this connection is for.
     * Alias for getService() for backward compatibility.
     * 
     * @return The service type
     */
    public ServiceType getServiceType() { return service; }

    /**
     * Gets the display name for this connection.
     * 
     * @return The display name
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Sets the display name for this connection.
     * 
     * @param displayName The display name to set
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Gets the timestamp of the last successful connection.
     * 
     * @return The last connected timestamp
     */
    public LocalDateTime getLastConnected() {
        return lastConnected;
    }

    /**
     * Sets the timestamp of the last successful connection.
     * 
     * @param lastConnected The last connected timestamp to set
     */
    public void setLastConnected(LocalDateTime lastConnected) {
        this.lastConnected = lastConnected;
    }

    /**
     * Gets the tenant ID associated with this connection.
     * 
     * @return The tenant ID
     */
    public String getExternalTenantId() {
        return externalTenantId;
    }

    /**
     * Sets the tenant ID associated with this connection.
     * 
     * @param externalTenantId The tenant ID to set
     */
    public void setExternalTenantId(String externalTenantId) {
        this.externalTenantId = externalTenantId;
    }

    /**
     * Gets the Customer ID this connection belongs to.
     * 
     * @return The customer ID
     */
    public UUID getCustomerId() {
        return customerId;
    }

    /**
     * Sets the customer ID this connection belongs to.
     * 
     * @param customerId The customer ID to set
     */
    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }

    /**
     * Gets the domain associated with this connection.
     * 
     * @return The domain
     */
    public String getDomain() {
        return domain;
    }

    /**
     * Sets the domain associated with this connection.
     * 
     * @param domain The domain to set
     */
    public void setDomain(String domain) {
        this.domain = domain;
    }

    /**
     * Gets the authentication token for this connection.
     * 
     * @return The authentication token
     */
    public Token getToken() {
        return token;
    }

    /**
     * Sets the authentication token for this connection.
     * 
     * @param token The authentication token to set
     */
    public void setToken(Token token) {
        this.token = token;
    }

    /**
     * Sets the last update timestamp for this connection.
     * 
     * @param time The last update timestamp to set
     */
    public void setUpdatedAt(LocalDateTime time) { this.lastUpdatedAt = time; }

    /**
     * Gets the last update timestamp for this connection.
     * 
     * @return The last update timestamp
     */
    public LocalDateTime getUpdatedAt() { return lastUpdatedAt; }

    /**
     * Sets the creation timestamp for this connection.
     * 
     * @param time The creation timestamp to set
     */
    public void setCreatedAt(LocalDateTime time) { this.createddAt = time; }

    /**
     * Gets the creation timestamp for this connection.
     * 
     * @return The creation timestamp
     */
    public LocalDateTime getCreatedAt() { return createddAt;}

    /**
     * Gets the status of this connection.
     * 
     * @return The connection status
     */
    public ConnectionStatus getStatus() { return this.connectionStatus;}

    /**
     * Sets the status of this connection.
     * 
     * @param connectionStatus The connection status to set
     */
    public void setStatus(ConnectionStatus connectionStatus) { this.connectionStatus = connectionStatus; }
}
