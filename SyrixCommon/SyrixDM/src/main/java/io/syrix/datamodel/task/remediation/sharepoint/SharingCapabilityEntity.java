package io.syrix.datamodel.task.remediation.sharepoint;

/**
 * copy SharingCapability for save
>*/
public enum SharingCapabilityEntity {
    DISABLE(0), //ONLYPEOPLEINORG  External user sharing (share by email) and guest link sharing are both disabled.
    EXTERNAL_USER_SHARING_ONLY(1), //NEWANDEXISTINGGUESTS -- External user sharing (share by email) is enabled, but guest link sharing is disabled.
    EXTERNAL_USER_AND_GUEST_SHARING(2),  //ANYONE -- External user sharing (share by email) and guest link sharing are both enabled.
    EXISTING_EXTERNAL_USER_SHARING_ONLY(3); //EXISTINGGUESTS  -- РOnly guests already in your organization's directory.

    private final int intVal;

    SharingCapabilityEntity(int intVal) {
        this.intVal = intVal;
    }

    public int asInt() {
        return intVal;
    }

}
