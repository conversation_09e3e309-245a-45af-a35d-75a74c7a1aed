package io.syrix.datamodel.task;

import io.syrix.datamodel.BaseEntity;

import java.time.Instant;
import java.util.UUID;

public class Task extends BaseEntity {
	private UUID customerId;

	private TaskType type;
	private TaskStatus status;
	private Instant createdAt;

	public UUID getCustomerId() {
		return customerId;
	}

	public void setCustomerId(UUID customerId) {
		this.customerId = customerId;
	}

	public TaskType getType() {
		return type;
	}

	public void setType(TaskType type) {
		this.type = type;
	}

	public TaskStatus getStatus() {
		return status;
	}

	public void setStatus(TaskStatus status) {
		this.status = status;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}
}
