package io.syrix.datamodel.task;

import io.syrix.datamodel.report.ConfigurationServiceType;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public class RetrieveTask extends Task {
	private UUID connectionId;
	private String externalTenantId;

	private List<ConfigurationServiceType> serviceTypeList;

	public RetrieveTask() {
		this.setType(TaskType.RETRIEVE_CONFIGURATION);
		this.setCreatedAt(Instant.now());
	}

	public List<ConfigurationServiceType> getServiceTypeList() {
		return serviceTypeList;
	}

	public void setServiceTypeList(List<ConfigurationServiceType> serviceTypeList) {
		this.serviceTypeList = serviceTypeList;
	}

	public UUID getConnectionId() {
		return connectionId;
	}

	public void setConnectionId(UUID connectionId) {
		this.connectionId = connectionId;
	}

	public String getExternalTenantId() {
		return externalTenantId;
	}

	public void setExternalTenantId(String externalTenantId) {
		this.externalTenantId = externalTenantId;
	}
}
