package io.syrix.datamodel.oauth;

import io.syrix.datamodel.ServiceType;

/**
 * Microsoft-specific OAuth2 configuration for Office 365.
 * This class implements the OAuthConfig interface and provides
 * Microsoft Authentication Library (MSAL) specific implementation 
 * for OAuth2 authentication with Microsoft services.
 */
public class MSALOAuthConfig implements OAuthConfig {
    // Base OAuth fields
    private final String clientId;
    private final String clientSecret;
    private final String redirectUri;
    private final String scopes;
    private final String authUrl;
    private final String tokenUrl;
    private final ServiceType serviceType;
    private String id;


    /**
     * Constructor with all fields except serviceType, which defaults to OFFICE365.
     * 
     * @param clientId The client ID for OAuth2 authentication
     * @param clientSecret The client secret for OAuth2 authentication
     * @param redirectUri The redirect URI for OAuth2 authentication
     * @param scopes The scopes for OAuth2 authentication
     * @param authUrl The authorization URL for OAuth2 authentication
     * @param tokenUrl The token URL for OAuth2 authentication
     */
    public MSALOAuthConfig(
            String clientId, 
            String clientSecret, 
            String redirectUri,
            String scopes, 
            String authUrl, 
            String tokenUrl) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.redirectUri = redirectUri;
        this.scopes = scopes;
        this.authUrl = authUrl;
        this.tokenUrl = tokenUrl;
        this.serviceType = ServiceType.OFFICE365;
    }
    
    /**
     * Constructor with all fields including serviceType.
     * 
     * @param serviceType The service type for this OAuth2 configuration
     * @param clientId The client ID for OAuth2 authentication
     * @param clientSecret The client secret for OAuth2 authentication
     * @param redirectUri The redirect URI for OAuth2 authentication
     * @param scopes The scopes for OAuth2 authentication
     * @param authUrl The authorization URL for OAuth2 authentication
     * @param tokenUrl The token URL for OAuth2 authentication
     */
    public MSALOAuthConfig(
            ServiceType serviceType,
            String clientId, 
            String clientSecret, 
            String redirectUri,
            String scopes, 
            String authUrl, 
            String tokenUrl) {
        this.serviceType = serviceType;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.redirectUri = redirectUri;
        this.scopes = scopes;
        this.authUrl = authUrl;
        this.tokenUrl = tokenUrl;
    }
    
    // Implement getters from OAuthConfig interface
    @Override
    public String getClientId() {
        return clientId;
    }
    
    @Override
    public String getClientSecret() {
        return clientSecret;
    }
    
    @Override
    public String getRedirectUri() {
        return redirectUri;
    }
    
    @Override
    public String getScopes() {
        return scopes;
    }
    
    @Override
    public String getAuthUrl() {
        return authUrl;
    }
    
    @Override
    public String getTokenUrl() {
        return tokenUrl;
    }
    
    @Override
    public boolean isValid() {
        return clientId != null && !clientId.isEmpty() && redirectUri != null && !redirectUri.isEmpty();
    }
    
    @Override
    public String getDisplayName() {
        return "Microsoft (Office 365)";
    }
    
    @Override
    public ServiceType getServiceType() {
        return serviceType != null ? serviceType : ServiceType.OFFICE365;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    /**
     * Creates a new builder for MSALOAuthConfig.
     * 
     * @return A new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Builder class for creating MSALOAuthConfig instances.
     * This class provides a fluent API for constructing MSALOAuthConfig objects.
     */
    public static class Builder {
        private String clientId = "";
        private String clientSecret = "";
        private String redirectUri = "http://localhost:8080/oauth2/callback";
        private String scopes = "openid profile User.Read offline_access";
        private String authUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
        private String tokenUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
        private ServiceType serviceType = ServiceType.OFFICE365;
        
        /**
         * Default constructor that initializes the builder with default values.
         * This creates a builder instance with pre-configured default values for
         * all OAuth2 configuration properties.
         */
        public Builder() {
            // Default initialization occurs with field declarations
        }
        
        /**
         * Sets the client ID for the OAuth2 configuration.
         * 
         * @param clientId The client ID to set
         * @return This builder instance
         */
        public Builder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }
        
        /**
         * Sets the client secret for the OAuth2 configuration.
         * 
         * @param clientSecret The client secret to set
         * @return This builder instance
         */
        public Builder clientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
            return this;
        }
        
        /**
         * Sets the redirect URI for the OAuth2 configuration.
         * 
         * @param redirectUri The redirect URI to set
         * @return This builder instance
         */
        public Builder redirectUri(String redirectUri) {
            this.redirectUri = redirectUri;
            return this;
        }
        
        /**
         * Sets the scopes for the OAuth2 configuration.
         * 
         * @param scopes The scopes to set
         * @return This builder instance
         */
        public Builder scopes(String scopes) {
            this.scopes = scopes;
            return this;
        }
        
        /**
         * Sets the authorization URL for the OAuth2 configuration.
         * 
         * @param authUrl The authorization URL to set
         * @return This builder instance
         */
        public Builder authUrl(String authUrl) {
            this.authUrl = authUrl;
            return this;
        }
        
        /**
         * Sets the token URL for the OAuth2 configuration.
         * 
         * @param tokenUrl The token URL to set
         * @return This builder instance
         */
        public Builder tokenUrl(String tokenUrl) {
            this.tokenUrl = tokenUrl;
            return this;
        }
        
        /**
         * Sets the service type for the OAuth2 configuration.
         * 
         * @param serviceType The service type to set
         * @return This builder instance
         */
        public Builder serviceType(ServiceType serviceType) {
            this.serviceType = serviceType;
            return this;
        }
        
        /**
         * Builds a new MSALOAuthConfig with the configured properties.
         * 
         * @return A new MSALOAuthConfig instance
         */
        public MSALOAuthConfig build() {
            return new MSALOAuthConfig(serviceType, clientId, clientSecret, redirectUri, scopes, authUrl, tokenUrl);
        }
    }
}
