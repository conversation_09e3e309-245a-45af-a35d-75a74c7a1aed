package io.syrix.datamodel.task.remediation.exchange;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration class for anti-phishing remediation settings.
 * Contains configuration parameters for anti-phishing policy remediation.
 */
public class AntiPhishingRemediationConfig {
    private String policyName;
    private List<String> protectedUsers = new ArrayList<>();
    private List<String> protectedDomains = new ArrayList<>();
    private List<String> recipientDomain = new ArrayList<>();
    private Boolean enableFirstContactSafetyTips;
    private Boolean enableSimilarUsersSafetyTips;
    private Boolean enableSimilarDomainsSafetyTips;
    private Boolean enableUnusualCharactersSafetyTips;
    private Boolean enableMailboxIntelligence;
    private String mailboxIntelligenceAction;
    private Integer phishThresholdLevel;

    /**
     * Default constructor for AntiPhishingRemediationConfig.
     * Initializes the configuration with default values.
     */
    public AntiPhishingRemediationConfig() {
        // Default constructor
    }

    /**
     * Gets the policy name for the anti-phishing configuration.
     * @return the policy name
     */
    public String getPolicyName() {
        return policyName;
    }

    /**
     * Gets the rule name based on the policy name.
     * @return the rule name formatted as policyName + "-Rule"
     */
    public String getRuleName() {
        return policyName + "-Rule";
    }

    /**
     * Sets the policy name for the anti-phishing configuration.
     * @param policyName the policy name to set
     */
    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    /**
     * Gets the list of protected users for the anti-phishing policy.
     * @return the list of protected users
     */
    public List<String> getProtectedUsers() {
        return protectedUsers;
    }

    /**
     * Sets the list of protected users for the anti-phishing policy.
     * @param protectedUsers the list of protected users to set
     */
    public void setProtectedUsers(List<String> protectedUsers) {
        this.protectedUsers = protectedUsers;
    }

    /**
     * Gets the list of protected domains for the anti-phishing policy.
     * @return the list of protected domains
     */
    public List<String> getProtectedDomains() {
        return protectedDomains;
    }

    /**
     * Sets the list of protected domains for the anti-phishing policy.
     * @param protectedDomains the list of protected domains to set
     */
    public void setProtectedDomains(List<String> protectedDomains) {
        this.protectedDomains = protectedDomains;
    }

    /**
     * Gets the list of recipient domains for the anti-phishing policy.
     * @return the list of recipient domains
     */
    public List<String> getRecipientDomain() {
        return recipientDomain;
    }

    /**
     * Sets the list of recipient domains for the anti-phishing policy.
     * @param recipientDomain the list of recipient domains to set
     */
    public void setRecipientDomain(List<String> recipientDomain) {
        this.recipientDomain = recipientDomain;
    }

    /**
     * Gets the setting for enabling first contact safety tips.
     * @return true if first contact safety tips are enabled, false otherwise
     */
    public Boolean getEnableFirstContactSafetyTips() {
        return enableFirstContactSafetyTips;
    }

    /**
     * Sets the setting for enabling first contact safety tips.
     * @param enableFirstContactSafetyTips true to enable first contact safety tips, false otherwise
     */
    public void setEnableFirstContactSafetyTips(Boolean enableFirstContactSafetyTips) {
        this.enableFirstContactSafetyTips = enableFirstContactSafetyTips;
    }

    /**
     * Gets the setting for enabling similar users safety tips.
     * @return true if similar users safety tips are enabled, false otherwise
     */
    public Boolean getEnableSimilarUsersSafetyTips() {
        return enableSimilarUsersSafetyTips;
    }

    /**
     * Sets the setting for enabling similar users safety tips.
     * @param enableSimilarUsersSafetyTips true to enable similar users safety tips, false otherwise
     */
    public void setEnableSimilarUsersSafetyTips(Boolean enableSimilarUsersSafetyTips) {
        this.enableSimilarUsersSafetyTips = enableSimilarUsersSafetyTips;
    }

    /**
     * Gets the setting for enabling similar domains safety tips.
     * @return true if similar domains safety tips are enabled, false otherwise
     */
    public Boolean getEnableSimilarDomainsSafetyTips() {
        return enableSimilarDomainsSafetyTips;
    }

    /**
     * Sets the setting for enabling similar domains safety tips.
     * @param enableSimilarDomainsSafetyTips true to enable similar domains safety tips, false otherwise
     */
    public void setEnableSimilarDomainsSafetyTips(Boolean enableSimilarDomainsSafetyTips) {
        this.enableSimilarDomainsSafetyTips = enableSimilarDomainsSafetyTips;
    }

    /**
     * Gets the setting for enabling unusual characters safety tips.
     * @return true if unusual characters safety tips are enabled, false otherwise
     */
    public Boolean getEnableUnusualCharactersSafetyTips() {
        return enableUnusualCharactersSafetyTips;
    }

    /**
     * Sets the setting for enabling unusual characters safety tips.
     * @param enableUnusualCharactersSafetyTips true to enable unusual characters safety tips, false otherwise
     */
    public void setEnableUnusualCharactersSafetyTips(Boolean enableUnusualCharactersSafetyTips) {
        this.enableUnusualCharactersSafetyTips = enableUnusualCharactersSafetyTips;
    }

    /**
     * Gets the setting for enabling mailbox intelligence.
     * @return true if mailbox intelligence is enabled, false otherwise
     */
    public Boolean getEnableMailboxIntelligence() {
        return enableMailboxIntelligence;
    }

    /**
     * Sets the setting for enabling mailbox intelligence.
     * @param enableMailboxIntelligence true to enable mailbox intelligence, false otherwise
     */
    public void setEnableMailboxIntelligence(Boolean enableMailboxIntelligence) {
        this.enableMailboxIntelligence = enableMailboxIntelligence;
    }

    /**
     * Gets the action to take when mailbox intelligence is triggered.
     * @return the mailbox intelligence action
     */
    public String getMailboxIntelligenceAction() {
        return mailboxIntelligenceAction;
    }

    /**
     * Sets the action to take when mailbox intelligence is triggered.
     * @param mailboxIntelligenceAction the mailbox intelligence action to set
     */
    public void setMailboxIntelligenceAction(String mailboxIntelligenceAction) {
        this.mailboxIntelligenceAction = mailboxIntelligenceAction;
    }

    /**
     * Gets the phishing threshold level for the anti-phishing policy.
     * @return the phishing threshold level
     */
    public Integer getPhishThresholdLevel() {
        return phishThresholdLevel;
    }

    /**
     * Sets the phishing threshold level for the anti-phishing policy.
     * @param phishThresholdLevel the phishing threshold level to set
     */
    public void setPhishThresholdLevel(Integer phishThresholdLevel) {
        this.phishThresholdLevel = phishThresholdLevel;
    }
}
