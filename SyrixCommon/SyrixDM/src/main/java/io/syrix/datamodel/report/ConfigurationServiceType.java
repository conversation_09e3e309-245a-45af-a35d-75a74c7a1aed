package io.syrix.datamodel.report;

public enum ConfigurationServiceType {
    ENTRA("aad", "Microsoft Entra ID"),
    TEAMS("teams", "Microsoft Teams"),
    SHAREPOINT("sharepoint", "Microsoft SharePoint"),
    DEFENDER("defender", "Microsoft Defender Antivirus"),
    POWER_PLATFORM("powerplatform", "Microsoft Power Platform"),
    EXCHANGE_ONLINE("exo", "Microsoft Exchange Online"),
    FORMS("forms", "Microsoft Forms"),
    //    DYNAMICS("dynamics", "Microsoft Service"),
    ALL("all", "All Microsoft Services");

    private final String resultName;
    private final String fullName;

    ConfigurationServiceType(String resultName, String fullName) {
        this.resultName = resultName;
        this.fullName = fullName;
    }

    public String getResultName() {
        return resultName;
    }

    public String getFullName() {
        return fullName;
    }

    public static ConfigurationServiceType fromResultName(String value) {
        for (ConfigurationServiceType b : ConfigurationServiceType.values()) {
            if (b.resultName.equalsIgnoreCase(value)) {
                return b;
            }
        }
        throw new IllegalArgumentException("Unknown ConfigurationServiceType: " + value);
    }
}
