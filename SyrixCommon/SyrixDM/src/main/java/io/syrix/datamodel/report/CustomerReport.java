package io.syrix.datamodel.report;

import io.syrix.datamodel.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class CustomerReport extends BaseEntity {
	private UUID customerId;
	private LocalDateTime createdAt;
	private Map<ConfigurationServiceType, List<PolicyResult>> results;

	public UUID getCustomerId() {
		return customerId;
	}

	public void setCustomerId(UUID customerId) {
		this.customerId = customerId;
	}

	public LocalDateTime getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(LocalDateTime createdAt) {
		this.createdAt = createdAt;
	}

	public Map<ConfigurationServiceType, List<PolicyResult>> getResults() {
		return results;
	}

	public void setResults(Map<ConfigurationServiceType, List<PolicyResult>> results) {
		this.results = results;
	}
}