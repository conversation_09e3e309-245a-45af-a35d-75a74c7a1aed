package io.syrix.datamodel;

/**
 * Enum representing the status of a connection to an external service.
 * Used to indicate whether a connection is currently active or inactive.
 */
public enum ConnectionStatus {
    /**
     * Indicates an active connection.
     */
    ACTIVE("active"),
    
    /**
     * Indicates an inactive connection.
     */
    INACTIVE("inactive");

    private final String status;
    
    /**
     * Constructor for ConnectionStatus enum.
     * 
     * @param status The string representation of the status
     */
    ConnectionStatus(String status) {
        this.status = status;
    }
    
    /**
     * Gets the string representation of the connection status.
     * 
     * @return The status as a string
     */
    public String getStatus() {
        return status;
    }
}
