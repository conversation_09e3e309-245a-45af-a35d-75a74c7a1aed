package io.syrix.datamodel.oauth;

import io.syrix.datamodel.ServiceType;

/**
 * Base interface for all OAuth2 configurations.
 * This interface defines the common methods that all OAuth2 configuration
 * implementations must provide.
 */
public interface OAuthConfig {
    /**
     * Constant for the service type key used in serialization.
     */
    public static final String SERVICE_TYPE = "service_type";

    /**
     * Retrieves the client ID for the OAuth2 configuration.
     *
     * @return The client ID as a String.
     */
    String getClientId();

    /**
     * Retrieves the client secret for the OAuth2 configuration.
     *
     * @return The client secret as a String.
     */
    String getClientSecret();

    /**
     * Retrieves the redirect URI for the OAuth2 configuration.
     *
     * @return The redirect URI as a String.
     */
    String getRedirectUri();

    /**
     * Retrieves the scopes for the OAuth2 configuration.
     *
     * @return The scopes as a String.
     */
    String getScopes();

    /**
     * Retrieves the authorization URL for the OAuth2 configuration.
     *
     * @return The authorization URL as a String.
     */
    String getAuthUrl();

    /**
     * Retrieves the token URL for the OAuth2 configuration.
     *
     * @return The token URL as a String.
     */
    String getTokenUrl();

    /**
     * Validates the OAuth2 configuration.
     *
     * @return True if the configuration is valid, false otherwise.
     */
    boolean isValid();

    /**
     * Retrieves the display name for the OAuth2 configuration.
     *
     * @return The display name as a String.
     */
    String getDisplayName();

    /**
     * Retrieves the service type for the OAuth2 configuration.
     *
     * @return The service type as a ServiceType object.
     */
    ServiceType getServiceType();

    /**
     * Sets the unique identifier for the OAuth2 configuration.
     *
     * @param string The unique identifier to set.
     */
    void setId(String string);
}
