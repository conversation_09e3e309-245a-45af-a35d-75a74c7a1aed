package io.syrix.datamodel.task.remediation.exchange;


import java.util.List;

public class AttachmentFilterConfig {
	private String policyName;
	private String ruleName;
	private String adminDisplayName;

	private AttachmentFileTypeList filesTypeList;
	private AttachmentFilterAction action;
	private Boolean zapEnabled;

	//for rule
	private List<String> recipientDomainIs;
	private List<String> sentTo;
	private List<String> sentToMemberOf;

	public String getPolicyName() {
		return policyName;
	}

	public void setPolicyName(String policyName) {
		this.policyName = policyName;
	}

	public String getRuleName() {
		if (ruleName == null && policyName != null) {
			return policyName + " Rule";
		}
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getAdminDisplayName() {
		return adminDisplayName;
	}

	public void setAdminDisplayName(String adminDisplayName) {
		this.adminDisplayName = adminDisplayName;
	}

	public AttachmentFileTypeList getFilesTypeList() {
		return filesTypeList;
	}

	public void setFilesTypeList(AttachmentFileTypeList filesTypeList) {
		this.filesTypeList = filesTypeList;
	}

	public AttachmentFilterAction getAction() {
		return action;
	}

	public void setAction(AttachmentFilterAction action) {
		this.action = action;
	}

	public Boolean getZapEnabled() {
		return zapEnabled;
	}

	public void setZapEnabled(Boolean zapEnabled) {
		this.zapEnabled = zapEnabled;
	}

	public List<String> getRecipientDomainIs() {
		return recipientDomainIs;
	}

	public void setRecipientDomainIs(List<String> recipientDomainIs) {
		this.recipientDomainIs = recipientDomainIs;
	}

	public List<String> getSentTo() {
		return sentTo;
	}

	public void setSentTo(List<String> sentTo) {
		this.sentTo = sentTo;
	}

	public List<String> getSentToMemberOf() {
		return sentToMemberOf;
	}

	public void setSentToMemberOf(List<String> sentToMemberOf) {
		this.sentToMemberOf = sentToMemberOf;
	}
}
