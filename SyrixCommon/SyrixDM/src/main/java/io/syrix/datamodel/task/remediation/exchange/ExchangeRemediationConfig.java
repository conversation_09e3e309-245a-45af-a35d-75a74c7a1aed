package io.syrix.datamodel.task.remediation.exchange;

public class ExchangeRemediationConfig {
	private AttachmentFilterConfig attachmentFilterConfig;
	private AntiPhishingRemediationConfig antiPhishingConfig;
	private String defaultDomain;

	public AttachmentFilterConfig getAttachmentFilterConfig() {
		return attachmentFilterConfig;
	}

	public void setAttachmentFilterConfig(AttachmentFilterConfig attachmentFilterConfig) {
		this.attachmentFilterConfig = attachmentFilterConfig;
	}
	
	public AntiPhishingRemediationConfig getAntiPhishingConfig() {
		return antiPhishingConfig;
	}
	
	public void setAntiPhishingConfig(AntiPhishingRemediationConfig antiPhishingConfig) {
		this.antiPhishingConfig = antiPhishingConfig;
	}
	
	public String getDefaultDomain() {
		return defaultDomain;
	}
	
	public void setDefaultDomain(String defaultDomain) {
		this.defaultDomain = defaultDomain;
	}
}