package io.syrix.datamodel.task.remediation.exchange;


import java.util.*;

/**
 * Enum defining attachment file type lists for malware filtering policies.
 * Provides lists of file extensions that should be blocked in email attachments.
 */
public enum AttachmentFileTypeList {
	/** Base list of high-risk file extensions commonly used by malware. */
	BASE(new String[] {
			"ace", "ani", "apk", "app", "appx", "arj", "bat", "cab", "cmd", "com", "deb", "dex", "dll",
			"docm", "elf", "exe", "hta", "img", "iso", "jar", "jnlp", "kext", "lha", "lib", "library", "lnk",
			"lzh", "macho", "msc", "msi", "msix", "msp", "mst", "pif", "ppa", "ppam", "reg", "rev", "scf",
			"scr", "sct", "sys", "uif", "vb", "vbe", "vbs", "vxd", "wsc", "wsf", "wsh", "xll", "xz", "z"
	}),

	/** Extended list with additional potentially risky file types for comprehensive protection. */
	EXTENDED(new String[] {
			"7z", "7zip", "a", "accdb", "accde", "action", "ade", "adp", "appxbundle", "asf", "asp", "aspx",
			"avi", "bas", "bin", "bundle", "bz", "bz2", "bzip2", "caction", "cer", "chm", "command", "cpl",
			"crt", "csh", "css", "der", "dgz", "dmg", "doc", "docx", "dos", "dot", "dotm", "dtox", "dylib",
			"font", "fxp", "gadget", "gz", "gzip", "hlp", "htm", "html", "imp", "inf", "ins", "ipa",
			"isp", "its", "js", "jse", "ksh", "lnk", "lqy", "mad", "maf", "mag", "mam", "maq", "mar", "mas",
			"mat", "mau", "mav", "maw", "mda", "mdb", "mde", "mdt", "mdw", "mdz", "mht", "mhtml", "mscompress",
			"msh", "msh1", "msh1xml", "msh2", "msh2xml", "mshxml", "msixbundle", "o", "obj", "odp", "ods", "odt",
			"one", "onenote", "ops", "os2", "package", "pages", "pbix", "pcd", "pdb", "pdf", "php", "pkg", "plg",
			"plugin", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx", "prf", "prg", "ps1", "ps1xml", "ps2", "ps2xml",
			"psc1", "psc2", "pst", "pub", "py", "rar", "rpm", "rtf", "scpt", "service", "sh", "shb", "shs", "shtm",
			"shx", "so", "tar", "tarz", "terminal", "tgz", "tmp", "tool", "url", "vhd", "vsd", "vsdm", "vsdx",
			"vsmacros", "vss", "vssx", "vst", "vstm", "vstx", "vsw", "w16", "workflow", "ws", "xhtml", "xla",
			"xlam", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx", "xnk", "zi", "zip", "zipx"
	});

	private final String[] fileTypes;

	AttachmentFileTypeList(String[] fileTypes) {
		this.fileTypes = fileTypes;
	}

	/**
	 * Gets the list of file types for this list type.
	 * IMPORTANT: Use this method when passing to PowerShell to ensure
	 * we get the actual string array, not the enum value.
	 *
	 * @return Array of file extensions
	 */
	public String[] getFileTypes() {
		return fileTypes.clone(); // Return a copy to prevent modification
	}

	/**
	 * Gets the list of file types as a {@code List<String>}
	 *
	 * @return List of file extensions
	 */
	public List<String> getFileTypeList() {
		return Arrays.asList(fileTypes);
	}

	/**
	 * Gets the number of file types in this list
	 *
	 * @return Count of file extensions
	 */
	public int getCount() {
		return fileTypes.length;
	}

	/**
	 * Combines both BASE and EXTENDED lists, removing duplicates
	 *
	 * @return Combined list of unique file extensions
	 */
	public static List<String> getCombinedList() {
		Set<String> combinedSet = new HashSet<>();

		// Add all file types from BASE list
		for (String fileType : BASE.getFileTypes()) {
			combinedSet.add(fileType.toLowerCase());
		}

		// Add all file types from EXTENDED list
		for (String fileType : EXTENDED.getFileTypes()) {
			combinedSet.add(fileType.toLowerCase());
		}

		return new ArrayList<>(combinedSet);
	}

	/**
	 * Get combined file types as a String array for direct use with PowerShell
	 *
	 * @return Array of unique file extensions from both BASE and EXTENDED lists
	 */
	public static String[] getCombinedFileTypesArray() {
		List<String> combined = getCombinedList();
		return combined.toArray(new String[0]);
	}
}