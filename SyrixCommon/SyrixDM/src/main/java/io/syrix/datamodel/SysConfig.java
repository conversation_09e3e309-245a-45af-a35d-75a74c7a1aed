package io.syrix.datamodel;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Model class representing system configuration in the Syrix security platform.
 * This entity stores Microsoft 365 security policy configurations organized by service sections
 * for tenant security assessment and compliance tracking.
 */
public class SysConfig extends BaseEntity {
    private LocalDateTime createDate;
    private LocalDateTime updateDate;
    private Map<String, List<String>> policyIdsBySection;


    public SysConfig() {}


    /**
     * Gets the creation date and time of the configuration.
     * 
     * @return The creation date and time
     */
    public LocalDateTime getCreateDate() {
        return createDate;
    }

    /**
     * Sets the creation date and time of the configuration.
     * 
     * @param createDate The creation date and time to set
     */
    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    /**
     * Gets the last update date and time of the configuration.
     * 
     * @return The last update date and time
     */
    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    /**
     * Sets the last update date and time of the configuration.
     * 
     * @param updateDate The last update date and time to set
     */
    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * Gets the map of security policies grouped by Microsoft service sections.
     * Each key represents a Microsoft service (aad, defender, exo, etc.) and 
     * each value contains the list of PolicyId for that service.
     * 
     * @return Map of PolicyId grouped by service sections
     */
    public Map<String, List<String>> getPolicyIdsBySection() {
        return policyIdsBySection;
    }

    /**
     * Sets the map of security policies grouped by Microsoft service sections.
     * 
     * @param policyIdsBySection Map of PolicyId grouped by service sections to set
     */
    public void setPolicyIdsBySection(Map<String, List<String>> policyIdsBySection) {
        this.policyIdsBySection = policyIdsBySection != null ? new HashMap<>(policyIdsBySection) : new HashMap<>();
        this.updateDate = LocalDateTime.now();
    }

    /**
     * Gets the total number of policies across all Microsoft service sections.
     * 
     * @return Total count of policies
     */
    public int getTotalPolicyCount() {
        return policyIdsBySection.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    /**
     * Gets the number of Microsoft service sections configured.
     * 
     * @return Number of service sections
     */
    public int getSectionCount() {
        return policyIdsBySection.size();
    }

    @Override
    public String toString() {
        return "SysConfig{" +
                "id=" + id +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                ", sectionCount=" + getSectionCount() +
                ", totalPolicyCount=" + getTotalPolicyCount() +
                '}';
    }
}
