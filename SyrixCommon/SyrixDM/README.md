# Syrix Data Model

This module contains the pure data model (POJO) classes for the Syrix security platform.

## Design Principles

This data model module follows these principles:

1. **Clean POJOs**: All classes are plain Java objects with no database-specific annotations or dependencies.
2. **Separation of Concerns**: Data model is separate from database access logic.
3. **Standard Java Types**: Uses standard Java types (e.g., `LocalDateTime` instead of DB-specific date types).
4. **No External Dependencies**: No runtime dependencies on DAOs, JDBC, JPA, or ORM frameworks.

## Usage

These model classes can be used by any database implementation module. To use this module:

```xml
<dependency>
    <groupId>io.syrix</groupId>
    <artifactId>syrix-data-model</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

## Available Models

The following data models are available:

### Core Models

- `Alert`: Represents a security alert in the system
- `AuditLog`: Represents an audit log entry for system operations
- `Company`: Represents a client/tenant in the Syrix security platform
- `ConnectionInfo`: Information about a connection to an external service
- `NotificationData`: Represents notification data with service and application information
- `Risk`: Represents a risk assessment for an application or service
- `ScanStatus`: Represents the scan status for a service
- `SecurityMetrics`: Metrics about security status and compliance
- `ServiceType`: Enum of available service types for connection
- `SystemLogEntry`: Detailed log entry for system operations
- `UserProfile`: User profile information

### OAuth Models

- `OAuthConfig`: Base interface for OAuth2 configurations
- `MSALOAuth2onfig`: Microsoft-specific OAuth2 configuration

## Implementation Notes

Each model class includes:

- Default constructor
- Constructor with required fields
- Full constructor with all fields
- Appropriate getters and setters
- No database annotations or ORM-specific code

## Integration

This data model is designed to be used with the accompanying DAO interfaces and database implementation modules:

1. **dao-interfaces**: Contains DAO interfaces for CRUD operations on these models
2. **dao-impl-{database}**: Each implementation module provides database-specific access to these models
