#!/bin/bash

# Syrix Project Compilation Script
# This script builds the Syrix multi-module Maven project

# Set error handling
set -e

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print banner
echo -e "${CYAN}==============================================${NC}"
echo -e "${CYAN}       Syrix Project Compilation Tool        ${NC}"
echo -e "${CYAN}==============================================${NC}"

# Function to print usage
print_usage() {
  echo -e "\nUsage: ./compile.sh [OPTIONS]"
  echo -e "\nOptions:"
  echo -e "  -h, --help       Show this help message"
  echo -e "  -n, --no-tests   Skip tests during compilation"
  echo -e "  -c, --clean      Include clean phase (default: included)"
  echo -e "  -p, --package    Create package (JAR files) after compilation (default: enabled)"
  echo -e "  -v, --verbose    Show normal Maven build logs instead of progress bar"
  echo -e "\nExamples:"
  echo -e "  ./compile.sh                    # Normal build with progress bar and packaging"
  echo -e "  ./compile.sh -v                # Verbose build with full Maven output"
  echo -e "  ./compile.sh -n                # Build with packaging, skip tests"
  echo -e "  ./compile.sh -v -n              # Verbose build, skip tests"
}

# Parse command line arguments
SKIP_TESTS=false
CLEAN=true
PACKAGE=true  # Default to true - packaging enabled by default
VERBOSE=false

while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      print_usage
      exit 0
      ;;
    -n|--no-tests)
      SKIP_TESTS=true
      shift
      ;;
    -c|--clean)
      CLEAN=true
      shift
      ;;
    -p|--package)
      PACKAGE=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    *)
      echo -e "${RED}Error: Unknown option $1${NC}"
      print_usage
      exit 1
      ;;
  esac
done

# Find the Syrix project root directory
SCRIPT_DIR="$(pwd)"

# Check if we're already in the project root directory (contains mvnw and pom.xml)
if [ -f "./mvnw" ] && [ -f "./pom.xml" ]; then
  SYRIX_ROOT_DIR="$SCRIPT_DIR"
elif [ -f "../mvnw" ] && [ -f "../pom.xml" ]; then
  SYRIX_ROOT_DIR="$(realpath "..")"
else
  echo -e "${RED}Error: Cannot locate Syrix project root directory with Maven wrapper (mvnw)${NC}"
  echo -e "${YELLOW}This script must be run from the main Syrix project directory or a subdirectory${NC}"
  echo -e "${YELLOW}Looking for: mvnw and pom.xml in the project root${NC}"
  exit 1
fi

# Store the original directory
ORIGINAL_DIR="$SCRIPT_DIR"

# Change to the Syrix project root directory
echo -e "${CYAN}Using Syrix project root at: ${NC}$SYRIX_ROOT_DIR"
cd "$SYRIX_ROOT_DIR" || {
  echo -e "${RED}Error: Could not change to Syrix project root directory at $SYRIX_ROOT_DIR${NC}"
  exit 1
}

# Validate Maven wrapper and pom.xml exist
if [ ! -f "./mvnw" ]; then
  echo -e "${RED}Error: Maven wrapper (mvnw) not found in $SYRIX_ROOT_DIR${NC}"
  echo -e "${YELLOW}The project root directory should contain the Maven wrapper.${NC}"
  exit 1
fi

if [ ! -f "./pom.xml" ]; then
  echo -e "${RED}Error: pom.xml not found in $SYRIX_ROOT_DIR${NC}"
  echo -e "${YELLOW}The project root directory should contain the main pom.xml file.${NC}"
  exit 1
fi

# Build Maven command
MAVEN_CMD="./mvnw"
MAVEN_ARGS=""

# Add options to Maven command
if [ "$SKIP_TESTS" = true ]; then
  MAVEN_ARGS="$MAVEN_ARGS -DskipTests"
  echo -e "${YELLOW}Tests will be skipped${NC}"
fi

if [ "$VERBOSE" = false ]; then
  MAVEN_ARGS="$MAVEN_ARGS --no-transfer-progress"  # Reduces output while preserving colors
fi

# Always enable color output
export MAVEN_COLOR=always

# Build Maven goals
MAVEN_GOALS=""

if [ "$CLEAN" = true ]; then
  MAVEN_GOALS="$MAVEN_GOALS clean"
fi

MAVEN_GOALS="$MAVEN_GOALS compile"

if [ "$PACKAGE" = true ]; then
  # Try install first, but fallback to package if install fails due to Maven config issues
  MAVEN_GOALS="$MAVEN_GOALS package"
fi

# Show project modules that will be built
echo -e "\n${CYAN}Project Modules to be built (in order from pom.xml):${NC}"
if [ ! -f "pom.xml" ]; then
  echo -e "${RED}Error: pom.xml not found. Please run this script from the project root directory.${NC}"
  exit 1
fi
module_paths_str_display=$(grep '<module>' pom.xml | awk -F'[<>]' '{print $3}')
module_paths_display=($module_paths_str_display)
for module_path in "${module_paths_display[@]}"; do
    echo -e "  - ${GREEN}${module_path}${NC}"
done


# Display build information
echo -e "\n${CYAN}Build Configuration:${NC}"
echo -e "  - Skip Tests: $([ "$SKIP_TESTS" = true ] && echo -e "${YELLOW}Yes${NC}" || echo -e "No")"
echo -e "  - Clean: $([ "$CLEAN" = true ] && echo -e "Yes" || echo -e "No")"
echo -e "  - Package: $([ "$PACKAGE" = true ] && echo -e "Yes (with install)" || echo -e "No")"
echo -e "  - Verbose Output: $([ "$VERBOSE" = true ] && echo -e "Yes" || echo -e "No")"
echo -e "\n${CYAN}Maven Command:${NC} $MAVEN_CMD $MAVEN_ARGS $MAVEN_GOALS"

# Execute Maven
# Function to draw progress bar
draw_progress_bar() {
  local progress=$1
  local width=50
  local filled=$((progress * width / 100))
  local empty=$((width - filled))

  printf "\r${CYAN}Progress: [${GREEN}"
  printf "%.0s█" $(seq 1 $filled)
  printf "${NC}"
  printf "%.0s░" $(seq 1 $empty)
  printf "${CYAN}] %3d%%${NC}" $progress
}

# Function to show module completion
show_module_completion() {
  local module_name=$1
  echo -e "\n${GREEN}✓${NC} ${module_name} ${GREEN}compiled successfully${NC}"
}

# Function to get module pattern
get_module_pattern() {
  local module_name="$1"
  # Extract the last part of the path, which is the module name
  local pattern_name=$(basename "$module_name")
  # Create a regex pattern that matches either the full path or just the name
  echo "$module_name|$pattern_name"
}

# Function to monitor build progress
monitor_build_progress() {
  local pid=$1
  local build_log=$2
  local progress=0
  local last_size=0
  local build_started=false

  # Dynamically get module names from pom.xml
  local module_paths_str=$(grep '<module>' pom.xml | awk -F'[<>]' '{print $3}')
  local module_paths=($module_paths_str)
  local module_names=()
  for module_path in "${module_paths[@]}"; do
      module_names+=("$(basename "$module_path")")
  done

  local completed_modules=()
  local currently_building=""

  echo -e "\n${CYAN}Starting build process...${NC}"
  draw_progress_bar 0

  while ps -p $pid > /dev/null 2>&1; do
    if [ -f "$build_log" ]; then
      local current_size=$(wc -l < "$build_log" 2>/dev/null || echo "0")

      # Check what's currently being built
      if [ -s "$build_log" ]; then
        local building_line=$(tail -20 "$build_log" | grep -i "\[INFO\] Building" | tail -1 2>/dev/null || echo "")
        if [ -n "$building_line" ]; then
          for module_name in "${module_names[@]}"; do
            local pattern=$(get_module_pattern "$module_name")
            if [ -n "$pattern" ] && echo "$building_line" | grep -qi "$pattern"; then
              if [ "$currently_building" != "$module_name" ]; then
                currently_building="$module_name"
                echo -e "\n${YELLOW}🔨 Building ${module_name}...${NC}"
                build_started=true
              fi
              break
            fi
          done
        fi

        # Check for module completions
        for module_name in "${module_names[@]}"; do
          if [[ ! " ${completed_modules[@]} " =~ " ${module_name} " ]]; then
            local pattern=$(get_module_pattern "$module_name")
            if [ -n "$pattern" ]; then
              # Look for SUCCESS patterns specific to each module
              if grep -qi "\[INFO\] $pattern.*SUCCESS\|"Building".*$pattern.*SUCCESS" "$build_log" 2>/dev/null; then
                completed_modules+=("$module_name")
                show_module_completion "$module_name"
              fi
            fi
          fi
        done
      fi

      # Update progress
      if [ $current_size -gt $last_size ] || [ $build_started = true ]; then
        # Base progress on log activity (up to 50%)
        local base_progress=$((current_size / 20))
        if [ $base_progress -gt 50 ]; then
          base_progress=50
        fi

        # Module completion progress (up to 40%)
        local module_progress=$((${#completed_modules[@]} * 8))

        # Total progress
        progress=$((base_progress + module_progress))

        # Cap at 95% until build completes
        if [ $progress -gt 95 ]; then
          progress=95
        fi

        draw_progress_bar $progress
        last_size=$current_size
      fi
    fi

    sleep 0.3
  done

  # Complete progress bar
  draw_progress_bar 100
  echo -e "\n"
}

# Function to find and display artifact paths
show_artifact_paths() {
  echo -e "\n${CYAN}==============================================${NC}"
  echo -e "${CYAN}           Build Artifacts Generated         ${NC}"
  echo -e "${CYAN}==============================================${NC}"

  local found_artifacts=false

  # Function to display artifacts for a module
  display_module_artifacts() {
    local module_path="$1"
    local module_name="$2"

    if [ -d "$module_path/target" ]; then
      # Find JAR files (excluding sources and javadoc)
      local jars=()
      while IFS= read -r -d '' jar; do
        jars+=("$jar")
      done < <(find "$module_path/target" -name "*.jar" -not -name "*-sources.jar" -not -name "*-javadoc.jar" -not -name "*-tests.jar" -print0 2>/dev/null)

      # Find WAR files
      local wars=()
      while IFS= read -r -d '' war; do
        wars+=("$war")
      done < <(find "$module_path/target" -name "*.war" -print0 2>/dev/null)

      # Display artifacts if found
      if [ ${#jars[@]} -gt 0 ] || [ ${#wars[@]} -gt 0 ]; then
        echo -e "\n${GREEN}📦 ${module_name}:${NC}"

        # Display JAR files
        for jar in "${jars[@]}"; do
          local full_path="$(realpath "$jar")"
          local size=$(ls -lh "$jar" | awk '{print $5}')
          local timestamp=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$jar" 2>/dev/null || stat -c "%y" "$jar" 2>/dev/null | cut -d' ' -f1-2)
          echo -e "  ${YELLOW}• JAR:${NC} $full_path ${CYAN}($size)${NC} ${YELLOW}[$timestamp]${NC}"
          found_artifacts=true
        done

        # Display WAR files
        for war in "${wars[@]}"; do
          local full_path="$(realpath "$war")"
          local size=$(ls -lh "$war" | awk '{print $5}')
          local timestamp=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$war" 2>/dev/null || stat -c "%y" "$war" 2>/dev/null | cut -d' ' -f1-2)
          echo -e "  ${YELLOW}• WAR:${NC} $full_path ${CYAN}($size)${NC} ${YELLOW}[$timestamp]${NC}"
          found_artifacts=true
        done
      fi
    fi
  }

  # Dynamically get module paths from pom.xml
  local module_paths_str_artifacts=$(grep '<module>' pom.xml | awk -F'[<>]' '{print $3}')
  local module_paths_artifacts=($module_paths_str_artifacts)
  for module_path in "${module_paths_artifacts[@]}"; do
      display_module_artifacts "$module_path" "$module_path"
  done

  # Show summary
  if [ "$found_artifacts" = true ]; then
    echo -e "\n${GREEN}✅ Artifacts generated successfully!${NC}"
    if [ "$PACKAGE" = true ]; then
      echo -e "${CYAN}ℹ️  Packaging was enabled - JAR/WAR files ready for deployment${NC}"
    fi
  else
    echo -e "\n${YELLOW}⚠️  No artifacts found.${NC}"
    if [ "$PACKAGE" = false ]; then
      echo -e "${YELLOW}💡 Tip: Use --package option to generate JAR/WAR files${NC}"
    else
      echo -e "${YELLOW}🔍 This might be a compile-only build or no modules were packaged${NC}"
    fi
  fi

  echo -e "\n${CYAN}==============================================${NC}"
}

# Function to display installation artifacts
display_install_artifacts() {
  echo -e "\n${CYAN}==============================================${NC}"
  echo -e "${CYAN}           Installed Artifacts          ${NC}"
  echo -e "${CYAN}==============================================${NC}"
  grep "\[INFO\] Installing" "$BUILD_LOG" || echo -e "${YELLOW}No installation artifacts found.${NC}"
  echo -e "\n${CYAN}==============================================${NC}"
}

# Execute Maven based on verbose mode
if [ "$VERBOSE" = true ]; then
  # Verbose mode: show normal Maven output with real-time logs
  echo -e "\n${CYAN}Starting build process (verbose mode)...${NC}"
  echo -e "${CYAN}==============================================${NC}"

  # Run Maven with direct output (no background process needed)
  $MAVEN_CMD $MAVEN_ARGS $MAVEN_GOALS
  BUILD_RESULT=$?

else
  # Normal mode: use progress bar and capture output
  # Create temporary files for output capture
  BUILD_LOG=$(mktemp)
  BUILD_ERR=$(mktemp)

  # Ensure cleanup on exit
  trap 'rm -f "$BUILD_LOG" "$BUILD_ERR"' EXIT

  # Run Maven build in background and capture output
  $MAVEN_CMD $MAVEN_ARGS $MAVEN_GOALS > "$BUILD_LOG" 2> "$BUILD_ERR" &
  MAVEN_PID=$!

  # Monitor build progress with progress bar and module tracking
  monitor_build_progress $MAVEN_PID "$BUILD_LOG"

  # Wait for Maven to complete and get exit code
  wait $MAVEN_PID
  BUILD_RESULT=$?
fi

# Check build result
if [ $BUILD_RESULT -eq 0 ]; then
  echo -e "${GREEN}✓ Build completed successfully!${NC}\n"

  # Try to run install phase separately to get installation lines
  if [ "$VERBOSE" = false ] && [ "$PACKAGE" = true ]; then
    echo -e "${CYAN}Installing artifacts to local repository...${NC}"

    # Run install phase for each module separately to handle potential config issues
    INSTALL_LOG=$(mktemp)
    ./mvnw $MAVEN_ARGS install -rf :syrix-parent > "$INSTALL_LOG" 2>&1 || true

    # Display installation artifacts from the install run
    if [ -f "$INSTALL_LOG" ] && grep -q "\[INFO\] Installing" "$INSTALL_LOG"; then
      echo -e "\n${CYAN}==============================================${NC}"
      echo -e "${CYAN}           Installed Artifacts          ${NC}"
      echo -e "${CYAN}==============================================${NC}"
      grep "\[INFO\] Installing" "$INSTALL_LOG"
      echo -e "\n${CYAN}==============================================${NC}"
    else
      echo -e "\n${CYAN}==============================================${NC}"
      echo -e "${CYAN}           Installed Artifacts          ${NC}"
      echo -e "${CYAN}==============================================${NC}"
      echo -e "${YELLOW}No installation artifacts found or install phase failed.${NC}"
      echo -e "${CYAN}Build artifacts were successfully packaged.${NC}"
      echo -e "\n${CYAN}==============================================${NC}"
    fi

    # Clean up temp file
    rm -f "$INSTALL_LOG"
  fi

  # Return to the original directory
  cd "$ORIGINAL_DIR" || {
    echo -e "${YELLOW}Warning: Could not return to original directory at $ORIGINAL_DIR${NC}"
  }
  exit 0
else
  echo -e "${RED}✗ Build failed!${NC}\n"

  # Only show detailed error info in non-verbose mode (verbose already showed output)
  if [ "$VERBOSE" = false ]; then
    echo -e "${RED}==============================================${NC}"
    echo -e "${RED}       BUILD FAILURE - Root Cause:          ${NC}"
    echo -e "${RED}==============================================${NC}"

    # Show the last part of the build log to identify the failure
    if [ -s "$BUILD_ERR" ]; then
      echo -e "${RED}Error Output:${NC}"
      cat "$BUILD_ERR"
      echo -e "\n${RED}---------------------------------------------${NC}"
    fi

    if [ -s "$BUILD_LOG" ]; then
      echo -e "${RED}Build Log (last 50 lines):${NC}"
      tail -50 "$BUILD_LOG"
      echo -e "\n${RED}---------------------------------------------${NC}"
    fi

    echo -e "${RED}Full build log saved to: $BUILD_LOG${NC}"
    echo -e "${RED}Full error log saved to: $BUILD_ERR${NC}"
    echo -e "${RED}==============================================${NC}"

    # Don't cleanup temp files on failure so user can inspect them
    trap - EXIT
  else
    echo -e "${RED}Build failed with exit code: $BUILD_RESULT${NC}"
  fi

  # Return to the original directory
  cd "$ORIGINAL_DIR" || {
    echo -e "${YELLOW}Warning: Could not return to original directory at $ORIGINAL_DIR${NC}"
  }

  exit $BUILD_RESULT
fi