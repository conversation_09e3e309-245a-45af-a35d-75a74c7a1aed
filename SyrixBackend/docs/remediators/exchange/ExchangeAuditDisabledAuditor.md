# Exchange Online Audit Disabled Auditor

## Overview

The `ExchangeAuditDisabledAuditor` class provides functionality to audit the `AuditDisabled` setting in Exchange Online organization configuration without performing any remediation. This allows for compliance checking and reporting before taking any action.

**Related Policy ID**: MS.EXO.15.1v1

## Purpose

This auditor exists to:

- Check compliance with security best practices for Exchange Online audit logging
- Generate reports on the current state of the `AuditDisabled` setting
- Provide detailed information about non-compliant configurations
- Support decision-making about whether remediation is needed

## Implementation Details

The auditor performs the following operations:

1. Connects to Exchange Online using the provided PowerShell client
2. Retrieves the organization configuration using the `Get-OrganizationConfig` cmdlet
3. Examines the `AuditDisabled` setting in the configuration
4. Returns a detailed audit result indicating compliance status and current values

### Key Components

- **PowerShell Commands Used**:
  - `Get-OrganizationConfig`: To retrieve the current state of the AuditDisabled setting

- **Error Handling**:
  - Handles connection issues with Exchange Online
  - Handles missing or malformed configuration data
  - Provides detailed error messages for troubleshooting

## Audit Result Format

The auditor returns an `AuditResult` object with the following properties:

- **isCompliant**: Boolean indicating whether the setting is compliant (AuditDisabled = false)
- **failureReason**: If not compliant, contains the reason for non-compliance
- **currentValue**: The current value of the AuditDisabled setting
- **expectedValue**: The expected value for compliance (always "False")

## Usage

The auditor can be used as follows:

```java
PowerShellClient exchangeClient = PowerShellClient.builder()
    .withAppId(appId)
    .withTenantId(tenantId)
    .withCertificatePath(certPath)
    .withCertificatePassword(certPassword)
    .withDomain(domain)
    .withEnvironment(MSEnvironment.COMMERCIAL)
    .withEndpointPath("/adminapi/beta")
    .build();

ExchangeAuditDisabledAuditor auditor = new ExchangeAuditDisabledAuditor(exchangeClient);
CompletableFuture<ExchangeAuditDisabledAuditor.AuditResult> result = auditor.auditAuditDisabledSetting();

// Process the result
result.thenAccept(auditResult -> {
    System.out.println("Is Compliant: " + auditResult.isCompliant());
    System.out.println("Current Value: " + auditResult.getCurrentValue());
    System.out.println("Expected Value: " + auditResult.getExpectedValue());
    
    if (!auditResult.isCompliant()) {
        System.out.println("Failure Reason: " + auditResult.getFailureReason());
    }
});
```

## Integration with Remediation

The auditor is designed to be used in conjunction with the `ExchangeAuditDisabledRemediator` class:

1. Use the auditor to check compliance status
2. If non-compliant, decide whether to perform remediation
3. If remediation is desired, use the remediator class

```java
// First audit
auditor.auditAuditDisabledSetting().thenAccept(auditResult -> {
    if (!auditResult.isCompliant()) {
        System.out.println("Non-compliant configuration detected: " + auditResult.getFailureReason());
        
        // Ask for confirmation to remediate
        if (shouldRemediate()) {
            ExchangeAuditDisabledRemediator remediator = new ExchangeAuditDisabledRemediator(exchangeClient);
            remediator.remediate().thenAccept(result -> {
                System.out.println("Remediation completed with status: " + result.get("status").asText());
            });
        }
    } else {
        System.out.println("Configuration is compliant.");
    }
});
```

## Prerequisites

- Exchange Online PowerShell module
- At minimum, Exchange Administrator role with View-Only Configuration permission
- Valid authentication credentials

## Related Components

- **ExchangeAuditDisabledRemediator**: The companion remediator class that can fix non-compliant settings

## References

- [Exchange Online audit logging overview](https://docs.microsoft.com/en-us/exchange/security-and-compliance/exchange-auditing-reports/exchange-auditing-reports)
- [Exchange Online PowerShell cmdlets reference](https://docs.microsoft.com/en-us/powershell/exchange/exchange-online-powershell)
