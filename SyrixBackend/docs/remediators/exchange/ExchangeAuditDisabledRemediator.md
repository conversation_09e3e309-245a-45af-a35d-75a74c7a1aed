# Exchange Online Audit Disabled Remediator

## Overview

The `ExchangeAuditDisabledRemediator` class implements a security control to ensure that audit logging is enabled in Exchange Online by setting the `AuditDisabled` organization configuration parameter to `false`.

**Policy ID**: MS.EXO.15.1v1

## Security Impact

Enabling audit logging in Exchange Online is critical for:

- Security monitoring and threat detection
- Compliance with regulatory requirements
- Forensic investigation of security incidents
- User activity tracking and accountability
- Supporting legal and audit requirements

When audit logging is disabled (AuditDisabled = true), organizations lose visibility into critical Exchange Online activities, creating a significant security blind spot.

## Implementation Details

The remediator performs the following operations:

1. **Audit**: Checks the current state of the `AuditDisabled` setting in the organization configuration.
2. **Remediate**: If `AuditDisabled` is set to `true`, changes it to `false`.
3. **Verify**: Confirms that the change was successfully applied by re-checking the configuration.

### Key Components

- **PowerShell Commands Used**:
  - `Get-OrganizationConfig`: To retrieve the current state of the AuditDisabled setting
  - `Set-OrganizationConfig -AuditDisabled $false`: To enable audit logging

- **Error Handling**:
  - <PERSON>les connection issues with Exchange Online
  - Handles permission and access rights issues
  - Validates configuration changes after remediation

## Prerequisites

- Exchange Online PowerShell module
- Global Administrator or Exchange Administrator permissions
- Valid authentication credentials with sufficient permissions to modify organization settings

## Usage

The remediator can be used as follows:

```java
PowerShellClient exchangeClient = PowerShellClient.builder()
    .withAppId(appId)
    .withTenantId(tenantId)
    .withCertificatePath(certPath)
    .withCertificatePassword(certPassword)
    .withDomain(domain)
    .withEnvironment(MSEnvironment.COMMERCIAL)
    .withEndpointPath("/adminapi/beta")
    .build();

ExchangeAuditDisabledRemediator remediator = new ExchangeAuditDisabledRemediator(exchangeClient);
CompletableFuture<JsonNode> result = remediator.remediate();

// Process the result
result.thenAccept(jsonNode -> {
    String status = jsonNode.get("status").asText();
    String message = jsonNode.get("message").asText();
    System.out.println("Remediation status: " + status);
    System.out.println("Message: " + message);
});
```

## Response Format

The remediator returns a JSON object with the following structure:

```json
{
  "status": "success|failed",
  "policyId": "MS.EXO.15.1v1",
  "message": "Descriptive message about the outcome"
}
```

## Possible Result Messages

### Success Messages
- "Compliance check passed: AuditDisabled is already set to False"
- "Successfully set AuditDisabled to False, audit logging is now enabled"

### Error Messages
- "Failed to retrieve organization configuration: Empty or invalid response"
- "Failed to extract organization config from response"
- "Failed to enable audit logging: [specific error message]"
- "Failed to validate configuration: [specific error message]"
- "Failed to enable audit logging: Configuration change did not take effect"

## Related Components

- **ExchangeAuditDisabledAuditor**: A companion class that can audit the `AuditDisabled` setting without performing remediation
- **ExchangeMailboxAuditRemediator**: A related remediator that enables mailbox auditing organization-wide

## References

- [Exchange Online audit logging best practices](https://docs.microsoft.com/en-us/exchange/security-and-compliance/exchange-auditing-reports/exchange-auditing-reports)
- [Microsoft 365 security best practices](https://docs.microsoft.com/en-us/microsoft-365/security/office-365-security/security-best-practices)
