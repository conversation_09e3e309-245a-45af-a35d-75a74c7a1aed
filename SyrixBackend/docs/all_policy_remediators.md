# Microsoft 365 Security Remediator Documentation

This document provides comprehensive details about all available security remediators for Microsoft 365 services.

## Table of Contents

1. [Defender](#defender)
2. [Entra ID](#entra-id)
3. [Exchange Online](#exchange-online)
4. [Microsoft 365](#microsoft-365)
5. [SharePoint](#sharepoint)
6. [Teams](#teams)

## Remediator Documentation

### Defender

This section describes the available security remediators for Defender.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| MS.DEFENDER.1.1v1 | Remediates Defender Preset Security Policies policies. | PolicyType |
| MS.DEFENDER.1.2v1 | Remediates Defender EOP Protection policies. |  |
| MS.DEFENDER.1.3v1 | Remediates Defender Office 365 Protection policies. | PolicyType |
| MS.DEFENDER.1.4v1 | Remediates Defender EOP Strict Protection policies. | List, List, List |
| MS.DEFENDER.1.5v1 | Remediates Defender Office 365 Strict Protection policies. | List, List, List |
| MS.DEFENDER.2.1v1 | Remediates Defender User Impersonation Protection policies. | List |
| MS.DEFENDER.2.2v1 | Remediates Defender Domain Impersonation Protection policies. |  |
| MS.DEFENDER.2.3v1 | Remediates Defender Partner Domain Impersonation Protection policies. | List |
| MS.DEFENDER.3.1v1 | Remediates Defender Safe Attachments policies. |  |
| MS.DEFENDER.5.1v1 | Remediates Defender Required Alerts policies. | String, List |
| MS.DEFENDER.5.2v1 | Remediates Defender Alert Notification Configurator policies. | String, String, List |
| MS.DEFENDER.6.1v1, MS.DEFENDER.6.2v1, MS.DEFENDER.6.3v1 | Remediates Defender Audit policies. |  |

#### Detailed Information

**MS.DEFENDER.1.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator` **Description:** Remediates Defender Preset Security Policies policies. **Policy IDs:** MS.DEFENDER.1.1v1



**Required Parameters**
* **policyType** - Select the policytype from these options:
  * STANDARD (Standard): Applies standard level of security protection
  * STRICT (Strict): Applies strict level of security protection


**Parameters Summary**
This policy requires the user to select a policyType (STANDARD or STRICT).


---

**MS.DEFENDER.1.2v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderEOPProtectionRemediator` **Description:** Remediates Defender EOP Protection policies. **Policy IDs:** MS.DEFENDER.1.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.DEFENDER.1.3v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderOffice365ProtectionRemediator` **Description:** Remediates Defender Office 365 Protection policies. **Policy IDs:** MS.DEFENDER.1.3v1



**Required Parameters**
* **policyType** - Select the policytype from these options:
  * STANDARD (Standard): Applies standard level of security protection
  * STRICT (Strict): Applies strict level of security protection


**Parameters Summary**
This policy requires the user to select a policyType (STANDARD or STRICT).


---

**MS.DEFENDER.1.4v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderEOPStrictProtectionRemediator` **Description:** Remediates Defender EOP Strict Protection policies. **Policy IDs:** MS.DEFENDER.1.4v1



**Required Parameters**
* **excludedUsers** - Provide the excludedusers
* **excludedGroups** - Provide the excludedgroups
* **sensitiveAccounts** - Provide the sensitiveaccounts

**Parameters Summary**
This policy requires the user to provide excludedUsers and the user to provide excludedGroups and the user to provide sensitiveAccounts.


---

**MS.DEFENDER.1.5v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderOffice365StrictProtectionRemediator` **Description:** Remediates Defender Office 365 Strict Protection policies. **Policy IDs:** MS.DEFENDER.1.5v1



**Required Parameters**
* **excludedUsers** - Provide the excludedusers
* **excludedGroups** - Provide the excludedgroups
* **sensitiveAccounts** - Provide the sensitiveaccounts

**Parameters Summary**
This policy requires the user to provide excludedUsers and the user to provide excludedGroups and the user to provide sensitiveAccounts.


---

**MS.DEFENDER.2.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderUserImpersonationProtectionRemediator` **Description:** Remediates Defender User Impersonation Protection policies. **Policy IDs:** MS.DEFENDER.2.1v1



**Required Parameters**
* **sensitiveAccounts** - Provide the sensitiveaccounts

**Parameters Summary**
This policy requires the user to provide sensitiveAccounts.


---

**MS.DEFENDER.2.2v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderDomainImpersonationProtectionRemediator` **Description:** Remediates Defender Domain Impersonation Protection policies. **Policy IDs:** MS.DEFENDER.2.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.DEFENDER.2.3v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderPartnerDomainImpersonationProtectionRemediator` **Description:** Remediates Defender Partner Domain Impersonation Protection policies. **Policy IDs:** MS.DEFENDER.2.3v1



**Required Parameters**
* **partnerDomains** - Provide the partnerdomains

**Parameters Summary**
This policy requires the user to provide partnerDomains.


---

**MS.DEFENDER.3.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderSafeAttachmentsRemediator` **Description:** Remediates Defender Safe Attachments policies. **Policy IDs:** MS.DEFENDER.3.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.DEFENDER.5.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderRequiredAlertsRemediator` **Description:** Remediates Defender Required Alerts policies. **Policy IDs:** MS.DEFENDER.5.1v1



**Required Parameters**
* **tenantId** - Select the tenantid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **additionalAlerts** - Provide the additionalalerts

**Parameters Summary**
This policy requires the user to select a tenantId (true or false) and the user to provide additionalAlerts.


---

**MS.DEFENDER.5.2v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderAlertNotificationConfigurator` **Description:** Remediates Defender Alert Notification Configurator policies. **Policy IDs:** MS.DEFENDER.5.2v1



**Required Parameters**
* **notificationRecipient** - Select the notificationrecipient from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **tenantId** - Select the tenantid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **additionalAlerts** - Provide the additionalalerts

**Parameters Summary**
This policy requires the user to select a notificationRecipient (true or false) and the user to select a tenantId (true or false) and the user to provide additionalAlerts.


---

**MS.DEFENDER.6.1v1, MS.DEFENDER.6.2v1, MS.DEFENDER.6.3v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderAuditRemediator` **Description:** Remediates Defender Audit policies. **Policy IDs:** MS.DEFENDER.6.1v1, MS.DEFENDER.6.2v1, MS.DEFENDER.6.3v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

### Entra ID

This section describes the available security remediators for Entra ID.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| MS.AAD.1.1v1 | Remediates Entra ID Legacy Auth policies. |  |
| MS.AAD.2.1v1 | Remediates Entra ID High Risk Blocking policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.2.2v1 | Remediates Entra ID High Risk Notification policies. |  |
| MS.AAD.2.3v1 | Remediates Entra ID High Risk Sign In policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.3.1v1 | Remediates Entra ID Phishing Resistant Mfa policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.3.2v1 | Remediates Entra ID Alternative MFA policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState, EntraIDAuthenticationStrength, boolean |
| MS.AAD.3.3v1 | Remediates Entra ID Authenticator Context policies. |  |
| MS.AAD.3.4v1 | Remediates Entra ID Auth Methods Migration policies. |  |
| MS.AAD.3.5v1 | Remediates Entra ID Disable Weak Auth Methods policies. |  |
| MS.AAD.3.6v1 | Remediates Entra ID Privileged Mfa policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.3.7v1 | Remediates Entra ID Managed Device policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.3.8v1 | Remediates Entra ID Mfa Registration policies. | ConditionalAccessUserConfiguration, ConditionalPolisyState |
| MS.AAD.5.1v1 | Remediates Entra ID App Registration policies. |  |
| MS.AAD.5.2v1 | Remediates Entra ID App Consent policies. |  |
| MS.AAD.5.3v1 | Remediates Entra ID Admin Approval Workflow policies. | List |
| MS.AAD.5.4v1 | Remediates Entra ID Group Consent policies. |  |
| MS.AAD.6.1v1 | Remediates Entra ID Password Expiry policies. | String |
| MS.AAD.8.1v1 | Remediates Entra ID Guest Directory Access policies. |  |
| MS.AAD.8.2v1 | Remediates Entra ID Guest Invitation policies. |  |
| MS.AAD.8.3v1 | Remediates Entra ID Guest Domain Restriction policies. | List, List, boolean |
| MS.AAD.8.4v1 | Remediates Entra ID Groups Privacy policies. |  |
| MS.AAD.8.5v1 | Remediates Entra ID Guest Access Review policies. |  |
| MS.AAD.9.1v1 | Remediates Entra ID Tenant Creation Restriction policies. |  |

#### Detailed Information

**MS.AAD.1.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDLegacyAuthRemediator` **Description:** Remediates Entra ID Legacy Auth policies. **Policy IDs:** MS.AAD.1.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.2.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDHighRiskBlockingRemediator` **Description:** Remediates Entra ID High Risk Blocking policies. **Policy IDs:** MS.AAD.2.1v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.2.2v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDHighRiskNotificationRemediator` **Description:** Remediates Entra ID High Risk Notification policies. **Policy IDs:** MS.AAD.2.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.2.3v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDHighRiskSignInRemediator` **Description:** Remediates Entra ID High Risk Sign In policies. **Policy IDs:** MS.AAD.2.3v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.3.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDPhishingResistantMfaRemediator` **Description:** Remediates Entra ID Phishing Resistant Mfa policies. **Policy IDs:** MS.AAD.3.1v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.3.2v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAlternativeMFARemediator` **Description:** Remediates Entra ID Alternative MFA policies. **Policy IDs:** MS.AAD.3.2v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate
* **authenticationStrength** - Provide the authenticationstrength
* **useBuiltInMfa** - Provide the usebuiltinmfa

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState and the user to provide authenticationStrength and the user to provide useBuiltInMfa.


---

**MS.AAD.3.3v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAuthenticatorContextRemediator` **Description:** Remediates Entra ID Authenticator Context policies. **Policy IDs:** MS.AAD.3.3v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.3.4v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAuthMethodsMigrationRemediator` **Description:** Remediates Entra ID Auth Methods Migration policies. **Policy IDs:** MS.AAD.3.4v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.3.5v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDDisableWeakAuthMethodsRemediator` **Description:** Remediates Entra ID Disable Weak Auth Methods policies. **Policy IDs:** MS.AAD.3.5v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.3.6v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDPrivilegedMfaRemediator` **Description:** Remediates Entra ID Privileged Mfa policies. **Policy IDs:** MS.AAD.3.6v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.3.7v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDManagedDeviceRemediator` **Description:** Remediates Entra ID Managed Device policies. **Policy IDs:** MS.AAD.3.7v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.3.8v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDMfaRegistrationRemediator` **Description:** Remediates Entra ID Mfa Registration policies. **Policy IDs:** MS.AAD.3.8v1



**Required Parameters**
* **userConfiguration** - Provide the userconfiguration
* **policyState** - Provide the policystate

**Parameters Summary**
This policy requires the user to provide userConfiguration and the user to provide policyState.


---

**MS.AAD.5.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAppRegistrationRemediator` **Description:** Remediates Entra ID App Registration policies. **Policy IDs:** MS.AAD.5.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.5.2v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAppConsentRemediator` **Description:** Remediates Entra ID App Consent policies. **Policy IDs:** MS.AAD.5.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.5.3v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDAdminApprovalWorkflowRemediator` **Description:** Remediates Entra ID Admin Approval Workflow policies. **Policy IDs:** MS.AAD.5.3v1



**Required Parameters**
* **admins** - Provide the admins

**Parameters Summary**
This policy requires the user to provide admins.


---

**MS.AAD.5.4v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDGroupConsentRemediator` **Description:** Remediates Entra ID Group Consent policies. **Policy IDs:** MS.AAD.5.4v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.6.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDPasswordExpiryRemediator` **Description:** Remediates Entra ID Password Expiry policies. **Policy IDs:** MS.AAD.6.1v1



**Required Parameters**
* **domainName** - Select the domainname from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection


**Parameters Summary**
This policy requires the user to select a domainName (true or false).


---

**MS.AAD.8.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDGuestDirectoryAccessRemediator` **Description:** Remediates Entra ID Guest Directory Access policies. **Policy IDs:** MS.AAD.8.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.8.2v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDGuestInvitationRemediator` **Description:** Remediates Entra ID Guest Invitation policies. **Policy IDs:** MS.AAD.8.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.8.3v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDGuestDomainRestrictionRemediator` **Description:** Remediates Entra ID Guest Domain Restriction policies. **Policy IDs:** MS.AAD.8.3v1



**Required Parameters**
* **allowedDomains** - Provide the alloweddomains
* **blockedDomains** - Provide the blockeddomains
* **overrideDomains** - Provide the overridedomains

**Parameters Summary**
This policy requires the user to provide allowedDomains and the user to provide blockedDomains and the user to provide overrideDomains.


---

**MS.AAD.8.4v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDGroupsPrivacyRemediator` **Description:** Remediates Entra ID Groups Privacy policies. **Policy IDs:** MS.AAD.8.4v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.8.5v1** **Class:** `io.syrix.products.microsoft.entra.accessreview.EntraIDGuestAccessReviewRemediator` **Description:** Remediates Entra ID Guest Access Review policies. **Policy IDs:** MS.AAD.8.5v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.9.1v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.EntraIDTenantCreationRestrictionRemediator` **Description:** Remediates Entra ID Tenant Creation Restriction policies. **Policy IDs:** MS.AAD.9.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

### Exchange Online

This section describes the available security remediators for Exchange Online.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| MS.EXO.1.1v1 | Remediates Exchange Auto Forward policies. |  |
| MS.EXO.1.2v1 | Remediates Exchange Smtp Auth policies. |  |
| MS.EXO.12.1v1, MS.EXO.12.2v1 | Remediates Exchange Allow List policies. | String[], boolean |
| MS.EXO.13.1v1 | Remediates Exchange Mailbox Audit policies. |  |
| MS.EXO.14.1v2, MS.EXO.14.2v1, MS.EXO.14.3v1 | Remediates Exchange Anti Spam policies. |  |
| MS.EXO.15.1v1 | Remediates Exchange Audit Disabled policies. |  |
| MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1 | Remediates Exchange Safe Links policies. |  |
| MS.EXO.16.1v1 | Remediates Exchange Alerts Enabler policies. | String, List |
| MS.EXO.16.2v1 | Remediates Exchange Alert Notification Configurator policies. | String, String, List |
| MS.EXO.18.1v1 | Remediates Exchange Shared Mailbox Sign In policies. | ExchangeOnlineConfigurationService |
| MS.EXO.2.2v2 | Remediates Exchange Spf Remediation Service policies. | DnsService, ExchangeOnlineConfigurationService, TokenCredential, String, String |
| MS.EXO.6.1v1, MS.EXO.6.2v1 | Remediates Exchange Sharing Policy policies. |  |
| MS.EXO.7.1v1 | Remediates Exchange Sender Warning policies. |  |

#### Detailed Information

**MS.EXO.1.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAutoForwardRemediator` **Description:** Remediates Exchange Auto Forward policies. **Policy IDs:** MS.EXO.1.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.1.2v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSmtpAuthRemediator` **Description:** Remediates Exchange Smtp Auth policies. **Policy IDs:** MS.EXO.1.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.12.1v1, MS.EXO.12.2v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAllowListRemediator` **Description:** Remediates Exchange Allow List policies. **Policy IDs:** MS.EXO.12.1v1, MS.EXO.12.2v1



**Required Parameters**
* **ipBlockList** - Provide the ipblocklist
* **clearBlockList** - Provide the clearblocklist

**Parameters Summary**
This policy requires the user to provide ipBlockList and the user to provide clearBlockList.


---

**MS.EXO.13.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeMailboxAuditRemediator` **Description:** Remediates Exchange Mailbox Audit policies. **Policy IDs:** MS.EXO.13.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.14.1v2, MS.EXO.14.2v1, MS.EXO.14.3v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAntiSpamRemediatorBase` **Description:** Remediates Exchange Anti Spam policies. **Policy IDs:** MS.EXO.14.1v2, MS.EXO.14.2v1, MS.EXO.14.3v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.15.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAuditDisabledRemediator` **Description:** Remediates Exchange Audit Disabled policies. **Policy IDs:** MS.EXO.15.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSafeLinksRemediator` **Description:** Remediates Exchange Safe Links policies. **Policy IDs:** MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.16.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAlertsEnabler` **Description:** Remediates Exchange Alerts Enabler policies. **Policy IDs:** MS.EXO.16.1v1



**Required Parameters**
* **tenantId** - Select the tenantid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **additionalAlerts** - Provide the additionalalerts

**Parameters Summary**
This policy requires the user to select a tenantId (true or false) and the user to provide additionalAlerts.


---

**MS.EXO.16.2v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeAlertNotificationConfigurator` **Description:** Remediates Exchange Alert Notification Configurator policies. **Policy IDs:** MS.EXO.16.2v1



**Required Parameters**
* **notificationRecipient** - Select the notificationrecipient from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **tenantId** - Select the tenantid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **additionalAlerts** - Provide the additionalalerts

**Parameters Summary**
This policy requires the user to select a notificationRecipient (true or false) and the user to select a tenantId (true or false) and the user to provide additionalAlerts.


---

**MS.EXO.18.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSharedMailboxSignInRemediator` **Description:** Remediates Exchange Shared Mailbox Sign In policies. **Policy IDs:** MS.EXO.18.1v1



**Required Parameters**
* **exchangeService** - Provide the exchangeservice

**Parameters Summary**
This policy requires the user to provide exchangeService.


---

**MS.EXO.2.2v2** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSpfRemediationService` **Description:** Remediates Exchange Spf Remediation Service policies. **Policy IDs:** MS.EXO.2.2v2



**Required Parameters**
* **dnsService** - Provide the dnsservice
* **exchangeService** - Provide the exchangeservice
* **azureCredential** - Provide the azurecredential
* **azureSubscriptionId** - Select the azuresubscriptionid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **azureResourceGroup** - Select the azureresourcegroup from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection


**Parameters Summary**
This policy requires the user to provide dnsService and the user to provide exchangeService and the user to provide azureCredential and the user to select a azureSubscriptionId (true or false) and the user to select a azureResourceGroup (true or false).


---

**MS.EXO.6.1v1, MS.EXO.6.2v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSharingPolicyRemediator` **Description:** Remediates Exchange Sharing Policy policies. **Policy IDs:** MS.EXO.6.1v1, MS.EXO.6.2v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.7.1v1** **Class:** `io.syrix.products.microsoft.exo.remediation.ExchangeSenderWarningRemediator` **Description:** Remediates Exchange Sender Warning policies. **Policy IDs:** MS.EXO.7.1v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

### Microsoft 365

This section describes the available security remediators for Microsoft 365.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| DLP - MS.EXO.8.1v2 | Remediates Dlp policies. | SkuProcessingResult, DlpConfiguration |
| MS.AAD.7.5v1 | Remediates Direct Assignment policies. |  |
| MS.AAD.7.6v1 | Remediates Global Admin PIM policies. | String |
| MS.AAD.7.7v1 | Remediates Privileged Role Alert policies. |  |
| MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1 | Remediates Enhanced Anti Phishing Policy policies. | AntiPhishingConfiguration |
| MS.EXO.17.1v1, MS.EXO.17.2v1, MS.EXO.17.3v1 | Remediates Microsoft Log Audit Retention policies. |  |
| MS.EXO.9.1v2, MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.5v1 | Remediates Attachment Filter policies. | AttachmentFilterConfig |

#### Detailed Information

**DLP - MS.EXO.8.1v2** **Class:** `io.syrix.products.microsoft.DLP.DlpRemediator` **Description:** Remediates Dlp policies. **Policy IDs:** DLP - MS.EXO.8.1v2



**Required Parameters**
* **skus** - Select the skus from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **config** - Provide the config

**Parameters Summary**
This policy requires the user to select a skus (true or false) and the user to provide config.


---

**MS.AAD.7.5v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.DirectAssignmentRemediator` **Description:** Remediates Direct Assignment policies. **Policy IDs:** MS.AAD.7.5v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.AAD.7.6v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.GlobalAdminPIMRemediator` **Description:** Remediates Global Admin PIM policies. **Policy IDs:** MS.AAD.7.6v1



**Required Parameters**
* **globalAdminRoleId** - Select the globaladminroleid from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection


**Parameters Summary**
This policy requires the user to select a globalAdminRoleId (true or false).


---

**MS.AAD.7.7v1** **Class:** `io.syrix.products.microsoft.entra.service.remediation.PrivilegedRoleAlertRemediator` **Description:** Remediates Privileged Role Alert policies. **Policy IDs:** MS.AAD.7.7v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1** **Class:** `io.syrix.products.microsoft.exo.remediation.antiphishing.EnhancedAntiPhishingPolicyRemediator` **Description:** Remediates Enhanced Anti Phishing Policy policies. **Policy IDs:** MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1



**Required Parameters**
* **config** - Select the config from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection


**Parameters Summary**
This policy requires the user to select a config (true or false).


---

**MS.EXO.17.1v1, MS.EXO.17.2v1, MS.EXO.17.3v1** **Class:** `io.syrix.products.microsoft.exo.remediation.MicrosoftLogAuditRetentionRemediator` **Description:** Remediates Microsoft Log Audit Retention policies. **Policy IDs:** MS.EXO.17.1v1, MS.EXO.17.2v1, MS.EXO.17.3v1



**Required Parameters**
* None

**Parameters Summary**
This policy requires no parameters.


---

**MS.EXO.9.1v2, MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.5v1** **Class:** `io.syrix.products.microsoft.exo.remediation.malwareFilter.AttachmentFilterRemediator` **Description:** Remediates Attachment Filter policies. **Policy IDs:** MS.EXO.9.1v2, MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.5v1



**Required Parameters**
* **config** - Select the config from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection


**Parameters Summary**
This policy requires the user to select a config (true or false).


---

### SharePoint

This section describes the available security remediators for SharePoint.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| MS.SHAREPOINT.1.1v1 | Remediates SP Sharing Capability policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.1.2v1 | Remediates SP One Drive Sharing Capability policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.1.3v1 | Remediates SP Allowed Domain List policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.1.4v1 | Remediates SP Account Match Invited Account policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.2.1v1 | Remediates SP Default Sharing Link policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.2.2v1 | Remediates SP Default Link Permission policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.3.1v1 | Remediates SP Links Expire policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.3.2v1 | Remediates SP File And Folder Anonymous Link policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.3.3v1 | Remediates SP Email Attestation Required And Re Auth policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |
| MS.SHAREPOINT.4.2v1 | Remediates SP Custom Script policies. | PowerShellSharepointClient, SharePointTenantProperties, SharepointRemediationConfig |

#### Detailed Information

**MS.SHAREPOINT.1.1v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPSharingCapabilityRemediator` **Description:** Remediates SP Sharing Capability policies. **Policy IDs:** MS.SHAREPOINT.1.1v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.1.2v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPOneDriveSharingCapabilityRemediator` **Description:** Remediates SP One Drive Sharing Capability policies. **Policy IDs:** MS.SHAREPOINT.1.2v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.1.3v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPAllowedDomainListRemediator` **Description:** Remediates SP Allowed Domain List policies. **Policy IDs:** MS.SHAREPOINT.1.3v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.1.4v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPAccountMatchInvitedAccountRemediator` **Description:** Remediates SP Account Match Invited Account policies. **Policy IDs:** MS.SHAREPOINT.1.4v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.2.1v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPDefaultSharingLinkRemediator` **Description:** Remediates SP Default Sharing Link policies. **Policy IDs:** MS.SHAREPOINT.2.1v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.2.2v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPDefaultLinkPermissionRemediator` **Description:** Remediates SP Default Link Permission policies. **Policy IDs:** MS.SHAREPOINT.2.2v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.3.1v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPLinksExpireRemediator` **Description:** Remediates SP Links Expire policies. **Policy IDs:** MS.SHAREPOINT.3.1v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.3.2v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPFileAndFolderAnonymousLinkRemediator` **Description:** Remediates SP File And Folder Anonymous Link policies. **Policy IDs:** MS.SHAREPOINT.3.2v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.3.3v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPEmailAttestationRequiredAndReAuthRemediator` **Description:** Remediates SP Email Attestation Required And Re Auth policies. **Policy IDs:** MS.SHAREPOINT.3.3v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

**MS.SHAREPOINT.4.2v1** **Class:** `io.syrix.products.microsoft.sharepoint.remediation.SPCustomScriptRemediator` **Description:** Remediates SP Custom Script policies. **Policy IDs:** MS.SHAREPOINT.4.2v1



**Required Parameters**
* **client** - Select the client from these options:
  * COMMERCIAL (COMMERCIAL): Applies commercial level of security protection
  * GCC (GCC_HIGH): Applies gcc_high level of security protection
  * GCC (GCC): Applies gcc level of security protection
  * GCC_HIGH (GCC_HIGH): Applies gcc_high level of security protection
  * DOD (DOD): Applies dod level of security protection

* **tenant** - Select the tenant from these options:
  * true (true): Applies true level of security protection
  * false (false): Applies false level of security protection

* **spConfig** - Select the spconfig from these options:
  * DISABLE (DISABLE): Applies disable level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection
  * EXTERNAL_USER_SHARING_ONLY (EXTERNAL_USER_SHARING_ONLY): Applies external_user_sharing_only level of security protection
  * EXTERNAL_USER_AND_GUEST_SHARING (EXTERNAL_USER_AND_GUEST_SHARING): Applies external_user_and_guest_sharing level of security protection
  * EXISTING_EXTERNAL_USER_SHARING_ONLY (EXISTING_EXTERNAL_USER_SHARING_ONLY): Applies existing_external_user_sharing_only level of security protection


**Parameters Summary**
This policy requires the user to select a client (COMMERCIAL or GCC or GCC or GCC_HIGH or DOD) and the user to select a tenant (true or false) and the user to select a spConfig (DISABLE or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_SHARING_ONLY or EXTERNAL_USER_AND_GUEST_SHARING or EXISTING_EXTERNAL_USER_SHARING_ONLY).


---

### Teams

This section describes the available security remediators for Teams.

#### Overview

| Policy ID | Description | Parameters |
|-----------|-------------|------------|
| MS.TEAMS.1.1v1 | Remediates Teams Meetings Allowing External Control policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.2v1 | Remediates Teams Anonymous Users To Start Meeting policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.3v1 | Remediates Teams Auto Admitted Users And PSTN User policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.4v1 | Remediates Teams Auto Admitted Users policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.5v1 | Remediates Teams Meetings Allowing PSTN Bypass policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.6v1 | Remediates Teams Allow Cloud Recording policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.1.7v1 | Remediates Teams Broadcast Recording Mode policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.2.1v1 | Remediates Teams Allow Federated Users policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.2.2v1 | Remediates Teams Allow Teams Consumer Inbound policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.2.3v1 | Remediates Teams Allow Teams Consumer policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.3.1v1 | Remediates Teams Allow Public Users policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.4.1v1 | Remediates Teams Allow Email Into Channel policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.5.1v1 | Remediates Teams Default Catalog Apps Type policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.5.2v1 | Remediates Teams Global Catalog Apps Type policies. | ObjectNode, TeamsRemediationConfig |
| MS.TEAMS.5.3v1 | Remediates Teams Private Catalog Apps Type policies. | ObjectNode, TeamsRemediationConfig |

#### Detailed Information

**MS.TEAMS.1.1v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsMeetingsAllowingExternalControlRemediator` **Description:** Remediates Teams Meetings Allowing External Control policies. **Policy IDs:** MS.TEAMS.1.1v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.2v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAnonymousUsersToStartMeetingRemediator` **Description:** Remediates Teams Anonymous Users To Start Meeting policies. **Policy IDs:** MS.TEAMS.1.2v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.3v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAutoAdmittedUsersAndPSTNUserRemediator` **Description:** Remediates Teams Auto Admitted Users And PSTN User policies. **Policy IDs:** MS.TEAMS.1.3v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.4v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAutoAdmittedUsersRemediator` **Description:** Remediates Teams Auto Admitted Users policies. **Policy IDs:** MS.TEAMS.1.4v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.5v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsMeetingsAllowingPSTNBypassRemediator` **Description:** Remediates Teams Meetings Allowing PSTN Bypass policies. **Policy IDs:** MS.TEAMS.1.5v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.6v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowCloudRecordingRemediator` **Description:** Remediates Teams Allow Cloud Recording policies. **Policy IDs:** MS.TEAMS.1.6v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.1.7v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsBroadcastRecordingModeRemediator` **Description:** Remediates Teams Broadcast Recording Mode policies. **Policy IDs:** MS.TEAMS.1.7v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.2.1v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowFederatedUsersRemediator` **Description:** Remediates Teams Allow Federated Users policies. **Policy IDs:** MS.TEAMS.2.1v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.2.2v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowTeamsConsumerInboundRemediator` **Description:** Remediates Teams Allow Teams Consumer Inbound policies. **Policy IDs:** MS.TEAMS.2.2v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.2.3v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowTeamsConsumerRemediator` **Description:** Remediates Teams Allow Teams Consumer policies. **Policy IDs:** MS.TEAMS.2.3v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.3.1v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowPublicUsersRemediator` **Description:** Remediates Teams Allow Public Users policies. **Policy IDs:** MS.TEAMS.3.1v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.4.1v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsAllowEmailIntoChannelRemediator` **Description:** Remediates Teams Allow Email Into Channel policies. **Policy IDs:** MS.TEAMS.4.1v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.5.1v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsDefaultCatalogAppsTypeRemediator` **Description:** Remediates Teams Default Catalog Apps Type policies. **Policy IDs:** MS.TEAMS.5.1v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.5.2v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsGlobalCatalogAppsTypeRemediator` **Description:** Remediates Teams Global Catalog Apps Type policies. **Policy IDs:** MS.TEAMS.5.2v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

**MS.TEAMS.5.3v1** **Class:** `io.syrix.products.microsoft.teams.remediation.TeamsPrivateCatalogAppsTypeRemediator` **Description:** Remediates Teams Private Catalog Apps Type policies. **Policy IDs:** MS.TEAMS.5.3v1



**Required Parameters**
* **configNode** - Provide the confignode
* **remediationConfig** - Select the remediationconfig from these options:
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection
  * EVERYONE_IN_COMPANY (EVERYONE_IN_COMPANY): Applies everyone_in_company level of security protection
  * EVERYONE_IN_SAME_AND_FEDERATED_COMPANY (EVERYONE_IN_SAME_AND_FEDERATED_COMPANY): Applies everyone_in_same_and_federated_company level of security protection
  * EVERYONE_IN_COMPANY_EXCLUDING_GUESTS (EVERYONE_IN_COMPANY_EXCLUDING_GUESTS): Applies everyone_in_company_excluding_guests level of security protection


**Parameters Summary**
This policy requires the user to provide configNode and the user to select a remediationConfig (EVERYONE_IN_COMPANY or EVERYONE_IN_COMPANY or EVERYONE_IN_SAME_AND_FEDERATED_COMPANY or EVERYONE_IN_COMPANY_EXCLUDING_GUESTS).


---

