# Implementation Guide: Malware Protection Using Preset Security Policies

This document provides guidance on implementing malware protection policies (MS.EXO.10.1v1 and MS.EXO.10.2v1) using Microsoft 365 Defender's preset security policies. This approach offers a more standardized and comprehensive security posture compared to individually configuring separate policies.

## Prerequisites

Before using the `MalwarePolicyRemediator` class, ensure the following prerequisites are met:

1. **Microsoft 365 Defender Portal Access**: Administrator access to the Microsoft 365 Defender portal.
2. **Exchange Online PowerShell**: Exchange Online PowerShell V3 module installed and proper permissions.
3. **Microsoft 365 Groups**: Mail-enabled security groups or Microsoft 365 groups created for policy assignments.
4. **Preset Policies Enabled**: Standard and Strict protection policies must be manually enabled in the Microsoft 365 Defender portal.

## Manual One-Time Setup

Preset security policies must first be enabled in the Microsoft 365 Defender portal before they can be managed programmatically:

1. Navigate to **Microsoft 365 Defender Portal** → **Email & Collaboration → Policies & Rules → Threat Policies → Preset Security Policies**.
2. Turn on both **Standard Protection** and **Strict Protection** policies.
3. You can initially leave the group assignments empty, as they will be managed by the remediator.

## How This Implementation Works

The refactored implementation uses Microsoft's preset security policies, which include pre-configured settings for various security controls including malware protection:

1. **Standard Protection Policy**: Applied to general users, provides baseline malware scanning and action settings.
2. **Strict Protection Policy**: Applied to high-risk users, provides enhanced malware scanning and action settings.

### Advantages of This Approach

1. **Comprehensive Coverage**: Preset policies configure multiple security settings at once.
2. **Microsoft Best Practices**: Policies are configured according to Microsoft's recommended security baselines.
3. **Simplified Management**: Fewer policies to manage compared to individual configurations.
4. **Automatic Updates**: Microsoft updates the preset policies as new threats emerge.

## Implementation Details

### Class Structure

1. **PresetSecurityPolicyRemediator (Base Class)**
    - Abstract base class providing common functionality for working with preset security policies
    - Handles policy status checks, configuration retrieval, and group assignments

2. **MalwarePolicyRemediator**
    - Implements both MS.EXO.10.1v1 (malware scanning) and MS.EXO.10.2v1 (malware actions)
    - Configures group assignments for Standard and Strict preset security policies
    - Supports two modes:
        - Standard/Strict split (default): Regular users get Standard, high-risk users get Strict
        - Strict-only: All users receive the Strict policy (higher security)

## Configuration Options

When instantiating `MalwarePolicyRemediator`, you can customize:

1. **Group Assignments**: Specify which groups should receive Standard and Strict policies
2. **Policy Mode**: Choose between Standard/Strict split or Strict-only for all users

Example:
```java
// Standard/Strict split with custom groups
MalwarePolicyRemediator remediator = new MalwarePolicyRemediator(
    exchangeOnlineClient,
    "CompanyWideGroup",    // Group to receive Standard policy
    "ExecutiveTeam",       // Group to receive Strict policy
    false                  // Use Standard/Strict split mode
);

// Strict-only mode for all users
MalwarePolicyRemediator strictOnlyRemediator = new MalwarePolicyRemediator(
    exchangeOnlineClient,
    "AllCompanyUsers",     // All users will receive Strict policy
    "NotUsedInThisMode",   // This group isn't used in Strict-only mode
    true                   // Use Strict-only mode
);
```

## Limitations and Considerations

1. **Manual Activation Requirement**: Preset security policies must be enabled manually in the Microsoft 365 Defender portal before they can be configured programmatically.

2. **Group Verification**: The remediator doesn't verify if the specified groups exist. Ensure groups are created before running the remediator.

3. **License Requirements**: Defender for Office 365 Plan 1 or Plan 2 license is required for preset security policies.

4. **Policy Application Delay**: After assignment, it may take 1-2 hours for policies to fully apply to all users.

## Troubleshooting

If you encounter issues:

1. **Verify Policy Status**: Check if policies are enabled in the Defender portal.
   ```powershell
   Get-PresetSecurityPolicy | Format-Table Type, GroupName, Enabled
   ```

2. **Verify Group Memberships**: Ensure users are in the correct groups.
   ```powershell
   Get-DistributionGroupMember -Identity "GroupName"
   ```

3. **Check for Errors**: Review the logs generated by the remediator for detailed error messages.

4. **Conflicting Policies**: If you have other malware policies, they may conflict with preset policies. Check priority settings.

## Advanced: Custom Malware Settings

If you need custom malware settings beyond what preset policies offer, consider implementing the original approach with individual policies. However, note that preset policies provide a more comprehensive and maintainable security posture for most organizations.