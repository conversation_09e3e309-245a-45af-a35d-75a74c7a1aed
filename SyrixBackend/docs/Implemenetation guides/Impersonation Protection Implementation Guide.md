# Impersonation Protection Implementation Guide

This guide outlines how to implement and configure anti-phishing and impersonation protection policies in Exchange Online and Microsoft 365 Defender using the provided Java classes. These implementations address the following policy requirements:

- **MS.EXO.11.1v1**: Configure Impersonation Protection
- **MS.EXO.11.2v1**: Enable User Safety Tips
- **MS.EXO.11.3v1**: Implement AI-based Detection (Mailbox Intelligence)

## Implementation Overview

The implementation consists of the following key components:

1. **AntiPhishingConfiguration**: A configuration class with a builder pattern for flexible policy settings
2. **AntiPhishingPolicyRemediator**: A basic implementation with fixed settings
3. **EnhancedAntiPhishingPolicyRemediator**: An enhanced implementation that uses the configuration class

These classes create and manage anti-phishing policies and rules in Exchange Online to detect and block impersonation attempts, display safety tips to users, and leverage AI-based detection mechanisms.

## Prerequisites

Before using these classes, ensure:

1. **Exchange Online PowerShell**: The PowerShell client must be connected to Exchange Online with appropriate permissions
2. **Microsoft 365 Defender**: Your tenant must have Microsoft 365 Defender (formerly Office 365 ATP) for full functionality
3. **Protected Entities**: Identify your organization's domains and high-value users that need protection

## Configuration Options

### Basic Configuration

For basic implementation with default settings, use the `AntiPhishingPolicyRemediator`:

```java
// Create remediator with default settings
PowerShellClient exchangeOnlineClient = /* your PowerShell client */;
AntiPhishingPolicyRemediator remediator = new AntiPhishingPolicyRemediator(exchangeOnlineClient);

// Execute remediation
remediator.remediate();
```

### Enhanced Configuration

For more control over policy settings, use the `EnhancedAntiPhishingPolicyRemediator` with `AntiPhishingConfiguration`:

```java
// Create configuration
AntiPhishingConfiguration config = new AntiPhishingConfiguration.Builder()
    .withProtectedDomains("contoso.com")
    .withProtectedUsers("CEO;<EMAIL>", "CFO;<EMAIL>")
    .withPhishThresholdLevel(2) // Aggressive
    .build();

// Create remediator with custom configuration
EnhancedAntiPhishingPolicyRemediator remediator = new EnhancedAntiPhishingPolicyRemediator(
    exchangeOnlineClient, config);

// Execute remediation
remediator.remediate();
```

### Preset Configurations

The `AntiPhishingConfiguration` class provides preset configurations for common scenarios:

```java
// Standard protection
AntiPhishingConfiguration standardConfig = 
    AntiPhishingConfiguration.createStandardConfig("contoso.com");

// Enhanced protection for executives
AntiPhishingConfiguration enhancedConfig = 
    AntiPhishingConfiguration.createEnhancedConfig(
        "contoso.com", 
        "CEO;<EMAIL>", 
        "CFO;<EMAIL>");

// Strict protection for high-security environments
AntiPhishingConfiguration strictConfig = 
    AntiPhishingConfiguration.createStrictConfig(
        "contoso.com", 
        "CEO;<EMAIL>", 
        "CFO;<EMAIL>");
```

## Policy Settings Details

### Impersonation Protection (MS.EXO.11.1v1)

These settings protect against domain and user impersonation:

- **Spoof Intelligence**: Detects and blocks spoofed sender domains
- **Domain Protection**: Protects specified domains from impersonation
- **User Protection**: Protects specified users from impersonation
- **Organization Domain Protection**: Protects all registered domains in your organization

### User Safety Tips (MS.EXO.11.2v1)

These settings enable warning indicators to users:

- **First Contact Safety Tips**: Warnings when receiving mail from a new sender
- **Similar Users/Domains Safety Tips**: Warns of potential impersonation attempts
- **Unusual Characters Safety Tips**: Alerts for homograph attacks
- **Via Tag**: Displays "via" tag in suspicious From addresses
- **Unauthenticated Sender**: Shows a question mark (?) icon for unauthenticated senders

### AI-based Detection (MS.EXO.11.3v1)

These settings leverage machine learning for detection:

- **Mailbox Intelligence**: Uses AI to analyze user email patterns with contacts
- **Mailbox Intelligence Protection**: Takes action on suspicious patterns
- **Phish Threshold Level**: Adjusts sensitivity of phishing detection (1-4)

## Customizing Protection by User Groups

For organizations with varying security needs across departments:

1. Create multiple remediators with different configurations
2. Specify different recipient domains or create different rules for each group

```java
// Create configuration for executives with strict settings
AntiPhishingConfiguration execConfig = new AntiPhishingConfiguration.Builder()
    .withProtectedUsers(/* executive users */)
    .withPhishThresholdLevel(4) // Most aggressive
    .withRecipientDomain("executives.contoso.com")
    .build();

// Create configuration for general staff with standard settings
AntiPhishingConfiguration staffConfig = new AntiPhishingConfiguration.Builder()
    .withProtectedDomains("contoso.com")
    .withPhishThresholdLevel(2) // Aggressive
    .withRecipientDomain("contoso.com")
    .build();
```

## Monitoring and Validation

After implementing these policies, monitor effectiveness through:

1. **Exchange Admin Center**: Check anti-phishing policy status and effectiveness
2. **Security & Compliance Center**: Review detected impersonation attempts
3. **Microsoft Graph API**: Programmatically retrieve phishing incidents and remediation actions

## Best Practices

1. **Start with Standard Configuration**: Begin with standard protection and increase as needed
2. **Prioritize High-Value Targets**: Focus on protecting executives and high-value domains first
3. **Test Before Deployment**: Test policies on a subset of users before full deployment
4. **Regular Updates**: Review and update protected users/domains as organization changes
5. **Threat Intelligence**: Incorporate threat intelligence feeds for enhanced protection

## Troubleshooting

Common issues and solutions:

1. **False Positives**: If legitimate emails are blocked, adjust the phish threshold level or add exceptions
2. **Missing Safety Tips**: Ensure safety tip settings are correctly enabled
3. **Policy Priority Conflicts**: Check if other anti-phishing policies have higher priority
4. **Protection Not Applied**: Verify rule recipient configuration and policy assignment