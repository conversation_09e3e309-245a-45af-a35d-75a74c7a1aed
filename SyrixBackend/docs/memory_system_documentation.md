# Syrix Documentation Generator - System Overview

## Project Structure

The Syrix Java backend is a security application focusing on Microsoft 365 security configuration retrieval and analysis with these features:
- Security configuration retrieval from Microsoft services (Entra, Defender, Exchange Online, Teams, SharePoint)
- Tenant security assessment and auditing
- Security policy verification and compliance checking
- Security improvement recommendations

### Key Components

1. **Documentation Generator** (`PolicyRemediatorDocumentationGenerator`)
   - Generates documentation for security policy remediators
   - Creates consolidated documentation in JSON or Markdown format
   - Provides LLM prompt templates for parameter extraction

2. **Enum Type Extractor** (`EnumTypeExtractor`)
   - Handles extraction of enum values from Java classes
   - Provides human-readable descriptions for enum constants
   - Includes caching for performance optimization
   - Has special case handling for known enum types

3. **Enhanced Policy Remediator Info** (`EnhancedPolicyRemediatorInfo`)
   - Stores detailed information about policy remediators
   - Contains nested classes for parameter and field information
   - Includes utility methods for extracting service names and descriptions

4. **Policy Remediator Registry**
   - Maintains registry of all remediator classes
   - Provides methods to retrieve enhanced policy information

## Documentation Format Examples

The documentation is generated in a specific format, as shown in these examples:

```
**POLICY.ID.1** **Class:** `io.syrix.package.ClassName` **Description:** Description text. **Policy IDs:** POLICY.ID.1 **Parameters**
* **paramName (ParamType) (Required)**
  * **Possible Values:** VALUE1 (Display1), VALUE2 (Display2)
  * Fields: **Field Type Description** fieldName Type Description *Possible values:* values

**Parameters Summary**
This policy requires the user to provide parameters.

**Required Parameters**
* **paramName** - Description of what to provide
  * List of options if applicable
```

Example with MS.DEFENDER.1.1v1 (used only as an illustration):

```
**MS.DEFENDER.1.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator` **Description:** Remediates Defender Preset Security Policies policies. **Policy IDs:** MS.DEFENDER.1.1v1 **Parameters**
* **policyType (PolicyType) (Required)**
  * **Possible Values:** STANDARD (Standard), STRICT (Strict)
  * Fields: **Field Type Description** policyType String References the policy or configuration to use.

**Parameters Summary**
This policy requires the user to select a policyType (STANDARD or STRICT).

**Required Parameters**
* **policyType** - Select the policyType from these options:
  * STANDARD (Standard): Applies standard level of security protection
  * STRICT (Strict): Applies strict level of security protection
```

## Recent Enhancements

1. **Improved Enum Parameter Handling**
   - Added special case handling for enum parameters like `PolicyType`
   - Created an enhanced system to extract and display enum values
   - Implemented both technical names and human-readable values (e.g., "STANDARD (Standard)")

2. **Enhanced Documentation Format**
   - Removed unnecessary fields like "Purpose" and "Possible Results"
   - Added proper spacing for better readability
   - Fixed display of parameter information
   - Implemented parameter name inclusion with types

3. **Parameter Display Improvements**
   - Added parameter names with types for clarity (e.g., "excludedUsers (List<String>)")
   - Used parameter names in descriptions for more meaningful responses
   - Added "None" for policies without parameters

4. **Special Case Handling**
   - Created mechanism to detect and process special cases
   - Implemented dynamic example generation for all policies
   - Added handling for duplicate field prevention

## Code Structure

### EnumTypeExtractor
- `extractEnumValues(String enumClassName)`: Extracts enum values from a class
- `getEnumValuesByShortName(String enumShortName, String contextClassName)`: Finds enum classes by short name
- `getDefenderPolicyTypeValues()`: Special helper for PolicyType enum

### PolicyRemediatorDocumentationGenerator
- `generateAllDocumentation(String outputDir, String format, List<String> excludedParameterClasses)`: Main entry point
- `processSpecialCaseRemediators(EnhancedPolicyRemediatorInfo info)`: Handles special case remediators
- `generateConsolidatedMarkdownDocumentation(Map<String, List<EnhancedPolicyRemediatorInfo>> serviceGroups)`: Creates MD docs
- `generateLlmPromptTemplate(String outputDir)`: Creates LLM prompt templates

### EnhancedPolicyRemediatorInfo
- Contains nested classes `ParameterInfo` and `FieldInfo`
- Includes getters and setters for all properties
- Has utility methods for extracting metadata from classes

## Technical Integration

The documentation generator is designed to work with:

1. **LLM Integrations**
   - Creates optimized prompts for LLM-based parameter extraction
   - Generates UX input specifications from parameter information

2. **Microsoft Graph API**
   - Documentation references APIs used for security configuration

3. **Java Reflection System**
   - Uses reflection to extract enum values and class metadata

## Documentation Files

1. `all_policy_remediators.md` - Comprehensive documentation of all policies
2. `llm_prompt_templates.md` - Templates for LLM interaction
3. `example_formatted_output_updated.md` - Examples of properly formatted documentation
4. `memory_system_documentation.md` - This file, for system memory

## Example Case: MS.DEFENDER.1.1v1

MS.DEFENDER.1.1v1 is example (don't need to be handled specially)
- Class: `io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator`
- Parameter: `policyType (PolicyType)`
- Possible Values: STANDARD (Standard), STRICT (Strict)

This policy is an important example of enum parameter handling in the system.

## Future Enhancements

1. Further improvement of enum value extraction
2. Addition of more special case handlers as needed
3. Integration with new Microsoft 365 security features
4. Enhanced LLM prompts for better parameter description generation
