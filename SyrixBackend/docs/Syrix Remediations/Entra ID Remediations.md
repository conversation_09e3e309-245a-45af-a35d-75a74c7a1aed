## Azure Entra ID Security Baseline Controls and Remediation Suggestions (Combined)

Below are the extracted controls (MS.AAD) from the CISA M365 Security Configuration Baseline for Microsoft Entra ID document, along with detailed explanations, remediation steps, and PowerShell scripts with Microsoft Graph API URLs where applicable.

**1. Legacy Authentication**

#### MS.AAD.1.1v1 - Block Legacy Authentication
**Control:** Legacy authentication SHALL be blocked.
**Rationale:** The security risk of allowing legacy authentication protocols is they do not support MFA. Blocking legacy protocols reduces the impact of user credential theft.
**Remediation:** Implement a Conditional Access policy to block legacy authentication protocols.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
# Install Microsoft Graph Beta Module if not already installed
# Install-Module Microsoft.Graph.Beta

Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

# Define the Conditional Access policy
$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Block Legacy Authentication"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

    # Client Applications (Legacy Authentication)
    $clientAppTypes = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessClientAppTypes
    $clientAppTypes.ExcludeClientAppTypes = @("browser", "mobileAppsAndDesktops") #Exclude modern auth clients, include all legacy
    $conditions.ClientAppTypes = $clientAppTypes

# Grant Controls - Block Access
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.GrantType = "Block" # Block access
$conditionalAccessPolicy.GrantControls = $grantControls

# Create the Conditional Access policy
# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Block Legacy Authentication' created successfully."
```

**2. Risk Based Policies**

#### MS.AAD.2.1v1 - Block High Risk Users
**Control:** Users detected as high risk SHALL be blocked.
**Rationale:** Blocking high-risk users may prevent compromised accounts from accessing the tenant.
**Remediation:** Implement a Conditional Access policy to block users identified as high risk by Microsoft Entra ID Protection.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Block High Risk Users"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

    # User Risk Level
    $userRisk = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUserRisk
    $userRisk.Levels = @("high")
    $conditions.UserRiskLevels = $userRisk

# Grant Controls - Block Access
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.GrantType = "Block"
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Block High Risk Users' created successfully."
```

#### MS.AAD.2.2v1 - Notify Admin for High Risk Users
**Control:** A notification SHOULD be sent to the administrator when high-risk users are detected.
**Rationale:** Notification enables the admin to monitor the event and remediate the risk. This helps the organization proactively respond to cyber intrusions as they occur.
**Remediation:** Configure Microsoft Entra ID Protection to send email notifications to administrators when user accounts are detected as high risk.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "IdentityRiskyUser.ReadWrite.All"

# Get current Identity Protection settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/identityProtection
$identityProtectionSettings = Get-MgBetaIdentityProtection

# Update UserRiskDetection Configuration to send email notifications
$userRiskDetection = $identityProtectionSettings.UserRiskDetection
if (-not $userRiskDetection) {
    $userRiskDetection = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphUserRiskDetection
}
$userRiskDetection.NotifyAdmin = "Enabled"
$userRiskDetection.AdminEmails = @("<EMAIL>") # Replace with actual admin email

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/identityProtection
Update-MgBetaIdentityProtection -BodyParameter $identityProtectionSettings

Write-Host "High-risk user detection notifications configured."
```

**Note:** Replace `<EMAIL>` with the actual email address for security administrators.

#### MS.AAD.2.3v1 - Block High Risk Sign-ins
**Control:** Sign-ins detected as high risk SHALL be blocked.
**Rationale:** This prevents compromised accounts from accessing the tenant based on sign-in risk.
**Remediation:** Implement a Conditional Access policy to block sign-ins detected as high risk by Microsoft Entra ID Protection.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Block High Risk Sign-ins"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

    # Sign-in Risk Level
    $signInRisk = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccess риска
    $signInRisk.Levels = @("high")
    $conditions.SignInRiskLevels = $signInRisk

# Grant Controls - Block Access
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.GrantType = "Block"
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Block High Risk Sign-ins' created successfully."
```

**3. Strong Authentication and a Secure Registration Process**

#### MS.AAD.3.1v1 - Enforce Phishing-Resistant MFA for All Users
**Control:** Phishing-resistant MFA SHALL be enforced for all users.
**Rationale:** Weaker forms of MFA do not protect against sophisticated phishing attacks. By enforcing methods resistant to phishing, those risks are minimized.
**Remediation:** Implement a Conditional Access policy requiring phishing-resistant MFA methods for all users. Recommended methods include Microsoft Entra ID certificate-based authentication (CBA), FIDO2 Security Keys, and Windows Hello for Business.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Require Phishing-Resistant MFA for All Users"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

# Grant Controls - Require Phishing-Resistant MFA
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.BuiltInControls = @("phishingResistantMfa") # Require phishing-resistant MFA
$grantControls.Operator = "OR" #Ensure 'Require one of the selected controls' is set if needed in UI
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Require Phishing-Resistant MFA for All Users' created."
```

#### MS.AAD.3.2v1 - Enforce Alternative MFA for All Users (If Phishing-Resistant MFA Not Enforced)
**Control:** If phishing-resistant MFA has not been enforced, an alternative MFA method SHALL be enforced for all users.
**Rationale:** This is a stopgap security policy to help protect the tenant if phishing-resistant MFA has not been enforced. This policy requires MFA enforcement, thus reducing single-form authentication risk.
**Remediation:** If phishing-resistant MFA is not yet fully implemented, enforce MFA using a Conditional Access policy, without specifying a particular MFA method, as an interim security measure.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Require MFA for All Users (Alternative Method)"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

# Grant Controls - Require MFA
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.BuiltInControls = @("mfa") # Require MFA
$grantControls.Operator = "OR" #Ensure 'Require one of the selected controls' is set if needed in UI
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Require MFA for All Users (Alternative Method)' created."
```

#### MS.AAD.3.3v1 - Enable Login Context in Microsoft Authenticator
**Control:** If phishing-resistant MFA has not been enforced and Microsoft Authenticator is enabled, it SHALL be configured to show login context information.
**Rationale:** This stopgap security policy helps protect the tenant when phishing-resistant MFA has not been enforced and Microsoft Authenticator is used. This policy helps improve the security of Microsoft Authenticator by showing user context information, which helps reduce MFA phishing compromises.
**Remediation:** Configure Microsoft Authenticator to display application name and geographic location in push and passwordless notifications.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.AuthenticationMethods"

# Get current Authentication Methods Policy
# MS Graph API URL: GET https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
$authMethodsPolicy = Get-MgBetaPolicyAuthenticationMethodsPolicy

# Find the Microsoft Authenticator Authentication Method Configuration
$authenticatorAuthConfig = $authMethodsPolicy.AuthenticationMethodConfigurations | Where-Object {$_.AuthenticationMethodType -eq "microsoftAuthenticator"}

if ($authenticatorAuthConfig) {
    # Enable Application Name and Geographic Location Context
    $authenticatorAuthConfig.Settings.IsDisplayNameShownInPushNotificationsEnabled = $true
    $authenticatorAuthConfig.Settings.IsLocationShownInPushNotificationsEnabled = $true
    $authenticatorAuthConfig.IncludeTargets = @(
        @{
            Id = "all_users"
            TargetType = "group" # Using 'group' type to target all users, adjust if needed
            DisplayName = "All users" #Optional Display Name
        }
    )
    $authenticatorAuthConfig.State = "enabled" #Ensure it's enabled

    # Update the Authentication Methods Policy
    # MS Graph API URL: PATCH https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
    Update-MgBetaPolicyAuthenticationMethodsPolicy -BodyParameter $authMethodsPolicy
    Write-Host "Microsoft Authenticator context information enabled successfully."
} else {
    Write-Warning "Microsoft Authenticator Authentication Method Configuration not found."
}
```

#### MS.AAD.3.4v1 - Set Authentication Methods Manage Migration to Migration Complete
**Control:** The Authentication Methods Manage Migration feature SHALL be set to Migration Complete.
**Rationale:** To disable the legacy authentication methods screen for the tenant, configure the Manage Migration feature to Migration Complete. The MFA and Self-Service Password Reset (SSPR) authentication methods are both managed from a central admin page, thereby reducing administrative complexity and potential security misconfigurations.
**Remediation:** Set the "Manage Migration" option to "Migration Complete" in the Authentication methods policy to consolidate MFA and SSPR policy management.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.AuthenticationMethods"

# Get current Authentication Methods Policy
# MS Graph API URL: GET https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
$authMethodsPolicy = Get-MgBetaPolicyAuthenticationMethodsPolicy

# Set Manage Migration to Migration Complete
$authMethodsPolicy.PolicyMigrationStage = "migrationComplete"

# Update the Authentication Methods Policy
# MS Graph API URL: PATCH https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
Update-MgBetaPolicyAuthenticationMethodsPolicy -BodyParameter $authMethodsPolicy

Write-Host "Authentication Methods Manage Migration set to Migration Complete."
```

#### MS.AAD.3.5v1 - Disable Weak Authentication Methods (SMS, Voice, Email OTP)
**Control:** The authentication methods SMS, Voice Call, and Email One-Time Passcode (OTP) SHALL be disabled.
**Rationale:** SMS, voice call, and email OTP are the weakest authenticators. This policy forces users to use stronger MFA methods.
**Remediation:** Disable SMS, Voice Call, and Email OTP authentication methods within the Authentication methods policy to encourage the use of stronger authentication methods.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.AuthenticationMethods"

# Get current Authentication Methods Policy
# MS Graph API URL: GET https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
$authMethodsPolicy = Get-MgBetaPolicyAuthenticationMethodsPolicy

# Disable SMS Sign-in
$smsAuthConfig = $authMethodsPolicy.AuthenticationMethodConfigurations | Where-Object {$_.AuthenticationMethodType -eq "sms"}
if ($smsAuthConfig) {
    $smsAuthConfig.State = "disabled"
}

# Disable Voice Call Sign-in
$voiceAuthConfig = $authMethodsPolicy.AuthenticationMethodConfigurations | Where-Object {$_.AuthenticationMethodType -eq "voice"}
if ($voiceAuthConfig) {
    $voiceAuthConfig.State = "disabled"
}

# Disable Email OTP Sign-in
$emailOtpAuthConfig = $authMethodsPolicy.AuthenticationMethodConfigurations | Where-Object {$_.AuthenticationMethodType -eq "emailOTP"}
if ($emailOtpAuthConfig) {
    $emailOtpAuthConfig.State = "disabled"
}

# Update the Authentication Methods Policy
# MS Graph API URL: PATCH https://graph.microsoft.com/beta/policies/authenticationMethodsPolicy
Update-MgBetaPolicyAuthenticationMethodsPolicy -BodyParameter $authMethodsPolicy

Write-Host "SMS, Voice Call, and Email OTP authentication methods disabled."
```

#### MS.AAD.3.6v1 - Enforce Phishing-Resistant MFA for Highly Privileged Roles
**Control:** Phishing-resistant MFA SHALL be required for highly privileged roles.
**Rationale:** This is a backup security policy to help protect privileged access to the tenant if the conditional access policy, which requires MFA for all users, is disabled or misconfigured.
**Remediation:** Implement a Conditional Access policy specifically requiring phishing-resistant MFA for users in highly privileged roles as a critical security measure.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess", "Directory.Read.All"

# Array of Highly Privileged Roles (as defined in the document)
$privilegedRoles = @(
    "Global Administrator",
    "Privileged Role Administrator",
    "User Administrator",
    "SharePoint Administrator",
    "Exchange Administrator",
    "Hybrid Identity Administrator",
    "Application Administrator",
    "Cloud Application Administrator"
)

# Get Role IDs for the role names
# MS Graph API URL: GET https://graph.microsoft.com/beta/directoryRoles?$filter=startswith(displayName, '{roleName}')
$directoryRoles = Get-MgBetaDirectoryRole -Filter "startswith(displayName, '$($privilegedRoles[0])')"
for ($i = 1; $i -lt $privilegedRoles.Count; $i++) {
    $directoryRoles += Get-MgBetaDirectoryRole -Filter "startswith(displayName, '$($privilegedRoles[$i])')"
}
$roleObjectIds = $directoryRoles | Select-Object -ExpandProperty Id


$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Require Phishing-Resistant MFA for Privileged Roles"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Include Directory Roles
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeRoles = $roleObjectIds
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

# Grant Controls - Require Phishing-Resistant MFA
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.BuiltInControls = @("phishingResistantMfa")
$grantControls.Operator = "OR" #Ensure 'Require one of the selected controls' is set if needed in UI
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Require Phishing-Resistant MFA for Privileged Roles' created."
```

#### MS.AAD.3.7v1 - Require Managed Devices for Authentication (SHOULD)
**Control:** Managed devices SHOULD be required for authentication.
**Rationale:** The security risk of an adversary authenticating to the tenant from their own device is reduced by requiring a managed device to authenticate. Managed devices are under the provisioning and control of the agency.
**Remediation:** Implement a Conditional Access policy to enforce that users must use managed devices (Hybrid Azure AD Joined or Compliant devices) to authenticate to Azure Entra ID.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Require Managed Devices for Authentication"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # Applications - Target all cloud apps
    $applications = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessApplications
    $applications.IncludeApplications = @("All")
    $conditions.Applications = $applications

# Grant Controls - Require Compliant or Hybrid Joined Device
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.BuiltInControls = @("compliantDevice", "domainJoinedDevice")
$grantControls.Operator = "OR" #Require one of the selected controls
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Require Managed Devices for Authentication' created."
```

**Note:** This policy requires devices to be managed by Intune (for compliance status) or Hybrid Azure AD Joined.

#### MS.AAD.3.8v1 - Require Managed Devices for MFA Registration (SHOULD)
**Control:** Managed Devices SHOULD be required to register MFA.
**Rationale:** Reduce risk of an adversary using stolen user credentials and then registering their own MFA device to access the tenant by requiring a managed device provisioned and controlled by the agency to perform registration actions.
**Remediation:** Create a Conditional Access policy to ensure that MFA registration can only be performed on managed devices.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Policy.ReadWrite.ConditionalAccess"

$conditionalAccessPolicy = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessPolicy
$conditionalAccessPolicy.DisplayName = "Require Managed Devices for MFA Registration"
$conditionalAccessPolicy.State = "Enabled" # Set to "ReportOnly" for testing initially

# Conditions
$conditions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessConditionSet
$conditionalAccessPolicy.Conditions = $conditions

    # Users and Groups - Apply to all users
    $users = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUsers
    $users.IncludeUsers = @("AllUsers")
    $conditions.Users = $users

    # User Actions - Target MFA Registration
    $userActions = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessUserActions
    $userActions.IncludeUserActions = @("urn:user:registersecurityinfo") # Register security information action
    $conditions.UserActions = $userActions

# Grant Controls - Require Compliant or Hybrid Joined Device
$grantControls = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphConditionalAccessGrantControls
$grantControls.BuiltInControls = @("compliantDevice", "domainJoinedDevice")
$grantControls.Operator = "OR" #Require one of the selected controls
$conditionalAccessPolicy.GrantControls = $grantControls

# MS Graph API URL: POST https://graph.microsoft.com/beta/identity/conditionalAccess/policies
New-MgBetaIdentityConditionalAccessPolicy -BodyParameter $conditionalAccessPolicy

Write-Host "Conditional Access policy 'Require Managed Devices for MFA Registration' created."
```

**4. Centralized Log Collection**

#### MS.AAD.4.1v1 - Send Security Logs to Security Operations Center
**Control:** Security logs SHALL be sent to the agency's security operations center for monitoring.
**Rationale:** The security risk of not having visibility into cyber attacks is reduced by collecting logs in the agency’s centralized security detection infrastructure. This makes security events available for auditing, query, and incident response.
**Remediation:** Configure Azure Diagnostic Settings for Microsoft Entra ID to stream security logs (AuditLogs, SignInLogs, etc.) to the agency's Security Information and Event Management (SIEM) system, typically via Azure Log Analytics Workspace, Azure Event Hubs, or Azure Storage.

**PowerShell using Azure Az PowerShell Module:**

```powershell
# Install Azure Az PowerShell Module if not already installed
# Install-Module Az

Connect-AzAccount

# Replace with your Resource Group and Log Analytics Workspace details
$resourceGroupName = "YourResourceGroupName"
$workspaceName = "YourLogAnalyticsWorkspaceName"
$workspaceResourceId = (Get-AzOperationalInsightsWorkspace -ResourceGroupName $resourceGroupName -Name $workspaceName).ResourceId

# Get current Diagnostic Settings for Entra ID (if any)
$diagnosticSettings = Get-AzDiagnosticSetting -ResourceId "/providers/Microsoft.AADIAM/diagnosticSettings/azureAD" -ErrorAction SilentlyContinue

if ($diagnosticSettings) {
    Write-Host "Diagnostic settings for Azure AD already exist. Updating..."
    $diagnosticSettings = Set-AzDiagnosticSetting -ResourceId "/providers/Microsoft.AADIAM/diagnosticSettings/azureAD" -WorkspaceId $workspaceResourceId -Enabled $true -Category @("AuditLogs", "SignInLogs", "RiskyUsers", "UserRiskEvents", "NonInteractiveUserSignInLogs", "ServicePrincipalSignInLogs", "ADFSSignInLogs", "RiskyServicePrincipals", "ServicePrincipalRiskEvents", "EnrichedOffice365AuditLogs", "MicrosoftGraphActivityLogs", "ManagedIdentitySignInLogs", "ProvisioningLogs") -RetentionInDays 30 # Adjust categories and retention as needed
} else {
    Write-Host "Creating new Diagnostic settings for Azure AD..."
    Set-AzDiagnosticSetting -ResourceId "/providers/Microsoft.AADIAM/diagnosticSettings/azureAD" -WorkspaceId $workspaceResourceId -Enabled $true -Category @("AuditLogs", "SignInLogs", "RiskyUsers", "UserRiskEvents", "NonInteractiveUserSignInLogs", "ServicePrincipalSignInLogs", "ADFSSignInLogs", "RiskyServicePrincipals", "ServicePrincipalRiskEvents", "EnrichedOffice365AuditLogs", "MicrosoftGraphActivityLogs", "ManagedIdentitySignInLogs", "ProvisioningLogs") -RetentionInDays 30 # Adjust categories and retention as needed
}

Write-Host "Microsoft Entra ID security logs are now being sent to Log Analytics Workspace: $($workspaceName)."
```

**Note:** Azure Diagnostic Settings are managed via Azure Resource Manager API, not Microsoft Graph API.  Replace `"YourResourceGroupName"` and `"YourLogAnalyticsWorkspaceName"` with your actual Azure details. Adjust `-Category` to include all specified log types.

**5. Application Registration and Consent**

#### MS.AAD.5.1v1 - Restrict Application Registration to Administrators
**Control:** Only administrators SHALL be allowed to register applications.
**Rationale:** Application access for the tenant presents a heightened security risk compared to interactive user access because applications are typically not subject to critical security protections, such as MFA policies. Reduce risk of unauthorized users installing malicious applications into the tenant by ensuring that only specific privileged users can register applications.
**Remediation:** Configure User settings in Azure Entra ID to prevent non-administrator users from registering applications.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Directory.ReadWrite.All"

# Get current User Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/organizationSettings/userSettings
$userSettings = Get-MgBetaOrganizationSettingsUserSetting

# Update User Settings to restrict app registrations to admins
$userSettings.UsersUnableToRegisterApplications = $true

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/organizationSettings/userSettings
Update-MgBetaOrganizationSettingsUserSetting -BodyParameter $userSettings

Write-Host "Application registration restricted to administrators only."
```

#### MS.AAD.5.2v1 - Restrict User Consent to Applications to Administrators
**Control:** Only administrators SHALL be allowed to consent to applications.
**Rationale:** Limiting applications consent to only specific privileged users reduces risk of users giving insecure applications access to their data via consent grant attacks.
**Remediation:** Disable user consent for applications in the Enterprise Applications settings of Azure Entra ID, ensuring only administrators can grant consent.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Application.ReadWrite.All", "Directory.Read.All"

# Get current Enterprise Applications Consent Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/policy/adminConsentRequestPolicy
$consentSettings = Get-MgBetaPolicyAdminConsentRequestPolicy

# Update Consent Settings to disable user consent
$consentSettings.IsEnabled = $false # Set to false to disable user consent

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/policy/adminConsentRequestPolicy
Update-MgBetaPolicyAdminConsentRequestPolicy -BodyParameter $consentSettings

Write-Host "User consent for applications disabled. Only admins can consent."
```

#### MS.AAD.5.3v1 - Configure Admin Consent Workflow for Applications
**Control:** An admin consent workflow SHALL be configured for applications.
**Rationale:** Configuring an admin consent workflow reduces the risk of the previous policy by setting up a process for users to securely request access to applications necessary for business purposes. Administrators have the opportunity to review the permissions requested by new applications and approve or deny access based on a risk assessment.
**Remediation:** Set up an admin consent workflow in Enterprise Applications settings, enabling users to request admin review and approval for applications they need to access.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Application.ReadWrite.All", "Directory.Read.All", "Group.Read.All"

# Replace with the Object ID of the Azure AD Group for Admin Consent Reviewers
$adminReviewerGroupId = "YOUR_ADMIN_REVIEWER_GROUP_OBJECT_ID" # Example: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Get current Enterprise Applications Consent Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/policy/adminConsentRequestPolicy
$consentSettings = Get-MgBetaPolicyAdminConsentRequestPolicy

# Enable Admin Consent Requests and set Reviewers Group
$consentSettings.IsEnabled = $true # Enable admin consent requests
$consentSettings.Reviewers = @(
    @{
        GroupId = $adminReviewerGroupId
    }
)

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/policy/adminConsentRequestPolicy
Update-MgBetaPolicyAdminConsentRequestPolicy -BodyParameter $consentSettings

Write-Host "Admin consent workflow configured. Admin reviewers group set."
```

**Note:** Replace `"YOUR_ADMIN_REVIEWER_GROUP_OBJECT_ID"` with the actual Object ID of the Azure AD group designated for reviewing admin consent requests.

#### MS.AAD.5.4v1 - Disable Group Owner Consent to Applications
**Control:** Group owners SHALL NOT be allowed to consent to applications.
**Rationale:** In M365, group owners and team owners can consent to applications accessing data in the tenant. By requiring consent requests to go through an approval workflow, risk of exposure to malicious applications is reduced.
**Remediation:**  Disable the ability for group owners to consent to applications accessing group data in the User consent settings of Enterprise Applications.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Application.ReadWrite.All", "Directory.Read.All"

# Get current Enterprise Applications User Consent Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/policy/authorizationPolicy
$userConsentSettings = Get-MgBetaPolicyAuthorizationPolicy

# Update User Consent Settings to disable group owner consent
$userConsentSettings.AllowGroupOwnersToConsentToApps = $false

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/policy/authorizationPolicy
Update-MgBetaPolicyAuthorizationPolicy -BodyParameter $userConsentSettings

Write-Host "Group owner consent for apps accessing data disabled."
```

**6. Passwords**

#### MS.AAD.6.1v1 - Set Passwords to Never Expire
**Control:** User passwords SHALL NOT expire.
**Rationale:** The National Institute of Standards and Technology (NIST), OMB, and Microsoft have published guidance indicating mandated periodic password changes make user accounts less secure.
**Remediation:** Configure the Password expiration policy in Azure AD to "Set passwords to never expire," aligning with modern security best practices.

**PowerShell using AzureAD PowerShell Module:**

```powershell
# Install AzureAD Module if not already installed
# Install-Module AzureAD

Connect-AzureAD

# Set Password Expiration Policy to Never Expire
Set-AzureADMSPasswordPolicy -NotificationDays 0 -ValidityPeriodInDays 0

Write-Host "Password expiration policy set to 'Passwords never expire'."
```

**Note:** Password policies in Azure AD (configured via `AzureAD` module) are not directly managed through the Microsoft Graph API.

**7. Highly Privileged User Access**

#### MS.AAD.7.1v1 - Limit Number of Global Administrator Accounts
**Control:** A minimum of two users and a maximum of eight users SHALL be provisioned with the Global Administrator role.
**Rationale:** The Global Administrator role provides unfettered access to the tenant. Limiting the number of users with this level of access makes tenant compromise more challenging.
**Remediation:** Manually audit and limit the number of accounts with the Global Administrator role to between two and eight. Regularly review and remove unnecessary Global Administrator assignments.

**PowerShell using Microsoft Graph PowerShell SDK (Beta) for counting Active Global Admins:**

```powershell
Connect-MgGraph -Scopes "RoleManagement.Read.AzureAD"

# Get Global Administrator Role Definition
# MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleDefinitions?$filter=displayName eq 'Global Administrator'
$globalAdminRoleDefinition = Get-MgBetaRoleManagementDirectoryRoleDefinition -Filter "displayName eq 'Global Administrator'"

if ($globalAdminRoleDefinition) {
    $globalAdminRoleId = $globalAdminRoleDefinition.Id

    # Count Active Global Administrator Assignments
    # MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleAssignments?$filter=roleDefinitionId eq '{globalAdminRoleId}'
    $globalAdminActiveAssignmentsCount = (Get-MgBetaRoleManagementDirectoryRoleAssignment -Filter "roleDefinitionId eq '$globalAdminRoleId'").Count

    Write-Host "Number of Active Global Administrator assignments: $globalAdminActiveAssignmentsCount"

    # Note: For Eligible assignments and PIM for Groups, you'd need to use PIM-specific cmdlets or APIs which might be more complex.
} else {
    Write-Warning "Global Administrator Role Definition not found."
}
```

**Note:** This script helps count active Global Admin assignments. For a complete count, manual review in the Azure portal is recommended.

#### MS.AAD.7.2v1 - Provision Finer-Grained Roles Instead of Global Administrator
**Control:** Privileged users SHALL be provisioned with finer-grained roles instead of Global Administrator.
**Rationale:** Many privileged administrative users do not need unfettered access to the tenant to perform their duties. By assigning them to roles based on least privilege, the risks associated with having their accounts compromised are reduced.
**Remediation:** Conduct a role-based access control (RBAC) review and assign users finer-grained, less privileged Azure AD roles based on the principle of least privilege, instead of broadly assigning the Global Administrator role.

**PowerShell using Microsoft Graph PowerShell SDK (Beta) for listing role assignments for a user (example):**

```powershell
Connect-MgGraph -Scopes "RoleManagement.Read.AzureAD", "User.ReadBasic.All"

# Replace with the User Principal Name of the user to check
$userPrincipalName = "<EMAIL>"

# Get User Object
# MS Graph API URL: GET https://graph.microsoft.com/beta/users?$filter=userPrincipalName eq '{userPrincipalName}'
$user = Get-MgBetaUser -Filter "userPrincipalName eq '$userPrincipalName'"

if ($user) {
    $userId = $user.Id

    # Get Role Assignments for the user
    # MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleAssignments?$filter=principalId eq '{userId}'
    $roleAssignments = Get-MgBetaRoleManagementDirectoryRoleAssignment -Filter "principalId eq '$userId'"

    if ($roleAssignments) {
        Write-Host "Role Assignments for user '$userPrincipalName':"
        foreach ($assignment in $roleAssignments) {
            # MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleDefinitions/{roleDefinitionId}
            $roleDefinition = Get-MgBetaRoleManagementDirectoryRoleDefinition -RoleDefinitionId $assignment.RoleDefinitionId
            Write-Host "- Role: $($roleDefinition.DisplayName)"
        }
    } else {
        Write-Host "No role assignments found for user '$userPrincipalName'."
    }
} else {
    Write-Warning "User '$userPrincipalName' not found."
}
```

**Note:** Use this script to audit individual users' roles. A broader audit and role reassignment strategy is needed for full remediation.

#### MS.AAD.7.3v1 - Use Cloud-Only Accounts for Privileged Users
**Control:** Privileged users SHALL be provisioned cloud-only accounts separate from an on-premises directory or other federated identity providers.
**Rationale:** By provisioning cloud-only Microsoft Entra ID user accounts to privileged users, the risks associated with a compromise of on-premises federation infrastructure are reduced.
**Remediation:** For highly privileged roles, use cloud-only Azure AD accounts that are not synchronized from on-premises Active Directory or federated with other identity providers. This isolates cloud admin accounts from on-premises infrastructure vulnerabilities.

**PowerShell using Microsoft Graph PowerShell SDK (Beta) to check if a user is cloud-only:**

```powershell
Connect-MgGraph -Scopes "User.Read.All"

# Replace with the User Principal Name of the privileged user to check
$userPrincipalName = "<EMAIL>"

# Get User Object
# MS Graph API URL: GET https://graph.microsoft.com/beta/users?$filter=userPrincipalName eq '{userPrincipalName}'&$select=onPremisesImmutableId
$user = Get-MgBetaUser -Filter "userPrincipalName eq '$userPrincipalName'" -SelectProperty OnPremisesImmutableId

if ($user) {
    if ($user.OnPremisesImmutableId) {
        Write-Host "User '$userPrincipalName' is NOT cloud-only. OnPremisesImmutableId is present."
    } else {
        Write-Host "User '$userPrincipalName' IS cloud-only. OnPremisesImmutableId is NOT present."
    }
} else {
    Write-Warning "User '$userPrincipalName' not found."
}
```

**Note:** If `OnPremisesImmutableId` is present, the account is *not* cloud-only. Remediation involves manual account creation and migration.

#### MS.AAD.7.4v1 - Prohibit Permanent Active Role Assignments for Highly Privileged Roles
**Control:** Permanent active role assignments SHALL NOT be allowed for highly privileged roles.
**Rationale:** Instead of giving users permanent assignments to privileged roles, provisioning access just in time lessens exposure if those accounts become compromised.
**Remediation:** Utilize Privileged Identity Management (PIM) to assign highly privileged roles as "eligible" rather than "active" and ensure that active assignments have an expiration date. Avoid permanent active assignments except for specific exceptions (emergency access, service accounts).

**Implementation:** This control is primarily managed through the Azure portal's PIM interface when assigning roles. PowerShell can be used to *audit* assignments but direct remediation is best done via PIM in the portal.

#### MS.AAD.7.5v1 - Provision Privileged Roles Only Through PAM System
**Control:** Provisioning users to highly privileged roles SHALL NOT occur outside of a PAM system.
**Rationale:** Provisioning users to privileged roles within a PAM system enables enforcement of numerous privileged access policies and monitoring.
**Remediation:** Enforce that all assignments to highly privileged roles are done exclusively through a Privileged Access Management (PAM) system like Azure PIM or a third-party PAM solution. Prevent direct role assignments outside of the PAM system.

**Implementation:**  This is primarily a procedural control. Audit role assignments regularly to ensure they are managed via PIM. PowerShell can help audit, but enforcement is process-driven.

#### MS.AAD.7.6v1 - Require Approval for Global Administrator Role Activation
**Control:** Activation of the Global Administrator role SHALL require approval.
**Rationale:** Requiring approval for a user to activate Global Administrator, which provides unfettered access, makes it more challenging for an attacker to compromise the tenant.
**Remediation:** Configure Privileged Identity Management (PIM) role settings for the Global Administrator role to require approval for activation. This adds an approval workflow whenever a user needs to activate the Global Administrator role.

**PowerShell using Microsoft Graph PowerShell SDK (Beta) to configure PIM Role Settings for Global Administrator:**

```powershell
Connect-MgGraph -Scopes "RoleManagement.ReadWrite.AzureAD", "RoleManagement.Read.AzureAD"

# Get Global Administrator Role Definition
# MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleDefinitions?$filter=displayName eq 'Global Administrator'
$globalAdminRoleDefinition = Get-MgBetaRoleManagementDirectoryRoleDefinition -Filter "displayName eq 'Global Administrator'"

if ($globalAdminRoleDefinition) {
    $globalAdminRoleId = $globalAdminRoleDefinition.Id

    # Get PIM Role Settings for Global Administrator (if exists, otherwise create)
    # MS Graph API URL: GET https://graph.microsoft.com/beta/roleManagement/directory/roleDefinitions/{unifiedRoleDefinitionId}/roleSettings
    $pimRoleSettings = Get-MgBetaRoleManagementDirectoryRoleDefinitionRoleSetting -UnifiedRoleDefinitionId $globalAdminRoleId -ErrorAction SilentlyContinue

    if (-not $pimRoleSettings) {
        Write-Host "PIM Role Settings for Global Administrator not found. Creating..."
        $pimRoleSettings = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphUnifiedRoleManagementPolicyRuleTarget
        $pimRoleSettings.TargetOperations = @("All") # Apply to all operations
        $pimRoleSettings.TargetObjects = @(
            @{
                Id = $globalAdminRoleId
                Type = "unifiedRoleDefinition"
            }
        )
    }

    # Configure Approval Required for Activation
    $approvalRule = $pimRoleSettings.Rules | Where-Object {$_.Id -eq "Expiration_Admin"} # Assuming 'Expiration_Admin' rule exists, adjust if needed. You might need to inspect existing rules or create a new one.
    if (-not $approvalRule) {
        $approvalRule = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphUnifiedRoleManagementPolicyApprovalRule
        $approvalRule.Id = "Approval_Admin" # Give it a unique ID
        $approvalRule.Target = $pimRoleSettings.Target
        $pimRoleSettings.Rules += $approvalRule # Add the new rule
    }
    $approvalRule.IsApprovalRequiredForActivation = $true
    $approvalRule.ApprovalMode = "SingleStage" # Or "MultiStage" as needed
    # Configure Approval Stages (e.g., who are the approvers - needs more complex configuration based on your approvers)
    # Example: (needs further refinement based on your approver setup)
    # $approvalStage = New-Object Microsoft.Graph.Beta.Models.MicrosoftGraphApprovalStage
    # $approvalStage.ApprovalStageTimeOutInDays = 1
    # $approvalStage.IsEscalationEnabled = $false
    # $approvalStage.PrimaryApprovers = @(
    #     @{
    #         UserId = "approver_user_object_id_1" # Replace with approver user object IDs
    #     },
    #     @{
    #         UserId = "approver_user_object_id_2"
    #     }
    # )
    # $approvalRule.Stages = @($approvalStage)


    # Update PIM Role Settings
    # MS Graph API URL: PATCH https://graph.microsoft.com/beta/roleManagement/directory/roleDefinitions/{unifiedRoleDefinitionId}/roleSettings
    Set-MgBetaRoleManagementDirectoryRoleDefinitionRoleSetting -UnifiedRoleDefinitionId $globalAdminRoleId -BodyParameter $pimRoleSettings

    Write-Host "PIM Role Settings for Global Administrator updated to require approval for activation."

} else {
    Write-Warning "Global Administrator Role Definition not found."
}
```

**Note:** Approver configuration within the script is a complex example and needs adjustment based on your specific approver setup and PIM rule structure. Portal configuration for PIM approval workflows is often more straightforward for initial setup.

#### MS.AAD.7.7v1 - Alert on Eligible and Active Highly Privileged Role Assignments
**Control:** Eligible and Active highly privileged role assignments SHALL trigger an alert.
**Rationale:** Closely monitor assignment of the highest privileged roles for signs of compromise. Send assignment alerts to enable the security monitoring team to detect compromise attempts.
**Remediation:** Configure Privileged Identity Management (PIM) security alerts to notify security monitoring teams when users are assigned as eligible or active members of highly privileged roles.

**Implementation:** Alert configuration for PIM role assignments is primarily done within the Azure portal's PIM settings for each role. You would configure email notifications to a security monitoring mailbox within the PIM role settings. PowerShell scripts to *configure* these alerts directly via API might be complex and are not provided here, as portal configuration is generally recommended for alert setup.

#### MS.AAD.7.8v1 - Alert on Global Administrator Role Activation
**Control:** User activation of the Global Administrator role SHALL trigger an alert.
**Rationale:** Closely monitor activation of the Global Administrator role for signs of compromise. Send activation alerts to enable the security monitoring team to detect compromise attempts.
**Remediation:** Configure Privileged Identity Management (PIM) security alerts to specifically notify security monitoring when the Global Administrator role is activated.

**Implementation:** Similar to MS.AAD.7.7v1, alert configuration for PIM role *activation* is primarily managed within the Azure portal's PIM settings, specifically for the Global Administrator role.  Configure email notifications for role activation events within the PIM role settings.

#### MS.AAD.7.9v1 - Alert on Activation of Other Highly Privileged Roles (SHOULD)
**Control:** User activation of other highly privileged roles SHOULD trigger an alert.
**Rationale:** Closely monitor activation of high-risk roles for signs of compromise. Send activation alerts to enable the security monitoring team to detect compromise attempts.
**Remediation:**  Consider configuring Privileged Identity Management (PIM) security alerts for the activation of other highly privileged roles (beyond Global Administrator) to enhance monitoring, balancing alert volume with security needs.

**Implementation:**  Alert configuration for activation of other privileged roles is also primarily managed within the Azure portal's PIM settings for each role, similar to MS.AAD.7.7v1 and MS.AAD.7.8v1. Configure email notifications for role activation events for the desired highly privileged roles.

**8. Guest User Access**

#### MS.AAD.8.1v1 - Limit Guest User Access to Directory Objects (SHOULD)
**Control:** Guest users SHOULD have limited or restricted access to Microsoft Entra ID directory objects.
**Rationale:** Limiting the amount of object information available to guest users in the tenant, reduces malicious reconnaissance exposure, should a guest account become compromised or be created by an adversary.
**Remediation:** Configure External collaboration settings in Azure Entra ID to limit guest users' access to directory objects. Options include "Guest users have limited access" or "Guest user access is restricted to properties and memberships of their own directory objects (most restrictive)."

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Directory.ReadWrite.All"

# Get current External Collaboration Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
$externalCollaborationSettings = Get-MgBetaOrganizationSettingsExternalCollaborationSetting

# Set Guest User Access Restrictions - Example: Limited access
$externalCollaborationSettings.GuestUserRoleSettings.GuestUserAllowedToReadOtherUsers = $false # Limited access

# To set to "most restrictive" (properties of their own objects only):
# $externalCollaborationSettings.GuestUserRoleSettings.GuestUserAllowedToReadOtherUsers = $false
# $externalCollaborationSettings.GuestUsersRestrictAccessToServerityInformation = $true # More restrictive setting (might need to verify exact property for 'most restrictive')

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
Update-MgBetaOrganizationSettingsExternalCollaborationSetting -BodyParameter $externalCollaborationSettings

Write-Host "Guest user access to directory objects restricted."
```

#### MS.AAD.8.2v1 - Restrict Guest Invitations to Guest Inviter Role (SHOULD)
**Control:** Only users with the Guest Inviter role SHOULD be able to invite guest users.
**Rationale:** By only allowing an authorized group of individuals to invite external users to create accounts in the tenant, an agency can enforce a guest user account approval process, reducing the risk of unauthorized account creation.
**Remediation:** Configure External collaboration settings to restrict guest invitations to only users assigned to specific administrator roles, effectively controlling who can invite external users.

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Directory.ReadWrite.All"

# Get current External Collaboration Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
$externalCollaborationSettings = Get-MgBetaOrganizationSettingsExternalCollaborationSetting

# Restrict Guest Invitations to Admin Roles
$externalCollaborationSettings.InvitationsAllowedTo = "adminRoleMembers" # Only admins can invite

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
Update-MgBetaOrganizationSettingsExternalCollaborationSetting -BodyParameter $externalCollaborationSettings

Write-Host "Guest user invitations restricted to admin roles."
```

#### MS.AAD.8.3v1 - Allow Guest Invites Only to Authorized Domains (SHOULD)
**Control:** Guest invites SHOULD only be allowed to specific external domains that have been authorized by the agency for legitimate business purposes.
**Rationale:** Limiting which domains can be invited to create guest accounts in the tenant helps reduce the risk of users from unauthorized external organizations getting access.
**Remediation:** In External collaboration settings, configure a list of allowed external domains to which guest invitations can be sent. Set the collaboration restrictions to "Allow invitations only to the specified domains (most restrictive)."

**PowerShell using Microsoft Graph PowerShell SDK (Beta):**

```powershell
Connect-MgGraph -Scopes "Directory.ReadWrite.All"

# Replace with your authorized domains
$allowedDomains = @("contoso.com", "fabrikam.com")

# Get current External Collaboration Settings
# MS Graph API URL: GET https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
$externalCollaborationSettings = Get-MgBetaOrganizationSettingsExternalCollaborationSetting

# Allow Invitations only to specified domains
$externalCollaborationSettings.AllowlistDomains = $allowedDomains
$externalCollaborationSettings.B2BCollaborationMode = "allowlist" # Set to allowlist mode

# MS Graph API URL: PATCH https://graph.microsoft.com/beta/organizationSettings/externalCollaborationSettings
Update-MgBetaOrganizationSettingsExternalCollaborationSetting -BodyParameter $externalCollaborationSettings

Write-Host "Guest invitations allowed only to specified domains: $($allowedDomains -join ', ')."
```
