## MS.TEAMS Controls Analysis and Remediation (Updated with Login & PowerShell Cmdlets)

Below are the extracted MS.TEAMS controls from the document, along with PowerShell remediation scripts using MS Graph cmdlets and the corresponding MS Graph API URLs.  **Each PowerShell script now includes the `Connect-MgGraph` command for login (interactive login for simplicity).**

**Important Notes:**

*   **Prerequisites:** Ensure you have the `Microsoft.Graph.Teams` PowerShell module installed (`Install-Module Microsoft.Graph.Teams`) and are connected to MS Graph with sufficient permissions (e.g., `Policy.ReadWrite.All`, `Organization.ReadWrite.All`, `TeamworkSettings.ReadWrite.All`).
*   **Global Policy vs. Custom Policies:** The document mentions both Global (Org-wide default) and custom meeting policies. The provided scripts primarily target the **Global policy** as indicated in the "Implementation" sections. If you need to apply these settings to custom policies, you will need to adjust the `Identity` parameter in the PowerShell commands to the name of your custom policy.
*   **Criticality:**  The document specifies criticality levels (SHOULD, SHALL).  "SHALL" policies are mandatory, while "SHOULD" policies are recommended best practices.
*   **API URLs:**  The provided API URLs are based on the current MS Graph documentation and might be subject to change by Microsoft. Always refer to the official Microsoft Graph documentation for the most up-to-date information. We will use Beta endpoint where needed for latest features.
*   **Testing:**  It is highly recommended to test these configurations in a non-production environment before implementing them in production.

**General Login Instructions for MS Graph PowerShell:**

Before running any of the scripts below, you need to connect to Microsoft Graph PowerShell. Open PowerShell and run:

```powershell
Install-Module Microsoft.Graph.Teams -Scope CurrentUser # Install the Teams module if not already installed
Connect-MgGraph -Scopes Policy.ReadWrite.All, Organization.ReadWrite.All, TeamworkSettings.ReadWrite.All # Connect with necessary permissions
```

---

### MS.TEAMS.1.1v1

**Control:** External meeting participants SHOULD NOT be enabled to request control of shared desktops or windows.

<!--Policy: MS.TEAMS.1.1v1; Criticality: SHOULD -->
- _Rationale:_ An external participant with control of a shared screen could potentially perform unauthorized actions on the shared screen. This policy reduces that risk by removing an external participant's ability to request control. However, if an agency has a legitimate use case to grant this control, it may be done on a case-by-case basis.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy, as well as custom meeting policies.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Set External participants can give or request control to Off
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowExternalParticipantToGiveOrRequestControl 'false'
    Write-Host "MS.TEAMS.1.1v1 Remediation applied to Global Meeting Policy: External participants control requests disabled." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "allowExternalParticipantToGiveOrRequestControl": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowExternalParticipantToGiveOrRequestControl 'false'
```

### MS.TEAMS.1.2v1

**Control:** Anonymous users SHALL NOT be enabled to start meetings.

<!--Policy: MS.TEAMS.1.2v1; Criticality: SHALL -->
- _Rationale:_ For agencies that implemented custom policies providing more flexibility to some users to automatically admit "everyone" to a meeting - this policy provides protection from anonymous users starting meeting to scrape internal contacts.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy, and custom meeting policies if they exist.
- _MITRE ATT&CK TTP Mapping:_
  - [T1078: Valid Accounts](https://attack.mitre.org/techniques/T1078/)
    - [T1078.001: Default Accounts](https://attack.mitre.org/techniques/T1078/001/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Set Anonymous users and dial-in callers can start a meeting to Off
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowAnnonymousUsersToStartMeeting $false
    Write-Host "MS.TEAMS.1.2v1 Remediation applied to Global Meeting Policy: Anonymous users meeting start disabled." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "allowAnnonymousUsersToStartMeeting": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowAnnonymousUsersToStartMeeting $false
```

### MS.TEAMS.1.3v1

**Control:** Anonymous users and dial-in callers SHOULD NOT be admitted automatically.

<!--Policy: MS.TEAMS.1.3v1; Criticality: SHOULD -->
- _Rationale:_ Automatically allowing admittance to anonymous and dial-in users diminishes control of meeting participation and invites potential data breach. This policy reduces that risk by requiring all anonymous and dial-in users to wait in a lobby until admitted by an authorized meeting participant. If the agency has a use case to admit members of specific trusted organizations and/or B2B guests automatically, custom policies may be created and assigned to authorized meeting organizers.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy. Custom meeting policies MAY be created to allow specific users more flexibility. For example, B2B guest users and trusted partner members may be admitted automatically into meetings organized by authorized users.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Set Who can bypass the lobby to "People in my organization" and People dialing in can bypass the lobby to Off
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AutoAdmittedUsers 'organization' -AllowDialInBypassLobby $false
    Write-Host "MS.TEAMS.1.3v1 Remediation applied to Global Meeting Policy: Anonymous and dial-in users bypass lobby disabled." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "autoAdmittedUsers": "organization",
  "allowDialInBypassLobby": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AutoAdmittedUsers 'organization' -AllowDialInBypassLobby $false
```

### MS.TEAMS.1.4v1

**Control:** Internal users SHOULD be admitted automatically.

<!--Policy: MS.TEAMS.1.4v1; Criticality: SHOULD -->
- _Rationale:_ Requiring internal users to wait in the lobby for explicit admission can lead to admission fatigue. This policy enables internal users to be automatically admitted to the meeting through global policy.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy. Custom meeting policies MAY be created to allow specific users more flexibility.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Ensure Who can bypass the lobby is set to "People in my organization"
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AutoAdmittedUsers 'organization'
    Write-Host "MS.TEAMS.1.4v1 Remediation applied to Global Meeting Policy: Internal users auto-admitted." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "autoAdmittedUsers": "organization"
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AutoAdmittedUsers 'organization'
```

### MS.TEAMS.1.5v1

**Control:** Dial-in users SHOULD NOT be enabled to bypass the lobby.

<!--Policy: MS.TEAMS.1.5v1; Criticality: SHOULD -->
- _Rationale:_ Automatically admitting dial-in users reduces control over who can participate in a meeting and increases potential for data breaches. This policy reduces the risk by requiring all dial-in users to wait in a lobby until they are admitted by an authorized meeting participant.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy, as well as custom meeting policies.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Set People dialing in can bypass the lobby to Off
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowDialInBypassLobby $false
    Write-Host "MS.TEAMS.1.5v1 Remediation applied to Global Meeting Policy: Dial-in users bypass lobby disabled." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "allowDialInBypassLobby": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowDialInBypassLobby $false
```

### MS.TEAMS.1.6v1

**Control:** Meeting recording SHOULD be disabled.

<!--Policy: MS.TEAMS.1.6v1; Criticality: SHOULD -->
- _Rationale:_ Allowing any user to record a Teams meeting or group call may lead to unauthorized disclosure of shared information, including audio, video, and shared screens. By disabling the meeting recording setting in the Global (Org-wide default) meeting policy, an agency limits information exposure.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy, as well as custom meeting policies. Custom policies MAY be created to allow more flexibility for specific users.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Meeting Policy
$meetingPolicy = Get-MgBetaPolicyMeetingPolicy -PolicyId Global

# Set Meeting recording to Off
if ($meetingPolicy) {
    Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowCloudRecording $false
    Write-Host "MS.TEAMS.1.6v1 Remediation applied to Global Meeting Policy: Meeting recording disabled." -ForegroundColor Green
} else {
    Write-Warning "Global Meeting Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/meetingPolicies/Global
Content-Type: application/json

{
  "allowCloudRecording": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyMeetingPolicy -PolicyId Global -AllowCloudRecording $false
```

### MS.TEAMS.1.7v1

**Control:** Record an event SHOULD be set to Organizer can record.

<!--Policy: MS.TEAMS.1.7v1; Criticality: SHOULD -->
- _Rationale:_ The security risk of the default settings for Live Events is Live Events can be recorded by all participants by default. Limiting recording permissions to only the organizer minimizes the security risk to the organizer's discretion for these Live Events.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) meeting policy, as well as custom meeting policies. Custom policies MAY be created to allow more flexibility for specific users.
- _MITRE ATT&CK TTP Mapping:_
  - None

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get the Global Live Events Policy (Note: Different policy type)
$liveEventsPolicy = Get-MgBetaPolicyTeamsLivestreamPolicy -PolicyId Global

# Set Record an event to Organizer can record
if ($liveEventsPolicy) {
    Update-MgBetaPolicyTeamsLivestreamPolicy -PolicyId Global -LiveEventRecordingMode 'Organizer'
    Write-Host "MS.TEAMS.1.7v1 Remediation applied to Global Live Events Policy: Live event recording set to Organizer only." -ForegroundColor Green
} else {
    Write-Warning "Global Live Events Policy not found. Please ensure it exists."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/teamsLivestreamPolicies/Global
Content-Type: application/json

{
  "liveEventRecordingMode": "Organizer"
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyTeamsLivestreamPolicy -PolicyId Global -LiveEventRecordingMode 'Organizer'
```

### MS.TEAMS.2.1v1

**Control:** External access for users SHALL only be enabled on a per-domain basis.

<!--Policy: MS.TEAMS.2.1v1; Criticality: SHALL -->
- _Rationale:_ The default configuration allows members to communicate with all external users with similar access permissions. This unrestricted access can lead to data breaches and other security threats. This policy provides protection against threats posed by unrestricted access by allowing communication with only trusted domains.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1199: Trusted Relationship](https://attack.mitre.org/techniques/T1199/)
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Organization.ReadWrite.All

# Get External Access Settings
$externalAccessSettings = Get-MgBetaOrganizationSettingExternalAccessSetting

# Set to allow only specific domains and clear currently allowed domains (if needed, replace with your allowed domains)
if ($externalAccessSettings) {
    Update-MgBetaOrganizationSettingExternalAccessSetting -AllowListType 'AllowSpecific' -AllowedDomains @()
    Write-Host "MS.TEAMS.2.1v1 Remediation applied: External access restricted to specific domains. Allowed domains list is now empty (configure allowed domains as needed)." -ForegroundColor Green
} else {
    Write-Warning "External Access Settings not found."
}
```

**To add specific domains to the allow list, modify the `-AllowedDomains` parameter like this:**

```powershell
Update-MgBetaOrganizationSettingExternalAccessSetting -AllowListType 'AllowSpecific' -AllowedDomains @("domain1.com", "domain2.net")
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/organizationSettings/externalAccessSettings
Content-Type: application/json

{
  "allowListType": "allowSpecific",
  "allowedDomains": []  // Add your allowed domains here if needed
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaOrganizationSettingExternalAccessSetting -AllowListType 'AllowSpecific' -AllowedDomains @()
```

### MS.TEAMS.2.2v1

**Control:** Unmanaged users SHALL NOT be enabled to initiate contact with internal users.

<!--Policy: MS.TEAMS.2.2v1; Criticality: SHALL -->
- _Rationale:_ Allowing contact from unmanaged users can expose users to email and contact address harvesting. This policy provides protection against this type of harvesting.
- _Last modified:_ July 2023
- _Note:_ This policy is not applicable to Government Community Cloud (GCC), GCC High, and Department of Defense (DoD) tenants.
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Organization.ReadWrite.All

# Get External Access Settings
$externalAccessSettings = Get-MgBetaOrganizationSettingExternalAccessSetting

# Disable communication with unmanaged Teams accounts
if ($externalAccessSettings) {
    Update-MgBetaOrganizationSettingExternalAccessSetting -AllowFederationWithUnmanagedTenants $false
    Write-Host "MS.TEAMS.2.2v1 Remediation applied: Unmanaged users cannot initiate contact." -ForegroundColor Green
} else {
    Write-Warning "External Access Settings not found."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/organizationSettings/externalAccessSettings
Content-Type: application/json

{
  "allowFederationWithUnmanagedTenants": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaOrganizationSettingExternalAccessSetting -AllowFederationWithUnmanagedTenants $false
```

### MS.TEAMS.2.3v1

**Control:** Internal users SHOULD NOT be enabled to initiate contact with unmanaged users.

<!--Policy: MS.TEAMS.2.3v1; Criticality: SHOULD -->
- _Rationale:_ Contact with unmanaged users can pose the risk of data leakage and other security threats. This policy provides protection by disabling internal user access to unmanaged users.
- _Last modified:_ July 2023
- _Note:_ This policy is not applicable to Government Community Cloud (GCC), GCC High, and Department of Defense (DoD) tenants.
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Organization.ReadWrite.All

# Get External Access Settings
$externalAccessSettings = Get-MgBetaOrganizationSettingExternalAccessSetting

# Disable internal users initiating contact with unmanaged Teams accounts
if ($externalAccessSettings) {
    Update-MgBetaOrganizationSettingExternalAccessSetting -AllowUsersInOrganizationToCommunicateWithUnmanagedUsers $false
    Write-Host "MS.TEAMS.2.3v1 Remediation applied: Internal users cannot initiate contact with unmanaged users." -ForegroundColor Green
} else {
    Write-Warning "External Access Settings not found."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/organizationSettings/externalAccessSettings
Content-Type: application/json

{
  "allowUsersInOrganizationToCommunicateWithUnmanagedUsers": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaOrganizationSettingExternalAccessSetting -AllowUsersInOrganizationToCommunicateWithUnmanagedUsers $false
```

### MS.TEAMS.3.1v1

**Control:** Contact with Skype users SHALL be blocked.

<!--Policy: MS.TEAMS.3.1v1; Criticality: SHALL -->
- _Rationale:_ Microsoft is officially retiring all forms of Skype as listed above. Allowing contact with Skype users puts agency users at additional security risk.  By blocking contact with Skype users an agency limits access to security threats utilizing the vulnerabilities of the Skype product.
- _Last modified:_ July 2023
- _Note:_ This policy is not applicable to Government Community Cloud (GCC), GCC High, and Department of Defense (DoD) tenants.
- _MITRE ATT&CK TTP Mapping:_
  - [T1567: Exfiltration Over Web Service](https://attack.mitre.org/techniques/T1567/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Organization.ReadWrite.All

# Get External Access Settings
$externalAccessSettings = Get-MgBetaOrganizationSettingExternalAccessSetting

# Disable communication with Skype users
if ($externalAccessSettings) {
    Update-MgBetaOrganizationSettingExternalAccessSetting -AllowSkypeConsumer $false
    Write-Host "MS.TEAMS.3.1v1 Remediation applied: Communication with Skype users blocked." -ForegroundColor Green
} else {
    Write-Warning "External Access Settings not found."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/organizationSettings/externalAccessSettings
Content-Type: application/json

{
  "allowSkypeConsumer": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaOrganizationSettingExternalAccessSetting -AllowSkypeConsumer $false
```

### MS.TEAMS.4.1v1

**Control:** Teams email integration SHALL be disabled.

<!--Policy: MS.TEAMS.4.1v1; Criticality: SHALL -->
- _Rationale:_ Microsoft Teams email integration associates a Microsoft, not tenant domain, email address with a Teams channel. Channel emails are addressed using the Microsoft-owned domain <code>&lt;teams.ms&gt;</code>. By disabling Teams email integration, an agency prevents potentially sensitive Teams messages from being sent through external email gateways.
- _Last modified:_ July 2023
- _Note:_ Teams email integration is not available in GCC, GCC High, or DoD regions.
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)
    - [T1204.002: Malicious File](https://attack.mitre.org/techniques/T1204/002/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes TeamworkSettings.ReadWrite.All

# Get Teams Settings
$teamsSettings = Get-MgBetaTeamworkTeamsSetting

# Disable email integration
if ($teamsSettings) {
    Update-MgBetaTeamworkTeamsSetting -EmailIntegrationEnabled $false
    Write-Host "MS.TEAMS.4.1v1 Remediation applied: Teams email integration disabled." -ForegroundColor Green
} else {
    Write-Warning "Teams Settings not found."
}
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/teamwork/teamsSettings
Content-Type: application/json

{
  "emailIntegrationEnabled": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaTeamworkTeamsSetting -EmailIntegrationEnabled $false
```

### MS.TEAMS.5.1v1

**Control:** Agencies SHOULD only allow installation of Microsoft apps approved by the agency.

<!--Policy: MS.TEAMS.5.1v1; Criticality: SHOULD -->
- _Rationale:_ Allowing Teams integration with all Microsoft apps can expose the agency to potential vulnerabilities present in those apps. By only allowing specific apps and blocking all others, the agency will better manage its app integration and potential exposure points.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) policy, all custom policies, and the org-wide app settings. Custom policies MAY be created to allow more flexibility for specific users.
- _MITRE ATT&CK TTP Mapping:_
  - [T1195: Supply Chain Compromise](https://attack.mitre.org/techniques/T1195/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All

# Get Global App Permission Policy
$appPermissionPolicy = Get-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global

if ($appPermissionPolicy) {
    # Set Microsoft App permission to "Allow specific apps and block all others"
    Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -PermissionType 'SpecificApps'

    # To allow specific Microsoft apps, you'd need to manage allowed app IDs.
    # Example to clear existing allowed Microsoft Apps (you'll need to add specific apps based on agency approval)
    Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -AllowedMicrosoftApps @()

    Write-Host "MS.TEAMS.5.1v1 Remediation applied to Global App Permission Policy: Microsoft apps restricted to specific apps (currently none allowed, configure as needed)." -ForegroundColor Green
} else {
    Write-Warning "Global App Permission Policy not found."
}
```

**To allow specific Microsoft Apps, you need to know their IDs and add them to the `AllowedMicrosoftApps` list. Example to allow specific apps (replace with actual app IDs):**

```powershell
$allowedAppIds = @(
    @{ 'Id' = 'microsoft-app-id-1' },
    @{ 'Id' = 'microsoft-app-id-2' }
)
Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -PermissionType 'SpecificApps' -AllowedMicrosoftApps $allowedAppIds
```

**MS Graph API URL & PowerShell Call:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/teamsAppPermissionPolicies/Global
Content-Type: application/json

{
  "permissionType": "specificApps",
  "allowedMicrosoftApps": [] // Add Microsoft App IDs as needed in array of objects with "id" property
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -PermissionType 'SpecificApps' -AllowedMicrosoftApps @() # or with specific app IDs
```

### MS.TEAMS.5.2v1

**Control:** Agencies SHOULD only allow installation of third-party apps approved by the agency.

<!--Policy: MS.TEAMS.5.2v1; Criticality: SHOULD -->
- _Rationale:_ Allowing Teams integration with third-party apps can expose the agency to potential vulnerabilities present in an app not managed by the agency. By allowing only specific apps approved by the agency and blocking all others, the agency can limit its exposure to third-party app vulnerabilities.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) policy, all custom policies if they exist, and the org-wide settings. Custom policies MAY be created to allow more flexibility for specific users. Third-party apps are not available in GCC, GCC High, or DoD regions.
- _MITRE ATT&CK TTP Mapping:_
  - [T1195: Supply Chain Compromise](https://attack.mitre.org/techniques/T1195/)
  - [T1528: Steal Application Access Token](https://attack.mitre.org/techniques/T1528/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All, TeamworkSettings.ReadWrite.All

# Get Org-Wide App Settings to disable 3rd party apps org-wide initially
$orgWideAppSettings = Get-MgBetaTeamworkTeamsAppSetting

if ($orgWideAppSettings) {
    Update-MgBetaTeamworkTeamsSetting -AllowThirdPartyApps $false
    Write-Host "Org-wide Third-party apps disabled as initial step for MS.TEAMS.5.2v1 remediation." -ForegroundColor Yellow
} else {
    Write-Warning "Org-wide App Settings not found."
}


# Get Global App Permission Policy
$appPermissionPolicy = Get-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global

if ($appPermissionPolicy) {
    # Set Third-party App permission to "Block all apps" initially
    Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -ThirdPartyAppPermissionType 'BlockAllApps'
    Write-Host "MS.TEAMS.5.2v1 Remediation applied to Global App Permission Policy: Third-party apps blocked by default (configure specific allowed apps if needed)." -ForegroundColor Green
} else {
    Write-Warning "Global App Permission Policy not found."
}
```

**To allow specific third-party Apps, you would need to change `ThirdPartyAppPermissionType` to `'SpecificApps'` and manage `AllowedThirdPartyApps` similar to Microsoft apps, using App IDs.**

**MS Graph API URLs & PowerShell Calls:**

**Disable 3rd party apps Org-Wide:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/teamwork/teamsAppSettings
Content-Type: application/json

{
  "allowThirdPartyApps": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaTeamworkTeamsSetting -AllowThirdPartyApps $false
```

**Set Global App Permission Policy for 3rd Party Apps:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/teamsAppPermissionPolicies/Global
Content-Type: application/json

{
  "thirdPartyAppPermissionType": "blockAllApps" // or "specificApps" if allowing specific apps
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -ThirdPartyAppPermissionType 'BlockAllApps' # or 'SpecificApps'
```

### MS.TEAMS.5.3v1

**Control:** Agencies SHOULD only allow installation of custom apps approved by the agency.

<!--Policy: MS.TEAMS.5.3v1; Criticality: SHOULD -->
- _Rationale:_ Allowing custom apps integration can expose the agency to potential vulnerabilities present in an app not managed by the agency. By allowing only specific apps approved by the agency and blocking all others, the agency can limit its exposure to custom app vulnerabilities.
- _Last modified:_ July 2023
- _Note:_ This policy applies to the Global (Org-wide default) policy, all custom policies if they exist, and the org-wide settings. Custom policies MAY be created to allow more flexibility for specific users. Custom apps are not available in GCC, GCC High, or DoD regions.
- _MITRE ATT&CK TTP Mapping:_
  - [T1195: Supply Chain Compromise](https://attack.mitre.org/techniques/T1195/)
  - [T1528: Steal Application Access Token](https://attack.mitre.org/techniques/T1528/)

**PowerShell Remediation:**

```powershell
# Login to MS Graph PowerShell
Connect-MgGraph -Scopes Policy.ReadWrite.All, TeamworkSettings.ReadWrite.All

# Get Org-Wide App Settings to disable custom apps org-wide initially
$orgWideAppSettings = Get-MgBetaTeamworkTeamsAppSetting

if ($orgWideAppSettings) {
    Update-MgBetaTeamworkTeamsAppSetting -AllowUserSideloadingApps $false
    Write-Host "Org-wide Custom apps disabled as initial step for MS.TEAMS.5.3v1 remediation." -ForegroundColor Yellow
} else {
    Write-Warning "Org-wide App Settings not found."
}

# Get Global App Permission Policy
$appPermissionPolicy = Get-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global

if ($appPermissionPolicy) {
    # Set Custom App permission to "Block all apps" initially
    Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -CustomAppPermissionType 'BlockAllApps'
    Write-Host "MS.TEAMS.5.3v1 Remediation applied to Global App Permission Policy: Custom apps blocked by default (configure specific allowed apps if needed)." -ForegroundColor Green
} else {
    Write-Warning "Global App Permission Policy not found."
}
```

**To allow specific custom Apps, you would need to change `CustomAppPermissionType` to `'SpecificApps'` and manage `AllowedCustomApps` similar to Microsoft and third-party apps, using App IDs.**

**MS Graph API URLs & PowerShell Calls:**

**Disable Custom Apps Org-Wide:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/teamwork/teamsAppSettings
Content-Type: application/json

{
  "allowUserSideloadingApps": false
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaTeamworkTeamsSetting -AllowUserSideloadingApps $false
```

**Set Global App Permission Policy for Custom Apps:**

```
# MS Graph API URL:
PATCH https://graph.microsoft.com/beta/policies/teamsAppPermissionPolicies/Global
Content-Type: application/json

{
  "customAppPermissionType": "blockAllApps" // or "specificApps" if allowing specific apps
}

# MS Graph PowerShell Cmdlet (as used in script above):
Update-MgBetaPolicyTeamsAppPermissionPolicy -PolicyId Global -CustomAppPermissionType 'BlockAllApps' # or 'SpecificApps'
```

### MS.TEAMS.6.1v1 & MS.TEAMS.6.2v1

**Control:**
*   **MS.TEAMS.6.1v1:** A DLP solution SHALL be enabled. The selected DLP solution SHOULD offer services comparable to the native DLP solution offered by Microsoft.
*   **MS.TEAMS.6.2v1:** The DLP solution SHALL protect personally identifiable information (PII) and sensitive information, as defined by the agency. At a minimum, sharing of credit card numbers, taxpayer identification numbers (TINs), and Social Security numbers (SSNs) via email SHALL be restricted.

<!--Policy: MS.TEAMS.6.1v1; Criticality: SHALL -->
- _Rationale:_ Teams users may inadvertently disclose sensitive information to unauthorized individuals. Data loss prevention policies provide a way for agencies to detect and prevent unauthorized disclosures.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1213: Data from Information Repositories](https://attack.mitre.org/techniques/T1213/)
  - [T1530: Data from Cloud Storage](https://attack.mitre.org/techniques/T1530/)

<!--Policy: MS.TEAMS.6.2v1; Criticality: SHALL -->
- _Rationale:_ Teams users may inadvertently share sensitive information with others who should not have access to it. Data loss prevention policies provide a way for agencies to detect and prevent unauthorized sharing of sensitive information.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1213: Data from Information Repositories](https://attack.mitre.org/techniques/T1213/)
  - [T1530: Data from Cloud Storage](https://attack.mitre.org/techniques/T1530/)

**Remediation:** These controls are about enabling and configuring a DLP solution.  The document refers to Microsoft Purview DLP but allows for third-party solutions.  **Code-based remediation for DLP is complex and highly dependent on the specific DLP solution used.**

**Using Microsoft Purview DLP (Example - Creating a DLP Policy for Teams):**

PowerShell for Purview DLP policy creation is possible, but it's more commonly managed through the Purview Compliance Portal.  However, here's a conceptual PowerShell example to create a basic DLP policy for Teams using the `Set-DlpCompliancePolicy` and `Set-DlpComplianceRule` cmdlets (These cmdlets are part of Security & Compliance PowerShell, not MS Graph directly, but are relevant to M365 administration):

```powershell
# **Important**: Connect to Security & Compliance PowerShell - NOT MS Graph PowerShell
# Use: Connect-IPPSSession

# Define policy parameters
$policyName = "Teams DLP Policy - Sensitive Info"
$policyDescription = "DLP Policy to protect PII in Teams"
$policyLocation = "Teams" # Target Teams locations
$sensitiveInfoTypes = @("Credit Card Number","U.S. Social Security Number (SSN)") # Example sensitive info types

# Create DLP Compliance Policy (if it doesn't exist)
if (!(Get-DlpCompliancePolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
    New-DlpCompliancePolicy -Name $policyName -Description $policyDescription -Workload $policyLocation
}

# Get the newly created or existing policy
$dlpPolicy = Get-DlpCompliancePolicy -Identity $policyName

# Create DLP Compliance Rule within the policy
if (!(Get-DlpComplianceRule -Policy $policyName -Identity "Block Sensitive Info Sharing in Teams" -ErrorAction SilentlyContinue)) {
    New-DlpComplianceRule -Policy $policyName -Name "Block Sensitive Info Sharing in Teams" -ContentContainsSensitiveInformation $sensitiveInfoTypes -BlockAccess $true - ঘরে UserNotificationsState 'On' - ঘরে NotifyUserWhenMatches ' ঘরে' - ঘরে NotifyUserWithMessage "Sensitive information detected. Sharing is blocked."
}

Write-Host "MS.TEAMS.6.1v1 & MS.TEAMS.6.2v1 Remediation (Microsoft Purview DLP Policy) configured (PowerShell example - further configuration in Purview portal needed)." -ForegroundColor Green
```

**Important for DLP:**

*   **Sensitive Information Types:**  You need to define and configure the specific sensitive information types relevant to your agency in the Purview Compliance portal or through PowerShell.
*   **Policy Actions:**  DLP policies can have various actions (block, warn, audit). The example above shows blocking. You need to configure actions based on your agency's requirements.
*   **Policy Tuning:** DLP policies often require fine-tuning to reduce false positives and ensure effective protection.

**MS Graph API URLs (for DLP are limited for direct policy creation):**

MS Graph API for Purview DLP is focused more on reporting and data classification.  Direct policy creation and management are primarily done through Security & Compliance PowerShell or the Purview Compliance Portal.  You might use MS Graph APIs to:

*   **List DLP Policies:**  Potentially using endpoints related to security and compliance but these are not directly for DLP policy *management* in the same way as the Teams settings.
*   **Data Classification APIs:** To manage sensitive information types and data classification, which are foundational to DLP.

For DLP Policy management, Security & Compliance PowerShell cmdlets are the more direct code-based approach.  **There are no direct MS Graph API calls for creating DLP policies in the same way as Teams settings policies.**

### MS.TEAMS.7.1v1 & MS.TEAMS.7.2v1

**Control:**
*   **MS.TEAMS.7.1v1:** Attachments included with Teams messages SHOULD be scanned for malware.
*   **MS.TEAMS.7.2v1:** Users SHOULD be prevented from opening or downloading files detected as malware.

<!--Policy: MS.TEAMS.7.1v1; Criticality: SHOULD -->
- _Rationale:_ Teams can be used as a mechanism for delivering malware. In many cases, malware can be detected through scanning, reducing the risk for end users.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1566: Phishing](https://attack.mitre.org/techniques/T1566/)
    - [T1566.001: Spearphishing Attachment](https://attack.mitre.org/techniques/T1566/001/)

<!--Policy: MS.TEAMS.7.2v1; Criticality: SHOULD -->
- _Rationale:_ Teams can be used as a mechanism for delivering malware. In many cases, malware can be detected through scanning, reducing the risk for end users.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.002: Malicious File](https://attack.mitre.org/techniques/T1204/002/)

**Remediation:** These controls are addressed by enabling Safe Attachments in Microsoft Defender for Office 365 for SharePoint, OneDrive, and Teams.

**PowerShell Remediation (using Security & Compliance PowerShell - not directly MS Graph):**

```powershell
# **Important**: Connect to Security & Compliance PowerShell - NOT MS Graph PowerShell
# Use: Connect-IPPSSession

# Check if a Safe Attachments policy for SPO, OneDrive, and Teams exists (adjust policy name if needed)
$safeAttachmentsPolicyName = "SafeAttachmentsForSPOTeamsOneDrivePolicy"

if (!(Get-SafeAttachmentsRule -Identity $safeAttachmentsPolicyName -ErrorAction SilentlyContinue)) {
    # Create a new Safe Attachments policy for SPO, OneDrive, and Teams if it doesn't exist
    New-SafeAttachmentsRule -Name $safeAttachmentsPolicyName -SafeAttachmentPolicy "Default Safe Attachments Policy" -SharePointOneDriveLocation 'All' -TeamsLocation 'All' -State 'Enabled' -Priority 0
    Write-Host "MS.TEAMS.7.1v1 & MS.TEAMS.7.2v1 Remediation (Safe Attachments Policy) created and enabled." -ForegroundColor Green
} else {
    Write-Host "MS.TEAMS.7.1v1 & MS.TEAMS.7.2v1 Remediation (Safe Attachments Policy) already exists. Ensuring it's enabled." -ForegroundColor Yellow
    Set-SafeAttachmentsRule -Identity $safeAttachmentsPolicyName -State 'Enabled'
}

# Ensure the associated Safe Attachments Policy is configured to block malicious files (adjust policy settings if needed)
$safeAttachmentsPolicy = Get-SafeAttachmentPolicy -Identity "Default Safe Attachments Policy" # Or your policy name

if ($safeAttachmentsPolicy) {
    if ($safeAttachmentsPolicy.Action -ne "Block") { # Check if action is not Block
        Set-SafeAttachmentPolicy -Identity "Default Safe Attachments Policy" -Action "Block" -Redirect $false # Set action to Block
        Write-Host "Safe Attachments Policy 'Default Safe Attachments Policy' updated to 'Block' action." -ForegroundColor Green
    } else {
        Write-Host "Safe Attachments Policy 'Default Safe Attachments Policy' already configured with 'Block' action." -ForegroundColor Green
    }
} else {
    Write-Warning "Safe Attachments Policy 'Default Safe Attachments Policy' not found. Ensure a Safe Attachments policy exists or adjust the policy name."
}
```

**Explanation:**

*   This script checks for and creates/enables a Safe Attachments policy that applies to SharePoint, OneDrive, and Teams.
*   It also ensures that the associated Safe Attachments Policy (e.g., "Default Safe Attachments Policy") is set to the "Block" action, which prevents users from opening or downloading malicious files.

**MS Graph API URLs (for Safe Attachments are limited):**

Similar to DLP, direct MS Graph API management of Safe Attachments policies is limited.  Security & Compliance PowerShell is the primary code-based management tool.  **There are no direct MS Graph API calls for creating Safe Attachment policies in the same way as Teams settings policies.**

### MS.TEAMS.8.1v1 & MS.TEAMS.8.2v1

**Control:**
*   **MS.TEAMS.8.1v1:** URL comparison with a blocklist SHOULD be enabled.
*   **MS.TEAMS.8.2v1:** User click tracking SHOULD be enabled.

<!--Policy: MS.TEAMS.8.1v1; Criticality: SHOULD -->
- _Rationale:_ Users may be directed to malicious websites via links in Teams. Blocking access to known malicious URLs can help prevent users from accessing known malicious websites.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)
    - [T1204.002: Malicious File](https://attack.mitre.org/techniques/T1204/002/)
  - [T1566: Phishing](https://attack.mitre.org/techniques/T1566/)
    - [T1566.001: Spearphishing Attachment](https://attack.mitre.org/techniques/T1566/001/)
  - [T1189: Drive-by Compromise](https://attack.mitre.org/techniques/T1189/)

<!--Policy: MS.TEAMS.8.2v1; Criticality: SHOULD -->
- _Rationale:_ Users may click on malicious links in Teams, leading to compromise or authorized data disclosure. Enabling user click tracking lets agencies know if a malicious link may have been visited after the fact to help tailor a response to a potential incident.
- _Last modified:_ July 2023
- _MITRE ATT&CK TTP Mapping:_
  - [T1204: User Execution](https://attack.mitre.org/techniques/T1204/)
    - [T1204.001: Malicious Link](https://attack.mitre.org/techniques/T1204/001/)
    - [T1204.002: Malicious File](https://attack.mitre.org/techniques/T1204/002/)
  - [T1566: Phishing](https://attack.mitre.org/techniques/T1566/)
    - [T1566.001: Spearphishing Attachment](https://attack.mitre.org/techniques/T1566/001/)
  - [T1189: Drive-by Compromise](https://attack.mitre.org/techniques/T1189/)

**Remediation:** These controls are addressed by enabling Safe Links in Microsoft Defender for Office 365.  The document recommends using either the "Standard" or "Strict" preset security policies in Defender for Office 365, which include Safe Links.

**PowerShell Remediation (using Security & Compliance PowerShell - not directly MS Graph):**

Preset Security Policies are primarily managed via the Microsoft 365 Defender portal. While you can't directly *create* or *modify* the *preset* security policies via PowerShell in the same way you configure custom policies, you *can* use PowerShell to:

1.  **Check the configuration of preset security policies.** (Cmdlets like `Get- হৈ PresetSecurityPolicy` exist, but detailed configuration inspection is limited via PowerShell).
2.  **Apply preset security policies to users or groups.**

**Example PowerShell to apply the Standard Preset Security Policy to a user (Security & Compliance PowerShell):**

```powershell
# **Important**: Connect to Security & Compliance PowerShell - NOT MS Graph PowerShell
# Use: Connect-IPPSSession

# Define user to apply policy to (replace with actual user UPN)
$userPrincipalName = "<EMAIL>"

# Apply Standard Preset Security Policy to the user
Set-PresetSecurityPolicyUserOverride -PresetSecurityPolicyType "Standard" -Users $userPrincipalName

Write-Host "MS.TEAMS.8.1v1 & MS.TEAMS.8.2v1 Remediation (Standard Preset Security Policy) applied to user '$userPrincipalName'." -ForegroundColor Green
```

**To apply to multiple users or groups, you would use the `-Users` and `-Groups` parameters accordingly.**

**Checking Preset Security Policy Configuration (Example):**

You can use `Get- হৈ PresetSecurityPolicy -PresetSecurityPolicyType "Standard"`  to retrieve some properties of the Standard preset policy, but detailed setting inspection and modification is primarily done through the Microsoft 365 Defender portal.

**MS Graph API URLs (for Preset Security Policies are limited for direct management):**

Similar to Safe Attachments and DLP, direct MS Graph API management of Preset Security Policies is limited. Security & Compliance PowerShell and the Microsoft 365 Defender portal are the primary management tools.  **There are no direct MS Graph API calls for managing Preset Security Policies in the same way as Teams settings policies.**

---

**Disclaimer:** The PowerShell scripts and API URLs provided are for guidance and informational purposes based on the document and current understanding of MS Graph and related PowerShell modules. Always test and validate in a non-production environment. Refer to official Microsoft documentation for the most accurate and up-to-date information. For DLP, Safe Attachments, and Safe Links, Security & Compliance PowerShell is often the more relevant code-based management tool compared to MS Graph API for policy *configuration*.  For Security & Compliance PowerShell related cmdlets, ensure you have the necessary permissions and have connected using `Connect-IPPSSession`. Remember to replace placeholder values (like email addresses and app IDs) with your actual values.