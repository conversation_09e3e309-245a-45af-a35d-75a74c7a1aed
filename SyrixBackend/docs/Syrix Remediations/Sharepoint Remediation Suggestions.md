## MS SHAREPOINT Remediation Suggestions

**Important Notes:**

*   **Login:** All PowerShell scripts include login using `Connect-MgGraph`. Ensure you have the necessary permissions (e.g., SharePoint Administrator) and have installed the MS Graph PowerShell module (`Install-Module Microsoft.Graph`).
*   **Tenant Admin URL:** Replace `<your-tenant-admin.sharepoint.com>` with your actual SharePoint Admin Center URL.
*   **Scope:**  The `Connect-MgGraph` calls include scopes necessary for SharePoint administration. Adjust scopes if needed based on your specific requirements and least privilege principles.
*   **API URLs:** For many SharePoint Admin settings, direct MS Graph API endpoints are not always explicitly documented or directly available.  SharePoint Online PowerShell cmdlets often use underlying SharePoint Admin APIs which might not be public MS Graph APIs. Where direct MS Graph API is not readily available for a specific setting, it will be mentioned that SharePoint specific cmdlets are used which might not directly translate to a public MS Graph API endpoint.
*   **Testing:**  It is highly recommended to test these scripts in a non-production environment before applying them to your production tenant.

---

### MS.SHAREPOINT.1.1v1

**Policy Text:** External sharing for SharePoint SHALL be limited to Existing guests or Only people in your organization.

**Rationale:** Sharing information outside the organization via SharePoint increases the risk of unauthorized access. By limiting external sharing, administrators decrease the risk of access to information.

**Remediation:** Limit SharePoint external sharing at the organization level to 'Existing guests only' or 'Only people in your organization'.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set SharePoint external sharing capability to 'Existing guests only'
Set-SPOTenant -SharingCapability ExistingGuestsOnly

# Alternatively, to set to 'Only people in your organization'
# Set-SPOTenant -SharingCapability InternalOnly

Write-Host "SharePoint External Sharing Capability set to ExistingGuestsOnly (or InternalOnly)."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

1. Sign in to the **SharePoint admin center**.
2. Select **Policies** \> **Sharing**.
3. Adjust external sharing slider for SharePoint to **Existing guests** or **Only people in your organization**.
4. Select **Save**.

---

### MS.SHAREPOINT.1.2v1

**Policy Text:** External sharing for OneDrive SHALL be limited to Existing guests or Only people in your organization.

**Rationale:** Sharing files outside the organization via OneDrive increases the risk of unauthorized access. By limiting external sharing, administrators decrease the risk of unauthorized unauthorized access to information.

**Remediation:** Limit OneDrive external sharing at the organization level to 'Existing guests only' or 'Only people in your organization'.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set OneDrive external sharing capability to 'Existing guests only'
Set-SPOTenant -OneDriveSharingCapability ExistingGuestsOnly

# Alternatively, to set to 'Only people in your organization'
# Set-SPOTenant -OneDriveSharingCapability InternalOnly

Write-Host "OneDrive External Sharing Capability set to ExistingGuestsOnly (or InternalOnly)."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Adjust external sharing slider for OneDrive to **Existing Guests** or **Only people in your organization**.
4. Select **Save**.

---

### MS.SHAREPOINT.1.3v1

**Policy Text:** External sharing SHALL be restricted to approved external domains and/or users in approved security groups per interagency collaboration needs.

**Rationale:** By limiting sharing to domains or approved security groups used for interagency collaboration purposes, administrators help prevent sharing with unknown organizations and individuals.

**Remediation:** Configure allowed domains and/or security groups to restrict external sharing in SharePoint.

**PowerShell Script (MS Graph PowerShell - Domains):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Example: Allow sharing only with domains 'domain1.com' and 'domain2.net'
Set-SPOTenant -SharingDomainRestrictionMode AllowList
Set-SPOTenant -SharingAllowedDomainList "domain1.com:domain2.net"

Write-Host "External sharing restricted to allowed domains."
```

**PowerShell Script (MS Graph PowerShell - Security Groups):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "Group.Read.All, SharePoint.FullControl.All"

# Example: Allow sharing for members of specific security groups (replace with actual Group IDs)
$groupIds = @("group-id-1", "group-id-2") # Get-MgGroup to find group IDs
foreach ($groupId in $groupIds) {
  Set-SPOTenant -ExternalSharingGroupsAllowList @{add=$groupId}
}

Write-Host "External sharing restricted to members of specified security groups."
```

**MS Graph API URLs:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.  MS Graph API can be used to manage groups (`/groups/{id}`), but the restriction itself is set via SharePoint cmdlets.

**Implementation Steps from Document:**

Note: This policy is only applicable if the external sharing slider on the admin page is set to any value other than **Only people in your organization**.

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Expand **More external sharing settings**.
4.  Select **Limit external sharing by domain**.
5.  Select **Add domains**.
6.  Add each approved external domain users are allowed to share files with.
7.  Select **Manage security groups**
8. Add each approved security group. Members of these groups will be allowed to share files externally.
9.  Select **Save**.

---

### MS.SHAREPOINT.1.4v1

**Policy Text:** Guest access SHALL be limited to the email the invitation was sent to.

**Rationale:** Email invitations allow external guests to access shared information. By requiring guests to sign in using the same account where the invite was sent, administrators help ensure only the intended guest can use the invite.

**Remediation:** Enable the setting "Guests must sign in using the same account to which sharing invitations are sent".

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Enable 'Guests must sign in using the same account to which sharing invitations are sent'
Set-SPOTenant -Require শেয়ারিংInvitationSignIn লাইট $true

Write-Host "Guest access limited to the invited email account."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

Note: This policy is only applicable if the external sharing slider on the admin page is set to any value other than **Only people in your organization**.

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Expand **More external sharing settings**.
4. Select **Guests must sign in using the same account to which sharing invitations are sent**.
5. Select **Save**.

---

### MS.SHAREPOINT.2.1v1

**Policy Text:** File and folder default sharing scope SHALL be set to Specific people (only the people the user specifies).

**Rationale:** By making the default sharing the most restrictive, administrators prevent accidentally sharing information too broadly.

**Remediation:** Set the default sharing link type for files and folders to "Specific people".

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set default sharing link type to 'SpecificPeople'
Set-SPOTenant -Default শেয়ারিংLinkType SpecificPeople

Write-Host "Default sharing link type set to Specific People."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**
3.  Under **File and folder links**, set the default link type to **Specific people (only the people the user specifies)**
4.  Select **Save**

---

### MS.SHAREPOINT.2.2v1

**Policy Text:** File and folder default sharing permissions SHALL be set to View.

**Rationale:** Edit access to files and folders could allow a user to make unauthorized changes.  By restricting default permissions to **View**, administrators prevent unintended or malicious modification.

**Remediation:** Set the default sharing link permission for files and folders to "View".

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set default sharing link permission to 'View'
Set-SPOTenant -Default শেয়ারিংLinkPermission View

Write-Host "Default sharing link permission set to View."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

1.  Sign in to the **SharePoint admin center**.
2. Select **Policies** \> **Sharing**.
3. Under **File and folder links**, set the permission that is selected by default for sharing links to **View**.
4. Select **Save**.

---

### MS.SHAREPOINT.3.1v1

**Policy Text:** Expiration days for Anyone links SHALL be set to 30 days or less.

**Rationale:** Links may be used to provide access to information for a short period of time. Without expiration, however, access is indefinite. By setting expiration timers for links, administrators prevent unintended sustained access to information.

**Remediation:** Configure Anyone links to expire within 30 days.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set expiration for Anyone links to 30 days
Set-SPOTenant -AnonymousLink শেয়ারিংCapabilityExpirationInDays 30

Write-Host "Expiration for Anyone links set to 30 days."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

Note: This policy is only applicable if the external sharing slider on the admin center sharing page is set to **Anyone**.

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Scroll to the section **Choose expiration and permissions options for Anyone links**.
4.  Select the checkbox **These links must expire within this many days**.
5.  Enter **30** days or less.
6.  Select **Save**.

---

### MS.SHAREPOINT.3.2v1

**Policy Text:** The allowable file and folder permissions for links SHALL be set to View only.

**Rationale:** Unauthorized changes to files can be made if permissions allow editing by anyone.  By restricting permissions on links to **View** only, administrators prevent anonymous file changes.

**Remediation:** Set the permission for Anyone links to "View" for both files and folders.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set default permission for Anyone links to 'View'
Set-SPOTenant - AnonymousLinkByDefault শেয়ারিংPermission View

Write-Host "Default permission for Anyone links set to View."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

Note: This policy is only applicable if the external sharing slider on the admin center sharing page is set to **Anyone**.

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Scroll to the section **Choose expiration and permissions options for Anyone links**.
4.  Set the configuration items in the section **These links can give these permissions**.
5.  Set the **Files** option to **View**.
6.  Set the **Folders** option to **View**.
7.  Select **Save**.

---

### MS.SHAREPOINT.3.3v1

**Policy Text:** Reauthentication days for people who use a verification code SHALL be set to 30 days or less.

**Rationale:** A verification code may be given out to provide access to information for a short period of time. By setting expiration timers for verification code access, administrators prevent  unintended sustained access to information.

**Remediation:** Set the re-authentication period for verification code users to 30 days.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Set re-authentication days for verification code users to 30 days
Set-SPOTenant -Verification শেয়ারিংCapabilityExpirationInDays 30

Write-Host "Re-authentication days for verification code users set to 30 days."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

Note: This policy is only applicable if the external sharing slider on the admin center sharing page is set to **Anyone** or **New and existing guests**.

1.  Sign in to the **SharePoint admin center**.
2.  Select **Policies** \> **Sharing**.
3.  Expand **More external sharing settings**.
4. Select **People who use a verification code must reauthenticate after this many days**.
5.  Enter **30** days or less.
6. Select **Save**.

---

### MS.SHAREPOINT.4.2v1

**Policy Text:** Users SHALL be prevented from running custom scripts on self-service created sites.

**Rationale:** Scripts on SharePoint sites run in the context of users visiting the site and therefore provide access to everything users can access. By preventing custom scripts on self-service created sites, administrators block a path for potentially malicious code execution.

**Remediation:** Prevent users from running custom scripts on self-service created SharePoint sites.

**PowerShell Script (MS Graph PowerShell):**

```powershell
# Login to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "SharePoint.FullControl.All"

# Prevent users from running custom script on self-service created sites
Set-SPOTenant -SelfServiceSiteCreationUsersCanRunCustomScript Disabled

Write-Host "Custom scripts prevented on self-service created sites."
```

**MS Graph API URL:** Not directly available via documented MS Graph API. `Set-SPOTenant` cmdlet uses SharePoint Online admin APIs.

**Implementation Steps from Document:**

1.  Sign in to the **SharePoint admin center**.
2.  Select **Settings**.
3.  Scroll down and select **classic settings page**.
4.  Scroll to the **Custom Script** section.
5.  Select **Prevent users from running custom script on self-service created sites**.
6.  Select **OK**.