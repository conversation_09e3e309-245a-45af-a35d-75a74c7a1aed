## MS.EXO Controls and Suggested Remediations

**1. MS.EXO.1.1v1: Automatic Forwarding to External Domains**

*   **Control Description:** Automatic forwarding to external domains SHALL be disabled.
*   **Rationale:** Prevents data exfiltration via client-side forwarding rules.
*   **Implementation Instructions (from document):** Via Exchange Admin Center -> Mail flow -> Remote domains -> Default -> Edit reply types -> Clear "Allow automatic forwarding".
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    # Get the default remote domain policy
    $RemoteDomain = Get-RemoteDomain -Identity Default

    # Disable automatic forwarding for the default remote domain
    if ($RemoteDomain.AutoForwardEnabled -eq $true) {
        Set-RemoteDomain -Identity Default -AutoForwardEnabled $false
        Write-Host "Automatic forwarding to external domains disabled for Default Remote Domain." -ForegroundColor Green
    } else {
        Write-Host "Automatic forwarding to external domains is already disabled for Default Remote Domain." -ForegroundColor Yellow
    }

    # You might need to repeat for other remote domains if you have custom ones
    # To list all remote domains: Get-RemoteDomain
    # Example for a custom domain named "CustomRemoteDomain":
    # $RemoteDomainCustom = Get-RemoteDomain -Identity CustomRemoteDomain
    # if ($RemoteDomainCustom.AutoForwardEnabled -eq $true) {
    #     Set-RemoteDomain -Identity CustomRemoteDomain -AutoForwardEnabled $false
    #     Write-Host "Automatic forwarding disabled for CustomRemoteDomain." -ForegroundColor Green
    # }
    ```

**2. MS.EXO.2.2v2: Sender Policy Framework (SPF)**

*   **Control Description:** An SPF policy SHALL be published for each domain that fails all non-approved senders.
*   **Rationale:** Mitigates email spoofing by verifying authorized sending IPs.
*   **Implementation Instructions (from document):** Via DNS record configuration (outside Exchange Admin Center).
*   **Suggested Remediation (PowerShell/API - Verification & DNS Management Guidance):**

    *   **Verification (PowerShell):**  You can use `Resolve-DnsName` to check the existing SPF record.

        ```powershell
        $domain = "yourdomain.com" # Replace with your domain
        $spfRecord = Resolve-DnsName -Name $domain -Type TXT | Where-Object {$_.Strings -like "*spf1*"}

        if ($spfRecord) {
            Write-Host "SPF Record found for $($domain): $($spfRecord.Strings)" -ForegroundColor Green
            if ($spfRecord.Strings -notlike "*-all") {
                Write-Warning "SPF record for $($domain) does not end in '-all' (hard fail). Consider reviewing."
            }
        } else {
            Write-Warning "No SPF record found for $($domain). Please configure SPF record in your DNS settings."
        }
        ```
    *   **DNS Management (API Guidance):**  Directly managing DNS records via PowerShell within Exchange Online is not possible. DNS management is typically done through your DNS hosting provider's interface or APIs. Many DNS providers (like Azure DNS, AWS Route 53, Cloudflare) offer PowerShell modules or REST APIs to manage DNS records.  You would need to use the specific module/API for your DNS provider to automate SPF record creation/modification.

        *   **Example (Azure DNS - conceptual):** If using Azure DNS, you could use `Azure PowerShell` to manage DNS records.  The specific commands would depend on your Azure setup.

            ```powershell
            # Example - Conceptual and requires Azure DNS PowerShell modules and connection
            # Replace placeholders with your actual values
            # $resourceGroupName = "YourResourceGroupName"
            # $dnsZoneName = "yourdomain.com"
            # $recordSetName = "@" # or your subdomain if needed
            # $spfRecordValue = '"v=spf1 include:spf.protection.outlook.com -all"'

            # New-AzDnsRecordSet -Name $recordSetName -RecordType TXT -ZoneName $dnsZoneName -ResourceGroupName $resourceGroupName -Ttl 3600 -TxtRecord @([Microsoft.Azure.Commands.Dns.DnsRecordBase.TxtRecord]@{Text=$spfRecordValue}) -Overwrite
            # Write-Host "SPF record created/updated in Azure DNS." -ForegroundColor Green
            ```
        *   **General Guidance:**  Refer to your DNS provider's documentation for their specific PowerShell modules or API instructions to manage DNS TXT records programmatically.

**3. MS.EXO.3.1v1: DomainKeys Identified Mail (DKIM)**

*   **Control Description:** DKIM SHOULD be enabled for all domains.
*   **Rationale:** Enhances email authenticity and integrity using digital signatures.
*   **Implementation Instructions (from document):** Via Microsoft 365 Defender portal (UI).
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    $domain = "yourdomain.com" # Replace with your domain

    # Check DKIM signing configuration for the domain
    $dkimConfig = Get-DkimSigningConfig -Identity $domain

    if ($dkimConfig) {
        if ($dkimConfig.Enabled -eq $false) {
            Enable-DkimSigningConfig -Identity $domain
            Write-Host "DKIM signing enabled for domain $($domain)." -ForegroundColor Green
        } else {
            Write-Host "DKIM signing is already enabled for domain $($domain)." -ForegroundColor Yellow
        }
    } else {
        # If DKIM config doesn't exist, create it and then enable
        New-DkimSigningConfig -DomainName $domain -Enabled $true
        Write-Host "DKIM signing configuration created and enabled for domain $($domain)." -ForegroundColor Green
    }

    # To check status for all domains: Get-DkimSigningConfig
    ```

**4. MS.EXO.4.1v1, MS.EXO.4.2v1, MS.EXO.4.3v1, MS.EXO.4.4v1: Domain-Based Message Authentication, Reporting, and Conformance (DMARC)**

*   **Control Description:**
    *   MS.EXO.4.1v1: A DMARC policy SHALL be published for every second-level domain.
    *   MS.EXO.4.2v1: The DMARC message rejection option SHALL be p=reject.
    *   MS.EXO.4.3v1: The DMARC point of contact for aggregate reports SHALL include `<EMAIL>`.
    *   MS.EXO.4.4v1: An agency point of contact SHOULD be included for aggregate and failure reports.
*   **Rationale:** Works with SPF and DKIM to enhance email authentication and reporting.
*   **Implementation Instructions (from document):** Via DNS record configuration (outside Exchange Admin Center).
*   **Suggested Remediation (PowerShell/API - Verification & DNS Management Guidance):**

    *   **Verification (PowerShell):** You can use `Resolve-DnsName` to check the DMARC record.

        ```powershell
        $domain = "yourdomain.com" # Replace with your domain
        $dmarcRecord = Resolve-DnsName -Name "_dmarc.$domain" -Type TXT

        if ($dmarcRecord) {
            Write-Host "DMARC Record found for $($domain): $($dmarcRecord.Strings)" -ForegroundColor Green
            $dmarcString = $dmarcRecord.Strings -join "" # Combine strings if record is split
            if ($dmarcString -notlike "*p=reject*") {
                Write-Warning "DMARC record for $($domain) does not have 'p=reject'. Consider reviewing."
            }
            if ($dmarcString -notlike "*rua=mailto:<EMAIL>*") {
                 Write-Warning "DMARC record for $($domain) does not include 'rua=mailto:<EMAIL>'. Consider reviewing."
            }
             # You can add more checks for agency contact (MS.EXO.4.4v1) if needed.
        } else {
            Write-Warning "No DMARC record found for $($domain). Please configure DMARC record in your DNS settings."
        }
        ```
    *   **DNS Management (API Guidance):** Similar to SPF, DMARC record management is through your DNS provider. Use their PowerShell modules or APIs (e.g., Azure DNS, AWS Route 53, Cloudflare) to create/modify DMARC TXT records.

        *   **Example (Azure DNS - conceptual):**

            ```powershell
            # Example - Conceptual and requires Azure DNS PowerShell modules and connection
            # Replace placeholders with your actual values
            # $resourceGroupName = "YourResourceGroupName"
            # $dnsZoneName = "yourdomain.com"
            # $recordSetName = "_dmarc"
            # $dmarcRecordValue = '"v=DMARC1; p=reject; rua=mailto:<EMAIL>,mailto:<EMAIL>; ruf=mailto:<EMAIL>;"' # Example DMARC record

            # New-AzDnsRecordSet -Name $recordSetName -RecordType TXT -ZoneName $dnsZoneName -ResourceGroupName $resourceGroupName -Ttl 3600 -TxtRecord @([Microsoft.Azure.Commands.Dns.DnsRecordBase.TxtRecord]@{Text=$dmarcRecordValue}) -Overwrite
            # Write-Host "DMARC record created/updated in Azure DNS." -ForegroundColor Green
            ```
        *   **General Guidance:**  Consult your DNS provider's documentation for their programmatic DNS management methods.

**5. MS.EXO.5.1v1: Simple Mail Transfer Protocol Authentication (SMTP AUTH)**

*   **Control Description:** SMTP AUTH SHALL be disabled.
*   **Rationale:** Modern clients don't need SMTP AUTH, disabling it reduces attack surface.
*   **Implementation Instructions (from document):** Via Exchange Admin Center -> Settings -> Mail Flow -> "Turn off SMTP AUTH protocol for your organization" (checkbox).
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    # Get organization configuration
    $OrgConfig = Get-OrganizationConfig

    # Check if SMTP AUTH is disabled
    if ($OrgConfig.SmtpClientAuthenticationDisabled -eq $false) {
        Set-OrganizationConfig -SmtpClientAuthenticationDisabled $true
        Write-Host "SMTP AUTH has been disabled for the organization." -ForegroundColor Green
    } else {
        Write-Host "SMTP AUTH is already disabled for the organization." -ForegroundColor Yellow
    }
    ```

**6. MS.EXO.6.1v1, MS.EXO.6.2v1: Calendar and Contact Sharing**

*   **Control Description:**
    *   MS.EXO.6.1v1: Contact folders SHALL NOT be shared with all domains.
    *   MS.EXO.6.2v1: Calendar details SHALL NOT be shared with all domains.
*   **Rationale:** Prevents unintended information disclosure from contacts and calendars.
*   **Implementation Instructions (from document):** Via Exchange Admin Center -> Organization -> Sharing -> Individual Sharing -> Manage domains (for each policy) -> Ensure "Sharing with all domains" is not selected.
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    # Get all sharing policies
    $SharingPolicies = Get-SharingPolicy

    foreach ($Policy in $SharingPolicies) {
        Write-Host "Checking Sharing Policy: $($Policy.Name)" -ForegroundColor Cyan
        $Policy.Domains | ForEach-Object {
            if ($_.Domain -eq "*" -and $_.SharingPolicyAction -ne "Disabled") {
                Write-Host "  Policy $($Policy.Name) allows sharing with all domains. Disabling for '*' domain." -ForegroundColor Yellow
                Set-SharingPolicy -Identity $Policy.Identity -Domains @{Remove="*"} # Remove sharing with all domains
            } else {
                if ($_.Domain -eq "*") {
                    Write-Host "  Policy $($Policy.Name) already has sharing with all domains disabled." -ForegroundColor Green
                }
            }
        }
    }
    ```

**7. MS.EXO.7.1v1: External Sender Warnings**

*   **Control Description:** External sender warnings SHALL be implemented.
*   **Rationale:** Helps users identify potential phishing emails from external sources.
*   **Implementation Instructions (from document):** Via Exchange Admin Center -> Mail flow -> Rules -> Create rule -> Modify messages... -> Apply if sender is external -> Prepend subject.
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    $ruleName = "External Sender Warning"
    $prefix = "[External]"

    # Check if the rule already exists (to avoid duplicates)
    if (!(Get-TransportRule -Identity $ruleName -ErrorAction SilentlyContinue)) {
        New-TransportRule -Name $ruleName -SenderExternalInternal -SenderLocation 'NotInOrganization' -PrependSubject $prefix -Mode Enforce
        Write-Host "Mail flow rule '$ruleName' created to prepend subject with '$prefix' for external senders." -ForegroundColor Green
    } else {
        Write-Host "Mail flow rule '$ruleName' already exists." -ForegroundColor Yellow
        # Optionally, you can update the existing rule if needed:
        # Set-TransportRule -Identity $ruleName -PrependSubject $prefix -Mode Enforce
        # Write-Host "Mail flow rule '$ruleName' updated." -ForegroundColor Green
    }

    # To list all mail flow rules: Get-TransportRule
    ```

**8. MS.EXO.8.1v2, MS.EXO.8.2v2, MS.EXO.8.3v1, MS.EXO.8.4v1: Data Loss Prevention (DLP) Solutions**

*   **Control Description:**
    *   MS.EXO.8.1v2: A DLP solution SHALL be used.
    *   MS.EXO.8.2v2: The DLP solution SHALL protect PII and sensitive information.
    *   MS.EXO.8.3v1: The DLP solution SHOULD offer services comparable to Microsoft's native DLP.
    *   MS.EXO.8.4v1: At a minimum, the DLP solution SHALL restrict sharing credit card numbers, ITINs, and SSNs via email.
*   **Rationale:** Prevents data leakage and exfiltration of sensitive information.
*   **Implementation Instructions (from document):**  General guidance to use a DLP solution, with reference to Microsoft Purview DLP in Defender for Office 365 baseline.
*   **Suggested Remediation (PowerShell/API - Policy Management Guidance):**

    *   **PowerShell (Microsoft Purview Compliance PowerShell):**  Microsoft Purview DLP policies can be managed using the `Microsoft Purview Compliance PowerShell` module.

        ```powershell
        # Connect to Microsoft Purview Compliance PowerShell (if not already connected)
        # Connect-IPPSSession

        # Example: Create a DLP policy to protect SSNs, Credit Card Numbers, and ITINs in Exchange Online
        # (This is a basic example, customize rules and actions as per your requirements)

        $policyName = "DLP Policy - Protect PII in Exchange"

        if (!(Get-DlpCompliancePolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
            New-DlpCompliancePolicy -Name $policyName -ExchangeLocation All -Mode Enforce

            # Rule to detect and block sharing of sensitive info (SSN, Credit Card, ITIN) externally
            New-DlpComplianceRule -Policy $policyName -Name "Block External Sharing of PII" -ContentContainsSensitiveInformation @((Get-DlpSensitiveInformationType -Identity "U.S. Social Security Number (SSN)"), (Get-DlpSensitiveInformationType -Identity "Credit Card Number"), (Get-DlpSensitiveInformationType -Identity "U.S. Individual Taxpayer Identification Number (ITIN)")) -Action BlockAccess -BlockAccessScope "External" -UserScope All

            Write-Host "DLP Policy '$policyName' created to protect PII." -ForegroundColor Green
        } else {
            Write-Host "DLP Policy '$policyName' already exists. Consider reviewing and updating its rules." -ForegroundColor Yellow
            # To modify existing policies and rules: Use Get-DlpCompliancePolicy, Set-DlpCompliancePolicy, Get-DlpComplianceRule, Set-DlpComplianceRule
        }

        # To list DLP policies: Get-DlpCompliancePolicy
        ```
    *   **API (Microsoft Graph API - Beta):**  Microsoft Graph API (beta endpoint) also supports DLP policy management.  You would use REST API calls to create, read, update, and delete DLP policies and rules. Refer to Microsoft Graph API documentation for DLP for details on endpoints and request bodies.

        *   **Example (Conceptual - Graph API):**  A conceptual Graph API call to create a DLP policy would involve sending a POST request to `/beta/security/dlpPolicies` with a JSON payload defining the policy and rules.

**9. MS.EXO.9.1v2, MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.4v1, MS.EXO.9.5v1: Attachment File Type Filtering**

*   **Control Description:**
    *   MS.EXO.9.1v2: Emails SHALL be filtered by attachment file types.
    *   MS.EXO.9.2v1: The attachment filter SHOULD attempt to determine the true file type and assess the file extension.
    *   MS.EXO.9.3v2: Disallowed file types SHALL be determined and enforced.
    *   MS.EXO.9.4v1: Alternatively chosen filtering solutions SHOULD offer services comparable to Microsoft Defender's Common Attachment Filter.
    *   MS.EXO.9.5v1: At a minimum, click-to-run files SHOULD be blocked (e.g., .exe, .cmd, .vbe).
*   **Rationale:** Prevents malware spread via malicious attachments.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Defender Preset Security Policies.
*   **Suggested Remediation (PowerShell - Security & Compliance PowerShell / Defender PowerShell):**

    *   **PowerShell (Security & Compliance PowerShell - Anti-Malware Policies):** You can configure anti-malware policies, including the common attachments filter, using Security & Compliance PowerShell.

        ```powershell
        # Connect to Security & Compliance PowerShell (if not already connected)
        # Connect-IPPSSession

        $policyName = "Anti-Malware Policy - Block Common Executables"

        if (!(Get-AntiMalwarePolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
            New-AntiMalwarePolicy -Name $policyName -RecommendedPolicyType Standard # Or Strict if needed

            # Configure common attachment filter to block .exe, .cmd, .vbe (and you can add more)
            Set-AntiMalwarePolicy -Identity $policyName -EnableCommonAttachmentFilter $true -CommonAttachmentFilterLists @(".exe", ".cmd", ".vbe") -CommonAttachmentFilterAction Quarantine

            Write-Host "Anti-Malware Policy '$policyName' created to block common executable attachments." -ForegroundColor Green
        } else {
            Write-Host "Anti-Malware Policy '$policyName' already exists. Consider reviewing and updating its settings." -ForegroundColor Yellow
            # To modify existing policies: Get-AntiMalwarePolicy, Set-AntiMalwarePolicy
        }

        # To list anti-malware policies: Get-AntiMalwarePolicy
        ```
    *   **PowerShell (Defender PowerShell - Preset Security Policies):**  As the document mentions Preset Security Policies in Defender, you can also manage these using Defender PowerShell.  Preset security policies are often the easiest way to implement a baseline.

        ```powershell
        # Connect to Microsoft Defender PowerShell (if not already connected)
        # Connect-MsolService # If needed for older modules
        # Import-Module Microsoft.Online.SharePoint.PowerShell # If needed for older modules
        # Connect-PnPOnline # If needed for PnP PowerShell

        # Get- ষ্ট-PresetSecurityPolicy # Command to manage Preset Security Policies (check current cmdlet - might vary based on module version)
        # Explore cmdlets related to Preset Security Policies in Defender PowerShell for more specific control.
        # Note: Direct granular control over Common Attachment Filter within Preset Security Policies via PowerShell might be limited. UI or Security & Compliance PowerShell might be more suitable for detailed customization.
        # Refer to Microsoft Defender PowerShell documentation for latest cmdlets and capabilities related to Preset Security Policies.
        ```

**10. MS.EXO.10.1v1, MS.EXO.10.2v1, MS.EXO.10.3v1: Malware Scanning**

*   **Control Description:**
    *   MS.EXO.10.1v1: Emails SHALL be scanned for malware.
    *   MS.EXO.10.2v1: Emails identified as containing malware SHALL be quarantined or dropped.
    *   MS.EXO.10.3v1: Email scanning SHALL be capable of reviewing emails after delivery (ZAP).
*   **Rationale:** Protects against malware delivered via email attachments.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Defender Preset Security Policies and Exchange Online Protection.
*   **Suggested Remediation (PowerShell - Security & Compliance PowerShell / Defender PowerShell):**

    *   **PowerShell (Security & Compliance PowerShell - Anti-Malware Policies):** Malware scanning and quarantine actions are core features of anti-malware policies in Exchange Online Protection. Enabling and configuring anti-malware policies in Security & Compliance PowerShell covers these requirements.  The example in point 9 (MS.EXO.9) also implicitly enables malware scanning as part of the Anti-Malware policy.

        *   **Zero-hour auto purge (ZAP):** ZAP for malware is generally enabled by default in Exchange Online Protection. To ensure it's active, review the settings of your anti-malware policies (though explicit ZAP settings might not be directly configurable via PowerShell cmdlets; it's more of a service-level feature).
    *   **PowerShell (Defender PowerShell - Preset Security Policies):** Using Preset Security Policies (Standard or Strict) in Defender for Office 365 also automatically enables robust malware scanning and ZAP.  Preset policies are often the easiest way to ensure baseline malware protection.

**11. MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1: Phishing Protections**

*   **Control Description:**
    *   MS.EXO.11.1v1: Impersonation protection checks SHOULD be used.
    *   MS.EXO.11.2v1: User warnings, comparable to EOP user safety tips, SHOULD be displayed.
    *   MS.EXO.11.3v1: The phishing protection solution SHOULD include an AI-based phishing detection tool comparable to EOP Mailbox Intelligence.
*   **Rationale:** Protects against phishing attacks, including impersonation and malicious links.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Defender Preset Security Policies and Impersonation Protection in Defender for Office 365.
*   **Suggested Remediation (PowerShell - Defender PowerShell):**

    *   **PowerShell (Defender PowerShell - Anti-Phishing Policies):** Defender for Office 365 anti-phishing policies provide impersonation protection, user safety tips, and AI-based mailbox intelligence.  You can configure these using Defender PowerShell.

        ```powershell
        # Connect to Microsoft Defender PowerShell (if not already connected)
        # Connect-MsolService # If needed for older modules
        # Import-Module Microsoft.Online.SharePoint.PowerShell # If needed for older modules
        # Connect-PnPOnline # If needed for PnP PowerShell

        $policyName = "Anti-Phishing Policy - Enhanced Protection"

        if (!(Get-AntiPhishPolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
            New-AntiPhishPolicy -Name $policyName -RecommendedPolicyType Standard # Or Strict

            # Configure Impersonation Protection (example - customize as needed)
            Set-AntiPhishPolicy -Identity $policyName -EnableOrganizationImpersonation $true -EnableMailboxIntelligence $true -EnableSpoofIntelligence $true -EnableUserSafetyTips $true

            Write-Host "Anti-Phishing Policy '$policyName' created with enhanced protections." -ForegroundColor Green
        } else {
            Write-Host "Anti-Phishing Policy '$policyName' already exists. Consider reviewing and updating its settings." -ForegroundColor Yellow
            # To modify existing policies: Get-AntiPhishPolicy, Set-AntiPhishPolicy
        }

        # To list anti-phishing policies: Get-AntiPhishPolicy
        ```
    *   **PowerShell (Defender PowerShell - Preset Security Policies):** Again, Preset Security Policies (Standard or Strict) in Defender for Office 365 are a streamlined way to enable comprehensive phishing protection, including impersonation, safety tips, and AI-based detection.  Using preset policies is often recommended for easier baseline configuration.

**12. MS.EXO.12.1v1, MS.EXO.12.2v1: IP Allow Lists and Safe Lists**

*   **Control Description:**
    *   MS.EXO.12.1v1: IP allow lists SHOULD NOT be created.
    *   MS.EXO.12.2v1: Safe lists SHOULD NOT be enabled. (IP block lists MAY be used).
*   **Rationale:** Allow lists bypass security filters, safe lists can be risky. Block lists can be used for known spammers.
*   **Implementation Instructions (from document):** Via Microsoft 365 Defender portal -> Threat policies -> Anti-spam -> Connection filter policy (Default) -> Edit connection filter policy -> Ensure allow list is empty, safe list is off, configure block list if needed.
*   **Suggested Remediation (PowerShell - Defender PowerShell):**

    ```powershell
    # Connect to Microsoft Defender PowerShell (if not already connected)
    # Connect-MsolService # If needed for older modules
    # Import-Module Microsoft.Online.SharePoint.PowerShell # If needed for older modules
    # Connect-PnPOnline # If needed for PnP PowerShell

    # Get the default connection filter policy
    $ConnectionFilterPolicy = Get-HostedConnectionFilterPolicy -Identity "Default"

    # Check and clear IP Allow List
    if ($ConnectionFilterPolicy.IPAllowList -ne $null -and $ConnectionFilterPolicy.IPAllowList.Count -gt 0) {
        Set-HostedConnectionFilterPolicy -Identity "Default" -IPAllowList @()
        Write-Host "IP Allow List cleared in Default Connection Filter Policy." -ForegroundColor Green
    } else {
        Write-Host "IP Allow List is already empty in Default Connection Filter Policy." -ForegroundColor Yellow
    }

    # Check and disable Safe List
    if ($ConnectionFilterPolicy.EnableSafeList -eq $true) {
        Set-HostedConnectionFilterPolicy -Identity "Default" -EnableSafeList $false
        Write-Host "Safe List disabled in Default Connection Filter Policy." -ForegroundColor Green
    } else {
        Write-Host "Safe List is already disabled in Default Connection Filter Policy." -ForegroundColor Yellow
    }

    # Example - To add to IP Block List (optional - use with caution and for specific known spammers)
    # $blockIPsToAdd = "************", "********/32" # Example IPs to block
    # Set-HostedConnectionFilterPolicy -Identity "Default" -IPBlockList @{Add = $blockIPsToAdd}
    # Write-Host "IPs added to Block List in Default Connection Filter Policy." -ForegroundColor Green

    # To view the Connection Filter Policy: Get-HostedConnectionFilterPolicy -Identity "Default"
    ```

**13. MS.EXO.13.1v1: Mailbox Auditing**

*   **Control Description:** Mailbox auditing SHALL be enabled.
*   **Rationale:** Provides logs for investigating compromised accounts and illicit access.
*   **Implementation Instructions (from document):** Via Exchange Online PowerShell.
*   **Suggested Remediation (PowerShell):**

    ```powershell
    # Connect to Exchange Online PowerShell (if not already connected)
    # Import-Module ExchangeOnlineManagement
    # Connect-ExchangeOnline

    # Get organization configuration
    $OrgConfig = Get-OrganizationConfig

    # Check if mailbox auditing is disabled
    if ($OrgConfig.AuditDisabled -eq $true) {
        Set-OrganizationConfig -AuditDisabled $false
        Write-Host "Mailbox auditing has been enabled for the organization." -ForegroundColor Green
    } else {
        Write-Host "Mailbox auditing is already enabled for the organization." -ForegroundColor Yellow
    }

    # To check current status: Get-OrganizationConfig | Format-List AuditDisabled
    ```

**14. MS.EXO.14.1v2, MS.EXO.14.2v1, MS.EXO.14.3v1, MS.EXO.14.4v1: Inbound Anti-Spam Protections**

*   **Control Description:**
    *   MS.EXO.14.1v2: A spam filter SHALL be enabled.
    *   MS.EXO.14.2v1: Spam and high confidence spam SHALL be moved to junk/quarantine.
    *   MS.EXO.14.3v1: Allowed domains SHALL NOT be added to inbound anti-spam policies.
    *   MS.EXO.14.4v1: Third-party solutions SHOULD offer comparable services.
*   **Rationale:** Reduces spam, junk mail, and potential phishing threats.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Defender Preset Security Policies and Exchange Online Protection.
*   **Suggested Remediation (PowerShell - Security & Compliance PowerShell / Defender PowerShell):**

    *   **PowerShell (Security & Compliance PowerShell - Anti-Spam Policies):** You can configure anti-spam policies in Exchange Online Protection using Security & Compliance PowerShell.

        ```powershell
        # Connect to Security & Compliance PowerShell (if not already connected)
        # Connect-IPPSSession

        $policyName = "Anti-Spam Policy - Standard Protection"

        if (!(Get-HostedContentFilterPolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
            New-HostedContentFilterPolicy -Name $policyName -RecommendedPolicyType Standard # Or Strict

            # Ensure spam and high confidence spam are moved to Junk Email or Quarantine (default is usually Junk Email)
            # You can adjust SpamAction and HighConfidenceSpamAction to "Quarantine" if needed instead of "MoveToJunkEmail"
            # Set-HostedContentFilterPolicy -Identity $policyName -SpamAction Quarantine -HighConfidenceSpamAction Quarantine

            # Check and clear allowed domains (AllowedSendersDomains) - important for MS.EXO.14.3v1
            if ($policy.AllowedSendersDomains -ne $null -and $policy.AllowedSendersDomains.Count -gt 0) {
                Set-HostedContentFilterPolicy -Identity $policyName -AllowedSendersDomains @()
                Write-Host "Allowed Senders Domains cleared in Anti-Spam Policy '$policyName'." -ForegroundColor Green
            } else {
                Write-Host "Allowed Senders Domains is already empty in Anti-Spam Policy '$policyName'." -ForegroundColor Yellow
            }


            Write-Host "Anti-Spam Policy '$policyName' created with standard protection." -ForegroundColor Green
        } else {
            Write-Host "Anti-Spam Policy '$policyName' already exists. Consider reviewing and updating its settings." -ForegroundColor Yellow
            # To modify existing policies: Get-HostedContentFilterPolicy, Set-HostedContentFilterPolicy
        }

        # To list anti-spam policies: Get-HostedContentFilterPolicy
        ```
    *   **PowerShell (Defender PowerShell - Preset Security Policies):** Using Preset Security Policies (Standard or Strict) in Defender for Office 365 also automatically enables and configures anti-spam protection, including spam filtering and actions. Preset policies are often the easiest way to implement baseline spam protection.

**15. MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1: Link Protection (Safe Links)**

*   **Control Description:**
    *   MS.EXO.15.1v1: URL comparison with a block-list SHOULD be enabled.
    *   MS.EXO.15.2v1: Direct download links SHOULD be scanned for malware.
    *   MS.EXO.15.3v1: User click tracking SHOULD be enabled.
*   **Rationale:** Protects against malicious links in emails.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Defender Preset Security Policies and Safe Links in Defender for Office 365.
*   **Suggested Remediation (PowerShell - Defender PowerShell):**

    *   **PowerShell (Defender PowerShell - Safe Links Policies):** Defender for Office 365 Safe Links policies provide URL blocklist comparison, real-time scanning of download links, and user click tracking. You can manage these using Defender PowerShell.

        ```powershell
        # Connect to Microsoft Defender PowerShell (if not already connected)
        # Connect-MsolService # If needed for older modules
        # Import-Module Microsoft.Online.SharePoint.PowerShell # If needed for older modules
        # Connect-PnPOnline # If needed for PnP PowerShell

        $policyName = "Safe Links Policy - Enhanced Protection"

        if (!(Get-SafeLinksPolicy -Identity $policyName -ErrorAction SilentlyContinue)) {
            New-SafeLinksPolicy -Name $policyName -RecommendedPolicyType Standard # Or Strict

            # Ensure Safe Links protections are enabled (often enabled by default in Standard/Strict preset policies)
            # You can explicitly set settings if needed - refer to Get-SafeLinksPolicy and Set-SafeLinksPolicy for detailed options
            # Example - Enable click tracking (likely enabled by default in preset, but to be explicit):
            # Set-SafeLinksPolicy -Identity $policyName -IsOfficeClickProtectionEnabled $true -IsClickThroughAllowed $false # Example - disable click-through for stricter control

            Write-Host "Safe Links Policy '$policyName' created with enhanced link protection." -ForegroundColor Green
        } else {
            Write-Host "Safe Links Policy '$policyName' already exists. Consider reviewing and updating its settings." -ForegroundColor Yellow
            # To modify existing policies: Get-SafeLinksPolicy, Set-SafeLinksPolicy
        }

        # To list Safe Links policies: Get-SafeLinksPolicy
        ```
    *   **PowerShell (Defender PowerShell - Preset Security Policies):** Preset Security Policies (Standard or Strict) in Defender for Office 365 are the easiest way to enable comprehensive Safe Links protection, including URL scanning, download link scanning, and click tracking.

**16. MS.EXO.16.1v1, MS.EXO.16.2v1: Alerts**

*   **Control Description:**
    *   MS.EXO.16.1v1: At a minimum, specific alerts SHALL be enabled (list provided in document).
    *   MS.EXO.16.2v1: Alerts SHOULD be sent to a monitored address or SIEM.
*   **Rationale:** Provides notifications for suspicious activities and potential security incidents.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft 365 alert policies and Defender for Office 365 baseline.
*   **Suggested Remediation (PowerShell - Security & Compliance PowerShell / Microsoft Graph API):**

    *   **PowerShell (Security & Compliance PowerShell - Alert Policies):** You can manage alert policies using Security & Compliance PowerShell.

        ```powershell
        # Connect to Security & Compliance PowerShell (if not already connected)
        # Connect-IPPSSession

        # Example - Enable "Suspicious email sending patterns detected" alert
        $alertName = "Suspicious email sending patterns detected"

        $alertPolicy = Get- রক্ষা-ActivityAlert -Identity "$alertName" -ErrorAction SilentlyContinue
        if ($alertPolicy) {
            if ($alertPolicy.State -ne "Enabled") {
                Set-ActivityAlert -Identity "$alertName" -State Enabled
                Write-Host "Alert '$alertName' enabled." -ForegroundColor Green
            } else {
                Write-Host "Alert '$alertName' is already enabled." -ForegroundColor Yellow
            }
            # To configure notification recipients (email addresses) - you'd use Set-ActivityAlert and the -NotificationRecipients parameter.
            # Example: Set-ActivityAlert -Identity "$alertName" -NotificationRecipients "<EMAIL>", "<EMAIL>"
        } else {
            Write-Warning "Alert policy '$alertName' not found. You might need to create it if it's a custom alert, or check the name if it's a built-in alert."
            # For creating custom alerts: New-ActivityAlert (requires defining conditions, actions, etc.)
        }

        # Repeat for other required alerts (Suspicious Connector Activity, Suspicious Email Forwarding Activity, etc.) based on MS.EXO.16.1v1 list.
        # To list all alert policies: Get-ActivityAlert
        ```
    *   **API (Microsoft Graph API - Security API):**  Microsoft Graph API (Security API endpoint `/beta/security/alerts_v2`) also provides access to security alerts and alert policies. You can use Graph API to query alerts, manage alert policies, and integrate with SIEM systems. Refer to Microsoft Graph Security API documentation for details.

        *   **SIEM Integration (PowerShell/API):** For sending alerts to a SIEM, you would typically configure a webhook or use a SIEM connector (if available for your SIEM and M365).  Microsoft Graph Security API is often used for SIEM integration. PowerShell can be used to configure webhooks or connectors if APIs are available for your SIEM and M365 integration.

**17. MS.EXO.17.1v1, MS.EXO.17.2v1, MS.EXO.17.3v1: Audit Logging**

*   **Control Description:**
    *   MS.EXO.17.1v1: Microsoft Purview Audit (Standard) logging SHALL be enabled.
    *   MS.EXO.17.2v1: Microsoft Purview Audit (Premium) logging SHALL be enabled.
    *   MS.EXO.17.3v1: Audit logs SHALL be maintained for at least OMB M-21-31 duration.
*   **Rationale:** Essential for incident response, threat detection, and compliance.
*   **Implementation Instructions (from document):** General guidance, with reference to Microsoft Purview compliance portal and Defender for Office 365 baseline.
*   **Suggested Remediation (PowerShell - Security & Compliance PowerShell):**

    *   **PowerShell (Security & Compliance PowerShell - Audit Configuration):**

        ```powershell
        # Connect to Security & Compliance PowerShell (if not already connected)
        # Connect-IPPSSession

        # Check if Unified Audit Log is enabled (Microsoft Purview Audit Standard - should be enabled by default)
        $AuditConfig = Get-OrganizationConfig | Format-List UnifiedAuditLogEnabled
        if ($AuditConfig.UnifiedAuditLogEnabled -ne "True") {
            Write-Warning "Unified Audit Log (Purview Audit Standard) is NOT enabled. It should be enabled by default. Investigate in Compliance Portal if disabled."
            # Enabling Unified Audit Log directly via PowerShell might be less common now as it's usually default.
            # In older environments, you might have used Enable-OrganizationCustomization, but this is generally not needed for audit log enabling in modern tenants.
        } else {
            Write-Host "Unified Audit Log (Purview Audit Standard) is enabled." -ForegroundColor Green
        }

        # For Purview Audit (Premium), licensing is the primary requirement. After licensing, "Premium" features are generally enabled.
        # PowerShell can be used to verify if Audit (Premium) features are active (though direct "enablement" via PowerShell might be less common, it's more about license assignment).
        # Example - Check if AdvancedAuditLogEnabled is true (indicative of Audit Premium features - might depend on specific feature set and licensing):
        # $AdvancedAuditConfig = Get-OrganizationConfig | Format-List AdvancedAuditLogEnabled
        # if ($AdvancedAuditConfig.AdvancedAuditLogEnabled -eq "True") { ... }

        # For Audit Log Retention (MS.EXO.17.3v1), you need to create Audit Log Retention Policies in Purview Compliance Portal UI or using Purview Compliance PowerShell cmdlets.
        # Example - Create a Retention Policy for Exchange Online audit logs for longer than default (e.g., 1 year - adjust duration as needed for OMB M-21-31):
        # (Note: Requires Purview Audit (Premium) or appropriate licensing for custom retention policies)

        $retentionPolicyName = "Audit Log Retention - Exchange Online - 1 Year"
        $retentionDurationDays = 365 # 1 year
        $logTypes = "Exchange" # Specify log types to retain

        if (!(Get-AuditLogRetentionPolicy -Identity $retentionPolicyName -ErrorAction SilentlyContinue)) {
            New-AuditLogRetentionPolicy -Name $retentionPolicyName -RetentionDuration Days($retentionDurationDays) -RecordTypes $logTypes
            Write-Host "Audit Log Retention Policy '$retentionPolicyName' created for Exchange Online logs for $($retentionDurationDays) days." -ForegroundColor Green
        } else {
            Write-Host "Audit Log Retention Policy '$retentionPolicyName' already exists. Consider reviewing and updating its settings." -ForegroundColor Yellow
            # To modify policies: Get-AuditLogRetentionPolicy, Set-AuditLogRetentionPolicy
        }

        # To list audit log retention policies: Get-AuditLogRetentionPolicy
        ```

**Important Notes:**

*   **PowerShell Modules:** Ensure you have the necessary PowerShell modules installed and connected for Exchange Online, Security & Compliance, and Defender for Office 365 as needed. (`ExchangeOnlineManagement`, `Microsoft Purview Compliance PowerShell`, potentially Defender PowerShell modules depending on the specific cmdlets used -  Defender cmdlets can sometimes be part of ExchangeOnlineManagement or require separate modules).
*   **Permissions:**  You need to have the appropriate administrative roles in Microsoft 365 to run these PowerShell commands (e.g., Global Administrator, Exchange Administrator, Security Administrator, Compliance Administrator).
*   **Testing:** Always test PowerShell scripts in a non-production or test environment first before applying them to your production environment.
*   **API Usage:** For Graph API, you'll need to register an application in Azure AD and grant it the necessary API permissions. API usage is generally more complex than PowerShell cmdlets.
*   **Microsoft Documentation:**  Refer to the official Microsoft documentation for the most up-to-date information on PowerShell cmdlets, API endpoints, parameters, and best practices.  Cmdlet names and parameters can change over time.
*   **Preset Security Policies (Defender):** For many security features (Anti-malware, Anti-spam, Anti-phishing, Safe Links, Attachment Filtering), using Microsoft Defender for Office 365's **Preset Security Policies (Standard or Strict)** is often the easiest and most recommended way to implement a strong baseline configuration. While PowerShell allows for granular control, Preset Policies provide a pre-configured set of settings that align with security best practices. You can manage Preset Security Policies via the Microsoft 365 Defender portal, and to some extent via Defender PowerShell (though granular customization within preset policies might be limited via PowerShell, UI is often preferred for Preset Policy management).
*   **DNS Records (SPF, DKIM, DMARC):** Remember that SPF, DKIM, and DMARC involve DNS record management, which is typically outside of Exchange Online PowerShell. The PowerShell suggestions for these controls primarily focus on *verification* and provide *guidance* on using DNS provider APIs for programmatic management (which is provider-specific).