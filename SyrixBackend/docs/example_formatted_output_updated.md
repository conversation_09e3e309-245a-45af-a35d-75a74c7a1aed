**MS.DEFENDER.1.1v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator` **Description:** Remediates Defender Preset Security Policies policies. **Policy IDs:** MS.DEFENDER.1.1v1 **Parameters**
* **policyType (PolicyType) (Required)**
  * **Possible Values:** STANDARD (Standard), STRICT (Strict)
  * Fields: **Field Type Description** policyType String References the policy or configuration to use. *Possible values:* STANDARD (Standard), STRICT (Strict)

**Parameters Summary**
This policy requires the user to select a policyType (STANDARD or STRICT).

**Required Parameters**
* **policyType** - Select the policyType from these options:
  * STANDARD (Standard): Applies standard level of security protection
  * STRICT (Strict): Applies strict level of security protection

---

**MS.DEFENDER.1.4v1** **Class:** `io.syrix.products.microsoft.defender.remediation.DefenderEOPStrictProtectionRemediator` **Description:** Remediates Defender EOP Strict Protection policies. **Policy IDs:** MS.DEFENDER.1.4v1 **Parameters**
* **excludedUsers (List<String>) (Required)**
  * Fields: **Field Type Description** excludedUsers List A collection of excludedUsers elements.

**Parameters Summary**
This policy requires the user to provide excludedUsers.

**Required Parameters**
* **excludedUsers** - Provide the excludedUsers

---

**MS.EXO.6.2v1** **Class:** `io.syrix.products.microsoft.exo.remediation.DisableMailboxArchiveRemediator` **Description:** Remediates Exchange Online Mailbox Archive settings. **Policy IDs:** MS.EXO.6.2v1 **Parameters**
* None

**Parameters Summary**
This policy requires no parameters.

**Required Parameters**
* None

---