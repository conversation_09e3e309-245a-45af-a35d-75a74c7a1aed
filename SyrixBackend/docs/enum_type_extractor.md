# Enum Type Extractor

This document describes the `EnumTypeExtractor` utility class, which was created to improve the handling of enum parameters in the Syrix documentation generator.

## Overview

The `EnumTypeExtractor` class helps extract enum values and their human-readable descriptions from Java enum classes. This information is used to generate better documentation for parameters that are enums, ensuring that UX designers have a complete understanding of the possible values.

## Key Features

### 1. Intelligent Enum Value Extraction

The class uses reflection to extract enum values and attempts to find human-readable descriptions for each value:

```java
public static List<String> extractEnumValues(String enumClassName) {
    // Uses reflection to inspect the enum class
    // Extracts enum constants and their display names
    // Returns formatted values like "ENUM_NAME (Display Name)"
}
```

### 2. Smart Enum Class Discovery

The new helper method can find enum classes even when only the short name is known:

```java
public static List<String> getEnumValuesByShortName(String enumShortName, String contextClassName) {
    // First tries using the context class's package
    // Then searches common packages in the Syrix codebase
    // Returns the formatted enum values if found
}
```

### 3. Specialized Handling for Known Enums

For well-known enum types like `PolicyType` in MS.DEFENDER.1.1v1, the class provides specialized methods:

```java
public static List<String> getDefenderPolicyTypeValues() {
    // First tries to load the actual enum class
    // Provides special handling for STANDARD and STRICT values
    // Falls back to hardcoded values if needed
}
```

### 4. Performance Optimization with Caching

To improve performance, the class caches extracted enum values:

```java
private static final Map<String, List<String>> enumValuesCache = new HashMap<>();
```

This ensures that repeated calls to extract values from the same enum class are fast and efficient.

## Integration with Documentation Generator

The `EnumTypeExtractor` is integrated with the `PolicyRemediatorDocumentationGenerator` to automatically detect and extract enum values for parameters:

```java
// In processSpecialCaseRemediators method
List<String> enumValues = EnumTypeExtractor.getEnumValuesByShortName(param.getType(), info.getClassName());
```

This provides a more robust solution than hardcoding values for specific enum types.

## Testing

The class is thoroughly tested to ensure it works correctly:

- `testGetDefenderPolicyTypeValues`: Tests the special helper method for MS.DEFENDER.1.1v1 PolicyType
- `testGetEnumValuesByShortName`: Tests finding an enum by short name
- `testCachingBehavior`: Tests that caching works correctly
- `testHandlingOfNonExistentEnum`: Tests behavior with non-existent enums

## Future Enhancements

1. Add support for more specialized enum types as needed
2. Enhance display name detection for complex enum patterns
3. Add additional context information for enum values
4. Integrate with Java annotation processing for even better metadata extraction

## Example Output

For the MS.DEFENDER.1.1v1 PolicyType parameter, the extractor produces:

```
["STANDARD (Standard)", "STRICT (Strict)"]
```

This ensures that the documentation shows both the technical enum constant and its human-readable description.
