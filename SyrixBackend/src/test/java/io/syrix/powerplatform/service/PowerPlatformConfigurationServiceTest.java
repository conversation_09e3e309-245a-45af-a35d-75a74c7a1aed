package io.syrix.powerplatform.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.utils.TestUtils;
import io.syrix.products.microsoft.powerplatform.PowerPlatformClient;
import io.syrix.products.microsoft.powerplatform.PowerPlatformConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Disabled("Temporarily disabled as requested")
class PowerPlatformConfigurationServiceTest {

	@Mock
	private MicrosoftGraphClient graphClient;
	private PowerPlatformClient ppClient;

	@Captor
	private ArgumentCaptor<GraphRequest> requestCaptor;

	private PowerPlatformConfigurationService service;
	private ObjectMapper objectMapper;

	@BeforeEach
	void setUp() {
		objectMapper = new ObjectMapper();
		service = new PowerPlatformConfigurationService(graphClient, ppClient);
	}

	@Nested
	@DisplayName("Power Platform Configuration Export Tests")
	class ConfigurationExportTests {

		@Test
		@DisplayName("Should successfully export Power Platform configuration")
		void shouldExportConfiguration() throws Exception {
			// Given
			JsonNode envSettings = TestUtils.loadTestData("powerplatform/environment-creation-settings.json");
			JsonNode dlpPolicies = TestUtils.loadTestData("powerplatform/dlp-policies.json");
			JsonNode envList = TestUtils.loadTestData("powerplatform/environment-list.json");
			JsonNode isolationSettings = TestUtils.loadTestData("powerplatform/tenant-isolation-settings.json");

			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(envSettings))
					.thenReturn(CompletableFuture.completedFuture(dlpPolicies))
					.thenReturn(CompletableFuture.completedFuture(isolationSettings))
					.thenReturn(CompletableFuture.completedFuture(envList));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result)
					.isNotNull()
					.satisfies(r -> {
						assertThat(r.getData()).isNotNull();
						assertThat(r.getData().get("environment_creation")).isNotNull();
						assertThat(r.getData().get("dlp_policies")).isNotNull();
						assertThat(r.getData().get("tenant_isolation")).isNotNull();
						assertThat(r.getData().get("environment_list")).isNotNull();
					});

			verify(graphClient, atLeast(4)).makeGraphRequest(requestCaptor.capture());
			List<GraphRequest> requests = requestCaptor.getAllValues();

			assertThat(requests)
					.extracting(GraphRequest::getEndpoint)
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/powerPlatform/settings/environmentCreation"))
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/powerPlatform/policies/dlp"))
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/powerPlatform/settings/tenantIsolation"))
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/powerPlatform/environments"));
		}

		@Test
		@DisplayName("Should handle export failures with retries")
		void shouldHandleExportFailuresWithRetries() {
			// Given
			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.failedFuture(
							new RuntimeException("API Error")
					));

			// When & Then
			assertThatThrownBy(() -> service.exportConfiguration())
					.isInstanceOf(ConfigurationExportException.class)
					.hasMessageContaining("Power Platform configuration export failed");

			verify(graphClient, atLeast(3)).makeGraphRequest(any(GraphRequest.class));
		}
	}

	@Nested
	@DisplayName("Individual Configuration Tests")
	class IndividualConfigTests {

		@Test
		@DisplayName("Should correctly fetch environment creation settings")
		void shouldFetchEnvironmentCreationSettings() throws Exception {
			// Given
			JsonNode envSettings = TestUtils.loadTestData("powerplatform/environment-creation-settings.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/powerPlatform/settings/environmentCreation"))))
					.thenReturn(CompletableFuture.completedFuture(envSettings));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("environment_creation"))
					.isNotNull()
					.satisfies(settings -> {
						JsonNode settingData = (JsonNode) settings;
						assertThat(settingData.has("isCreationAllowed")).isTrue();
						assertThat(settingData.has("restrictToDefaultTenant")).isTrue();
						assertThat(settingData.has("allowedDataLocations")).isTrue();
					});
		}

		@Test
		@DisplayName("Should correctly fetch DLP policies")
		void shouldFetchDLPPolicies() throws Exception {
			// Given
			JsonNode dlpPolicies = TestUtils.loadTestData("powerplatform/dlp-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/powerPlatform/policies/dlp"))))
					.thenReturn(CompletableFuture.completedFuture(dlpPolicies));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("dlp_policies"))
					.isNotNull()
					.satisfies(policies -> {
						JsonNode policiesData = (JsonNode) policies;
						assertThat(policiesData.isArray()).isTrue();
						if (policiesData.size() > 0) {
							assertThat(policiesData.get(0).has("displayName")).isTrue();
							assertThat(policiesData.get(0).has("environments")).isTrue();
							assertThat(policiesData.get(0).has("connectorGroups")).isTrue();
						}
					});
		}

		@Test
		@DisplayName("Should correctly fetch tenant isolation settings")
		void shouldFetchTenantIsolationSettings() throws Exception {
			// Given
			JsonNode isolationSettings = TestUtils.loadTestData("powerplatform/tenant-isolation-settings.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/powerPlatform/settings/tenantIsolation"))))
					.thenReturn(CompletableFuture.completedFuture(isolationSettings));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("tenant_isolation"))
					.isNotNull()
					.satisfies(settings -> {
						JsonNode settingsData = (JsonNode) settings;
						assertThat(settingsData.has("isIsolated")).isTrue();
						assertThat(settingsData.has("allowedTenants")).isTrue();
						assertThat(settingsData.has("blockExternalTenants")).isTrue();
					});
		}

		@Test
		@DisplayName("Should correctly fetch environment list")
		void shouldFetchEnvironmentList() throws Exception {
			// Given
			JsonNode envList = TestUtils.loadTestData("powerplatform/environment-list.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/powerPlatform/environments"))))
					.thenReturn(CompletableFuture.completedFuture(envList));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("environment_list"))
					.isNotNull()
					.satisfies(environments -> {
						JsonNode environmentsData = (JsonNode) environments;
						assertThat(environmentsData.isArray()).isTrue();
						if (environmentsData.size() > 0) {
							assertThat(environmentsData.get(0).has("name")).isTrue();
							assertThat(environmentsData.get(0).has("displayName")).isTrue();
							assertThat(environmentsData.get(0).has("properties")).isTrue();
						}
					});
		}
	}
}