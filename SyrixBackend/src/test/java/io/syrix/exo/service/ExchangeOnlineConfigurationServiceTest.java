package io.syrix.exo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.dns.DnsService;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.common.utils.TestUtils;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;
@Disabled //TODO Slava
@ExtendWith(MockitoExtension.class)
class ExchangeOnlineConfigurationServiceTest {

	@Mock
	private MicrosoftGraphClient graphClient;
	
	@Mock
	private PowerShellClient powershellClient;

	@Mock
	private DnsService dnsService;

	@Mock
	private MetricsCollector metrics;

	@Captor
	private ArgumentCaptor<GraphRequest> requestCaptor;

	private ExchangeOnlineConfigurationService service;
	private ObjectMapper objectMapper;

	@BeforeEach
	void setUp() {
		objectMapper = new ObjectMapper();
		service = new ExchangeOnlineConfigurationService(graphClient, powershellClient, objectMapper, metrics, dnsService);

		// Setup default DNS responses using lists of strings
		when(dnsService.lookupTxtRecords(anyString()))
				.thenAnswer(invocation -> {
					String domain = invocation.getArgument(0);
					if (domain.contains("._domainkey.")) {
						return List.of("v=DKIM1; k=rsa; p=MIIBIjANB...");
					} else if (domain.startsWith("_dmarc.")) {
						return List.of("v=DMARC1; p=reject; rua=mailto:<EMAIL>");
					} else {
						return List.of("v=spf1 include:spf.protection.outlook.com -all");
					}
				});
	}

	@Test
	@DisplayName("Should verify DNS records lookup")
	void shouldVerifyDNSRecordsLookup() throws Exception {
		// Given
		JsonNode domainsResponse = objectMapper.createObjectNode()
				.set("value", objectMapper.valueToTree(Arrays.asList(
						objectMapper.createObjectNode().put("id", "example.com"),
						objectMapper.createObjectNode().put("id", "test.com")
				)));

		when(graphClient.makeGraphRequest(argThat(req ->
				req.getEndpoint().contains("/domains"))))
				.thenReturn(CompletableFuture.completedFuture(domainsResponse));

		// When
		ConfigurationResult result = service.exportConfiguration();

		// Then
		verify(dnsService, atLeastOnce()).lookupTxtRecords(argThat(domain ->
				!domain.contains("._domainkey.") && !domain.startsWith("_dmarc."))); // SPF
		verify(dnsService, atLeastOnce()).lookupTxtRecords(argThat(domain ->
				domain.contains("._domainkey."))); // DKIM
		verify(dnsService, atLeastOnce()).lookupTxtRecords(argThat(domain ->
				domain.startsWith("_dmarc."))); // DMARC

		assertThat(result.getData())
				.isNotNull()
				.satisfies(data -> {
					JsonNode dataNode = (JsonNode) data;
					assertThat(dataNode.has("spf_records")).isTrue();
					assertThat(dataNode.has("dkim_records")).isTrue();
					assertThat(dataNode.has("dmarc_records")).isTrue();
				});
	}


	@Nested
	@DisplayName("Exchange Online Configuration Export Tests")
	class ConfigurationExportTests {

		@Test
		@DisplayName("Should successfully export Exchange Online configuration")
		void shouldExportConfiguration() throws Exception {
			// Given
			JsonNode remoteDomainData = TestUtils.loadTestData("exo/remote-domains.json");
			JsonNode orgConfig = TestUtils.loadTestData("exo/organization-config.json");

			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(remoteDomainData))
					.thenReturn(CompletableFuture.completedFuture(orgConfig));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result)
					.isNotNull()
					.satisfies(r -> {
						assertThat(r.getData()).isNotNull();
						assertThat(r.getData().get("remote_domains")).isNotNull();
						assertThat(r.getData().get("org_config")).isNotNull();
					});

			verify(graphClient, atLeast(2)).makeGraphRequest(requestCaptor.capture());
			List<GraphRequest> requests = requestCaptor.getAllValues();

			assertThat(requests)
					.extracting(GraphRequest::getEndpoint)
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/remoteDomains"))
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/organizationConfig"));
		}

		@Test
		@DisplayName("Should handle export failures with retries")
		void shouldHandleExportFailuresWithRetries() {
			// Given
			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.failedFuture(
							new RuntimeException("API Error")
					));

			// When & Then
			assertThatThrownBy(() -> service.exportConfiguration())
					.isInstanceOf(ConfigurationExportException.class)
					.hasMessageContaining("Exchange Online configuration export failed");

			verify(graphClient, atLeast(3)).makeGraphRequest(any(GraphRequest.class));
		}

		@Test
		@DisplayName("Should include all required configuration components")
		void shouldIncludeAllComponents() throws Exception {
			// Given
			mockAllConfigResponses();

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData())
					.isNotNull()
					.satisfies(data -> {
						JsonNode dataNode = (JsonNode) data;
						assertThat(dataNode.has("remote_domains")).isTrue();
						assertThat(dataNode.has("spf_records")).isTrue();
						assertThat(dataNode.has("dkim_config")).isTrue();
						assertThat(dataNode.has("dmarc_records")).isTrue();
						assertThat(dataNode.has("transport_config")).isTrue();
						assertThat(dataNode.has("sharing_policy")).isTrue();
						assertThat(dataNode.has("transport_rule")).isTrue();
						assertThat(dataNode.has("conn_filter")).isTrue();
						assertThat(dataNode.has("org_config")).isTrue();
					});

			verify(graphClient, times(9)).makeGraphRequest(requestCaptor.capture());
		}
	}

	@Nested
	@DisplayName("Individual Configuration Tests")
	class IndividualConfigTests {

		@Test
		@DisplayName("Should correctly fetch remote domains")
		void shouldFetchRemoteDomains() throws Exception {
			// Given
			JsonNode remoteDomains = TestUtils.loadTestData("exo/remote-domains.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/remoteDomains"))))
					.thenReturn(CompletableFuture.completedFuture(remoteDomains));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("remote_domains"))
					.isNotNull()
					.satisfies(domains -> {
						JsonNode domainsNode = (JsonNode) domains;
						assertThat(domainsNode.isArray()).isTrue();
						if (domainsNode.size() > 0) {
							JsonNode firstDomain = domainsNode.get(0);
							assertThat(firstDomain.has("domainName")).isTrue();
							assertThat(firstDomain.has("allowedOOFType")).isTrue();
						}
					});
		}

		@Test
		@DisplayName("Should correctly fetch transport config")
		void shouldFetchTransportConfig() throws Exception {
			// Given
			JsonNode transportConfig = TestUtils.loadTestData("exo/transport-config.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/transportConfig"))))
					.thenReturn(CompletableFuture.completedFuture(transportConfig));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("transport_config"))
					.isNotNull()
					.satisfies(config -> {
						JsonNode configNode = (JsonNode) config;
						assertThat(configNode.has("maxReceiveSize")).isTrue();
						assertThat(configNode.has("maxSendSize")).isTrue();
					});
		}

		@Test
		@DisplayName("Should fetch and validate DKIM configuration")
		void shouldFetchDkimConfig() throws Exception {
			// Given
			JsonNode dkimConfig = TestUtils.loadTestData("exo/dkim-config.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/dkimSigningConfig"))))
					.thenReturn(CompletableFuture.completedFuture(dkimConfig));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("dkim_config"))
					.isNotNull()
					.satisfies(config -> {
						JsonNode configNode = (JsonNode) config;
						assertThat(configNode.has("enabled")).isTrue();
					});
		}
	}

	private void mockAllConfigResponses() {
		JsonNode mockResponse = TestUtils.createMockJsonResponse();
		when(graphClient.makeGraphRequest(any(GraphRequest.class)))
				.thenReturn(CompletableFuture.completedFuture(mockResponse));

		// Mock DNS lookups
		when(dnsService.lookupTxtRecords(anyString()))
				.thenAnswer(invocation -> {
					String domain = invocation.getArgument(0);
					if (domain.contains("._domainkey.")) {
						return List.of("v=DKIM1; k=rsa; p=MIIBIjANB...");
					} else if (domain.startsWith("_dmarc.")) {
						return List.of("v=DMARC1; p=reject;");
					} else {
						return List.of("v=spf1 include:spf.protection.outlook.com -all");
					}
				});
	}

	@Nested
	@DisplayName("Role Assignment Policy Tests")
	class RoleAssignmentPolicyTests {

		@Test
		@DisplayName("Should retrieve role assignment policies for add-in compliance")
		void shouldRetrieveRoleAssignmentPoliciesForAddInCompliance() {
			// Given - Mock role discovery response (Get-ManagementRole)
			JsonNode mockManagementRoles = objectMapper.createArrayNode()
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyCustomApps")
					.put("Name", "My Custom Apps"))
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyMarketplaceApps")
					.put("Name", "My Marketplace Apps"))
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyReadWriteMailboxApps")
					.put("Name", "My ReadWriteMailbox Apps"));

			// Mock role assignment policies response (Get-RoleAssignmentPolicy)
			JsonNode mockRoleAssignmentPolicies = objectMapper.createArrayNode()
				.add(objectMapper.createObjectNode()
					.put("Identity", "Default Role Assignment Policy")
					.set("AssignedRoles", objectMapper.createArrayNode()
						.add("MyBaseOptions")
						.add("MyContactInformation")))
				.add(objectMapper.createObjectNode()
					.put("Identity", "Executive Role Assignment Policy")
					.set("AssignedRoles", objectMapper.createArrayNode()
						.add("MyBaseOptions")
						.add("My Custom Apps")));

			// Mock PowerShell calls in order: first Get-ManagementRole, then Get-RoleAssignmentPolicy
			when(powershellClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
				.thenReturn(CompletableFuture.completedFuture(mockManagementRoles))
				.thenReturn(CompletableFuture.completedFuture(mockRoleAssignmentPolicies));

			// When
			JsonNode result = service.getRoleAssignmentPoliciesForAddInCompliance().join();

			// Then - Verify enhanced configuration structure
			assertThat(result).isNotNull();
			assertThat(result.isObject()).isTrue();
			assertThat(result.has("discovered_roles")).isTrue();
			assertThat(result.has("policies")).isTrue();
			
			// Verify discovered roles mapping
			JsonNode discoveredRoles = result.get("discovered_roles");
			assertThat(discoveredRoles.isObject()).isTrue();
			assertThat(discoveredRoles.has("MyCustomApps")).isTrue();
			assertThat(discoveredRoles.get("MyCustomApps").asText()).isEqualTo("My Custom Apps");
			assertThat(discoveredRoles.has("MyMarketplaceApps")).isTrue();
			assertThat(discoveredRoles.get("MyMarketplaceApps").asText()).isEqualTo("My Marketplace Apps");
			assertThat(discoveredRoles.has("MyReadWriteMailboxApps")).isTrue();
			assertThat(discoveredRoles.get("MyReadWriteMailboxApps").asText()).isEqualTo("My ReadWriteMailbox Apps");
			
			// Verify policies array structure
			JsonNode policies = result.get("policies");
			assertThat(policies.isArray()).isTrue();
			assertThat(policies.size()).isEqualTo(2);
			
			// Verify filtered response has lowercase field names (mapped by filterRoleAssignmentPoliciesForRegoValidation)
			JsonNode firstPolicy = policies.get(0);
			assertThat(firstPolicy.has("identity")).isTrue(); // Should be lowercase
			assertThat(firstPolicy.has("assignedRoles")).isTrue(); // Should be camelCase
			
			// Verify both PowerShell commands were called correctly
			verify(powershellClient).executeCmdletCommand(argThat(request -> 
				"Get-ManagementRole".equals(request.getCmdletName())
			));
			verify(powershellClient).executeCmdletCommand(argThat(request -> 
				"Get-RoleAssignmentPolicy".equals(request.getCmdletName())
			));
		}

		@Test
		@DisplayName("Should handle PowerShell failures gracefully")
		void shouldHandlePowerShellFailuresGracefully() {
			// Given - First call (Get-ManagementRole) fails
			when(powershellClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
				.thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell error")));

			// When & Then - Should throw RuntimeException since role discovery is required
			assertThatThrownBy(() -> service.getRoleAssignmentPoliciesForAddInCompliance().join())
				.hasCauseInstanceOf(RuntimeException.class)
				.hasMessageContaining("PowerShell error");
		}

		@Test
		@DisplayName("Should validate role assignment policies contain expected enhanced structure")
		void shouldValidateRoleAssignmentPolicyStructure() {
			// Given - Mock role discovery response (Get-ManagementRole)
			JsonNode mockManagementRoles = objectMapper.createArrayNode()
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyCustomApps")
					.put("Name", "My Custom Apps"))
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyMarketplaceApps")
					.put("Name", "My Marketplace Apps"))
				.add(objectMapper.createObjectNode()
					.put("RoleType", "MyReadWriteMailboxApps")
					.put("Name", "My ReadWriteMailbox Apps"));
			
			// Mock role assignment policies response (Get-RoleAssignmentPolicy) 
			JsonNode mockRoleAssignmentPolicies = objectMapper.createArrayNode()
				.add(objectMapper.createObjectNode()
					.put("Identity", "Default Role Assignment Policy")
					.set("AssignedRoles", objectMapper.createArrayNode()
						.add("MyBaseOptions")
						.add("MyContactInformation")
						.add("My Custom Apps")))
				.add(objectMapper.createObjectNode()
					.put("Identity", "Executive Role Assignment Policy")
					.set("AssignedRoles", objectMapper.createArrayNode()
						.add("MyBaseOptions")
						.add("My Marketplace Apps")));

			// Mock PowerShell calls in order: first Get-ManagementRole, then Get-RoleAssignmentPolicy
			when(powershellClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
				.thenReturn(CompletableFuture.completedFuture(mockManagementRoles))
				.thenReturn(CompletableFuture.completedFuture(mockRoleAssignmentPolicies));

			// When
			JsonNode result = service.getRoleAssignmentPoliciesForAddInCompliance().join();

			// Then - Verify enhanced structure with discovered_roles and policies
			assertThat(result).isNotNull();
			assertThat(result.isObject()).isTrue();
			assertThat(result.has("discovered_roles")).isTrue();
			assertThat(result.has("policies")).isTrue();
			
			// Verify discovered_roles mapping
			JsonNode discoveredRoles = result.get("discovered_roles");
			assertThat(discoveredRoles.isObject()).isTrue();
			assertThat(discoveredRoles.has("MyCustomApps")).isTrue();
			assertThat(discoveredRoles.get("MyCustomApps").asText()).isEqualTo("My Custom Apps");
			assertThat(discoveredRoles.has("MyMarketplaceApps")).isTrue();
			assertThat(discoveredRoles.get("MyMarketplaceApps").asText()).isEqualTo("My Marketplace Apps");
			assertThat(discoveredRoles.has("MyReadWriteMailboxApps")).isTrue();
			assertThat(discoveredRoles.get("MyReadWriteMailboxApps").asText()).isEqualTo("My ReadWriteMailbox Apps");
			
			// Verify policies array structure (should be filtered with lowercase field names)
			JsonNode policies = result.get("policies");
			assertThat(policies.isArray()).isTrue();
			assertThat(policies.size()).isEqualTo(2);
			
			JsonNode firstPolicy = policies.get(0);
			assertThat(firstPolicy.has("identity")).isTrue(); // Filtered to lowercase
			assertThat(firstPolicy.has("assignedRoles")).isTrue(); // Filtered to camelCase
			assertThat(firstPolicy.get("identity").asText()).isEqualTo("Default Role Assignment Policy");
			
			// Verify assigned roles are accessible
			JsonNode assignedRoles = firstPolicy.get("assignedRoles"); // Use camelCase field name
			assertThat(assignedRoles.isArray()).isTrue();
			assertThat(assignedRoles.size()).isEqualTo(3);
		}
	}
}