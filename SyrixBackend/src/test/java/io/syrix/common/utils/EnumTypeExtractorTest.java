package io.syrix.common.utils;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for EnumTypeExtractor.
 */
class EnumTypeExtractorTest {
    
    @Test
    void testGetDefenderPolicyTypeValues() {
        // Test the special helper method for MS.DEFENDER.1.1v1 PolicyType
        List<String> values = EnumTypeExtractor.getDefenderPolicyTypeValues();
        
        // Verify the expected values
        assertNotNull(values, "Values should not be null");
        assertFalse(values.isEmpty(), "Values should not be empty");
        assertEquals(2, values.size(), "Should have exactly 2 values for PolicyType");
        
        // Verify the specific values and their format
        assertTrue(values.contains("STANDARD (Standard)"), "Should contain STANDARD (Standard)");
        assertTrue(values.contains("STRICT (Strict)"), "Should contain STRICT (Strict)");
    }
    
    @Test
    void testGetEnumValuesByShortName() {
        // Test finding an enum by short name using context class
        List<String> values = EnumTypeExtractor.getEnumValuesByShortName(
                "PolicyType", 
                "io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator");
        
        // Verify the results
        assertNotNull(values, "Values should not be null");
        assertFalse(values.isEmpty(), "Values should not be empty");
        
        // In a real test environment with actual classes, there would be values
        // Here we're just testing that the method returns without errors
    }
    
    @Test
    void testCachingBehavior() {
        // Test that caching works by calling the same method multiple times
        long startTime = System.nanoTime();
        List<String> firstCall = EnumTypeExtractor.getDefenderPolicyTypeValues();
        long firstCallTime = System.nanoTime() - startTime;
        
        // Second call should be faster due to caching
        startTime = System.nanoTime();
        List<String> secondCall = EnumTypeExtractor.getDefenderPolicyTypeValues();
        long secondCallTime = System.nanoTime() - startTime;
        
        // Verify the results
        assertEquals(firstCall.size(), secondCall.size(), "Both calls should return same number of values");
        
        // The second call should typically be faster, but this is not guaranteed in all environments
        // So we don't assert on the timing, just log it
        System.out.println("First call time: " + firstCallTime + " ns");
        System.out.println("Second call time: " + secondCallTime + " ns");
    }
    
    @Test
    void testHandlingOfNonExistentEnum() {
        // Test behavior with a non-existent enum
        List<String> values = EnumTypeExtractor.getEnumValuesByShortName(
                "NonExistentEnum", 
                "io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator");
        
        // Verify the method returns an empty list for non-existent enums
        assertNotNull(values, "Values should not be null even for non-existent enum");
        assertTrue(values.isEmpty(), "Values should be empty for non-existent enum");
        
        // Also test with a completely invalid package
        List<String> valuesWithInvalidPackage = EnumTypeExtractor.getEnumValuesByShortName(
                "NonExistentEnum",
                "io.syrix.nonexistent.package.Class");
                
        // This should also return an empty list without throwing exceptions
        assertNotNull(valuesWithInvalidPackage, "Values should not be null for invalid package");
        assertTrue(valuesWithInvalidPackage.isEmpty(), "Values should be empty for invalid package");
    }
}
