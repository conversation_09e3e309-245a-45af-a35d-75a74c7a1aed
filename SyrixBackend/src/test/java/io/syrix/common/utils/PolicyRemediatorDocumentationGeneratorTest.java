package io.syrix.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PolicyRemediatorDocumentationGeneratorTest {
    
    @Test
    void testSpecialCaseRemediatorHandling(@TempDir Path tempDir) {
        // Generate documentation to a temp directory
        int filesGenerated = PolicyRemediatorDocumentationGenerator.generateAllDocumentation(
                tempDir.toString(), "md", List.of());
        
        // Verify a file was generated
        assertEquals(1, filesGenerated, "Should generate 1 file");
        
        try {
            // Read the generated markdown file
            String markdownContent = Files.readString(tempDir.resolve("all_policy_remediators.md"));
            
            // Check for special case handling of MS.DEFENDER.1.1v1 PolicyType parameter
            assertTrue(markdownContent.contains("MS.DEFENDER.1.1v1"), 
                    "Generated documentation should include MS.DEFENDER.1.1v1");
            
            assertTrue(markdownContent.contains("**PolicyType"), 
                    "Generated documentation should include PolicyType parameter");
            
            assertTrue(markdownContent.contains("**Possible Values:** STANDARD (Standard), STRICT (Strict)"), 
                    "Generated documentation should include enum values for PolicyType");
            
            // Check that we're not including both the special case handler and the inline handling
            int occurrencesOfEnumValues = countOccurrences(markdownContent, 
                    "**Possible Values:** STANDARD (Standard), STRICT (Strict)");
            assertEquals(1, occurrencesOfEnumValues, 
                    "Enum values should appear exactly once for the PolicyType parameter");
            
        } catch (Exception e) {
            fail("Failed to read or verify documentation file: " + e.getMessage());
        }
    }
    
    private int countOccurrences(String text, String searchString) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(searchString, index)) != -1) {
            count++;
            index += searchString.length();
        }
        return count;
    }
}
