// src/test/java/com/syrix/common/utils/TestUtils.java
package io.syrix.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * General test utility class for creating mock data and test helpers.
 * Not specific to any particular service or component.
 */
public class TestUtils {
	private static final Logger logger = LoggerFactory.getLogger(TestUtils.class);
	private static final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * Loads test data from a JSON file in the test resources.
	 */
	public static JsonNode loadTestData(String fileName) {
		try (InputStream is = TestUtils.class.getResourceAsStream("/" + fileName)) {
			if (is == null) {
				throw new IllegalArgumentException("Test data file not found: " + fileName);
			}
			String content = new String(is.readAllBytes(), StandardCharsets.UTF_8);
			return objectMapper.readTree(content);
		} catch (IOException e) {
			logger.error("Failed to load test data: {}", fileName, e);
			throw new RuntimeException("Failed to load test data", e);
		}
	}

	/**
	 * Creates a mock JSON response with provided fields.
	 */
	public static JsonNode createMockJsonResponse(Map<String, JsonNode> fields) {
		ObjectNode root = objectMapper.createObjectNode();
		fields.forEach(root::set);
		return root;
	}
	
	/**
	 * Creates a JSON node with a "value" property containing the provided JsonNode.
	 * This is useful for mocking Microsoft Graph API responses which often follow this pattern.
	 */
	public static JsonNode createValueNode(ObjectMapper mapper, JsonNode value) {
		ObjectNode root = mapper.createObjectNode();
		root.set("value", value);
		return root;
	}

	/**
	 * Creates a mock JSON response with default test data.
	 */
	public static JsonNode createMockJsonResponse() {
		ObjectNode valueObject = objectMapper.createObjectNode()
				.put("id", "test-id-" + System.currentTimeMillis())
				.put("displayName", "Test Object")
				.put("description", "Test Description")
				.put("createdDateTime", "2024-01-01T00:00:00Z")
				.put("lastModifiedDateTime", "2024-01-01T00:00:00Z");

		valueObject.put("@odata.context", "https://graph.microsoft.com/beta/$metadata#test");
		valueObject.put("@odata.type", "#microsoft.graph.testObject");
		valueObject.put("status", "enabled");

		ObjectNode settings = objectMapper.createObjectNode()
				.put("isEnabled", true)
				.put("allowExternalAccess", true)
				.put("defaultSetting", "standard");
		valueObject.set("settings", settings);

		valueObject.set("members", objectMapper.createArrayNode()
				.add(createTestMember("1"))
				.add(createTestMember("2")));

		valueObject.put("@odata.count", 2);
		return valueObject;
	}

	/**
	 * Creates a mock error response.
	 */
	public static JsonNode createMockErrorResponse(String code, String message) {
		ObjectNode error = objectMapper.createObjectNode();
		error.set("error", objectMapper.createObjectNode()
				.put("code", code)
				.put("message", message)
				.put("requestId", "test-request-" + System.currentTimeMillis()));
		return error;
	}

	/**
	 * Creates a mock paginated response.
	 */
	public static JsonNode createMockPaginatedResponse(JsonNode[] items, String nextLink) {
		ObjectNode response = objectMapper.createObjectNode();
		response.put("@odata.context", "https://graph.microsoft.com/beta/$metadata#test");
		response.putArray("value").addAll(List.of(items));

		if (nextLink != null) {
			response.put("@odata.nextLink", nextLink);
		}

		response.put("@odata.count", items.length);
		return response;
	}

	private static ObjectNode createTestMember(String suffix) {
		return objectMapper.createObjectNode()
				.put("id", "member-" + suffix)
				.put("displayName", "Test Member " + suffix)
				.put("email", "member" + suffix + "@test.com");
	}

	/**
	 * Creates a test access token.
	 */
	public static String createTestAccessToken() {
		return "test-access-token-" + System.currentTimeMillis();
	}
}