package io.syrix.common.utils;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that the documentation format matches the example.
 */
@Disabled
public class ExampleFormatTest {
    
    @Test
    void testGeneratedFormatMatchesExample(@TempDir Path tempDir) throws Exception {
        // 1. First generate the documentation to a temp directory
        int filesGenerated = PolicyRemediatorDocumentationGenerator.generateAllDocumentation(
                tempDir.toString(), "md", List.of());
        
        // 2. Read the generated documentation
        String generatedDocs = Files.readString(tempDir.resolve("all_policy_remediators.md"));
        
        // 3. Extract the MS.DEFENDER.1.1v1 section from the generated docs
        String defenderSection = extractPolicySection(generatedDocs, "MS.DEFENDER.1.1v1");
        assertNotNull(defenderSection, "MS.DEFENDER.1.1v1 section should exist in generated docs");
        
        // 4. Read the example format
        String exampleFormat = Files.readString(Paths.get(
                "/Users/<USER>/Documents/Development/private/syrix/SyrixBackend/docs/example_documentation_output.md"));
        
        // 5. Extract the same section from the example
        String exampleSection = extractPolicySection(exampleFormat, "MS.DEFENDER.1.1v1");
        assertNotNull(exampleSection, "MS.DEFENDER.1.1v1 section should exist in example format");
        
        // 6. Compare key elements to ensure they match
        assertTrue(defenderSection.contains("**Class:** `io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator`"), 
                "Class name should match");
        assertTrue(defenderSection.contains("**policyType**"), 
                "Parameter name should match");
        assertTrue(defenderSection.contains("**Possible Values:** STANDARD (Standard), STRICT (Strict)"), 
                "Enum values should match");
        
        // 7. Write both sections to files for manual inspection if needed
        Files.writeString(tempDir.resolve("generated_section.md"), defenderSection);
        Files.writeString(tempDir.resolve("example_section.md"), exampleSection);
        
        // 8. Log the paths for manual inspection if needed
        System.out.println("Generated section: " + tempDir.resolve("generated_section.md"));
        System.out.println("Example section: " + tempDir.resolve("example_section.md"));
    }
    
    /**
     * Extract a specific policy section from documentation text.
     * 
     * @param documentation The full documentation text
     * @param policyId The policy ID to extract
     * @return The extracted section or null if not found
     */
    private String extractPolicySection(String documentation, String policyId) {
        // Find the policy section by looking for the policy ID surrounded by ** (bold)
        int startIdx = documentation.indexOf("**" + policyId + "**");
        if (startIdx == -1) {
            return null;
        }
        
        // Find the end of the section (next policy section or end of document)
        int endIdx = documentation.indexOf("\n\n---\n\n", startIdx);
        if (endIdx == -1) {
            endIdx = documentation.length();
        }
        
        // Return the extracted section
        return documentation.substring(startIdx, endIdx);
    }
}
