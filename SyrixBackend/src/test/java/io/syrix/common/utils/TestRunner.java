package io.syrix.common.utils;

public class TestRunner {
    public static void main(String[] args) throws Exception {
        // Run tests directly
        System.out.println("Running PolicyRemediatorDocumentationGeneratorTest...");
        PolicyRemediatorDocumentationGeneratorTest test1 = new PolicyRemediatorDocumentationGeneratorTest();
        test1.testSpecialCaseRemediatorHandling(null); // This will use JUnit's TempDir
        
        System.out.println("Running ExampleFormatTest...");
        ExampleFormatTest test2 = new ExampleFormatTest();
        test2.testGeneratedFormatMatchesExample(null); // This will use JUnit's TempDir
        
        System.out.println("All tests passed successfully!");
    }
}
