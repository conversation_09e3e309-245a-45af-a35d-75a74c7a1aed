package io.syrix.common.storage;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilsTest {

    @Test
    @DisplayName("Should convert date with microsecond precision to millisecond format")
    void testInstantToInt_WithMicrosecondPrecision() {
        // given: date from task 2025-06-29T08:27:41.835385Z
        Instant testDate = Instant.parse("2025-06-29T08:27:41.835385Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then: should be 20250629082741835L
        assertEquals(20250629082741835L, result);
    }

    @Test
    @DisplayName("Should handle date with exact millisecond precision")
    void testInstantToInt_WithMillisecondPrecision() {
        // given: date with millisecond precision
        Instant testDate = Instant.parse("2025-01-15T12:30:45.123Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then
        assertEquals(20250115123045123L, result);
    }

    @Test
    @DisplayName("Should handle date with zero milliseconds")
    void testInstantToInt_WithZeroMilliseconds() {
        // given: date without milliseconds
        Instant testDate = Instant.parse("2024-12-31T23:59:59.000Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then
        assertEquals(20241231235959000L, result);
    }

    @Test
    @DisplayName("Should handle date with nanosecond precision and round down to milliseconds")
    void testInstantToInt_WithNanosecondPrecision() {
        // given: date with nanosecond precision
        Instant testDate = Instant.parse("2025-03-10T14:25:30.999888777Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then: nanoseconds should be truncated to milliseconds (999)
        assertEquals(20250310142530999L, result);
    }

    @Test
    @DisplayName("Should handle edge case with single digit values")
    void testInstantToInt_WithSingleDigitValues() {
        // given: date with single digit values
        Instant testDate = Instant.parse("2025-01-01T01:01:01.001Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then: leading zeros should be preserved
        assertEquals(20250101010101001L, result);
    }

    @Test
    @DisplayName("Should handle current date format consistently")
    void testInstantToInt_WithCurrentDateFormat() {
        // given: current date in UTC
        Instant now = Instant.now();
        
        // when
        long result = DateUtils.instantToInt(now);
        
        // then: result should be positive number with 17 digits
        assertTrue(result > 0);
        assertEquals(17, String.valueOf(result).length());
    }

    @Test
    @DisplayName("Should handle beginning of year date")
    void testInstantToInt_BeginningOfYear() {
        // given: beginning of year
        Instant testDate = Instant.parse("2025-01-01T00:00:00.000Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then
        assertEquals(20250101000000000L, result);
    }

    @Test
    @DisplayName("Should handle end of year date")
    void testInstantToInt_EndOfYear() {
        // given: end of year
        Instant testDate = Instant.parse("2025-12-31T23:59:59.999Z");
        
        // when
        long result = DateUtils.instantToInt(testDate);
        
        // then
        assertEquals(20251231235959999L, result);
    }
}
