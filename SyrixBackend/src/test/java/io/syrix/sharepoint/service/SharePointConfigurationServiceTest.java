package io.syrix.sharepoint.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.utils.TestUtils;
import io.syrix.products.microsoft.sharepoint.SharePointConfigurationService;
import io.syrix.protocols.client.graph.response.Site;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SiteProperties;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AllowToBeDeleted;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Collections;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SharePointConfigurationServiceTest {

    @Mock
    private MicrosoftGraphClient graphClient;
    
    @Mock
    private PowerShellSharepointClient powerShellClient;

    @Captor
    private ArgumentCaptor<GraphRequest> requestCaptor;

    private SharePointConfigurationService service;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        // Only setup the service without unnecessary stubbing
        service = new SharePointConfigurationService(graphClient, powerShellClient, objectMapper, mock(MetricsCollector.class));
        
        // Individual stubs will be handled in each test method as needed
    }

    @Nested
    @DisplayName("SharePoint Configuration Export Tests")
    @Disabled("Disabled until SharePoint configuration export issues are fixed")
    class ConfigurationExportTests {

        @Test
        @DisplayName("Should successfully export SharePoint and OneDrive configuration")
        void shouldExportConfiguration() throws Exception {
            // Given
            // Set up mocks for the SharePoint Configuration Service
            TenantProperties tenantProperties = new TenantProperties();
            AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = true;
            SiteProperties siteProperties = new SiteProperties();
            
            // Setup the mock for Site with WebUrl
            Site site = new Site();
            site.setWebUrl("https://contoso.sharepoint.com");
            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));
            
            // Setup PowerShell mock to return appropriate objects
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProperties)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProperties)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should handle export failures with retries")
        void shouldHandleExportFailuresWithRetries() {
            // Given
            when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                    .thenReturn(CompletableFuture.failedFuture(
                            new RuntimeException("API Error")
                    ));
            
            // Ensure PowerShell client returns properly structured CompletableFuture objects
            when(powerShellClient.execute(any(SPShellCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When & Then
            assertThatThrownBy(() -> service.exportConfiguration())
                    .isInstanceOf(ConfigurationExportException.class)
                    .hasMessageContaining("SharePoint/OneDrive configuration export failed");

            verify(graphClient, atLeast(1)).makeGraphRequest(any(GraphRequest.class));
        }
    }

    @Nested
    @DisplayName("Individual Configuration Tests")
    @Disabled("Disabled until SharePoint configuration issues are fixed")
    class IndividualConfigTests {

        @Test
        @DisplayName("Should correctly fetch tenant settings")
        void shouldFetchTenantSettings() throws Exception {
            // Given
            // Setup PowerShellClient mock with proper return types
            TenantProperties tenantProperties = new TenantProperties();
            AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = true;
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProperties)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should correctly fetch OneDrive settings")
        void shouldFetchOneDriveSettings() throws Exception {
            // Given
            // Setup PowerShellClient mock with proper return types
            TenantProperties tenantProperties = new TenantProperties();
            AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = true;
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProperties)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should correctly fetch security settings")
        void shouldFetchSecuritySettings() throws Exception {
            // Given
            // Setup PowerShellClient mock with proper return types
            TenantProperties tenantProperties = new TenantProperties();
            AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = true;
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProperties)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should correctly fetch site settings")
        void shouldFetchSiteSettings() throws Exception {
            // Given
            // Setup GraphClient with Site that has WebUrl
            Site site = new Site();
            site.setWebUrl("https://contoso.sharepoint.com");
            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));
            
            // Setup PowerShellClient mock with proper return types
            TenantProperties tenantProperties = new TenantProperties();
            AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = true;
            SiteProperties siteProperties = new SiteProperties();
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProperties)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProperties)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
        }
    }
}