package io.syrix.teams.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.utils.TestUtils;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.products.microsoft.teams.TeamsConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TeamsConfigurationServiceTest {

	@Mock
	private MicrosoftGraphClient graphClient;
	private PowerShellTeamsClient powerShellClient;

	@Captor
	private ArgumentCaptor<GraphRequest> requestCaptor;

	private TeamsConfigurationService service;
	private ObjectMapper objectMapper;

	@BeforeEach
	void setUp() {
	objectMapper = new ObjectMapper();
	powerShellClient = mock(PowerShellTeamsClient.class);
	service = new TeamsConfigurationService(graphClient, powerShellClient);
	}

	@Nested
	@Disabled
	@DisplayName("Teams Configuration Export Tests")
	class ConfigurationExportTests {

		@Test
		@DisplayName("Should fetch and validate tenant info")
		void shouldFetchTenantInfo() throws Exception {
			// Given
			JsonNode tenantInfo = TestUtils.loadTestData("teams/tenant-info.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/teams/tenantsettings"))))
					.thenReturn(CompletableFuture.completedFuture(tenantInfo));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("tenant_info"))
					.isNotNull()
					.satisfies(info -> {
						JsonNode infoData = (JsonNode) info;
						assertThat(infoData.get("displayName").asText()).isEqualTo("Test Tenant");
						assertThat(infoData.get("isTeamsEnabled").asBoolean()).isTrue();
						assertThat(infoData.get("isExternalCommunicationEnabled").asBoolean()).isTrue();
					});
		}

		@Test
		@DisplayName("Should fetch and validate meeting policies")
		void shouldFetchMeetingPolicies() throws Exception {
			// Given
			JsonNode policies = TestUtils.loadTestData("teams/meeting-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/policies/teamsAppSetupPolicies"))))
					.thenReturn(CompletableFuture.completedFuture(policies));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("meeting_policies"))
					.isNotNull()
					.satisfies(policiesNode -> {
						JsonNode policiesData = (JsonNode) policiesNode;
						assertThat(policiesData.get("value"))
								.isNotNull()
								.hasSize(2);

						JsonNode globalPolicy = policiesData.get("value").get(0);
						assertThat(globalPolicy.get("displayName").asText()).isEqualTo("Global Meeting Policy");
						assertThat(globalPolicy.get("isOrganizationDefault").asBoolean()).isTrue();
						assertThat(globalPolicy.get("meetingsEnabled").asBoolean()).isTrue();

						JsonNode restrictedPolicy = policiesData.get("value").get(1);
						assertThat(restrictedPolicy.get("displayName").asText()).isEqualTo("Restricted Meeting Policy");
						assertThat(restrictedPolicy.get("meetingsEnabled").asBoolean()).isTrue();
						assertThat(restrictedPolicy.get("allowAnonymousUsersToJoinMeeting").asBoolean()).isFalse();
					});
		}

		@Test
		@DisplayName("Should fetch and validate federation configuration")
		void shouldFetchFederationConfig() throws Exception {
			// Given
			JsonNode fedConfig = TestUtils.loadTestData("teams/federation-config.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/policies/teamsFederationConfiguration"))))
					.thenReturn(CompletableFuture.completedFuture(fedConfig));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("federation_configuration"))
					.isNotNull()
					.satisfies(config -> {
						JsonNode configData = (JsonNode) config;
						assertThat(configData.get("allowTeamsConsumer").asBoolean()).isTrue();
						assertThat(configData.get("allowFederatedUsers").asBoolean()).isTrue();
						assertThat(configData.get("allowedDomains"))
								.isNotNull()
								.hasSize(2)
								.satisfies(domains -> {
									JsonNode domainsData = (JsonNode) domains;
									assertThat(domainsData.get(0).asText()).isEqualTo("partner.com");
									assertThat(domainsData.get(1).asText()).isEqualTo("trusted.org");
								});
					});
		}

		@Test
		@DisplayName("Should fetch and validate client configuration")
		void shouldFetchClientConfig() throws Exception {
			// Given
			JsonNode clientConfig = TestUtils.loadTestData("teams/client-config.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/policies/teamsClientConfiguration"))))
					.thenReturn(CompletableFuture.completedFuture(clientConfig));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("client_configuration"))
					.isNotNull()
					.satisfies(config -> {
						JsonNode configData = (JsonNode) config;
						assertThat(configData.get("allowEmailIntoChannel").asBoolean()).isTrue();
						assertThat(configData.get("allowGiphy").asBoolean()).isTrue();
						assertThat(configData.get("allowUserEditMessages").asBoolean()).isTrue();
						assertThat(configData.get("giphyRatingType").asText()).isEqualTo("moderate");
					});
		}

		@Test
		@DisplayName("Should fetch and validate app policies")
		void shouldFetchAppPolicies() throws Exception {
			// Given
			JsonNode appPolicies = TestUtils.loadTestData("teams/app-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/policies/teamsAppPermissionPolicies"))))
					.thenReturn(CompletableFuture.completedFuture(appPolicies));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("app_policies.json"))
					.isNotNull()
					.satisfies(policies -> {
						JsonNode policiesData = (JsonNode) policies;
						JsonNode firstPolicy = policiesData.get("value").get(0);
						assertThat(firstPolicy.get("displayName").asText()).isEqualTo("Global App Policy");
						assertThat(firstPolicy.get("defaultAppAccess").asText()).isEqualTo("allow");
						assertThat(firstPolicy.get("allowedAppCategories"))
								.hasSize(2)
								.satisfies(categories -> {
									JsonNode categoriesData = (JsonNode) categories;
									assertThat(categoriesData.get(0).asText()).isEqualTo("productivity");
									assertThat(categoriesData.get(1).asText()).isEqualTo("communication");
								});
					});
		}

		@Test
		@DisplayName("Should fetch and validate broadcast policies")
		void shouldFetchBroadcastPolicies() throws Exception {
			// Given
			JsonNode broadcastPolicies = TestUtils.loadTestData("teams/broadcast-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().equals("/policies/teamsMeetingBroadcastPolicies"))))
					.thenReturn(CompletableFuture.completedFuture(broadcastPolicies));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("broadcast_policies"))
					.isNotNull()
					.satisfies(policies -> {
						JsonNode policiesData = (JsonNode) policies;
						JsonNode firstPolicy = policiesData.get("value").get(0);
						assertThat(firstPolicy.get("displayName").asText()).isEqualTo("Global Broadcast Policy");
						assertThat(firstPolicy.get("allowBroadcastScheduling").asBoolean()).isTrue();
						assertThat(firstPolicy.get("broadcastRecordingMode").asText()).isEqualTo("always");
					});
		}

		@Test
		@DisplayName("Should handle export failures with retries")
		void shouldHandleExportFailuresWithRetries() {
			// Given
			when(graphClient.getTenantId()).thenReturn("mock-tenant-id");
			when(powerShellClient.execute(any())).thenReturn(
					CompletableFuture.failedFuture(new RuntimeException("PowerShell execution failed"))
			);

			// When & Then
			assertThatThrownBy(() -> service.exportConfiguration())
					.isInstanceOf(ConfigurationExportException.class)
					.hasMessageContaining("Teams configuration export failed");

			// Verify retry attempts
			verify(powerShellClient, atLeast(1)).execute(any());
			verify(graphClient, atLeast(1)).getTenantId();
		}

		@Test
		@DisplayName("Should collect all configurations successfully")
		void shouldCollectAllConfigurations() throws Exception {
			// Given
			mockAllConfigResponses();

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData())
					.isNotNull()
					.satisfies(data -> {
						JsonNode dataNode = (JsonNode) data;
						assertThat(dataNode.has("tenant_info")).isTrue();
						assertThat(dataNode.has("meeting_policies")).isTrue();
						assertThat(dataNode.has("federation_configuration")).isTrue();
						assertThat(dataNode.has("client_configuration")).isTrue();
						assertThat(dataNode.has("app_policies.json")).isTrue();
						assertThat(dataNode.has("broadcast_policies")).isTrue();
					});

			verify(graphClient, times(6)).makeGraphRequest(requestCaptor.capture());
			assertThat(requestCaptor.getAllValues())
					.extracting(GraphRequest::getEndpoint)
					.containsExactlyInAnyOrder(
							"/teams/tenantsettings",
							"/policies/teamsAppSetupPolicies",
							"/policies/teamsFederationConfiguration",
							"/policies/teamsClientConfiguration",
							"/policies/teamsAppPermissionPolicies",
							"/policies/teamsMeetingBroadcastPolicies"
					);
		}

		private void mockAllConfigResponses() {
			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenAnswer(invocation -> {
						GraphRequest request = invocation.getArgument(0);
						String endpoint = request.getEndpoint();

						JsonNode response = switch (endpoint) {
							case "/teams/tenantsettings" -> TestUtils.loadTestData("teams/tenant-info.json");
							case "/policies/teamsAppSetupPolicies" -> TestUtils.loadTestData("teams/meeting-policies.json");
							case "/policies/teamsFederationConfiguration" -> TestUtils.loadTestData("teams/federation-config.json");
							case "/policies/teamsClientConfiguration" -> TestUtils.loadTestData("teams/client-config.json");
							case "/policies/teamsAppPermissionPolicies" -> TestUtils.loadTestData("teams/app-policies.json");
							case "/policies/teamsMeetingBroadcastPolicies" -> TestUtils.loadTestData("teams/broadcast-policies.json");
							default -> throw new IllegalArgumentException("Unexpected endpoint: " + endpoint);
						};

						return CompletableFuture.completedFuture(response);
					});
		}
	}
}