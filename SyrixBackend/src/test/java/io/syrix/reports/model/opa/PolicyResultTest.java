package io.syrix.reports.model.opa;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PolicyResultTest {
	String body = """
			[ {
			  "ActualValue" : "",
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedUser" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.3v1",
			  "ReportDetails" : "0 admin(s) that are not cloud-only found",
			  "RequirementMet" : true
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ ],
			  "Criticality" : "Shall/Not-Implemented",
			  "PolicyId" : "MS.AAD.4.1v1",
			  "ReportDetails" : "This product does not currently have the capability to check compliance for this policy. See <a href=\\"https://github.com/cisagov/ScubaGear/blob/vmain/PowerShell/ScubaGear/baselines/aad.md#msaad41v1\\" target=\\"_blank\\">Secure Configuration Baseline policy</a> for instructions on manual check",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ ],
			  "Criticality" : "Should/Not-Implemented",
			  "PolicyId" : "MS.AAD.2.2v1",
			  "ReportDetails" : "This product does not currently have the capability to check compliance for this policy. See <a href=\\"https://github.com/cisagov/ScubaGear/blob/vmain/PowerShell/ScubaGear/baselines/aad.md#msaad22v1\\" target=\\"_blank\\">Secure Configuration Baseline policy</a> for instructions on manual check",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ ],
			  "Criticality" : "Should/Not-Implemented",
			  "PolicyId" : "MS.AAD.8.3v1",
			  "ReportDetails" : "This product does not currently have the capability to check compliance for this policy. See <a href=\\"https://github.com/cisagov/ScubaGear/blob/vmain/PowerShell/ScubaGear/baselines/aad.md#msaad83v1\\" target=\\"_blank\\">Secure Configuration Baseline policy</a> for instructions on manual check",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaPolicyAuthenticationMethodPolicy" ],
			  "Criticality" : "Shall/Not-Implemented",
			  "PolicyId" : "MS.AAD.3.3v1",
			  "ReportDetails" : "This policy is only applicable if phishing-resistant MFA is not enforced and MS Authenticator is enabled. See <a href=\\"https://github.com/cisagov/ScubaGear/blob/vmain/PowerShell/ScubaGear/baselines/aad.md#msaad33v1\\" target=\\"_blank\\">Secure Configuration Baseline policy</a> for more info",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaPolicyAuthenticationMethodPolicy" ],
			  "Criticality" : "Shall/Not-Implemented",
			  "PolicyId" : "MS.AAD.3.5v1",
			  "ReportDetails" : "This policy is only applicable if the tenant has their Manage Migration feature set to Migration Complete. See <a href=\\"https://github.com/cisagov/ScubaGear/blob/vmain/PowerShell/ScubaGear/baselines/aad.md#msaad34v1\\" target=\\"_blank\\">Secure Configuration Baseline policy</a> for more info",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : {
			    "all_admin_consent_policies" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaDirectorySetting" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.5.3v1",
			  "ReportDetails" : "Requirement not met",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : {
			    "all_allow_invite_values" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaPolicyAuthorizationPolicy" ],
			  "Criticality" : "Should",
			  "PolicyId" : "MS.AAD.8.2v1",
			  "ReportDetails" : "Permission level set to ",
			  "RequirementMet" : true
			}, {
			  "ActualValue" : {
			    "all_allowed_create_values" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaPolicyAuthorizationPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.5.1v1",
			  "ReportDetails" : "0 authorization policies found that allow non-admin users to register third-party applications",
			  "RequirementMet" : true
			}, {
			  "ActualValue" : {
			    "all_grant_policy_values" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaPolicyAuthorizationPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.5.2v1",
			  "ReportDetails" : "0 authorization policies found that allow non-admin users to consent to third-party applications",
			  "RequirementMet" : true
			}, {
			  "ActualValue" : {
			    "all_roleid_values" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaPolicyAuthorizationPolicy" ],
			  "Criticality" : "Should",
			  "PolicyId" : "MS.AAD.8.1v1",
			  "ReportDetails" : "Permission level set to ",
			  "RequirementMet" : true
			}, {
			  "ActualValue" : {
			    "federated_domains" : [ ],
			    "invalid_domains" : [ ],
			    "valid_domains" : [ ]
			  },
			  "Commandlet" : [ "Get-MgBetaDomain" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.6.1v1",
			  "ReportDetails" : "Requirement not met",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaDirectorySetting" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.5.4v1",
			  "ReportDetails" : "Requirement not met",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.1.1v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.2.1v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.2.3v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.3.1v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.3.2v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.3.6v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Should",
			  "PolicyId" : "MS.AAD.3.7v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaIdentityConditionalAccessPolicy" ],
			  "Criticality" : "Should",
			  "PolicyId" : "MS.AAD.3.8v1",
			  "ReportDetails" : "0 conditional access policy(s) found that meet(s) all requirements. <a href='#caps'>View all CA policies</a>.",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.4v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.5v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.6v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.7v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.8v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedRole" ],
			  "Criticality" : "Should",
			  "PolicyId" : "MS.AAD.7.9v1",
			  "ReportDetails" : "**NOTE: Your tenant does not have a Microsoft Entra ID P2 license, which is required for this feature**",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedUser" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.1v1",
			  "ReportDetails" : "0 global admin(s) found",
			  "RequirementMet" : false
			}, {
			  "ActualValue" : [ ],
			  "Commandlet" : [ "Get-MgBetaSubscribedSku", "Get-PrivilegedUser" ],
			  "Criticality" : "Shall",
			  "PolicyId" : "MS.AAD.7.2v1",
			  "ReportDetails" : "Requirement not met: Policy MS.AAD.7.1 failed so score not computed",
			  "RequirementMet" : false
			} ]
			""";

	@Test
	public void testParse() throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		JsonNode resultNode = mapper.readTree(body);

		List<PolicyResult> results = mapper.convertValue(
				resultNode,
				mapper.getTypeFactory().constructCollectionType(List.class, PolicyResult.class)
		);

		// Verify deserialization worked
		assertNotNull(results);
		assertEquals(29, results.size());

		// Test first element with string ActualValue
		PolicyResult firstResult = results.getFirst();
		assertEquals("MS.AAD.7.3v1", firstResult.getPolicyId());
		assertEquals("", firstResult.getActualValue()); // String value
		assertTrue(firstResult.isRequirementMet());

		// Test element with array ActualValue
		PolicyResult secondResult = results.get(1);
		assertEquals("MS.AAD.4.1v1", secondResult.getPolicyId());
		assertNotNull(secondResult.getActualValue()); // Should be empty array
		assertFalse(secondResult.isRequirementMet());

		// Test element with object ActualValue
		PolicyResult objectResult = results.get(6);
		assertEquals("MS.AAD.5.3v1", objectResult.getPolicyId());
		assertNotNull(objectResult.getActualValue()); // Should be Map/Object
		assertFalse(objectResult.isRequirementMet());
	}

}