package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for OWAThirdPartyStorageRemediator (MS.EXO.8.5v1).
 * Tests third-party storage provider prevention in Outlook on the web.
 */
@ExtendWith(MockitoExtension.class)
class OWAThirdPartyStorageRemediatorTest {

    @Mock
    private MicrosoftGraphClient graphClient;
    
    @Mock
    private PowerShellClient exchangeClient;
    
    @Mock
    private ExchangeRemediationContext remediationContext;
    
    @Mock
    private ExchangeRemediationConfig remediationConfig;

    @Captor
    private ArgumentCaptor<PowerShellClient.CommandRequest> requestCaptor;

    private OWAThirdPartyStorageRemediator remediator;
    private ObjectMapper objectMapper;
    private ObjectNode configNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        configNode = objectMapper.createObjectNode();

        // Create mock mailbox policy array
        ArrayNode policiesArray = objectMapper.createArrayNode();
        ObjectNode policy = objectMapper.createObjectNode();
        policy.put("Identity", "OwaMailboxPolicy-Default");
        policy.put("AdditionalStorageProvidersAvailable", true);
        policiesArray.add(policy);
        configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, policiesArray);

        // Create successful PowerShell response - make it lenient for tests that don't use it
        ObjectNode successResponse = objectMapper.createObjectNode();
        successResponse.put("status", "success");
        lenient().when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

        remediator = new OWAThirdPartyStorageRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
    }

    @Nested
    @DisplayName("Policy Compliance Tests")
    class PolicyComplianceTests {

        @Test
        @DisplayName("Should detect compliant policies with third-party storage already disabled")
        void shouldDetectCompliantPolicies() {
            // Given
            ArrayNode compliantPolicies = createCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, compliantPolicies);

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.REQUIREMENT_MET);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            
            // Should not attempt any PowerShell commands for compliant policies
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }

        @Test
        @DisplayName("Should detect non-compliant policies with third-party storage enabled")
        void shouldDetectNonCompliantPolicies() {
            // Given
            ArrayNode nonCompliantPolicies = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, nonCompliantPolicies);
            
            // Mock Set-OwaMailboxPolicy to succeed
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(result.getDesc()).contains("Disabled third-party storage providers");
            assertThat(result.getChanges()).hasSize(2); // Two non-compliant policies

            verify(exchangeClient, times(2)).executeCmdletCommand(requestCaptor.capture());
            List<PowerShellClient.CommandRequest> requests = requestCaptor.getAllValues();
            
            // Verify Set-OwaMailboxPolicy commands were executed
            requests.forEach(request -> {
                assertThat(request.getCmdletName()).isEqualTo(ExoConstants.SET_OWA_MAILBOX_POLICY);
                @SuppressWarnings("unchecked")
                Map<String, Object> cmdletInput = (Map<String, Object>) request.toRequestBody().get("CmdletInput");
                @SuppressWarnings("unchecked")
                Map<String, Object> params = (Map<String, Object>) cmdletInput.get("Parameters");
                assertThat(params).containsKey(ExoConstants.IDENTITY);
                assertThat(params).containsEntry("AdditionalStorageProvidersAvailable", false);
            });
        }

        @Test
        @DisplayName("Should handle mixed compliance scenarios")
        void shouldHandleMixedComplianceScenarios() {
            // Given
            ArrayNode mixedPolicies = createMixedPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, mixedPolicies);
            
            // Mock Set-OwaMailboxPolicy to succeed
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(result.getChanges()).hasSize(1); // Only one non-compliant policy

            verify(exchangeClient, times(1)).executeCmdletCommand(requestCaptor.capture());
            PowerShellClient.CommandRequest request = requestCaptor.getValue();
            @SuppressWarnings("unchecked")
            Map<String, Object> cmdletInput = (Map<String, Object>) request.toRequestBody().get("CmdletInput");
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) cmdletInput.get("Parameters");
            assertThat(params).containsEntry(ExoConstants.IDENTITY, "OwaMailboxPolicy-NonCompliant");
        }

        @Test
        @DisplayName("Should handle empty policy configuration")
        void shouldHandleEmptyPolicyConfiguration() {
            // Given
            ArrayNode emptyPolicies = objectMapper.createArrayNode();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, emptyPolicies);

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(result.getDesc()).contains(ExoConstants.ERROR_NO_OWA_MAILBOX_POLICIES);
        }

        @Test
        @DisplayName("Should handle missing configuration node")
        void shouldHandleMissingConfigurationNode() {
            // Given - create remediator with empty configuration node
            ObjectNode emptyConfigNode = objectMapper.createObjectNode();
            OWAThirdPartyStorageRemediator emptyRemediator = new OWAThirdPartyStorageRemediator(
                graphClient, exchangeClient, emptyConfigNode, remediationContext, remediationConfig);

            // When
            PolicyChangeResult result = emptyRemediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(result.getDesc()).contains(ExoConstants.ERROR_NO_OWA_MAILBOX_POLICIES);
        }
    }

    @Nested
    @DisplayName("Remediation Execution Tests")
    class RemediationExecutionTests {

        @Test
        @DisplayName("Should execute PowerShell commands with correct parameters")
        void shouldExecutePowerShellCommandsWithCorrectParameters() {
            // Given
            ArrayNode nonCompliantPolicies = createSingleNonCompliantPolicy();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, nonCompliantPolicies);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            remediator.remediate_().join();

            // Then
            verify(exchangeClient).executeCmdletCommand(requestCaptor.capture());
            PowerShellClient.CommandRequest request = requestCaptor.getValue();
            
            assertThat(request.getCmdletName()).isEqualTo(ExoConstants.SET_OWA_MAILBOX_POLICY);
            @SuppressWarnings("unchecked")
            Map<String, Object> cmdletInput = (Map<String, Object>) request.toRequestBody().get("CmdletInput");
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) cmdletInput.get("Parameters");
            assertThat(params).containsEntry(ExoConstants.IDENTITY, "TestPolicy");
            assertThat(params).containsEntry("AdditionalStorageProvidersAvailable", false);
        }

        @Test
        @DisplayName("Should handle PowerShell command failures")
        void shouldHandlePowerShellCommandFailures() {
            // Given
            ArrayNode nonCompliantPolicies = createSingleNonCompliantPolicy();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, nonCompliantPolicies);
            
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell execution failed")));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(result.getDesc()).contains("Failed to disable third-party storage providers in all");
            
            assertThat(result.getChanges()).hasSize(1);
            ParameterChangeResult change = result.getChanges().getFirst();
            assertThat(change.getStatus()).isEqualTo(ParameterChangeStatus.FAILED);
            // Decode the parameter to verify the policy identity
            Map<String, String> decodedParams = OWAThirdPartyStorageRemediator.ParameterEncoder.decode(change.getParameter());
            assertThat(decodedParams.get("identity")).isEqualTo("TestPolicy");
        }

        @Test
        @DisplayName("Should track parameter changes correctly")
        void shouldTrackParameterChangesCorrectly() {
            // Given
            ArrayNode nonCompliantPolicies = createSingleNonCompliantPolicy();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, nonCompliantPolicies);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getChanges()).hasSize(1);
            ParameterChangeResult change = result.getChanges().getFirst();
            
            // Decode the parameter to verify the policy identity
            Map<String, String> decodedParams = OWAThirdPartyStorageRemediator.ParameterEncoder.decode(change.getParameter());
            assertThat(decodedParams.get("identity")).isEqualTo("TestPolicy");
            assertThat(change.getPrevValue()).isEqualTo("true");
            assertThat(change.getNewValue()).isEqualTo("false");
            assertThat(change.getStatus()).isEqualTo(ParameterChangeStatus.SUCCESS);
            assertThat(change.getTimeStamp()).isNotNull();
        }
    }

    @Nested
    @DisplayName("Rollback Functionality Tests")
    class RollbackFunctionalityTests {

        @Test
        @DisplayName("Should execute rollback successfully")
        void shouldExecuteRollbackSuccessfully() {
            // Given
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(rollbackResult.getPolicyId()).isEqualTo("MS.EXO.8.5v1");
            assertThat(rollbackResult.getDesc()).contains("Restored third-party storage settings in");

            verify(exchangeClient).executeCmdletCommand(requestCaptor.capture());
            PowerShellClient.CommandRequest request = requestCaptor.getValue();
            
            assertThat(request.getCmdletName()).isEqualTo(ExoConstants.SET_OWA_MAILBOX_POLICY);
            @SuppressWarnings("unchecked")
            Map<String, Object> cmdletInput = (Map<String, Object>) request.toRequestBody().get("CmdletInput");
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) cmdletInput.get("Parameters");
            assertThat(params).containsEntry(ExoConstants.IDENTITY, "TestPolicy");
            assertThat(params).containsEntry("AdditionalStorageProvidersAvailable", true);
        }

        @Test
        @DisplayName("Should handle rollback with no changes")
        void shouldHandleRollbackWithNoChanges() {
            // Given
            PolicyChangeResult emptyResult = new PolicyChangeResult()
                .policyId("MS.EXO.8.5v1")
                .result(RemediationResult.SUCCESS)
                .changes(List.of());

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(emptyResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getDesc()).contains("No changes found in fix result");
            
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }

        @Test
        @DisplayName("Should handle rollback PowerShell failures")
        void shouldHandleRollbackPowerShellFailures() {
            // Given
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getDesc()).contains("Failed to restore third-party storage settings in all");
        }

        @Test
        @DisplayName("Should skip rollback for failed original changes")
        void shouldSkipRollbackForFailedOriginalChanges() {
            // Given
            PolicyChangeResult failedResult = createFailedRemediationResult();

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(failedResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getDesc()).contains("Failed to restore third-party storage settings in all");
            
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }
    }

    @Nested
    @DisplayName("Parameter Encoder Tests")
    class ParameterEncoderTests {

        @Test
        @DisplayName("Should encode and decode parameters correctly")
        void shouldEncodeAndDecodeParametersCorrectly() {
            // Given
            String policyIdentity = "Test Policy with Special=Characters||More";
            String previousValue = "true";

            // When
            String encoded = OWAThirdPartyStorageRemediator.ParameterEncoder.encode(policyIdentity, previousValue);
            Map<String, String> decoded = OWAThirdPartyStorageRemediator.ParameterEncoder.decode(encoded);

            // Then
            assertThat(decoded.get("identity")).isEqualTo(policyIdentity);
            assertThat(decoded.get("previous_value")).isEqualTo(previousValue);
        }

        @Test
        @DisplayName("Should handle null and empty values gracefully")
        void shouldHandleNullAndEmptyValuesGracefully() {
            // Given
            String policyIdentity = "TestPolicy";
            String previousValue = null;

            // When
            String encoded = OWAThirdPartyStorageRemediator.ParameterEncoder.encode(policyIdentity, previousValue);
            Map<String, String> decoded = OWAThirdPartyStorageRemediator.ParameterEncoder.decode(encoded);

            // Then
            assertThat(decoded.get("identity")).isEqualTo(policyIdentity);
            assertThat(decoded.get("previous_value")).isNull();
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    class IntegrationTests {

        @Test
        @DisplayName("Should complete full remediation workflow")
        void shouldCompleteFullRemediationWorkflow() {
            // Given
            ArrayNode policies = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_OWA_THIRD_PARTY_STORAGE, policies);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When - Execute remediation
            JsonNode remediateResult = remediator.remediate().join();
            
            // Then - Verify JSON response structure (using UPPER_CAMEL_CASE field names)
            assertThat(remediateResult.has("Result")).isTrue();
            assertThat(remediateResult.has("PolicyId")).isTrue();
            assertThat(remediateResult.has("Desc")).isTrue();
            assertThat(remediateResult.has("Changes")).isTrue();
            
            assertThat(remediateResult.get("PolicyId").asText()).isEqualTo("MS.EXO.8.5v1");
            assertThat(remediateResult.get("Result").asText()).isEqualTo("SUCCESS");
        }
    }

    // Test Data Creation Methods

    private ArrayNode createCompliantPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("Identity", "OwaMailboxPolicy-Default");
        policy1.put("AdditionalStorageProvidersAvailable", false);
        
        ObjectNode policy2 = objectMapper.createObjectNode();
        policy2.put("Identity", "OwaMailboxPolicy-Restricted");
        policy2.put("AdditionalStorageProvidersAvailable", false);
        
        policies.add(policy1);
        policies.add(policy2);
        return policies;
    }

    private ArrayNode createNonCompliantPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("Identity", "OwaMailboxPolicy-Lenient");
        policy1.put("AdditionalStorageProvidersAvailable", true);
        
        ObjectNode policy2 = objectMapper.createObjectNode();
        policy2.put("Identity", "OwaMailboxPolicy-Legacy");
        policy2.put("AdditionalStorageProvidersAvailable", true);
        
        policies.add(policy1);
        policies.add(policy2);
        return policies;
    }

    private ArrayNode createMixedPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode compliantPolicy = objectMapper.createObjectNode();
        compliantPolicy.put("Identity", "OwaMailboxPolicy-Compliant");
        compliantPolicy.put("AdditionalStorageProvidersAvailable", false);
        
        ObjectNode nonCompliantPolicy = objectMapper.createObjectNode();
        nonCompliantPolicy.put("Identity", "OwaMailboxPolicy-NonCompliant");
        nonCompliantPolicy.put("AdditionalStorageProvidersAvailable", true);
        
        policies.add(compliantPolicy);
        policies.add(nonCompliantPolicy);
        return policies;
    }

    private ArrayNode createSingleNonCompliantPolicy() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy = objectMapper.createObjectNode();
        policy.put("Identity", "TestPolicy");
        policy.put("AdditionalStorageProvidersAvailable", true);
        
        policies.add(policy);
        return policies;
    }

    private PolicyChangeResult createSuccessfulRemediationResult() {
        // Use the actual ParameterEncoder format for consistency
        String encodedParameter = OWAThirdPartyStorageRemediator.ParameterEncoder.encode("TestPolicy", "true");
        ParameterChangeResult change = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter(encodedParameter)
            .prevValue("true")
            .newValue("false")
            .status(ParameterChangeStatus.SUCCESS);
        
        return new PolicyChangeResult()
            .policyId("MS.EXO.8.5v1")
            .result(RemediationResult.SUCCESS)
            .desc("Test successful remediation")
            .changes(List.of(change));
    }

    private PolicyChangeResult createFailedRemediationResult() {
        // Use the actual ParameterEncoder format for consistency
        String encodedParameter = OWAThirdPartyStorageRemediator.ParameterEncoder.encode("TestPolicy", "true");
        ParameterChangeResult change = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter(encodedParameter)
            .prevValue("true")
            .newValue("true")
            .status(ParameterChangeStatus.FAILED);
        
        return new PolicyChangeResult()
            .policyId("MS.EXO.8.5v1")
            .result(RemediationResult.FAILED)
            .desc("Test failed remediation")
            .changes(List.of(change));
    }
}

