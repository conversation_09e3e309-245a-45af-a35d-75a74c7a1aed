package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExchangeMailTipsRemediator (MS.EXO.7.2v1)
 * Tests verify the MailTips enablement functionality including:
 * - Configuration loading and validation
 * - Remediation logic for enabling MailTips
 * - Rollback functionality
 * - Error handling scenarios
 */
@ExtendWith(MockitoExtension.class)
class ExchangeMailTipsRemediatorTest {

    @Mock
    private MicrosoftGraphClient mockGraphClient;

    @Mock
    private PowerShellClient mockPowerShellClient;

    @Mock
    private ExchangeRemediationContext mockContext;

    @Mock
    private ExchangeRemediationConfig mockRemediationConfig;

    @Captor
    private ArgumentCaptor<PowerShellClient.CommandRequest> requestCaptor;

    private ObjectMapper objectMapper;
    private ExchangeMailTipsRemediator remediator;
    private ObjectNode mockConfigNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mockConfigNode = objectMapper.createObjectNode();
        
        remediator = new ExchangeMailTipsRemediator(
            mockGraphClient, 
            mockPowerShellClient, 
            mockConfigNode, 
            mockContext, 
            mockRemediationConfig
        );
    }

    @Test
    void testRemediateWithMailTipsAlreadyEnabled() throws Exception {
        // Arrange - MailTips already enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_GROUP_METRICS_ENABLED, true);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals(RemediationResult.REQUIREMENT_MET, changeResult.getResult());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ALREADY_ENABLED, changeResult.getDesc());
        
        // Verify no PowerShell commands were executed since MailTips already enabled
        verify(mockPowerShellClient, never()).executeCmdletCommand(any());
    }

    @Test
    void testRemediateEnablesMailTipsSuccessfully() throws Exception {
        // Arrange - MailTips not enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock successful PowerShell execution for Set-OrganizationConfig
        JsonNode setConfigResult = objectMapper.createArrayNode();
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(setConfigResult));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ENABLED, changeResult.getDesc());

        // Verify Set-OrganizationConfig was called with correct parameters
        verify(mockPowerShellClient, times(1)).executeCmdletCommand(requestCaptor.capture());

        PowerShellClient.CommandRequest setConfigRequest = requestCaptor.getValue();
        assertEquals(ExoConstants.SET_ORGANIZATION_CONFIG, setConfigRequest.getCmdletName());
    }

    @Test
    void testRemediateFailsWhenConfigurationNotFound() throws Exception {
        // Arrange - No organization config available
        mockConfigNode = null;
        remediator = new ExchangeMailTipsRemediator(
            mockGraphClient, 
            mockPowerShellClient, 
            mockConfigNode, 
            mockContext, 
            mockRemediationConfig
        );

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals(RemediationResult.FAILED, changeResult.getResult());
        assertEquals(ExoConstants.ERROR_NO_MAILTIPS_CONFIG, changeResult.getDesc());
        
        verify(mockPowerShellClient, never()).executeCmdletCommand(any());
    }

    @Test
    void testRemediateHandlesPowerShellException() throws Exception {
        // Arrange - MailTips not enabled in organization config
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, false);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock PowerShell exception
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell execution failed")));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals(RemediationResult.FAILED, changeResult.getResult());
        assertTrue(changeResult.getDesc().contains("Failed to enable MailTips"));
        assertTrue(changeResult.getDesc().contains("PowerShell execution failed"));
    }

    @Test
    void testRollbackDisablesMailTipsSuccessfully() throws Exception {
        // Arrange
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(objectMapper.createArrayNode()));

        // Create mock fix result with parameter changes
        List<ParameterChangeResult> changes = List.of(
            new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED)
                .prevValue(false)
                .newValue(true)
                .status(ParameterChangeStatus.SUCCESS),
            new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED)
                .prevValue(false)
                .newValue(true)
                .status(ParameterChangeStatus.SUCCESS)
        );
        PolicyChangeResult mockFixResult = IPolicyRemediator.success_("MS.EXO.7.2v1", "Test fix", changes);

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.rollback(mockFixResult);
        PolicyChangeResult rollbackResult = result.get();

        // Assert
        assertEquals(RemediationResult.SUCCESS, rollbackResult.getResult());
        assertEquals("MS.EXO.7.2v1", rollbackResult.getPolicyId());
        assertEquals("MailTips settings restored to original values", rollbackResult.getDesc());

        // Verify rollback parameters
        verify(mockPowerShellClient).executeCmdletCommand(requestCaptor.capture());

        PowerShellClient.CommandRequest rollbackRequest = requestCaptor.getValue();
        assertEquals(ExoConstants.SET_ORGANIZATION_CONFIG, rollbackRequest.getCmdletName());
    }

    @Test
    void testRollbackHandlesException() throws Exception {
        // Arrange
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));

        // Create mock fix result with parameter changes
        List<ParameterChangeResult> changes = List.of(
            new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED)
                .prevValue(false)
                .newValue(true)
                .status(ParameterChangeStatus.SUCCESS)
        );
        PolicyChangeResult mockFixResult = IPolicyRemediator.success_("MS.EXO.7.2v1", "Test fix", changes);

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.rollback(mockFixResult);
        PolicyChangeResult rollbackResult = result.get();

        // Assert
        assertEquals(RemediationResult.FAILED, rollbackResult.getResult());
        assertEquals("MS.EXO.7.2v1", rollbackResult.getPolicyId());
        assertTrue(rollbackResult.getDesc().contains("Error during MailTips rollback"));
        assertTrue(rollbackResult.getDesc().contains("Rollback failed"));
    }

    @Test
    void testRemediatePartiallyEnabledMailTips() throws Exception {
        // Arrange - Only some MailTips settings are enabled, missing others
        ObjectNode orgConfig = objectMapper.createObjectNode();
        orgConfig.put(ExoConstants.MAILTIPS_ALL_TIPS_ENABLED, true);
        orgConfig.put(ExoConstants.MAILTIPS_MAILBOX_SOURCED_TIPS_ENABLED, false);
        // Missing MAILTIPS_EXTERNAL_RECIPIENTS_ENABLED and MAILTIPS_GROUP_METRICS_ENABLED
        mockConfigNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

        // Mock successful PowerShell execution
        when(mockPowerShellClient.executeCmdletCommand(any()))
            .thenReturn(CompletableFuture.completedFuture(objectMapper.createArrayNode()));

        // Act
        CompletableFuture<PolicyChangeResult> result = remediator.updateMailTipsProperties();
        PolicyChangeResult changeResult = result.get();

        // Assert - Should proceed with remediation since not fully enabled
        assertEquals("MS.EXO.7.2v1", changeResult.getPolicyId());
        assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
        assertEquals(ExoConstants.SUCCESS_MAILTIPS_ENABLED, changeResult.getDesc());

        // Verify PowerShell was called once to enable all settings
        verify(mockPowerShellClient, times(1)).executeCmdletCommand(any());
    }

    @Test  
    void testGetPolicyId() {
        // Act & Assert
        assertEquals("MS.EXO.7.2v1", remediator.getPolicyId());
    }
}