package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@Disabled
@ExtendWith(MockitoExtension.class)
class EntraAdminCenterAccessRemediatorTest {

    @Mock
    private MicrosoftGraphClient graphClient;

    private ObjectMapper objectMapper;
    private EntraAdminCenterAccessRemediator remediator;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        remediator = new EntraAdminCenterAccessRemediator(graphClient);
    }

    @Test
    void validateConfiguration_WhenRestrictNonAdminAccessIsTrue_ReturnsTrue() {
        // Arrange
        ObjectNode config = objectMapper.createObjectNode();
        config.put("restrictNonAdminAccess", true);
        
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(config));

        // Act
        CompletableFuture<Boolean> result = remediator.validateConfiguration();

        // Assert
        assertThat(result.join()).isTrue();
        verify(graphClient).makeGraphRequest(argThat(request -> 
            request.getEndpoint().equals("/admin/entra/uxSetting") &&
            request.getMethod() == HttpMethod.GET
        ));
    }

    @Test
    void validateConfiguration_WhenRestrictNonAdminAccessIsFalse_ReturnsFalse() {
        // Arrange
        ObjectNode config = objectMapper.createObjectNode();
        config.put("restrictNonAdminAccess", false);
        
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(config));

        // Act
        CompletableFuture<Boolean> result = remediator.validateConfiguration();

        // Assert
        assertThat(result.join()).isFalse();
    }

    @Test
    void validateConfiguration_WhenRestrictNonAdminAccessIsMissing_ReturnsFalse() {
        // Arrange
        ObjectNode config = objectMapper.createObjectNode();
        // restrictNonAdminAccess field is missing
        
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(config));

        // Act
        CompletableFuture<Boolean> result = remediator.validateConfiguration();

        // Assert
        assertThat(result.join()).isFalse();
    }

    @Test
    void validateConfiguration_WhenGraphRequestFails_ReturnsFalse() {
        // Arrange
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Graph API error")));

        // Act
        CompletableFuture<Boolean> result = remediator.validateConfiguration();

        // Assert
        assertThat(result.join()).isFalse();
    }

    @Test
    void remediate_WhenConfigurationAlreadyValid_ReturnsRequirementMet() {
        // Arrange
        ObjectNode config = objectMapper.createObjectNode();
        config.put("restrictNonAdminAccess", true);
        
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(config));

        // Act
        CompletableFuture<JsonNode> result = remediator.remediate();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("REQUIREMENT_MET");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("already restricted");
    }

    @Test
    void remediate_WhenConfigurationNeedsUpdate_PerformsRemediationAndReturnsSuccess() {
        // Arrange
        ObjectNode currentConfig = objectMapper.createObjectNode();
        currentConfig.put("restrictNonAdminAccess", false);
        
        ObjectNode successResponse = objectMapper.createObjectNode();
        successResponse.put("restrictNonAdminAccess", true);

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.GET)))
                .thenReturn(CompletableFuture.completedFuture(currentConfig));

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.PATCH)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

        // Act
        CompletableFuture<JsonNode> result = remediator.remediate();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("SUCCESS");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Successfully restricted");

        // Verify PATCH request was made with correct body
        ArgumentCaptor<GraphRequest> requestCaptor = ArgumentCaptor.forClass(GraphRequest.class);
        verify(graphClient, times(2)).makeGraphRequest(requestCaptor.capture());
        
        GraphRequest patchRequest = requestCaptor.getAllValues().stream()
                .filter(req -> req.getMethod() == HttpMethod.PATCH)
                .findFirst()
                .orElseThrow();
        
        assertThat(patchRequest.getEndpoint()).isEqualTo("/admin/entra/uxSetting");
    }

    @Test
    void remediate_WhenCannotRetrieveCurrentConfig_ReturnsFailed() {
        // Arrange
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // Act
        CompletableFuture<JsonNode> result = remediator.remediate();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Cannot retrieve current configuration");
    }

    @Test
    void remediate_WhenUpdateFails_ReturnsFailed() {
        // Arrange
        ObjectNode currentConfig = objectMapper.createObjectNode();
        currentConfig.put("restrictNonAdminAccess", false);

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.GET)))
                .thenReturn(CompletableFuture.completedFuture(currentConfig));

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.PATCH)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Update failed")));

        // Act
        CompletableFuture<JsonNode> result = remediator.remediate();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
    }

    @Test
    void rollback_WhenCurrentlyRestricted_PerformsRollbackAndReturnsSuccess() {
        // Arrange
        ObjectNode currentConfig = objectMapper.createObjectNode();
        currentConfig.put("restrictNonAdminAccess", true);
        
        ObjectNode rollbackResponse = objectMapper.createObjectNode();
        rollbackResponse.put("restrictNonAdminAccess", false);

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.GET)))
                .thenReturn(CompletableFuture.completedFuture(currentConfig));

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.PATCH)))
                .thenReturn(CompletableFuture.completedFuture(rollbackResponse));

        // Act
        CompletableFuture<JsonNode> result = remediator.rollback();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("SUCCESS");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Successfully rolled back");

        // Verify rollback request was made with correct body
        ArgumentCaptor<GraphRequest> requestCaptor = ArgumentCaptor.forClass(GraphRequest.class);
        verify(graphClient, times(2)).makeGraphRequest(requestCaptor.capture());
        
        GraphRequest patchRequest = requestCaptor.getAllValues().stream()
                .filter(req -> req.getMethod() == HttpMethod.PATCH)
                .findFirst()
                .orElseThrow();
        
        assertThat(patchRequest.getEndpoint()).isEqualTo("/admin/entra/uxSetting");
    }

    @Test
    void rollback_WhenCannotRetrieveCurrentConfig_ReturnsFailed() {
        // Arrange
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // Act
        CompletableFuture<JsonNode> result = remediator.rollback();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Cannot retrieve current configuration");
    }

    @Test
    void rollback_WhenRollbackUpdateFails_ReturnsFailed() {
        // Arrange
        ObjectNode currentConfig = objectMapper.createObjectNode();
        currentConfig.put("restrictNonAdminAccess", true);

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.GET)))
                .thenReturn(CompletableFuture.completedFuture(currentConfig));

        when(graphClient.makeGraphRequest(argThat(request -> 
            request.getMethod() == HttpMethod.PATCH)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));

        // Act
        CompletableFuture<JsonNode> result = remediator.rollback();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
    }

    @Test
    void getPolicyId_ReturnsCorrectPolicyId() {
        // Act & Assert
        assertThat(remediator.getPolicyId()).isEqualTo("MS.AAD.7.10v1");
    }

    @Test
    void remediate_WhenExceptionOccurs_ReturnsFailed() {
        // Arrange
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Unexpected error")));

        // Act
        CompletableFuture<JsonNode> result = remediator.remediate();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Unexpected error");
    }

    @Test
    void rollback_WhenExceptionOccurs_ReturnsFailed() {
        // Arrange
        when(graphClient.makeGraphRequest(any(GraphRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback exception")));

        // Act
        CompletableFuture<JsonNode> result = remediator.rollback();

        // Assert
        JsonNode response = result.join();
        assertThat(response.get("status").asText()).isEqualTo("FAILED");
        assertThat(response.get("policyId").asText()).isEqualTo("MS.AAD.7.10v1");
        assertThat(response.get("message").asText()).contains("Rollback exception");
    }
}