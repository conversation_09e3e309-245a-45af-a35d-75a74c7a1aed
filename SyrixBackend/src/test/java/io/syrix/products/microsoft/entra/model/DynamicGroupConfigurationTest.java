package io.syrix.products.microsoft.entra.model;

import org.junit.jupiter.api.Test;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for DynamicGroupConfiguration
 */
class DynamicGroupConfigurationTest {

    @Test
    void testConstructorWithDefaults() {
        // Given
        String displayName = "Test Guest Group";
        String description = "Test description for guest users";
        
        // When
        DynamicGroupConfiguration config = new DynamicGroupConfiguration(displayName, description);
        
        // Then
        assertThat(config.getDisplayName()).isEqualTo(displayName);
        assertThat(config.getDescription()).isEqualTo(description);
        assertThat(config.getGroupTypes()).containsExactly("DynamicMembership");
        assertThat(config.getSecurityEnabled()).isTrue();
        assertThat(config.getMailEnabled()).isFalse();
        assertThat(config.getMembershipRule()).isEqualTo("(user.userType -eq \"Guest\")");
        assertThat(config.getMembershipRuleProcessingState()).isEqualTo("On");
        assertThat(config.getMailNickname()).isEqualTo("testguestgroup");
    }

    @Test
    void testIsProperlyConfiguredForGuestUsers_ValidConfiguration() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setSecurityEnabled(true);
        config.setMailEnabled(false);
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setMembershipRule("(user.userType -eq \"Guest\")");
        config.setMembershipRuleProcessingState("On");
        
        // When
        boolean result = config.isProperlyConfiguredForGuestUsers();
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsProperlyConfiguredForGuestUsers_InvalidSecurityEnabled() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setSecurityEnabled(false); // Invalid
        config.setMailEnabled(false);
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setMembershipRule("(user.userType -eq \"Guest\")");
        config.setMembershipRuleProcessingState("On");
        
        // When
        boolean result = config.isProperlyConfiguredForGuestUsers();
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsProperlyConfiguredForGuestUsers_InvalidMembershipRule() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setSecurityEnabled(true);
        config.setMailEnabled(false);
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setMembershipRule("(user.department -eq \"Sales\")"); // Invalid rule
        config.setMembershipRuleProcessingState("On");
        
        // When
        boolean result = config.isProperlyConfiguredForGuestUsers();
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsProperlyConfiguredForGuestUsers_ProcessingDisabled() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setSecurityEnabled(true);
        config.setMailEnabled(false);
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setMembershipRule("(user.userType -eq \"Guest\")");
        config.setMembershipRuleProcessingState("Paused"); // Invalid state
        
        // When
        boolean result = config.isProperlyConfiguredForGuestUsers();
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsMisconfiguredGuestGroup_TrueCase() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setDisplayName("Guest Users Group");
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setSecurityEnabled(true);
        config.setMailEnabled(false);
        config.setMembershipRule("(user.department -eq \"Sales\")"); // Wrong rule
        config.setMembershipRuleProcessingState("On");
        
        // When
        boolean result = config.isMisconfiguredGuestGroup();
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsMisconfiguredGuestGroup_FalseCase_ProperlyConfigured() {
        // Given
        DynamicGroupConfiguration config = new DynamicGroupConfiguration();
        config.setDisplayName("Guest Users Group");
        config.setGroupTypes(List.of("DynamicMembership"));
        config.setSecurityEnabled(true);
        config.setMailEnabled(false);
        config.setMembershipRule("(user.userType -eq \"Guest\")"); // Correct rule
        config.setMembershipRuleProcessingState("On");
        
        // When
        boolean result = config.isMisconfiguredGuestGroup();
        
        // Then
        assertThat(result).isFalse(); // Properly configured, not misconfigured
    }

    @Test
    void testAlternativeMembershipRuleFormats() {
        // Test all supported membership rule formats
        String[] validRules = {
            "(user.userType -eq \"Guest\")",
            "(user.userType -eq 'Guest')",
            "user.userType -eq \"Guest\"",
            "user.userType -eq 'Guest'"
        };
        
        for (String rule : validRules) {
            DynamicGroupConfiguration config = new DynamicGroupConfiguration();
            config.setSecurityEnabled(true);
            config.setMailEnabled(false);
            config.setGroupTypes(List.of("DynamicMembership"));
            config.setMembershipRule(rule);
            config.setMembershipRuleProcessingState("On");
            
            assertThat(config.isProperlyConfiguredForGuestUsers())
                .as("Rule should be valid: %s", rule)
                .isTrue();
        }
    }

    @Test
    void testMailNicknameGeneration() {
        // Given
        String displayName = "All Guest Users (Dynamic) - Test!@#$%";
        String description = "Test description";
        
        // When
        DynamicGroupConfiguration config = new DynamicGroupConfiguration(displayName, description);
        
        // Then
        assertThat(config.getMailNickname())
            .isEqualTo("allguestusersdynamictest")
            .doesNotContain(" ")
            .doesNotContain("(")
            .doesNotContain(")")
            .doesNotContain("-")
            .doesNotContain("!")
            .doesNotContain("@");
    }
}