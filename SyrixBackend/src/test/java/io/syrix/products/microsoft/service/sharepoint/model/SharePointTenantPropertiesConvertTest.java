package io.syrix.products.microsoft.service.sharepoint.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import org.junit.jupiter.api.Test;

public class SharePointTenantPropertiesConvertTest {
    private static final String json = "{\"_ObjectType_\":\"Microsoft.Online.SharePoint.TenantAdministration.Tenant\",\"_ObjectIdentity_\":\"6c3f7aa1-60c0-b000-5a21-3a6dd6ea9caf|908bed80-a04a-4433-b4a0-883d9847d110:0a5f09f2-57fd-456d-9b47-8a7485abaeb4\\nTenant\",\"AIBuilderDefaultPowerAppsEnvironment\":\"\",\"AIBuilderEnabled\":true,\"AIBuilderEnabledInContentCenter\":0,\"AIBuilderSiteListFileName\":null,\"AllOrganizationSecurityGroupId\":\"/Guid(00000000-0000-0000-0000-000000000000)/\",\"AllowAnonymousMeetingParticipantsToAccessWhiteboards\":0,\"AllowCommentsTextOnEmailEnabled\":true,\"AllowDownloadingNonWebViewableFiles\":false,\"AllowedDomainListForSyncClient\":[],\"AllowEditing\":true,\"AllowEveryoneExceptExternalUsersClaimInPrivateSite\":true,\"AllowGuestUserShareToUsersNotInSiteCollection\":false,\"AllowLimitedAccessOnUnmanagedDevices\":true,\"AllowOverrideForBlockUserInfoVisibility\":false,\"AllowSelectSecurityGroupsInSPSitesList\":null,\"AllowSelectSGsInODBListInTenant\":null,\"AllowSensitivityLabelOnRecords\":false,\"AllowSharingOutsideRestrictedAccessControlGroups\":true,\"AllowWebPropertyBagUpdateWhenDenyAddAndCustomizePagesIsEnabled\":false,\"AmplifyAdminSettings\":\"{\\\"EapAccessType\\\":2,\\\"EapGroupDetails\\\":\\\"00000000-0000-0000-0000-000000000000\\\",\\\"CampaignCreationAccessType\\\":0,\\\"CampaignCreationGroupDetails\\\":\\\"00000000-0000-0000-0000-000000000000\\\"}\",\"AnyoneLinkTrackUsers\":false,\"AppBypassInformationBarriers\":false,\"ApplyAppEnforcedRestrictionsToAdHocRecipients\":true,\"AppOnlyBypassPeoplePickerPolicies\":false,\"ArchiveRedirectUrl\":null,\"AuthContextResilienceMode\":0,\"AutofillColumnsEnabled\":true,\"AutofillColumnsSiteListFileName\":\"\",\"BccExternalSharingInvitations\":false,\"BccExternalSharingInvitationsList\":null,\"BlockAccessOnUnmanagedDevices\":false,\"BlockAppAccessWithAuthenticationContext\":false,\"BlockDownloadFileTypeIds\":[],\"BlockDownloadFileTypePolicy\":false,\"BlockDownloadLinksFileType\":1,\"BlockDownloadOfAllFilesForGuests\":false,\"BlockDownloadOfAllFilesOnUnmanagedDevices\":true,\"BlockDownloadOfViewableFilesForGuests\":false,\"BlockDownloadOfViewableFilesOnUnmanagedDevices\":false,\"BlockMacSync\":false,\"BlockSendLabelMismatchEmail\":false,\"BlockUserInfoVisibility\":\"ExternalPeopleInOD\",\"BlockUserInfoVisibilityInOneDrive\":1,\"BlockUserInfoVisibilityInSharePoint\":0,\"BonusStorageQuotaMB\":0,\"BusinessConnectivityServiceDisabled\":false,\"CommentsOnFilesDisabled\":false,\"CommentsOnListItemsDisabled\":false,\"CommentsOnSitePagesDisabled\":false,\"CompatibilityRange\":\"15,15\",\"ConditionalAccessPolicy\":1,\"ConditionalAccessPolicyErrorHelpLink\":\"\",\"ContentSecurityPolicyConfigSynced\":false,\"ContentSecurityPolicyEnforcement\":false,\"ContentTypeSyncSiteTemplatesList\":[],\"CoreBlockGuestsAsSiteAdmin\":0,\"CoreDefaultLinkToExistingAccess\":false,\"CoreDefaultShareLinkRole\":0,\"CoreDefaultShareLinkScope\":-1,\"CoreLoopDefaultSharingLinkRole\":0,\"CoreLoopDefaultSharingLinkScope\":-1,\"CoreLoopSharingCapability\":0,\"CoreRequestFilesLinkEnabled\":false,\"CoreRequestFilesLinkExpirationInDays\":30,\"CoreSharingCapability\":0,\"CustomizedExternalSharingServiceUrl\":\"\",\"DefaultContentCenterSite\":null,\"DefaultLinkPermission\":0,\"DefaultODBMode\":\"Explicit\",\"DefaultSharingLinkType\":2,\"DelayDenyAddAndCustomizePagesEnforcement\":false,\"DelegateRestrictedAccessControlConfiguration\":false,\"DelegateRestrictedContentDiscoveryConfiguration\":false,\"DenySelectSecurityGroupsInSPSitesList\":null,\"DenySelectSGsInODBListInTenant\":null,\"DisableAddToOneDrive\":false,\"DisableBackToClassic\":false,\"DisableCustomAppAuthentication\":false,\"DisabledAdaptiveCardExtensionIds\":null,\"DisabledModernListTemplateIds\":[],\"DisableDocumentLibraryDefaultLabeling\":false,\"DisabledWebPartIds\":null,\"DisableOutlookPSTVersionTrimming\":false,\"DisablePersonalListCreation\":false,\"DisableReportProblemDialog\":false,\"DisableSpacesActivation\":false,\"DisableVivaConnectionsAnalytics\":false,\"DisallowInfectedFileDownload\":false,\"DisplayNamesOfFileViewers\":true,\"DisplayNamesOfFileViewersInSpo\":true,\"DisplayStartASiteOption\":true,\"DocumentUnderstandingEnabled\":true,\"DocumentUnderstandingEnabledInContentCenter\":2,\"DocumentUnderstandingSiteListFileName\":\"\",\"EmailAttestationEnabled\":false,\"EmailAttestationReAuthDays\":30,\"EmailAttestationRequired\":false,\"EnableAIPIntegration\":true,\"EnableAutoExpirationVersionTrim\":false,\"EnableAutoNewsDigest\":true,\"EnableAzureADB2BIntegration\":false,\"EnabledFlightAllowAADB2BSkipCheckingOTP\":true,\"EnableDirectToCustomerBilling\":false,\"EnableDiscoverableByOrganizationForVideos\":true,\"EnableGuestSignInAcceleration\":false,\"EnableMediaReactions\":true,\"EnableMinimumVersionRequirement\":true,\"EnableMipSiteLabel\":false,\"EnablePromotedFileHandlers\":true,\"EnableRestrictedAccessControl\":false,\"EnableSensitivityLabelForPDF\":false,\"EnableSiteArchive\":true,\"EnableTenantRestrictionsInsights\":false,\"ESignatureAppList\":[\"{\\\"AppName\\\":\\\"WordDesktop\\\",\\\"IsEnabled\\\":true}\"],\"ESignatureEnabled\":false,\"ESignatureSiteList\":[],\"ESignatureThirdPartyProviderInfoList\":[\"{\\\"ProviderName\\\":\\\"DocuSign\\\",\\\"IsEnabled\\\":false}\",\"{\\\"ProviderName\\\":\\\"AdobeSign\\\",\\\"IsEnabled\\\":false}\"],\"ExcludedBlockDownloadGroupIds\":[],\"ExcludedFileExtensionsForSyncClient\":[\"\"],\"ExemptNativeUsersFromTenantLevelRestricedAccessControl\":false,\"ExpireVersionsAfterDays\":0,\"ExtendPermissionsToUnprotectedFiles\":false,\"ExternalServicesEnabled\":true,\"ExternalUserExpirationRequired\":false,\"ExternalUserExpireInDays\":60,\"FileAnonymousLinkType\":1,\"FilePickerExternalImageSearchEnabled\":true,\"FileVersionPolicyXml\":\"\",\"FolderAnonymousLinkType\":1,\"GuestSharingGroupAllowListInTenant\":\"\",\"GuestSharingGroupAllowListInTenantByGroupId\":[],\"GuestSharingGroupAllowListInTenantByPrincipalIdentity\":null,\"HasAdminCompletedCUConfiguration\":false,\"HasIntelligentContentServicesCapability\":false,\"HasTopicExperiencesCapability\":false,\"HideSyncButtonOnDocLib\":false,\"HideSyncButtonOnODB\":false,\"IBImplicitGroupBased\":false,\"ImageTaggingOption\":2,\"ImageTaggingSiteListFileName\":\"\",\"IncludeAtAGlanceInShareEmails\":true,\"InformationBarriersSuspension\":true,\"IPAddressAllowList\":\"************,*************,***********,*********/16\",\"IPAddressEnforcement\":false,\"IPAddressWACTokenLifetime\":15,\"IsAppBarTemporarilyDisabled\":false,\"IsCollabMeetingNotesFluidEnabled\":true,\"IsDataAccessInCardDesignerEnabled\":false,\"IsEnableAppAuthPopUpEnabled\":false,\"IsFluidEnabled\":true,\"IsHubSitesMultiGeoFlightEnabled\":true,\"IsLoopEnabled\":true,\"IsMnAFlightEnabled\":false,\"IsMultiGeo\":false,\"IsMultipleHomeSitesFlightEnabled\":false,\"IsMultipleVivaConnectionsFlightEnabled\":true,\"IsUnmanagedSyncClientForTenantRestricted\":false,\"IsUnmanagedSyncClientRestrictionFlightEnabled\":true,\"IsVivaHomeFlightEnabled\":true,\"IsVivaHomeGAFlightEnabled\":true,\"IsWBFluidEnabled\":true,\"LabelMismatchEmailHelpLink\":null,\"LegacyAuthProtocolsEnabled\":true,\"LegacyBrowserAuthProtocolsEnabled\":true,\"LimitedAccessFileType\":1,\"MachineLearningCaptureEnabled\":false,\"MajorVersionLimit\":500,\"MarkAllFilesAsSensitiveByDefault\":false,\"MarkNewFilesSensitiveByDefault\":0,\"MassDeleteNotificationDisabled\":false,\"MediaTranscription\":0,\"MediaTranscriptionAutomaticFeatures\":0,\"MobileFriendlyUrlEnabledInTenant\":true,\"NoAccessRedirectUrl\":null,\"NotificationsInOneDriveForBusinessEnabled\":true,\"NotificationsInSharePointEnabled\":true,\"NotifyOwnersWhenInvitationsAccepted\":false,\"NotifyOwnersWhenItemsReshared\":true,\"OCRAdminSiteListFileName\":\"\",\"OCRComplianceSiteListFileName\":\"\",\"OCRModeForAdminSites\":0,\"OCRModeForComplianceODBs\":0,\"OCRModeForComplianceSites\":0,\"ODBAccessRequests\":0,\"ODBMembersCanShare\":0,\"ODBSensitivityRefreshWindowInHours\":24,\"ODBSharingCapability\":0,\"ODBTranslationEnabled\":true,\"OfficeClientADALDisabled\":false,\"OneDriveBlockGuestsAsSiteAdmin\":0,\"OneDriveDefaultLinkToExistingAccess\":false,\"OneDriveDefaultShareLinkRole\":0,\"OneDriveDefaultShareLinkScope\":-1,\"OneDriveForGuestsEnabled\":false,\"OneDriveLoopDefaultSharingLinkRole\":0,\"OneDriveLoopDefaultSharingLinkScope\":-1,\"OneDriveLoopSharingCapability\":0,\"OneDriveRequestFilesLinkEnabled\":false,\"OneDriveRequestFilesLinkExpirationInDays\":30,\"OneDriveStorageQuota\":4194304,\"OptOutOfGrooveBlock\":false,\"OptOutOfGrooveSoftBlock\":false,\"OrgNewsSiteUrl\":null,\"OrphanedPersonalSitesRetentionPeriod\":30,\"OwnerAnonymousNotification\":true,\"PermissiveBrowserFileHandlingOverride\":false,\"PrebuiltEnabled\":true,\"PrebuiltEnabledInContentCenter\":2,\"PrebuiltSiteListFileName\":\"\",\"PreventExternalUsersFromResharing\":true,\"ProvisionSharedWithEveryoneFolder\":false,\"PublicCdnAllowedFileTypes\":\"CSS,EOT,GIF,ICO,JPEG,JPG,JS,MAP,PNG,SVG,TTF,WOFF\",\"PublicCdnEnabled\":false,\"PublicCdnOrigins\":[],\"ReactivationCost\":0.6,\"RecycleBinRetentionPeriod\":93,\"ReduceTempTokenLifetimeEnabled\":false,\"ReduceTempTokenLifetimeValue\":15,\"RequireAcceptingAccountMatchInvitedAccount\":false,\"RequireAnonymousLinksExpireInDays\":30,\"ResourceQuota\":0,\"ResourceQuotaAllocated\":0,\"RestrictedAccessControlForOneDriveErrorHelpLink\":\"None\",\"RestrictedAccessControlforSitesErrorHelpLink\":\"None\",\"RestrictedOneDriveLicense\":false,\"RestrictedSharePointLicense\":false,\"RootSiteUrl\":\"https://cloudally1.sharepoint.com\",\"SearchResolveExactEmailOrUPN\":false,\"SelfServiceSiteCreationDisabled\":false,\"SharePointAddInsDisabled\":false,\"SharingAllowedDomainList\":null,\"SharingBlockedDomainList\":null,\"SharingCapability\":0,\"SharingDomainRestrictionMode\":0,\"ShowAllUsersClaim\":false,\"ShowEveryoneClaim\":false,\"ShowEveryoneExceptExternalUsersClaim\":true,\"ShowNGSCDialogForSyncOnODB\":true,\"ShowOpenInDesktopOptionForSyncedFiles\":false,\"ShowPeoplePickerGroupSuggestionsForIB\":false,\"ShowPeoplePickerSuggestionsForGuestUsers\":false,\"SignInAccelerationDomain\":\"\",\"SiteOwnerManageLegacyServicePrincipalEnabled\":false,\"SocialBarOnSitePagesDisabled\":false,\"SpecialCharactersStateInFileFolderNames\":1,\"SPJitDlpPolicyData\":null,\"StartASiteFormUrl\":null,\"StopNew2010Workflows\":false,\"StopNew2013Workflows\":false,\"StorageQuota\":1212416,\"StorageQuotaAllocated\":0,\"StreamLaunchConfig\":0,\"StreamLaunchConfigLastUpdated\":\"/Date(1,0,1,0,0,0,0)/\",\"StreamLaunchConfigUpdateCount\":0,\"SyncPrivacyProfileProperties\":true,\"TaxonomyTaggingEnabled\":true,\"TaxonomyTaggingSiteListFileName\":\"\",\"TlsTokenBindingPolicyValue\":0,\"TranslationEnabled\":true,\"TranslationSiteListFileName\":\"\",\"UniversalAnnotationDisabled\":false,\"UnlicensedOdbSyntexBillingEnabled\":false,\"UnlicensedOneDriveForBusinessTenantMetricsData\":{\"_ObjectType_\":\"Microsoft.SharePoint.Administration.OdbLicenseEnforcement.UnlicensedOdbTenantMetrics\",\"billableCount\":0,\"billableSizeBytes\":0,\"complianceHoldCount\":0,\"complianceHoldSizeBytes\":0,\"count\":70,\"duplicateSiteCount\":2,\"duplicateSiteSizeBytes\":**********,\"invalidLicenseCount\":68,\"invalidLicenseSizeBytes\":***********,\"lastRefreshOn\":\"/Date(1737513293257)/\",\"restoredByTenantAdminCount\":0,\"restoredByTenantAdminSizeBytes\":0,\"retentionPeriodCount\":0,\"retentionPeriodSizeBytes\":0,\"sizeBytes\":***********},\"UseFindPeopleInPeoplePicker\":false,\"UsePersistentCookiesForExplorerView\":false,\"ViewersCanCommentOnMediaDisabled\":false,\"ViewInFileExplorerEnabled\":false,\"WhoCanShareAllowListInTenant\":\"\",\"WhoCanShareAllowListInTenantByGroupId\":[],\"WhoCanShareAllowListInTenantByPrincipalIdentity\":null,\"Workflow2010Disabled\":false,\"Workflows2013State\":2,\"HideDefaultThemes\":false}";

    @Test
    public void testConvert() throws JsonProcessingException {
        JsonMapper.builder().configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true).build().readValue(json, TenantProperties.class);
    }

}
