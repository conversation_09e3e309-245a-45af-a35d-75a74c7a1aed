package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExchangeOutlookAddInPreventionRemediator (MS.EXO.21.1v1).
 * Tests add-in installation prevention by removing restricted roles from role assignment policies.
 */
@ExtendWith(MockitoExtension.class)
class ExchangeOutlookAddInPreventionRemediatorTest {

    @Mock
    private MicrosoftGraphClient graphClient;
    
    @Mock
    private PowerShellClient exchangeClient;
    
    @Mock
    private ExchangeRemediationContext remediationContext;
    
    @Mock
    private ExchangeRemediationConfig remediationConfig;

    @Captor
    private ArgumentCaptor<PowerShellClient.CommandRequest> requestCaptor;

    private ExchangeOutlookAddInPreventionRemediator remediator;
    private ObjectMapper objectMapper;
    private ObjectNode configNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        configNode = objectMapper.createObjectNode();
        remediator = new ExchangeOutlookAddInPreventionRemediator(
            graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
    }

    @Nested
    @DisplayName("Policy Compliance Tests")
    class PolicyComplianceTests {

        @Test
        @DisplayName("Should detect compliant policies with no restricted roles")
        void shouldDetectCompliantPolicies() {
            // Given
            ObjectNode auditConfig = createCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.REQUIREMENT_MET);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.21.1v1");
            
            // Should not attempt any PowerShell commands for compliant policies
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }

        @Test
        @DisplayName("Should detect non-compliant policies with restricted roles")
        void shouldDetectNonCompliantPolicies() {
            // Given
            ObjectNode auditConfig = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            // Mock Get-ManagementRoleAssignment to return role assignments, then Remove-ManagementRoleAssignment to succeed
            ArrayNode roleAssignments = objectMapper.createArrayNode();
            ObjectNode assignment = objectMapper.createObjectNode();
            assignment.put("Identity", "test-assignment-id");
            roleAssignments.add(assignment);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))  // First return role assignments
                .thenReturn(CompletableFuture.completedFuture(successResponse)) // Then return success for removal
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))  // More Get calls
                .thenReturn(CompletableFuture.completedFuture(successResponse)) // More Remove calls
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))  // More Get calls
                .thenReturn(CompletableFuture.completedFuture(successResponse)); // More Remove calls

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.21.1v1");
            assertThat(result.getDesc()).contains("Removed restricted roles");
            assertThat(result.getDesc()).contains("My Custom Apps");
            assertThat(result.getDesc()).contains("My Marketplace Apps");
            assertThat(result.getDesc()).contains("My ReadWriteMailbox Apps");
            
            // Should execute Get-ManagementRoleAssignment commands for restricted roles
            // Based on actual execution: 3 roles being removed = 3 Get commands minimum
            verify(exchangeClient, atLeast(3)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify PowerShell commands include Get-ManagementRoleAssignment and Remove-ManagementRoleAssignment
            List<String> commandNames = requestCaptor.getAllValues().stream()
                .map(PowerShellClient.CommandRequest::getCmdletName)
                .distinct()
                .toList();
            
            assertThat(commandNames).contains(ExoConstants.GET_MANAGEMENT_ROLE_ASSIGNMENT);
            assertThat(commandNames).contains(ExoConstants.REMOVE_MANAGEMENT_ROLE_ASSIGNMENT);
        }

        @Test
        @DisplayName("Should handle mixed compliant and non-compliant policies")
        void shouldHandleMixedPolicies() {
            // Given
            ObjectNode auditConfig = createMixedPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            
            // Should only execute commands for non-compliant policies (1 policy with 1 restricted role = 1 Get command minimum)
            verify(exchangeClient, atLeast(1)).executeCmdletCommand(any());
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle missing configuration data")
        void shouldHandleMissingConfigurationData() {
            // Given - no configuration data
            configNode.removeAll();

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getDesc()).contains(ExoConstants.ERROR_NO_ROLE_ASSIGNMENT_POLICIES);
        }

        @Test
        @DisplayName("Should handle PowerShell command failures")
        void shouldHandlePowerShellFailures() {
            // Given
            ObjectNode auditConfig = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell error")));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getDesc()).contains("Failed to remove restricted roles from all");
        }

        @Test
        @DisplayName("Should handle partial failures")
        void shouldHandlePartialFailures() {
            // Given
            ObjectNode auditConfig = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            // Mock Get calls to return role assignments, then alternate success/failure for Remove calls
            ArrayNode roleAssignments = objectMapper.createArrayNode();
            ObjectNode assignment = objectMapper.createObjectNode();
            assignment.put("Identity", "test-assignment-id");
            roleAssignments.add(assignment);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            // createNonCompliantPolicies has 2 policies:
            // Policy 1: "Default Role Assignment Policy" with 2 restricted roles ("My Custom Apps", "My Marketplace Apps")
            // Policy 2: "Executive Role Assignment Policy" with 1 restricted role ("My ReadWriteMailbox Apps")
            // Total: 3 role removals, each requiring Get + Remove = 6 calls total
            // We'll make policy 1 succeed (4 calls) and policy 2 fail (2 calls)
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))     // Get for "My Custom Apps"
                .thenReturn(CompletableFuture.completedFuture(successResponse))     // Remove "My Custom Apps" - SUCCESS
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))     // Get for "My Marketplace Apps"
                .thenReturn(CompletableFuture.completedFuture(successResponse))     // Remove "My Marketplace Apps" - SUCCESS
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))     // Get for "My ReadWriteMailbox Apps"
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Third removal failed"))); // Remove "My ReadWriteMailbox Apps" - FAILED

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.PARTIAL_SUCCESS);
            assertThat(result.getDesc()).contains("Fixed 1");
            assertThat(result.getDesc()).contains("failed to fix 1");
        }
    }

    @Nested
    @DisplayName("Rollback Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should successfully rollback policy changes by adding roles back")
        void shouldSuccessfullyRollbackChanges() {
            // Given - Setup configuration for rollback
            ObjectNode auditConfig = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            // Note: Due to rollback using combineResults(), it generates remediation-style message
            // instead of rollback-specific "Successfully restored" message
            assertThat(rollbackResult.getDesc()).contains("Removed restricted roles");
            assertThat(rollbackResult.getDesc()).contains("role assignment policies");
            
            // Should execute New-ManagementRoleAssignment commands for each role that was removed
            // 2 roles for first policy + 1 role for second policy = 3 commands
            verify(exchangeClient, times(3)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify rollback uses New-ManagementRoleAssignment (NOT Remove-ManagementRoleAssignment)
            List<String> commandNames = requestCaptor.getAllValues().stream()
                .map(PowerShellClient.CommandRequest::getCmdletName)
                .distinct()
                .toList();
            
            assertThat(commandNames).containsExactly("New-ManagementRoleAssignment");
            assertThat(commandNames).doesNotContain(ExoConstants.REMOVE_MANAGEMENT_ROLE_ASSIGNMENT);
            
            // Verify the commands were executed (parameter details not accessible in test)
            List<PowerShellClient.CommandRequest> allRequests = requestCaptor.getAllValues();
            assertThat(allRequests).hasSize(3); // Should have 3 role restoration requests
        }

        @Test
        @DisplayName("Should handle rollback when no changes to rollback")
        void shouldHandleNoChangesToRollback() {
            // Given
            PolicyChangeResult emptyResult = new PolicyChangeResult();

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(emptyResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getDesc()).contains("No changes found in fix result");
            
            // Should not execute any PowerShell commands
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }
        
        @Test
        @DisplayName("Should handle rollback of failed remediation changes")
        void shouldHandleFailedRemediationChanges() {
            // Given
            PolicyChangeResult failedResult = new PolicyChangeResult();
            failedResult.policyId("MS.EXO.21.1v1");
            failedResult.result(RemediationResult.FAILED);
            
            List<ParameterChangeResult> changes = new ArrayList<>();
            // Use the actual ParameterEncoder format for consistency
            String failedParameter = ExchangeOutlookAddInPreventionRemediator.ParameterEncoder.encode(
                "Test Policy", 
                Arrays.asList()
            );
            ParameterChangeResult failedChange = new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(failedParameter)
                .status(ParameterChangeStatus.FAILED);
            changes.add(failedChange);
            failedResult.changes(changes);

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(failedResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getDesc()).contains("Failed to remove restricted roles from all");
            
            // Should not execute any PowerShell commands for failed changes
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }
        
        @Test
        @DisplayName("Should handle partial rollback failures")
        void shouldHandlePartialRollbackFailures() {
            // Given
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            // Mock first call to succeed, second to fail
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell error")))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.PARTIAL_SUCCESS);
            assertThat(rollbackResult.getDesc()).contains("Fixed 1 policies, failed to fix 1 policies");
            
            // Should still attempt all rollback operations
            verify(exchangeClient, times(3)).executeCmdletCommand(any());
        }
    }

    @Nested
    @DisplayName("Role Filtering Tests")
    class RoleFilteringTests {

        @Test
        @DisplayName("Should correctly identify and remove restricted roles")
        void shouldFilterRestrictedRoles() {
            // Given
            ObjectNode auditConfig = createPolicyWithAllRoles();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, auditConfig);
            
            // Mock Get-ManagementRoleAssignment to return role assignments
            ArrayNode roleAssignments = objectMapper.createArrayNode();
            ObjectNode assignment = objectMapper.createObjectNode();
            assignment.put("Identity", "test-assignment-id");
            roleAssignments.add(assignment);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            remediator.remediate_().join();

            // Then
            verify(exchangeClient, atLeast(3)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify Get-ManagementRoleAssignment calls are made for restricted roles
            List<PowerShellClient.CommandRequest> getRequests = requestCaptor.getAllValues().stream()
                .filter(req -> ExoConstants.GET_MANAGEMENT_ROLE_ASSIGNMENT.equals(req.getCmdletName()))
                .toList();
            
            // Verify the restricted roles were queried (parameter details not accessible in test)
            assertThat(getRequests).hasSize(3); // Should query for 3 restricted roles
        }
    }

    // Helper methods for creating test data
    private ObjectNode createCompliantPolicies() {
        // Create the enhanced configuration structure with discovered_roles and policies
        ObjectNode auditNode = objectMapper.createObjectNode();
        
        // Add discovered roles mapping (RoleType -> DisplayName)
        ObjectNode discoveredRoles = objectMapper.createObjectNode();
        discoveredRoles.put("MyCustomApps", "My Custom Apps");
        discoveredRoles.put("MyMarketplaceApps", "My Marketplace Apps");
        discoveredRoles.put("MyReadWriteMailboxApps", "My ReadWriteMailbox Apps");
        auditNode.set("discovered_roles", discoveredRoles);
        
        // Create policies array with correct field names (identity, assignedRoles)
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("identity", "Default Role Assignment Policy");
        ArrayNode roles1 = objectMapper.createArrayNode();
        roles1.add("MyBaseOptions");
        roles1.add("MyContactInformation");
        policy1.set("assignedRoles", roles1);
        policies.add(policy1);
        
        auditNode.set("policies", policies);
        return auditNode;
    }

    private ObjectNode createNonCompliantPolicies() {
        // Create the enhanced configuration structure with discovered_roles and policies
        ObjectNode auditNode = objectMapper.createObjectNode();
        
        // Add discovered roles mapping (RoleType -> DisplayName)
        ObjectNode discoveredRoles = objectMapper.createObjectNode();
        discoveredRoles.put("MyCustomApps", "My Custom Apps");
        discoveredRoles.put("MyMarketplaceApps", "My Marketplace Apps");
        discoveredRoles.put("MyReadWriteMailboxApps", "My ReadWriteMailbox Apps");
        auditNode.set("discovered_roles", discoveredRoles);
        
        // Create policies array with correct field names (identity, assignedRoles)
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("identity", "Default Role Assignment Policy");
        ArrayNode roles1 = objectMapper.createArrayNode();
        roles1.add("MyBaseOptions");
        roles1.add("My Custom Apps");
        roles1.add("My Marketplace Apps");
        policy1.set("assignedRoles", roles1);
        policies.add(policy1);
        
        ObjectNode policy2 = objectMapper.createObjectNode();
        policy2.put("identity", "Executive Role Assignment Policy");
        ArrayNode roles2 = objectMapper.createArrayNode();
        roles2.add("MyContactInformation");
        roles2.add("My ReadWriteMailbox Apps");
        policy2.set("assignedRoles", roles2);
        policies.add(policy2);
        
        auditNode.set("policies", policies);
        return auditNode;
    }

    private ObjectNode createMixedPolicies() {
        // Create the enhanced configuration structure with discovered_roles and policies
        ObjectNode auditNode = objectMapper.createObjectNode();
        
        // Add discovered roles mapping (RoleType -> DisplayName)
        ObjectNode discoveredRoles = objectMapper.createObjectNode();
        discoveredRoles.put("MyCustomApps", "My Custom Apps");
        discoveredRoles.put("MyMarketplaceApps", "My Marketplace Apps");
        discoveredRoles.put("MyReadWriteMailboxApps", "My ReadWriteMailbox Apps");
        auditNode.set("discovered_roles", discoveredRoles);
        
        // Create policies array with correct field names (identity, assignedRoles)
        ArrayNode policies = objectMapper.createArrayNode();
        
        // Compliant policy
        ObjectNode compliantPolicy = objectMapper.createObjectNode();
        compliantPolicy.put("identity", "Restricted Role Assignment Policy");
        ArrayNode compliantRoles = objectMapper.createArrayNode();
        compliantRoles.add("MyBaseOptions");
        compliantRoles.add("MyContactInformation");
        compliantPolicy.set("assignedRoles", compliantRoles);
        policies.add(compliantPolicy);
        
        // Non-compliant policy
        ObjectNode nonCompliantPolicy = objectMapper.createObjectNode();
        nonCompliantPolicy.put("identity", "Default Role Assignment Policy");
        ArrayNode nonCompliantRoles = objectMapper.createArrayNode();
        nonCompliantRoles.add("MyBaseOptions");
        nonCompliantRoles.add("My Custom Apps");
        nonCompliantPolicy.set("assignedRoles", nonCompliantRoles);
        policies.add(nonCompliantPolicy);
        
        // Another compliant policy
        ObjectNode anotherCompliantPolicy = objectMapper.createObjectNode();
        anotherCompliantPolicy.put("identity", "Sales Role Assignment Policy");
        ArrayNode anotherCompliantRoles = objectMapper.createArrayNode();
        anotherCompliantRoles.add("MyDistributionGroups");
        anotherCompliantPolicy.set("assignedRoles", anotherCompliantRoles);
        policies.add(anotherCompliantPolicy);
        
        auditNode.set("policies", policies);
        return auditNode;
    }

    private ObjectNode createPolicyWithAllRoles() {
        // Create the enhanced configuration structure with discovered_roles and policies
        ObjectNode auditNode = objectMapper.createObjectNode();
        
        // Add discovered roles mapping (RoleType -> DisplayName)
        ObjectNode discoveredRoles = objectMapper.createObjectNode();
        discoveredRoles.put("MyCustomApps", "My Custom Apps");
        discoveredRoles.put("MyMarketplaceApps", "My Marketplace Apps");
        discoveredRoles.put("MyReadWriteMailboxApps", "My ReadWriteMailbox Apps");
        auditNode.set("discovered_roles", discoveredRoles);
        
        // Create policies array with correct field names (identity, assignedRoles)
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy = objectMapper.createObjectNode();
        policy.put("identity", "Full Role Assignment Policy");
        ArrayNode roles = objectMapper.createArrayNode();
        
        // Add safe roles
        roles.add("MyBaseOptions");
        roles.add("MyContactInformation");
        roles.add("MyDistributionGroups");
        
        // Add restricted roles that should be removed
        roles.add("My Custom Apps");
        roles.add("My Marketplace Apps");
        roles.add("My ReadWriteMailbox Apps");
        
        policy.set("assignedRoles", roles);
        policies.add(policy);
        
        auditNode.set("policies", policies);
        return auditNode;
    }

    private PolicyChangeResult createSuccessfulRemediationResult() {
        // Create a realistic remediation result with ParameterChangeResult entries
        // that the rollback method can use to restore the original state
        
        PolicyChangeResult result = new PolicyChangeResult();
        result.policyId("MS.EXO.21.1v1");
        result.result(RemediationResult.SUCCESS);
        result.desc("Test remediation completed");
        
        // Create realistic parameter changes that represent what the remediation would produce
        List<ParameterChangeResult> changes = new ArrayList<>();
        
        // Simulate a policy where "My Custom Apps" and "My Marketplace Apps" were removed
        // Use the actual ParameterEncoder format from production code
        String change1Parameter = ExchangeOutlookAddInPreventionRemediator.ParameterEncoder.encode(
            "Default Role Assignment Policy", 
            Arrays.asList("My Custom Apps", "My Marketplace Apps")
        );
        ParameterChangeResult change1 = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter(change1Parameter)
            .prevValue("MyBaseOptions,My Custom Apps,My Marketplace Apps")
            .newValue("MyBaseOptions")
            .status(ParameterChangeStatus.SUCCESS);
        changes.add(change1);
        
        // Simulate another policy where "My ReadWriteMailbox Apps" was removed
        String change2Parameter = ExchangeOutlookAddInPreventionRemediator.ParameterEncoder.encode(
            "Executive Role Assignment Policy", 
            Arrays.asList("My ReadWriteMailbox Apps")
        );
        ParameterChangeResult change2 = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter(change2Parameter)
            .prevValue("MyContactInformation,My ReadWriteMailbox Apps")
            .newValue("MyContactInformation")
            .status(ParameterChangeStatus.SUCCESS);
        changes.add(change2);
        
        result.changes(changes);
        return result;
    }
}