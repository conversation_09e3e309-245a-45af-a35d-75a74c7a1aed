package io.syrix.defender.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.utils.TestUtils;
import io.syrix.products.microsoft.defender.DefenderConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefenderConfigurationServiceTest {

	@Mock
	private MicrosoftGraphClient graphClient;
	private PowerShellClient powerShellClient;

	@Captor
	private ArgumentCaptor<GraphRequest> requestCaptor;

	private DefenderConfigurationService service;
	private ObjectMapper objectMapper;

	@BeforeEach
	void setUp() {
	objectMapper = new ObjectMapper();
	powerShellClient = mock(PowerShellClient.class);
	when(powerShellClient.getCPPSEndpoint()).thenReturn(CompletableFuture.completedFuture("https://test-cpps.protection.outlook.com"));
		service = new DefenderConfigurationService(graphClient, powerShellClient);
	}

	@Nested
	@Disabled
	@DisplayName("Defender Configuration Export Tests")
	class ConfigurationExportTests {

		@Test
		@DisplayName("Should successfully export Defender configuration")
		void shouldExportConfiguration() throws Exception {
			// Given
			JsonNode antiPhishData = TestUtils.loadTestData("defender/anti-phish-policies.json");
			JsonNode atpData = TestUtils.loadTestData("defender/atp-policies.json");

			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(antiPhishData))
					.thenReturn(CompletableFuture.completedFuture(atpData));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result)
					.isNotNull()
					.satisfies(r -> {
						assertThat(r.getData()).isNotNull();
						assertThat(r.getData().get("anti_phish_policies")).isNotNull();
						assertThat(r.getData().get("atp_policies")).isNotNull();
					});

			verify(graphClient, atLeast(2)).makeGraphRequest(requestCaptor.capture());
			List<GraphRequest> requests = requestCaptor.getAllValues();

			assertThat(requests)
					.extracting(GraphRequest::getEndpoint)
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/threatProtection/targetedProtectionPolicies"))
					.anySatisfy(endpoint -> assertThat(endpoint).contains("/threatProtection/advancedThreatProtectionPolicies"));
		}

		@Test
		@DisplayName("Should handle export failures with retries")
		void shouldHandleExportFailuresWithRetries() {
			// Given
			when(graphClient.makeGraphRequest(any(GraphRequest.class)))
					.thenReturn(CompletableFuture.failedFuture(
							new RuntimeException("API Error")
					));

			// When & Then
			assertThatThrownBy(() -> service.exportConfiguration())
					.isInstanceOf(ConfigurationExportException.class)
					.hasMessageContaining("Defender configuration export failed");

			verify(graphClient, atLeast(3)).makeGraphRequest(any(GraphRequest.class));
		}
	}

	@Nested
	@Disabled
	@DisplayName("Individual Configuration Tests")
	class IndividualConfigTests {

		@Test
		@DisplayName("Should correctly fetch anti-phishing policies")
		void shouldFetchAntiPhishingPolicies() throws Exception {
			// Given
			JsonNode antiPhishData = TestUtils.loadTestData("defender/anti-phish-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/threatProtection/targetedProtectionPolicies"))))
					.thenReturn(CompletableFuture.completedFuture(antiPhishData));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("anti_phish_policies"))
					.isNotNull()
					.satisfies(policies -> {
						JsonNode policiesData = (JsonNode) policies;
						assertThat(policiesData.isArray()).isTrue();
						if (policiesData.size() > 0) {
							assertThat(policiesData.get(0).has("displayName")).isTrue();
							assertThat(policiesData.get(0).has("isEnabled")).isTrue();
						}
					});
		}

		@Test
		@DisplayName("Should correctly fetch DLP policies")
		void shouldFetchDLPPolicies() throws Exception {
			// Given
			JsonNode dlpData = TestUtils.loadTestData("defender/dlp-policies.json");
			when(graphClient.makeGraphRequest(argThat(req ->
					req.getEndpoint().contains("/informationProtection/policies"))))
					.thenReturn(CompletableFuture.completedFuture(dlpData));

			// When
			ConfigurationResult result = service.exportConfiguration();

			// Then
			assertThat(result.getData().get("dlp_policies"))
					.isNotNull()
					.satisfies(policies -> {
						JsonNode policiesData = (JsonNode) policies;
						assertThat(policiesData.isArray()).isTrue();
						if (policiesData.size() > 0) {
							assertThat(policiesData.get(0).has("displayName")).isTrue();
							assertThat(policiesData.get(0).has("status")).isTrue();
						}
					});
		}
	}
}