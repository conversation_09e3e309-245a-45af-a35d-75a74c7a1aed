package io.syrix.protocols.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Test class for PaginatedResult using JUnit Jupiter 5.11.3.
 * Demonstrates various testing approaches including parameterized tests.
 */
@DisplayName("PaginatedResult Tests")
class PaginatedResultTest {

	private final ObjectMapper objectMapper = new ObjectMapper();

	@Nested
	@DisplayName("Constructor Tests")
	class ConstructorTests {

		@Test
		@DisplayName("Should create instance with all values provided")
		void shouldCreateWithAllValues() {
			// Given
			List<JsonNode> values = Arrays.asList(
					createTestNode("1"),
					createTestNode("2")
			);
			String nextLink = "https://graph.microsoft.com/v1.0/users?$skip=100";
			int totalCount = 150;

			// When
			PaginatedResult result = new PaginatedResult(values, nextLink, totalCount);

			// Then
			assertThat(result.getValues()).hasSize(2);
			assertThat(result.getNextLink()).isEqualTo(nextLink);
			assertThat(result.getTotalCount()).isEqualTo(totalCount);
		}
	}

	@Nested
	@DisplayName("Pagination Behavior Tests")
	class PaginationBehaviorTests {

		@ParameterizedTest(name = "NextLink value: {0}")
		@MethodSource("nextLinkTestCases")
		@DisplayName("Should handle various nextLink values")
		void shouldHandleNextLinkValues(String nextLink, boolean expectedHasNext) {
			// When
			PaginatedResult result = PaginatedResult.builder()
					.withValues(Collections.emptyList())
					.withNextLink(nextLink)
					.build();

			// Then
			assertThat(result.hasNextPage())
					.as("NextLink '%s' should indicate hasNextPage=%s", nextLink, expectedHasNext)
					.isEqualTo(expectedHasNext);
		}

		// Method source for nextLink test cases
		static Stream<Arguments> nextLinkTestCases() {
			return Stream.of(
					Arguments.of(null, false),
					Arguments.of("", false),
					Arguments.of("   ", false),
					Arguments.of("https://next.page", true)
			);
		}

		@Test
		@DisplayName("Should maintain values immutability")
		void shouldMaintainValuesImmutability() {
			// Given
			List<JsonNode> values = Arrays.asList(createTestNode("1"));
			PaginatedResult result = PaginatedResult.builder()
					.withValues(values)
					.build();

			// Then
			assertThatThrownBy(() -> result.getValues().add(createTestNode("2")))
					.isInstanceOf(UnsupportedOperationException.class);
			// We don't check the message content as it varies by JDK implementation
		}
	}

	@Nested
	@DisplayName("Builder Tests")
	class BuilderTests {

		@ParameterizedTest(name = "Total count: {0}")
		@MethodSource("totalCountTestCases")
		@DisplayName("Should handle different total count values")
		void shouldHandleTotalCountValues(Integer totalCount, boolean shouldHaveCount) {
			// When
			PaginatedResult result = PaginatedResult.builder()
					.withValues(Collections.emptyList())
					.withTotalCount(totalCount)
					.build();

			// Then
			if (shouldHaveCount) {
				assertThat(result.getTotalCount())
						.as("Total count should be present")
						.isEqualTo(totalCount);
			} else {
				assertThat(result.getTotalCount())
						.as("Total count should be null")
						.isNull();
			}
		}

		// Method source for total count test cases
		static Stream<Arguments> totalCountTestCases() {
			return Stream.of(
					Arguments.of(null, false),
					Arguments.of(0, true),
					Arguments.of(100, true),
					Arguments.of(-1, true)
			);
		}
	}

	/**
	 * Creates a test JsonNode with the specified ID.
	 * Helper method for creating test data.
	 */
	private JsonNode createTestNode(String id) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put("id", id);
		return node;
	}
}