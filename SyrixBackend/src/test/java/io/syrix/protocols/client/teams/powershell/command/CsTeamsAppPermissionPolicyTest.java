package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CsTeamsAppPermissionPolicyTest {
    public final String body = """
            [{"DefaultCatalogApps":[],"GlobalCatalogApps":[],"PrivateCatalogApps":[],"Description":null,"DefaultCatalogAppsType":"BlockedAppList","GlobalCatalogAppsType":"BlockedAppList","PrivateCatalogAppsType":"BlockedAppList","DataSource":null,"Key":{"ScopeClass":"Global","SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsAppPermissionPolicy"}},"AuthorityId":{"Class":"Tenant","InstanceId":"763c49e2-afb5-446b-b41e-e2521fed4802","XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AuthorityId"}},"DefaultXml":{"SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsAppPermissionPolicy"}},"Data":{"TeamsAppPermissionPolicy":{"@xmlns":"urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017","DefaultCatalogApps":null,"GlobalCatalogApps":null,"PrivateCatalogApps":null}},"ConfigObject":null,"Signature":"00000000-0000-0000-0000-000000000000","IsModified":true},"XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AnchoredXmlKey"}},"Identity":"Global","ConfigMetadata":{"Authority":"Tenant"},"ConfigId":"Global"}]
            """;

    @Test
    public void testParse() throws Exception {
        final CsTeamsCommand<AppPermissionPolicy> command = CsTeamsCommand.CsTeamsAppPermissionPolicy.GET();
        List<AppPermissionPolicy> res = command.parseResponse(body);
        assertEquals(1, res.size());
    }

}