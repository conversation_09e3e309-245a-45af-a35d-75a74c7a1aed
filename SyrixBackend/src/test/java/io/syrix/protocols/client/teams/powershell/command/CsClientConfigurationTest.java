package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.teams.powershell.command.types.ClientConfiguration;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CsClientConfigurationTest {
    private final String body = """
            [{"AllowEmailIntoChannel":true,"RestrictedSenderList":null,"AllowDropBox":true,"AllowBox":true,"AllowGoogleDrive":true,"AllowShareFile":true,"AllowEgnyte":true,"AllowOrganizationTab":true,"AllowSkypeBusinessInterop":true,"ContentPin":"RequiredOutsideScheduleMeeting","AllowResourceAccountSendMessage":true,"ResourceAccountContentAccess":"NoAccess","AllowGuestUser":true,"AllowScopedPeopleSearchandAccess":false,"AllowRoleBasedChatPermissions":false,"ExtendedWorkInfoInPeopleSearch":false,"DataSource":null,"Key":{"ScopeClass":"Global","SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Settings.TeamsConfiguration.2016}TeamsClientConfiguration"}},"AuthorityId":{"Class":"Tenant","InstanceId":"763c49e2-afb5-446b-b41e-e2521fed4802","XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AuthorityId"}},"DefaultXml":{"SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Settings.TeamsConfiguration.2016}TeamsClientConfiguration"}},"Data":{"TeamsClientConfiguration":{"@xmlns":"urn:schema:Microsoft.Rtc.Management.Settings.TeamsConfiguration.2016"}},"ConfigObject":null,"Signature":"********-0000-0000-0000-************","IsModified":true},"XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AnchoredXmlKey"}},"Identity":"Global","ConfigMetadata":{"Authority":"Tenant"},"ConfigId":"Global"}]
            """;

    @Test
    void parseResponse() throws Exception {
        final CsTeamsCommand<ClientConfiguration> command = CsTeamsCommand.CsTeamsClientConfiguration.GET();
        List<ClientConfiguration> res = command.parseResponse(body);
        assertEquals(1, res.size());
    }
}