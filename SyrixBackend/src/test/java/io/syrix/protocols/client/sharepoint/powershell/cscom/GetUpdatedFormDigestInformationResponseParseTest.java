package io.syrix.protocols.client.sharepoint.powershell.cscom;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import io.syrix.protocols.client.sharepoint.powershell.cscom.response.GetUpdatedFormDigestInformationResponse;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class GetUpdatedFormDigestInformationResponseParseTest {
    @Test
    public void testParse() throws JsonProcessingException {
        String expectedDigestValue = "0xFC583CDA90321A8CDD6B5EA7785379A767C920CF27BA817308EB9C448E430B1012A1BAA3102A30DB3F34C8210F5DE0F177AB269F1093DACD445E63AA97BE769D,06 Jan 2025 18:20:40 -0000";
        Integer expectedTimeoutSeconds = 1800;
        String expectedWebFullUrl="https://cloudally1-admin.sharepoint.com";
        String expectedLibraryVersion="16.0.25520.12010";
        String expectedSupportedSchemaVersions="14.0.0.0,15.0.0.0";

        String xml = """
                <?xml version="1.0" encoding="utf-8" standalone="no"?>
                <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <soap:Body>
                        <GetUpdatedFormDigestInformationResponse xmlns="http://schemas.microsoft.com/sharepoint/soap/">
                            <GetUpdatedFormDigestInformationResult>
                                <DigestValue>%s</DigestValue>
                                <TimeoutSeconds>%s</TimeoutSeconds>
                                <WebFullUrl>%s</WebFullUrl>
                                <LibraryVersion>%s</LibraryVersion>
                                <SupportedSchemaVersions>%s</SupportedSchemaVersions>
                            </GetUpdatedFormDigestInformationResult>
                        </GetUpdatedFormDigestInformationResponse>
                    </soap:Body>
                </soap:Envelope>
                """.formatted(expectedDigestValue,expectedTimeoutSeconds,expectedWebFullUrl,expectedLibraryVersion, expectedSupportedSchemaVersions);

        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
        GetUpdatedFormDigestInformationResponse.Envelope envelope = xmlMapper.readValue(xml, GetUpdatedFormDigestInformationResponse.Envelope.class);


        assertEquals(expectedDigestValue, envelope.body.response.result.digestValue);
        assertEquals(Long.valueOf(expectedTimeoutSeconds), envelope.body.response.result.timeoutSeconds);
        assertEquals(expectedWebFullUrl, envelope.body.response.result.webFullUrl);
        assertEquals(expectedLibraryVersion, envelope.body.response.result.libraryVersion);
        assertEquals(expectedSupportedSchemaVersions, envelope.body.response.result.supportedSchemaVersions);



    }
}
