package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class PnpTenantTest {
    String bodyAll = """
            [
            {
            "SchemaVersion":"15.0.0.0","LibraryVersion":"16.0.26211.12013","ErrorInfo":null,"TraceCorrelationId":"52eeada1-a003-d000-368e-96319e75e8a5"
            },17,{
            "IsNull":false
            },18,{
            "_ObjectType_":"Microsoft.Online.SharePoint.TenantAdministration.Tenant","_ObjectIdentity_":"52eeada1-a003-d000-368e-96319e75e8a5|908bed80-a04a-4433-b4a0-883d9847d110:1ab217e3-8b43-4ff4-810b-22f9acd3e06d\\nTenant","AIBuilderDefaultPowerAppsEnvironment":"","AIBuilderEnabled":true,"AIBuilderEnabledInContentCenter":0,"AIBuilderSiteListFileName":null,"AllOrganizationSecurityGroupId":"\\/Guid(00000000-0000-0000-0000-000000000000)\\/","AllowAnonymousMeetingParticipantsToAccessWhiteboards":0,"AllowClassicPublishingSiteCreation":false,"AllowCommentsTextOnEmailEnabled":true,"AllowDownloadingNonWebViewableFiles":true,"AllowedDomainListForSyncClient":[
            
            ],"AllowEditing":true,"AllowEveryoneExceptExternalUsersClaimInPrivateSite":false,"AllowGuestUserShareToUsersNotInSiteCollection":false,"AllowLimitedAccessOnUnmanagedDevices":false,"AllowOverrideForBlockUserInfoVisibility":false,"AllowSelectSecurityGroupsInSPSitesList":null,"AllowSelectSGsInODBListInTenant":null,"AllowSensitivityLabelOnRecords":false,"AllowSharingOutsideRestrictedAccessControlGroups":true,"AllowWebPropertyBagUpdateWhenDenyAddAndCustomizePagesIsEnabled":false,"AmplifyAdminSettings":"{\\"EapAccessType\\":2,\\"EapGroupDetails\\":\\"00000000-0000-0000-0000-000000000000\\",\\"CampaignCreationAccessType\\":0,\\"CampaignCreationGroupDetails\\":\\"00000000-0000-0000-0000-000000000000\\"}","AnyoneLinkTrackUsers":false,"AppAccessInformationBarriersAllowList":[
            
            ],"AppBypassInformationBarriers":false,"ApplyAppEnforcedRestrictionsToAdHocRecipients":true,"AppOnlyBypassPeoplePickerPolicies":false,"ArchiveRedirectUrl":null,"AuthContextResilienceMode":0,"AutofillColumnsEnabled":true,"AutofillColumnsSiteListFileName":"","BccExternalSharingInvitations":false,"BccExternalSharingInvitationsList":null,"BlockAccessOnUnmanagedDevices":false,"BlockAppAccessWithAuthenticationContext":false,"BlockDownloadFileTypeIds":[
            
            ],"BlockDownloadFileTypePolicy":false,"BlockDownloadLinksFileType":1,"BlockDownloadOfAllFilesForGuests":false,"BlockDownloadOfAllFilesOnUnmanagedDevices":false,"BlockDownloadOfViewableFilesForGuests":false,"BlockDownloadOfViewableFilesOnUnmanagedDevices":false,"BlockMacSync":false,"BlockSendLabelMismatchEmail":false,"BlockUserInfoVisibility":"ExternalPeopleInOD","BlockUserInfoVisibilityInOneDrive":1,"BlockUserInfoVisibilityInSharePoint":0,"BonusStorageQuotaMB":0,"BusinessConnectivityServiceDisabled":false,"CommentsOnFilesDisabled":false,"CommentsOnListItemsDisabled":false,"CommentsOnSitePagesDisabled":false,"CompatibilityRange":"15,15","ConditionalAccessPolicy":0,"ConditionalAccessPolicyErrorHelpLink":"","ContentSecurityPolicyConfigSynced":true,"ContentSecurityPolicyEnforcement":false,"ContentTypeSyncSiteTemplatesList":[
            
            ],"CoreBlockGuestsAsSiteAdmin":0,"CoreDefaultLinkToExistingAccess":false,"CoreDefaultShareLinkRole":0,"CoreDefaultShareLinkScope":-1,"CoreLoopDefaultSharingLinkRole":0,"CoreLoopDefaultSharingLinkScope":-1,"CoreLoopSharingCapability":0,"CoreRequestFilesLinkEnabled":false,"CoreRequestFilesLinkExpirationInDays":30,"CoreSharingCapability":0,"CustomizedExternalSharingServiceUrl":"","DataverseUsageConsentEnabled":false,"DefaultContentCenterSite":null,"DefaultLinkPermission":1,"DefaultODBMode":"Explicit","DefaultSharingLinkType":1,"DelayDenyAddAndCustomizePagesEnforcement":false,"DelayDenyAddAndCustomizePagesEnforcementOnClassicPublishingSites":false,"DelegateRestrictedAccessControlConfiguration":false,"DelegateRestrictedContentDiscoveryConfiguration":false,"DenySelectSecurityGroupsInSPSitesList":null,"DenySelectSGsInODBListInTenant":null,"DisableAddToOneDrive":false,"DisableBackToClassic":false,"DisableCustomAppAuthentication":true,"DisabledAdaptiveCardExtensionIds":null,"DisabledModernListTemplateIds":[
            
            ],"DisableDocumentLibraryDefaultLabeling":false,"DisabledWebPartIds":null,"DisableOutlookPSTVersionTrimming":false,"DisablePersonalListCreation":false,"DisableReportProblemDialog":false,"DisableSharePointStoreAccess":false,"DisableSpacesActivation":false,"DisableVivaConnectionsAnalytics":false,"DisallowInfectedFileDownload":false,"DisplayNamesOfFileViewers":true,"DisplayNamesOfFileViewersInSpo":true,"DisplayStartASiteOption":true,"DocumentUnderstandingEnabled":true,"DocumentUnderstandingEnabledInContentCenter":0,"DocumentUnderstandingSiteListFileName":"","EmailAttestationEnabled":true,"EmailAttestationReAuthDays":30,"EmailAttestationRequired":true,"EnableAIPIntegration":false,"EnableAutoExpirationVersionTrim":false,"EnableAutoNewsDigest":true,"EnableAzureADB2BIntegration":true,"EnabledFlightAllowAADB2BSkipCheckingOTP":true,"EnableDirectToCustomerBilling":false,"EnableDiscoverableByOrganizationForVideos":false,"EnableGuestSignInAcceleration":false,"EnableMediaReactions":true,"EnableMinimumVersionRequirement":true,"EnableMipSiteLabel":false,"EnablePromotedFileHandlers":true,"EnableRestrictedAccessControl":false,"EnableSensitivityLabelForPDF":false,"EnableSiteArchive":false,"EnableTenantRestrictionsInsights":false,"EnforceRequestDigest":false,"ESignatureAppList":[
            "{\\"AppName\\":\\"WordDesktop\\",\\"IsEnabled\\":true}"
            ],"ESignatureEnabled":false,"ESignatureSiteList":[
            
            ],"ESignatureThirdPartyProviderInfoList":[
            "{\\"ProviderName\\":\\"DocuSign\\",\\"IsEnabled\\":false}","{\\"ProviderName\\":\\"AdobeSign\\",\\"IsEnabled\\":false}"
            ],"ExcludedBlockDownloadGroupIds":[
            
            ],"ExcludedFileExtensionsForSyncClient":[
            ""
            ],"ExemptNativeUsersFromTenantLevelRestricedAccessControl":false,"ExpireVersionsAfterDays":0,"ExtendPermissionsToUnprotectedFiles":false,"ExternalServicesEnabled":true,"ExternalUserExpirationRequired":true,"ExternalUserExpireInDays":365,"FileAnonymousLinkType":2,"FilePickerExternalImageSearchEnabled":true,"FileVersionPolicyXml":"<VersionPolicies><VersionPolicy Name=\\"OutlookPST\\" MajorVersionLimit=\\"30\\" MajorWithMinorVersionsLimit=\\"30\\"><Extension Name=\\"pst\\" \\u002f><\\u002fVersionPolicy><\\u002fVersionPolicies>","FolderAnonymousLinkType":2,"GuestSharingGroupAllowListInTenant":"","GuestSharingGroupAllowListInTenantByGroupId":[
            
            ],"GuestSharingGroupAllowListInTenantByPrincipalIdentity":null,"HasAdminCompletedCUConfiguration":false,"HasIntelligentContentServicesCapability":false,"HasTopicExperiencesCapability":false,"HideSyncButtonOnDocLib":false,"HideSyncButtonOnODB":false,"IBImplicitGroupBased":false,"ImageTaggingOption":1,"ImageTaggingSiteListFileName":"","IncludeAtAGlanceInShareEmails":true,"InformationBarriersSuspension":true,"IPAddressAllowList":"","IPAddressEnforcement":false,"IPAddressWACTokenLifetime":15,"IsAppBarTemporarilyDisabled":false,"IsCollabMeetingNotesFluidEnabled":true,"IsDataAccessInCardDesignerEnabled":false,"IsEnableAppAuthPopUpEnabled":false,"IsFluidEnabled":true,"IsHubSitesMultiGeoFlightEnabled":true,"IsLoopEnabled":true,"IsMnAFlightEnabled":false,"IsMultiGeo":false,"IsMultipleHomeSitesFlightEnabled":false,"IsMultipleVivaConnectionsFlightEnabled":true,"IsUnmanagedSyncClientForTenantRestricted":false,"IsUnmanagedSyncClientRestrictionFlightEnabled":true,"IsVivaHomeFlightEnabled":true,"IsVivaHomeGAFlightEnabled":true,"IsWBFluidEnabled":true,"LabelMismatchEmailHelpLink":null,"LegacyAuthProtocolsEnabled":true,"LegacyBrowserAuthProtocolsEnabled":true,"LimitedAccessFileType":1,"MachineLearningCaptureEnabled":false,"MajorVersionLimit":5000,"MarkAllFilesAsSensitiveByDefault":false,"MarkNewFilesSensitiveByDefault":0,"MassDeleteNotificationDisabled":false,"MediaTranscription":0,"MediaTranscriptionAutomaticFeatures":0,"MobileFriendlyUrlEnabledInTenant":true,"NoAccessRedirectUrl":null,"NotificationsInOneDriveForBusinessEnabled":true,"NotificationsInSharePointEnabled":true,"NotifyOwnersWhenInvitationsAccepted":false,"NotifyOwnersWhenItemsReshared":true,"OCRAdminSiteListFileName":"","OCRComplianceSiteListFileName":"","OCRModeForAdminSites":0,"OCRModeForComplianceODBs":0,"OCRModeForComplianceSites":0,"ODBAccessRequests":0,"ODBMembersCanShare":0,"ODBSensitivityRefreshWindowInHours":24,"ODBSharingCapability":0,"ODBTranslationEnabled":true,"OfficeClientADALDisabled":false,"OneDriveBlockGuestsAsSiteAdmin":0,"OneDriveDefaultLinkToExistingAccess":false,"OneDriveDefaultShareLinkRole":0,"OneDriveDefaultShareLinkScope":-1,"OneDriveForGuestsEnabled":false,"OneDriveLoopDefaultSharingLinkRole":0,"OneDriveLoopDefaultSharingLinkScope":-1,"OneDriveLoopSharingCapability":0,"OneDriveRequestFilesLinkEnabled":false,"OneDriveRequestFilesLinkExpirationInDays":30,"OneDriveStorageQuota":1048576,"OptOutOfGrooveBlock":false,"OptOutOfGrooveSoftBlock":false,"OrgNewsSiteUrl":null,"OrphanedPersonalSitesRetentionPeriod":30,"OwnerAnonymousNotification":true,"PermissiveBrowserFileHandlingOverride":false,"PrebuiltEnabled":true,"PrebuiltEnabledInContentCenter":0,"PrebuiltSiteListFileName":"","PreventExternalUsersFromResharing":false,"ProvisionSharedWithEveryoneFolder":false,"PublicCdnAllowedFileTypes":"CSS,EOT,GIF,ICO,JPEG,JPG,JS,MAP,PNG,SVG,TTF,WOFF","PublicCdnEnabled":false,"PublicCdnOrigins":[
            
            ],"ReactivationCost":null,"RecycleBinRetentionPeriod":93,"ReduceTempTokenLifetimeEnabled":false,"ReduceTempTokenLifetimeValue":15,"RequireAcceptingAccountMatchInvitedAccount":false,"RequireAnonymousLinksExpireInDays":30,"ResourceQuota":0,"ResourceQuotaAllocated":0,"RestrictedAccessControlForOneDriveErrorHelpLink":"None","RestrictedAccessControlforSitesErrorHelpLink":"None","RestrictedOneDriveLicense":false,"RestrictedSharePointLicense":false,"RootSiteUrl":"https:\\u002f\\u002fsyrix.sharepoint.com","SearchResolveExactEmailOrUPN":false,"SelfServiceSiteCreationDisabled":false,"SharePointAddInsBlocked":false,"SharePointAddInsDisabled":false,"SharingAllowedDomainList":"gmail.com ya.ru","SharingBlockedDomainList":"gmail.com ya.ru","SharingCapability":0,"SharingDomainRestrictionMode":1,"ShowAllUsersClaim":false,"ShowEveryoneClaim":false,"ShowEveryoneExceptExternalUsersClaim":true,"ShowNGSCDialogForSyncOnODB":true,"ShowOpenInDesktopOptionForSyncedFiles":false,"ShowPeoplePickerGroupSuggestionsForIB":false,"ShowPeoplePickerSuggestionsForGuestUsers":false,"SignInAccelerationDomain":"","SiteOwnerManageLegacyServicePrincipalEnabled":false,"SocialBarOnSitePagesDisabled":false,"SpecialCharactersStateInFileFolderNames":1,"SPJitDlpPolicyData":null,"StartASiteFormUrl":null,"StopNew2010Workflows":false,"StopNew2013Workflows":false,"StorageQuota":1120256,"StorageQuotaAllocated":0,"StreamLaunchConfig":21,"StreamLaunchConfigLastUpdated":"\\/Date(1,0,1,0,0,0,0)\\/","StreamLaunchConfigUpdateCount":0,"SyncPrivacyProfileProperties":true,"TaxonomyTaggingEnabled":true,"TaxonomyTaggingSiteListFileName":"","TlsTokenBindingPolicyValue":0,"TranslationEnabled":true,"TranslationSiteListFileName":"","UniversalAnnotationDisabled":false,"UnlicensedOdbSyntexBillingEnabled":false,"UnlicensedOneDriveForBusinessTenantMetricsData":{
            "_ObjectType_":"Microsoft.SharePoint.Administration.OdbLicenseEnforcement.UnlicensedOdbTenantMetrics","adminLockedCount":0,"adminLockedSizeBytes":0,"billableCount":0,"billableSizeBytes":0,"complianceHoldCount":0,"complianceHoldSizeBytes":0,"count":0,"duplicateSiteCount":0,"duplicateSiteSizeBytes":0,"invalidLicenseCount":0,"invalidLicenseSizeBytes":0,"lastRefreshOn":"\\/Date(1751418276541)\\/","restoredByTenantAdminCount":0,"restoredByTenantAdminSizeBytes":0,"retentionPeriodCount":0,"retentionPeriodSizeBytes":0,"sizeBytes":0,"unknownCount":0,"unknownSizeBytes":0
            },"UseFindPeopleInPeoplePicker":false,"UsePersistentCookiesForExplorerView":false,"ViewersCanCommentOnMediaDisabled":false,"ViewInFileExplorerEnabled":false,"WhoCanShareAllowListInTenant":"","WhoCanShareAllowListInTenantByGroupId":[
            
            ],"WhoCanShareAllowListInTenantByPrincipalIdentity":null,"Workflow2010Disabled":true,"Workflows2013State":3
            }
            ]
            """;

    @Test
    public void test() throws Exception {
        SPShellCommand<TenantProperties> command = SPShellCommand.PnPTenant.GET_ALL();
        List<TenantProperties> properties = command.parseResponse(bodyAll);
        assertNotNull(properties);
        assertEquals(1, properties.size());
    }
}
