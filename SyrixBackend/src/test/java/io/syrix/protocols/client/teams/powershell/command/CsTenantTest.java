package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.teams.powershell.command.types.TenantInfo;
import org.junit.jupiter.api.Test;

class CsTenantTest {
public final String body = """
        {"assignedPlans":[{"servicePlanId":"57ff2da0-773e-42df-b2af-ffb7a2317929","subscribedPlanId":"b61f745d-b1ff-4499-9d33-62158b0bc188","assignedTimestamp":"2025-02-20T17:45:16Z","capabilityStatus":"Enabled","capability":"Teams","isInGracePeriod":false},{"servicePlanId":"0feaeb32-d00e-4d66-bd5a-43b5b83db82c","subscribedPlanId":"b48d8e08-5f09-4eca-af16-315018611b33","assignedTimestamp":"2025-02-20T17:45:16Z","capabilityStatus":"Enabled","capability":"MCOProfessional","isInGracePeriod":false}],"city":"Rehovot","companyPartnership":[],"countryLetterCode":"IL","createdDateTime":"2025-02-05T14:19:55+00:00","displayName":"Syrix","objectId":"763c49e2-afb5-446b-b41e-e2521fed4802","postalCode":"7608673","preferredLanguage":"en","provisionedPlans":[{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"},{"capabilityStatus":"Enabled","provisioningStatus":"Success","service":"MicrosoftCommunicationsOnline"}],"serviceInfo":"[]","street":"Prof Efraim Katzir 15","verifiedDomains":[{"name":"SyrixDev.onmicrosoft.com","status":"Enabled"}],"sipDomains":["SyrixDev.onmicrosoft.com"],"serviceInstance":"MicrosoftCommunicationsOnline/EMEA-0E-S3","teamsUpgradeEffectiveMode":"TeamsOnly","teamsUpgradeNotificationsEnabled":false,"teamsUpgradeOverridePolicy":"UpgradeToTeams","teamsUpgradePolicyIsReadOnly":"ModeAndNotifications","whenCreated":"2025-02-20T17:45:19.7586762+00:00","lastSyncTimeStamp":"2025-02-20T17:47:58.121594+00:00","lastProvisionTimeStamps":{"TenantSipDomains":"2025-02-20T17:45:22.4732309+00:00","TenantRegistrarPool":"2025-02-20T17:45:45.1300388+00:00"},"lastPublishTimeStamps":{"ProvisionedPlanPublishAuthoredProps":"2025-02-20T17:45:46.1036307+00:00","PublishProvisionedPlanProcessor":"2025-02-20T17:45:46.1240638+00:00"},"serviceDiscovery":{"Endpoints":{"ConfigApiEndpoint":"api.interfaces.records.teams.microsoft.com","AdminServiceEndpoint":"admin0e.online.lync.com"},"Headers":{"X-MS-Forest":"0e"}}}
        """;

    @Test
    public void testParse() throws Exception {
        final CsTeamsCommand<TenantInfo> command = CsTeamsCommand.CsTenant.GET();
        command.parseResponse(body);
    }
}