package io.syrix.protocols.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.MSEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.assertj.core.api.SoftAssertions.assertSoftly;

@ExtendWith(MockitoExtension.class)
class EntraGraphClientTest {
    private static final String TEST_ENDPOINT = "/test-endpoint";
    private MicrosoftGraphClient client;

    @BeforeEach
    void setUp() {
        // Use mock values for the test
        client = MicrosoftGraphClient.builder()
                .withClientId("mock-client-id")
                .withClientSecret("mock-client-secret")
                .withRefreshToken("mock-refresh-token")
                .build();
        
        // Modify the client to handle test without making real API calls
        try {
            java.lang.reflect.Field httpClientField = MicrosoftGraphClient.class.getDeclaredField("httpClient");
            httpClientField.setAccessible(true);
            httpClientField.set(client, new MockHttpClient());
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up mock HTTP client", e);
        }
    }
    
    // Simple mock HTTP client for tests
    private static class MockHttpClient {
        public CompletableFuture<JsonNode> sendAsync(Object request) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode response = mapper.createObjectNode()
                    .put("@odata.context", "https://graph.microsoft.com/v1.0/$metadata#policies")
                    .set("value", mapper.createArrayNode()
                            .add(mapper.createObjectNode()
                                    .put("id", "policy-1")
                                    .put("displayName", "Test Policy")));
            return CompletableFuture.completedFuture(response);
        }
    }

    @Nested
    @DisplayName("Client Builder Tests")
    @Disabled("Disabled until authentication issues are fixed")
    class BuilderTests {

        @Test
        @DisplayName("Should create client with minimal configuration")
        void shouldCreateClientWithMinimalConfig() {
            try (MicrosoftGraphClient client = MicrosoftGraphClient.builder()
                    .withClientId("mock-client-id")
                    .withClientSecret("mock-client-secret")
                    .withRefreshToken("mock-refresh-token")
                    .build()) {

                assertThat(client.getEnvironment()).isEqualTo(MSEnvironment.COMMERCIAL);
            }
        }

        @Test
        @DisplayName("Should fail when required parameters are missing")
        void shouldFailWithoutRequiredParams() {
            assertThatThrownBy(() ->
                    MicrosoftGraphClient.builder().build()
            )
                    .isInstanceOf(NullPointerException.class)
                    .hasMessageContaining("Client ID cannot be null");
        }
    }

    @Nested
    @DisplayName("Graph API Request Tests")
    @Disabled("Disabled until authentication issues are fixed")
    class GraphRequestTests {

        @Test
        @DisplayName("Should handle successful request")
        void shouldHandleSuccessfulRequest() throws Exception {
            // When
            CompletableFuture<JsonNode> future = client.makeGraphRequest(GraphRequest.builder()
                .withEndpoint("/policies")
                .withMethod(HttpMethod.GET)
                .build());

            // Then
            assertThat(future)
                    .succeedsWithin(Duration.ofSeconds(5))
                    .satisfies(response -> {
                        // Verify we have a response and the 'value' field exists
                        assertThat(response).isNotNull();
                        JsonNode valueNode = response.get("value");
                        assertThat(valueNode).isNotNull();

                        // Verify the value node is an array
                        assertThat(valueNode.isArray())
                                .withFailMessage("Expected 'value' field to be an array")
                                .isTrue();

                        // Check array properties
                        assertThat(valueNode.size())
                                .withFailMessage("Array should not be empty")
                                .isGreaterThan(0);

                        // Verify structure of array elements
                        JsonNode firstElement = valueNode.get(0);
                        assertThat(firstElement).isNotNull();

                        // Verify required fields exist and have correct types
                        assertThat(firstElement.has("id")).isTrue();
                        assertThat(firstElement.has("displayName")).isTrue();
                        assertThat(firstElement.get("id").isTextual()).isTrue();
                        assertThat(firstElement.get("displayName").isTextual()).isTrue();
                    });
        }

        @Test
        @DisplayName("Should handle requests for different environments")
        void shouldHandleAllEnvironments() {
            // Given
            client = MicrosoftGraphClient.builder()
                    .withClientId("mock-client-id")
                    .withClientSecret("mock-client-secret")
                    .withRefreshToken("mock-refresh-token")
                    .build();

            // When
            CompletableFuture<JsonNode> future = client.makeGraphRequest(GraphRequest.builder()
                    .withEndpoint("/policies")
                    .withMethod(HttpMethod.GET)
                    .build());

            // Then
            assertThat(future)
                    .succeedsWithin(Duration.ofSeconds(5))
                    .satisfies(response -> {
                        JsonNode valueNode = response.get("value");

                        // Use assertSoftly to group related assertions for better error reporting
                        assertSoftly(softly -> {
                            softly.assertThat(valueNode)
                                    .as("value node")
                                    .isNotNull();

                            softly.assertThat(valueNode.isArray())
                                    .as("value node type")
                                    .isTrue();
                        });
                    });
        }
    }
}