package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;
import org.junit.jupiter.api.Test;


class CsTenantFederationConfigurationTest {
    public final String body = """
                     [{"AllowedDomains":{},"BlockedDomains":[],"AllowedTrialTenantDomains":[],"AllowFederatedUsers":false,"AllowTeamsSms":true,"AllowTeamsConsumer":true,"AllowTeamsConsumerInbound":false,"TreatDiscoveredPartnersAsUnverified":false,"SharedSipAddressSpace":false,"RestrictTeamsConsumerToExternalUserProfiles":false,"BlockAllSubdomains":false,"ExternalAccessWithTrialTenants":"Blocked","DomainBlockingForMDOAdminsInTeams":"Disabled","Key":{"ScopeClass":"Global","SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Settings.Edge.2008}TenantFederationSettings"}},"AuthorityId":{"Class":"Tenant","InstanceId":"1ab217e3-8b43-4ff4-810b-22f9acd3e06d","XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AuthorityId"}},"DefaultXml":{"SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Settings.Edge.2008}TenantFederationSettings"}},"Data":{"TenantFederationSettings":{"@xmlns":"urn:schema:Microsoft.Rtc.Management.Settings.Edge.2008","AllowedDomains":{"AllowAllKnownDomains":null},"BlockedDomains":null,"AllowedTrialTenantDomains":null}},"ConfigObject":null,"Signature":"00000000-0000-0000-0000-000000000000","IsModified":true},"XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AnchoredXmlKey"}},"Identity":"Global","ConfigMetadata":{"Authority":"Tenant"},"ConfigId":"Global"}]
            """;

    @Test
    void parseResponse() throws Exception {
        final CsTeamsCommand<TenantFederationConfiguration> command = CsTeamsCommand.CsTenantFederationConfiguration.GET();
        command.parseResponse(body);
    }
}