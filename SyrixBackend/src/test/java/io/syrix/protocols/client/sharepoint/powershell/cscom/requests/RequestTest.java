package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.junit.jupiter.api.Test;

public class RequestTest {
    @Test
    public void testGen() throws JsonProcessingException {
        Request request = new Request.Builder()
                .addExpandoFieldTypeSuffix(true)
                .schemaVersion("15.0.0.0")
                .libraryVersion("16.0.0.0")
                .applicationName(".NET Library")
                .build();

        XmlMapper xmlMapper = new XmlMapper();
        String body = xmlMapper.writeValueAsString(request);
        System.out.println(body);

        //			String body = """
//					<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0"
//					         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
//					    <Actions>
//					        <ObjectPath Id="2" ObjectPathId="1"/>
//					        <Query Id="3" ObjectPathId="1">
//					            <Query SelectAllProperties="true">
//					                <Properties>
//					                    <Property Name="HideDefaultThemes" ScalarProperty="true"/>
//					                </Properties>
//					            </Query>
//					        </Query>
//					    </Actions>
//					    <ObjectPaths>
//					        <Constructor Id="1" TypeId="{268004ae-ef6b-4e9b-8425-127220d84719}"/>
//					    </ObjectPaths>
//					</Request>
//					""";


    }
}
