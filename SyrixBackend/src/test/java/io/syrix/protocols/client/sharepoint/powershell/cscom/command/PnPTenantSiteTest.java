package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SiteProperties;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class PnPTenantSiteTest {
	public final String body = """
			[
			{
			"SchemaVersion":"15.0.0.0","LibraryVersion":"16.0.26204.12017","ErrorInfo":null,"TraceCorrelationId":"d5e8ada1-1051-d000-368e-98f7f3ad3660"
			},8,{
			"IsNull":false
			},10,{
			"IsNull":false
			},11,{
			"_ObjectType_":"Microsoft.Online.SharePoint.TenantAdministration.SiteProperties","_ObjectIdentity_":"d5e8ada1-1051-d000-368e-98f7f3ad3660|908bed80-a04a-4433-b4a0-883d9847d110:1ab217e3-8b43-4ff4-810b-22f9acd3e06d\\nSiteProperties\\nhttps%3a%2f%2fsyrix.sharepoint.com%2f","AllowDownloadingNonWebViewableFiles":false,"AllowEditing":true,"AllowFileArchive":false,"AllowSelfServiceUpgrade":true,"AllowWebPropertyBagUpdateWhenDenyAddAndCustomizePagesIsEnabled":false,"AnonymousLinkExpirationInDays":0,"ApplyToExistingDocumentLibraries":false,"ApplyToNewDocumentLibraries":false,"ArchivedBy":"","ArchivedTime":"\\/Date(1,0,1,0,0,0,0)\\/","ArchiveStatus":"NotArchived","AuthContextStrength":null,"AuthenticationContextLimitedAccess":false,"AuthenticationContextName":null,"AverageResourceUsage":0,"BlockDownloadLinksFileType":1,"BlockDownloadMicrosoft365GroupIds":null,"BlockDownloadPolicy":false,"BlockDownloadPolicyFileTypeIds":null,"BlockGuestsAsSiteAdmin":0,"BonusDiskQuota":0,"ClearGroupId":false,"ClearRestrictedAccessControl":false,"CommentsOnSitePagesDisabled":false,"CompatibilityLevel":15,"ConditionalAccessPolicy":0,"CreatedTime":"\\/Date(2024,11,20,15,15,22,887)\\/","CurrentResourceUsage":0,"DefaultLinkPermission":0,"DefaultLinkToExistingAccess":false,"DefaultLinkToExistingAccessReset":false,"DefaultShareLinkRole":0,"DefaultShareLinkScope":-1,"DefaultSharingLinkType":0,"DenyAddAndCustomizePages":2,"Description":"","DisableAppViews":2,"DisableCompanyWideSharingLinks":2,"DisableFlows":2,"EnableAutoExpirationVersionTrim":false,"ExcludeBlockDownloadPolicySiteOwners":false,"ExcludeBlockDownloadSharePointGroups":[
			
			],"ExcludedBlockDownloadGroupIds":[
			
			],"ExpireVersionsAfterDays":0,"ExternalUserExpirationInDays":0,"GroupId":"\\/Guid(00000000-0000-0000-0000-000000000000)\\/","GroupOwnerLoginName":"c:0o.c|federateddirectoryclaimprovider|00000000-0000-0000-0000-000000000000_o","HasHolds":false,"HidePeoplePreviewingFiles":false,"HidePeopleWhoHaveListsOpen":false,"HubSiteId":"\\/Guid(00000000-0000-0000-0000-000000000000)\\/","IBMode":"","IBSegments":[
			
			],"IBSegmentsToAdd":null,"IBSegmentsToRemove":null,"InheritVersionPolicyFromTenant":true,"IsGroupOwnerSiteAdmin":false,"IsHubSite":false,"IsTeamsChannelConnected":false,"IsTeamsConnected":false,"LastContentModifiedDate":"\\/Date(2025,5,30,16,41,2,240)\\/","Lcid":1033,"LimitedAccessFileType":1,"ListsShowHeaderAndNavigation":false,"LockIssue":null,"LockReason":0,"LockState":"Unlock","LoopDefaultSharingLinkRole":0,"LoopDefaultSharingLinkScope":-1,"MajorVersionLimit":0,"MajorWithMinorVersionsLimit":0,"MediaTranscription":0,"OverrideBlockUserInfoVisibility":0,"OverrideSharingCapability":false,"OverrideTenantAnonymousLinkExpirationPolicy":false,"OverrideTenantExternalUserExpirationPolicy":false,"Owner":"42deaafd-52c5-4ed6-9239-23523a115d53","OwnerEmail":null,"OwnerLoginName":"c:0t.c|tenant|42deaafd-52c5-4ed6-9239-23523a115d53","OwnerName":"Global Administrator","PWAEnabled":1,"ReadOnlyAccessPolicy":false,"ReadOnlyForBlockDownloadPolicy":false,"ReadOnlyForUnmanagedDevices":false,"RelatedGroupId":"\\/Guid(00000000-0000-0000-0000-000000000000)\\/","RequestFilesLinkEnabled":false,"RequestFilesLinkExpirationInDays":-1,"RestrictContentOrgWideSearch":false,"RestrictedAccessControl":false,"RestrictedAccessControlGroups":[
			
			],"RestrictedAccessControlGroupsToAdd":null,"RestrictedAccessControlGroupsToRemove":null,"RestrictedContentDiscoveryforCopilotAndAgents":false,"RestrictedToRegion":3,"SandboxedCodeActivationCapability":2,"SensitivityLabel":"\\/Guid(00000000-0000-0000-0000-000000000000)\\/","SensitivityLabel2":null,"SetOwnerWithoutUpdatingSecondaryAdmin":false,"SharingAllowedDomainList":"","SharingBlockedDomainList":"","SharingCapability":0,"SharingDomainRestrictionMode":0,"SharingLockDownCanBeCleared":true,"SharingLockDownEnabled":false,"ShowPeoplePickerSuggestionsForGuestUsers":false,"SiteDefinedSharingCapability":2,"SiteId":"\\/Guid(480abded-7561-4f95-aa18-092bbdd54858)\\/","SocialBarOnSitePagesDisabled":false,"Status":"Active","StorageMaximumLevel":26214400,"StorageQuotaType":null,"StorageUsage":81,"StorageWarningLevel":25574400,"TeamsChannelType":0,"Template":"SITEPAGEPUBLISHING#0","TimeZoneId":13,"Title":"Communication site","TitleTranslations":null,"Url":"https:\\u002f\\u002fsyrix.sharepoint.com\\u002f","UserCodeMaximumLevel":300,"UserCodeWarningLevel":255,"VersionCount":360,"VersionSize":52380939,"WebsCount":1
			}
			]
""";

	@Test
	public void testParse() throws Exception {
		String domain = "domain";
		SPShellCommand<SiteProperties> cmd = SPShellCommand.PnPTenantSite.GET(domain);
		List<SiteProperties> siteProperties = cmd.parseResponse(body);
		assertNotNull(siteProperties);
		assertEquals(1, siteProperties.size());
	}

}
