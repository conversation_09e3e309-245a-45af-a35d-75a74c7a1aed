package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.teams.powershell.command.types.MeetingBroadcastPolicy;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CsTeamsMeetingBroadcastPolicyTest {
    private final String body = """
            [{"Description":null,"AllowBroadcastScheduling":true,"AllowBroadcastTranscription":false,"BroadcastAttendeeVisibilityMode":"EveryoneInCompany","BroadcastRecordingMode":"AlwaysEnabled","DataSource":null,"Key":{"ScopeClass":"Global","SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsMeetingBroadcastPolicy"}},"AuthorityId":{"Class":"Tenant","InstanceId":"763c49e2-afb5-446b-b41e-e2521fed4802","XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AuthorityId"}},"DefaultXml":{"SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsMeetingBroadcastPolicy"}},"Data":{"TeamsMeetingBroadcastPolicy":{"@xmlns":"urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017"}},"ConfigObject":null,"Signature":"00000000-0000-0000-0000-000000000000","IsModified":true},"XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AnchoredXmlKey"}},"Identity":"Global","ConfigMetadata":{"Authority":"Tenant"},"ConfigId":"Global"},{"Description":null,"AllowBroadcastScheduling":true,"AllowBroadcastTranscription":false,"BroadcastAttendeeVisibilityMode":"EveryoneInCompany","BroadcastRecordingMode":"AlwaysEnabled","DataSource":"Memory","Key":{"ScopeClass":"Tag","SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsMeetingBroadcastPolicy"}},"AuthorityId":{"Class":"Host","InstanceId":"00000000-0000-0000-0000-000000000000","XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AuthorityId"}},"DefaultXml":{"SchemaId":{"XName":{"name":"{urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017}TeamsMeetingBroadcastPolicy"}},"Data":{"TeamsMeetingBroadcastPolicy":{"@xmlns":"urn:schema:Microsoft.Rtc.Management.Policy.Teams.2017"}},"ConfigObject":null,"Signature":"00000000-0000-0000-0000-000000000000","IsModified":true},"XmlRoot":{"name":"{urn:schema:Microsoft.Rtc.Management.ScopeFramework.2008}AnchoredXmlKey"}},"Identity":"Tag:Default","ConfigMetadata":{"Authority":"Host"},"ConfigId":"nypeLX04KOHQHrU13bs1Ovd3CuWLFqDLO1Togz6l5YA"}]
            """;

    @Test
    void parseResponse() throws Exception {
        final CsTeamsCommand<MeetingBroadcastPolicy> command = CsTeamsCommand.CsTeamsMeetingBroadcastPolicy.GET();
        List<MeetingBroadcastPolicy> res = command.parseResponse(body);
        assertEquals(2, res.size());
    }
}