package io.syrix.worker.service;

import io.syrix.dao.TaskDao;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.TaskStatus;
import io.syrix.messaging.TaskMessage;
import io.syrix.worker.exception.InvalidTaskTransitionException;
import io.syrix.worker.exception.TaskNotFoundException;
import io.syrix.worker.exception.TaskStatusUpdateException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for TaskStatusService, focusing on status transition validation and exception handling.
 */
@ExtendWith(MockitoExtension.class)
class TaskStatusServiceTest {

    @Mock
    private TaskDao taskDao;

    private TaskStatusService taskStatusService;
    
    private Task testTask;
    private TaskMessage testMessage;
    private UUID taskId;
    private UUID clientId;

    @BeforeEach
    void setUp() {
        taskStatusService = new TaskStatusService(taskDao);
        
        taskId = UUID.randomUUID();
        clientId = UUID.randomUUID();
        
        testTask = new Task();
        testTask.setId(taskId);
        testTask.setCustomerId(clientId);
        testTask.setStatus(TaskStatus.CREATED);
        
        testMessage = new TaskMessage();
        testMessage.setTaskId(taskId);
        testMessage.setCustomerId(clientId);
    }

    @Test
    void testValidTransition_CreatedToQueued_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.CREATED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToQueued(testMessage));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.QUEUED, testTask.getStatus());
    }

    @Test
    void testValidTransition_QueuedToReceived_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.QUEUED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToReceived(testMessage));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.RECEIVED, testTask.getStatus());
    }

    @Test
    void testValidTransition_ReceivedToInProgress_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.RECEIVED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToInProgress(testMessage));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.IN_PROGRESS, testTask.getStatus());
    }

    @Test
    void testValidTransition_InProgressToCompleted_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToCompleted(testMessage));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.COMPLETED, testTask.getStatus());
    }

    @Test
    void testInvalidTransition_CreatedToCompleted_ShouldThrowException() {
        // Arrange
        testTask.setStatus(TaskStatus.CREATED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        InvalidTaskTransitionException exception = assertThrows(InvalidTaskTransitionException.class,
                () -> taskStatusService.updateTaskStatus(taskId, clientId, TaskStatus.COMPLETED));
        
        assertEquals(TaskStatus.CREATED, exception.getCurrentStatus());
        assertEquals(TaskStatus.COMPLETED, exception.getTargetStatus());
        verify(taskDao, never()).save(any());
        assertEquals(TaskStatus.CREATED, testTask.getStatus()); // Status should remain unchanged
    }

    @Test
    void testInvalidTransition_QueuedToFailed_ShouldThrowException() {
        // Arrange
        testTask.setStatus(TaskStatus.QUEUED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        InvalidTaskTransitionException exception = assertThrows(InvalidTaskTransitionException.class, () ->
                taskStatusService.updateToFailed(testMessage, "Test error"));
        
        assertEquals(TaskStatus.QUEUED, exception.getCurrentStatus());
        assertEquals(TaskStatus.FAILED, exception.getTargetStatus());
        verify(taskDao, never()).save(any());
        assertEquals(TaskStatus.QUEUED, testTask.getStatus()); // Status should remain unchanged
    }

    @Test
    void testValidTransition_ReceivedToFailed_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.RECEIVED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToFailed(testMessage, "Processing error"));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.FAILED, testTask.getStatus());
    }

    @Test
    void testValidTransition_InProgressToAborted_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToAborted(testMessage));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.ABORTED, testTask.getStatus());
    }

    @Test
    void testInvalidTransition_FromTerminalStatus_ShouldThrowException() {
        // Arrange
        testTask.setStatus(TaskStatus.COMPLETED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        InvalidTaskTransitionException exception = assertThrows(InvalidTaskTransitionException.class, () ->
                taskStatusService.updateTaskStatus(taskId, clientId, TaskStatus.IN_PROGRESS));
        
        assertEquals(TaskStatus.COMPLETED, exception.getCurrentStatus());
        assertEquals(TaskStatus.IN_PROGRESS, exception.getTargetStatus());
        verify(taskDao, never()).save(any());
        assertEquals(TaskStatus.COMPLETED, testTask.getStatus()); // Status should remain unchanged
    }

    @Test
    void testIdempotentOperation_SameStatus_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateTaskStatus(taskId, clientId, TaskStatus.IN_PROGRESS));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.IN_PROGRESS, testTask.getStatus());
    }

    @Test
    void testTaskNotFound_ShouldThrowTaskNotFoundException() {
        // Arrange
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.empty());
        
        // Act & Assert
        TaskNotFoundException exception = assertThrows(TaskNotFoundException.class, () ->
                taskStatusService.updateToReceived(testMessage));
        
        assertTrue(exception.getMessage().contains("Task not found"));
        verify(taskDao, never()).save(any());
    }

    @Test
    void testDatabaseException_ShouldThrowTaskStatusUpdateException() {
        // Arrange
        testTask.setStatus(TaskStatus.RECEIVED);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        when(taskDao.save(any())).thenThrow(new RuntimeException("Database connection failed"));
        
        // Act & Assert
        TaskStatusUpdateException exception = assertThrows(TaskStatusUpdateException.class, () ->
                taskStatusService.updateToInProgress(testMessage));
        
        assertTrue(exception.getMessage().contains("Failed to update task status to IN_PROGRESS"));
        assertEquals("Database connection failed", exception.getCause().getMessage());
    }

    @Test
    void testDirectTaskNotFound_ShouldThrowTaskNotFoundException() {
        // Arrange
        UUID differentclientId = UUID.randomUUID();
        when(taskDao.findByIdAndCustomerId(taskId, differentclientId)).thenReturn(Optional.empty());
        
        // Act & Assert
        TaskNotFoundException exception = assertThrows(TaskNotFoundException.class, () ->
                taskStatusService.updateTaskStatus(taskId, differentclientId, TaskStatus.IN_PROGRESS));
        
        assertTrue(exception.getMessage().contains("Task not found"));
        verify(taskDao, never()).save(any());
    }

    @Test
    void testUpdateToFailedWithoutErrorMessage_ShouldSucceed() {
        // Arrange
        testTask.setStatus(TaskStatus.IN_PROGRESS);
        when(taskDao.findByIdAndCustomerId(taskId, clientId)).thenReturn(Optional.of(testTask));
        
        // Act & Assert
        assertDoesNotThrow(() -> taskStatusService.updateToFailed(testMessage, null));
        
        verify(taskDao).save(testTask);
        assertEquals(TaskStatus.FAILED, testTask.getStatus());
    }
}
