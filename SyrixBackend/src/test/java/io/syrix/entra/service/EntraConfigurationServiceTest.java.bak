package io.syrix.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.utils.TestUtils;
import io.syrix.products.microsoft.entra.model.PrivilegedUser;
import io.syrix.products.microsoft.entra.service.*;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.PaginatedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.IS_VERIFIED;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EntraConfigurationServiceTest {

    @Mock
    private MicrosoftGraphClient graphClient;

    @Captor
    private ArgumentCaptor<GraphRequest> requestCaptor;

    private EntraConfigurationService service;
    private ObjectMapper objectMapper;

    @Mock
    private MetricsCollector metricsCollector;
    
    // Mock dependent services
    @Mock
    private DirectoryRoleService directoryRoleService;
    
    @Mock
    private ConditionalAccessService conditionalAccessService;
    
    @Mock
    private AuthenticationService authenticationService;
    
    @Mock
    private RiskyPermissionsService riskyPermissionsService;
    
    @Mock
    private EnterpriseApplicationService enterpriseApplicationService;
    
    @Mock
    private GroupMemberProcessor groupMemberProcessor;
    
    @Mock
    private io.syrix.products.microsoft.base.LicenseProcessor licenseProcessor;
    
    // Test data fields
    private JsonNode skusData;
    private JsonNode domainsData;
    private JsonNode settingsData;
    private JsonNode authMethodsData;
    private JsonNode directoryRolesData;
    private JsonNode privilegedUsersData;
    private JsonNode conditionalAccessPoliciesData;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        
        // Load all test data upfront
        loadTestData();
        
        // Setup basic GraphClient mocks for direct EntraConfigurationService calls
        mockBasicGraphClientResponses();
        
        // Create real service with mocked dependencies
        service = new EntraConfigurationService(graphClient, objectMapper, metricsCollector) {
            @Override
            protected DirectoryRoleService createDirectoryRoleService() {
                return directoryRoleService;
            }
            
            @Override
            protected GroupMemberProcessor createGroupMemberProcessor() {
                return groupMemberProcessor;
            }
            
            @Override
            protected ConditionalAccessService createConditionalAccessService() {
                return conditionalAccessService;
            }
            
            @Override
            protected AuthenticationService createAuthenticationService() {
                return authenticationService;
            }
            
            @Override
            protected RiskyPermissionsService createRiskyPermissionsService() {
                return riskyPermissionsService;
            }
            
            @Override
            protected EnterpriseApplicationService createEnterpriseApplicationService() {
                return enterpriseApplicationService;
            }
            
            @Override
            protected LicenseProcessor createLicenseProcessor() {
                return licenseProcessor;
            }
            
            @Override
            public CompletableFuture<Map<String, PrivilegedUser>> getPrivilegedUsers(
                    CompletableFuture<JsonNode> privilegeDirectoryRoles,
                    boolean hasAadPremiumP2) {
                return CompletableFuture.completedFuture(new HashMap<>());
            }
        };
        
        // Now mock the dependent services after service is created
        mockDependentServices();
    }

    private void loadTestData() throws Exception {
        skusData = TestUtils.loadTestData("entra/subscribed-skus.json");
        domainsData = TestUtils.loadTestData("entra/domains.json");
        settingsData = TestUtils.loadTestData("entra/directory-settings.json");
        authMethodsData = TestUtils.loadTestData("entra/authentication-methods.json");
        directoryRolesData = TestUtils.loadTestData("entra/directory-roles.json");
        privilegedUsersData = TestUtils.loadTestData("entra/privileged-users.json");
        conditionalAccessPoliciesData = TestUtils.loadTestData("entra/conditional-access-policies.json");
    }

    private void mockBasicGraphClientResponses() {
        // Mock GraphClient response for getTotalUserCount
        CompletableFuture<JsonNode> userCountFuture = CompletableFuture.completedFuture(
            objectMapper.createObjectNode().put("value", "100")
        );
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().contains("/users/$count")))).thenReturn(userCountFuture);
        
        // Mock GraphClient response for getDirectorySettings
        CompletableFuture<JsonNode> settingsFuture = CompletableFuture.completedFuture(settingsData);
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().contains("/settings")))).thenReturn(settingsFuture);
        
        // Mock GraphClient response for getDomains
        CompletableFuture<JsonNode> domainsFuture = CompletableFuture.completedFuture(domainsData);
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().contains("/domains")))).thenReturn(domainsFuture);
            
        // Mock GraphClient response for getDirectoryRoles
        CompletableFuture<JsonNode> directoryRolesFuture = CompletableFuture.completedFuture(directoryRolesData);
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().equals("/directoryRoles") || 
            req.getEndpoint().contains("/directoryRoleTemplates")))).thenReturn(directoryRolesFuture);
        
        // Mock getRoleMembers for directoryRoles
        PaginatedResult mockPaginatedResult = new PaginatedResult();
        mockPaginatedResult.setValues(Arrays.asList(privilegedUsersData.get("value").get(0)));
        mockPaginatedResult.setNextLink(null); // No more pages
        
        when(graphClient.makePaginatedRequest(argThat(req -> 
            req.getEndpoint().contains("/directoryRoles/") && 
            req.getEndpoint().contains("/members")))).thenReturn(mockPaginatedResult);
            
        // Mock individual user lookup
        CompletableFuture<JsonNode> userDetailsFuture = CompletableFuture.completedFuture(
            objectMapper.createObjectNode()
                .put("id", "user1")
                .put("displayName", "Admin User")
                .put("userPrincipalName", "<EMAIL>")
        );
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().contains("/users/")))).thenReturn(userDetailsFuture);

        // Mock auditAndExportPublicM365Groups
        ArrayNode emptyGroups = objectMapper.createArrayNode();
        CompletableFuture<JsonNode> groupsFuture = CompletableFuture.completedFuture(emptyGroups);
        when(graphClient.makeGraphRequest(argThat(req -> 
            req.getEndpoint().contains("/groups") && 
            req.getQueryParameters() != null && 
            req.getQueryParameters().containsKey("$filter") && 
            req.getQueryParameters().get("$filter").contains("Unified")))).thenReturn(groupsFuture);
    }

    private void mockDependentServices() throws Exception {
        // Mock LicenseProcessor
        io.syrix.products.microsoft.base.SkuProcessingResult mockSkuResult = mock(io.syrix.products.microsoft.base.SkuProcessingResult.class);
        when(mockSkuResult.hasAadPremiumP2()).thenReturn(true);
        when(licenseProcessor.processSkus()).thenReturn(mockSkuResult);
        
        // Mock DirectoryRoleService getDirectoryRoles
        CompletableFuture<JsonNode> directoryRolesFuture = CompletableFuture.completedFuture(directoryRolesData);
        when(directoryRoleService.getDirectoryRoles(anyBoolean())).thenReturn(directoryRolesFuture);
        
        // Setup ConfigurationResult objects for each dependent service
        ConfigurationResult capResult = createMockConfigurationResult("conditional_access_policies", conditionalAccessPoliciesData);
        when(conditionalAccessService.exportConfiguration()).thenReturn(capResult);
        
        ConfigurationResult authResult = createMockConfigurationResult("authentication_methods", authMethodsData);
        when(authenticationService.exportConfiguration()).thenReturn(authResult);
        
        // Create empty risky permissions result
        ObjectNode riskyNode = objectMapper.createObjectNode();
        ArrayNode emptyArray = objectMapper.createArrayNode();
        riskyNode.set("risky_applications", emptyArray);
        riskyNode.set("risky_third_party_service_principals", emptyArray);
        ConfigurationResult riskyResult = ConfigurationResult.builder()
            .withData(riskyNode)
            .withTimestamp(Instant.now())
            .withMetadata(objectMapper.createObjectNode().put("version", "1.0"))
            .build();
        when(riskyPermissionsService.exportConfiguration()).thenReturn(riskyResult);
        
        // Create enterprise applications result
        ObjectNode enterpriseNode = objectMapper.createObjectNode();
        enterpriseNode.set("enterprise_applications", emptyArray);
        ConfigurationResult enterpriseResult = ConfigurationResult.builder()
            .withData(enterpriseNode)
            .withTimestamp(Instant.now())
            .withMetadata(objectMapper.createObjectNode().put("version", "1.0"))
            .build();
        when(enterpriseApplicationService.exportConfiguration()).thenReturn(enterpriseResult);
    }
    
    private ConfigurationResult createMockConfigurationResult(String key, JsonNode data) {
        ObjectNode dataNode = objectMapper.createObjectNode();
        dataNode.set(key, data);
        dataNode.set("successful_commands", objectMapper.createArrayNode());
        dataNode.set("unsuccessful_commands", objectMapper.createArrayNode());
        
        return ConfigurationResult.builder()
            .withData(dataNode)
            .withTimestamp(Instant.now())
            .withMetadata(objectMapper.createObjectNode().put("version", "1.0"))
            .build();
    }

    @Nested
    @Disabled
    @DisplayName("License and Subscription Tests")
    class SubscriptionTests {

        @Test
        @DisplayName("Should fetch and validate subscribed SKUs")
        void shouldFetchSubscribedSkus() throws Exception {
            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result.getData().has("subscribed_skus")).isTrue();
            verify(graphClient, atLeastOnce()).makeGraphRequest(any(GraphRequest.class));
        }
    }

    @Nested
    @Disabled
    @DisplayName("Domain Configuration Tests")
    class DomainTests {

        @Test
        @DisplayName("Should fetch and validate domain settings")
        void shouldFetchDomainSettings() throws Exception {
            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result.getData().has("domains")).isTrue();
            verify(graphClient, atLeastOnce()).makeGraphRequest(argThat(req -> 
                req.getEndpoint().contains("/domains")));
        }
    }

    @Nested
    @Disabled
    @DisplayName("Directory Settings Tests")
    class DirectorySettingsTests {

        @Test
        @DisplayName("Should fetch and validate directory settings")
        void shouldFetchDirectorySettings() throws Exception {
            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result.getData().has("directory_settings")).isTrue();
            verify(graphClient, atLeastOnce()).makeGraphRequest(argThat(req -> 
                req.getEndpoint().contains("/settings")));
        }
    }

    @Nested
    @Disabled
    @DisplayName("Authentication Methods Tests")
    class AuthenticationMethodsTests {

        @Test
        @DisplayName("Should fetch and validate authentication methods")
        void shouldFetchAuthenticationMethods() throws Exception {
            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result.getData().has("authentication_method")).isTrue();
            verify(authenticationService).exportConfiguration();
        }
    }

    @Nested
    @Disabled
    @DisplayName("Privileged Users Tests")
    class PrivilegedUsersTests {

        @Test
        @DisplayName("Should fetch and validate privileged users")
        void shouldFetchPrivilegedUsers() throws Exception {
            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result.getData().has("privileged_users")).isTrue();
            verify(directoryRoleService).getDirectoryRoles(anyBoolean());
        }
    }

    @Test
    @Disabled
    @DisplayName("Should fetch and validate conditional access policies")
    void shouldFetchConditionalAccessPolicies() throws Exception {
        // When
        ConfigurationResult result = service.exportConfiguration();

        // Then
        assertThat(result.getData().has("conditional_access_policies")).isTrue();
        verify(conditionalAccessService).exportConfiguration();
    }

    @Test
    @Disabled
    @DisplayName("Should fetch and validate directory roles")
    void shouldFetchDirectoryRoles() throws Exception {
        // When
        ConfigurationResult result = service.exportConfiguration();

        // Then
        assertThat(result.getData().has("privileged_roles")).isTrue();
        verify(directoryRoleService).getDirectoryRoles(anyBoolean());
    }

    @Test
    @Disabled
    @DisplayName("Should collect all configurations successfully")
    void shouldCollectAllConfigurations() throws Exception {
        // When
        ConfigurationResult result = service.exportConfiguration();

        // Then - Check that result contains all expected sections
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().has("domains")).isTrue();
        assertThat(result.getData().has("directory_settings")).isTrue();
        assertThat(result.getData().has("conditional_access_policies")).isTrue();
        assertThat(result.getData().has("authentication_method")).isTrue();
        assertThat(result.getData().has("privileged_roles")).isTrue();
        
        // Verify all dependent services were called
        verify(conditionalAccessService).exportConfiguration();
        verify(authenticationService).exportConfiguration();
        verify(riskyPermissionsService).exportConfiguration();
        verify(enterpriseApplicationService).exportConfiguration();
        verify(directoryRoleService).getDirectoryRoles(anyBoolean());
    }
}