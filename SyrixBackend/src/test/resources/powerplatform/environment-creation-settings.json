{"@odata.context": "https://graph.microsoft.com/beta/$metadata#admin/powerPlatform/settings/environmentCreation", "isCreationAllowed": true, "restrictToDefaultTenant": true, "allowedDataLocations": ["NAM", "EUR"], "defaultLocation": "NAM", "rules": [{"type": "TrialEnvironment", "isCreationAllowed": true, "maxAllowedCount": 2, "maxAllowedCountPerUser": 1, "expirationInDays": 30}, {"type": "Sandbox", "isCreationAllowed": true, "maxAllowedCount": 5, "maxAllowedCountPerUser": 2, "expirationInDays": null}, {"type": "Production", "isCreationAllowed": false, "maxAllowedCount": 0, "maxAllowedCountPerUser": 0, "expirationInDays": null}], "allowedRoles": ["Global administrator", "Power Platform administrator"], "requireJustification": true, "requireUniqueDisplayNames": true, "defaultSKU": "Premium", "minNameLength": 3, "maxNameLength": 50}