{"@odata.context": "https://graph.microsoft.com/beta/$metadata#admin/powerPlatform/environments", "value": [{"id": "/providers/Microsoft.PowerPlatform/environments/prod-env-01", "name": "prod-env-01", "displayName": "Production Environment", "location": "NAM", "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "createdBy": {"id": "33333333-3333-3333-3333-333333333333", "displayName": "System Administrator", "type": "User"}, "properties": {"environmentSku": "Production", "azureRegion": "northamerica", "creationType": "User", "databaseType": "CommonDataService", "environmentType": "Production", "isDefault": true, "retentionPeriod": "Indefinite", "securityGroupId": "44444444-4444-4444-4444-444444444444", "securityGroupName": "Production Environment Admins", "settings": {"disablePortalsCreation": true, "disableTrialEnvironmentCreation": true, "requireEnvironmentAdmins": true}}}, {"id": "/providers/Microsoft.PowerPlatform/environments/dev-env-01", "name": "dev-env-01", "displayName": "Development Environment", "location": "NAM", "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "createdBy": {"id": "55555555-5555-5555-5555-555555555555", "displayName": "Development Lead", "type": "User"}, "properties": {"environmentSku": "Sandbox", "azureRegion": "northamerica", "creationType": "User", "databaseType": "CommonDataService", "environmentType": "Sandbox", "isDefault": false, "retentionPeriod": "90Days", "settings": {"disablePortalsCreation": false, "disableTrialEnvironmentCreation": false, "requireEnvironmentAdmins": false}}}, {"id": "/providers/Microsoft.PowerPlatform/environments/test-env-01", "name": "test-env-01", "displayName": "Test Environment", "location": "EUR", "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "createdBy": {"id": "66666666-6666-6666-6666-666666666666", "displayName": "Test Lead", "type": "User"}, "properties": {"environmentSku": "Sandbox", "azureRegion": "europe", "creationType": "User", "databaseType": "CommonDataService", "environmentType": "Sandbox", "isDefault": false, "retentionPeriod": "30Days", "settings": {"disablePortalsCreation": true, "disableTrialEnvironmentCreation": true, "requireEnvironmentAdmins": true}}}]}