{"@odata.context": "https://graph.microsoft.com/beta/$metadata#admin/powerPlatform/policies/dlp", "value": [{"id": "a1b2c3d4-e5f6-7g8h-9i10-j11k12l13m14", "displayName": "Default DLP Policy", "description": "Default Data Loss Prevention Policy for all environments", "isEnabled": true, "mode": "Enforce", "environments": ["*"], "connectorGroups": [{"classification": "Confidential", "connectors": [{"id": "shared_sql", "name": "SQL Server", "type": "ApiConnection"}, {"id": "shared_salesforce", "name": "Salesforce", "type": "ApiConnection"}], "behaviors": [{"name": "BlockDownload", "isEnabled": true}, {"name": "RequireMFA", "isEnabled": true}]}, {"classification": "General", "connectors": [{"id": "shared_office365", "name": "Office 365", "type": "ApiConnection"}], "behaviors": []}]}, {"id": "n15o16p17-q18r-s19t-u20v-w21x22y23z24", "displayName": "High Security DLP Policy", "description": "Enhanced security policy for sensitive environments", "isEnabled": true, "mode": "Enforce", "environments": ["/providers/Microsoft.PowerPlatform/environments/prod-env-01", "/providers/Microsoft.PowerPlatform/environments/prod-env-02"], "connectorGroups": [{"classification": "Blocked", "connectors": [{"id": "shared_http", "name": "HTTP", "type": "Request"}, {"id": "shared_ftp", "name": "FTP", "type": "ApiConnection"}], "behaviors": [{"name": "Block", "isEnabled": true}]}]}]}