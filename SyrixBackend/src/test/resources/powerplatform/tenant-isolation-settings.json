{"@odata.context": "https://graph.microsoft.com/beta/$metadata#admin/powerPlatform/settings/tenantIsolation", "isIsolated": true, "blockExternalTenants": true, "allowedTenants": [{"tenantId": "11111111-1111-1111-1111-111111111111", "displayName": "Trusted Partner Org 1", "domainName": "partner1.onmicrosoft.com", "allowedConnections": ["PowerApps", "PowerAutomate"], "status": "Enabled", "lastModifiedDateTime": "2024-01-01T00:00:00Z"}, {"tenantId": "22222222-2222-2222-2222-222222222222", "displayName": "Trusted Partner Org 2", "domainName": "partner2.onmicrosoft.com", "allowedConnections": ["PowerApps"], "status": "Enabled", "lastModifiedDateTime": "2024-01-01T00:00:00Z"}], "settings": {"requireJustification": true, "requireApproval": true, "approverGroups": ["Global Administrators", "Power Platform Administrators"], "notifyOnNewConnection": true, "notificationEmails": ["<EMAIL>"]}, "restrictions": {"blockDataflows": true, "blockCustomConnectors": true, "blockOnPremiseConnections": true, "allowedIPRanges": ["10.0.0.0/24", "***********/24"]}}