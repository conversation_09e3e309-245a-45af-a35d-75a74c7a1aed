[{"id": "policy1", "displayName": "Require MFA for all users", "state": "enabled", "createdDateTime": "2023-01-01T00:00:00Z", "modifiedDateTime": "2023-02-01T00:00:00Z", "conditions": {"users": {"includeUsers": ["All"], "excludeUsers": [], "includeGroups": [], "excludeGroups": ["admin-group-id"]}, "applications": {"includeApplications": ["All"], "excludeApplications": []}, "locations": {"includeLocations": ["All"], "excludeLocations": ["trusted-locations-id"]}}, "grantControls": {"operator": "OR", "builtInControls": ["mfa"], "customAuthenticationFactors": [], "termsOfUse": []}}, {"id": "policy2", "displayName": "Block high-risk sign-ins", "state": "enabled", "createdDateTime": "2023-01-15T00:00:00Z", "modifiedDateTime": "2023-02-15T00:00:00Z", "conditions": {"users": {"includeUsers": ["All"], "excludeUsers": [], "includeGroups": [], "excludeGroups": ["admin-group-id"]}, "applications": {"includeApplications": ["All"], "excludeApplications": []}, "riskLevels": ["high"]}, "grantControls": {"operator": "OR", "builtInControls": ["block"], "customAuthenticationFactors": [], "termsOfUse": []}}]