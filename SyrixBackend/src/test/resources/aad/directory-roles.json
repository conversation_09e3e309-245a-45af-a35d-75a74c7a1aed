[{"id": "role1", "displayName": "Global Administrator", "description": "Can manage all aspects of the directory and services", "isEnabled": true, "resourceScopes": ["/"], "templateId": "62e90394-69f5-4237-9190-012177145e10", "members": [{"id": "user123", "displayName": "Admin User", "userPrincipalName": "<EMAIL>"}]}, {"id": "role2", "displayName": "Security Administrator", "description": "Can manage security aspects of the directory and services", "isEnabled": true, "resourceScopes": ["/"], "templateId": "194ae4cb-b126-40b2-bd5b-6091b380977d", "members": [{"id": "user456", "displayName": "Security Admin", "userPrincipalName": "<EMAIL>"}]}]