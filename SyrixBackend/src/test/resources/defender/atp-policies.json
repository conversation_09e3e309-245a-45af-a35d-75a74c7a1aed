{"@odata.context": "https://graph.microsoft.com/beta/$metadata#security/threatProtection/advancedThreatProtectionPolicies", "value": [{"id": "d4c1e26f-c71f-4c3a-c6df-1c49f48d3f84", "displayName": "Default ATP Policy", "isEnabled": true, "isDefault": true, "safeAttachments": {"isEnabled": true, "actionOnError": "Block", "redirectAddress": null, "enable": true, "allowSafeDocsOpen": false}, "safeLinks": {"isEnabled": true, "enableForEmail": true, "enableForOffice": true, "enableForTeams": true, "allowClickThrough": false, "scanUrls": true, "blockUrls": ["*.malicious.com", "*.phishing.com"], "trackClicks": true, "displayName": "Default Safe Links"}, "spoof": {"isEnabled": true, "action": "Quarantine", "allowedSpoofedSenders": []}, "impersonation": {"isEnabled": true, "protectTargetedDomains": true, "protectTargetedUsers": true, "targetedDomains": ["contoso.com"], "targetedUsers": ["<EMAIL>"]}}, {"id": "e5c1e26f-d71f-4c3a-d6df-1c49f48d3f85", "displayName": "High Security ATP Policy", "isEnabled": true, "isDefault": false, "safeAttachments": {"isEnabled": true, "actionOnError": "Block", "redirectAddress": "<EMAIL>", "enable": true, "allowSafeDocsOpen": false}, "safeLinks": {"isEnabled": true, "enableForEmail": true, "enableForOffice": true, "enableForTeams": true, "allowClickThrough": false, "scanUrls": true, "blockUrls": ["*.suspicious.com", "*.untrusted.com", "*.risky.net"], "trackClicks": true, "displayName": "Strict Safe Links"}, "spoof": {"isEnabled": true, "action": "Quarantine", "allowedSpoofedSenders": []}, "impersonation": {"isEnabled": true, "protectTargetedDomains": true, "protectTargetedUsers": true, "targetedDomains": ["contoso.com", "contoso.net"], "targetedUsers": ["<EMAIL>", "<EMAIL>"]}}]}