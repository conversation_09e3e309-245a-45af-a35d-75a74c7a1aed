{"@odata.context": "https://graph.microsoft.com/beta/$metadata#policies/teamsAppSetupPolicies", "value": [{"id": "policy2", "displayName": "Restricted Policy", "description": "Restricted meeting policy settings", "isGlobalDefault": false, "allowMeetingRegistration": false, "allowChannelMeeting": false, "allowPrivateMeeting": true, "allowTranscription": false, "allowRecording": false, "allowCloudRecording": false, "allowedStreamingApps": [], "blockedApps": ["broadcast"], "createdDateTime": "2024-01-01T00:00:00Z", "modifiedDateTime": "2024-01-01T00:00:00Z"}]}