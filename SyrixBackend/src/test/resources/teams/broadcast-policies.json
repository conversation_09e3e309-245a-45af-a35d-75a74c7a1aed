[{"identity": "Global", "description": "Global broadcast policy", "allowBroadcastScheduling": true, "allowBroadcastTranscription": true, "broadcastRecordingMode": "AlwaysEnabled", "allowSdnProviderForBroadcastMeeting": true}, {"identity": "Limited", "description": "Limited broadcast policy", "allowBroadcastScheduling": false, "allowBroadcastTranscription": false, "broadcastRecordingMode": "AlwaysDisabled", "allowSdnProviderForBroadcastMeeting": false}]