{"@odata.context": "https://graph.microsoft.com/beta/$metadata#policies/teamsAppSetupPolicies", "value": [{"id": "policy1", "displayName": "Global Policy", "description": "Global meeting policy settings", "isGlobalDefault": true, "allowMeetingRegistration": true, "allowChannelMeeting": true, "allowPrivateMeeting": true, "allowTranscription": true, "allowRecording": true, "allowCloudRecording": true, "allowedStreamingApps": ["broadcast"], "blockedApps": [], "createdDateTime": "2024-01-01T00:00:00Z", "modifiedDateTime": "2024-01-01T00:00:00Z"}], "@odata.nextLink": "https://graph.microsoft.com/beta/policies/teamsAppSetupPolicies?$skip=1"}