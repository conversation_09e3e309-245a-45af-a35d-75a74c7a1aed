// src/test/resources/test-data/mock-responses/conditional-access-policies.json
{
  "value": [
    {
      "id": "policy-1",
      "displayName": "Test Policy 1",
      "state": "enabled",
      "conditions": {
        "users": {
          "includeUsers": ["all"],
          "excludeUsers": []
        }
      }
    }
  ]
}

// src/test/resources/test-data/mock-responses/directory-roles.json
{
  "value": [
    {
      "id": "role-1",
      "displayName": "Global Administrator",
      "description": "Global Administrator role"
    }
  ]
}