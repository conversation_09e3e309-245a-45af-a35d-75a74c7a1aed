{"value": [{"id": "c1c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "Customer Data Access", "description": "Defines access levels for customer data", "type": "EntityPolicy", "settings": {"entityName": "account", "accessRights": ["Read", "Write", "Append", "AppendTo"], "scopeType": "BusinessUnit", "inheritanceSettings": {"inheritFromParent": true, "propagateToChild": true}}}, {"id": "c2c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "Sales Data Sharing", "description": "Controls sharing settings for sales data", "type": "SharingPolicy", "settings": {"entityName": "opportunity", "cascadeSettings": {"cascadeAssign": true, "cascadeShare": true, "cascadeUnshare": true}, "defaultAccessLevel": "Read", "allowedAccessLevels": ["Read", "Write", "Delete"]}}]}