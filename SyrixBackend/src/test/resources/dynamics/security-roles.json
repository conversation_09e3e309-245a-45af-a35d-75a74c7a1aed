{"value": [{"id": "e2c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "System Administrator", "description": "Full administrative privileges across all components", "businessUnitId": "d4c7f8ac-8196-48ed-86ac-14ef9f232e22", "isSystemRole": true, "roleprivileges": [{"privilegeId": "a1c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvCreateRecord", "depth": "Global", "privilegeType": "Create"}, {"privilegeId": "a2c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvReadRecord", "depth": "Global", "privilegeType": "Read"}, {"privilegeId": "a3c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvWriteRecord", "depth": "Global", "privilegeType": "Write"}, {"privilegeId": "a4c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvDeleteRecord", "depth": "Global", "privilegeType": "Delete"}]}, {"id": "e3c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "Sales Manager", "description": "Sales team management privileges", "businessUnitId": "d4c7f8ac-8196-48ed-86ac-14ef9f232e22", "isSystemRole": false, "roleprivileges": [{"privilegeId": "a5c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvCreateSales", "depth": "BusinessUnit", "privilegeType": "Create"}, {"privilegeId": "a6c7f8ac-8196-48ed-86ac-14ef9f232e22", "name": "prvReadSales", "depth": "BusinessUnit", "privilegeType": "Read"}]}]}