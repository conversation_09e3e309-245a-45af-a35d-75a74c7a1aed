{"auditSettings": {"isAuditEnabled": true, "retentionPeriodDays": 365, "auditLogCategories": [{"name": "UserAccess", "isEnabled": true, "retentionPeriodDays": 90}, {"name": "SecurityConfiguration", "isEnabled": true, "retentionPeriodDays": 365}, {"name": "EntityChanges", "isEnabled": true, "retentionPeriodDays": 180}, {"name": "SharedAccess", "isEnabled": true, "retentionPeriodDays": 90}], "entityAuditSettings": [{"entityName": "account", "isAuditEnabled": true, "auditOperations": ["Create", "Update", "Delete", "Read"]}, {"entityName": "contact", "isAuditEnabled": true, "auditOperations": ["Create", "Update", "Delete"]}]}}