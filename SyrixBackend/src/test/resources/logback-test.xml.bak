<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>

    <!-- Set test-specific log levels -->
    <logger name="com.syrix.entra" level="DEBUG" />
    
    <!-- Set remediator error logs to DEBUG during tests to avoid flooding console with expected test errors -->
    <logger name="io.syrix.products.microsoft.exo.remediation" level="INFO" />
    <logger name="io.syrix.products.microsoft.exo.remediation.ExchangeAuditDisabledRemediator" level="INFO" />
</configuration>