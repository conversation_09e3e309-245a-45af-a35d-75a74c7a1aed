{"value": [{"id": "/providers/Microsoft.BusinessAppPlatform/environments/5f7c40a2-06b4-4b40-a560-b42e4f3e7b6d", "type": "Microsoft.BusinessAppPlatform/environments", "name": "5f7c40a2-06b4-4b40-a560-b42e4f3e7b6d", "location": "unitedstates", "properties": {"displayName": "Default Environment", "description": "Default environment for the organization", "createdTime": "2023-06-15T10:30:45.000Z", "createdBy": {"id": "a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890", "displayName": "System Admin", "email": "<EMAIL>", "type": "User"}, "lastModifiedTime": "2024-04-01T08:15:30.000Z", "environmentType": "Production", "environmentSku": "Standard", "isDefault": true, "state": "Enabled", "databaseType": "CommonDataService", "retentionPeriod": "90 days", "linkedEnvironments": [], "apiEndpoints": {"apiEndpoint": "https://org12345.api.crm.dynamics.com/api/data/v9.2/", "dataverseEndpoint": "https://org12345.crm.dynamics.com/", "flowEndpoint": "https://us.flow.microsoft.com/environments/5f7c40a2-06b4-4b40-a560-b42e4f3e7b6d/"}, "capabilities": {"powerApps": true, "powerAutomate": true, "powerBI": true, "dataverse": true, "aiBuilder": true}, "securityConfiguration": {"dlpStatus": "Enabled", "conditionalAccessPolicies": "Enabled", "dataLossPrevention": "Enhanced"}}}, {"id": "/providers/Microsoft.BusinessAppPlatform/environments/9a1b23c4-5d6e-7f8a-9b0c-1d2e3f4a5b6c", "type": "Microsoft.BusinessAppPlatform/environments", "name": "9a1b23c4-5d6e-7f8a-9b0c-1d2e3f4a5b6c", "location": "northamerica", "properties": {"displayName": "Development Environment", "description": "Environment for development and testing", "createdTime": "2023-08-20T14:25:10.000Z", "createdBy": {"id": "f7e6d5c4-b3a2-1098-f7e6-d5c4b3a21098", "displayName": "Development Lead", "email": "<EMAIL>", "type": "User"}, "lastModifiedTime": "2024-05-10T16:45:20.000Z", "environmentType": "Sandbox", "environmentSku": "Standard", "isDefault": false, "state": "Enabled", "databaseType": "CommonDataService", "retentionPeriod": "30 days", "linkedEnvironments": [{"environmentId": "5f7c40a2-06b4-4b40-a560-b42e4f3e7b6d", "linkType": "Test<PERSON>arget"}], "apiEndpoints": {"apiEndpoint": "https://orgdev56789.api.crm.dynamics.com/api/data/v9.2/", "dataverseEndpoint": "https://orgdev56789.crm.dynamics.com/", "flowEndpoint": "https://us.flow.microsoft.com/environments/9a1b23c4-5d6e-7f8a-9b0c-1d2e3f4a5b6c/"}, "capabilities": {"powerApps": true, "powerAutomate": true, "powerBI": true, "dataverse": true, "aiBuilder": false}, "securityConfiguration": {"dlpStatus": "Enabled", "conditionalAccessPolicies": "Enabled", "dataLossPrevention": "Basic"}}}, {"id": "/providers/Microsoft.BusinessAppPlatform/environments/3e4r5t6y-7u8i-9o0p-3e4r-5t6y7u8i9o0p", "type": "Microsoft.BusinessAppPlatform/environments", "name": "3e4r5t6y-7u8i-9o0p-3e4r-5t6y7u8i9o0p", "location": "europe", "properties": {"displayName": "Europe Production", "description": "Production environment for European operations", "createdTime": "2023-11-05T09:45:30.000Z", "createdBy": {"id": "p0o9i8u7-y6t5-r4e3-p0o9-i8u7y6t5r4e3", "displayName": "European Operations Lead", "email": "<EMAIL>", "type": "User"}, "lastModifiedTime": "2024-04-25T11:20:15.000Z", "environmentType": "Production", "environmentSku": "Premium", "isDefault": false, "state": "Enabled", "databaseType": "CommonDataService", "retentionPeriod": "90 days", "linkedEnvironments": [], "apiEndpoints": {"apiEndpoint": "https://orgeur78901.api.crm.dynamics.com/api/data/v9.2/", "dataverseEndpoint": "https://orgeur78901.crm.dynamics.com/", "flowEndpoint": "https://europe.flow.microsoft.com/environments/3e4r5t6y-7u8i-9o0p-3e4r-5t6y7u8i9o0p/"}, "capabilities": {"powerApps": true, "powerAutomate": true, "powerBI": true, "dataverse": true, "aiBuilder": true}, "securityConfiguration": {"dlpStatus": "Enabled", "conditionalAccessPolicies": "Enabled", "dataLossPrevention": "Enhanced"}}}], "nextLink": null}