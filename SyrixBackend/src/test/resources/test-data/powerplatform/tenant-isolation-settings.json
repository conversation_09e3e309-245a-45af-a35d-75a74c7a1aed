{"properties": {"tenantIsolationPolicy": "AllowTenantIsolation", "isIsolated": true, "lastModifiedTime": "2024-03-25T14:30:45Z", "enforcedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isolationStatus": "Active", "isolationLevel": "High", "allowedTenants": [{"tenantId": "72f988bf-86f1-41af-91ab-2d7cd011db47", "displayName": "Partner Organization", "allowedConnectionTypes": ["Dataflow", "PowerBI"], "lastModifiedTime": "2024-02-15T10:20:30Z", "approvedBy": "<EMAIL>"}, {"tenantId": "a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890", "displayName": "Vendor Organization", "allowedConnectionTypes": ["Dataflow"], "lastModifiedTime": "2024-03-10T08:45:15Z", "approvedBy": "<EMAIL>"}], "blockedTenants": [{"tenantId": "z9y8x7w6-v5u4-t3s2-z9y8-x7w6v5u4t3s2", "displayName": "Blocked Organization", "blockReason": "Security Policy", "lastModifiedTime": "2024-01-30T16:15:45Z", "blockedBy": "<EMAIL>"}], "defaultConnectionPolicy": "BlockByDefault", "dataExfiltrationPreventionStatus": "Enabled", "connector": {"customConnectorsPolicy": "BlockNonAdminCreation", "standardConnectorsRestriction": "RestrictHigh", "httpConnectorRestriction": "BlockAll", "approvalWorkflow": "Enabled", "reviewRequired": true}, "externalSharingSettings": {"allowExternalSharing": false, "requireAuthentication": true, "allowedDomains": ["partner.com", "vendor.org"], "blockedDomains": ["competitor.com"]}, "auditSettings": {"auditCrossTenantConnections": true, "auditDataExfiltration": true, "retentionPeriodInDays": 90, "alertOnSuspiciousActivity": true}}, "id": "/providers/Microsoft.BusinessAppPlatform/scopes/admin/tenantIsolationPolicies/default", "name": "default", "type": "Microsoft.BusinessAppPlatform/scopes/tenantIsolationPolicies"}