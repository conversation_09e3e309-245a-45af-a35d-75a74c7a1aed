{"properties": {"disableEnvironmentCreationByNonAdminUsers": true, "protectionStatus": "enabled", "protectionLevel": "high", "maxNumberOfEnvironmentsAllowedPerTenant": 50, "createNewEnvironmentApiDisabled": false, "environmentCreationRequestsRequireAdminApproval": true, "enableAutomaticEnvironmentDeletion": true, "automaticEnvironmentDeletionRetentionPeriodInDays": 90, "defaultDataLossPrevention": "basic", "creationTypeSettings": [{"type": "Trial", "status": "enabled", "limitations": {"maxDurationInDays": 30, "maxAllowedPerUser": 2, "requireApproval": false}}, {"type": "Sandbox", "status": "enabled", "limitations": {"maxAllowedPerUser": 3, "requireApproval": true, "approvalWorkflow": "standard"}}, {"type": "Production", "status": "restrictedToAdmins", "limitations": {"requireBusinessJustification": true, "requireApproval": true, "approvalWorkflow": "enhanced"}}, {"type": "Developer", "status": "enabled", "limitations": {"maxAllowedPerUser": 5, "requireApproval": false}}], "defaultRegionSettings": {"defaultRegion": "NorthAmerica", "allowUserRegionOverride": false, "disabledRegions": ["Asia", "SouthAmerica"]}, "allowedDataStorageLocations": ["UnitedStates", "Canada", "Europe"], "securitySettings": {"requireMFA": true, "conditionalAccessPoliciesEnabled": true, "minSecurityBaseline": "enhanced"}}, "id": "/providers/Microsoft.BusinessAppPlatform/scopes/admin/environmentCreationPolicies/default", "name": "default", "type": "Microsoft.BusinessAppPlatform/scopes/environmentCreationPolicies"}