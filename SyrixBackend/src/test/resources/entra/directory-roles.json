{"@odata.context": "https://graph.microsoft.com/beta/$metadata#directoryRoles", "value": [{"id": "role-1", "displayName": "Global Administrator", "description": "Company administrators with full access", "roleTemplateId": "62e90394-69f5-4237-9190-012177145e10", "isEnabled": true, "isBuiltIn": true, "resourceScopes": ["/"]}, {"id": "role-2", "displayName": "User Administrator", "description": "Manage user access to resources", "roleTemplateId": "fe930be7-5e62-47db-91af-98c3a49a38b1", "isEnabled": true, "isBuiltIn": true, "resourceScopes": ["/"]}]}