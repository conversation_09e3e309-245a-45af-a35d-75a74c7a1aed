{"@odata.context": "https://graph.microsoft.com/beta/$metadata#identity/conditionalAccess/policies", "value": [{"id": "ca-policy-1", "displayName": "Require MFA for Admin Users", "state": "enabled", "createdDateTime": "2024-01-01T00:00:00Z", "modifiedDateTime": "2024-01-01T00:00:00Z", "conditions": {"userRiskLevels": [], "signInRiskLevels": [], "clientAppTypes": ["all"], "platforms": {"includePlatforms": ["all"], "excludePlatforms": []}, "locations": {"includeLocations": ["all"], "excludeLocations": ["AllTrusted"]}, "applications": {"includeApplications": ["All"], "excludeApplications": [], "includeUserActions": []}, "users": {"includeUsers": ["All"], "excludeUsers": [], "includeGroups": [], "excludeGroups": [], "includeRoles": ["Global Administrator"], "excludeRoles": []}}, "grantControls": {"operator": "AND", "builtInControls": ["mfa"], "customAuthenticationFactors": [], "termsOfUse": []}}]}