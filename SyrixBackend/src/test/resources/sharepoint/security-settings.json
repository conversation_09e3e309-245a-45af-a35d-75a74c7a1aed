{"policies": [{"id": "policy1", "name": "SharePoint Sensitive Content Policy", "description": "Detects and protects sensitive content in SharePoint and OneDrive", "mode": "Enable", "rules": [{"name": "Financial Data Rule", "description": "Detects financial data in SharePoint and OneDrive", "contentTypes": ["Word", "Excel", "PowerPoint", "PDF"], "sensitiveTypes": ["Credit Card Number", "Bank Account Number"], "actions": ["Block", "Notify", "Encrypt"]}]}, {"id": "policy2", "name": "SharePoint External Sharing Policy", "description": "Controls external sharing of sensitive content", "mode": "Enable", "rules": [{"name": "Prevent External Sharing of Sensitive Content", "description": "Blocks external sharing of sensitive documents", "contentTypes": ["All"], "sensitiveTypes": ["Confidential", "Restricted"], "actions": ["BlockExternalSharing", "Notify"]}]}]}