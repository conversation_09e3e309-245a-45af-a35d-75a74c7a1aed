<?xml version="1.0" encoding="UTF-8"?>
<!-- 
This logback configuration DISABLES logback completely to prevent conflicts with JBoss LogManager.
DO NOT REMOVE - this prevents logback from activating even if it's on the classpath.
-->
<configuration>
    <!-- Completely disable logback -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    
    <!-- No appenders defined = no logging output from logback -->
    
    <!-- Root logger with no appenders = silent -->
    <root level="OFF">
        <!-- No appenders attached -->
    </root>
    
    <!-- Disable all automatic configuration -->
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
</configuration>