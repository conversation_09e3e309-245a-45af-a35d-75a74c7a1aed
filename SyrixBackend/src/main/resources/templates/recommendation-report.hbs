    <!DOCTYPE html>
    <html>
    <head>
        <title>Microsoft Entra ID Recommendations Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background-color: #0078d4;
                color: white;
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .recommendation {
                background-color: white;
                margin-bottom: 10px;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .recommendation-header {
                padding: 15px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
             .recommendation-header.expanded {
                background-color: #f0f0f0;
            }
            .recommendation-content {
                padding: 15px;
                border-top: 1px solid #eee;
                display: none;
            }
            .tag {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                margin-left: 10px;
                color: white;
            }
             .completed-tag {
                background-color: #28a745; /* Green */
                display: inline-flex;
                 align-items: center;
             }
            .completed-tag::before{
                content: '✓';
                margin-right: 5px;
            }
            .active-tag{
               background-color: #ffc107; /* Yellow */
            }
            .action-steps {
                background-color: #f8f9fa;
                padding: 15px;
                margin-top: 10px;
                border-radius: 5px;
            }
            .step {
                margin-bottom: 10px;
            }
            .expand-all {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-bottom: 20px;
            }
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }
            .stat-card {
                background-color: white;
                padding: 15px;
                border-radius: 5px;
                text-align: center;
            }
                /* Container around the toggle */
                .control-bar {
                    display: flex;
                    justify-content: space-between; /* or flex-end if you only want it on the right */
                    align-items: center;
                    margin-bottom: 20px;
                }
                /* The switch container */
                .switch {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    width: auto; /* allow text to fit alongside the slider */
                    margin-left: auto; /* optional: push toggle to the right */
                }
                .switch input {
                    display: none; /* hide the default checkbox */
                }
                /* The slider */
                .slider {
                    width: 60px;
                    height: 30px;
                    background-color: #ccc;
                    border-radius: 30px;
                    cursor: pointer;
                    position: relative;
                    transition: 0.4s;
                }
                .slider:before {
                    content: "";
                    position: absolute;
                    width: 26px;
                    height: 26px;
                    left: 2px;
                    bottom: 2px;
                    background-color: #fff;
                    border-radius: 50%;
                    transition: 0.4s;
                }
                /* Toggle checked */
                input:checked + .slider {
                    background-color: #2196f3;
                }
                input:checked + .slider:before {
                    transform: translateX(30px);
                }
                /* Text next to the slider */
                .switch-text {
                    margin-left: 10px; /* space between slider and text */
                    font-size: 14px;
                    color: #333;
                }

            </style>
        </head>
        <body>
        <div class="container">
            <div class="header">
                <h1>Microsoft Entra ID Recommendations Report</h1>
                <p>Generated on: {{formatDate generatedDate}}</p>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <h3>Total Recommendations</h3>
                    <p>{{totalCount}}</p>
                </div>
                <div class="stat-card">
                    <h3>Active</h3>
                    <p>{{activeCount}}</p>
                </div>
                <div class="stat-card">
                    <h3>Completed</h3>
                    <p>{{completedCount}}</p>
                </div>
                <div class="stat-card">
                    <h3>Postponed</h3>
                    <p>{{postponedCount}}</p>
                </div>
            </div>

            <div class="control-bar">
                <button class="expand-all" onclick="toggleAll()">Expand All</button>

                <!-- Toggle Switch (right-aligned) -->
                <label class="switch">
                    <input type="checkbox" id="toggleCompletedCheckbox">
                    <span class="slider"></span>
                    <span class="switch-text">Show Completed</span>
                </label>
            </div>

            {{#each recommendations}}
                <div class="recommendation">
                    <div class="recommendation-header">
                        <div>
                            <span>{{displayName}}</span>
                            {{#if (eq status "Completed")}}
                                <span class="tag completed-tag">{{status}}</span>
                            {{else if (eq status "Active")}}
                                <span class="tag active-tag">{{status}}</span>
                            {{else}}
                                <span class="tag" style="background-color: {{statusColor status}}">{{status}}</span>
                            {{/if}}
                            <span class="tag" style="background-color: {{priorityColor priority}}">{{priority}}</span>
                        </div>
                        <div>Score: {{currentScore}}/{{maxScore}}</div>
                    </div>
                    <div class="recommendation-content">
                        <p><strong>Category:</strong> {{category}}</p>
                        <p><strong>Benefits:</strong> {{benefits}}</p>
                        <p><strong>Insights:</strong> {{insights}}</p>
                        <p><strong>Impact Type:</strong> {{impactType}}</p>
                        <p><strong>Last Checked:</strong> {{formatDate lastCheckedDateTime}}</p>

                        {{#if actionSteps}}
                            <div class="action-steps">
                                <h4>Action Steps:</h4>
                                {{#each actionSteps}}
                                    <div class="step">
                                        {{text}}
                                        {{#if actionUrl}}
                                            <a href="{{actionUrl.url}}" target="_blank">{{actionUrl.displayName}}</a>
                                        {{/if}}
                                    </div>
                                {{/each}}
                            </div>
                        {{/if}}
                    </div>
                </div>
            {{/each}}
        </div>

        <script>
            let expanded = false;

            function toggle(element) {
                const content = element.nextElementSibling;
                const header = element;
                content.style.display = content.style.display === 'none' ? 'block' : 'none';
                 header.classList.toggle('expanded', content.style.display === 'block');
            }
            function init() {
            const headers = document.querySelectorAll('.recommendation-header');
            headers.forEach(header => {
              header.addEventListener('click', function(event) {
                // Prevent clicks on nested elements from interfering
                event.stopPropagation();

                // Find the content element
                const content = this.nextElementSibling;

                // Toggle display
                const isVisible = content.style.display === 'block';
                content.style.display = isVisible ? 'none' : 'block';

                // Toggle expanded styling
                this.classList.toggle('expanded', !isVisible);
              });
            });
          }

          document.addEventListener('DOMContentLoaded', init);
          document.addEventListener('DOMContentLoaded', () => {
            const toggleCheckbox = document.getElementById('toggleCompletedCheckbox');
            toggleCheckbox.addEventListener('change', toggleCompleted);
           });

            function toggleAll() {
                const contents = document.querySelectorAll('.recommendation-content');
                const headers = document.querySelectorAll('.recommendation-header');
                expanded = !expanded;
                contents.forEach(content => {
                    content.style.display = expanded ? 'block' : 'none';
                });
                 headers.forEach(header => {
                     header.classList.toggle('expanded', expanded)
                })
                document.querySelector('.expand-all').textContent =
                    expanded ? 'Collapse All' : 'Expand All';
            }

            let hideCompleted = false;

          function toggleCompleted() {
            hideCompleted = !hideCompleted;
            const completedTags = document.querySelectorAll('.recommendation-header .completed-tag');

            completedTags.forEach(tag => {
              const recommendation = tag.closest('.recommendation');
              // Hide if toggled, otherwise show
              recommendation.style.display = hideCompleted ? 'none' : 'block';
            });
          }

          document.addEventListener('DOMContentLoaded', () => {
            // Link the checkbox change to toggleCompleted
            const toggleCheckbox = document.getElementById('toggleCompletedCheckbox');
            toggleCheckbox.addEventListener('change', toggleCompleted);
          });
        </script>
        </body>
    </html>