<!DOCTYPE html>
<html>
<head>
    <title>Enterprise Applications Report</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-100" x-data="{}">
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Enterprise Applications Report</h1>

    <!-- Report Summary -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Report Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600">Generated</p>
                <p class="text-lg font-medium">{{formatDate generatedAt}}</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600">Total Applications</p>
                <p class="text-lg font-medium">{{totalApps}}</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600">High Risk Apps</p>
                <p class="text-lg font-medium text-red-600">{{appsWithHighRiskPerms}}</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600">Missing Justification</p>
                <p class="text-lg font-medium text-amber-600">{{appsWithoutJustification}}</p>
            </div>
        </div>

        <!-- Risk Distribution -->
        <div class="mt-6">
            <h3 class="font-semibold mb-2">Risk Level Distribution</h3>
            <div class="grid grid-cols-4 gap-4">
                {{#each riskDistribution}}
                    <div class="p-3 rounded-lg {{getRiskColorClass @key}}">
                        <p class="text-sm">{{@key}}</p>
                        <p class="text-lg font-medium">{{this}}</p>
                    </div>
                {{/each}}
            </div>
        </div>
    </div>

    <!-- Applications List -->
    {{#each applications}}
        <div class="mb-6 rounded-lg shadow-md p-6 {{getRiskColorClass (getRiskLevel this)}}" x-data="{
            isOpen: false,
            activeTab: 'basic'
        }">
            <!-- Application Header -->
            <div class="flex justify-between items-center cursor-pointer" @click="isOpen = !isOpen">
                <div class="flex items-center space-x-3">
                    <!-- Risk Level Icon -->
                    {{#if (eq (getRiskLevel this) 'CRITICAL')}}
                        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    {{else if (eq (getRiskLevel this) 'HIGH')}}
                        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    {{else if (eq (getRiskLevel this) 'MEDIUM')}}
                        <svg class="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    {{else}}
                        <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    {{/if}}
                    <div>
                        <h2 class="text-xl font-semibold">{{basic.displayName}}</h2>
                        <div class="flex space-x-2 text-sm text-gray-600">
                            <span>{{basic.publisherName}}</span>
                            {{#unless justification}}
                                <span class="text-amber-600">• No Justification</span>
                            {{/unless}}
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="{{getRiskBadgeClass (getRiskLevel this)}} px-3 py-1 rounded-full text-sm font-medium">
                        {{getRiskLevel this}} Risk
                    </span>
                    <button class="text-blue-500 hover:text-blue-700">
                        <span x-text="isOpen ? 'Hide Details' : 'Show Details'"></span>
                    </button>
                </div>
            </div>

            <!-- Collapsible Content -->
            <div x-cloak x-show="isOpen" x-transition class="mt-4">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-4" aria-label="Tabs">
                        <button @click="activeTab = 'basic'"
                                :class="activeTab === 'basic' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                                class="py-2 px-1 border-b-2 font-medium text-sm">
                            Basic Details
                        </button>
                        <button @click="activeTab = 'permissions'"
                                :class="activeTab === 'permissions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                                class="py-2 px-1 border-b-2 font-medium text-sm">
                            Permissions
                        </button>
                        <button @click="activeTab = 'users'"
                                :class="activeTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'"
                                class="py-2 px-1 border-b-2 font-medium text-sm">
                            Users & Groups
                        </button>
                    </nav>
                </div>

                <!-- Basic Details Tab -->
                <div x-show="activeTab === 'basic'" class="space-y-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Application Information</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600">Application ID</p>
                                <p class="font-medium">{{basic.id}}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Publisher</p>
                                <p class="font-medium">{{basic.publisherName}}</p>
                            </div>
                        </div>

                        <!-- Justification Section -->
                        <div class="mt-4">
                            <h4 class="font-semibold mb-2">Justification</h4>
                            {{#if justification}}
                                <div class="p-4 bg-green-50 rounded-lg">
                                    <p class="text-green-800">{{justification}}</p>
                                </div>
                            {{else}}
                                <div class="p-4 bg-amber-50 rounded-lg">
                                    <p class="text-amber-800">No justification provided for this application</p>
                                </div>
                            {{/if}}
                        </div>
                    </div>

                    {{#if basic.appRoles}}
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Application Roles</h3>
                            <div class="divide-y">
                                {{#each basic.appRoles}}
                                    <div class="py-2">
                                        <p class="font-medium">{{displayName}}</p>
                                        <p class="text-sm text-gray-600">{{description}}</p>
                                    </div>
                                {{/each}}
                            </div>
                        </div>
                    {{/if}}
                </div>

                <!-- Permissions Tab -->
                <div x-show="activeTab === 'permissions'" class="space-y-4">
                    {{#if permissions}}
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Application Permissions</h3>
                            <div class="divide-y">
                                {{#each permissions}}
                                    <div class="py-3">
                                        <div class="flex items-start justify-between">
                                            <div>
                                                <p class="font-medium">{{resourceDisplayName}}</p>
                                                <p>{{appRoleDisplayName}}</p>
                                                {{#if appRoleDescription}}
                                                    <p class="text-sm text-gray-600 mt-1">{{appRoleDescription}}</p>
                                                {{/if}}
                                            </div>
                                            {{#if isHighRisk}}
                                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">High Risk</span>
                                            {{/if}}
                                        </div>
                                    </div>
                                {{/each}}
                            </div>
                        </div>
                    {{/if}}

                    {{#if delegatedPermissions}}
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Delegated Permissions</h3>
                            <div class="divide-y">
                                {{#each delegatedPermissions}}
                                    <div class="py-3">
                                        <p class="font-medium">{{resourceDisplayName}}</p>
                                        <p>{{scope}}</p>
                                        <p class="text-sm text-gray-600 mt-1">Consent Type: {{consentType}}</p>
                                    </div>
                                {{/each}}
                            </div>
                        </div>
                    {{/if}}
                </div>

                <!-- Users & Groups Tab -->
                <div x-show="activeTab === 'users'" class="space-y-4">
                    {{#if assignedUsers}}
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Assigned Users & Groups</h3>
                            <div class="divide-y">
                                {{#each assignedUsers}}
                                    <div class="py-3">
                                        <div class="flex items-start justify-between">
                                            <div>
                                                <p class="font-medium">{{displayName}}</p>
                                                {{#if userPrincipalName}}
                                                    <p class="text-sm text-gray-600">{{userPrincipalName}}</p>
                                                {{/if}}
                                                <p class="text-sm text-gray-600">Type: {{principalType}}</p>
                                                {{#if appRoleDisplayName}}
                                                    <p class="text-sm text-blue-600 mt-1">Role: {{appRoleDisplayName}}</p>
                                                {{/if}}
                                            </div>
                                        </div>
                                    </div>
                                {{/each}}
                            </div>
                        </div>
                    {{else}}
                        <div class="bg-gray-50 rounded-lg p-4 text-center text-gray-600">
                            No users or groups assigned
                        </div>
                    {{/if}}
                </div>
            </div>
        </div>
    {{/each}}
</div>

<style>
    [x-cloak] { display: none !important; }
</style>
</body>
</html>