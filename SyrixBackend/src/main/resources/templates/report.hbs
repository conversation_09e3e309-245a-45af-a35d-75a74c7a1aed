<!-- templates/report.hbs -->
<!DOCTYPE html>
<html lang="en" class="{{darkModeClass darkMode}}">
<head>
    <meta charset="UTF-8">
    <title>{{title}}</title>
    <style>{{{css}}}</style>
</head>
<body>
    <div class="container">
        <header>
            <h1>{{title}}</h1>
            {{{baselineUrl}}}
        </header>

        <!-- Tenant Data -->
        <div class="tenant-info">
            {{#with tenantData}}
            <table id="tenant-data" style="text-align:center;">
                <tr><th>Tenant Display Name</th><td>{{displayName}}</td></tr>
                <tr><th>Report Date</th><td>{{reportDate}}</td></tr>
                <tr><th>Baseline Version</th><td>{{baselineVersion}}</td></tr>
                <tr><th>Module Version</th><td>{{moduleVersion}}</td></tr>
            </table>
            {{/with}}
        </div>

        <!-- AAD Warning -->
        {{#if aadWarning}}
        <div class="aad-warning">
            {{{aadWarning}}}
        </div>
        {{/if}}

        <!-- License Info for AAD -->
        {{#if licenseInfo}}
        <div class="license-info">
            <h2>Tenant Licensing Information</h2>
            <table id="license-info" style="text-align:center;">
                <tr>
                    <th>Product Name</th>
                    <th>License SKU Identifier</th>
                    <th>Licenses in Use</th>
                    <th>Total Licenses</th>
                </tr>
                {{#each licenseInfo}}
                <tr>
                    <td>{{productName}}</td>
                    <td>{{skuIdentifier}}</td>
                    <td>{{licensesInUse}}</td>
                    <td>{{totalLicenses}}</td>
                </tr>
                {{/each}}
            </table>
        </div>
        {{/if}}

        <!-- Report Content -->
        <div class="report-content">
            {{#each fragments}}
                {{{this}}}
            {{/each}}
        </div>
    </div>

    <script>{{{javascript}}}</script>
</body>
</html>