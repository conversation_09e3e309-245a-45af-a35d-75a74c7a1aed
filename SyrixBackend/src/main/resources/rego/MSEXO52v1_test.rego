package exo
import rego.v1

# Test cases for MS.EXO.5.2v1 - Modern Authentication (OAuth2ClientProfileEnabled) Policy

# Test 1: Compliant configuration - Modern Authentication is enabled
test_MSEXO52v1_compliant_modern_auth_enabled if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": true,
                "AuditDisabled": false,
                "CustomerLockboxEnabled": true
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    output.Criticality == "Shall"
    output.Commandlet == ["Get-OrganizationConfig"]
}

# Test 2: Non-compliant configuration - Modern Authentication is explicitly disabled
test_MSEXO52v1_non_compliant_modern_auth_disabled if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": false,
                "AuditDisabled": false,
                "CustomerLockboxEnabled": true
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    count(output.ActualValue) > 0
}

# Test 3: Non-compliant configuration - OAuth2ClientProfileEnabled field is missing (null)
test_MSEXO52v1_non_compliant_missing_oauth_field if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "AuditDisabled": false,
                "CustomerLockboxEnabled": true
                # OAuth2ClientProfileEnabled is missing
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    count(output.ActualValue) > 0
}

# Test 4: Multiple organization configs - mixed compliance
test_MSEXO52v1_multiple_orgs_mixed_compliance if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Compliant Organization",
                "OAuth2ClientProfileEnabled": true,
                "AuditDisabled": false
            },
            {
                "Name": "Non-Compliant Organization",
                "OAuth2ClientProfileEnabled": false,
                "AuditDisabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    # Should only include the non-compliant organization
    count(output.ActualValue) == 1
}

# Test 5: Multiple organization configs - all compliant
test_MSEXO52v1_multiple_orgs_all_compliant if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Organization 1",
                "OAuth2ClientProfileEnabled": true,
                "AuditDisabled": false
            },
            {
                "Name": "Organization 2", 
                "OAuth2ClientProfileEnabled": true,
                "CustomerLockboxEnabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    count(output.ActualValue) == 0
}

# Test 6: Multiple organization configs - all non-compliant
test_MSEXO52v1_multiple_orgs_all_non_compliant if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Organization 1",
                "OAuth2ClientProfileEnabled": false,
                "AuditDisabled": false
            },
            {
                "Name": "Organization 2",
                # OAuth2ClientProfileEnabled is missing
                "AuditDisabled": true
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    # Should include both non-compliant organizations
    count(output.ActualValue) == 2
}

# Test 7: Empty organization config array
test_MSEXO52v1_empty_organization_config if {
    output := tests[_] with input as {
        "organization_config": []
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    count(output.ActualValue) == 0
}

# Test 8: Missing organization_config field entirely
test_MSEXO52v1_missing_organization_config_field if {
    output := tests[_] with input as {
        "transport_config": [
            {"SmtpClientAuthenticationDisabled": true}
        ]
        # organization_config field is missing
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    count(output.ActualValue) == 0
}

# Test 9: Organization config with OAuth2ClientProfileEnabled as string "true"
test_MSEXO52v1_oauth_as_string_true if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": "true",  # String instead of boolean
                "AuditDisabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    # This should be non-compliant as we expect boolean true, not string "true"
    output.RequirementMet == false
    count(output.ActualValue) > 0
}

# Test 10: Organization config with OAuth2ClientProfileEnabled as string "false"
test_MSEXO52v1_oauth_as_string_false if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": "false",  # String instead of boolean
                "AuditDisabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    count(output.ActualValue) > 0
}

# Test 11: Organization config with OAuth2ClientProfileEnabled as null
test_MSEXO52v1_oauth_as_null if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": null,
                "AuditDisabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    count(output.ActualValue) > 0
}

# Test 12: Organization config without Name field (edge case)
test_MSEXO52v1_organization_without_name if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "OAuth2ClientProfileEnabled": false,
                "AuditDisabled": false
                # Name field is missing
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    # Should still detect the non-compliance even without Name field
    count(output.ActualValue) > 0
}

# Test 13: Verify ReportDetails structure for compliant case
test_MSEXO52v1_report_details_compliant if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": true
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    output.ReportDetails.Status == true
}

# Test 14: Verify ReportDetails structure for non-compliant case
test_MSEXO52v1_report_details_non_compliant if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Test Organization",
                "OAuth2ClientProfileEnabled": false
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == false
    output.ReportDetails.Status == false
}

# Test 15: Complex organization config with additional fields
test_MSEXO52v1_complex_organization_config if {
    output := tests[_] with input as {
        "organization_config": [
            {
                "Name": "Complex Test Organization",
                "OAuth2ClientProfileEnabled": true,
                "AuditDisabled": false,
                "CustomerLockboxEnabled": true,
                "AutoForwardEnabled": false,
                "SMTPEnabled": false,
                "AdditionalCustomField": "CustomValue"
            }
        ]
    }
    output.PolicyId == "MS.EXO.5.2v1"
    output.RequirementMet == true
    # Should be compliant regardless of additional fields
    count(output.ActualValue) == 0
}