package aad
import rego.v1

#
# MS.AAD.10.1v1
#--
# Dynamic security groups SHALL be configured for guest user management.
#--

# Policy Check: Verify dynamic guest user groups exist and are properly configured
default MSAAD101v1 := false

MSAAD101v1 := true if {
    count(ProperlyConfiguredDynamicGuestGroups) > 0
}

# Returns all properly configured dynamic guest user groups
ProperlyConfiguredDynamicGuestGroups contains Group if {
    some Group in input.groups
    
    # Must be a security group
    Group.securityEnabled == true
    
    # Must have dynamic membership
    "DynamicMembership" in Group.groupTypes
    
    # Must have guest user membership rule
    IsValidGuestUserRule(Group.membershipRule)
    
    # Must have processing enabled
    Group.membershipRuleProcessingState == "On"
    
    # Should not be mail enabled (security groups typically aren't)
    Group.mailEnabled == false
}

# Checks if the membership rule correctly identifies guest users
IsValidGuestUserRule(rule) := true if {
    # Standard guest user rule variations
    rule == "(user.userType -eq \"Guest\")"
} else := true if {
    rule == "(user.userType -eq 'Guest')"
} else := true if {
    rule == "user.userType -eq \"Guest\""
} else := true if {
    rule == "user.userType -eq 'Guest'"
}

# Returns all dynamic groups that exist but are misconfigured
MisconfiguredDynamicGuestGroups contains Group if {
    some Group in input.groups
    
    # Has some dynamic guest characteristics but not all correct
    "DynamicMembership" in Group.groupTypes
    
    # Contains "guest" in name or description
    contains(lower(Group.displayName), "guest")
    
    # But is not in the properly configured set
    not Group in ProperlyConfiguredDynamicGuestGroups
}

MisconfiguredDynamicGuestGroups contains Group if {
    some Group in input.groups
    
    # Has some dynamic guest characteristics but not all correct
    "DynamicMembership" in Group.groupTypes
    
    # Contains "guest" in description
    contains(lower(Group.description), "guest")
    
    # But is not in the properly configured set
    not Group in ProperlyConfiguredDynamicGuestGroups
}

# Report details for compliance checking
MSAAD101v1_Conditional := MSAAD101v1

# Additional reporting for visibility
ValidDynamicGuestGroupsCount := count(ProperlyConfiguredDynamicGuestGroups)
MisconfiguredGroupsCount := count(MisconfiguredDynamicGuestGroups)

# Details for remediation
ProperlyConfiguredGroups := ProperlyConfiguredDynamicGuestGroups
MisconfiguredGroups := MisconfiguredDynamicGuestGroups

# Check if any groups exist at all
DynamicGroupsExist := count([group | 
    some group in input.groups
    "DynamicMembership" in group.groupTypes
]) > 0

# Policy test result following AADConfig.rego pattern
tests contains {
    "PolicyId": "MS.AAD.10.1v1",
    "Criticality": "Shall",
    "Commandlet": ["Get-MgGroup"],
    "ActualValue": {
        "properly_configured_groups": ProperlyConfiguredGroups,
        "misconfigured_groups": MisconfiguredGroups,
        "dynamic_groups_exist": DynamicGroupsExist
    },
    "ReportDetails": ReportDetails,
    "RequirementMet": MSAAD101v1
} if {
    ValidCount := ValidDynamicGuestGroupsCount
    MisconfiguredCount := MisconfiguredGroupsCount
    ReportDetails := sprintf("Found %d properly configured dynamic guest user group(s) and %d misconfigured group(s). Dynamic groups for guest user management are required for compliance.", [
        ValidCount,
        MisconfiguredCount
    ])
}