package aad
import rego.v1

#
# Policy MS.AAD.7.10v1
#
test_Restrict_Non_Admin_Access_Correct if {
    Output := tests with input as {
        "entraAdminCenterAccess": {
            "restrictNonAdminAccess": true
        }
    }

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == true
}

test_Restrict_Non_Admin_Access_Incorrect if {
    Output := tests with input as {
        "entraAdminCenterAccess": {
            "restrictNonAdminAccess": false
        }
    }

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == false
}

test_Restrict_Non_Admin_Access_Missing_Field if {
    Output := tests with input as {
        "entraAdminCenterAccess": {}
    }

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == false
}

test_Restrict_Non_Admin_Access_Missing_Object if {
    Output := tests with input as {}

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == false
}

test_Restrict_Non_Admin_Access_Null_Value if {
    Output := tests with input as {
        "entraAdminCenterAccess": {
            "restrictNonAdminAccess": null
        }
    }

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == false
}

test_Restrict_Non_Admin_Access_Wrong_Type if {
    Output := tests with input as {
        "entraAdminCenterAccess": {
            "restrictNonAdminAccess": "true"
        }
    }

    TestResult := Output[_]
    TestResult.PolicyId == "MS.AAD.7.10v1"
    TestResult.RequirementMet == false
}