package policies.microsoft.entra

import data.utils
import data.common.graph_helpers as gh

# Policy Information
policy_id := "MS.AAD.8.5v1"
policy_title := "Guest User Access Reviews"
policy_description := "Ensure access reviews for guest users are configured and conducted monthly"
policy_level := "Level 1"
policy_category := "Identity and Access Management"
policy_remediation_available := true

# Default values for expected configuration
default_review_name := "Monthly Guest User Access Review"
default_frequency := "Monthly"
default_duration := "P3D"
default_reviewer_type := "Member"
default_approval_justification := true
default_auto_apply := true
default_removal_on_no_review := true
default_guest_user_scope := true

# Default expected input structure for policy evaluation
default expected_input = {
    "accessReviews": [],
    "environment": {"isMicrosoftEntraP2Licensed": false}
}

# Check if tenant has required license
has_required_license(environment) if {
    environment.isMicrosoftEntraP2Licensed == true
}

# Main policy evaluation
guest_access_review_compliant[policy_result] if {
    # Check for required license first
    not has_required_license(input.environment)
    policy_result := {
        "policy_id": policy_id,
        "compliant": false,
        "status": "Not applicable - Microsoft Entra ID P2 license required",
        "remediable": false,
        "evidence": {"missing_license": true}
    }
}

guest_access_review_compliant[policy_result] if {
    # If licensed, check for proper access review configuration
    has_required_license(input.environment)

    # Find monthly guest user access reviews
    reviews := [review |
        review := input.accessReviews[_]
        review.displayName == default_review_name
    ]

    # No reviews found
    count(reviews) == 0

    policy_result := {
        "policy_id": policy_id,
        "compliant": false,
        "status": "Guest user access review not configured",
        "remediable": true,
        "evidence": {"missing_review": true}
    }
}

guest_access_review_compliant[policy_result] if {
    # If licensed, check for proper access review configuration
    has_required_license(input.environment)

    # Find monthly guest user access reviews
    reviews := [review |
        review := input.accessReviews[_]
        review.displayName == default_review_name
    ]

    # Review exists but has configuration issues
    count(reviews) > 0
    review := reviews[0]
    issues := get_configuration_issues(review)
    count(issues) > 0

    policy_result := {
        "policy_id": policy_id,
        "compliant": false,
        "status": sprintf("Guest user access review misconfigured: %s", [concat(", ", issues)]),
        "remediable": true,
        "evidence": {"misconfigured_review": true, "issues": issues}
    }
}

guest_access_review_compliant[policy_result] if {
    # If licensed, check for proper access review configuration
    has_required_license(input.environment)

    # Find monthly guest user access reviews
    reviews := [review |
        review := input.accessReviews[_]
        review.displayName == default_review_name
    ]

    # Review exists and is properly configured
    count(reviews) > 0
    review := reviews[0]
    issues := get_configuration_issues(review)
    count(issues) == 0

    policy_result := {
        "policy_id": policy_id,
        "compliant": true,
        "status": "Guest user access review properly configured",
        "remediable": false,
        "evidence": {"review_id": review.id}
    }
}

# Helper function to identify configuration issues
get_configuration_issues(review) = issues if {
    all_issues := []

    # Check recurrence pattern - should be monthly
    is_monthly := is_monthly_review(review)
    not is_monthly
    all_issues = array.concat(all_issues, ["not configured for monthly review"])

    # Check duration - should be 3 days or less
    has_proper_duration := review.settings.activityDuration == default_duration
    not has_proper_duration
    all_issues = array.concat(all_issues, ["review duration exceeds recommendation"])

    # Check auto-apply setting
    auto_apply := review.settings.autoApplyDecisionsEnabled == true
    not auto_apply
    all_issues = array.concat(all_issues, ["auto-apply not enabled"])

    # Check justification required
    justification_required := review.settings.justificationRequiredOnApproval == true
    not justification_required
    all_issues = array.concat(all_issues, ["justification not required for approval"])

    # Check reviewer type - should not be guests
    proper_reviewers := has_proper_reviewers(review)
    not proper_reviewers
    all_issues = array.concat(all_issues, ["improper reviewer configuration"])

    # Check default decision for unreviewed items
    removes_unreviewed := has_deny_for_unreviewed(review)
    not removes_unreviewed
    all_issues = array.concat(all_issues, ["access not removed for unreviewed guests"])

    # Check scope - should be guest users only
    guest_scope := targets_guest_users(review)
    not guest_scope
    all_issues = array.concat(all_issues, ["not scoped to guest users"])

    issues := all_issues
}

# Check if review is configured for monthly frequency
is_monthly_review(review) if {
    review.settings.recurrence.pattern.type == "absoluteMonthly"
    review.settings.recurrence.pattern.interval == 1
}

# Check if review targets guest users
targets_guest_users(review) if {
    review.reviewedEntity.id == "externalUsers"
}

# Check if review has proper reviewers (not guests themselves)
has_proper_reviewers(review) if {
    some i
    reviewer := review.reviewers[i]
    reviewer.queryType == "MicrosoftGraph"
    contains(reviewer.query, "userType eq 'Member'")
}

# Check if unreviewed items are set to be denied/removed
has_deny_for_unreviewed(review) if {
    review.settings.autoReview.notReviewedResult == "Deny"
}

# Logic for remediation
remediate[remediation_action] if {
    reviews := [review |
        review := input.accessReviews[_]
        review.displayName == default_review_name
    ]

    count(reviews) == 0

    remediation_action := {
        "policy_id": policy_id,
        "action": "create_access_review",
        "params": {
            "displayName": default_review_name,
            "scope": "guest_users_in_m365_groups",
            "frequency": "monthly",
            "autoApply": true,
            "justificationRequired": true,
            "emailNotifications": true,
            "reminders": true,
            "duration": 3,
            "notReviewedResult": "Deny"
        }
    }
}

remediate[remediation_action] if {
    reviews := [review |
        review := input.accessReviews[_]
        review.displayName == default_review_name
    ]

    count(reviews) > 0
    review := reviews[0]
    issues := get_configuration_issues(review)
    count(issues) > 0

    remediation_action := {
        "policy_id": policy_id,
        "action": "update_access_review",
        "params": {
            "reviewId": review.id,
            "displayName": default_review_name,
            "scope": "guest_users_in_m365_groups",
            "frequency": "monthly",
            "autoApply": true,
            "justificationRequired": true,
            "emailNotifications": true,
            "reminders": true,
            "duration": 3,
            "notReviewedResult": "Deny"
        }
    }
}