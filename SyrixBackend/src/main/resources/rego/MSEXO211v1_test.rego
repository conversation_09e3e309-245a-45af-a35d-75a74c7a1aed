package exo
import rego.v1

# Test cases for MS.EXO.21.1v1 - Outlook Add-In Prevention Policy

# Test 1: Compliant configuration - no restricted roles in any policy
test_MSEXO211v1_compliant_configuration if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Default Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "MyContactInformation", 
                        "MyDistributionGroups",
                        "MyVoiceMail"
                    ]
                },
                {
                    "identity": "Sales Role Assignment Policy", 
                    "assignedRoles": [
                        "MyBaseOptions",
                        "MyTextMessaging"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == true
}

# Test 2: Non-compliant configuration - policies contain restricted roles
test_MSEXO211v1_non_compliant_with_custom_apps if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Default Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "MyContactInformation",
                        "My Custom Apps",
                        "MyDistributionGroups"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    contains(output.ReportDetails.Details, "My Custom Apps")
}

# Test 3: Non-compliant with Marketplace Apps
test_MSEXO211v1_non_compliant_with_marketplace_apps if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Executive Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "My Marketplace Apps",
                        "MyContactInformation"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    contains(output.ReportDetails.Details, "My Marketplace Apps")
}

# Test 4: Non-compliant with ReadWriteMailbox Apps
test_MSEXO211v1_non_compliant_with_readwrite_apps if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Power User Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "MyContactInformation",
                        "My ReadWriteMailbox Apps"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    contains(output.ReportDetails.Details, "My ReadWriteMailbox Apps")
}

# Test 5: Multiple policies with multiple restricted roles
test_MSEXO211v1_multiple_policies_multiple_violations if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Default Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "My Custom Apps",
                        "My Marketplace Apps"
                    ]
                },
                {
                    "identity": "Executive Role Assignment Policy",
                    "assignedRoles": [
                        "MyContactInformation",
                        "My ReadWriteMailbox Apps"
                    ]
                },
                {
                    "identity": "Restricted Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "MyDistributionGroups"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    
    # Should detect both non-compliant policies
    violating_policies := {policy | policy := output.ActualValue[_]}
    "Default Role Assignment Policy" in violating_policies
    "Executive Role Assignment Policy" in violating_policies
    # Should NOT include the compliant policy
    not "Restricted Role Assignment Policy" in violating_policies
}

# Test 6: Mixed case role names (should still be detected)
test_MSEXO211v1_case_insensitive_role_detection if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Test Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "my custom apps",  # Different case - should still be detected if policy is case-insensitive
                        "MY MARKETPLACE APPS"  # Different case
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    # Note: This test depends on whether the Rego policy is case-sensitive
    # The current implementation uses exact string matching, so this would be compliant
    # If case-insensitive matching is required, the Rego policy would need updating
}

# Test 7: Empty role assignment policies
test_MSEXO211v1_empty_policies if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": []
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == true
    count(output.ActualValue) == 0
}

# Test 8: Policy with empty assigned roles
test_MSEXO211v1_policy_with_empty_roles if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Empty Role Assignment Policy",
                    "assignedRoles": []
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == true
}

# Test 9: Policy with null assigned roles
test_MSEXO211v1_policy_with_null_roles if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Null Roles Policy"
                    # assignedRoles is missing/null
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == true
}

# Test 10: All three restricted roles in single policy
test_MSEXO211v1_all_restricted_roles_single_policy if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Dangerous Role Assignment Policy",
                    "assignedRoles": [
                        "MyBaseOptions",
                        "My Custom Apps",
                        "My Marketplace Apps", 
                        "My ReadWriteMailbox Apps",
                        "MyContactInformation"
                    ]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    
    # Should detect the policy with all restricted roles
    violating_policies := {policy | policy := output.ActualValue[_]}
    "Dangerous Role Assignment Policy" in violating_policies
    count(violating_policies) == 1
}

# Test 11: Verify error message contains expected information
test_MSEXO211v1_error_message_content if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Test Policy",
                    "assignedRoles": ["My Custom Apps"]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.RequirementMet == false
    
    # Verify the error message contains expected elements
    contains(output.ReportDetails.Details, "role assignment policy")
    contains(output.ReportDetails.Details, "Outlook add-ins")
    contains(output.ReportDetails.Details, "Test Policy")
}

# Test 12: Verify commandlet specification
test_MSEXO211v1_commandlet_specification if {
    output := tests[_] with input as {
        "role_assignment_addin_audit": {
            "discovered_roles": {
                "MyCustomApps": "My Custom Apps",
                "MyMarketplaceApps": "My Marketplace Apps",
                "MyReadWriteMailboxApps": "My ReadWriteMailbox Apps"
            },
            "policies": [
                {
                    "identity": "Test Policy",
                    "assignedRoles": ["MyBaseOptions"]
                }
            ]
        }
    }
    output.PolicyId == "MS.EXO.21.1v1"
    output.Commandlet == ["Get-ManagementRole", "Get-RoleAssignmentPolicy"]
    output.Criticality == "Shall"
}