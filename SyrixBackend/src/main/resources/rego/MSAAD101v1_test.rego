package aad
import rego.v1

# Test cases for MS.AAD.10.1v1 - Dynamic guest user group policy

# Test 1: Properly configured dynamic guest user group
test_MSAAD101v1_correct_configuration if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "All Guest Users (Dynamic)",
                "description": "Dynamic group for all guest users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    output == true
}

# Test 2: Multiple correctly configured groups
test_MSAAD101v1_multiple_correct_groups if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "11111111-1111-1111-1111-111111111111",
                "displayName": "Guest Users - Sales",
                "description": "Dynamic group for guest users in sales",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "On"
            },
            {
                "id": "22222222-2222-2222-2222-222222222222",
                "displayName": "All External Users",
                "description": "All guest users in organization",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "user.userType -eq 'Guest'",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    output == true
}

# Test 3: No dynamic guest groups exist - should fail
test_MSAAD101v1_no_dynamic_guest_groups if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Regular Security Group",
                "description": "Standard security group",
                "groupTypes": ["Unified"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": null,
                "membershipRuleProcessingState": null
            }
        ]
    }
    output == false
}

# Test 4: Dynamic group exists but wrong membership rule
test_MSAAD101v1_wrong_membership_rule if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Guest Users Group",
                "description": "Group for guest users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.department -eq \"Marketing\")",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    output == false
}

# Test 5: Dynamic group with correct rule but processing disabled
test_MSAAD101v1_processing_disabled if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Guest Users Group",
                "description": "Group for guest users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "Paused"
            }
        ]
    }
    output == false
}

# Test 6: Dynamic group with correct rule but not security enabled
test_MSAAD101v1_not_security_enabled if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Guest Users Group",
                "description": "Group for guest users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": false,
                "mailEnabled": true,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    output == false
}

# Test 7: Mixed configuration - one correct, one incorrect
test_MSAAD101v1_mixed_configuration if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "11111111-1111-1111-1111-111111111111",
                "displayName": "Correct Guest Group",
                "description": "Properly configured guest group",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "On"
            },
            {
                "id": "22222222-2222-2222-2222-222222222222",
                "displayName": "Incorrect Guest Group",
                "description": "Misconfigured guest group",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.department -eq \"HR\")",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    output == true  # Should pass because at least one is correctly configured
}

# Test 8: Alternative membership rule formats
test_MSAAD101v1_alternative_rule_formats if {
    # Test without parentheses
    output1 := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Guest Users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "user.userType -eq \"Guest\"",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    
    # Test with single quotes
    output2 := data.aad.MSAAD101v1 with input as {
        "groups": [
            {
                "id": "12345678-1234-5678-9012-123456789012",
                "displayName": "Guest Users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq 'Guest')",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    
    output1 == true
    output2 == true
}

# Test 9: Empty groups array
test_MSAAD101v1_no_groups if {
    output := data.aad.MSAAD101v1 with input as {
        "groups": []
    }
    output == false
}

# Test 10: Verify ProperlyConfiguredDynamicGuestGroups count
test_properly_configured_groups_count if {
    groups_set := data.aad.ProperlyConfiguredDynamicGuestGroups with input as {
        "groups": [
            {
                "id": "11111111-1111-1111-1111-111111111111",
                "displayName": "Guest Group 1",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.userType -eq \"Guest\")",
                "membershipRuleProcessingState": "On"
            },
            {
                "id": "22222222-2222-2222-2222-222222222222",
                "displayName": "Guest Group 2",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "user.userType -eq 'Guest'",
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    count(groups_set) == 2
}

# Test 11: Verify MisconfiguredDynamicGuestGroups detection
test_misconfigured_groups_detection if {
    misconfigured := data.aad.MisconfiguredDynamicGuestGroups with input as {
        "groups": [
            {
                "id": "11111111-1111-1111-1111-111111111111",
                "displayName": "Guest Users Group",
                "description": "For guest users",
                "groupTypes": ["DynamicMembership"],
                "securityEnabled": true,
                "mailEnabled": false,
                "membershipRule": "(user.department -eq \"Sales\")",  # Wrong rule
                "membershipRuleProcessingState": "On"
            }
        ]
    }
    count(misconfigured) == 1
}