package forms

import rego.v1
import data.utils.report.ReportDetailsBoolean

##############
# MS.FORMS.1 #
##############

# MS.FORMS.1.1v1: Ensure internal phishing protection for Forms is enabled
# CIS Microsoft 365 Foundations Benchmark v5.0.0 - Control 1.3.5

tests contains {
    "PolicyId": "MS.FORMS.1.1v1",
    "Criticality": "Shall",
    "Commandlet": ["Get-FormsSettings"],
    "ActualValue": input.forms_phishing_protection.isInOrgFormsPhishingScanEnabled,
    "ReportDetails": ReportDetailsBoolean(Status),
    "RequirementMet": Status
} if {
    # Check if Forms phishing protection is enabled
    Status := input.forms_phishing_protection.isInOrgFormsPhishingScanEnabled == true
}