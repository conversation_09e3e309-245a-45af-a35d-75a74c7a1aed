# Quarkus Configuration
quarkus:
  application:
    name: syrix-backend
    version: 1.0.0-SNAPSHOT
  
  
  # HTTP Configuration
  http:
    port: 8989
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      headers: "*"
      methods: "*"
  
  # Logging Configuration
  log:
    level: INFO
    category:
      io.syrix: DEBUG
      io.syrix.protocols.client: INFO
    console:
      enable: true
      format: "%d{HH:mm:ss,SSS} %-5p [%c{36}] (%t) %s%e%n"
    file:
      enable: true
      path: outputs/logs/syrix-backend.log
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{36}] (%t) %s%e%n"
      rotation:
        max-file-size: 10M
        max-backup-index: 30
  
  # Native compilation settings
  native:
    builder-image: graalvm
  
  # Dev Services (disable for production)
  devservices:
    enabled: false

# Custom application properties
syrix:
  api:
    version: v1
    base-path: /api
  
  microsoft:
    graph:
      base-url: https://graph.microsoft.com/v1.0
  
  # MongoDB Configuration
  mongodb:
    host: "localhost"
    port: 27017
    database: "Syrix"
    username: "admin"
    password: "password123"
    authentication-database: "admin"
  
  storage:
    type: local # local or s3
    s3:
      endpoint: http://localhost:4566
      bucket-name: syrix-storage
      region: us-east-1
      access-key: test
      secret-key: test
      force-path-style: true
    
  security:
    jwt:
      enabled: false # Enable when implementing JWT security

# Additional logging configuration (if needed for specific frameworks)
# Note: Main logging is configured above under quarkus.log
