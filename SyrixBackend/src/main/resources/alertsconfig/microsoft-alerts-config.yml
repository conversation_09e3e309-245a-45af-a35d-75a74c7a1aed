# Microsoft Alerts Configuration
# This file defines alerts that will be created in Microsoft 365 Security & Compliance Center.
# The 'notifyUser' field is NOT specified here as it will be provided as a parameter to the method.

# ===== Available Keys for Alert Definitions =====
# Required fields:
# id: Unique identifier for the alert configuration
# name: Unique name for the alert policy
#
# Optional custom parameters:
# customParameters: Map of additional properties to pass directly to the PowerShell cmdlet
#
# Standard optional fields:
# description: Descriptive text for the alert policy
# category: AccessGovernance, ComplianceManager, DataGovernance, MailFlow, Others, PrivacyManagement, Supervision, ThreatManagement
# severity: Low, Medium, High
# operation: The activity to monitor (for Activity threat type)
# aggregationType: None, SimpleAggregation, AnomalousAggregation
# threatType: Activity, Malware
# threshold: Number of detections for SimpleAggregation
# timeWindow: Time interval in minutes for SimpleAggregation
# filter: OPATH filter syntax for conditions
# alertBy: Scope for aggregated alerts
# notificationEnabled: Whether to send notifications
# ===================================================

# List of all alert configurations
alerts:
  # Standard fully-defined alert with all properties
  - Id: "delayed-messages-alert"
    Name: "Messages have been delayed"
    Operation: "MessageDeferred"
    Filter: ""
    ThreatType: "Activity"
    #description: "Triggers when messages are delayed in transport"
    #category: "MailFlow"
    #severity: "Medium"
    #aggregationType: "None"
    #threatType: "Activity"

  - Id: "malicious-url-click"
    Name: "A potentially malicious URL click was detected"
    Description: "Triggers when a user clicks a URL identified as malicious by Defender for Office 365."
    Category: "ThreatManagement"
    Severity: "Medium"
    Operation: "ClickMaliciousUrl"
    AggregationType: "None"
    ThreatType: "Activity"
    # The NotifyUser field is not included here as it will be provided as a parameter to the method

  - Id: "suspicious-connector-activity"
    Name: "Suspicious Connector Activity"
    Description: "Triggers when a connector is created or modified (e.g., Power Automate, Power Apps)."
    Category: "DataGovernance"
    Severity: "High"
    # Operation is an array of values
    Operation:
      - "CreateConnector"
      - "UpdateConnector"
    AggregationType: "None"
    ThreatType: "Activity"
    # Additional parameter specific to this alert
    LogicalOperationName: "Or"

  # Alert with standard properties and some custom parameters
#  - id: "high-spam-alert"
#    name: "High Spam Activity Alert"
#    description: "Triggers when a high volume of spam is detected"
#    category: "ThreatManagement"
#    severity: "High"
#    operation: "SpamDetection"
#    aggregationType: "SimpleAggregation"
#    threatType: "Activity"
#    threshold: 100
#    timeWindow: 60
#    customParameters:
#      Comment: "Created via automated process"
#      UseCreatedDateTime: true

  # Minimal alert with only required fields and custom parameters
#  - id: "simple-alert"
#    name: "Simple Custom Alert"
#    customParameters:
#      Category: "Others"
#      Description: "Custom alert created with minimal configuration"
#      Severity: "Medium"
#      Operation: "SearchRemoved"
#      AggregationType: "None"
#      ThreatType: "Activity"
#      Comment: "Created via the customParameters approach"

  # Advanced alert using mostly custom parameters for flexibility
#  - id: "advanced-alert"
#    name: "Advanced Custom Alert"
#    category: "DataGovernance"
#    customParameters:
#      Description: "Detects unusual access to classified documents"
#      Severity: "High"
#      Operation: "FileAccessed"
#      AggregationType: "AnomalousAggregation"
#      ThreatType: "Activity"
#      Filter: "Activity.ItemType -eq 'File' -and Activity.SourceFileExtension -in ('docx','xlsx','pptx')"
#      CorrelationPolicyId: "3a6d8f9c-5b2e-4c7a-9d1f-bf7a8e2a0b5d"
#      NotifyUserOnFilterMatch: true
#      CustomProperties:
#        Classification: "Confidential"
#        Department: "Legal"
#        ReviewRequired: true