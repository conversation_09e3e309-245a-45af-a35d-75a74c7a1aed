package io.syrix.agent;

import java.lang.instrument.Instrumentation;

/**
 * Java agent to set system properties before any class loading occurs.
 * This ensures the LogManager property is set at the absolute earliest point.
 */
public class LoggingAgent {
    
    /**
     * Premain method called by JVM when agent is loaded.
     * This runs BEFORE the main method and any class loading.
     */
    public static void premain(String agentArgs, Instrumentation inst) {
        initializeLoggingProperties();
    }
    
    /**
     * Agent main method for dynamic attachment.
     */
    public static void agentmain(String agentArgs, Instrumentation inst) {
        initializeLoggingProperties();
    }
    
    private static void initializeLoggingProperties() {
        // Set these properties at the absolute earliest point
        System.setProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager");
        System.setProperty("slf4j.provider", "org.slf4j.impl.JBossSlf4jServiceProvider");
        System.setProperty("logback.configurationFile", "");
        System.setProperty("org.jboss.logging.provider", "jboss");
        System.setProperty("java.util.logging.manager.class", "org.jboss.logmanager.LogManager");
        
        System.out.println("LoggingAgent: System properties initialized for JBoss LogManager");
    }
}