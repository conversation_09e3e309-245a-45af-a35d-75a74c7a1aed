package io.syrix.classifiers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.AI.llm.LLMService;
import io.syrix.common.AI.llm.OpenAIService;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.utils.StringUtil;
import io.syrix.products.microsoft.entra.model.RiskAssessment;
import io.syrix.products.microsoft.entra.model.RiskLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ApplicationSecurityRiskClassifier {
	private static final Logger logger = LoggerFactory.getLogger(ApplicationSecurityRiskClassifier.class);
	private final ObjectMapper objectMapper;
	private final String apiKey;
	private static final String ADDR = "localhost:1234";
	private LLMService llm;

	private static final String SYSTEM_PROMPT = """
			As a security analyst, evaluate the given Azure Enterprise application according to this risk matrix:
			
			LOW:
			    - Microsoft-published applications with any permission scope
			    - Read-only permissions to non-sensitive resources
			    - Limited user access
			
			MEDIUM:
			    - Non-Microsoft verified publishers with read-only permissions
			    - Write access to non-sensitive resources
			
			HIGH:
			    - Non-Microsoft publisher with write permissions to sensitive resources
			    - Broad user access scope
			    - Unverified publishers
			
			CRITICAL:
			    - Non-Microsoft unverified publishers with highest privilege access
			    - Write access to critical systems/data
			    - Organization-wide scope
			
			Special conditions:
			    - Microsoft-published applications cannot exceed LOW risk
			    - Microsoft 365 core services (Exchange, SharePoint, Teams) cannot exceed LOW risk
			    - Read-only permissions cannot exceed MEDIUM risk
			
			Return only valid JSON. Do not include additional commentary.\s
			Use the following JSON structure:
			{
			   "riskLevel": "[LOW|MEDIUM|HIGH|CRITICAL]",
			   "justification": "[Brief explanation including publisher]"
			}
			""";

	public ApplicationSecurityRiskClassifier(String apiKey) {
		this.apiKey = apiKey;
		this.objectMapper = new ObjectMapper();
		this.llm = new OpenAIService("kuku", "http://"+ADDR+"/v1");
	}

	public RiskAssessment classifyRisk(JsonNode jsonInput) {
		try {
			String prompt = buildAnalysisPrompt(jsonInput);
			JsonNode response = classify(prompt);
			return parseResponse(response);
		} catch (Exception e) {
			logger.error("Error classifying security risk", e);
			throw new SecurityRiskClassificationException("Failed to classify risk", e);
		}
	}

	private JsonNode classify(String prompt)  {
		try {
			String response = this.llm.generate(SYSTEM_PROMPT, prompt);
			response =  StringUtil.extractJson(response);
			return objectMapper.readTree(response);
		} catch (JsonProcessingException e) {
			throw new SecurityRiskClassificationException("Failed to parse JSON", e);
		}
	}

	private String buildAnalysisPrompt(JsonNode json) {
		StringBuilder prompt = new StringBuilder("Analyze this Azure Enterprise application:\n");
		JsonNode basic = json.get("basic");
		prompt.append("App: ").append(basic.get("displayName").asText())
				.append(", Publisher: ").append(basic.get("publisherName").asText()).append("\n");

		JsonNode permissions = json.get("permissions");
		permissions.forEach(perm ->
			prompt.append("Permission: ").append(perm.get("appRoleValue").asText())
					.append(" (").append(perm.get("permissionType").asText()).append(")\n")
					.append("Resource: ").append(perm.get("resourceDisplayName").asText()).append("\n")
					.append("Role Name: ").append(perm.get("appRoleDisplayName").asText()).append("\n")
					.append("Description: ").append(perm.get("appRoleDescription").asText()).append("\n\n")
		);
		return prompt.toString();
	}

	private RiskAssessment parseResponse(JsonNode response) {
		String riskLevel = extractRiskLevel(response);
		String justification = response.get("justification").asText();
		return new RiskAssessment(RiskLevel.valueOf(riskLevel), justification);
	}

	private String extractRiskLevel(JsonNode response) {
		return response.get("riskLevel").asText();
	}
}

class SecurityRiskClassificationException extends SyrixRuntimeException {
	SecurityRiskClassificationException(String message, Throwable cause) {
		super(message, cause);
	}
}