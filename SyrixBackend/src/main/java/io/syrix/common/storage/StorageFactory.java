package io.syrix.common.storage;

import io.syrix.main.Context;

/**
 * Factory for creating Storage implementations.
 * Supports both local file storage and S3 storage.
 */
public class StorageFactory {
	
	/**
	 * Storage type enumeration.
	 */
	public enum StorageType {
		LOCAL,
		S3
	}
	
	/**
	 * Creates Storage instance based on context configuration.
	 * Defaults to LOCAL storage.
	 */
	public static Storage getStorage(Context context) {
		return getStorage(context, StorageType.LOCAL);
	}
	
	/**
	 * Creates Storage instance based on specified storage type.
	 * 
	 * @param context Application context
	 * @param storageType Type of storage to create
	 * @return Storage implementation
	 */
	public static Storage getStorage(Context context, StorageType storageType) {
		return switch (storageType) {
			case S3 -> new StorageS3();
			case LOCAL -> new StorageFileLocal(context);
		};
	}
	
	/**
	 * Creates S3 Storage instance with custom configuration.
	 * 
	 * @param config S3 storage configuration
	 * @return S3 Storage implementation
	 */
	public static Storage getS3Storage(S3StorageConfig config) {
		return new StorageS3(config);
	}
	
	/**
	 * Creates S3 Storage instance.
	 * Convenience method for direct S3 storage creation.
	 */
	public static Storage getS3Storage() {
		return new StorageS3();
	}
}
