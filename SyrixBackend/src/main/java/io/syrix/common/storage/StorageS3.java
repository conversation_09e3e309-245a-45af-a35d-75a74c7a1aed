package io.syrix.common.storage;

import io.syrix.common.tmp.TempFolder;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.domain.RollbackTask;
import jakarta.enterprise.context.ApplicationScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;


import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;


/**
 * S3-based implementation of Storage interface using LocalStack for development.
 * Stores task-related files (configurations, metrics, reports, remediation results)
 * in S3-compatible storage.
 */
@ApplicationScoped
public class StorageS3 implements Storage {
	private static final Logger logger = LoggerFactory.getLogger(StorageS3.class);
	private static final String dateDelimiter = "-@-";

	private final S3Client s3Client;
	private final S3StorageConfig config;
	private final TempFolder tempFolder;

	/**
	 * Constructor with default LocalStack configuration.
	 */
	public StorageS3() {
		this(createDefaultConfig());
	}

	/**
	 * Constructor with custom S3 configuration.
	 *
	 * @param config S3 storage configuration
	 */
	public StorageS3(S3StorageConfig config) {
		this.config = config;
		this.s3Client = createS3Client();
		initializeBucket();
		this.tempFolder = TempFolder.newInstance();
	}

	/**
	 * Creates default configuration for LocalStack.
	 */
	private static S3StorageConfig createDefaultConfig() {
		S3StorageConfig defaultConfig = new S3StorageConfig();
		defaultConfig.setEndpoint("http://localhost:4566");
		defaultConfig.setBucketName("syrix-storage");
		defaultConfig.setRegion("us-east-1");
		defaultConfig.setAccessKey("test");
		defaultConfig.setSecretKey("test");
		defaultConfig.setForcePathStyle(true);
		return defaultConfig;
	}

	private static String getPath(String customerId, String externalTenantId) {
		return customerId + "/" + externalTenantId;
	}

	/**
	 * Gets the retrieve task ID from task (for RemediationTask and RollbackTask)
	 * or returns task ID for regular tasks.
	 * <p>
	 * customerId/tenantId
	 */
	private String getPath(Task task) {
		return switch (task) {
			case RetrieveTask rTask -> getPath(rTask.getCustomerId().toString(), rTask.getExternalTenantId());
			case RemediationTask rTask -> rTask.getRetrieveTaskId();
			case RollbackTask rTask -> rTask.getRetrieveTaskId();
			default -> task.getId().toString();
		};
	}

	/**
	 * Creates S3 client configured from config.
	 */
	private S3Client createS3Client() {
		AwsBasicCredentials credentials = AwsBasicCredentials.create(
				config.getAccessKey(),
				config.getSecretKey()
		);

		return S3Client.builder()
				.endpointOverride(URI.create(config.getEndpoint()))
				.credentialsProvider(StaticCredentialsProvider.create(credentials))
				.region(Region.of(config.getRegion()))
				.forcePathStyle(config.isForcePathStyle())
				.build();
	}

	/**
	 * Initializes S3 bucket if it doesn't exist.
	 */
	private void initializeBucket() {
		try {
			if (!bucketExists()) {
				createBucket();
				logger.info("S3 bucket '{}' created successfully", config.getBucketName());
			}
		} catch (Exception e) {
			logger.error("Failed to initialize S3 bucket: {}", e.getMessage(), e);
			throw new RuntimeException("S3 storage initialization failed", e);
		}
	}

	private boolean bucketExists() {
		try {
			s3Client.headBucket(HeadBucketRequest.builder().bucket(config.getBucketName()).build());
			return true;
		} catch (NoSuchBucketException e) {
			return false;
		}
	}

	private void createBucket() {
		s3Client.createBucket(CreateBucketRequest.builder().bucket(config.getBucketName()).build());
	}

	@Override
	public void saveConfig(Task task, Path filePath) {
		String s3Key = generateConfigKey(task);
		uploadFile(s3Key, filePath);
		logger.debug("Configuration saved for task {} to S3 key: {}", task.getId(), s3Key);
	}

	@Override
	public void saveMetrics(Task task, Path filePath) {
		String s3Key = generateMetricsKey(task);
		uploadFile(s3Key, filePath);
		logger.debug("Metrics saved for task {} to S3 key: {}", task.getId(), s3Key);
	}

	@Override
	public void saveReport(Task task, Path reportFolder) {
		try {
			String baseKey = generateReportKey(task);

			// Upload all files from report folder
			try (var files = Files.walk(reportFolder)) {
				files.filter(Files::isRegularFile)
						.forEach(file -> {
							String relativePath = reportFolder.relativize(file).toString().replace("\\", "/");
							String s3Key = baseKey + "/" + relativePath;
							uploadFile(s3Key, file);
						});
			}
			logger.debug("Report folder uploaded for task {} to S3 base key: {}", task.getId(), baseKey);
		} catch (IOException e) {
			logger.error("Failed to save report folder for task {}: {}", task.getId(), e.getMessage(), e);
			throw new RuntimeException("Failed to save report to S3", e);
		}
	}

	@Override
	public void saveRemediationResult(Task task, Path filePath) {
		String s3Key = generateRemediationResultKey(task);
		uploadFile(s3Key, filePath);
		logger.debug("Remediation result saved for task {} to S3 key: {}", task.getId(), s3Key);
	}

	@Override
	public void saveRollbackResult(Task task, Path filePath) {
		String s3Key = generateRollbackResultKey(task);
		uploadFile(s3Key, filePath);
		logger.debug("Rollback result saved for task {} to S3 key: {}", task.getId(), s3Key);
	}

	@Override
	public Path loadConfigPath(Task task) {
		String s3Key = generateConfigKey(task);
		return downloadToTempFile(s3Key, "config-" + task.getId());
	}

	@Override
	public Path loadRemediationResult(Task task) {
		String s3Key = generateRemediationResultKey(task);
		return downloadToTempFile(s3Key, "remediation-" + task.getId());
	}

	/**
	 * Uploads file to S3.
	 */
	private void uploadFile(String s3Key, Path filePath) {
		try {
			PutObjectRequest request = PutObjectRequest.builder()
					.bucket(config.getBucketName())
					.key(s3Key)
					.build();

			s3Client.putObject(request, RequestBody.fromFile(filePath));
			logger.trace("File uploaded successfully: {} -> s3://{}/{}", filePath, config.getBucketName(), s3Key);

		} catch (Exception e) {
			logger.error("Failed to upload file {} to S3 key {}: {}", filePath, s3Key, e.getMessage(), e);
			throw new RuntimeException("S3 upload failed", e);
		}
	}

	/**
	 * Downloads file from S3 to temporary file.
	 */
	private Path downloadToTempFile(String s3Key, String tempFilePrefix) {
		try {
			GetObjectRequest request = GetObjectRequest.builder()
					.bucket(config.getBucketName())
					.key(s3Key)
					.build();

			Path tempFile = tempFolder.generateUniqueFilePath(tempFilePrefix, ".tmp");
			GetObjectResponse response = s3Client.getObject(request, tempFile);
			logger.trace("File downloaded successfully: s3://{}/{} -> {}", config.getBucketName(), s3Key, tempFile);
			return tempFile;
		} catch (NoSuchKeyException e) {
			logger.warn("File not found in S3: s3://{}/{}", config.getBucketName(), s3Key);
			throw new RuntimeException("File not found in S3: " + s3Key, e);
		} catch (Exception e) {
			logger.error("Failed to download file from S3 key {}: {}", s3Key, e.getMessage(), e);
			throw new RuntimeException("S3 download failed", e);
		}
	}

	// Key generation methods for organized S3 storage structure
	private String generateConfigKey(Task task) {
		String retrieveTaskPath = getPath(task);
		return String.format("%s/config%s%s.json", retrieveTaskPath, dateDelimiter, DateUtils.instantToInt(task.getCreatedAt()));
	}

	private String generateMetricsKey(Task task) {
		String retrieveTaskPath = getPath(task);
		return String.format("%s/metrics%s%s.json", retrieveTaskPath, dateDelimiter, DateUtils.instantToInt(task.getCreatedAt()));
	}

	private String generateReportKey(Task task) {
		String retrieveTaskPath = getPath(task);
		return String.format("%s/report%s%s", retrieveTaskPath, dateDelimiter, DateUtils.instantToInt(task.getCreatedAt()));
	}

	private String generateRemediationResultKey(Task task) {
		String retrieveTaskPath = getPath(task);
		return String.format("%s/remediationResult%s%s.json", retrieveTaskPath, dateDelimiter, DateUtils.instantToInt(task.getCreatedAt()));
	}

	private String generateRollbackResultKey(Task task) {
		String retrieveTaskPath = getPath(task);
		return String.format("%s/rollbackResult%s%s.json", retrieveTaskPath, dateDelimiter, DateUtils.instantToInt(task.getCreatedAt()));
	}


}
