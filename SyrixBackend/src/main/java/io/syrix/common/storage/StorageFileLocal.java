package io.syrix.common.storage;

import io.syrix.common.utils.FileUtils;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.domain.RollbackTask;
import io.syrix.main.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static java.nio.file.StandardCopyOption.REPLACE_EXISTING;

public class StorageFileLocal implements Storage {
	private static final Logger logger = LoggerFactory.getLogger(StorageFileLocal.class);
	private static final String rootDirName = "SyrixStorage";
	private final Path localRootDir;

	public StorageFileLocal(Context context) {
		localRootDir = Path.of(context.getConfig().storagePath()).resolve(rootDirName);
		if (Files.notExists(localRootDir)) {
			try {
				Files.createDirectories(localRootDir);
				logger.info("Create storage directory {}", localRootDir.toAbsolutePath());
			} catch (IOException e) {
				logger.warn("Can not create storage directory {}", localRootDir.toAbsolutePath(), e);
				throw new RuntimeException(e);
			}
		}
	}

	private String getRetrieveTaskId(Task task) {
		return switch (task) {
			case RemediationTask rTask -> rTask.getRetrieveTaskId();
			case RollbackTask rTask -> rTask.getRetrieveTaskId();
			default -> task.getId().toString();
		};
	}

	@Override
	public void saveConfig(Task task, Path configPath) {
		try {
			Path taskDir = localRootDir.resolve(getRetrieveTaskId(task));
			if (!Files.exists(taskDir)) {
				Files.createDirectories(taskDir);
			}
			Path filePath = taskDir.resolve(task.getId() + "-config.json");
			Files.copy(configPath, filePath, REPLACE_EXISTING); //TODO remove, replace existing config
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}


	@Override
	public void saveMetrics(Task task, Path configPath) {
		try {
			String key = getRetrieveTaskId(task);
			Path taskDir = localRootDir.resolve(key);
			if (!Files.exists(taskDir)) {
				Files.createDirectories(taskDir);
			}
			Path filePath = taskDir.resolve(key + "-metrics.json");
			Files.copy(configPath, filePath, REPLACE_EXISTING); //TODO remove, replace existing config
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public void saveReport(Task task, Path reportFolder) {
		try {
			String key = getRetrieveTaskId(task);
			Path taskDir = localRootDir.resolve(key);
			if (!Files.exists(taskDir)) {
				Files.createDirectories(taskDir);
			}
			Path report = taskDir.resolve("report");
			if (!Files.exists(report)) {
				Files.createDirectories(report);
			}

			FileUtils.copyDirectory(reportFolder, report);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public void saveRemediationResult(Task task, Path filePath) {
		try {
			String key = getRetrieveTaskId(task);
			Path taskDir = localRootDir.resolve(key);
			if (!Files.exists(taskDir)) {
				Files.createDirectories(taskDir);
			}
			Path resFile = taskDir.resolve(key + "-remediationResult.json");
			Files.copy(filePath, resFile, REPLACE_EXISTING); //TODO remove, replace existing config
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public void saveRollbackResult(Task task, Path filePath) {
		try {
			String key = getRetrieveTaskId(task);
			Path taskDir = localRootDir.resolve(key);
			if (!Files.exists(taskDir)) {
				Files.createDirectories(taskDir);
			}
			Path resFile = taskDir.resolve(key + "-rollbackResult.json");
			Files.copy(filePath, resFile, REPLACE_EXISTING); //TODO remove, replace existing config
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public Path loadConfigPath(Task task) {
		String key = getRetrieveTaskId(task);
		Path taskDir = localRootDir.resolve(key);
		return taskDir.resolve(key + "-config.json");
	}

	@Override
	public Path loadRemediationResult(Task task) {
		String key = getRetrieveTaskId(task);
		Path taskDir = localRootDir.resolve(key);
		return taskDir.resolve(key + "-remediationResult.json");
	}

}
