package io.syrix.common.storage;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class DateUtils {

	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").withZone(ZoneOffset.UTC);

	/**
	 * Converts an Instant to a long representation in format: YYYYMMDDHHMMSSSS
	 * where SSS is milliseconds
	 * 
	 * @param date the Instant to convert
	 * @return long representation of the date
	 */
	public static long instantToInt(Instant date) {
		// Get nanoseconds and calculate milliseconds
		long nanos = date.getNano();
		long millis = nanos / 1_000_000; // milliseconds part
		
		// Format date parts
		String dateStr = formatter.format(date);
		
		// Combine: YYYYMMDDHHmmss + SSS
		String result = String.format("%s%03d", dateStr, millis);
		
		return Long.parseLong(result);
	}
}
