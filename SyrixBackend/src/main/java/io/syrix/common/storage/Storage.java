package io.syrix.common.storage;


import io.syrix.datamodel.task.Task;

import java.nio.file.Path;

public interface Storage {
	void saveConfig(Task task, Path filePath);
	void saveMetrics(Task task, Path filePath);
	void saveReport(Task task, Path reportFolder);
	void saveRemediationResult(Task task, Path filePath);
	void saveRollbackResult(Task task, Path filePath);

	Path loadConfigPath(Task task);
	Path loadRemediationResult(Task task);
}
