package io.syrix.common.constants;

import java.time.Duration;

public final class Constants {
	private Constants() {
		// Private constructor to prevent instantiation
	}
	// Common API paths and endpoints
	public static final String DOMAINS_ENDPOINT = "/domains";
	public static final String CONDITIONAL_ACCESS_ENDPOINT = "/identity/conditionalAccess/policies";
	public static final String SUBSCRIBED_SKUS_ENDPOINT = "/subscribedSkus";
	public static final String AUTHENTICATION_METHODS_ENDPOINT = "/policies/authenticationMethodsPolicy";
	public static final String DIRECTORY_SETTINGS_ENDPOINT = "/settings";
	public static final String DIRECTORY_ROLES_ENDPOINT = "/directoryRoles";
	public static final String GROUP_MEMBERS_ENDPOINT = "/members";
	public static final String GROUPS_ENDPOINT = "/groups";
	public static final String PIM_GROUP_ELIGIBILITY_ENDPOINT = "/privilegedAccess/aadGroups/eligibilitySchedules";

	// Response fields and property names
	public static final String DATA_FIELD = "data";
	public static final String METADATA_FIELD = "metadata";
	public static final String TIMESTAMP_FIELD = "timestamp";
	public static final String EXPORT_ID_FIELD = "export_id";
	public static final String STATUS_CODE_FIELD = "statusCode";
	public static final String BODY_FIELD = "body";

	// Common string literals
	public static final String VERSION = "version";
	public static final String TENANT_ID = "tenantId";
	public static final String CLIENT_ID = "clientId";
	public static final String USER_ID = "userId";
	public static final String GROUP_ID = "groupId";
	public static final String ROLE_ID = "roleId";
	public static final String ROLE_NAME = "roleName";

	// DNS and domain-related fields
	public static final String DOMAIN_FIELD = "domain";
	public static final String SELECTOR_FIELD = "selector";
	public static final String DKIM_RECORD_FIELD = "dkim_record";
	public static final String SELECTOR1 = "selector1";
	public static final String SELECTOR2 = "selector2";
	public static final String SPF_RECORD_FIELD = "spf_record";

	// Error response fields
	public static final String ERROR_FIELD = "error";
	public static final String MESSAGE_FIELD = "message";

	// Graph API Query Parameters
	public static final String VALUE_FIELD = "value";
	public static final String ID_FIELD = "id";
	public static final String DISPLAY_NAME_FIELD = "displayName";
	public static final String DESCRIPTION_FIELD = "description";

	//Scopes
	public static final String MS_GRAPH_SCOPE_URL = "https://graph.microsoft.com/.default";
	public static final String MS_MANAGMENT_SCOPE_URL = "https://management.azure.com/.default";
	public static final String MS_OUTLOOK_SCOPE_URL = "https://outlook.office365.com/.default";

	// PowerShell Commands
	public static final String GET_REMOTE_DOMAIN_CMD = "Get-RemoteDomain";
	public static final String GET_ACCEPTED_DOMAIN_CMD = "Get-AcceptedDomain";
	public static final String GET_TRANSPORT_CONFIG_CMD = "Get-TransportConfig";
	public static final String GET_SHARING_POLICY_CMD = "Get-SharingPolicy";
	public static final String GET_TRANSPORT_RULE_CMD = "Get-TransportRule";

	// Authentication-related fields
	public static final String ACCESS_TOKEN_FIELD = "access_token";
	public static final String REFRESH_TOKEN_FIELD = "refresh_token";
	public static final String EXPIRES_IN_FIELD = "expires_in";
	public static final String SCOPE_FIELD = "scope";
	public static final String GRANT_TYPE_FIELD = "grant_type";
	public static final String AUTHORIZATION_HEADER = "Authorization";
	public static final String BEARER_PREFIX = "Bearer ";

	// HTTP Request fields
	public static final String CONTENT_TYPE_HEADER = "Content-Type";
	public static final String ACCEPT_HEADER = "Accept";
	public static final String APPLICATION_JSON = "application/json";
	public static final String NEXT_LINK_FIELD = "@odata.nextLink";
	public static final String COUNT_FIELD = "@odata.count";
	public static final String ODATA_CONTEXT = "@odata.context";

	//connection timeout threshhold
	public static final Duration connectTimeout = Duration.ofSeconds(30);

	public static final String FALSE_VALUE = "$false";
	public static final String TRUE_VALUE = "$true";

	public static final String ENABLED_VALUE = "enabled";
	public static final String DISABLED_VALUE = "disabled";
	public static final String STATE = "state";

	public static final String RETRY_AFTER = "Retry-After";

	public static final String EMAIL = "Email";
	public static final String OPERATIONS = "operations";
	public static final String VALUE_TRUE = "true";
	public static final String IDENTITY_FIELD = "Identity";
	public static final String STATUS_FIELD = "status";
	public static final String POLICY_ID_FIELD = "policyId";
	public static final String DETAILS_FIELD = "details";
	public static final String SUCCESS_STATUS = "success";
	public static final String FAILURE_STATUS = "failure";
	public static final String NAME_FIELD = "Name";
	public static final String MEMBERS_VALUE = "Members";
	public static final String WARNING_STATUS = "warning";
	public static final String USER = "User";
	public static final String UNLIMITED_VALUE = "Unlimited";
	public static final String USER_PRINCIPAL_NAME = "userPrincipalName";
	public static final String IS_VERIFIED = "isVerified";
}