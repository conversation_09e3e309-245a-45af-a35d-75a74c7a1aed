package io.syrix.common.metrics;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Represents metrics for a specific API endpoint.
 * Tracks calls, success rates, and timing information.
 */
public class ApiMetrics {
	@JsonProperty("total_calls")
	private final AtomicInteger totalCalls;

	@JsonProperty("successful_calls")
	private final AtomicInteger successfulCalls;

	@JsonProperty("rate_limit_hits")
	private final AtomicInteger rateLimitHits;

	@JsonProperty("retry_attempts")
	private final AtomicInteger retryAttempts;

	@JsonProperty("total_duration")
	private final AtomicLong totalDuration;

	@JsonProperty("average_duration")
	private final AtomicLong averageDuration;

	public ApiMetrics() {
		this.totalCalls = new AtomicInteger(0);
		this.successfulCalls = new AtomicInteger(0);
		this.rateLimitHits = new AtomicInteger(0);
		this.retryAttempts = new AtomicInteger(0);
		this.totalDuration = new AtomicLong(0);
		this.averageDuration = new AtomicLong(0);
	}

	public int getTotalCalls() {
		return totalCalls.get();
	}

	public int getSuccessfulCalls() {
		return successfulCalls.get();
	}

	public int getRateLimitHits() {
		return rateLimitHits.get();
	}

	public int getRetryAttempts() {
		return retryAttempts.get();
	}

	public long getTotalDuration() {
		return totalDuration.get();
	}

	public long getAverageDuration() {
		return averageDuration.get();
	}

	public void recordCall(Duration duration, boolean success) {
		totalCalls.incrementAndGet();
		if (success) {
			successfulCalls.incrementAndGet();
		}
		updateTimingMetrics(duration);
	}

	public void recordRateLimit() {
		rateLimitHits.incrementAndGet();
	}

	public void recordRetry() {
		retryAttempts.incrementAndGet();
	}
	
	public void recordFailure() {
		totalCalls.incrementAndGet();
		// No increment to successfulCalls, which affects success rate
	}

	private void updateTimingMetrics(Duration duration) {
		long current = totalDuration.addAndGet(duration.toMillis());
		int total = totalCalls.get();
		if (total > 0) {
			averageDuration.set(current / total);
		}
	}

	// Keep existing methods
	@JsonProperty("success_rate")
	public double getSuccessRate() {
		int total = getTotalCalls();
		return total == 0 ? 0.0 : (double) getSuccessfulCalls() / total * 100.0;
	}
}