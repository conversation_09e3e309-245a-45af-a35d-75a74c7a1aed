package io.syrix.common.metrics;

import java.time.Duration;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Collects and manages metrics for Entra ID operations.
 * This class is thread-safe and designed for concurrent access.
 */
public class MetricsCollector {
	private static final Logger logger = LoggerFactory.getLogger(MetricsCollector.class);

	// Counters for different types of operations
	private final AtomicInteger totalExports;
	private final AtomicInteger successfulExports;
	private final AtomicInteger failedExports;

	// Timing metrics
	private final AtomicLong totalExportTime;
	private final AtomicLong averageExportTime;

	// API call metrics
	private final ConcurrentHashMap<String, ApiMetrics> apiMetrics;

	// Rate limiting metrics
	private final AtomicInteger rateLimitHits;
	private final AtomicInteger retryAttempts;

	public MetricsCollector() {
		this.totalExports = new AtomicInteger(0);
		this.successfulExports = new AtomicInteger(0);
		this.failedExports = new AtomicInteger(0);
		this.totalExportTime = new AtomicLong(0);
		this.averageExportTime = new AtomicLong(0);
		this.apiMetrics = new ConcurrentHashMap<>();
		this.rateLimitHits = new AtomicInteger(0);
		this.retryAttempts = new AtomicInteger(0);
	}

	/**
	 * Records the start of a configuration export operation.
	 * Updates the total exports counter.
	 */
	public void recordExportStart() {
		totalExports.incrementAndGet();
		logger.debug("Started new export operation. Total exports: {}", totalExports.get());
	}

	/**
	 * Records a successful export operation with its duration.
	 * Updates success counters and timing metrics.
	 */
	public void recordExportSuccess(Duration duration) {
		successfulExports.incrementAndGet();
		updateTimingMetrics(duration);

		logger.info("Export completed successfully in {} ms. Success rate: {}%",
				duration.toMillis(),
				calculateSuccessRate());
	}

	/**
	 * Records a failed export operation.
	 * Updates failure counters and logs detailed information.
	 */
	public void recordExportFailure() {
		failedExports.incrementAndGet();

		logger.warn("Export operation failed. Total failures: {}. Success rate: {}%",
				failedExports.get(),
				calculateSuccessRate());
	}

	/**
	 * Records metrics for an individual API call.
	 */
	public void recordApiCall(String endpoint, Duration duration, boolean success) {
		apiMetrics.computeIfAbsent(endpoint, k -> new ApiMetrics())
				.recordCall(duration, success);

		logger.debug("API call to {} completed in {} ms. Success: {}",
				endpoint, duration.toMillis(), success);
	}

	/**
	 * Records a rate limit hit for an API endpoint.
	 */
	public void recordRateLimitHit(String endpoint) {
		rateLimitHits.incrementAndGet();
		apiMetrics.computeIfAbsent(endpoint, k -> new ApiMetrics())
				.recordRateLimit();

		logger.warn("Rate limit hit for endpoint: {}. Total rate limits: {}",
				endpoint, rateLimitHits.get());
	}

	/**
	 * Records a retry attempt for an API call.
	 */
	public void recordRetryAttempt(String endpoint) {
		retryAttempts.incrementAndGet();
		apiMetrics.computeIfAbsent(endpoint, k -> new ApiMetrics())
				.recordRetry();

		logger.debug("Retry attempt for endpoint: {}. Total retries: {}",
				endpoint, retryAttempts.get());
	}
	
	/**
	 * Records a component failure.
	 */
	public void recordComponentFailure(String componentName, Throwable ex) {
		logger.warn("Component '{}' failed: {}", componentName, ex.getMessage());
		// Track component failures in metrics
		apiMetrics.computeIfAbsent("component_" + componentName, k -> new ApiMetrics())
				.recordFailure();
	}

	/**
	 * Gets a snapshot of current metrics.
	 */
	public MetricsSnapshot getSnapshot() {
		return new MetricsSnapshot(
				totalExports.get(),
				successfulExports.get(),
				failedExports.get(),
				averageExportTime.get(),
				rateLimitHits.get(),
				retryAttempts.get(),
				new HashMap<>(apiMetrics)
		);
	}

	private void updateTimingMetrics(Duration duration) {
		long currentTotal = totalExportTime.addAndGet(duration.toMillis());
		long successCount = successfulExports.get();
		if (successCount > 0) {
			averageExportTime.set(currentTotal / successCount);
		}
	}

	private double calculateSuccessRate() {
		int total = totalExports.get();
		if (total == 0) return 0.0;
		return (double) successfulExports.get() / total * 100.0;
	}
}

