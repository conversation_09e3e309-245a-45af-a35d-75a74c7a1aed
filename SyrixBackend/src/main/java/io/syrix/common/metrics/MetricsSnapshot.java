package io.syrix.common.metrics;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Immutable snapshot of metrics at a point in time.
 * Used for reporting and analysis.
 */
public class MetricsSnapshot {
	@JsonProperty("total_exports")
	private final int totalExports;

	@JsonProperty("successful_exports")
	private final int successfulExports;

	@JsonProperty("failed_exports")
	private final int failedExports;

	@JsonProperty("average_export_time")
	private final long averageExportTime;

	@JsonProperty("rate_limit_hits")
	private final int rateLimitHits;

	@JsonProperty("retry_attempts")
	private final int retryAttempts;

	@JsonProperty("api_metrics")
	private final Map<String, ApiMetrics> apiMetrics;

	@JsonProperty("snapshot_time")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private final Instant snapshotTime;

	public MetricsSnapshot(
			int totalExports,
			int successfulExports,
			int failedExports,
			long averageExportTime,
			int rateLimitHits,
			int retryAttempts,
			Map<String, ApiMetrics> apiMetrics) {

		this.totalExports = totalExports;
		this.successfulExports = successfulExports;
		this.failedExports = failedExports;
		this.averageExportTime = averageExportTime;
		this.rateLimitHits = rateLimitHits;
		this.retryAttempts = retryAttempts;
		this.apiMetrics = new HashMap<>(apiMetrics);
		this.snapshotTime = Instant.now();
	}

	public int getTotalExports() {
		return totalExports;
	}

	public int getSuccessfulExports() {
		return successfulExports;
	}

	public int getFailedExports() {
		return failedExports;
	}

	public long getAverageExportTime() {
		return averageExportTime;
	}

	public int getRateLimitHits() {
		return rateLimitHits;
	}

	public int getRetryAttempts() {
		return retryAttempts;
	}

	public Map<String, ApiMetrics> getApiMetrics() {
		return new HashMap<>(apiMetrics);
	}

	public Instant getSnapshotTime() {
		return snapshotTime;
	}

	@Override
	public String toString() {
		return String.format(
				"Metrics Snapshot [Time: %s]%n" +
						"Total Exports: %d%n" +
						"Successful Exports: %d%n" +
						"Failed Exports: %d%n" +
						"Average Export Time: %d ms%n" +
						"Rate Limit Hits: %d%n" +
						"Retry Attempts: %d",
				snapshotTime,
				totalExports,
				successfulExports,
				failedExports,
				averageExportTime,
				rateLimitHits,
				retryAttempts
		);
	}
}