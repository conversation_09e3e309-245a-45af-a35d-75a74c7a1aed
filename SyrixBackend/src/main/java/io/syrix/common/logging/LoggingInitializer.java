package io.syrix.common.logging;

/**
 * Logging initializer that MUST be called before any other class loading
 * to prevent SLF4J conflicts and JBoss LogManager issues.
 */
public final class LoggingInitializer {
    
    static {
        // This static block MUST run before any logging or Quarkus initialization
        initializeLogging();
    }
    
    /**
     * Initialize logging system properties to prevent SLF4J conflicts.
     * This method MUST be called as early as possible in the application lifecycle.
     */
    public static void initializeLogging() {
        // Set LogManager early to avoid SLF4J conflicts and JBoss LogManager warnings
        System.setProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager");
        
        // Force SLF4J to use JBoss LogManager implementation
        System.setProperty("slf4j.provider", "org.slf4j.impl.JBossSlf4jServiceProvider");
        
        // Disable logback configuration scanning
        System.setProperty("logback.configurationFile", "");
        
        // Additional properties to prevent conflicts
        System.setProperty("org.jboss.logging.provider", "jboss");
        System.setProperty("java.util.logging.manager.class", "org.jboss.logmanager.LogManager");
    }
    
    private LoggingInitializer() {
        // Utility class - prevent instantiation
    }
}