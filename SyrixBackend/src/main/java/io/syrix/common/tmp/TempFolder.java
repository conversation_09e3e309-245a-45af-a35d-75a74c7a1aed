package io.syrix.common.tmp;

import io.syrix.common.exceptions.SyrixRuntimeException;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public final class TempFolder implements Closeable {
	private static final Logger logger = LoggerFactory.getLogger(TempFolder.class);
	private static final Path basePath;
	private static final Set<Path> registeredTempFolders = ConcurrentHashMap.newKeySet(); //for statistics

	static {
		try {
			basePath = Files.createTempDirectory("Syrix");
		} catch (IOException ex) {
			throw new SyrixRuntimeException("Can not create base temp directory", ex);
		}
	}

	private final Path tempFolder;
	private final String folderName;

	public static TempFolder newInstance() {
		return new TempFolder();
	}

	public TempFolder() {
		String threadName = Thread.currentThread().getName().trim();
		try {
			this.tempFolder = Files.createTempDirectory(basePath, threadName);
			folderName = tempFolder.getFileName().toString();
			registeredTempFolders.add(this.tempFolder);
			logger.info("Registering new temp folder: {}", folderName);
		} catch (IOException ex) {
			throw new SyrixRuntimeException("Can not create temp directory for: " + threadName, ex);
		}
	}

	public Path getFolder() {
		return tempFolder;
	}

	public Path generateUniqueFilePath(String prefix, String suffix) {
		Path dir = getFolder();
		Path uniquePath;

		String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
		String uuid = UUID.randomUUID().toString().substring(0, 8); // Короткий UUID

		String fileName = prefix + timestamp + "_" + uuid + suffix;
		uniquePath = dir.resolve(fileName);

		// Дополнительная проверка на всякий случай
		int counter = 1;
		while (Files.exists(uniquePath)) {
			fileName = prefix + timestamp + "_" + uuid + "_" + counter + suffix;
			uniquePath = dir.resolve(fileName);
			counter++;
		}

		return uniquePath;
	}

	@Override
	public void close() {
		try {
			if (Files.notExists(this.tempFolder)) {
				if (registeredTempFolders.contains(tempFolder)) {
					logger.warn("The temporary folder was destroyed earlier. folderName: {}", folderName);
				} else {
					logger.warn("Temp folder has been deleted to another location. folderName: {}", folderName);
				}
			}
			logger.info("Temp folder deleting. folderName: {}", folderName);

			registeredTempFolders.remove(tempFolder);

			Files.walkFileTree(tempFolder, new SimpleFileVisitor<>() {
				@NotNull
				@Override
				public FileVisitResult visitFile(@NotNull Path file, @NotNull BasicFileAttributes attrs) throws IOException {
					Files.delete(file);
					return FileVisitResult.CONTINUE;
				}

				@NotNull
				@Override
				public FileVisitResult postVisitDirectory(@NotNull Path dir, IOException exc) throws IOException {
					Files.delete(dir);
					return FileVisitResult.CONTINUE;
				}
			});
		} catch (Exception ex) {
			logger.warn("Problem with delete folder {}", folderName, ex);
		}
	}
}
