package io.syrix.common.exceptions;

/**
 * Exception thrown when DNS lookup operations fail.
 * This could be due to network issues, invalid domains, or missing records.
 */
public class DnsLookupException extends SyrixRuntimeException {
	/**
	 * Creates a new DnsLookupException with the specified message and cause.
	 *
	 * @param message Description of the lookup failure
	 * @param cause The underlying exception that caused the lookup to fail
	 */
	public DnsLookupException(String message, Throwable cause) {
		super(message, cause);
	}
}