package io.syrix.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;

public record TenantDetails(
		String displayName,
		String domainName,
		String tenantId,
		@JsonProperty("AADAdditionalData")
		ObjectNode additionalData
) {
	// Static nested builder class
	public static class Builder {
		private String displayName;
		private String domainName;
		private String tenantId;
		private ObjectNode additionalData;

		public Builder displayName(String displayName) {
			this.displayName = displayName;
			return this;
		}

		public Builder domainName(String domainName) {
			this.domainName = domainName;
			return this;
		}

		public Builder tenantId(String tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder additionalData(ObjectNode additionalData) {
			this.additionalData = additionalData;
			return this;
		}

		public TenantDetails build() {
			return new TenantDetails(displayName, domainName, tenantId, additionalData);
		}
	}

	// Static builder factory method
	public static Builder builder() {
		return new Builder();
	}
}
