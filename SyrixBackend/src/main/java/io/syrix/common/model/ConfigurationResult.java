package io.syrix.common.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.syrix.datamodel.report.ConfigurationServiceType;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents the complete result of an Entra ID configuration export.
 * This class is immutable and thread-safe.
 */
public class ConfigurationResult {
	private final JsonNode data;
	private final JsonNode metadata;
	private final Instant timestamp;
	private final String exportId;
	private final ConfigurationServiceType serviceType;

	private ConfigurationResult(Builder builder) {
		this.data = Objects.requireNonNull(builder.data, "Configuration data cannot be null");
		this.metadata = Objects.requireNonNull(builder.metadata, "Metadata cannot be null");
		this.timestamp = Objects.requireNonNull(builder.timestamp, "Timestamp cannot be null");
		this.exportId = builder.exportId != null ? builder.exportId : generateExportId(timestamp);
//		this.serviceType = Objects.requireNonNull(builder.serviceType, "Service type cannot be null");
		this.serviceType = builder.serviceType;
	}

	@JsonProperty("data")
	public JsonNode getData() {
		return data;
	}

	@JsonProperty("metadata")
	public JsonNode getMetadata() {
		return metadata;
	}

	@JsonProperty("timestamp")
	public Instant getTimestamp() {
		return timestamp;
	}

	@JsonProperty("export_id")
	public String getExportId() {
		return exportId;
	}

	public ConfigurationServiceType getServiceType() {
		return serviceType;
	}

	private String generateExportId(Instant timestamp) {
		return String.format("export_%s_%s",
				timestamp.toEpochMilli(),
				UUID.randomUUID().toString().substring(0, 8));
	}

	public static Builder builder() {
		return new Builder();
	}

	/**
	 * Builder for creating ConfigurationResult instances.
	 */
	public static class Builder {
		private JsonNode data;
		private JsonNode metadata;
		private Instant timestamp;
		private String exportId;
		private ConfigurationServiceType serviceType;

		public Builder withData(JsonNode data) {
			this.data = data;
			return this;
		}

		public Builder withMetadata(JsonNode metadata) {
			this.metadata = metadata;
			return this;
		}

		public Builder withTimestamp(Instant timestamp) {
			this.timestamp = timestamp;
			return this;
		}

		public Builder withExportId(String exportId) {
			this.exportId = exportId;
			return this;
		}

		public Builder withServiceType(ConfigurationServiceType serviceType) {
			this.serviceType = serviceType;
			return this;
		}

		public ConfigurationResult build() {
			return new ConfigurationResult(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (!(o instanceof ConfigurationResult that)) return false;
		return Objects.equals(exportId, that.exportId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(exportId);
	}

	@Override
	public String toString() {
		return String.format("ConfigurationResult{exportId='%s', timestamp=%s}",
				exportId, timestamp);
	}
}