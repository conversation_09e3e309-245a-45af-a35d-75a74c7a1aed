package io.syrix.common.AI.llm;

/**
 * Provides API-friendly prompt templates for LLM-assisted policy remediation.
 * These templates are designed for programmatic integration with LLM APIs
 * in both Java and Python environments.
 */
public class RemediatorApiPromptTemplate {

    /**
     * System prompt for API integration to guide the LLM's parameter description generation.
     */
    public static final String API_SYSTEM_PROMPT = """
        You are an AI assistant specialized in Microsoft 365 security features for the Syrix application.
        Your task is to create user-friendly descriptions for security policy parameters.
        
        Guidelines:
        1. Analyze Microsoft service names and technical terms in class names
        2. Create clear descriptions explaining parameter purpose and security impact
        3. For enum parameters, explain differences between possible values
        4. Include Microsoft 365 security context in your descriptions
        5. Keep descriptions concise (under 150 words) but comprehensive
        """;

    /**
     * User prompt template for API integration to provide specific parameter details.
     */
    public static final String API_USER_PROMPT = """
        # Parameter Description Task
        
        ## POLICY INFO:
        - Policy ID: {{policyId}}
        - Remediator Class: {{remediatorClassName}}
        - Parameter: {{parameterName}} ({{parameterType}})
        - Microsoft Service: {{serviceName}}
        
        ## YOUR TASK:
        Create a user-friendly description for this parameter that explains:
        1. What this parameter controls in Microsoft {{serviceName}} security
        2. How it affects security posture when configured
        3. What typical values would be appropriate
        
        Use internet search to understand technical terms in the class name and parameter.
        Consider looking up "{{remediatorClassName}}" and "Microsoft {{serviceName}}" for context.
        
        ## YOUR RESPONSE:
        Write only the description text as your response. Keep it under 150 words.
        """;

    /**
     * JSON representation of the prompts for direct API integration.
     */
    public static final String API_JSON_TEMPLATE = """
        {
          "system": "You are an AI assistant specialized in Microsoft 365 security features for the Syrix application. Your task is to create user-friendly descriptions for security policy parameters.",
          "messages": [
            {
              "role": "user",
              "content": "# Parameter Description Task\\n\\n## POLICY INFO:\\n- Policy ID: {{policyId}}\\n- Remediator Class: {{remediatorClassName}}\\n- Parameter: {{parameterName}} ({{parameterType}})\\n- Microsoft Service: {{serviceName}}\\n\\n## YOUR TASK:\\nCreate a user-friendly description for this parameter that explains:\\n1. What this parameter controls in Microsoft {{serviceName}} security\\n2. How it affects security posture when configured\\n3. What typical values would be appropriate\\n\\nUse internet search to understand technical terms in the class name and parameter.\\nConsider looking up \\"{{remediatorClassName}}\\" and \\"Microsoft {{serviceName}}\\" for context.\\n\\n## YOUR RESPONSE:\\nWrite only the description text as your response. Keep it under 150 words."
            }
          ]
        }
        """;

    /**
     * Example Java code for API integration.
     */
    public static final String JAVA_INTEGRATION_EXAMPLE = """
        import com.anthropic.api.Anthropic;
        import com.anthropic.api.messages.Message;
        import com.anthropic.api.messages.MessageRequest;
        
        public class ParameterDescriptionGenerator {
            private final Anthropic anthropicClient;
        
            public ParameterDescriptionGenerator(String apiKey) {
                this.anthropicClient = Anthropic.builder()
                    .apiKey(apiKey)
                    .build();
            }
        
            public String generateParameterDescription(
                    String policyId,
                    String remediatorClassName,
                    String parameterName,
                    String parameterType,
                    String serviceName) {
                
                // Extract just the class name without package
                String shortClassName = remediatorClassName.substring(
                        remediatorClassName.lastIndexOf('.') + 1);
                
                // Format the prompt with specific parameter info
                String userPrompt = RemediatorApiPromptTemplate.API_USER_PROMPT
                        .replace("{{policyId}}", policyId)
                        .replace("{{remediatorClassName}}", shortClassName)
                        .replace("{{parameterName}}", parameterName)
                        .replace("{{parameterType}}", parameterType)
                        .replace("{{serviceName}}", serviceName);
                
                // Make API call to Claude
                MessageRequest request = MessageRequest.builder()
                    .model("claude-3-5-sonnet-20240620")
                    .maxTokens(300)
                    .system(RemediatorApiPromptTemplate.API_SYSTEM_PROMPT)
                    .message(userPrompt)
                    .build();
                
                Message response = anthropicClient.messages(request);
                return response.getContent().get(0).getText();
            }
        }
        """;

    /**
     * Example Python code for API integration.
     */
    public static final String PYTHON_INTEGRATION_EXAMPLE = """
        import anthropic
        
        class ParameterDescriptionGenerator:
            def __init__(self, api_key):
                self.client = anthropic.Anthropic(api_key=api_key)
                
            def generate_parameter_description(
                self, 
                policy_id,
                remediator_class_name,
                parameter_name,
                parameter_type,
                service_name
            ):
                # Extract just the class name without package
                short_class_name = remediator_class_name.split('.')[-1]
                
                # Format the prompt with specific parameter info
                user_prompt = '''# Parameter Description Task
        
        ## POLICY INFO:
        - Policy ID: {policy_id}
        - Remediator Class: {short_class_name}
        - Parameter: {parameter_name} ({parameter_type})
        - Microsoft Service: {service_name}
        
        ## YOUR TASK:
        Create a user-friendly description for this parameter that explains:
        1. What this parameter controls in Microsoft {service_name} security
        2. How it affects security posture when configured
        3. What typical values would be appropriate
        
        Use internet search to understand technical terms in the class name and parameter.
        Consider looking up "{short_class_name}" and "Microsoft {service_name}" for context.
        
        ## YOUR RESPONSE:
        Write only the description text as your response. Keep it under 150 words.'''
                
                # Format the prompt with the specific details
                formatted_prompt = user_prompt.format(
                    policy_id=policy_id,
                    short_class_name=short_class_name,
                    parameter_name=parameter_name,
                    parameter_type=parameter_type,
                    service_name=service_name
                )
                
                # Make the API call
                message = self.client.messages.create(
                    model="claude-3-5-sonnet-20240620",
                    max_tokens=300,
                    system='''You are an AI assistant specialized in Microsoft 365 security features for the Syrix application.
        Your task is to create user-friendly descriptions for security policy parameters.''',
                    messages=[
                        {"role": "user", "content": formatted_prompt}
                    ]
                )
                
                return message.content[0].text
        
        # Usage example
        if __name__ == "__main__":
            generator = ParameterDescriptionGenerator("your-api-key")
            description = generator.generate_parameter_description(
                policy_id="MS.DEFENDER.1.1v1",
                remediator_class_name="io.syrix.products.microsoft.defender.remediation.DefenderPresetSecurityPoliciesRemediator",
                parameter_name="policyType",
                parameter_type="PolicyType",
                service_name="Defender"
            )
            print(description)
        """;

    /**
     * Generates formatted API prompt objects for a specific parameter.
     * 
     * @param policyId The policy ID
     * @param className The full class name
     * @param parameterName The parameter name
     * @param parameterType The parameter type
     * @param serviceName The Microsoft service name
     * @return Formatted JSON prompt for API integration
     */
    public static String generateApiJsonPrompt(
            String policyId, 
            String className, 
            String parameterName, 
            String parameterType, 
            String serviceName) {
        
        // Extract just the class name without package
        String shortClassName = className.substring(className.lastIndexOf('.') + 1);
        
        return API_JSON_TEMPLATE
                .replace("{{policyId}}", policyId)
                .replace("{{remediatorClassName}}", shortClassName)
                .replace("{{parameterName}}", parameterName)
                .replace("{{parameterType}}", parameterType)
                .replace("{{serviceName}}", serviceName);
    }
}
