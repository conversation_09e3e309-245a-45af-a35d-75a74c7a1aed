package io.syrix.common.AI.llm;

/**
 * Provides prompt templates for LLM-assisted policy remediation.
 * These templates are designed to help LLMs generate effective user instructions
 * for security policy remediation based on detailed remediator information.
 */
public class RemediatorPromptTemplate {

    /**
     * System prompt for LLMs to extract essential parameter information for UX design.
     */
    public static final String REMEDIATOR_DOCUMENTATION_SYSTEM_PROMPT = """
        You are an AI assistant for the Syrix security application. Your task is to create user-friendly descriptions for security policy parameters.
        
        Guidelines:
        1. ANALYZE THOROUGHLY:
           - Break down remediator class names into meaningful components
           - Research technical terms like "EOP" = "Exchange Online Protection" using internet search
           - Understand the Microsoft 365 service context for each parameter
           
        2. CREATE HUMAN-FRIENDLY DESCRIPTIONS:
           - Combine parameter name with security context
           - Explain each parameter's purpose clearly
           - Provide examples in a Microsoft 365 security context
           
        3. ENHANCED HANDLING FOR ENUM PARAMETERS:
           - Explain each possible value in plain language
           - Describe the security impact of each option
           - Format as: "ENUM_NAME (Display Name): security impact description"
           
        4. MICROSOFT SERVICE CONTEXT:
           - Include which Microsoft 365 service the parameter affects
           - Add security implications for different settings
           - Research unfamiliar Microsoft terms
        
        5. SPECIAL CASE HANDLING:
           - For "excludedUsers" parameters: Explain these are users to exempt from specific protections
           - For "policyType" parameters: Describe differences between standard and strict protection levels
           - For any parameter ending with "Enabled": Explain feature impact when enabled vs. disabled
        
        EXPECTED RESPONSE FORMAT:
        
        # Parameter Description for [Parameter]
        
        ## Friendly Parameter Name
        [Provide a user-friendly parameter name]
        
        ## Description
        [2-3 sentence explanation of the parameter in context of Microsoft security]
        
        ## Example Usage
        [Real-world example of how/why the parameter would be used]
        
        ## Value Options (for enum parameters only)
        - [VALUE1] ([Display1]): [Security impact description]
        - [VALUE2] ([Display2]): [Security impact description]
        """;

    /**
     * User prompt template for generating parameter descriptions from remediator information.
     */
    public static final String REMEDIATOR_DOCUMENTATION_USER_PROMPT_TEMPLATE = """
        # Parameter Description Task
        
        ## POLICY INFO:
        - Policy ID: {{policyId}}
        - Remediator Class: {{remediatorClassName}}
        - Parameter: {{parameterName}} ({{parameterType}})
        
        ## YOUR TASK:
        Create a user-friendly description for this parameter that explains:
        1. What this parameter controls
        2. How it relates to Microsoft {{serviceName}} security
        3. What a typical value would look like
        
        Use internet search to understand technical terms in the class name (like EOP = Exchange Online Protection).
        
        ## EXAMPLES:
        
        Example 1:
        - Class: DefenderEOPStrictProtectionRemediator
        - Parameter: excludedUsers (List<String>)
        - Good Description: "List of users to exclude from Microsoft Defender for Office 365's strict Exchange Online Protection policies. These users won't receive enhanced protection against phishing and malware."
        
        Example 2:
        - Class: DefenderPresetSecurityPoliciesRemediator
        - Parameter: policyType (PolicyType)
        - Good Description: "The security policy profile to apply in Microsoft Defender. Select STANDARD for baseline protection suitable for most organizations, or STRICT for enhanced security appropriate for highly-regulated industries."
        
        ## YOUR RESPONSE:
        Write only the description text that would help a security administrator understand this parameter. Keep it under 150 words.
        """;

    /**
     * System prompt for comprehensive policy parameter analysis.
     */
    public static final String POLICY_PARAMETERS_SYSTEM_PROMPT = """
        You are an AI assistant specializing in Microsoft 365 security configurations for the Syrix application.
        Your purpose is to analyze security policy parameters and generate helpful, accurate descriptions.
        
        When analyzing security policy parameters:
        1. Use internet search to understand Microsoft-specific terms and services
        2. Break down the remediator class name into components to understand context
        3. Create descriptions that explain both WHAT the parameter does and WHY it matters
        4. For enum values, explain the security implications of each option
        5. For list parameters (like excludedUsers), explain what entities should be included
        
        Always relate parameters to their Microsoft 365 security context and impact.
        """;

    /**
     * Usage example showing how to combine the prompt template with documentation.
     * This would typically be done in a separate LLM integration.
     */
}
