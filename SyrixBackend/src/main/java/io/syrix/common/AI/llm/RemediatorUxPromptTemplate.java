package io.syrix.common.AI.llm;

/**
 * Provides prompt templates for generating UX input specifications from policy documentation.
 * These templates help LLMs transform technical policy documentation into user-friendly
 * UX specifications that designers can use to build input forms.
 */
public class RemediatorUxPromptTemplate {

    /**
     * System prompt for instructing the LLM to generate UX input specifications.
     */
    public static final String UX_SYSTEM_PROMPT = """
        You are a UX specification creator for Microsoft 365 security configuration interfaces.
        Your expertise is in transforming technical policy documentation into clear, user-friendly input specifications
        that UX designers can use to build intuitive configuration forms.
        
        Your role is to bridge the gap between complex security APIs and end users by:
        - Creating clear, simple language for form labels
        - Organizing parameters logically for better user flow
        - Explaining technical parameters in user-friendly terms
        - Suggesting appropriate input types and validation
        - Providing example values for complex parameters
        
        For each policy, you'll analyze its parameters and create a comprehensive UX specification
        that clearly explains what information needs to be collected from users.
        """;

    /**
     * User prompt template for generating UX input specifications from policy documentation.
     */
    public static final String UX_USER_PROMPT = """
        # UX Input Specification Request
        
        ## Task Overview
        I need you to create a UX input specification document for Microsoft 365 security policies.
        This specification will be used by UX designers to build input forms for security configuration.
        
        ## Policies to Focus On
        {{policyIdsList}}
        
        ## Documentation
        Here is the comprehensive documentation containing details about these policies:
        
        ```
        {{policyDocumentation}}
        ```
        
        ## Instructions
        
        1. For each policy specified above, create a UX input specification that includes:
           - A clear, user-friendly title
           - A brief explanation of what the policy configures (1-2 sentences)
           - A complete list of all required input fields with labels and descriptions
           - Appropriate input types for each field (text field, dropdown, multi-select, etc.)
           - Example values or options for each field
        
        2. For dropdown fields (enum parameters):
           - List ALL possible values exactly as shown in the documentation
           - Create a user-friendly display label for each value
           - Include a brief explanation of each option
        
        3. For list parameters (like excludedUsers):
           - Specify what items should be in the list
           - Suggest an appropriate input method (comma-separated text, multi-select, etc.)
           - Provide example values
        
        4. Format and organize the specification for clarity, using:
           - Clear hierarchical structure
           - Consistent formatting for all parameters
           - Visual separation between different policies (if multiple)
        
        ## Output Format
        
        For each policy, structure your response like this:
        
        ```
        # [POLICY_ID] [User-friendly Policy Name]
        
        ## Purpose
        [1-2 sentence explanation of what this policy configures]
        
        ## Required Inputs
        
        ### [Parameter Name]
        - **Label**: [User-friendly field label]
        - **Input Type**: [Appropriate input type]
        - **Description**: [User-friendly explanation]
        - **Options**: [For dropdown fields, list all options with explanations]
        - **Example**: [Example valid input]
        
        [Repeat for each parameter]
        ```
        
        Focus ONLY on the input requirements - do not include implementation details, policy results, or technical background that isn't needed for the input form.
        """;

    /**
     * Generates a formatted UX specification prompt for specific policies.
     * 
     * @param policyIds Array of policy IDs to focus on
     * @param policyDocumentation The full policy documentation
     * @return Formatted UX specification prompt
     */
    public static String generateUxSpecPrompt(String[] policyIds, String policyDocumentation) {
        // Create policy IDs list
        StringBuilder policyIdsList = new StringBuilder();
        for (String policyId : policyIds) {
            policyIdsList.append("- ").append(policyId).append("\n");
        }
        
        return UX_USER_PROMPT
                .replace("{{policyIdsList}}", policyIdsList.toString())
                .replace("{{policyDocumentation}}", policyDocumentation);
    }
    
    /**
     * Example showing how to use this with a single policy.
     * 
     * @param policyId The policy ID
     * @param policyDocumentation The full policy documentation
     * @return Formatted UX specification prompt for a single policy
     */
    public static String generateSinglePolicyUxPrompt(String policyId, String policyDocumentation) {
        return generateUxSpecPrompt(new String[] { policyId }, policyDocumentation);
    }
}
