package io.syrix.common.AI.llm;

import dev.langchain4j.model.anthropic.AnthropicChatModel;

public class AnthropicService implements LLMService{

	private final AnthropicChatModel model;

	public AnthropicService(String apiKey) {
		if (apiKey == null || apiKey.isEmpty()) {
			throw new IllegalArgumentException("API key cannot be null or empty");
		}
		this.model = AnthropicChatModel.builder()
				.apiKey(apiKey)
				.modelName("CLAUDE_3_5_SONNET_20240620")
				.temperature(0.0)
				.build();
	}

	@Override
	public String generate(String system, String prompt) {
		return this.model.generate(prompt);
	}
}
