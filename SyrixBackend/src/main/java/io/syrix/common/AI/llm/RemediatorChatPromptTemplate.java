package io.syrix.common.AI.llm;

/**
 * Provides conversational prompt templates for LLM-assisted policy remediation in chat interfaces.
 * These templates are designed for direct LLM chat interfaces (like Cha<PERSON><PERSON><PERSON>, <PERSON>, etc.)
 * and include detailed instructions for the LLM.
 */
public class RemediatorChatPromptTemplate {

    /**
     * Conversational prompt for chat interfaces to generate user-friendly parameter descriptions.
     * This prompt provides detailed instructions for the LLM and encourages internet searches.
     */
    public static final String CHAT_PROMPT_TEMPLATE = """
        # Microsoft 365 Security Parameter Description Task

        ## About This Task
        I need you to create a user-friendly description for a parameter in a Microsoft 365 security policy remediation. These parameters are used in security configurations but often have technical names that aren't intuitive for users.

        ## Policy Information
        - **Policy ID**: {{policyId}}
        - **Remediator Class**: {{remediatorClassName}}
        - **Parameter**: {{parameterName}} ({{parameterType}})

        ## Your Instructions
        1. First, analyze the class name to understand the security context:
           - Break down "{{remediatorClassName}}" into meaningful components
           - Use internet search to understand technical terms (e.g., "EOP" = "Exchange Online Protection")
           - Research Microsoft 365 security features related to this class

        2. Then, create a user-friendly description for the parameter that:
           - Explains what the parameter controls in plain language
           - Describes how it relates to Microsoft 365 security
           - Provides context about why this setting matters
           - Gives examples of typical values or use cases

        3. If the parameter is an enum type or has predefined values:
           - Explain each possible value and its security implications
           - Describe differences between options (e.g., STANDARD vs. STRICT protection)

        4. For list parameters (like excludedUsers):
           - Explain what entities should be included and why
           - Give examples of when exclusions might be needed

        ## Examples

        ### Example 1
        - **Policy ID**: MS.DEFENDER.4.1v1
        - **Remediator Class**: DefenderEOPStrictProtectionRemediator
        - **Parameter**: excludedUsers (List<String>)

        **Good Description**:
        "List of users to exclude from Microsoft Defender for Office 365's strict Exchange Online Protection policies. These users won't receive enhanced protection against phishing and malware. Typically used for service accounts or specialized roles that need to receive certain file types that would normally be blocked by strict protection rules."

        ### Example 2
        - **Policy ID**: MS.DEFENDER.1.1v1
        - **Remediator Class**: DefenderPresetSecurityPoliciesRemediator
        - **Parameter**: policyType (PolicyType)

        **Good Description**:
        "The security policy profile to apply in Microsoft Defender. Select STANDARD for baseline protection suitable for most organizations, or STRICT for enhanced security appropriate for highly-regulated industries or organizations handling sensitive data."

        ## Your Response
        Please provide a clear, helpful description that a security administrator would understand. Make your description 2-3 sentences long, focused on what this parameter does and why it's important. Feel free to use internet search to understand Microsoft 365 security features in detail.
        """;
    
    /**
     * Generates a formatted chat prompt for a specific parameter.
     * 
     * @param policyId The policy ID
     * @param className The full class name
     * @param parameterName The parameter name
     * @param parameterType The parameter type
     * @return Formatted chat prompt
     */
    public static String generateChatPrompt(String policyId, String className, String parameterName, String parameterType) {
        // Extract just the class name without package
        String shortClassName = className.substring(className.lastIndexOf('.') + 1);
        
        return CHAT_PROMPT_TEMPLATE
                .replace("{{policyId}}", policyId)
                .replace("{{remediatorClassName}}", shortClassName)
                .replace("{{parameterName}}", parameterName)
                .replace("{{parameterType}}", parameterType);
    }
}
