package io.syrix.common.AI.llm;

import dev.langchain4j.model.chat.request.ResponseFormat;
import dev.langchain4j.model.chat.request.ResponseFormatType;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import dev.langchain4j.service.output.JsonSchemas;
import io.syrix.products.microsoft.entra.model.RiskAssessment;
import org.apache.commons.lang3.StringUtils;

public class GeminiService implements LLMService{

	private final GoogleAiGeminiChatModel model;

	public GeminiService(String apiKey) {
		String googleKey = System.getenv("GOOGLE_API_KEY");
		if (StringUtils.isEmpty(apiKey)) {
			if (StringUtils.isEmpty(googleKey)) {
				throw new IllegalArgumentException("API key cannot be null or empty");
			}
			apiKey = googleKey;
		}
		this.model = GoogleAiGeminiChatModel
				.builder()
				.apiKey(apiKey)
				.responseFormat(ResponseFormat.builder()
						.type(ResponseFormatType.JSON)
						.jsonSchema(JsonSchemas.jsonSchemaFrom(RiskAssessment.class).get())
						.build())
				.modelName("gemini-2.0-flash-exp")
				.temperature(0.0)
				.build();
	}
	@Override
	public String generate(String system, String prompt) {
		return this.model.generate(prompt);
	}
}
