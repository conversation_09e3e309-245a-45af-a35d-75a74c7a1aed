package io.syrix.common.AI.llm;

import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;

import java.util.List;

// OpenAI implementation
public class OpenAIService implements LLMService {
	private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(OpenAIService.class);
	private final ChatLanguageModel model;

	public OpenAIService(String apiKey, String baseUrl) {
		this.model = OpenAiChatModel.builder()
				.apiKey(apiKey)
				.baseUrl(baseUrl)
				.modelName("gpt-4o-mini")
				.temperature(0.0)
				.build();
	}

	@Override
	public String generate(String system, String prompt) {
		logger.info("Start generating response for prompt: {}", prompt);
		long startTime = System.currentTimeMillis();
		ChatMessage message = SystemMessage.from(system);
		UserMessage userMessage = UserMessage.from(prompt);
		List<ChatMessage> messages = List.of(message, userMessage);
		String response = model.generate(messages).content().text();
		logger.info("Finished generating response for prompt, time {}", System.currentTimeMillis()-startTime);
		return response;
	}
}