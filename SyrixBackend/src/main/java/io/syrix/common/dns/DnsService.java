package io.syrix.common.dns;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.HttpPipeline;
import com.azure.core.http.HttpPipelineBuilder;
import com.azure.core.http.policy.BearerTokenAuthenticationPolicy;
import com.azure.core.http.policy.RetryPolicy;
import com.azure.core.http.policy.UserAgentPolicy;
import com.azure.resourcemanager.dns.fluent.DnsManagementClient;
import com.azure.resourcemanager.dns.implementation.DnsManagementClientBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.net.InternetDomainName;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.DnsLookupException;
import io.syrix.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.naming.NameNotFoundException;
import javax.naming.NamingEnumeration;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Hashtable;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * Service for performing DNS lookups including SPF, DKIM, and DMARC records.
 * Provides multiple resolution strategies with fallback support.
 */
public class DnsService implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(DnsService.class);

	// Constants
	private static final String AZURE_MANAGEMENT_SCOPE = "https://management.azure.com/.default";
	private static final Duration DNS_TIMEOUT = Duration.ofSeconds(10);
	private static final String[] DEFAULT_DNS_SERVERS = {"*******", "*******"};
	private static final Pattern SPF_PATTERN = Pattern.compile("^\\s*\"?\\s*v=spf1");
	private static final String TRADITONAL = "traditional";
	private static final String QUERY_RETURNED_NOT_EMPTY = "Query returned %s txt records";
	private static final String QUERY_RETURNED_EMPTY = "Query returned NXDomain";
	private static final String RDATA = "rdata";
	private static final String DNS_NOT_FOUND = "DNS name not found; remaining name : {}";
	private static final String DMARC_PREFIX = "_dmarc.";
	// Service components
	private final List<DnsResolver> resolvers;
	private final ExecutorService executor;
	private final ObjectMapper objectMapper;

	/**
	 * Creates a new DnsService instance with the specified configuration.
	 */
	private DnsService(List<String> dnsServers,
					   TokenCredential azureCredential,
					   String azureSubscriptionId,
					   ObjectMapper objectMapper) {
		this.executor = Executors.newVirtualThreadPerTaskExecutor();
		this.resolvers = initializeResolvers(dnsServers, azureCredential, azureSubscriptionId);
		this.objectMapper = objectMapper;
	}

	private List<DnsResolver> initializeResolvers(List<String> dnsServers,
												  TokenCredential azureCredential, String azureSubscriptionId) {
		List<DnsResolver> resolverList = new ArrayList<>();

		// Add Java's built-in resolver first
		resolverList.add(new JavaDnsResolver());

		// Add Azure resolver if credentials provided
		if (azureCredential != null && azureSubscriptionId != null) {
			DnsManagementClient azureClient = createAzureClient(azureCredential, azureSubscriptionId);
			if (azureClient != null) {
				resolverList.add(new AzureDnsResolver(azureClient));
			}
		}

		// Add custom DNS server resolvers
		List<String> servers = dnsServers.isEmpty() ? Arrays.asList(DEFAULT_DNS_SERVERS) : dnsServers;
		servers.forEach(server -> resolverList.add(new CustomDnsResolver(server)));

		return resolverList;
	}

	private DnsManagementClient createAzureClient(TokenCredential credential, String subscriptionId) {
		try {
			HttpPipeline pipeline = new HttpPipelineBuilder()
					.policies(
							new UserAgentPolicy(),
							new RetryPolicy(),
							new BearerTokenAuthenticationPolicy(credential, AZURE_MANAGEMENT_SCOPE)
					)
					.build();

			return new DnsManagementClientBuilder()
					.pipeline(pipeline)
					.subscriptionId(subscriptionId)
					.buildClient();
		} catch (Exception e) {
			logger.error("Failed to initialize Azure DNS client: {}", e.getMessage(), e);
			return null;
		}
	}

	public CompletableFuture<JsonNode> getDkimConfiguration(
			CompletableFuture<List<String>> domainsFuture,
			CompletableFuture<JsonNode> dkimRecordsFuture) {
		return domainsFuture.thenCompose(domains ->
				dkimRecordsFuture.thenApply(dkimRecords -> {
					ObjectNode aggregatedResults = objectMapper.createObjectNode();

					// For each domain, attach the precomputed DKIM records
					domains.forEach(domain -> {
						ObjectNode domainResult = objectMapper.createObjectNode();
						domainResult.put(Constants.DOMAIN_FIELD, domain);

						// Attach DKIM records filtered by the current domain
						ObjectNode domainDkimRecords = objectMapper.createObjectNode();
						dkimRecords.fields().forEachRemaining(entry -> {
							if (entry.getKey().startsWith(domain)) {
								domainDkimRecords.set(entry.getKey(), entry.getValue());
							}
						});
						domainResult.set("dkim_records", domainDkimRecords);
						aggregatedResults.set(domain, domainResult);
					});

					return aggregatedResults;
				})
		);
	}

	public CompletableFuture<JsonNode> getDomainDmarcRecords(CompletableFuture<JsonNode> domainsFuture, Set<String> successfulCommands, Set<String> unsuccessfulCommands) {
		return domainsFuture.thenCompose(domains -> {
			List<String> domainsList = extractDomainNames(domains);
			List<CompletableFuture<JsonNode>> dmarcFutures = domainsList.stream().map(this::lookupDmarcRecords).toList();
			successfulCommands.add("getDomainDmarcRecords");
			return combineToJsonObject(dmarcFutures);
		});
	}

	public CompletableFuture<JsonNode> getDomainDkimRecords(
			CompletableFuture<JsonNode> domainsFuture,
			Set<String> successfulCommands,
			Set<String> unsuccessfulCommands) {
		return domainsFuture.thenCompose(domains -> {
			List<String> domainsList = extractDomainNames(domains);
			List<CompletableFuture<JsonNode>> dkimFutures = domainsList.stream().map(this::getDomainDkimRecords).toList();
			successfulCommands.add("Get-DkimSigningConfig");
			successfulCommands.add("getDomainDkimRecords");
			return combineToJsonObject(dkimFutures);
		});
	}

	private CompletableFuture<JsonNode> getDomainDkimRecords(String domain) {
		List<String> selectors = new ArrayList<>();
		selectors.add("selector1");
		selectors.add("selector2");
		selectors.add("selector1." + domain.replace(".", "-"));
		selectors.add("selector2." + domain.replace(".", "-"));

		List<DnsQueryLog> logs = new ArrayList<>();
		// Try each selector until we find records
		for (String selector : selectors) {
			String lookupDomain = String.format("%s._domainkey.%s", selector, domain);
			DnsQueryResults queryResult = lookupTxtRecords(lookupDomain);
			if (!queryResult.records().isEmpty()) {
				ObjectNode result = objectMapper.createObjectNode();
				result.put(Constants.DOMAIN_FIELD, domain);
				List<String> rdata = queryResult.records();
				result.set(RDATA, objectMapper.valueToTree(rdata));
				logs.add(new DnsQueryLog(TRADITONAL,
						lookupDomain,
						String.format(QUERY_RETURNED_NOT_EMPTY, queryResult.records().size())));
				result.set("log", objectMapper.valueToTree(logs));
				return CompletableFuture.completedFuture((JsonNode) result);
			} else {
				logs.add(new DnsQueryLog(TRADITONAL, lookupDomain, QUERY_RETURNED_EMPTY));
			}
		}

		// No records found for any selector
		ObjectNode result = objectMapper.createObjectNode();
		result.put(Constants.DOMAIN_FIELD, domain);
		result.set(RDATA, objectMapper.createArrayNode());
		result.set("log", objectMapper.valueToTree(logs));
		return CompletableFuture.completedFuture((JsonNode) result);
	}

	public static List<String> extractDomainNames(JsonNode domains) {
		List<String> domainNames = new ArrayList<>();

		// Check if the root node is an array
		if (domains != null && domains.isArray()) {
			// Iterate over each element in the array
			for (JsonNode node : domains) {
				// Get the "DomainName" field as a TextNode and extract the text value
				JsonNode domainNameNode = node.get("DomainName");
				if (domainNameNode != null && domainNameNode.isTextual()) {
					domainNames.add(domainNameNode.asText());
				} else if(node.get("id") != null && node.get("id").isTextual()) {
					domainNames.add(node.get("id").asText());
				}
			}
		}
		return domainNames;
	}

	public CompletableFuture<JsonNode> combineToJsonObject(List<CompletableFuture<JsonNode>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> futures.stream()
						.map(CompletableFuture::join)
						.toList()).thenApply(jsonNodes -> {
					ArrayNode arrayNode = objectMapper.createArrayNode();
					jsonNodes.forEach(arrayNode::add);
					return arrayNode;
				});
	}

	/**
	 * Performs batch lookup of SPF records for multiple domains with detailed response.
	 */
	public CompletableFuture<JsonNode> getDomainSpfRecords(CompletableFuture<JsonNode> domainsFuture,
														   Set<String> successfulCommands,
														   Set<String> unsuccessfulCommands) {
		return domainsFuture.thenCompose(domains -> {
					List<String> domainsList = extractDomainNames(domains);
					if (domainsList.isEmpty()) {
						logger.warn("No domains provided for SPF record lookup");
						return CompletableFuture.completedFuture(objectMapper.createObjectNode());
					}
					logger.info("Starting SPF record lookup for {} domains", domains.size());
					List<CompletableFuture<JsonNode>> spfFutures = domainsList.stream()
							.map(this::processSpfRecordForDomain).toList();
					successfulCommands.add("getDomainSpfRecords");
					return combineToJsonObject(spfFutures);
				})
				.exceptionally(error -> {
					unsuccessfulCommands.add("getDomainSpfRecords");
					logger.error("Failed to process domain list: {}", error.getMessage(), error);
					return objectMapper.createObjectNode();
				});
	}

	/**
	 * Process SPF records for a single domain with fallback and confidence tracking.
	 */
	public CompletableFuture<JsonNode> processSpfRecordForDomain(String domain) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				DnsQueryResults result = performSpfLookup(domain);
				return createDomainRecordNode(domain, result);
			} catch (Exception e) {
				logger.error("Failed to lookup SPF record for domain {}: {}",
						domain, e.getMessage(), e);
				return createErrorRecordNode(domain, e);
			}
		}, executor);
	}

	/**
	 * Performs SPF lookup with fallback and confidence tracking.
	 */
	private DnsQueryResults performSpfLookup(String domain) {
		List<String> records = new ArrayList<>();
		List<DnsQueryLog> logEntries = new ArrayList<>();
		boolean atLeastOneResolverSucceeded = false;
		boolean isthereAnyRecord = false;
		for (DnsResolver resolver : resolvers) {
			try {
				List<String> foundRecords = resolver.lookup(domain, "TXT").stream()
						.filter(tmpRecord -> SPF_PATTERN.matcher(tmpRecord).find())
								.map(StringUtil::removeQuotes).toList();
				atLeastOneResolverSucceeded = true;
				if (!foundRecords.isEmpty()) {
					records = foundRecords;
					isthereAnyRecord = true;
					logEntries.add(new DnsQueryLog(TRADITONAL, domain,
							String.format(QUERY_RETURNED_NOT_EMPTY, records.size())));
					break;
				}
			} catch (Exception e) {
				logger.warn(DNS_NOT_FOUND, domain);
			}
		}
		if (!atLeastOneResolverSucceeded || !isthereAnyRecord) {
			logEntries.add(new DnsQueryLog(TRADITONAL, domain, QUERY_RETURNED_EMPTY));
		}

		return new DnsQueryResults(records, logEntries);
	}

	private JsonNode createDomainRecordNode(String domain, DnsQueryResults result) {
		ObjectNode recordNode = objectMapper.createObjectNode();
		recordNode.put(Constants.DOMAIN_FIELD, domain);
		recordNode.set("log", objectMapper.valueToTree(result.logEntries()));
		recordNode.set(RDATA, objectMapper.valueToTree(result.records()));
		return recordNode;
	}

	private JsonNode createErrorRecordNode(String domain, Throwable error) {
		ObjectNode recordNode = objectMapper.createObjectNode();
		recordNode.put(Constants.DOMAIN_FIELD, domain);
		recordNode.put("status", "error");
		recordNode.put("error", error.getMessage());
		recordNode.put("timestamp", Instant.now().toString());
		return recordNode;
	}

	/**
	 * Performs DNS lookups using all configured resolvers with fallback.
	 */
	public DnsQueryResults lookupTxtRecords(String domain) {
		for (DnsResolver resolver : resolvers) {
			try {
				List<String> records = resolver.lookup(domain, "TXT");
				if (!records.isEmpty()) {
					DnsQueryLog dnsLog = new DnsQueryLog(TRADITONAL, domain,
							String.format(QUERY_RETURNED_NOT_EMPTY, records.size()));
					return new DnsQueryResults(records.stream().map(StringUtil::removeQuotes).toList(), List.of(dnsLog));
				}
			} catch (Exception e) {
				logger.warn("{} lookup failed for {}", resolver.getClass().getSimpleName(), domain);
			}
		}

		DnsQueryLog dnsLog = new DnsQueryLog(TRADITONAL, domain, QUERY_RETURNED_EMPTY);
		return new DnsQueryResults(List.of(), List.of(dnsLog));
	}


	public CompletableFuture<JsonNode> lookupDmarcRecords(String domain) {
		return CompletableFuture.supplyAsync(() -> {
			String dmarcDomain = DMARC_PREFIX + domain;
			logger.debug("Looking up DMARC records for full domain: {}", dmarcDomain);
			DnsQueryResults result = lookupTxtRecords(dmarcDomain);
			if (result.records().isEmpty()){
				InternetDomainName idn = InternetDomainName.from(domain);
				if (idn.hasPublicSuffix()) {
					String orgLevelDomain = idn.topPrivateDomain().toString();
					dmarcDomain = DMARC_PREFIX + orgLevelDomain;
					logger.debug("Looking up DMARC records for org level domain : {}", dmarcDomain);
					DnsQueryResults resultOrgLevel = lookupTxtRecords(dmarcDomain);
					List<DnsQueryLog> newLsit = Stream.concat(resultOrgLevel.logEntries().stream(),
							result.logEntries().stream()).toList();
					DnsQueryResults returnResult = new DnsQueryResults(resultOrgLevel.records(), newLsit);
					return createDomainRecordNode(domain, returnResult);
				}
			}
			return createDomainRecordNode(domain, result);
		}, executor);
	}


	@Override
	public void close() {
		executor.shutdown();
		resolvers.forEach(DnsResolver::close);
	}

	// Internal resolver interfaces and implementations

	private interface DnsResolver extends AutoCloseable {
		List<String> lookup(String domain, String attribute);

		@Override
		default void close() {
		}
	}

	private static class JavaDnsResolver implements DnsResolver {
		@Override
		public List<String> lookup(String domain, String attribute) {
			List<String> records = new ArrayList<>();
			Hashtable<String, String> env = new Hashtable<>();
			env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
			env.put("java.naming.provider.url", "dns:");
			env.put("com.sun.jndi.dns.timeout.initial", String.valueOf(DNS_TIMEOUT.toMillis()));

			try {
				DirContext ctx = new InitialDirContext(env);
				Attributes attrs = null;
				if (attribute != null && !attribute.isEmpty()) {
					attrs = ctx.getAttributes(domain, new String[]{attribute});
					Attribute txt = attrs.get(attribute);
					if (txt != null) {
						NamingEnumeration<?> e = txt.getAll();
						while (e.hasMore()) {
							records.add(e.next().toString());
						}
					}
				} else {
					attrs = ctx.getAttributes(domain);
					NamingEnumeration<?> e = attrs.getAll();
					while (e.hasMore()) {
						records.add(e.next().toString());
					}
				}
			} catch (Exception e) {
				throw new DnsLookupException("Faild to dns lookup", e);
			}
			return records;
		}
	}

	private record AzureDnsResolver(DnsManagementClient client) implements DnsResolver {
		@Override
		public List<String> lookup(String domain, String attribute) {
			String zoneName = extractZoneName(domain);
			String recordName = extractRecordName(domain, zoneName);

			return client.getRecordSets().listByDnsZone(null, zoneName).stream()
					.filter(recordSet -> Objects.equals(recordSet.name(), recordName))
					.flatMap(recordSet -> recordSet.txtRecords().stream()
							.map(txt -> String.join("", txt.value()))).toList();
		}

		private String extractZoneName(String domain) {
			return domain.substring(domain.lastIndexOf('.', domain.lastIndexOf('.') - 1) + 1);
		}

		private String extractRecordName(String domain, String zoneName) {
			return domain.substring(0, domain.length() - zoneName.length() - 1);
		}
	}

	private record CustomDnsResolver(String serverAddress) implements DnsResolver {
		@Override
		public List<String> lookup(String domain, String attribute) {
			List<String> records = new ArrayList<>();
			Hashtable<String, String> env = new Hashtable<>();
			env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
			env.put("java.naming.provider.url", "dns://" + serverAddress);
			env.put("com.sun.jndi.dns.timeout.initial", String.valueOf(DNS_TIMEOUT.toMillis()));

			try {
				DirContext ctx = new InitialDirContext(env);
				Attributes attrs = null;
				if (attribute != null && !attribute.isEmpty()) {
					attrs = ctx.getAttributes(domain, new String[]{attribute});
					Attribute txt = attrs.get(attribute);
					if (txt != null) {
						NamingEnumeration<?> e = txt.getAll();
						while (e.hasMore()) {
							records.add(e.next().toString());
						}
					}
				} else {
					attrs = ctx.getAttributes(domain);
					NamingEnumeration<?> e = attrs.getAll();
					while (e.hasMore()) {
						records.add(e.next().toString());
					}
				}
			} catch (NameNotFoundException nnfe) {
				logger.warn(DNS_NOT_FOUND, domain);
			} catch (Exception e) {
				throw new DnsLookupException("Faild to dns lookup", e);
			}
			return records;
		}
	}

	// Builder pattern for configuration
	public static class Builder {
		private List<String> dnsServers = new ArrayList<>();
		private TokenCredential azureCredential;
		private String azureSubscriptionId;
		private ObjectMapper objectMapper;

		public Builder withDnsServers(String... servers) {
			this.dnsServers.addAll(Arrays.asList(servers));
			return this;
		}

		public Builder withAzureCredential(TokenCredential credential) {
			this.azureCredential = credential;
			return this;
		}

		public Builder withAzureSubscriptionId(String subscriptionId) {
			this.azureSubscriptionId = subscriptionId;
			return this;
		}

		public Builder withObjectMapper(ObjectMapper objectMapper) {
			this.objectMapper = objectMapper;
			return this;
		}

		public DnsService build() {
			return new DnsService(dnsServers, azureCredential, azureSubscriptionId, objectMapper);
		}
	}
}