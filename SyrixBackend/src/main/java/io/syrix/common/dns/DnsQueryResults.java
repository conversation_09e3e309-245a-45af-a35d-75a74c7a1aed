package io.syrix.common.dns;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Record representing a collection of DNS query results and logs.
 * Contains both the DNS records returned and the logs of queries performed.
 *
 * @param records List of DNS records returned from queries
 * @param logEntries List of DnsQueryLog entries documenting the query process
 */
public record DnsQueryResults(
		List<String> records,
		List<DnsQueryLog> logEntries
) {
	/**
	 * Constructs a new DnsQueryResults instance with defensive copies of the input lists.
	 *
	 * @param records List of DNS records
	 * @param logEntries List of query log entries
	 * @throws IllegalArgumentException if either parameter is null
	 */
	public DnsQueryResults {
		if (records == null || logEntries == null) {
			throw new IllegalArgumentException("Neither records nor logEntries can be null");
		}

		// Create defensive copies of the lists
		records = Collections.unmodifiableList(new ArrayList<>(records));
		logEntries = Collections.unmodifiableList(new ArrayList<>(logEntries));
	}

	/**
	 * Returns a string representation of the query results.
	 *
	 * @return A formatted string containing the records and log entries
	 */
	@Override
	public String toString() {
		return String.format(
				"DnsQueryResults[records=%s, logEntries=%s]",
				records,
				logEntries
		);
	}
}
