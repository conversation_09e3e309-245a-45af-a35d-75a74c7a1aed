package io.syrix.common.dns;

/**
 * Record representing a DNS query log entry.
 * Includes information about the query method, result, and the queried name.
 *
 * @param query_method The method used to perform the DNS query (e.g., "traditional", "DoH")
 * @param query_result The result of the DNS query operation
 * @param query_name   The fully qualified domain name that was queried
 */
public record DnsQueryLog(
		String query_method,
		String query_result,
		String query_name
) {
	/**
	 * Validates the record fields are not null or empty.
	 *
	 * @throws IllegalArgumentException if any field is null or empty
	 */
	public DnsQueryLog {
		if (query_method == null || query_method.isEmpty()) {
			throw new IllegalArgumentException("query_method cannot be null or empty");
		}
		if (query_result == null || query_result.isEmpty()) {
			throw new IllegalArgumentException("query_result cannot be null or empty");
		}
		if (query_name == null || query_name.isEmpty()) {
			throw new IllegalArgumentException("query_name cannot be null or empty");
		}
	}

	/**
	 * Returns a string representation of the DNS query log entry.
	 *
	 * @return A formatted string containing the log entry details
	 */
	@Override
	public String toString() {
		return String.format(
				"DnsQueryLog[method=%s, result=%s, name=%s]",
				query_method,
				query_result,
				query_name
		);
	}
}
