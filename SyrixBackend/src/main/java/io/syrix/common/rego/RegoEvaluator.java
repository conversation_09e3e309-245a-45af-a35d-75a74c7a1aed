package io.syrix.common.rego;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.policy.PolicyComparisonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Evaluates Rego rules against configurations using the OPA CLI.
 * Handles rule parsing, evaluation, and mapping results.
 */
public class RegoEvaluator {
	private static final Logger logger = LoggerFactory.getLogger(RegoEvaluator.class);
	private static final ObjectMapper objectMapper = new ObjectMapper();

	private final String opaPath;
	private final Path tempDir;

	public RegoEvaluator(String opaPath) {
		this.opaPath = opaPath;
		try {
			this.tempDir = Files.createTempDirectory("rego-eval-");
		} catch (Exception e) {
			throw new RuntimeException("Failed to create temp directory for Rego evaluation", e);
		}
	}

	/**
	 * Evaluates a set of Rego rules against a configuration.
	 * Returns list of evaluation results for each rule.
	 */
	public List<PolicyComparisonService.RuleEvaluation> evaluateRules(String regoRules, JsonNode config) {
		List<PolicyComparisonService.RuleEvaluation> results = new ArrayList<>();

		try {
			// Save config and rules to temp files
			Path configFile = writeTemp("config", config.toString());
			Path rulesFile = writeTemp("rules", regoRules);

			// Run OPA eval
			JsonNode evalResult = runOPAEval(configFile, rulesFile);

			// Parse results
			results = parseEvalResults(evalResult);

			logger.info("Evaluated {} Rego rules", results.size());

		} catch (Exception e) {
			logger.error("Failed to evaluate Rego rules: {}", e.getMessage());
			throw new RuntimeException("Rego evaluation failed", e);
		} finally {
			cleanup();
		}

		return results;
	}

	/**
	 * Writes content to a temp file and returns the path.
	 */
	private Path writeTemp(String prefix, String content) throws Exception {
		Path file = tempDir.resolve(prefix + "-" + System.currentTimeMillis());
		Files.writeString(file, content);
		return file;
	}

	/**
	 * Runs OPA eval command line and returns JSON result.
	 */
	private JsonNode runOPAEval(Path configFile, Path rulesFile) throws Exception {
		List<String> command = new ArrayList<>();
		command.add(opaPath);
		command.add("eval");
		command.add("--data");
		command.add(rulesFile.toString());
		command.add("--input");
		command.add(configFile.toString());
		command.add("data.tests"); // Evaluate tests namespace
		command.add("--format");
		command.add("json");

		ProcessBuilder pb = new ProcessBuilder(command);
		pb.redirectErrorStream(true);
		Process p = pb.start();

		// Read output
		StringBuilder output = new StringBuilder();
		try (BufferedReader reader = new BufferedReader(
				new InputStreamReader(p.getInputStream()))) {
			String line;
			while ((line = reader.readLine()) != null) {
				output.append(line).append("\n");
			}
		}

		// Wait for process to complete
		if (!p.waitFor(30, TimeUnit.SECONDS)) {
			p.destroyForcibly();
			throw new RuntimeException("OPA eval timed out");
		}

		if (p.exitValue() != 0) {
			throw new RuntimeException("OPA eval failed: " + output);
		}

		return objectMapper.readTree(output.toString());
	}

	/**
	 * Parses OPA evaluation results into RuleEvaluation objects.
	 */
	private List<PolicyComparisonService.RuleEvaluation> parseEvalResults(JsonNode evalResult) {
		List<PolicyComparisonService.RuleEvaluation> evaluations = new ArrayList<>();

		if (evalResult.has("result") && evalResult.get("result").isArray()) {
			for (JsonNode result : evalResult.get("result")) {
				// Parse individual test result
				if (result.has("expressions") && result.get("expressions").isArray()) {
					JsonNode expr = result.get("expressions").get(0);

					if (expr.has("value") && expr.get("value").isObject()) {
						JsonNode test = expr.get("value");

						String ruleId = test.path("PolicyId").asText();
						boolean compliant = test.path("RequirementMet").asBoolean();
						String details = test.path("ReportDetails").asText();

						evaluations.add(new PolicyComparisonService.RuleEvaluation(ruleId, compliant, details));

						// Log evaluation details
						if (!compliant) {
							logger.warn("Rule {} evaluation failed: {}", ruleId, details);
						}
					}
				}
			}
		}

		return evaluations;
	}

	/**
	 * Maps OPA test result fields to evaluation status.
	 */
	private boolean evaluateTestResult(JsonNode test) {
		// Handle different test result formats
		if (test.has("RequirementMet")) {
			return test.get("RequirementMet").asBoolean();
		}
		if (test.has("result")) {
			return test.get("result").asBoolean();
		}
		// Default to false if test format not recognized
		return false;
	}

	/**
	 * Cleanup temp files.
	 */
	private void cleanup() {
		try {
			Files.walk(tempDir)
					.sorted((a, b) -> b.compareTo(a))
					.map(Path::toFile)
					.forEach(File::delete);
		} catch (Exception e) {
			logger.error("Failed to cleanup temp files: {}", e.getMessage());
		}
	}

	/**
	 * Validates Rego rule syntax before evaluation.
	 */
	public boolean validateRules(String regoRules) {
		try {
			Path rulesFile = writeTemp("validate", regoRules);

			List<String> command = new ArrayList<>();
			command.add(opaPath);
			command.add("check");
			command.add(rulesFile.toString());

			ProcessBuilder pb = new ProcessBuilder(command);
			Process p = pb.start();

			return p.waitFor() == 0;

		} catch (Exception e) {
			logger.error("Failed to validate Rego rules: {}", e.getMessage());
			return false;
		}
	}

	/**
	 * Helper method to parse rule evaluation from test result.
	 */
	private static class RuleResult {
		private final String ruleId;
		private final boolean passed;
		private final String message;

		public RuleResult(JsonNode result) {
			this.ruleId = result.path("PolicyId").asText();
			this.passed = result.path("RequirementMet").asBoolean();
			this.message = result.path("ReportDetails").asText();
		}
	}
}