package io.syrix.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.FileOperationException;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.List;
import java.util.stream.StreamSupport;

public class FileUtils {
	private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);
	private static final ObjectMapper objectMapper = new ObjectMapper();

	public static void writeTextFile(String content, Path filePath) {
		try {
			ensureParentDirectoryExists(filePath);
			Files.writeString(filePath, content, StandardCharsets.UTF_8,
					StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
			logger.debug("Successfully wrote content to file: {}", filePath);
		} catch (IOException e) {
			throw new FileOperationException("Failed to write file: " + filePath, e);
		}
	}

	public static String readTextFile(Path filePath) {
		try {
			return Files.readString(filePath, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new FileOperationException("Failed to read file: " + filePath, e);
		}
	}

	public static JsonNode readJsonFile(Path filePath) {
		try {
			return objectMapper.readTree(filePath.toFile());
		} catch (IOException e) {
			throw new FileOperationException("Failed to parse JSON file: " + filePath, e);
		}
	}

	public static void writeJsonFile(JsonNode json, Path filePath) {
		try {
			ensureParentDirectoryExists(filePath);
			objectMapper.writerWithDefaultPrettyPrinter().writeValue(filePath.toFile(), json);
		} catch (IOException e) {
			throw new FileOperationException("Failed to write JSON file: " + filePath, e);
		}
	}

	public static void createDirectory(Path dirPath) {
		try {
			Files.createDirectories(dirPath);
			logger.debug("Created directory: {}", dirPath);
		} catch (IOException e) {
			throw new FileOperationException("Failed to create directory: " + dirPath, e);
		}
	}

	public static boolean fileExists(Path filePath) {
		return Files.exists(filePath) && Files.isRegularFile(filePath);
	}

	public static List<Path> listFiles(Path directory, String glob) {
		try (var stream = Files.newDirectoryStream(directory, glob)) {
			return StreamSupport.stream(stream.spliterator(), false).toList();
		} catch (IOException e) {
			throw new FileOperationException("Failed to list files in directory: " + directory, e);
		}
	}

	public static boolean deleteFile(Path filePath) {
		try {
			return Files.deleteIfExists(filePath);
		} catch (IOException e) {
			throw new FileOperationException("Failed to delete file: " + filePath, e);
		}
	}

	public static void moveFile(Path source, Path target) {
		try {
			ensureParentDirectoryExists(target);
			Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
		} catch (IOException e) {
			throw new FileOperationException("Failed to move file from " + source + " to " + target, e);
		}
	}

	private static void ensureParentDirectoryExists(Path filePath) throws IOException {
		Path parent = filePath.getParent();
		if (parent != null) {
			Files.createDirectories(parent);
		}
	}

	public static void copyDirectory(Path source, Path target) throws IOException {
		if (!Files.exists(source)) return;

		Files.walkFileTree(source, new SimpleFileVisitor<>() {
			@NotNull
			@Override
			public FileVisitResult preVisitDirectory(Path dir, @NotNull BasicFileAttributes attrs) throws IOException {
				Path relative = source.relativize(dir);
				Path targetDir = target.resolve(relative);
				Files.createDirectories(targetDir);
				return FileVisitResult.CONTINUE;
			}

			@NotNull
			@Override
			public FileVisitResult visitFile(Path file, @NotNull BasicFileAttributes attrs) throws IOException {
				Path relative = source.relativize(file);
				Path targetFile = target.resolve(relative);
				Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
				return FileVisitResult.CONTINUE;
			}
		});
	}
}