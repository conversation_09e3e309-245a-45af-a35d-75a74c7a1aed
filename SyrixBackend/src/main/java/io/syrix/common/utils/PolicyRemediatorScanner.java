package io.syrix.common.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Scans a codebase for classes annotated with @PolicyRemediator
 * and extracts the annotation values.
 */
public class PolicyRemediatorScanner {

	// Pattern to match @PolicyRemediator annotation with its value
	private static final Pattern ANNOTATION_PATTERN =
			Pattern.compile("@PolicyRemediator\\s*\\(\\s*\"([^\"]*)\"\\s*\\)");

	// Pattern to match class declaration
	private static final Pattern CLASS_PATTERN =
			Pattern.compile("public\\s+class\\s+(\\w+)");

	private static final String CSV_HEADER = "ClassFullName,ClassName,AnnotationValue\n";
	private static final String CSV_FILE_PATH = "policy_remediators.csv";
	private static final String BASE_DIR = "/Users/<USER>/Documents/Development/private/syrix/syrix";

	/**
	 * Main method to execute the scanning and CSV generation
	 */
	public static void main(String[] args) {
		try {
			List<RemediatorInfo> remediators = scanForRemediators();
			generateCsvFile(remediators, CSV_FILE_PATH);
			System.out.println("CSV file generated successfully at: " + new File(CSV_FILE_PATH).getAbsolutePath());
			System.out.println("Found " + remediators.size() + " policy remediator classes.");
		} catch (IOException e) {
			System.err.println("Error scanning codebase: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * Scan the codebase to find all classes with @PolicyRemediator annotation
	 */
	private static List<RemediatorInfo> scanForRemediators() throws IOException {
		List<RemediatorInfo> results = new ArrayList<>();

		// Get all Java files in the codebase
		List<Path> javaFiles = findAllJavaFiles(BASE_DIR);
		System.out.println("Found " + javaFiles.size() + " Java files to scan.");

		for (Path javaFile : javaFiles) {
			String content = Files.readString(javaFile);

			// Search for the @PolicyRemediator annotation
			Matcher annotationMatcher = ANNOTATION_PATTERN.matcher(content);
			if (annotationMatcher.find()) {
				String packageName = extractPackageName(content);
				String annotationValue = annotationMatcher.group(1);

				// Find the class name
				Matcher classMatcher = CLASS_PATTERN.matcher(content);
				if (classMatcher.find()) {
					String className = classMatcher.group(1);
					String fullClassName = packageName + "." + className;

					results.add(new RemediatorInfo(fullClassName, className, annotationValue));
					System.out.println("Found remediator: " + fullClassName + " with value: " + annotationValue);
				}
			}
		}

		return results;
	}

	/**
	 * Extract the package name from the Java file content
	 */
	private static String extractPackageName(String content) {
		Pattern packagePattern = Pattern.compile("package\\s+([\\w.]+);");
		Matcher matcher = packagePattern.matcher(content);
		if (matcher.find()) {
			return matcher.group(1);
		}
		return "";
	}

	/**
	 * Find all Java files in the given directory and its subdirectories
	 */
	private static List<Path> findAllJavaFiles(String baseDir) throws IOException {
		try (Stream<Path> walk = Files.walk(Paths.get(baseDir))) {
			return walk
					.filter(Files::isRegularFile)
					.filter(p -> p.toString().endsWith(".java"))
					.collect(Collectors.toList());
		}
	}

	/**
	 * Generate a CSV file with the remediator information
	 */
	private static void generateCsvFile(List<RemediatorInfo> remediators, String filePath) throws IOException {
		try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
			writer.write(CSV_HEADER);

			for (RemediatorInfo info : remediators) {
				String line = String.format("%s,%s,%s\n",
						info.getFullClassName(),
						info.getClassName(),
						info.getAnnotationValue());
				writer.write(line);
			}
		}
	}

	/**
	 * Class to hold information about a remediator
	 */
	private static class RemediatorInfo {
		private final String fullClassName;
		private final String className;
		private final String annotationValue;

		public RemediatorInfo(String fullClassName, String className, String annotationValue) {
			this.fullClassName = fullClassName;
			this.className = className;
			this.annotationValue = annotationValue;
		}

		public String getFullClassName() {
			return fullClassName;
		}

		public String getClassName() {
			return className;
		}

		public String getAnnotationValue() {
			return annotationValue;
		}
	}
}