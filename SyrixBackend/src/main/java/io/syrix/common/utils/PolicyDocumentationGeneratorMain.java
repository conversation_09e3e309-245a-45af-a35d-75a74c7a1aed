package io.syrix.common.utils;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.util.List;

/**
 * Standalone main class for generating policy remediator documentation.
 * This class is separate from production code and is intended for development/documentation purposes only.
 */
public class PolicyDocumentationGeneratorMain {
    private static final Logger logger = LoggerFactory.getLogger(PolicyDocumentationGeneratorMain.class);
    /**
     * Returns the list of parameter classes that should be excluded from documentation.
     * These classes are typically implementation details that users don't need to know about.
     * Both the classes themselves and their parameter names will be completely excluded from the generated documentation.
     *
     * @return List of fully qualified class names that should be excluded
     */
    private static List<String> getExcludedParameterClasses() {
        return List.of(
                // Client implementations (these are provided by the system)
                "io.syrix.protocols.client.MicrosoftGraphClient",
                "io.syrix.protocols.client.PowerShellTeamsClient",
                "io.syrix.protocols.client.PowerShellClient",
                // Add other classes that should be excluded
                "com.fasterxml.jackson.databind.ObjectMapper",
                "io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService",
                "io.syrix.common.dns.DnsService",
                "com.azure.core.credential.AzureSasCredential",
                "io.syrix.products.microsoft.base.SkuProcessingResult",
                "com.fasterxml.jackson.databind.node.ObjectNode"
        );
    }


    /**
     * Main method to generate comprehensive remediator documentation for LLM usage.
     * 
     * @param args Command-line arguments:
     *             args[0] - (optional) output directory (default: "docs")
     *             args[1] - (optional) format: "md" or "json" (default: "md")
     */
    public static void main(String[] args) {
        try {
            // Parse command-line arguments
            String outputDir = args.length > 0 ? args[0] : "docs";
            String format = args.length > 1 ? args[1] : "md";
            
            logger.info("Starting policy remediator documentation generation");
            logger.info("Output directory: {}", outputDir);
            logger.info("Documentation format: {}", format);

            // Get list of parameter classes to exclude
            List<String> excludedParameterClasses = getExcludedParameterClasses();
            logger.info("Excluding {} parameter classes from documentation", excludedParameterClasses.size());
            
            // Generate the documentation
            int count = PolicyRemediatorDocumentationGenerator.generateAllDocumentation(outputDir, format, excludedParameterClasses);
            
            logger.info("Documentation generation complete!");
            String outputFile = "md".equalsIgnoreCase(format) ? "all_policy_remediators.md" : "all_policy_remediators.json";
            logger.info("Generated comprehensive documentation file: {}/{}", outputDir, outputFile);
            logger.info("Documentation includes parameter types only, parameter names and class fields are excluded for better security");
            
            if ("md".equalsIgnoreCase(format)) {
                logger.info("LLM prompt templates available at: {}/llm_prompt_templates.md", outputDir);
            }
            
            // Print usage instructions
            printUsageInstructions(outputDir, format);
            
        } catch (Exception e) {
            logger.error("Error generating documentation:", e);
            System.err.println("Documentation generation failed: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * Prints usage instructions for the generated documentation.
     * 
     * @param outputDir Output directory
     * @param format Format used (md or json)
     */
    private static void printUsageInstructions(String outputDir, String format) {
        System.out.println("\n=== DOCUMENTATION GENERATION COMPLETE ===");
        System.out.println("\nHOW TO USE THE GENERATED DOCUMENTATION WITH AN LLM:\n");
        
        String outputFile = "md".equalsIgnoreCase(format) ? "all_policy_remediators.md" : "all_policy_remediators.json";
        System.out.println("1. Generated comprehensive documentation: " + outputDir + "/" + outputFile);
        
        if ("md".equalsIgnoreCase(format)) {
            System.out.println("2. Open " + outputDir + "/llm_prompt_templates.md for LLM instructions");
        } else {
            System.out.println("2. Example system prompt for your LLM:\n");
            System.out.println("You are a Microsoft 365 security assistant helping users understand policy remediations.");
            System.out.println("Your task is to create clear, concise instructions for users based on the provided remediator documentation...");
            System.out.println("\n(For the complete system prompt see the README file)");
        }
        
        System.out.println("\nTYPICAL WORKFLOW:\n");
        System.out.println("1. Use the system prompt from the template with your chosen LLM");
        System.out.println("2. Use the entire documentation file, or extract specific policy sections as needed");
        System.out.println("3. The LLM will generate user-friendly instructions for the policies");
        System.out.println("\n==============================================\n");
    }
}
