package io.syrix.common.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Enhanced version of PolicyRemediatorInfo that includes detailed information about
 * constructor parameters, field details, and other metadata needed for LLM-based documentation.
 */
public class EnhancedPolicyRemediatorInfo {
    private String policyId;
    private String className;
    private String description;
    private String microsoftService;
    private List<String> policyIds;
    private List<ParameterInfo> parameters;
    private List<FieldInfo> fields;
    private List<String> constantsUsed;
    private List<String> possibleResults;
    private String purpose;
    
    /**
     * Creates a new EnhancedPolicyRemediatorInfo instance.
     * 
     * @param policyId The policy identifier
     * @param className The fully qualified class name of the remediator
     * @param description Brief description of what the remediator does
     * @param microsoftService The Microsoft service this remediator targets (e.g., "Entra ID", "Exchange Online")
     * @param policyIds List of all policy IDs this remediator can handle (from annotation)
     * @param parameters Detailed information about constructor parameters
     * @param fields Important fields in the class that may be configured
     * @param constantsUsed Constants used or referenced by this remediator
     * @param possibleResults Possible remediation result types
     * @param purpose The purpose and expected business outcome of the remediation
     */
    public EnhancedPolicyRemediatorInfo(
            String policyId, 
            String className, 
            String description,
            String microsoftService,
            List<String> policyIds,
            List<ParameterInfo> parameters,
            List<FieldInfo> fields,
            List<String> constantsUsed,
            List<String> possibleResults,
            String purpose) {
        this.policyId = policyId;
        this.className = className;
        this.description = description;
        this.microsoftService = microsoftService;
        this.policyIds = policyIds;
        this.parameters = parameters;
        this.fields = fields;
        this.constantsUsed = constantsUsed;
        this.possibleResults = possibleResults;
        this.purpose = purpose;
    }

    public String getPolicyId() {
        return policyId;
    }

    public String getClassName() {
        return className;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getMicrosoftService() {
        return microsoftService;
    }
    
    public List<String> getPolicyIds() {
        return policyIds;
    }

    public List<ParameterInfo> getParameters() {
        return parameters;
    }
    
    @JsonIgnore
    public List<FieldInfo> getFields() {
        return fields;
    }
    
    public List<String> getConstantsUsed() {
        return constantsUsed;
    }
    
    public List<String> getPossibleResults() {
        return possibleResults;
    }
    
    public String getPurpose() {
        return purpose;
    }
    
    /**
     * Detailed information about constructor parameters.
     */
    public static class ParameterInfo {
        private String name;
        private String type;
        private String parameterName; // Parameter name in the constructor
        private String description;
        private List<FieldInfo> fields;
        private String purpose;
        private boolean isRequired;
        
        public ParameterInfo(String name, String type, String parameterName, String description, List<FieldInfo> fields, String purpose, boolean isRequired) {
            this.name = name;
            this.type = type;
            this.parameterName = parameterName;
            this.description = description;
            this.fields = fields;
            this.purpose = purpose;
            this.isRequired = isRequired;
        }
        
        @JsonIgnore
        public String getName() {
            return name;
        }
        
        public String getType() {
            return type;
        }
        
        @JsonIgnore
        public String getParameterName() {
            return parameterName;
        }
        
        public String getDescription() {
            return description;
        }
        
        public List<FieldInfo> getFields() {
            return fields;
        }
        
        public String getPurpose() {
            return purpose;
        }
        
        public boolean isRequired() {
            return isRequired;
        }
    }
    
    /**
     * Information about important fields in parameter classes or the remediator itself.
     */
    public static class FieldInfo {
        private String name;
        private String type;
        private String description;
        private Object defaultValue;
        private boolean isFinal;
        private List<String> possibleValues;
        
        /**
         * Creates a new FieldInfo with all required parameters.
         *
         * @param name Field name
         * @param type Field type
         * @param description Field description
         * @param defaultValue Default value for the field
         * @param isFinal Whether the field is final
         * @param possibleValues Possible values for the field
         */
        public FieldInfo(String name, String type, String description, Object defaultValue, boolean isFinal, List<String> possibleValues) {
            this.name = name;
            this.type = type;
            this.description = description;
            this.defaultValue = defaultValue;
            this.isFinal = isFinal;
            this.possibleValues = possibleValues;
        }
        
        /**
         * Default constructor for FieldInfo.
         * This is added for backward compatibility with code that might create instances without parameters.
         */
        public FieldInfo() {
            this.name = "";
            this.type = "";
            this.description = "";
            this.defaultValue = null;
            this.isFinal = false;
            this.possibleValues = new ArrayList<>();
        }
        
        public String getName() {
            return name;
        }
        
        /**
         * Sets the field name.
         *
         * @param name The field name
         */
        public void setName(String name) {
            this.name = name;
        }
        
        public String getType() {
            return type;
        }
        
        /**
         * Sets the field type.
         *
         * @param type The field type
         */
        public void setType(String type) {
            this.type = type;
        }
        
        public String getDescription() {
            return description;
        }
        
        /**
         * Sets the field description.
         *
         * @param description The field description
         */
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Object getDefaultValue() {
            return defaultValue;
        }
        
        /**
         * Sets the default value for the field.
         *
         * @param defaultValue The default value
         */
        public void setDefaultValue(Object defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public boolean isFinal() {
            return isFinal;
        }
        
        /**
         * Sets whether the field is final.
         *
         * @param isFinal Whether the field is final
         */
        public void setFinal(boolean isFinal) {
            this.isFinal = isFinal;
        }
        
        public List<String> getPossibleValues() {
            return possibleValues;
        }
        
        /**
         * Sets the possible values for the field.
         *
         * @param possibleValues The possible values
         */
        public void setPossibleValues(List<String> possibleValues) {
            this.possibleValues = possibleValues;
        }
    }
    
    /**
     * Utility method to extract Microsoft service name from the class name.
     * 
     * @param className The remediator class name
     * @return The Microsoft service name
     */
    public static String extractMicrosoftService(String className) {
        if (className.contains("Entra")) {
            return "Entra ID";
        } else if (className.contains("Exchange") || className.contains("Exo")) {
            return "Exchange Online";
        } else if (className.contains("SharePoint") || className.contains("SP")) {
            return "SharePoint";
        } else if (className.contains("Teams")) {
            return "Teams";
        } else if (className.contains("Defender")) {
            return "Defender";
        } else {
            return "Microsoft 365";
        }
    }
    
    /**
     * Utility method to extract policy description from JavaDoc or class name.
     * 
     * @param clazz The remediator class
     * @return Description of what the remediator does
     */
    public static String extractDescription(Class<?> clazz) {
        // Try to get from JavaDoc comment
        String simpleName = clazz.getSimpleName().replace("Remediator", "");
        return "Remediates " + splitCamelCase(simpleName) + " policies.";
    }
    
    /**
     * Utility method to split camel case into readable text.
     * 
     * @param s The camel case string
     * @return Human-readable string
     */
    private static String splitCamelCase(String s) {
        return s.replaceAll(
                String.format("%s|%s|%s",
                        "(?<=[A-Z])(?=[A-Z][a-z])",
                        "(?<=[^A-Z])(?=[A-Z])",
                        "(?<=[A-Za-z])(?=[^A-Za-z])"),
                " ");
    }
    
    /**
     * Utility method to extract purpose from class or its behavior.
     * 
     * @param clazz The remediator class
     * @return The business purpose of the remediator
     */
    public static String extractPurpose(Class<?> clazz) {
        // Use class name to infer purpose
        String name = clazz.getSimpleName();
        
        if (name.contains("AntiPhishing")) {
            return "Protects against phishing and impersonation attacks in email communications.";
        } else if (name.contains("Audit")) { 
            return "Enables and configures auditing for security and compliance monitoring.";
        } else if (name.contains("Sharing")) {
            return "Controls and restricts sharing of resources with external parties.";
        } else if (name.contains("MFA") || name.contains("Auth")) {
            return "Strengthens authentication security with multi-factor requirements or restrictions.";
        } else if (name.contains("Guest")) {
            return "Manages guest user access controls and restrictions.";
        } else if (name.contains("Mail")) {
            return "Configures email security and compliance settings.";
        } else if (name.contains("Domain")) {
            return "Manages domain configurations and restrictions.";
        } else if (name.contains("Risk")) {
            return "Addresses security risks with appropriate controls and mitigations.";
        } else {
            return "Implements security controls and policy configurations.";
        }
    }
    
    /**
     * Utility method to extract parameter information for a parameter class.
     * 
     * @param paramClass The parameter class
     * @return List of field info for the parameter class
     */
    public static List<FieldInfo> extractParameterFields(Class<?> paramClass) {
        if (paramClass == null) {
            return new ArrayList<>();
        }
        
        List<FieldInfo> fields = new ArrayList<>();
        for (Field field : paramClass.getDeclaredFields()) {
            // Skip static, synthetic, and internal fields
            if (Modifier.isStatic(field.getModifiers()) || field.isSynthetic() 
                    || field.getName().startsWith("$") || field.getName().startsWith("this$")) {
                continue;
            }
            
            String name = field.getName();
            String type = field.getType().getSimpleName();
            String description = inferFieldDescription(name, type);
            boolean isFinal = Modifier.isFinal(field.getModifiers());
            List<String> possibleValues = inferPossibleValues(field.getType());
            
            fields.add(new FieldInfo(name, type, description, null, isFinal, possibleValues));
        }
        
        return fields;
    }
    
    /**
     * Infers a description for a field based on its name and type.
     * 
     * @param name Field name
     * @param type Field type
     * @return Inferred description
     */
    private static String inferFieldDescription(String name, String type) {
        if (name.contains("enabled") || name.startsWith("is") || name.startsWith("enable")) {
            return "Controls whether this feature is enabled.";
        } else if (name.contains("action")) {
            return "Specifies the action to take when a condition is met.";
        } else if (name.contains("level") || name.contains("threshold")) {
            return "Specifies the level or threshold for this setting.";
        } else if (name.contains("domain")) {
            return "Specifies the domain(s) affected by this setting.";
        } else if (name.contains("policy") || name.contains("config")) {
            return "References the policy or configuration to use.";
        } else if (name.contains("user")) {
            return "Specifies the user(s) affected by this setting.";
        } else if (name.contains("url") || name.contains("Uri")) {
            return "Specifies the URL or URI for this resource.";
        } else if (name.contains("client")) {
            return "The client used to communicate with the service.";
        } else if (type.contains("List") || type.contains("Array") || type.contains("Collection")) {
            return "A collection of " + name + " elements.";
        } else if (type.equals("String")) {
            return "Text value for " + name.replaceAll("([A-Z])", " $1").toLowerCase() + ".";
        } else if (type.equals("boolean") || type.equals("Boolean")) {
            return "Flag indicating whether " + name.replaceAll("([A-Z])", " $1").toLowerCase() + ".";
        } else if (type.equals("int") || type.equals("Integer") || type.equals("long") || type.equals("Long")) {
            return "Numeric value for " + name.replaceAll("([A-Z])", " $1").toLowerCase() + ".";
        } else {
            return "Configuration for " + name.replaceAll("([A-Z])", " $1").toLowerCase() + ".";
        }
    }
    
    /**
     * Infers possible values for a field based on its type.
     * 
     * @param type Field type
     * @return List of possible values or empty list
     */
    private static List<String> inferPossibleValues(Class<?> type) {
        if (type.isEnum()) {
            // Extract both enum names and their string representations
            List<String> values = new ArrayList<>();
            Object[] enumConstants = type.getEnumConstants();
            
            for (Object enumConstant : enumConstants) {
                Enum<?> enumValue = (Enum<?>) enumConstant;
                String enumName = enumValue.name();
                
                // Try to extract human-readable value if available through toString()
                String displayValue = enumValue.toString();
                if (!displayValue.equals(enumName)) {
                    values.add(enumName + " (" + displayValue + ")");
                } else {
                    // If no custom toString, look for constants or fields that might contain readable values
                    try {
                        Field[] fields = type.getDeclaredFields();
                        for (Field field : fields) {
                            if (Modifier.isStatic(field.getModifiers()) && 
                                Modifier.isFinal(field.getModifiers()) && 
                                field.getName().contains(enumName)) {
                                field.setAccessible(true);
                                Object value = field.get(null);
                                if (value != null && !value.toString().equals(enumName)) {
                                    values.add(enumName + " (" + value + ")");
                                    break;
                                }
                            }
                        }
                        // If we couldn't find a readable value, just use the enum name
                        if (!values.contains(enumName) && !values.contains(enumName + " (" + displayValue + ")")) {
                            values.add(enumName);
                        }
                    } catch (Exception e) {
                        // If we encounter any issues, fallback to just using the enum name
                        values.add(enumName);
                    }
                }
            }
            
            return values;
        } else if (type == boolean.class || type == Boolean.class) {
            return Arrays.asList("true", "false");
        } else {
            return new ArrayList<>();
        }
    }
}
