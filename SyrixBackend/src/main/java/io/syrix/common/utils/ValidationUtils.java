package io.syrix.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class ValidationUtils {

    public static void isCondition(boolean condition, String msg) {
        if (condition) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static class Strings {
        public static void requireNonBlank(String value, String msg) {
            isCondition(isBlank(value), msg);
        }

        public static boolean isBlank(String value) {
            return StringUtils.isEmpty(StringUtils.trimToEmpty(value));
        }
    }

    public static class Maps {
        public static void requireNonEmpty(@SuppressWarnings("rawtypes") Map collection, String msg) {
            isCondition(collection == null || collection.isEmpty(), msg);
        }
    }

    public static class Objects {
        public static void requireNonNull(Object obj, String msg) {
            isCondition(obj == null, msg);
        }
    }

}
