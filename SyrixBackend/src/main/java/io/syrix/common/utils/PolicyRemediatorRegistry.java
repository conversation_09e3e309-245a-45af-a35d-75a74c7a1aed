package io.syrix.common.utils;

import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.products.microsoft.base.PolicyRemediator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import io.syrix.products.microsoft.base.RemediationResult;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class PolicyRemediatorRegistry {
	private static final Logger logger = LoggerFactory.getLogger(PolicyRemediatorRegistry.class);
	private static final String BASE_PACKAGE = "io.syrix";

	private final Map<String, Class<?>> annotationIndex = new HashMap<>();

	private static final class InstanceHolder {
		private static final PolicyRemediatorRegistry instance = new PolicyRemediatorRegistry(BASE_PACKAGE);
	}

	//------ public part
	public static PolicyRemediatorRegistry getInstance() {
		return InstanceHolder.instance;
	}

	@SuppressWarnings("unused")
	public static Collection<Class<?>> getAllClasses() {
		return Collections.unmodifiableCollection(getInstance().annotationIndex.values());
	}

	public static Class<?> get(String policyId) {
		return getInstance().annotationIndex.get(policyId);
	}

	@SuppressWarnings("unchecked")
	public static <T> T getPolicyRemediator(String policyId, Object... constructorParams) {
		try {
			@SuppressWarnings("unchecked")
			Class<? extends T> clazz = (Class<? extends T>) get(policyId);
			if (clazz == null) {
				throw new SyrixRuntimeException("Not found PolicyRemediator: " + policyId);
			}

			Class<?>[] paramTypes = Arrays.stream(constructorParams)
					.map(Object::getClass)
					.toArray(Class<?>[]::new);

			Constructor<? extends T> constructor = clazz.getConstructor(paramTypes);
			return constructor.newInstance(constructorParams);
		} catch (NoSuchMethodException e) {
			throw new SyrixRuntimeException("Constructor not found for policy: " + policyId, e);
		} catch (Exception ex) {
			throw new SyrixRuntimeException("Failed to create remediator for: " + policyId, ex);
		}
	}

//------ private part
	private PolicyRemediatorRegistry(String basePackage) {
		scanAndIndex(basePackage);
	}

	private void scanAndIndex(String basePackage) {
		String path = basePackage.replace('.', '/');
		ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

		try {
			Enumeration<URL> resources = classLoader.getResources(path);
			while (resources.hasMoreElements()) {
				URL resource = resources.nextElement();
				String protocol = resource.getProtocol();

				if ("file".equals(protocol)) {
					File directory = new File(URLDecoder.decode(resource.getFile(), StandardCharsets.UTF_8));
					if (directory.exists()) {
						findInDirectory(directory, basePackage);
					}
				} else if ("jar".equals(protocol)) {
					String jarPath = resource.getPath().substring(5, resource.getPath().indexOf("!"));
					try (JarFile jarFile = new JarFile(URLDecoder.decode(jarPath, StandardCharsets.UTF_8))) {
						Enumeration<JarEntry> entries = jarFile.entries();
						while (entries.hasMoreElements()) {
							JarEntry entry = entries.nextElement();
							String name = entry.getName();
							if (name.startsWith(path) && name.endsWith(".class")) {
								String className = name.replace('/', '.').replace(".class", "");
								loadAndIndexClass(className);
							}
						}
					}
				}
			}
		} catch (IOException e) {
			throw new SyrixRuntimeException("Can not scan PolicyRemediators", e);
		}
	}

	private void findInDirectory(File directory, String packageName) {
		for (File file : Objects.requireNonNull(directory.listFiles())) {
			if (file.isDirectory()) {
				findInDirectory(file, packageName + "." + file.getName());
			} else if (file.getName().endsWith(".class")) {
				String className = packageName + '.' + file.getName().replace(".class", "");
				loadAndIndexClass(className);
			}
		}
	}

	private void loadAndIndexClass(String className) {
		try {
			Class<?> clazz = Class.forName(className);
			if (clazz.isAnnotationPresent(PolicyRemediator.class)) {
				PolicyRemediator annotation = clazz.getAnnotation(PolicyRemediator.class);
				if (annotation != null) {
					String value = annotation.value();
					if (annotationIndex.containsKey(value)) {
						logger.error("Found duplicate PolicyRemediator PolicyId:{}, class1: {}, class2: {} will use class: {}",
								value, annotationIndex.get(value).getName(), className, annotationIndex.get(value).getName()
						);

					} else {
						annotationIndex.put(value, clazz);
					}
				}
			}
		} catch (Throwable ignored) {}
	}

	public static List<PolicyRemediatorInfo> getPolicyRemediatorInfos(List<String> excludedParameterClasses, boolean includeEmpty) {
		PolicyRemediatorRegistry registry = getInstance();
		List<PolicyRemediatorInfo> infos = new ArrayList<>();
		for (Map.Entry<String, Class<?>> entry : registry.annotationIndex.entrySet()) {
			String policyId = entry.getKey();
			Class<?> clazz = entry.getValue();
			List<String> parameters = new ArrayList<>();

			Constructor<?>[] constructors = clazz.getConstructors();
			if(constructors.length > 0) {
				for (Class<?> paramType : constructors[0].getParameterTypes()) {
					String paramName = paramType.getName();
					// Only add if not in excluded parameter classes
					if (CollectionUtils.isEmpty(excludedParameterClasses) || !excludedParameterClasses.contains(paramName)) {
						parameters.add(paramName);
					}
				}
			}
			if(includeEmpty) {
				infos.add(new PolicyRemediatorInfo(policyId, clazz.getName(), parameters));
			} else if (!CollectionUtils.isEmpty(parameters)) {
				infos.add(new PolicyRemediatorInfo(policyId, clazz.getName(), parameters));
			}
		}
		return infos;
	}
	
	/**
	 * Extra processing for special case remediators like MS.DEFENDER.1.1v1 with PolicyType enums.
	 * This ensures special cases have their enum values properly extracted.
	 * 
	 * @param infos List of remediator infos to process
	 */
	private static void processSpecialCaseRemediators(List<EnhancedPolicyRemediatorInfo> infos) {
		// Special handling for MS.DEFENDER.1.1v1 with PolicyType enum
		for (EnhancedPolicyRemediatorInfo info : infos) {
			if (info.getPolicyId().equals("MS.DEFENDER.1.1v1") && 
				info.getClassName().contains("DefenderPresetSecurityPoliciesRemediator")) {
				
				// Look for PolicyType parameter
				for (EnhancedPolicyRemediatorInfo.ParameterInfo param : info.getParameters()) {
					if (param.getType().equals("PolicyType")) {
						// If we don't have field values yet, add them manually
						if (param.getFields().isEmpty()) {
							List<EnhancedPolicyRemediatorInfo.FieldInfo> fields = new ArrayList<>();
							
							// Create a field info with the enum values
							List<String> possibleValues = Arrays.asList(
								"STANDARD (Standard)", 
								"STRICT (Strict)"
							);
							
							fields.add(new EnhancedPolicyRemediatorInfo.FieldInfo(
								"policyType",
								"Enum",
								"Policy type selection",
								null,
								false,
								possibleValues
							));
							
							// Add the field info
							try {
								// Use reflection to set the fields
								java.lang.reflect.Field fieldsField = EnhancedPolicyRemediatorInfo.ParameterInfo.class.getDeclaredField("fields");
								fieldsField.setAccessible(true);
								fieldsField.set(param, fields);
							} catch (Exception e) {
								logger.warn("Could not set fields for PolicyType parameter in MS.DEFENDER.1.1v1", e);
							}
						}
					}
				}
			}
		}
	}
	
	/**
	 * Returns a list of enhanced remediator information objects suitable for LLM-based documentation generation.
	 * This method provides comprehensive details about each remediator including parameter field information,
	 * purpose descriptions, and other metadata needed for generating detailed documentation.
	 * Note: While class fields are collected, they are not included in the generated documentation.
	 *
	 * @param excludedParameterClasses List of parameter class names to exclude from the documentation
	 * @param includeEmpty Whether to include remediators with no parameters
	 * @return List of EnhancedPolicyRemediatorInfo objects
	 */
	public static List<EnhancedPolicyRemediatorInfo> getEnhancedPolicyRemediatorInfos(List<String> excludedParameterClasses, boolean includeEmpty) {
		PolicyRemediatorRegistry registry = getInstance();
		List<EnhancedPolicyRemediatorInfo> infos = new ArrayList<>();
		
		for (Map.Entry<String, Class<?>> entry : registry.annotationIndex.entrySet()) {
			String policyId = entry.getKey();
			Class<?> clazz = entry.getValue();
			
			// Extract policy IDs from the annotation
			List<String> policyIds = Arrays.asList(policyId.split("\\s*,\\s*"));
			
			// Get other metadata using utility methods
			String description = EnhancedPolicyRemediatorInfo.extractDescription(clazz);
			String microsoftService = EnhancedPolicyRemediatorInfo.extractMicrosoftService(clazz.getName());
			String purpose = EnhancedPolicyRemediatorInfo.extractPurpose(clazz);
			
			// Get default possible results from RemediationResult enum
			List<String> possibleResults = Arrays.stream(RemediationResult.values())
				.map(Enum::name)
				.collect(Collectors.toList());
			
			// Process constructor parameters for detailed information
			List<EnhancedPolicyRemediatorInfo.ParameterInfo> parameterInfos = new ArrayList<>();
			Constructor<?>[] constructors = clazz.getConstructors();
			if (constructors.length > 0) {
				Constructor<?> mainConstructor = constructors[0];
				Class<?>[] paramTypes = mainConstructor.getParameterTypes();
				java.lang.reflect.Parameter[] params = mainConstructor.getParameters();
				
				for (int i = 0; i < paramTypes.length; i++) {
					Class<?> paramType = paramTypes[i];
					String paramName = params[i].getName();
					String paramTypeName = paramType.getName();
					
					// Skip excluded parameter classes - complete filtering
					if (!CollectionUtils.isEmpty(excludedParameterClasses) && excludedParameterClasses.contains(paramTypeName)) {
						continue;
					}
					
					// Extract parameter fields for complex types
					List<EnhancedPolicyRemediatorInfo.FieldInfo> paramFields = EnhancedPolicyRemediatorInfo.extractParameterFields(paramType);
					
					// Infer parameter description and purpose
					String paramDescription = "Parameter of type " + paramType.getSimpleName();
					String paramPurpose = inferParameterPurpose(paramName, paramType.getSimpleName());
					
					// Determine if parameter is required 
					boolean isRequired = true; // Assume all constructor parameters are required
					
					parameterInfos.add(new EnhancedPolicyRemediatorInfo.ParameterInfo(
					paramName, 
					paramType.getSimpleName(), 
					params[i].getName(), // Parameter name
					paramDescription, 
					paramFields, 
					paramPurpose, 
					 isRequired
						));
				}
			}
			
			// Extract important class fields
			List<EnhancedPolicyRemediatorInfo.FieldInfo> classFields = EnhancedPolicyRemediatorInfo.extractParameterFields(clazz);
			
			// Extract constants used - scan for public static final fields
			List<String> constantsUsed = new ArrayList<>();
			for (Field field : clazz.getDeclaredFields()) {
				int modifiers = field.getModifiers();
				if (Modifier.isPublic(modifiers) && Modifier.isStatic(modifiers) && Modifier.isFinal(modifiers)) {
					constantsUsed.add(field.getName());
				}
			}
			
			// Create and add the enhanced info object
			if (includeEmpty || !parameterInfos.isEmpty()) {
				infos.add(new EnhancedPolicyRemediatorInfo(
					policyId,
					clazz.getName(),
					description,
					microsoftService,
					policyIds,
					parameterInfos,
					classFields,
					constantsUsed,
					possibleResults,
					purpose
				));
			}
		}
		
		// Process special cases (like MS.DEFENDER.1.1v1) to ensure enum values are properly extracted
		processSpecialCaseRemediators(infos);
		
		return infos;
	}
	
	/**
	 * Infers the purpose of a parameter based on its name and type.
	 * 
	 * @param paramName Parameter name
	 * @param paramType Parameter type
	 * @return Inferred purpose description
	 */
	private static String inferParameterPurpose(String paramName, String paramType) {
		if (paramName.contains("client") || paramType.contains("Client")) {
			return "Provides API access to the Microsoft service.";
		} else if (paramName.contains("config") || paramType.contains("Configuration")) {
			return "Provides configuration settings for the remediation.";
		} else if (paramName.contains("domain") || paramType.contains("Domain")) {
			return "Specifies the domain(s) for which the remediation applies.";
		} else if (paramName.contains("tenant") || paramType.contains("Tenant")) {
			return "Provides tenant information for the remediation.";
		} else if (paramName.contains("user") || paramType.contains("User")) {
			return "Provides user information for the remediation.";
		} else if (paramName.contains("service") || paramType.contains("Service")) {
			return "Provides service access for the remediation.";
		} else {
			return "Required for remediation functionality.";
		}
	}
}
