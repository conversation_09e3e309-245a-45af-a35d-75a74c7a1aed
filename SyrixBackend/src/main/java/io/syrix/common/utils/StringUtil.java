package io.syrix.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {
	// Regex to find JSON wrapped in markdown code blocks
	public static Pattern MD_JSON_PATTERN = Pattern.compile("```(?:json)?\\s*\\n?(.*?)\\n?\\s*```", Pattern.DOTALL);
	/**
	 * Removes double quotes from the beginning and end of a string if they exist.
	 *
	 * @param str The input string.
	 * @return The string with leading and trailing double quotes removed, or the
	 *         original string if quotes are not present, or null if the input
	 *         is null.
	 */
	public static String removeQuotes(String str) {
		if (str == null) {
			return null;
		}
		if (str.startsWith("\"") && str.endsWith("\"")) {
			return str.substring(1, str.length() - 1);
		} else {
			return str;
		}
	}

	public static String extractJson(String response) {
		if (response == null || response.isEmpty()) {
			return null; // Or throw an exception if appropriate
		}

		Matcher matcher = MD_JSON_PATTERN.matcher(response);

		if (matcher.find()) {
			// Successfully found the code block, return group 1 (the actual JSON)
			return matcher.group(1).trim();

		} else {
			//If no code block was found, we could try assuming the string is a plain JSON
			//However, it might be worth logging this or doing some error handling instead.
			return response.trim();
		}
	}
}
