package io.syrix.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Utils {
	/**
	 * Generate a hash from data
	 *
	 * @param data hash
	 * @return A hash suffix for use in alert names
	 */
	public static String generateMD5Hash(String data) throws NoSuchAlgorithmException {
		MessageDigest digest = MessageDigest.getInstance("MD5");
		byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));

		// Convert the first 8 bytes of the hash to a hex string for the suffix
		StringBuilder hexString = new StringBuilder();
		for (int i = 0; i < 8 && i < hash.length; i++) {
			String hex = Integer.toHexString(0xff & hash[i]);
			if (hex.length() == 1) {
				hexString.append('0');
			}
			hexString.append(hex);
		}
		return "-" + hexString.toString();
	}
}
