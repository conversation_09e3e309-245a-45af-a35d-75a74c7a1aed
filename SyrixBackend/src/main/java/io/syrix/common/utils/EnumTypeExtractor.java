package io.syrix.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Utility class for extracting enum values and display information from Java enum classes.
 * This helps generate accurate documentation for enum parameters in remediator classes.
 */
public class EnumTypeExtractor {
    // Cache for enum values to avoid repeated reflection
    private static final Map<String, List<String>> enumValuesCache = new HashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(EnumTypeExtractor.class);
    
    // Common patterns for display name fields in enums
    private static final List<String> DISPLAY_NAME_FIELD_PATTERNS = Arrays.asList(
            "displayName", "label", "description", "title", "name", "value"
    );
    
    /**
     * Extract possible values from an enum class with their display names.
     * Results are cached for better performance.
     * 
     * @param enumClassName Fully qualified name of the enum class
     * @return List of formatted enum values (e.g., "ENUM_NAME (Display Name)") or empty list if extraction fails
     */
    public static List<String> extractEnumValues(String enumClassName) {
        // Check cache first
        if (enumValuesCache.containsKey(enumClassName)) {
            return enumValuesCache.get(enumClassName);
        }
        List<String> result = new ArrayList<>();
        
        try {
            // Try to load the enum class
            Class<?> enumClass = Class.forName(enumClassName);
            
            if (!enumClass.isEnum()) {
                logger.warn("Class {} is not an enum", enumClassName);
                return result;
            }
            
            // Get all enum constants
            Object[] enumConstants = enumClass.getEnumConstants();
            
            // Try to find a display name field
            Optional<Field> displayField = findDisplayNameField(enumClass);
            
            // Process each enum constant
            for (Object enumConstant : enumConstants) {
                String enumName = ((Enum<?>) enumConstant).name();
                
                // Try to get display name from field
                String displayName = displayField.map(field -> {
                    try {
                        field.setAccessible(true);
                        Object value = field.get(enumConstant);
                        return value != null ? value.toString() : null;
                    } catch (Exception e) {
                        logger.debug("Could not access field {} for enum {}", field.getName(), enumName, e);
                        return null;
                    }
                }).orElse(null);
                
                // Format the result
                if (displayName != null && !displayName.isEmpty()) {
                    result.add(enumName + " (" + displayName + ")");
                } else {
                    // If no display name is found, just use the enum name
                    result.add(enumName);
                }
            }
            
            // Special case handling for specific enums
            if (enumClassName.endsWith("PolicyType") && result.size() == 2) {
                // Check for specific enum values
                boolean hasStandard = result.stream().anyMatch(s -> s.startsWith("STANDARD"));
                boolean hasStrict = result.stream().anyMatch(s -> s.startsWith("STRICT"));
                
                if (hasStandard && hasStrict) {
                    // Override with known values for better documentation
                    List<String> specialCaseValues = Arrays.asList("STANDARD (Standard)", "STRICT (Strict)");
                    // Update cache before returning
                    enumValuesCache.put(enumClassName, specialCaseValues);
                    return specialCaseValues;
                }
            }
            
        } catch (ClassNotFoundException e) {
            // Just log at debug level to avoid excessive warnings in logs
            logger.debug("Enum class {} not found", enumClassName);
        } catch (Exception e) {
            logger.warn("Error extracting enum values from {}", enumClassName, e);
        }
        
        // Cache the result for future use
        enumValuesCache.put(enumClassName, result);
        return result;
    }
    
    /**
     * Get enum values by short name, trying to find the enum class in common packages.
     * This is useful when only the enum name is known but not the fully qualified name.
     * 
     * @param enumShortName The short name of the enum (e.g., "PolicyType")
     * @param contextClassName A class name in the same package, used to help locate the enum
     * @return List of formatted enum values or empty list if not found
     */
    public static List<String> getEnumValuesByShortName(String enumShortName, String contextClassName) {
        try {
            // First try directly using the context class's package
            if (contextClassName != null && contextClassName.contains(".")) {
                String packageName = contextClassName.substring(0, contextClassName.lastIndexOf('.'));
                String fullClassName = packageName + "." + enumShortName;
                
                try {
                    List<String> values = extractEnumValues(fullClassName);
                    if (!values.isEmpty()) {
                        return values;
                    }
                } catch (Exception e) {
                    logger.debug("Could not find enum {} in package {}", enumShortName, packageName, e);
                }
            }
            
            // Try common packages where enums might be defined
            String[] commonPackages = {
                "io.syrix.products.microsoft.defender.remediation",
                "io.syrix.products.microsoft.defender",
                "io.syrix.products.microsoft.entra.remediation",
                "io.syrix.products.microsoft.entra",
                "io.syrix.products.microsoft.sharepoint.remediation",
                "io.syrix.products.microsoft.sharepoint",
                "io.syrix.products.microsoft.exo.remediation",
                "io.syrix.products.microsoft.exo",
                "io.syrix.products.microsoft.teams.remediation",
                "io.syrix.products.microsoft.teams",
                "io.syrix.products.microsoft.common",
                "io.syrix.common.enums",
                "io.syrix.common.models"
            };
            
            for (String pkg : commonPackages) {
                String fullClassName = pkg + "." + enumShortName;
                try {
                    List<String> values = extractEnumValues(fullClassName);
                    if (!values.isEmpty()) {
                        return values;
                    }
                } catch (Exception e) {
                    logger.debug("Could not find enum {} in package {}", enumShortName, pkg, e);
                }
            }
        } catch (Exception e) {
            logger.warn("Error in getEnumValuesByShortName for {} with context {}", enumShortName, contextClassName, e);
        }
        
        // If all attempts fail, return an empty list
        return new ArrayList<>();
    }
    
    /**
     * Try to find a field that might contain display names for the enum.
     * 
     * @param enumClass The enum class to inspect
     * @return Optional field that might contain display names
     */
    private static Optional<Field> findDisplayNameField(Class<?> enumClass) {
        // First look for fields with common display name patterns
        for (String pattern : DISPLAY_NAME_FIELD_PATTERNS) {
            for (Field field : enumClass.getDeclaredFields()) {
                if (field.getName().toLowerCase().contains(pattern.toLowerCase())) {
                    return Optional.of(field);
                }
            }
        }
        
        // Next, look for String fields that might contain display information
        List<Field> stringFields = Arrays.stream(enumClass.getDeclaredFields())
                .filter(field -> field.getType() == String.class)
                .collect(Collectors.toList());
        
        if (!stringFields.isEmpty()) {
            return Optional.of(stringFields.get(0));
        }
        
        return Optional.empty();
    }
    
    /**
     * Special helper method specifically for MS.DEFENDER.1.1v1 PolicyType.
     * This method returns the formatted enum values for the PolicyType enum used in DefenderPresetSecurityPoliciesRemediator.
     * 
     * @return List of formatted enum values for PolicyType
     */
    public static List<String> getDefenderPolicyTypeValues() {
        try {
            // Try to load the enum class
            Class<?> enumClass = Class.forName("io.syrix.products.microsoft.defender.remediation.PolicyType");
            if (!enumClass.isEnum()) {
                logger.warn("PolicyType class is not an enum");
                // Fallback to hardcoded values
                return Arrays.asList("STANDARD (Standard)", "STRICT (Strict)");
            }
            
            // Get enum constants
            Object[] enumConstants = enumClass.getEnumConstants();
            List<String> result = new ArrayList<>();
            
            // Process each enum constant
            for (Object enumConstant : enumConstants) {
                String enumName = ((Enum<?>) enumConstant).name();
                String displayName = null;
                
                // For STANDARD, the display name should be "Standard"
                if ("STANDARD".equals(enumName)) {
                    displayName = "Standard";
                }
                // For STRICT, the display name should be "Strict"
                else if ("STRICT".equals(enumName)) {
                    displayName = "Strict";
                }
                
                // Format the result
                if (displayName != null) {
                    result.add(enumName + " (" + displayName + ")");
                } else {
                    // If no display name is found, just use the enum name
                    result.add(enumName);
                }
            }
            
            return result;
        } catch (ClassNotFoundException e) {
            logger.warn("PolicyType enum class not found", e);
            // Fallback to hardcoded values
            return Arrays.asList("STANDARD (Standard)", "STRICT (Strict)");
        } catch (Exception e) {
            logger.warn("Error extracting PolicyType enum values", e);
            // Fallback to hardcoded values
            return Arrays.asList("STANDARD (Standard)", "STRICT (Strict)");
        }
    }
}
