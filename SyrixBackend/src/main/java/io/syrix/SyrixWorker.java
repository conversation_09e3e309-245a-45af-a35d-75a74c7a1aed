package io.syrix;

import io.syrix.common.logging.LoggingInitializer;
import io.quarkus.runtime.Quarkus;
import io.quarkus.runtime.QuarkusApplication;
import io.quarkus.runtime.annotations.QuarkusMain;
import io.syrix.main.Context;
import io.syrix.worker.config.CommandLineArgsProcessor;
import io.syrix.worker.queue.ActiveMQTaskConsumer;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Main Quarkus application class for Syrix Backend
 * Handles application startup and initialization of core components.
 * <p>
 * Required command line arguments:
 * args[0] - Certificate password
 * args[1] - Path to certificate file (.p12/.pfx)
 * args[2] - Path to configuration file (.yml/.yaml)
 * <p>
 * Example: java -jar syrix-backend.jar myPassword /path/to/cert.p12 /path/to/config.yml
 */
@QuarkusMain
public class SyrixWorker implements QuarkusApplication {
    private static final Logger logger = LoggerFactory.getLogger(SyrixWorker.class);

    private final ActiveMQTaskConsumer activeMQTaskConsumer;
    private final Context context; // Context will be initialized via ContextProducer

    @Inject
    public SyrixWorker(ActiveMQTaskConsumer activeMQTaskConsumer, Context context) {
        this.activeMQTaskConsumer = activeMQTaskConsumer;
        this.context = context;
    }

    static {
        // Initialize logging system BEFORE any other operations
        LoggingInitializer.initializeLogging();
    }

    public static void main(String... args) {
        logger.info("Starting Syrix Backend with Quarkus...");

        // Quick validation before Quarkus startup to provide better error messages
        if (args.length == 0) {
            System.err.println("""
                    
                    ERROR: No command line arguments provided.
                    
                    Usage: java -jar syrix-backend.jar <certPassword> <certPath> <configPath> [--disable-activemq]
                    
                    Required arguments:
                      <certPassword>  - Password for the certificate file
                      <certPath>      - Path to certificate file (.p12 or .pfx)
                      <configPath>    - Path to configuration file (.yml or .yaml)
                      
                    Optional arguments:
                      --disable-activemq  - Disable ActiveMQ task queue integration (default: enabled)
                    
                    Examples:
                      java -jar syrix-backend.jar myPassword /path/to/cert.p12 /path/to/config.yml
                      java -jar syrix-backend.jar myPassword /path/to/cert.p12 /path/to/config.yml --disable-activemq
                    """);
            System.exit(1);
        }

        // Store command line arguments for CDI initialization
        CommandLineArgsProcessor.setCommandLineArgs(args);

        Quarkus.run(SyrixWorker.class, args);
    }

    @Override
    public int run(String... args) throws Exception {
        logger.info("Syrix Backend application starting...");

        try {
            // Command line arguments are now processed automatically via CDI startup events
            // Context initialization also happens automatically via CDI startup events

            // Verify context is properly initialized
            if (context != null) {
                logger.info("Context initialized successfully from config.yaml");
                logger.info("Environment: {}", context.getEnvironment());
                logger.info("Output path: {}", context.getConfig().outputPath());
                logger.info("Certificate configured: {}", context.getCertPath() != null ? "Yes" : "No");
            } else {
                logger.error("Context initialization failed - application will terminate");
                return 1;
            }

            if (activeMQTaskConsumer != null && activeMQTaskConsumer.isEnabled()) {
                logger.info("ActiveMQ Task Consumer initialized: {}", activeMQTaskConsumer.isRunning() ? "OK" : "FAILED");
            } else {
                logger.info("ActiveMQ Task Consumer: DISABLED");
            }

            logAvailableEndpoints();

            logger.info("Syrix Backend application started successfully");

            Quarkus.waitForExit();
            return 0;

        } catch (IllegalStateException e) {
            // Context initialization failed - application is already shutting down
            logger.error("Application startup failed due to configuration issues: {}", e.getMessage());
            return 1;
        } catch (Exception e) {
            logger.error("Failed to start Syrix Backend application", e);
            return 1;
        }
    }

    /**
     * Log available REST endpoints
     */
    private void logAvailableEndpoints() {
        logger.info("Available endpoints:");
        logger.info("  - Health: GET /api/v1/health");
        logger.info("  - Info: GET /api/v1/health/info");
        logger.info("  - System Config: GET /api/v1/configuration/system");
        logger.info("  - M365 Config: GET /api/v1/configuration/m365");
        logger.info("  - M365 Async Config: GET /api/v1/m365/config/async");
        logger.info("  - M365 Validate: GET /api/v1/m365/validate");
    }
}
