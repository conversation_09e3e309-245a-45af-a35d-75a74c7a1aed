package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Configuration class that defines which users, groups, and roles should be included in or excluded from
 * a Conditional Access policy.
 */
public class ConditionalAccessUserConfiguration {

	private List<String> includeUsers;
	private List<String> excludeUsers;
	private List<String> includeGroups;
	private List<String> excludeGroups;
	private List<String> includeRoles;
	private List<String> excludeRoles;
	private JsonNode includeGuestsOrExternalUsers;
	private JsonNode excludeGuestsOrExternalUsers;

	// Special constants for "All" and "None"
	public static final String ALL_USERS = "All";
	public static final String ALL_GUESTS_OR_EXTERNAL_USERS = "GuestsOrExternalUsers";
	public static final String ALL_GUESTS = "Guests";
	public static final String ALL_EXTERNAL_USERS = "ExternalUsers";

	/**
	 * Default constructor creates an empty configuration
	 */
	public ConditionalAccessUserConfiguration() {
		this.includeUsers = new ArrayList<>();
		this.excludeUsers = new ArrayList<>();
		this.includeGroups = new ArrayList<>();
		this.excludeGroups = new ArrayList<>();
		this.includeRoles = new ArrayList<>();
		this.excludeRoles = new ArrayList<>();
		this.includeGuestsOrExternalUsers = null;
		this.excludeGuestsOrExternalUsers = null;
	}

	/**
	 * Static factory method to create a configuration that includes all users
	 * @return A configuration that includes all users
	 */
	public static ConditionalAccessUserConfiguration includeAllUsers() {
		ConditionalAccessUserConfiguration config = new ConditionalAccessUserConfiguration();
		config.includeUsers = Collections.singletonList(ALL_USERS);
		return config;
	}

	/**
	 * Static factory method to create a configuration that blocks all users except specified exclusions
	 * @param excludedUserIds List of user IDs to exclude from the policy
	 * @param excludedGroupIds List of group IDs to exclude from the policy
	 * @param excludedRoleIds List of role IDs to exclude from the policy
	 * @return A configuration that blocks all users except specified exclusions
	 */
	public static ConditionalAccessUserConfiguration blockAllExcept(
			List<String> excludedUserIds,
			List<String> excludedGroupIds,
			List<String> excludedRoleIds) {

		ConditionalAccessUserConfiguration config = includeAllUsers();

		if (excludedUserIds != null && !excludedUserIds.isEmpty()) {
			config.excludeUsers = new ArrayList<>(excludedUserIds);
		}

		if (excludedGroupIds != null && !excludedGroupIds.isEmpty()) {
			config.excludeGroups = new ArrayList<>(excludedGroupIds);
		}

		if (excludedRoleIds != null && !excludedRoleIds.isEmpty()) {
			config.excludeRoles = new ArrayList<>(excludedRoleIds);
		}

		return config;
	}

	/**
	 * Convenience method to create a configuration that blocks all users except one specified user
	 * @param excludedUserId User ID to exclude from the policy
	 * @return A configuration that blocks all users except the specified user
	 */
	public static ConditionalAccessUserConfiguration blockAllExceptUser(String excludedUserId) {
		return blockAllExcept(
				Collections.singletonList(excludedUserId),
				Collections.emptyList(),
				Collections.emptyList()
		);
	}

	/**
	 * Convenience method to create a configuration that blocks all users except one specified group
	 * @param excludedGroupId Group ID to exclude from the policy
	 * @return A configuration that blocks all users except the specified group
	 */
	public static ConditionalAccessUserConfiguration blockAllExceptGroup(String excludedGroupId) {
		return blockAllExcept(
				Collections.emptyList(),
				Collections.singletonList(excludedGroupId),
				Collections.emptyList()
		);
	}

	/**
	 * Convenience method to create a configuration that blocks all users except one specified role
	 * @param excludedRoleId Role ID to exclude from the policy
	 * @return A configuration that blocks all users except the specified role
	 */
	public static ConditionalAccessUserConfiguration blockAllExceptRole(String excludedRoleId) {
		return blockAllExcept(
				Collections.emptyList(),
				Collections.emptyList(),
				Collections.singletonList(excludedRoleId)
		);
	}

	/**
	 * Sets specific users to include in the policy
	 * @param userIds List of user IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedUsers(List<String> userIds) {
		this.includeUsers = new ArrayList<>(userIds);
		return this;
	}

	/**
	 * Sets specific users to include in the policy (varargs version)
	 * @param userIds User IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedUsers(String... userIds) {
		this.includeUsers = Arrays.asList(userIds);
		return this;
	}

	/**
	 * Sets specific users to exclude from the policy
	 * @param userIds List of user IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedUsers(List<String> userIds) {
		this.excludeUsers = new ArrayList<>(userIds);
		return this;
	}

	/**
	 * Sets specific users to exclude from the policy (varargs version)
	 * @param userIds User IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedUsers(String... userIds) {
		this.excludeUsers = Arrays.asList(userIds);
		return this;
	}

	/**
	 * Sets specific groups to include in the policy
	 * @param groupIds List of group IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedGroups(List<String> groupIds) {
		this.includeGroups = new ArrayList<>(groupIds);
		return this;
	}

	/**
	 * Sets specific groups to include in the policy (varargs version)
	 * @param groupIds Group IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedGroups(String... groupIds) {
		this.includeGroups = Arrays.asList(groupIds);
		return this;
	}

	/**
	 * Sets specific groups to exclude from the policy
	 * @param groupIds List of group IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedGroups(List<String> groupIds) {
		this.excludeGroups = new ArrayList<>(groupIds);
		return this;
	}

	/**
	 * Sets specific groups to exclude from the policy (varargs version)
	 * @param groupIds Group IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedGroups(String... groupIds) {
		this.excludeGroups = Arrays.asList(groupIds);
		return this;
	}

	/**
	 * Sets specific roles to include in the policy
	 * @param roleIds List of role IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedRoles(List<String> roleIds) {
		this.includeRoles = new ArrayList<>(roleIds);
		return this;
	}

	/**
	 * Sets specific roles to include in the policy (varargs version)
	 * @param roleIds Role IDs to include
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withIncludedRoles(String... roleIds) {
		this.includeRoles = Arrays.asList(roleIds);
		return this;
	}

	/**
	 * Sets specific roles to exclude from the policy
	 * @param roleIds List of role IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedRoles(List<String> roleIds) {
		this.excludeRoles = new ArrayList<>(roleIds);
		return this;
	}

	/**
	 * Sets specific roles to exclude from the policy (varargs version)
	 * @param roleIds Role IDs to exclude
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration withExcludedRoles(String... roleIds) {
		this.excludeRoles = Arrays.asList(roleIds);
		return this;
	}

	/**
	 * Include all guest or external users in the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration includeAllGuestsOrExternalUsers() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_GUESTS_OR_EXTERNAL_USERS);
		this.includeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Include all guest users in the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration includeAllGuests() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_GUESTS);
		this.includeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Include all external users in the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration includeAllExternalUsers() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_EXTERNAL_USERS);
		this.includeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Exclude all guest or external users from the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration excludeAllGuestsOrExternalUsers() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_GUESTS_OR_EXTERNAL_USERS);
		this.excludeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Exclude all guest users from the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration excludeAllGuests() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_GUESTS);
		this.excludeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Exclude all external users from the policy
	 * @return This configuration instance for method chaining
	 */
	public ConditionalAccessUserConfiguration excludeAllExternalUsers() {
		ObjectMapper mapper = new ObjectMapper();
		ObjectNode node = mapper.createObjectNode();
		node.put("guestOrExternalUserTypes", ALL_EXTERNAL_USERS);
		this.excludeGuestsOrExternalUsers = node;
		return this;
	}

	/**
	 * Converts this configuration to a JsonNode for use in API requests
	 * @param objectMapper The ObjectMapper to use for creating JSON
	 * @return JsonNode representing this configuration
	 */
	public JsonNode toJsonNode(ObjectMapper objectMapper) {
		ObjectNode userNode = objectMapper.createObjectNode();

		// Include users
		ArrayNode includeUsersNode = objectMapper.createArrayNode();
		this.includeUsers.forEach(includeUsersNode::add);
		userNode.set("includeUsers", includeUsersNode);

		// Exclude users
		ArrayNode excludeUsersNode = objectMapper.createArrayNode();
		this.excludeUsers.forEach(excludeUsersNode::add);
		userNode.set("excludeUsers", excludeUsersNode);

		// Include groups
		ArrayNode includeGroupsNode = objectMapper.createArrayNode();
		this.includeGroups.forEach(includeGroupsNode::add);
		userNode.set("includeGroups", includeGroupsNode);

		// Exclude groups
		ArrayNode excludeGroupsNode = objectMapper.createArrayNode();
		this.excludeGroups.forEach(excludeGroupsNode::add);
		userNode.set("excludeGroups", excludeGroupsNode);

		// Include roles
		ArrayNode includeRolesNode = objectMapper.createArrayNode();
		this.includeRoles.forEach(includeRolesNode::add);
		userNode.set("includeRoles", includeRolesNode);

		// Exclude roles
		ArrayNode excludeRolesNode = objectMapper.createArrayNode();
		this.excludeRoles.forEach(excludeRolesNode::add);
		userNode.set("excludeRoles", excludeRolesNode);

		// Guest/external users
		userNode.set("includeGuestsOrExternalUsers", this.includeGuestsOrExternalUsers);
		userNode.set("excludeGuestsOrExternalUsers", this.excludeGuestsOrExternalUsers);

		return userNode;
	}

	/**
	 * Creates a user configuration from the example JSON in the requirements
	 * @return User configuration matching the example JSON
	 */
	public static ConditionalAccessUserConfiguration fromExample() {
		return ConditionalAccessUserConfiguration.includeAllUsers()
				.withExcludedUsers("196da83b-dfb6-4ef7-a1da-4473ec0b893a")
				.withExcludedGroups("b43719d6-9c79-4513-ab53-1afd80ce8626");
	}

	// Getters

	public List<String> getIncludeUsers() {
		return includeUsers;
	}

	public List<String> getExcludeUsers() {
		return excludeUsers;
	}

	public List<String> getIncludeGroups() {
		return includeGroups;
	}

	public List<String> getExcludeGroups() {
		return excludeGroups;
	}

	public List<String> getIncludeRoles() {
		return includeRoles;
	}

	public List<String> getExcludeRoles() {
		return excludeRoles;
	}

	public JsonNode getIncludeGuestsOrExternalUsers() {
		return includeGuestsOrExternalUsers;
	}

	public JsonNode getExcludeGuestsOrExternalUsers() {
		return excludeGuestsOrExternalUsers;
	}
}