package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.utils.ProtocolConstants.AUTHORIZATION_POLICY;

/**
 * Base class for remediators that configure Guest Access policies in Microsoft Entra ID.
 * Provides common functionality for managing authorization policies and cross-tenant access settings.
 */
public abstract class BaseGuestAccessRemediator extends RemediatorBase {
	// Common endpoints
	protected static final String CROSS_TENANT_POLICY_ENDPOINT = "/policies/crossTenantAccessPolicy/partners";

	// Common constants for guest access
	protected static final String GUEST_USER_ROLE_ID = "2af84b1e-32c8-42b7-82bc-daa82404023b"; // Restricted Guest User role
	protected static final String GUEST_INVITER_ROLE_ID = "95e79109-95c0-4d8e-aee3-d01accf2d47b"; // Guest Inviter role
	protected static final String ALLOW_INVITES_ADMINS_AND_GUEST_INVITERS = "adminsAndGuestInviters";

	protected final MicrosoftGraphClient graphClient;
	protected final ObjectMapper objectMapper;

	/**
	 * Constructor for the base guest access remediator.
	 *
	 * @param graphClient Microsoft Graph API client
	 */
	protected BaseGuestAccessRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	/**
	 * Gets the current authorization policy.
	 *
	 * @return CompletableFuture with the current policy
	 */
	protected CompletableFuture<JsonNode> getCurrentAuthorizationPolicy() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AUTHORIZATION_POLICY)
						.build()
		);
	}

	/**
	 * Gets the current cross-tenant access policy.
	 *
	 * @return CompletableFuture with the current policy
	 */
	protected CompletableFuture<JsonNode> getCurrentCrossTenantPolicy() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(CROSS_TENANT_POLICY_ENDPOINT)
						.build()
		);
	}

	/**
	 * Updates the authorization policy with the provided payload.
	 *
	 * @param updatePayload JSON payload for the update
	 * @param successMessage Message to include in the success response
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> updateAuthorizationPolicy(
			ObjectNode updatePayload,
			String successMessage) {
		try {
			String payloadJson = objectMapper.writeValueAsString(updatePayload);
			HttpRequest.BodyPublisher requestBody = HttpRequest.BodyPublishers.ofString(payloadJson);

			logger.info("Updating authorization policy: {}", payloadJson);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.PATCH)
							.withEndpoint(AUTHORIZATION_POLICY)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(requestBody)
							.build()
			).thenApply(response -> {
				logger.info("Successfully updated authorization policy: {}", successMessage);
				return IPolicyRemediator.success(getPolicyId(), successMessage).join();
			}).exceptionally(ex -> {
				logger.error("Failed to update authorization policy", ex);
				return IPolicyRemediator.failed(getPolicyId(),
						"Failed to update policy: " + ex.getMessage()).join();
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to create authorization policy update payload", e);
		}
	}

	/**
	 * Updates the cross-tenant access policy with the provided payload.
	 *
	 * @param updatePayload JSON payload for the update
	 * @return CompletableFuture with the response
	 */
	protected CompletableFuture<JsonNode> updateCrossTenantPolicy(ObjectNode updatePayload) {
		try {
			String payloadJson = objectMapper.writeValueAsString(updatePayload);
			HttpRequest.BodyPublisher requestBody = HttpRequest.BodyPublishers.ofString(payloadJson);

			logger.info("Updating cross-tenant access policy: {}", payloadJson);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.POST)
							.withEndpoint(CROSS_TENANT_POLICY_ENDPOINT)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(requestBody)
							.build()
			);
		} catch (Exception e) {
			throw new GraphClientException("Failed to create cross-tenant policy update payload", e);
		}
	}

	/**
	 * Checks if a specific boolean property in the authorization policy is set to the expected value.
	 *
	 * @param policy The authorization policy
	 * @param propertyPath Path to the property (for nested properties, use dot notation, e.g., "defaultUserRolePermissions.allowedToReadOtherUsers")
	 * @param expectedValue The expected value of the property
	 * @return True if the property exists and matches the expected value, false otherwise
	 */
	protected boolean isBooleanPropertySetCorrectly(JsonNode policy, String propertyPath, boolean expectedValue) {
		String[] pathParts = propertyPath.split("\\.");
		JsonNode currentNode = policy;

		for (String part : pathParts) {
			if (!currentNode.has(part)) {
				return false;
			}
			currentNode = currentNode.get(part);
		}

		return currentNode.isBoolean() && currentNode.asBoolean() == expectedValue;
	}

	/**
	 * Checks if a specific string property in the authorization policy is set to the expected value.
	 *
	 * @param policy The authorization policy
	 * @param propertyName Name of the property
	 * @param expectedValue The expected value of the property
	 * @return True if the property exists and matches the expected value, false otherwise
	 */
	protected boolean isStringPropertySetCorrectly(JsonNode policy, String propertyName, String expectedValue) {
		return policy.has(propertyName) &&
				expectedValue.equals(policy.get(propertyName).asText());
	}
}