package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.2.3v1")
public class TeamsAllowTeamsConsumerRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowTeamsConsumerRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowTeamsConsumerRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> federationConfigurations = getFederationConfigurations();

		if (federationConfigurations.isEmpty()) {
			logger.error("No federation configurations found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No federation configurations found in configuration"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = federationConfigurations.stream()
				.filter(configuration -> configuration.allowTeamsConsumer)
				.map(this::fixConfig_)
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig_(TeamsTenantFederationConfiguration fedConfig) {
		TenantFederationConfiguration configuration = new TenantFederationConfiguration();
		configuration.identity = fedConfig.identity;
		configuration.allowTeamsConsumer = false;

		ParameterChangeResult consumerParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowTeamsConsumer: " + fedConfig.identity)
				.prevValue(fedConfig.allowTeamsConsumer)
				.newValue(configuration.allowTeamsConsumer);

		return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(configuration))
				.thenApply(config -> {
					consumerParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration fixed: " + configuration.identity, List.of(consumerParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", configuration.identity, ex.getMessage());
					consumerParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(consumerParam));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.TEAMS_CONSUMER_SUCCESS_MESSAGE, allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any federation configurations", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " configurations, failed to fix " + failedCount + " configurations",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				boolean allowTeamsConsumer = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowTeamsConsumer = Boolean.parseBoolean(change.getNewValue().toString());

				TenantFederationConfiguration configuration = new TenantFederationConfiguration();
				configuration.identity = change.getParameter().split(": ")[1];
				configuration.allowTeamsConsumer = allowTeamsConsumer;

				ParameterChangeResult consumerParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowTeamsConsumer: " + configuration.identity)
						.prevValue(prevAllowTeamsConsumer)
						.newValue(allowTeamsConsumer);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), configuration.identity);
					consumerParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + configuration.identity + " skipped", List.of(consumerParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(configuration))
						.thenApply(jsonNode -> {
							consumerParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back teams consumer policy", List.of(consumerParam));
						})
						.exceptionally(ex -> {
							consumerParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during teams consumer policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(consumerParam));
						});

				results.add(result);
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
