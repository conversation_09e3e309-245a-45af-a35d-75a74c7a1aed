package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

/**
 * Implements remediation for MS.AAD.6.1v1: Configure passwords to never expire.
 *
 * This remediation ensures that password expiration is disabled for cloud-only users,
 * aligning with NIST/OMB guidance to avoid forced password rotations.
 * For hybrid environments (AD-synced users), additional on-premises configuration may be required.
 */
@PolicyRemediator("MS.AAD.6.1v1")
public class EntraIDPasswordExpiryRemediator extends RemediatorBase {
	private static final String DOMAINS_ENDPOINT = "/domains";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final String domainName;

	public EntraIDPasswordExpiryRemediator(MicrosoftGraphClient graphClient, String domainName) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.domainName = domainName;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for password expiration policy using MS Graph REST API");

		return updatePasswordPolicy()
				.exceptionally(ex -> {
					logger.error("Exception while configuring password expiration policy", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> updatePasswordPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
							GraphRequest.builder()
									.v1()
									.withMethod(HttpMethod.PATCH)
									.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
									.withBody(policyBody)
									.withEndpoint(DOMAINS_ENDPOINT + "/" + domainName)
									.build()
					)
					.thenCompose(policyResult -> {
						if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
							logger.info("Password expiration policy updated successfully for domain {}", domainName);
							return checkHybridEnvironment().thenCompose(isHybrid -> {
								String message = "Password expiration policy has been disabled for cloud users";
								if (isHybrid) {
									message += ". Note: For hybrid users, additional on-premises AD configuration is required";
								}
								return IPolicyRemediator.success(getPolicyId(), message);
							});
						} else {
							String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
									? policyResult.get(Constants.ERROR_FIELD).asText()
									: "Unknown error updating password policy";
							logger.error("Failed to update password expiration policy: {}", error);
							return IPolicyRemediator.failed(getPolicyId(), error);
						}
					});
		} catch (Exception e) {
			throw new GraphClientException("Failed to update password expiration policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		policyPayload.put("passwordValidityPeriodInDays", 0);
		return policyPayload;
	}

	/**
	 * Checks if the environment is hybrid (has AD-synced users).
	 * This helps provide appropriate guidance in the remediation message.
	 */
	private CompletableFuture<Boolean> checkHybridEnvironment() {
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.v1()
								.withEndpoint("/users?$select=onPremisesSyncEnabled&$top=1")
								.build()
				)
				.thenApply(response -> {
					if (response.has("value") && response.get("value").size() > 0) {
						JsonNode user = response.get("value").get(0);
						return user.has("onPremisesSyncEnabled") &&
								user.get("onPremisesSyncEnabled").asBoolean();
					}
					return false;
				})
				.exceptionally(e -> {
					logger.warn("Could not determine if environment is hybrid: {}", e.getMessage());
					return false;
				});
	}

	/**
	 * Checks the current status of password expiration policy.
	 *
	 * @return CompletableFuture<Boolean> true if passwords are set to never expire
	 */
	public CompletableFuture<Boolean> checkPasswordExpiryStatus() {
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.v1()
								.withEndpoint(DOMAINS_ENDPOINT + "/" + domainName +
										"?$select=id,passwordValidityPeriodInDays")
								.build()
				)
				.thenApply(response -> {
					int validityPeriod = response.path("passwordValidityPeriodInDays").asInt(-1);
					boolean isDisabled = validityPeriod == 0;
					logger.info("Password expiration is {} disabled",
							isDisabled ? "currently" : "not");
					return isDisabled;
				})
				.exceptionally(e -> {
					logger.error("Failed to check password expiration settings: {}",
							e.getMessage(), e);
					return false;
				});
	}

	/**
	 * Optional: Disables password expiration for a specific user (e.g., service accounts).
	 *
	 * @param userId The ID of the user to exempt from password expiration
	 * @return CompletableFuture<JsonNode> indicating success or failure
	 */
	public CompletableFuture<JsonNode> disablePasswordExpiryForUser(String userId) {
		ObjectNode userPayload = objectMapper.createObjectNode();
		userPayload.put("passwordPolicies", "DisablePasswordExpiration");

		try {
			String payloadStr = objectMapper.writeValueAsString(userPayload);
			HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(payloadStr);

			return graphClient.makeGraphRequest(
							GraphRequest.builder()
									.v1()
									.withMethod(HttpMethod.PATCH)
									.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
									.withBody(body)
									.withEndpoint("/users/" + userId)
									.build()
					)
					.thenApply(result -> {
						if (result != null && !result.has(Constants.ERROR_FIELD)) {
							logger.info("Password expiration disabled for user {}", userId);
							return result;
						}
						throw new GraphClientException("Failed to update user password policy");
					});
		} catch (Exception e) {
			return CompletableFuture.failedFuture(
					new GraphClientException("Failed to update user password policy", e));
		}
	}
}