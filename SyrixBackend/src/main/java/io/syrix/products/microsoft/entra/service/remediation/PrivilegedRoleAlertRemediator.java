package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.service.EntraIDRemediationException;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.apache.commons.lang3.tuple.Pair;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static io.syrix.common.constants.Constants.*;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ADMIN;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ALL;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ASSIGNMENT;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.CALLER;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ENF_SETTINGS;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.INH_SETTINGS;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.LEVEL;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ODATA_TYPE;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.RECIPIENT_TYPE;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.TARGET;

@PolicyRemediator("MS.AAD.7.7v1")
public class PrivilegedRoleAlertRemediator extends RemediatorBase {

	// Roles to exclude, as mentioned in the document
	private static final Set<String> EXCLUDED_ROLES = new HashSet<>(Arrays.asList(
			"Helpdesk Administrator",
			"Password Administrator",
			"Global Reader"
	));

	// Exclusion keywords for filtering roles
	private static final List<String> EXCLUDED_KEYWORDS = Arrays.asList("do not use","partner","read-only");

	private static final String POLICY_ASSIGNMENTS_ENDPOINT = "/policies/roleManagementPolicyAssignments";
	private static final String POLICIES_ENDPOINT = "/policies/roleManagementPolicies";
	private static final String ROLE_DEFS_ENDPOINT = "/roleManagement/directory/roleDefinitions";
	private static final String GROUP_ENDPOINT = "/groups";

	private static final String ELIGIBLE_ALERT_RULE_ID = "Notification_Admin_Admin_Eligibility";
	private static final String ACTIVE_ALERT_RULE_ID = "Notification_Admin_Admin_Assignment";
	private static final String END_USER_ASSIGNMENT = "Notification_Admin_EndUser_Assignment";
	private static final String NOTIFICATION_RULE_TYPE = "#microsoft.graph.unifiedRoleManagementPolicyNotificationRule";
	private static final List <Pair<String, String>> ROLE_ASSIGNMENTS = List.of(
			Pair.of("eligible", ELIGIBLE_ALERT_RULE_ID),
			Pair.of("active", ACTIVE_ALERT_RULE_ID),
			Pair.of(ASSIGNMENT, END_USER_ASSIGNMENT)
	);

	// Group role constants
	private static final String GROUP_POLICIES_FILTER = "scopeId eq '%s' and scopeType eq 'Group'";
	private static final String ROLE_ASSIGNMENTS_ENDPOINT = "/roleManagement/directory/roleAssignments";

	private static final String NOTIFICATION_GROUP_NAME = "Administrators Notifications Group";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private String securityMonitoringEmail = null;

	public PrivilegedRoleAlertRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to configure alerts for privileged role assignments");

		return getOrCreateSecurityGroup()
				.thenCompose(groupEmail -> {
							securityMonitoringEmail = groupEmail;
							return retrieveHighPrivilegeRoles()
									.thenCompose(this::configureAlertsForRoles)
									.thenCompose(this::handlePIMGroups);
						}
				).exceptionally(ex -> {
					logger.error("Exception during privileged role alert configuration", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Verifies that the security group for alerts exists and creates it if it doesn't
	 *
	 * @return CompletableFuture<String> with the group ID if successful, or an error message
	 */
	private CompletableFuture<String> getOrCreateSecurityGroup() {
		// Extract group name from email if it's an email address
		String groupName = NOTIFICATION_GROUP_NAME;
		String groupDescription = "Security group for privileged role assignment notifications";

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(GROUP_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "displayName eq '" + groupName + "'")
						.build()
		).thenCompose(response -> {
			JsonNode groups = response.get(VALUE_FIELD);

			// If group exists, return its ID
			if (groups != null && groups.isArray() && groups.size() > 0) {
				String groupEmail = groups.get(0).path("mail").asText();
				logger.info("Found existing security group: {} with ID: {}", groupName, groupEmail);
				return CompletableFuture.completedFuture(groupEmail);
			}

			// Group doesn't exist, create it
			logger.info("Security group {} not found, creating it", groupName);
			return createSecurityGroup(groupName, groupDescription);
		}).exceptionally(ex -> {
			logger.error("Failed to verify or create security group: {}", ex.getMessage());
			throw new CompletionException(new RuntimeException("Failed to verify or create security group", ex));
		});
	}

	/**
	 * Creates a new mail-enabled security group
	 *
	 * @param groupName The display name for the group
	 * @param description The description for the group
	 * @return CompletableFuture<String> containing the newly created group ID
	 */
	private CompletableFuture<String> createSecurityGroup(String groupName, String description) {
		ObjectNode groupBody = objectMapper.createObjectNode();
		groupBody.put("displayName", groupName);
		groupBody.put("description", description);
		groupBody.put("mailEnabled", true);
		groupBody.put("mailNickname", groupName.replaceAll("[^a-zA-Z0-9]", ""));
		groupBody.put("securityEnabled", false);
		groupBody.put("visibility","Private");
		groupBody.set("groupTypes", objectMapper.createArrayNode().add("Unified"));

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(GROUP_ENDPOINT)
						.withMethod(HttpMethod.POST)
						.addHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON)
						.withBody(HttpRequest.BodyPublishers.ofString(groupBody.toString()))
						.build()
		).thenApply(response -> {
			String groupEmail = response.path("mail").asText();
			logger.info("Successfully created security group: {} with ID: {}", groupName, groupEmail);
			return groupEmail;
		}).exceptionally(ex -> {
			logger.error("Failed to create security group: {}", ex.getMessage());
			throw new CompletionException(new RuntimeException("Failed to create security group", ex));
		});
	}

	/**
	 * Retrieves high privilege roles based on specified criteria from the flow document.
	 * This method:
	 * 1. Fetches all privileged roles from Microsoft Graph API
	 * 2. Filters out roles based on exclusion criteria (keywords, names, and categories)
	 * 3. Returns a list of roles that require monitoring
	 *
	 * @return CompletableFuture containing a list of filtered high privilege RoleInfo objects
	 */
	private CompletableFuture<List<RoleInfo>> retrieveHighPrivilegeRoles() {
		logger.info("Retrieving and filtering high privilege roles");

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.beta()
						.withEndpoint(ROLE_DEFS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "isPrivileged eq true")
						.build()
		).thenApply(response -> {
			List<RoleInfo> allRoles = new ArrayList<>();
			List<RoleInfo> highPrivilegeRoles = new ArrayList<>();
			JsonNode rolesNode = response.get(VALUE_FIELD);

			if (rolesNode == null || !rolesNode.isArray()) {
				logger.warn("No roles found or invalid response format");
				return highPrivilegeRoles;
			}

			// First pass: convert all roles to RoleInfo objects
			for (JsonNode role : rolesNode) {
				String id = role.path(ID_FIELD).asText("");
				String displayName = role.path(DISPLAY_NAME_FIELD).asText("");
				String description = role.path("description").asText("");
				String richDescription = role.path("richDescription").asText("");

				List<String> categories = new ArrayList<>();
				JsonNode categoryNode = role.path("categories");
				if (categoryNode.isArray()) {
					for (JsonNode cat : categoryNode) {
						categories.add(cat.asText());
					}
				}

				allRoles.add(new RoleInfo(id, displayName, description, richDescription, categories));
			}

			highPrivilegeRoles = allRoles.stream().filter(this::isHighPrivilegeRole).toList();

			logger.info("Filtered to {} high privilege roles that require monitoring", highPrivilegeRoles.size());
			return highPrivilegeRoles;
		}).exceptionally(ex -> {
			logger.error("Error retrieving high privilege roles: {}", ex.getMessage(), ex);
			return new ArrayList<>();
		});
	}

	/**
	 * Filters a role based on the criteria from the flow document.
	 *
	 * @param role The role to evaluate
	 * @return true if the role should be included, false if it should be filtered out
	 */
	private boolean isHighPrivilegeRole(RoleInfo role) {
		// Priority 1: Exclude explicitly excluded roles
		if (EXCLUDED_ROLES.contains(role.getDisplayName())) {
			logger.debug("Excluding explicitly excluded role: {}", role.getDisplayName());
			return false;
		}

		// Priority 2: Check for excluded keywords in descriptions
		String combinedText = (role.getDescription() + " " + role.getRichDescription()).toLowerCase();
		boolean containsExcludedKeyword = EXCLUDED_KEYWORDS.stream()
				.anyMatch(combinedText::contains);

		if (containsExcludedKeyword) {
			logger.debug("Excluding role with excluded keywords: {}", role.getDisplayName());
			return false;
		}
		// This role passed all filters
		return true;
	}

	/**
	 * Configures alerts for all filtered critical roles
	 */
	private CompletableFuture<JsonNode> configureAlertsForRoles(List<RoleInfo> roles) {
		List<CompletableFuture<Boolean>> configurationFutures = new ArrayList<>();

		for (RoleInfo role : roles) {
			configurationFutures.add(configureAlertForRole(role));
		}

		return CompletableFuture.allOf(configurationFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					long successCount = configurationFutures.stream()
							.filter(future -> {
								try {
									return future.join();
								} catch (Exception e) {
									return false;
								}
							})
							.count();

					if (successCount == roles.size()) {
						return IPolicyRemediator.success(getPolicyId(),
								String.format("Successfully configured alerts for all %d critical privileged roles",
										roles.size())).join();
					} else {
						return IPolicyRemediator.partial_success(getPolicyId(),
								String.format("Configured alerts for %d of %d critical privileged roles",
										successCount,
										roles.size())).join();
					}
				});
	}

	/**
	 * Handles PIM Groups by finding roles assigned to groups and configuring alerts for those groups
	 */
	private CompletableFuture<JsonNode> handlePIMGroups(JsonNode initialResult) {
		// We'll preserve the initial result unless we need to change it
		return findRolesAssignedToGroups()
				.thenCompose(groupRoles -> {
					if (groupRoles.isEmpty()) {
						// No group roles found, return the initial result
						return CompletableFuture.completedFuture(initialResult);
					}

					List<CompletableFuture<Boolean>> groupAlertFutures = new ArrayList<>();
					for (GroupRoleInfo groupRole : groupRoles) {
						groupAlertFutures.add(configureGroupRoleAlert(groupRole));
					}

					return CompletableFuture.allOf(groupAlertFutures.toArray(new CompletableFuture[0]))
							.thenApply(v -> {
								long successCount = groupAlertFutures.stream()
										.filter(future -> {
											try {
												return future.join();
											} catch (Exception e) {
												return false;
											}
										})
										.count();

								// If the initial result was a success and we handled all groups successfully
								if (initialResult.path("status").asText().equals("success") &&
										successCount == groupRoles.size()) {
									return initialResult;
								} else {
									// Create a combined message
									String message = initialResult.path("message").asText() +
											String.format("; Configured alerts for %d of %d group role assignments",
													successCount, groupRoles.size());

									return IPolicyRemediator.partial_success(getPolicyId(), message).join();
								}
							});
				})
				.exceptionally(ex -> {
					logger.error("Error handling PIM groups: {}", ex.getMessage());
					// If we fail here, we still want to preserve the initial result
					return initialResult;
				});
	}

	/**
	 * Finds roles that are assigned through PIM groups
	 */
	private CompletableFuture<List<GroupRoleInfo>> findRolesAssignedToGroups() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.beta()
						.withEndpoint(ROLE_ASSIGNMENTS_ENDPOINT)
						.addQueryParam("$expand", "principal")
						.build()
		).thenApply(response -> {
			List<GroupRoleInfo> groupRoles = new ArrayList<>();
			JsonNode assignments = response.get(VALUE_FIELD);

			if (assignments != null && assignments.isArray()) {
				for (JsonNode assignment : assignments) {
					JsonNode principal = assignment.path("principal");
					if (principal.has(ODATA_TYPE) &&
							principal.get(ODATA_TYPE).asText().contains("group")) {

						String roleId = assignment.path("roleDefinitionId").asText();
						String groupId = principal.path(ID_FIELD).asText();
						String groupName = principal.path(DISPLAY_NAME_FIELD).asText();

						groupRoles.add(new GroupRoleInfo(groupId, groupName, roleId));
					}
				}
			}

			logger.info("Found {} roles assigned through groups", groupRoles.size());
			return groupRoles;
		}).exceptionally(ex -> {
			logger.error("Failed to find roles assigned to groups: {}", ex.getMessage());
			return new ArrayList<>();
		});
	}

	/**
	 * Configures alerts for a role assigned to a group
	 */
	private CompletableFuture<Boolean> configureGroupRoleAlert(GroupRoleInfo groupRole) {
		String filter = String.format(GROUP_POLICIES_FILTER, groupRole.getGroupId());

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.beta()
						.withEndpoint(POLICIES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, filter)
						.build()
		).thenCompose(response -> {
			JsonNode policies = response.get(VALUE_FIELD);
			if (policies == null || policies.size() == 0) {
				logger.warn("No policies found for group: {}", groupRole.getGroupName());
				return CompletableFuture.completedFuture(false);
			}

			List<CompletableFuture<Boolean>> policyFutures = new ArrayList<>();
			for (JsonNode policy : policies) {
				String policyId = policy.get(ID_FIELD).asText();
				policyFutures.add(updateGroupPolicyRules(policyId, groupRole));
			}

			return CompletableFuture.allOf(policyFutures.toArray(new CompletableFuture[0]))
					.thenApply(v -> policyFutures.stream()
							.allMatch(future -> {
								try {
									return future.join();
								} catch (Exception e) {
									return false;
								}
							}));
		}).exceptionally(ex -> {
			logger.error("Failed to configure alerts for group {}: {}",
					groupRole.getGroupName(), ex.getMessage());
			return false;
		});
	}

	private CompletableFuture<Boolean> configureAlertForRule(JsonNode policy,
															 String ruleId,
															 String name,
															 String type) {
		// Find notification rule
		NotificationRule rule = parseNotificationRule(policy, ruleId);
		String policyId = policy.get(ID_FIELD).asText();
		if (rule != null) {
			configureNotificationRecipients(rule);
			 return updateNotificationRule(policyId, rule, name, type);
		} else {
			logger.warn("No assignment notification rule found for  : {}", type);
		}
		return CompletableFuture.completedFuture(false);
	}

	/**
	 * Updates the notification rules for a group policy
	 */
	private CompletableFuture<Boolean> updateGroupPolicyRules(String policyId, GroupRoleInfo groupRole) {
		return getRoleManagementPolicy(policyId)
				.thenCompose(policy -> {
					List<CompletableFuture<Boolean>> updateFutures = new ArrayList<>();

					ROLE_ASSIGNMENTS.forEach(pair ->
						updateFutures.add(configureAlertForRule(policy,
								pair.getRight(),
								groupRole.getGroupName(),
								pair.getLeft()))
					);

					return CompletableFuture.allOf(updateFutures.toArray(new CompletableFuture[0]))
							.thenApply(v -> updateFutures.stream()
									.allMatch(future -> {
										try {
											return future.join();
										} catch (Exception e) {
											return false;
										}
									}));
				});
	}

	private CompletableFuture<Boolean> configureAlertForRole(RoleInfo role) {
		return getPolicyAssignment(role.getId())
				.thenCompose(assignment -> {
					String policyId = assignment.get("policyId").asText();
					return getRoleManagementPolicy(policyId)
							.thenCompose(policy -> {
								ROLE_ASSIGNMENTS.forEach(pair ->
										configureAlertForRule(policy,
												pair.getRight(),
												role.getDisplayName(),
												pair.getLeft()).join());
								return verifyNotificationSettings(policyId, role);
							});
				})
				.exceptionally(ex -> {
					logger.error("Failed to configure alerts for role {}: {}", role.getDisplayName(), ex.getMessage());
					return false;
				});
	}

	private CompletableFuture<JsonNode> getPolicyAssignment(String roleDefinitionId) {
		String filter = String.format(
				"scopeId eq '/' and scopeType eq 'DirectoryRole' and roleDefinitionId eq '%s'",
				roleDefinitionId);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.beta()
						.withEndpoint(POLICY_ASSIGNMENTS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, filter)
						.build()
		).thenApply(response -> {
			JsonNode assignments = response.get(VALUE_FIELD);
			if (assignments == null || assignments.size() == 0) {
				throw new GraphClientException("No policy assignment found for role: " + roleDefinitionId);
			}
			return assignments.get(0);
		}).exceptionally(
				ex -> {
					logger.error("Failed to get policy assignment for role {}: {}", roleDefinitionId, ex.getMessage());
					throw new EntraIDRemediationException("Failed to get policy assignment for role: " + roleDefinitionId);
				}
		);
	}

	private CompletableFuture<JsonNode> getRoleManagementPolicy(String policyId) {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.beta()
						.withEndpoint(POLICIES_ENDPOINT + "/" + policyId)
						.addQueryParam("$expand", "rules")
						.build()
		);
	}

	private NotificationRule parseNotificationRule(JsonNode policy, String ruleId) {
		try {
			JsonNode rules = policy.path("rules");
			if (!rules.isArray()) {
				return null;
			}
			return findAndParseRule(rules, ruleId);
		} catch (Exception e) {
			logger.error("Error parsing notification rule: {}", e.getMessage(), e);
			throw new EntraIDRemediationException("Failed to parse notification rule: " + e.getMessage());
		}
	}

	private NotificationRule findAndParseRule(JsonNode rules, String ruleId) {
		for (JsonNode ruleNode : rules) {
			if (!isMatchingRule(ruleNode, ruleId)) {
				continue;
			}
			return createNotificationRule(ruleNode);
		}
		return null;
	}

	private boolean isMatchingRule(JsonNode ruleNode, String ruleId) {
		return ruleId.equals(ruleNode.path(ID_FIELD).asText()) &&
				NOTIFICATION_RULE_TYPE.equals(ruleNode.path(ODATA_TYPE).asText());
	}

	private NotificationRule createNotificationRule(JsonNode ruleNode) {
		NotificationRule rule = new NotificationRule();
		setBasicProperties(rule, ruleNode);
		setNotificationRecipients(rule, ruleNode.path("notificationRecipients"));
		setTargetProperties(rule, ruleNode.path(TARGET));
		return rule;
	}

	private void setBasicProperties(NotificationRule rule, JsonNode ruleNode) {
		rule.setId(ruleNode.path(ID_FIELD).asText());
		rule.setOdataType(ruleNode.path(ODATA_TYPE).asText());
		rule.setNotificationType(ruleNode.path("notificationType").asText(EMAIL));
		rule.setRecipientType(ruleNode.path(RECIPIENT_TYPE).asText(ADMIN));
		rule.setNotificationLevel(ruleNode.path("notificationLevel").asText(ALL));
		rule.setIsDefaultRecipientsEnabled(ruleNode.path("isDefaultRecipientsEnabled").asBoolean(true));
	}

	private void setNotificationRecipients(NotificationRule rule, JsonNode recipientsNode) {
		List<String> recipients = new ArrayList<>();
		if (recipientsNode.isArray()) {
			recipientsNode.forEach(recipient -> recipients.add(recipient.asText()));
		}
		rule.setNotificationRecipients(recipients);
	}

	private void setTargetProperties(NotificationRule rule, JsonNode targetNode) {
		if (targetNode.isMissingNode() || targetNode.isNull()) {
			return;
		}

		NotificationRule.Target target = new NotificationRule.Target();
		target.setCaller(targetNode.path(CALLER).asText("EndUser"));
		target.setOperations(extractOperations(targetNode.path(OPERATIONS)));
		target.setLevel(targetNode.path(LEVEL).asText(ASSIGNMENT));
		target.setInheritableSettings(extractSettings(targetNode.path(INH_SETTINGS)));
		target.setEnforcedSettings(extractSettings(targetNode.path(ENF_SETTINGS)));

		rule.setTarget(target);
	}

	private List<String> extractOperations(JsonNode opsNode) {
		List<String> operations = new ArrayList<>();
		if (opsNode.isArray()) {
			opsNode.forEach(op -> operations.add(op.asText()));
		} else {
			operations.add(ALL);
		}
		return operations;
	}

	private List<String> extractSettings(JsonNode settingsNode) {
		List<String> settings = new ArrayList<>();
		if (settingsNode.isArray()) {
			settingsNode.forEach(setting -> settings.add(setting.asText()));
		}
		return settings;
	}


	/**
	 * Configures notification recipients for a rule by ensuring required settings are enabled
	 * and adding the security monitoring email if not already present.
	 *
	 * @param rule The notification rule to configure
	 */
	private void configureNotificationRecipients(NotificationRule rule) {
		// Ensure default recipients are enabled
		rule.setIsDefaultRecipientsEnabled(true);

		// Set notification level to All to ensure comprehensive notifications
		rule.setNotificationLevel(ALL);

		// Initialize recipients list if null
		List<String> recipients = rule.getNotificationRecipients();
		if (recipients == null) {
			recipients = new ArrayList<>();
			rule.setNotificationRecipients(recipients);
		}

		// Add security monitoring email if not already present
		if (!recipients.contains(securityMonitoringEmail)) {
			recipients.add(securityMonitoringEmail);
		}

		// Ensure we have proper notification type
		if (rule.getNotificationType() == null) {
			rule.setNotificationType(EMAIL);
		}

		// Set recipient type to Admin for proper routing
		rule.setRecipientType(ADMIN);
	}

	private CompletableFuture<Boolean> updateNotificationRule(String policyId, NotificationRule rule,
															  String entityName, String assignmentType) {
		try {
			JsonNode ruleJson = ruleToJson(rule);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.beta()
							.withMethod(HttpMethod.PATCH)
							.withEndpoint(POLICIES_ENDPOINT + "/" + policyId + "/rules/" + rule.getId())
							.addHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON)
							.withBody(HttpRequest.BodyPublishers.ofString(ruleJson.toString()))
							.build()
			).thenApply(response -> {
				logger.info("Updated {} alert settings for {}: {}", assignmentType, entityName, rule.getId());
				return true;
			}).exceptionally(ex -> {
				logger.error("Failed to update {} alert settings for {}: {}",
						assignmentType, entityName, ex.getMessage());
				return false;
			});
		} catch (Exception e) {
			logger.error("Error converting rule to JSON: {}", e.getMessage());
			return CompletableFuture.completedFuture(false);
		}
	}

	/**
	 * Converts a NotificationRule object to a JsonNode for the API request
	 *
	 * @param rule The notification rule to convert
	 * @return JsonNode representing the rule
	 */
	private JsonNode ruleToJson(NotificationRule rule) {
		ObjectNode ruleNode = objectMapper.createObjectNode();
		addBasicProperties(ruleNode, rule);
		addNotificationSettings(ruleNode, rule);
		addRecipients(ruleNode, rule.getNotificationRecipients());
		addTargetObject(ruleNode, rule.getTarget());
		return ruleNode;
	}

	private void addBasicProperties(ObjectNode ruleNode, NotificationRule rule) {
		ruleNode.put(ODATA_TYPE, rule.getOdataType());
		ruleNode.put(ID_FIELD, rule.getId());
	}

	private void addNotificationSettings(ObjectNode ruleNode, NotificationRule rule) {
		ruleNode.put("notificationType", rule.getNotificationType() != null ?
				rule.getNotificationType() : EMAIL);
		ruleNode.put(RECIPIENT_TYPE, rule.getRecipientType() != null ?
				rule.getRecipientType() : ADMIN);
		ruleNode.put("notificationLevel", rule.getNotificationLevel());
		ruleNode.put("isDefaultRecipientsEnabled", rule.isDefaultRecipientsEnabled());
	}

	private void addRecipients(ObjectNode ruleNode, List<String> recipients) {
		ArrayNode recipientsArray = ruleNode.putArray("notificationRecipients");
		if (recipients != null) {
			recipients.forEach(recipientsArray::add);
		}
	}

	private void addTargetObject(ObjectNode ruleNode, NotificationRule.Target target) {
		ObjectNode targetNode = ruleNode.putObject(TARGET);
		if (target != null) {
			addExistingTarget(targetNode, target);
		} else {
			addDefaultTarget(targetNode);
		}
	}

	private void addExistingTarget(ObjectNode targetNode, NotificationRule.Target target) {
		targetNode.put(CALLER, target.getCaller() != null ? target.getCaller() : ADMIN);
		addOperations(targetNode, target.getOperations());
		targetNode.put(LEVEL, target.getLevel() != null ? target.getLevel() : ASSIGNMENT);
		addSettings(targetNode, INH_SETTINGS, target.getInheritableSettings());
		addSettings(targetNode, ENF_SETTINGS, target.getEnforcedSettings());
	}

	private void addOperations(ObjectNode targetNode, List<String> operations) {
		ArrayNode operationsArray = targetNode.putArray(OPERATIONS);
		if (operations != null && !operations.isEmpty()) {
			operations.forEach(operationsArray::add);
		} else {
			operationsArray.add(ALL);
		}
	}

	private void addSettings(ObjectNode targetNode, String settingType, List<String> settings) {
		ArrayNode settingsArray = targetNode.putArray(settingType);
		if (settings != null) {
			settings.forEach(settingsArray::add);
		}
	}

	private void addDefaultTarget(ObjectNode targetNode) {
		targetNode.put(CALLER, ADMIN);
		targetNode.putArray(OPERATIONS).add(ALL);
		targetNode.put(LEVEL, ASSIGNMENT);
		targetNode.putArray(INH_SETTINGS);
		targetNode.putArray(ENF_SETTINGS);
	}

	private CompletableFuture<Boolean> verifyNotificationSettings(String policyId, RoleInfo role) {
		return getRoleManagementPolicy(policyId)
				.thenApply(policy -> {
					NotificationRule eligibleRule = parseNotificationRule(policy, ELIGIBLE_ALERT_RULE_ID);
					NotificationRule activeRule = parseNotificationRule(policy, ACTIVE_ALERT_RULE_ID);

					boolean eligibleConfigured = verifyNotificationSettings(eligibleRule);
					boolean activeConfigured = verifyNotificationSettings(activeRule);

					if (!eligibleConfigured) {
						logger.warn("Eligible assignment alert not properly configured for role: {}",
								role.getDisplayName());
					}

					if (!activeConfigured) {
						logger.warn("Active assignment alert not properly configured for role: {}",
								role.getDisplayName());
					}

					return eligibleConfigured && activeConfigured;
				})
				.exceptionally(ex -> {
					logger.error("Failed to verify notification settings for role {}: {}",
							role.getDisplayName(), ex.getMessage());
					return false;
				});
	}

	/**
	 * Verifies that notification settings are properly configured for a rule.
	 *
	 * @param rule The notification rule to verify
	 * @return true if properly configured, false otherwise
	 */
	private boolean verifyNotificationSettings(NotificationRule rule) {
		// Basic validation
		if (rule == null) {
			return false;
		}

		// Check if default recipients are enabled
		boolean settingsEnabled = rule.isDefaultRecipientsEnabled();

		if (!settingsEnabled) {
			return false;
		}

		// Verify the security monitoring email is in the recipients list
		List<String> recipients = rule.getNotificationRecipients();
		if (recipients == null || recipients.isEmpty()) {
			return false;
		}

		return recipients.contains(securityMonitoringEmail);
	}

	/**
	 * Class representing a directory role with additional filtering information
	 */
	/**
	 * Class representing a directory role with additional filtering information
	 */
	private static class RoleInfo {
		private final String id;
		private final String displayName;
		private final String description;
		private final String richDescription;
		private final List<String> categories;

		/**
		 * Constructor for RoleInfo with all properties
		 *
		 * @param id              The unique identifier of the role
		 * @param displayName     The display name of the role
		 * @param description     The description of the role
		 * @param richDescription The rich description of the role
		 * @param categories      List of categories this role belongs to
		 */
		public RoleInfo(String id, String displayName, String description, String richDescription, List<String> categories) {
			this.id = id != null ? id : "";
			this.displayName = displayName != null ? displayName : "";
			this.description = description != null ? description : "";
			this.richDescription = richDescription != null ? richDescription : "";
			this.categories = categories != null ? new ArrayList<>(categories) : new ArrayList<>();
		}

		/**
		 * Simple constructor with just ID and display name
		 *
		 * @param id          The unique identifier of the role
		 * @param displayName The display name of the role
		 */
		public RoleInfo(String id, String displayName) {
			this(id, displayName, "", "", new ArrayList<>());
		}

		/**
		 * Get the role's unique identifier
		 *
		 * @return The role ID
		 */
		public String getId() {
			return id;
		}

		/**
		 * Get the role's display name
		 *
		 * @return The display name
		 */
		public String getDisplayName() {
			return displayName;
		}

		/**
		 * Get the role's description
		 *
		 * @return The description
		 */
		public String getDescription() {
			return description;
		}

		/**
		 * Get the role's rich description
		 *
		 * @return The rich description
		 */
		public String getRichDescription() {
			return richDescription;
		}

		/**
		 * Get the role's categories
		 *
		 * @return List of categories
		 */
		public List<String> getCategories() {
			return new ArrayList<>(categories); // Return a defensive copy
		}

		@Override
		public String toString() {
			return String.format("RoleInfo{id='%s', displayName='%s', categories=%s}",
					id, displayName, categories);
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;

			RoleInfo roleInfo = (RoleInfo) o;
			return id.equals(roleInfo.id);
		}

		@Override
		public int hashCode() {
			return id.hashCode();
		}
	}


	/**
	 * Class representing a group with an assigned role
	 */
	private static class GroupRoleInfo {
		private final String groupId;
		private final String groupName;
		private final String roleId;

		public GroupRoleInfo(String groupId, String groupName, String roleId) {
			this.groupId = groupId;
			this.groupName = groupName;
			this.roleId = roleId;
		}

		public String getGroupId() {
			return groupId;
		}

		public String getGroupName() {
			return groupName;
		}

		public String getRoleId() {
			return roleId;
		}
	}

	private static class NotificationRule {
		private String id;
		private String odataType;
		private String notificationType;
		private String recipientType;
		private String notificationLevel;
		private boolean isDefaultRecipientsEnabled;
		private List<String> notificationRecipients;
		private Target target;

		// Inner class for the target object
		private static class Target {
			private String caller;
			private List<String> operations;
			private String level;
			private List<String> inheritableSettings;
			private List<String> enforcedSettings;

			public String getCaller() {
				return caller;
			}

			public void setCaller(String caller) {
				this.caller = caller;
			}

			public List<String> getOperations() {
				return operations;
			}

			public void setOperations(List<String> operations) {
				this.operations = operations;
			}

			public String getLevel() {
				return level;
			}

			public void setLevel(String level) {
				this.level = level;
			}

			public List<String> getInheritableSettings() {
				return inheritableSettings;
			}

			public void setInheritableSettings(List<String> inheritableSettings) {
				this.inheritableSettings = inheritableSettings;
			}

			public List<String> getEnforcedSettings() {
				return enforcedSettings;
			}

			public void setEnforcedSettings(List<String> enforcedSettings) {
				this.enforcedSettings = enforcedSettings;
			}
		}

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public String getOdataType() {
			return odataType;
		}

		public void setOdataType(String odataType) {
			this.odataType = odataType;
		}

		public String getNotificationType() {
			return notificationType;
		}

		public void setNotificationType(String notificationType) {
			this.notificationType = notificationType;
		}

		public String getRecipientType() {
			return recipientType;
		}

		public void setRecipientType(String recipientType) {
			this.recipientType = recipientType;
		}

		public String getNotificationLevel() {
			return notificationLevel;
		}

		public void setNotificationLevel(String notificationLevel) {
			this.notificationLevel = notificationLevel;
		}

		public boolean isDefaultRecipientsEnabled() {
			return isDefaultRecipientsEnabled;
		}

		public void setIsDefaultRecipientsEnabled(boolean defaultRecipientsEnabled) {
			isDefaultRecipientsEnabled = defaultRecipientsEnabled;
		}

		public List<String> getNotificationRecipients() {
			return notificationRecipients;
		}

		public void setNotificationRecipients(List<String> notificationRecipients) {
			this.notificationRecipients = notificationRecipients;
		}

		public Target getTarget() {
			return target;
		}

		public void setTarget(Target target) {
			this.target = target;
		}

		@Override
		public String toString() {
			return String.format(
					"NotificationRule{id='%s', type='%s', level='%s', defaultEnabled=%s, recipients=%s}",
					id, notificationType, notificationLevel, isDefaultRecipientsEnabled, notificationRecipients
			);
		}
	}
}