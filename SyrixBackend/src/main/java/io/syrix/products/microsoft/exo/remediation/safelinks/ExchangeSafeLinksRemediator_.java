package io.syrix.products.microsoft.exo.remediation.safelinks;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.ExchangeBaseRemediator;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.15.1v1, MS.EXO.15.2v1, and MS.EXO.15.3v1 which configures Exchange Online
 * Safe Links policies according to the CISA M365 Secure Configuration Baseline.
 * <p>
 * This class implements the following security controls:
 * - MS.EXO.15.1v1: Enable URL comparison with block-list
 * - MS.EXO.15.2v1: Enable scanning of direct download links for malware
 * - MS.EXO.15.3v1: Enable user click tracking
 * <p>
 * These controls mitigate MITRE ATT&CK T1566 Phishing attacks, specifically T1566.002 (Spearphishing Link).
 */
@PolicyRemediator("MS.EXO.15.1v1")
public class ExchangeSafeLinksRemediator_ extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
    private static final String CREATE_RULE = "CreateRule:";
    private static final String UPDATE_RULE = "UpdateRule:";
    private static final String CREATE_POLICY = "CreatePolicy:";
    private static final String UPDATE_POLICY = "UpdatePolicy:";
    private static final String ENABLE_RULE = "EnabledRule:";

    // The name to use for our Safe Links policy
    private static final String POLICY_NAME = "SecurityBaselinePolicy(Artur1)"; //TODO Artur move to config
    private static final String RULE_NAME = POLICY_NAME + "-Rule";
    private static final String ADMIN_DISPLAY_NAME = "Security Baseline Safe Links Policy";

    // Field and parameter constants
    public static final String NAME = "Name";
    public static final String IDENTITY = "Identity";
    public static final String ENABLED = "Enabled";
    public static final String PRIORITY = "Priority";
    public static final String RECIPIENT_DOMAIN_IS = "RecipientDomainIs";
    public static final String ALLOW_CLICK_THROUGH = "AllowClickThrough";
    public static final String ENABLE_SAFE_LINKS_FOR_TEAMS = "EnableSafeLinksForTeams";
    public static final String ENABLE_SAFE_LINKS_FOR_OFFICE = "EnableSafeLinksForOffice";
    public static final String ENABLE_FOR_INTERNAL_SENDERS = "EnableForInternalSenders";
    public static final String TRACK_CLICKS = "TrackClicks";
    public static final String SCAN_URLS = "ScanUrls";
    public static final String DELIVER_MESSAGE_AFTER_SCAN = "DeliverMessageAfterScan";
    public static final String ADMIN_DISPLAY_NAME_KEY = "AdminDisplayName";
    public static final String SAFE_LINKS_POLICY = "SafeLinksPolicy";

    // Configure Safe Links policy settings according to security baseline
    private static final Map<String, Object> SAFELINK_POLICY_CREATE = Map.of(
            NAME, POLICY_NAME, // Use consistent policy name
            ALLOW_CLICK_THROUGH, false, // Block access to malicious URLs (MS.EXO.15.1v1)
            ENABLE_SAFE_LINKS_FOR_TEAMS, true, // Enable for Teams
            ENABLE_SAFE_LINKS_FOR_OFFICE, true, // Enable for Office
            ENABLE_FOR_INTERNAL_SENDERS, true, // Enable for Email
            TRACK_CLICKS, true, // Enable user click tracking (MS.EXO.15.3v1)
            SCAN_URLS, true, // Enable URL scanning against block list (MS.EXO.15.1v1)
            DELIVER_MESSAGE_AFTER_SCAN, true, // Enable scanning of direct download links (MS.EXO.15.2v1)
            ADMIN_DISPLAY_NAME_KEY, ADMIN_DISPLAY_NAME
    );

    // Rule doesn't exist, create it
    private static final Map<String, Object> RULE_PARAMS = Map.of(
            NAME, RULE_NAME,
            SAFE_LINKS_POLICY, POLICY_NAME,
            RECIPIENT_DOMAIN_IS, "*", // Apply to all recipients
            ENABLED, true,
            PRIORITY, 0); // High priority

    /**
     * Constructs a new ExchangeSafeLinksRemediator_ with the specified dependencies.
     *
     * @param graphClient          The Microsoft Graph client for Graph API operations
     * @param exchangeOnlineClient The PowerShell client for Exchange Online operations
     * @param configNode           The configuration node containing Exchange data
     * @param remediationContext   The context for remediation operations
     * @param remediationConfig    The configuration for remediation
     */
    public ExchangeSafeLinksRemediator_(
            MicrosoftGraphClient graphClient,
            PowerShellClient exchangeOnlineClient,
            ObjectNode configNode,
            ExchangeRemediationContext remediationContext,
            ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeOnlineClient, configNode, remediationContext, remediationConfig);
    }

    //for rollback
    public ExchangeSafeLinksRemediator_(
            MicrosoftGraphClient graphClient,
            PowerShellClient exchangeOnlineClient) {
        super(graphClient, exchangeOnlineClient, null, null, null);
    }


    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(jsonMapper::valueToTree);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        logger.info("Starting remediation for Safe Links policies (MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1)");

        return configurePolicy().thenCompose(policyResult -> {
            if (policyResult.getResult() != RemediationResult.FAILED) {
                return configureRule()
                        .thenApply(ruleResult -> combineResults_(List.of(policyResult, ruleResult)));
            }
            return CompletableFuture.completedFuture(policyResult);
        });
    }

    //-----------Policy remediate
    private CompletableFuture<PolicyChangeResult> configurePolicy() {
        try {
            SafeLinksPolicy foundPolicy = findSyrixPolicy();
            if (foundPolicy == null) {
                return createPolicy();
            }

            if (isValidPolicy(foundPolicy)) {
                return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Safe Links policy is already configured"));
            }
            return updatePolicy(foundPolicy);
        } catch (Exception ex) {
            logger.error("Exception during Safe Links policy configure", ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }

    private CompletableFuture<PolicyChangeResult> createPolicy() {
        logger.info("Creating Safe Links policy: {}", POLICY_NAME);
        Map<String, Object> parameters = new HashMap<>(SAFELINK_POLICY_CREATE);

        ParameterChangeResult change = new ParameterChangeResult().parameter(CREATE_POLICY + POLICY_NAME);
        return runCommand("New-SafeLinksPolicy", parameters, change);
    }

    private CompletableFuture<PolicyChangeResult> updatePolicy(SafeLinksPolicy foundPolicy) {
        logger.info("Updating Safe Links policy: {}", POLICY_NAME);

        Map<String, Object> parameters = new HashMap<>(SAFELINK_POLICY_CREATE);
        parameters.remove(NAME); // Don't need name for update
        parameters.put(IDENTITY, foundPolicy.name);

        ParameterChangeResult change = new ParameterChangeResult()
                .parameter(UPDATE_POLICY + POLICY_NAME)
                .prevValue(toParameters(foundPolicy));

        return runCommand("Set-SafeLinksPolicy", parameters, change);
    }

    //-----------Rule remediate
    private CompletableFuture<PolicyChangeResult> configureRule() {
        try {
            SafeLinksRule foundRule = findSyrixRule();
            if (foundRule == null) {
                return createRule();
            }

            if (isValidRule(foundRule)) {
                return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Safe Links rule is already configured"));
            }

            return updateRule(foundRule).thenCompose(updateResult -> {
                if (updateResult.getResult() != RemediationResult.FAILED) {
                    return enableRule(foundRule).thenApply(enableResult -> combineResults_(List.of(updateResult, enableResult)));
                }
                return CompletableFuture.completedFuture(updateResult);
            });
        } catch (Exception ex) {
            logger.error("Exception during Safe Links rule configure", ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }

    private CompletableFuture<PolicyChangeResult> createRule() {
        logger.info("Creating Safe Links rule: {}", RULE_NAME);

        Map<String, Object> parameters = new HashMap<>(RULE_PARAMS);

        ParameterChangeResult change = new ParameterChangeResult().parameter(CREATE_RULE + RULE_NAME);

        return runCommand("New-SafeLinksRule", parameters, change);
    }

    private CompletableFuture<PolicyChangeResult> updateRule(SafeLinksRule foundRule) {
        if (!isRuleUpdateNeeded(foundRule)) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Safe Links rule parameters are already valid"));
        }
        
        logger.info("Updating Safe Links rule: {}", RULE_NAME);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IDENTITY, RULE_NAME);
        parameters.put(PRIORITY, 0);
        parameters.put(RECIPIENT_DOMAIN_IS, "*");
//        parameters.put(SAFE_LINKS_POLICY, POLICY_NAME);

        ParameterChangeResult change = new ParameterChangeResult()
                .parameter(UPDATE_RULE + RULE_NAME)
                .prevValue(toParameters(foundRule));

        return runCommand("Set-SafeLinksRule", parameters, change);
    }

    //----------- Command
    private CompletableFuture<PolicyChangeResult> runCommand(String command, Map<String, Object> parameters, ParameterChangeResult change) {
        logger.info("run command: {}", command);

        return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(command, parameters))
                .thenApply(result -> {
                    logger.info("Successfully command: {}", command);
                    logger.trace("Successfully command: {}, result:{}", command, result);
                    timeOutIfNewCommand(command);
                    change.status(ParameterChangeStatus.SUCCESS).newValue(parameters);
                    return IPolicyRemediator.success_(getPolicyId(), "Successfully command: " + command, List.of(change));
                }).exceptionally(ex -> {
                    logger.error("Failed to execute command: {}", command, ex);
                    change.status(ParameterChangeStatus.FAILED).newValue(parameters);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to execute command: " + command + " " + ex.getMessage(), List.of(change));
                });
    }

    /**
     * Combines multiple PolicyChangeResult objects into a single result with combined status and changes.
     */
    private PolicyChangeResult combineResults_(List<PolicyChangeResult> results) {
        List<ParameterChangeResult> allChanges = results.stream()
                .map(PolicyChangeResult::getChanges)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .toList();

        // Count results by type
        long successCount = results.stream().filter(r -> r.getResult() == RemediationResult.SUCCESS).count();
        long failedCount = results.stream().filter(r -> r.getResult() == RemediationResult.FAILED).count();
        long requirementMetCount = results.stream().filter(r -> r.getResult() == RemediationResult.REQUIREMENT_MET).count();
        long partialSuccessCount = results.stream().filter(r -> r.getResult() == RemediationResult.PARTIAL_SUCCESS).count();

        // Requirement Met: If all parameters didn't require changes
        if (requirementMetCount == results.size()) {
            return IPolicyRemediator.requirementMet_(getPolicyId(), "No changes were required");
        }
        // Failed: If all policies failed
        else if (failedCount == results.size()) {
            return IPolicyRemediator.failed_(getPolicyId(), "All changes failed", allChanges);
        }
        // Success: If all changes are successful or some didn't need changes
        else if (failedCount == 0 && partialSuccessCount == 0 && (successCount > 0) && (successCount + requirementMetCount == results.size())) {
            return IPolicyRemediator.success_(getPolicyId(), "All changes successful or not required", allChanges);
        }
        // Partial Success: If there were changes, but not all were successful
        else if (successCount > 0 && (failedCount > 0 || partialSuccessCount > 0)) {
            return IPolicyRemediator.partial_success_(getPolicyId(), "Some changes were successful, but not all", allChanges);
        }
        // Unknown: In all other cases
        else {
            return IPolicyRemediator.unknown_(getPolicyId(), "Unable to determine overall status", allChanges);
        }
    }

    private SafeLinksPolicy findSyrixPolicy() {
        return loadSafeLinksPolicies().stream()
                .filter(policy -> POLICY_NAME.equals(policy.name))
                .findFirst()
                .orElse(null);
    }

    private SafeLinksRule findSyrixRule() {
        return loadSafeLinksRules().stream()
                .filter(rule -> rule.name.equals(RULE_NAME))
                .filter(rule -> rule.safeLinksPolicy.equals(POLICY_NAME))
                .findFirst()
                .orElse(null);
    }

    /**
     * Loads Safe Links policies from configuration JSON.
     *
     * @return List of Safe Links policies or empty list if none found
     */
    private List<SafeLinksPolicy> loadSafeLinksPolicies() {
        if (configNode == null) {
            logger.warn("Config node is null, returning empty list of Safe Links Policies");
            return List.of();
        }

        JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_SAFE_LINKS_POLICY);
        if (config == null || !config.isArray()) {
            logger.warn("Safe Links policy '{}' node not found or has invalid format", ExoConstants.CONFIG_KEY_SAFE_LINKS_POLICY);
            return List.of();
        }

        CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, SafeLinksPolicy.class);
        return jsonMapper.convertValue(config, collectionType);
    }

    /**
     * Loads Safe Links rules from configuration JSON.
     *
     * @return List of Safe Links rules or empty list if none found
     */
    private List<SafeLinksRule> loadSafeLinksRules() {
        if (configNode == null) {
            logger.warn("Config node is null, returning empty list of Safe Links Rules");
            return List.of();
        }

        // Assuming the rules are stored under the same key as policies
        // If there's a separate key for rules, this would need to be updated
        JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_SAFE_LINKS_RULE);
        if (config == null || !config.isArray()) {
            logger.warn("Safe Links rules node not found or has invalid format");
            return List.of();
        }

        CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, SafeLinksRule.class);
        return jsonMapper.convertValue(config, collectionType);
    }

    /**
     * Checks if a Safe Links policy is already compliant with security requirements.
     */
    private boolean isValidPolicy(SafeLinksPolicy policy) {
        boolean isValid = !policy.allowClickThrough; // MS.EXO.15.1v1: Block access to malicious URLs
        isValid &= policy.enableSafeLinksForTeams;
        isValid &= policy.enableSafeLinksForOffice;
        isValid &= policy.enableForInternalSenders;
        isValid &= policy.trackClicks; // MS.EXO.15.3v1: Enable user click tracking
        isValid &= policy.scanUrls; // MS.EXO.15.1v1: Enable URL scanning against block list
        isValid &= policy.deliverMessageAfterScan; // MS.EXO.15.2v1: Enable scanning of direct download links
        isValid &= ADMIN_DISPLAY_NAME.equals(policy.adminDisplayName);

        return isValid;
    }

    /**
     * Checks if a Safe Links rule is already compliant with security requirements.
     */
    private boolean isValidRule(SafeLinksRule rule) {
        boolean isValid = POLICY_NAME.equals(rule.safeLinksPolicy);
        isValid &= "Enabled".equals(rule.state);
        isValid &= rule.priority == 0;
        isValid &= rule.recipientDomainIs != null && rule.recipientDomainIs.contains("*");

        return isValid;
    }
    
    /**
     * Checks if a rule needs to be updated (excluding its enabled state).
     */
    private boolean isRuleUpdateNeeded(SafeLinksRule rule) {
        return !POLICY_NAME.equals(rule.safeLinksPolicy) || 
               rule.priority != 0 || 
               rule.recipientDomainIs == null || 
               !rule.recipientDomainIs.contains("*");
    }
    
    /**
     * Checks if a rule needs to be enabled.
     */
    private boolean isRuleEnableNeeded(SafeLinksRule rule) {
        return !"Enabled".equals(rule.state);
    }
    
    /**
     * Enables a Safe Links rule that is disabled.
     */
    private CompletableFuture<PolicyChangeResult> enableRule(SafeLinksRule foundRule) {
        if (!isRuleEnableNeeded(foundRule)) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Safe Links rule is already enabled"));
        }
        
        logger.info("Enabling Safe Links rule: {}", RULE_NAME);
        Map<String, Object> parameters = Map.of(IDENTITY, RULE_NAME);
        ParameterChangeResult change = new ParameterChangeResult()
                .parameter(ENABLE_RULE + RULE_NAME)
                .prevValue(toParameters(foundRule));

        return runCommand("Enable-SafeLinksRule", parameters, change);
    }

    /**
     * Converts a SafeLinksPolicy object to a map of parameters for PowerShell commands.
     */
    private Map<String, Object> toParameters(SafeLinksPolicy policy) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IDENTITY, policy.name);
        parameters.put(ALLOW_CLICK_THROUGH, policy.allowClickThrough);
        parameters.put(ENABLE_SAFE_LINKS_FOR_TEAMS, policy.enableSafeLinksForTeams);
        parameters.put(ENABLE_SAFE_LINKS_FOR_OFFICE, policy.enableSafeLinksForOffice);
        parameters.put(ENABLE_FOR_INTERNAL_SENDERS, policy.enableForInternalSenders);
        parameters.put(TRACK_CLICKS, policy.trackClicks);
        parameters.put(SCAN_URLS, policy.scanUrls);
        parameters.put(DELIVER_MESSAGE_AFTER_SCAN, policy.deliverMessageAfterScan);
        parameters.put(ADMIN_DISPLAY_NAME_KEY, policy.adminDisplayName);
        return parameters;
    }

    /**
     * Converts a SafeLinksRule object to a map of parameters for PowerShell commands.
     */
    private Map<String, Object> toParameters(SafeLinksRule rule) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IDENTITY, rule.name);
        parameters.put(SAFE_LINKS_POLICY, rule.safeLinksPolicy);
        parameters.put(PRIORITY, rule.priority);
        parameters.put(RECIPIENT_DOMAIN_IS, rule.recipientDomainIs);
        parameters.put(ENABLED, "Enabled".equals(rule.state));
        return parameters;
    }

    private void timeOutIfNewCommand(String command) {
        try {
            if (command.startsWith("New-")) {
                logger.info("Time out for 10 seconds to allow for policy creation");
                Thread.sleep(10 * 1000);
                logger.info("Time out for new command completed");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Thread interrupted during timeout", e);
        }
    }

    /**
     * Implements the rollback functionality for Safe Links policy changes.
     * This method follows the reverse order of the remediation process:
     * first rolling back rules, then policies.
     *
     * @param fixResult The result of the original remediation operation containing the changes to roll back
     * @return A CompletableFuture containing the result of the rollback operation
     */
    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        logger.info("Starting rollback for Safe Links policy changes (MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1)");

        if (fixResult == null) {
            logger.warn("Cannot rollback null policy changes");
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Cannot rollback null policy changes"));
        }

        List<ParameterChangeResult> changes = fixResult.getChanges();
        if (changes == null || changes.isEmpty()) {
            logger.warn("No parameter changes found in the fixResult, nothing to rollback");
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No parameter changes found in the fixResult, nothing to rollback"));
        }

        final ParameterChangeResult enableRuleChange = changes.stream()
                .filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
                .filter(changeResult -> changeResult.getParameter().startsWith(ENABLE_RULE))
                .findFirst()
                .orElse(null);

        final ParameterChangeResult ruleChange = changes.stream()
                .filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
                .filter(changeResult -> changeResult.getParameter().startsWith(CREATE_RULE) || changeResult.getParameter().startsWith(UPDATE_RULE))
                .findFirst()
                .orElse(null);

        final ParameterChangeResult policyChange = changes.stream()
                .filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
                .filter(changeResult -> changeResult.getParameter().startsWith(CREATE_POLICY) || changeResult.getParameter().startsWith(UPDATE_POLICY))
                .findFirst()
                .orElse(null);

        return disableSafeLinksRule(enableRuleChange).thenCompose(disableResult -> {
            if (disableResult.getResult() != RemediationResult.FAILED) {
                return rollbackRule(ruleChange).thenCompose(ruleResult -> {
                    if (ruleResult.getResult() != RemediationResult.FAILED) {
                        return rollbackPolicy(policyChange).thenCompose(policyResult ->
                                CompletableFuture.completedFuture(combineResults_(List.of(disableResult, ruleResult, policyResult))));
                    } else {
                        return CompletableFuture.completedFuture(combineResults_(List.of(disableResult, ruleResult)));
                    }
                });
            }
            return CompletableFuture.completedFuture(disableResult);
        });
    }

    /**
     * Disables a Safe Links rule as part of rollback.
     *
     * @param originalChange The original change result containing enable rule information
     * @return A CompletableFuture containing the result of the disable operation
     */
    private CompletableFuture<PolicyChangeResult> disableSafeLinksRule(ParameterChangeResult originalChange) {
        if (originalChange == null) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
        }
        String ruleName = originalChange.getParameter().substring(ENABLE_RULE.length());
        logger.info("Rolling back Safe Links rule enable: {}", ruleName);

        Map<String, Object> parameters = Map.of(IDENTITY, ruleName);
        ParameterChangeResult change = new ParameterChangeResult()
                .parameter("DisabledRule:" + ruleName)
                .prevValue(originalChange.getNewValue());

        return runCommand("Disable-SafeLinksRule", parameters, change);
    }

    /**
     * Rolls back policy changes, either by removing a newly created policy or restoring an updated policy to its previous state.
     *
     * @param originalChange The original change result containing policy information
     * @return A CompletableFuture containing the result of the rollback operation
     */
    private CompletableFuture<PolicyChangeResult> rollbackPolicy(ParameterChangeResult originalChange) {
        if (originalChange == null) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
        }
        String parameter = originalChange.getParameter();

        if (parameter.startsWith(CREATE_POLICY)) {
            return removePolicy(originalChange);
        } else if (parameter.startsWith(UPDATE_POLICY)) {
            return rollbackUpdatePolicy(originalChange);
        }
        return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Unknown policy change operation: " + parameter));
    }

    /**
     * Rolls back rule changes, either by removing a newly created rule or restoring an updated rule to its previous state.
     *
     * @param originalChange The original change result containing rule information
     * @return A CompletableFuture containing the result of the rollback operation
     */
    private CompletableFuture<PolicyChangeResult> rollbackRule(ParameterChangeResult originalChange) {
        if (originalChange == null) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
        }

        if (originalChange.getParameter().startsWith(CREATE_RULE)) {
            return removeRule(originalChange);
        } else if (originalChange.getParameter().startsWith(UPDATE_RULE)) {
            return rollbackUpdateRule(originalChange);
        }
        return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Unknown rule change operation: " + originalChange.getParameter()));
    }

    /**
     * Restores a policy to its previous state before update.
     *
     * @param originalChange The original change result containing the policy's previous state
     * @return A CompletableFuture containing the result of the restore operation
     */
    private CompletableFuture<PolicyChangeResult> rollbackUpdatePolicy(ParameterChangeResult originalChange) {
        String policyName = originalChange.getParameter().substring(UPDATE_POLICY.length());
        logger.info("Rolling back policy change for: {}", policyName);

        ParameterChangeResult rollbackChange = new ParameterChangeResult()
                .parameter(UPDATE_POLICY + policyName)
                .prevValue(originalChange.getNewValue());
        @SuppressWarnings("unchecked")
        Map<String, Object> parameters = (Map<String, Object>) originalChange.getPrevValue();

        return runCommand("Set-SafeLinksPolicy", parameters, rollbackChange);
    }

    /**
     * Removes a newly created policy as part of rollback.
     *
     * @param originalChange The original change result containing the policy creation information
     * @return A CompletableFuture containing the result of the remove operation
     */
    private CompletableFuture<PolicyChangeResult> removePolicy(ParameterChangeResult originalChange) {
        String policyName = originalChange.getParameter().substring(CREATE_POLICY.length());

        logger.info("Rolling back by removing Safe Links policy: {}", policyName);

        ParameterChangeResult rollbackChange = new ParameterChangeResult()
                .parameter("RemovePolicy:" + policyName)
                .prevValue(originalChange.getNewValue());

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IDENTITY, policyName);
        parameters.put("Confirm", false);

        return runCommand("Remove-SafeLinksPolicy", parameters, rollbackChange);
    }

    /**
     * Removes a newly created rule as part of rollback.
     *
     * @param originalChange The original change result containing the rule creation information
     * @return A CompletableFuture containing the result of the remove operation
     */
    private CompletableFuture<PolicyChangeResult> removeRule(ParameterChangeResult originalChange) {
        String ruleName = originalChange.getParameter().substring(CREATE_RULE.length());
        logger.info("Rolling back by removing Safe Links rule: {}", ruleName);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IDENTITY, ruleName);
        parameters.put("Confirm", false);

        ParameterChangeResult rollbackChange = new ParameterChangeResult()
                .parameter("RemoveRule:" + ruleName)
                .prevValue(originalChange.getNewValue());

        return runCommand("Remove-SafeLinksRule", parameters, rollbackChange);
    }

    /**
     * Restores a rule to its previous state before update.
     *
     * @param originalChange The original change result containing the rule's previous state
     * @return A CompletableFuture containing the result of the restore operation
     */
    private CompletableFuture<PolicyChangeResult> rollbackUpdateRule(ParameterChangeResult originalChange) {
        String ruleName = originalChange.getParameter().substring(UPDATE_RULE.length());
        logger.info("Rolling back rule update for: {}", ruleName);

        @SuppressWarnings("unchecked")
        Map<String, Object> parameters = (Map<String, Object>) originalChange.getPrevValue();
        parameters.put(IDENTITY, ruleName);
        ParameterChangeResult rollbackChange = new ParameterChangeResult()
                .parameter(UPDATE_RULE + ruleName)
                .prevValue(originalChange.getNewValue());

        return runCommand("Set-SafeLinksRule", parameters, rollbackChange);
    }

    /**
     * Helper class to represent a Safe Links policy in JSON configuration.
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    protected static class SafeLinksPolicy {
        public String name;
        public String identity;
        
        // MS.EXO.15.1v1: URL comparison with block-list
        public Boolean allowClickThrough;
        public Boolean scanUrls;
        
        // MS.EXO.15.2v1: Scanning of direct download links
        public Boolean deliverMessageAfterScan;
        
        // MS.EXO.15.3v1: User click tracking
        public Boolean trackClicks;
        
        // Additional settings
        public Boolean enableSafeLinksForTeams;
        public Boolean enableSafeLinksForOffice;
        public Boolean enableForInternalSenders;
        public String adminDisplayName;
    }

    /**
     * Helper class to represent a Safe Links rule in JSON configuration.
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    protected static class SafeLinksRule {
        public String name;
        public String safeLinksPolicy;
        public String state;
        public int priority;
        public List<String> recipientDomainIs;
    }
}