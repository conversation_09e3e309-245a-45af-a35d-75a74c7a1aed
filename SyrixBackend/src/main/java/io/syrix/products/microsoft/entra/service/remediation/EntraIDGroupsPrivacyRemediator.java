package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.service.Common;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.APPLICATION_JSON;
import static io.syrix.common.constants.Constants.CONTENT_TYPE_HEADER;
import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.GROUPS_ENDPOINT;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.MESSAGE_FIELD;

/**
 * Remediator for CIS control 1.2.1:
 * Ensure that only organizationally managed/approved public groups exist.

 * This class implements two flows:
 * 1. Audit Flow: Checks if existing Microsoft 365 Groups comply with the "private" visibility
 *    requirement and reports on non-compliant groups.
 * 2. Remediation Flow:
 *    a. Corrects the visibility of existing non-compliant groups.
 *    b. Sets the default visibility for newly created groups to "Private".
 */
@PolicyRemediator("MS.AAD.8.4v1")
public class EntraIDGroupsPrivacyRemediator extends RemediatorBase {
	public static final String PUBLIC_GROUPS_FOUND = "public_groups_found";
	private static final Logger logger = LoggerFactory.getLogger(EntraIDGroupsPrivacyRemediator.class);
	private static final String CONTROL_TITLE = "Ensure that only organizationally managed/approved public groups exist";
	private static final String GROUP_UNIFIED_TEMPLATE_ID = "62375ab9-6b52-47ed-826b-58e47e0e304b";

	// Settings constants
	private static final String SETTING_VISIBILITY_VALUE_PRIVATE = "Private";
	private static final String SETTING_LOOP_NAME = "EnableMSStandardLoop";
	private static final String SETTING_LOOP_VALUE = "false";
	private static final String TEMPLATE_ID = "templateId";
	public static final String SUCCESSFULLY_UPDATED_GROUP_TO_PRIVATE = "Successfully updated group {} to Private";
	public static final String FAILED_TO_UPDATE_GROUP = "Failed to update group {}: {}";
	public static final String STATISTICS = "statistics";
	public static final String COMPLIANT = "compliant";
	public static final String SUCCESS = "success";
	public static final String TITLE = "title";
	public static final String PUBLIC_GROUPS_REMAINING = "public_groups_remaining";
	public static final String PUBLIC_GROUPS_CONVERTED = "public_groups_converted";
	public static final String FINAL_COMPLIANCE = "final_compliance";
	public static final String INITIAL_COMPLIANCE = "initial_compliance";
	public static final String CONTROL_ID = "control_id";
	public static final String NO_PUBLIC_GROUPS_TO_UPDATE = "No public groups to update";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	/**
	 * Constructs a new remediator for CIS control 1.2.1.
	 *
	 * @param graphClient The Microsoft Graph client
	 */
	public EntraIDGroupsPrivacyRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}


	@Override
	public CompletableFuture<JsonNode> remediate() {
		return CompletableFuture.supplyAsync(() -> {
			try {
				logger.info("Starting remediation for control: {}", getPolicyId());

				// We'll track results and statistics here
				ObjectNode resultNode = objectMapper.createObjectNode();
				ObjectNode statsNode = objectMapper.createObjectNode();

				// First perform an audit to find all public groups
				JsonNode auditResult = Common.auditAndExportPublicM365Groups(logger, objectMapper, graphClient).join();
				boolean initiallyCompliant = auditResult.get(COMPLIANT).asBoolean();
				int publicGroupsCount = auditResult.get(STATISTICS).get(PUBLIC_GROUPS_FOUND).asInt();

				// If already compliant, we just need to ensure default setting is correct
				if (initiallyCompliant) {
					logger.info("No public groups found.");

					// Build the result
					statsNode.put(INITIAL_COMPLIANCE, true);
					statsNode.put(PUBLIC_GROUPS_CONVERTED, 0);
					resultNode.put(CONTROL_ID, getPolicyId());
					resultNode.put(TITLE, CONTROL_TITLE);
					resultNode.put(SUCCESS, true);
					resultNode.set(STATISTICS, statsNode);
					resultNode.put(MESSAGE_FIELD, "Control already compliant.");
					return resultNode;
				}

				// Otherwise we need to remediate
				logger.info("Found {} public groups to remediate", publicGroupsCount);

				// Part 2: Update existing public groups to "Private"
				CompletableFuture<Integer> updateGroupsFuture = updatePublicGroupsToPrivate(auditResult);
				Integer groupsUpdated = updateGroupsFuture.join();

				// Verify remediation with another audit
				JsonNode postRemediationAudit =Common.auditAndExportPublicM365Groups(logger, objectMapper, graphClient).join();
				boolean nowCompliant = postRemediationAudit.get(COMPLIANT).asBoolean();

				// Update statistics
				statsNode.put(INITIAL_COMPLIANCE, initiallyCompliant);
				statsNode.put(FINAL_COMPLIANCE, nowCompliant);
				statsNode.put(PUBLIC_GROUPS_CONVERTED, groupsUpdated);
				statsNode.put(PUBLIC_GROUPS_REMAINING,
						postRemediationAudit.get(STATISTICS).get(PUBLIC_GROUPS_FOUND).asInt());

				// Build the result
				resultNode.put(CONTROL_ID, CONTROL_ID);
				resultNode.put(SUCCESS, initiallyCompliant == nowCompliant);
				resultNode.put(TITLE, CONTROL_TITLE);
				resultNode.set(STATISTICS, statsNode);

				resultNode.put(MESSAGE_FIELD, String.format("Successfully updated %d existing public groups.", groupsUpdated));
				return resultNode;
			} catch (Exception e) {
				logger.error("Error during remediation: {}", e.getMessage(), e);
				return createFailureNode("Failed to remediate: " + e.getMessage());
			}
		});
	}

	/**
	 * Updates all existing public Microsoft 365 Groups to make them private.
	 *
	 * @param auditResult The audit result containing public groups information
	 * @return CompletableFuture that resolves to the number of groups updated, or -1 if failed
	 */
	private CompletableFuture<Integer> updatePublicGroupsToPrivate(JsonNode auditResult) {
		try {
			logger.info("Updating public M365 groups to Private");

			if (!auditResult.has("public_groups") || !auditResult.get("public_groups").isArray()) {
				logger.warn("No public groups found in audit result");
				return CompletableFuture.completedFuture(0);
			}

			ArrayNode publicGroups = (ArrayNode) auditResult.get("public_groups");
			if (publicGroups.isEmpty()) {
				logger.info(NO_PUBLIC_GROUPS_TO_UPDATE);
				return CompletableFuture.completedFuture(0);
			}

			logger.info("Found {} public groups to update", publicGroups.size());

			// Update each group
			List<CompletableFuture<Boolean>> updateFutures = new ArrayList<>();
			for (JsonNode group : publicGroups) {
				String groupId = group.get(ID_FIELD).asText();
				String groupName = group.has(DISPLAY_NAME_FIELD) ?
						group.get(DISPLAY_NAME_FIELD).asText() : "[Unknown]";

				// Create the request to update group visibility
				ObjectNode requestBody = objectMapper.createObjectNode();
				requestBody.put(EntraIDConstants.VISIBILITY_PROPERTY, SETTING_VISIBILITY_VALUE_PRIVATE);

				GraphRequest request = GraphRequest.builder()
						.v1()
						.withEndpoint(GROUPS_ENDPOINT + "/" + groupId)
						.withMethod(HttpMethod.PATCH)
						.withBody(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
						.addHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON)
						.build();

				updateFutures.add(
						graphClient.makeGraphRequest(request)
								.thenApply(response -> {
									logger.info(SUCCESSFULLY_UPDATED_GROUP_TO_PRIVATE, groupName);
									return true;
								})
								.exceptionally(e -> {
									logger.error(FAILED_TO_UPDATE_GROUP, groupName, e.getMessage(), e);
									return false;
								})
				);
			}

			// Wait for all updates to complete
			return CompletableFuture.allOf(
					updateFutures.toArray(new CompletableFuture[0])
			).thenApply(v -> {
				// Count successful updates
				long successCount = updateFutures.stream()
						.map(CompletableFuture::join)
						.filter(success -> success)
						.count();

				return (int) successCount;
			});
		} catch (Exception e) {
			logger.error("Failed to update public groups: {}", e.getMessage(), e);
			return CompletableFuture.completedFuture(-1);
		}
	}

	/**
	 * Updates a group's visibility to private.
	 *
	 * @param groupId The ID of the group to update
	 * @param groupName The display name of the group (for logging)
	 * @return CompletableFuture that resolves to true if successful
	 */
	private CompletableFuture<Boolean> updateGroupVisibility(String groupId, String groupName) {
		logger.info("Updating group {} ({}) to Private visibility", groupName, groupId);

		// Create the request body
		ObjectNode requestBody = objectMapper.createObjectNode();
		requestBody.put("visibility", SETTING_VISIBILITY_VALUE_PRIVATE);

		// Use the Graph client to update group
		return graphClient.updateGroup(groupId, requestBody)
				.thenApply(response -> {
					// For PATCH, a 204 No Content response is success
					logger.info(SUCCESSFULLY_UPDATED_GROUP_TO_PRIVATE, groupName);
					return true;
				}).exceptionally(e -> {
					logger.error(FAILED_TO_UPDATE_GROUP, groupName, e.getMessage(), e);
					return false;
				});
	}

	/**
	 * Creates a failure result node for error reporting.
	 *
	 * @param message The error message to include
	 * @return A JsonNode representing the failure
	 */
	private JsonNode createFailureNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(CONTROL_ID, CONTROL_ID);
		node.put(TITLE, CONTROL_TITLE);
		node.put(SUCCESS, false);
		node.put(MESSAGE_FIELD, message);

		// Add some statistics even on failure
		ObjectNode statsNode = objectMapper.createObjectNode();
		statsNode.put(PUBLIC_GROUPS_CONVERTED, 0);
		statsNode.put(INITIAL_COMPLIANCE, false);
		statsNode.put(FINAL_COMPLIANCE, false);
		node.set(STATISTICS, statsNode);

		return node;
	}
}