package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationStrategy;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.AutoAdmittedUsers;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

//user have 3 options:
//        "EveryoneInCompany",
//        "EveryoneInSameAndFederatedCompany",
//        "EveryoneInCompanyExcludingGuests"
@PolicyRemediator("MS.TEAMS.1.4v1")
public class TeamsAutoAdmittedUsersRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAutoAdmittedUsersRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAutoAdmittedUsersRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			logger.error("No meeting policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found in configuration"));
		}

		TeamsMeetingPolicy globalPolicy = policies.stream()
				.filter(p -> TeamsConstants.GLOBAL_POLICY_IDENTITY.equals(p.identity))
				.findFirst()
				.orElse(null);

		if (globalPolicy == null) {
			logger.error("Global policy not found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Global policy not found"));
		}

		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
		meetingPolicy.autoAdmittedUsers = selectautoAdmittedUsers(remediationConfig.getAutoAdmittedUsersStrategy());

		ParameterChangeResult autoAdmitChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("AutoAdmittedUsers")
				.prevValue(globalPolicy.autoAdmittedUsers)
				.newValue(meetingPolicy.autoAdmittedUsers.asString());

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(jsonNode -> {
					autoAdmitChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.AUTO_ADMITTED_USERS_SUCCESS_MESSAGE, List.of(autoAdmitChange));
				})
				.exceptionally(ex -> {
					autoAdmitChange.status(ParameterChangeStatus.FAILED);
					logger.error("Exception during auto admitted users remediation", ex);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during auto admitted users remediation:" + ex.getMessage(), List.of(autoAdmitChange));
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
			AutoAdmittedUsers autoAdmittedUsers = AutoAdmittedUsers.fromString(changeResult.getPrevValue().toString());
			String prevAutoAdmittedUsers = changeResult.getNewValue().toString();

			MeetingPolicy meetingPolicy = new MeetingPolicy();
			meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
			meetingPolicy.autoAdmittedUsers = autoAdmittedUsers;

			ParameterChangeResult autoAdmitChange = new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter("AutoAdmittedUsers")
					.prevValue(prevAutoAdmittedUsers)
					.newValue(meetingPolicy.autoAdmittedUsers.asString());

			return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
					.thenApply(jsonNode -> {
						autoAdmitChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back auto admitted users policy", List.of(autoAdmitChange));
					})
					.exceptionally(ex -> {
						autoAdmitChange.status(ParameterChangeStatus.FAILED);
						logger.error("Exception during auto admitted users policy rollback", ex);
						return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(autoAdmitChange));
					});
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private static AutoAdmittedUsers selectautoAdmittedUsers(TeamsRemediationStrategy strategy) {
		return switch (strategy) {
			case EVERYONE_IN_COMPANY -> AutoAdmittedUsers.EVERYONE_IN_COMPANY;
			case EVERYONE_IN_SAME_AND_FEDERATED_COMPANY -> AutoAdmittedUsers.EVERYONE_IN_SAME_AND_FEDERATED_COMPANY;
			case EVERYONE_IN_COMPANY_EXCLUDING_GUESTS -> AutoAdmittedUsers.EVERYONE_IN_COMPANY_EXCLUDING_GUESTS;
		};
	}

}
