package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamAppPermissionPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.CatalogAppsType;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@PolicyRemediator("MS.TEAMS.5.1v1")
public class TeamsDefaultCatalogAppsTypeRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {
	public TeamsDefaultCatalogAppsTypeRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsDefaultCatalogAppsTypeRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamAppPermissionPolicy> teamAppPermissionPolicies = getTeamsAppPermissionPolicies();
		List<CompletableFuture<PolicyChangeResult>> results = teamAppPermissionPolicies.stream()
				.filter(policy -> policy.defaultCatalogAppsType != null)
				.filter(policy -> CatalogAppsType.BLOCKED_APP_LIST.asString().equals(policy.defaultCatalogAppsType))
				.map(this::fixConfig)
				.collect(Collectors.toList());

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(TeamAppPermissionPolicy policy) {
		AppPermissionPolicy appPermissionPolicy = new AppPermissionPolicy();
		appPermissionPolicy.identity = policy.identity;
		appPermissionPolicy.defaultCatalogAppsType = CatalogAppsType.ALLOWED_APP_LIST;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("defaultCatalogAppsType: " + policy.identity)
				.prevValue(policy.defaultCatalogAppsType)
				.newValue(appPermissionPolicy.defaultCatalogAppsType.asString());

		return client.execute(CsTeamsCommand.CsTeamsAppPermissionPolicy.SET(appPermissionPolicy))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Teams App Permission Policy: " + appPermissionPolicy.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Teams App Permission Policy: {} , errMsg: {}", appPermissionPolicy.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Teams App Permission Policy '" + appPermissionPolicy.identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.CATALOG_APPS_TYPE_SUCCESS_MESSAGE, allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any app permission policies", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " app permission policies, failed to fix " + failedCount + " policies",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				CatalogAppsType defaultCatalogAppsType = CatalogAppsType.fromString(change.getPrevValue().toString());
				String prevDefaultCatalogAppsType = change.getNewValue().toString();

				AppPermissionPolicy appPermissionPolicy = new AppPermissionPolicy();
				appPermissionPolicy.identity = identity;
				appPermissionPolicy.defaultCatalogAppsType = defaultCatalogAppsType;

				ParameterChangeResult paramChange = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("defaultCatalogAppsType: " + identity)
						.prevValue(prevDefaultCatalogAppsType)
						.newValue(defaultCatalogAppsType.asString());

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					paramChange.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsAppPermissionPolicy.SET(appPermissionPolicy))
						.thenApply(jsonNode -> {
							paramChange.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back default catalog apps type policy", List.of(paramChange));
						})
						.exceptionally(ex -> {
							paramChange.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during default catalog apps type policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
						});

				results.add(result);
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
