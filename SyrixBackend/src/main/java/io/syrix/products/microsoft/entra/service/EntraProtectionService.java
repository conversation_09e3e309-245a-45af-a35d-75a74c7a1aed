package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.products.microsoft.base.BaseService;
import io.syrix.protocols.client.MicrosoftGraphClient;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

public class EntraProtectionService extends BaseService {


    public EntraProtectionService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
    }


    public CompletableFuture<JsonNode> listRiskyDetections(Instant fromDateTime) {
        return listWithFilterByDate("/identityProtection/riskDetections", fromDateTime,
                "activityDateTime", "listRiskDetection");
    }

    public CompletableFuture<JsonNode> listRiskyUsers(Instant fromDateTime) {
        return listWithFilterByDate("/identityProtection/riskyUsers", fromDateTime,
                "riskLastUpdatedDateTime", "listRiskyUsers");
    }

    public CompletableFuture<JsonNode> listServicePrincipalRiskDetection(Instant fromDateTime) {
        return listWithFilterByDate("/identityProtection/servicePrincipalRiskDetections", fromDateTime,
                "activityDateTime","listServicePrincipalRiskDetection");
    }

    public CompletableFuture<JsonNode> listRiskyServicePrincipal(Instant fromDateTime) {
        return listWithFilterByDate("/identityProtection/riskyServicePrincipals", fromDateTime,
                "riskLastUpdatedDateTime","listRiskyServicePrincipal");
    }
}
