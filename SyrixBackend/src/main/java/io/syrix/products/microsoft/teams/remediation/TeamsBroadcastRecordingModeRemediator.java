package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.teams.TeamsBroadcastRecordingStrategy;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingBroadcastPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.BroadcastRecordingMode;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingBroadcastPolicy;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.7v1")
public class TeamsBroadcastRecordingModeRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsBroadcastRecordingModeRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsBroadcastRecordingModeRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingBroadcastPolicy> policies = getBroadcastPolicies();

		if (policies.isEmpty()) {
			logger.error("No broadcast policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No broadcast policies found in configuration"));
		}

		TeamsMeetingBroadcastPolicy globalPolicy = policies.stream()
				.filter(p -> TeamsConstants.GLOBAL_POLICY_IDENTITY.equals(p.identity))
				.findFirst()
				.orElse(null);

		if (globalPolicy == null) {
			logger.error("Global broadcast policy not found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Global broadcast policy not found"));
		}

		MeetingBroadcastPolicy meetingPolicy = new MeetingBroadcastPolicy();
		meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
		meetingPolicy.broadcastRecordingMode = selectBroadcastRecordingMode(remediationConfig.getBroadcastRecordingModeStrategy());

		ParameterChangeResult broadcastRecordingChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(TeamsConstants.BROADCAST_RECORDING_MODE_PROPERTY)
				.prevValue(globalPolicy.broadcastRecordingMode)
				.newValue(meetingPolicy.broadcastRecordingMode.asString());

		return client.execute(CsTeamsCommand.CsTeamsMeetingBroadcastPolicy.SET(meetingPolicy))
				.thenApply(jsonNode -> {
					broadcastRecordingChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.BROADCAST_RECORDING_MODE_SUCCESS_MESSAGE, List.of(broadcastRecordingChange));
				})
				.exceptionally(ex -> {
					broadcastRecordingChange.status(ParameterChangeStatus.FAILED);
					logger.error("Exception during broadcast recording mode remediation", ex);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during broadcast recording mode remediation: " + ex.getMessage(), List.of(broadcastRecordingChange));
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
			BroadcastRecordingMode broadcastRecordingMode = BroadcastRecordingMode.fromString(changeResult.getPrevValue().toString());
			String prevBroadcastRecordingMode = changeResult.getNewValue().toString();

			MeetingBroadcastPolicy meetingPolicy = new MeetingBroadcastPolicy();
			meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
			meetingPolicy.broadcastRecordingMode = broadcastRecordingMode;

			ParameterChangeResult broadcastRecordingChange = new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter(TeamsConstants.BROADCAST_RECORDING_MODE_PROPERTY)
					.prevValue(prevBroadcastRecordingMode)
					.newValue(broadcastRecordingMode.asString());

			return client.execute(CsTeamsCommand.CsTeamsMeetingBroadcastPolicy.SET(meetingPolicy))
					.thenApply(jsonNode -> {
						broadcastRecordingChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back broadcast recording mode policy", List.of(broadcastRecordingChange));
					})
					.exceptionally(ex -> {
						broadcastRecordingChange.status(ParameterChangeStatus.FAILED);
						logger.error("Exception during broadcast recording mode policy rollback", ex);
						return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(broadcastRecordingChange));
					});
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private static BroadcastRecordingMode selectBroadcastRecordingMode(TeamsBroadcastRecordingStrategy strategy) {
		return switch (strategy) {
			case ALWAYS_ENABLED -> BroadcastRecordingMode.ALWAYS_ENABLED;
			case ALWAYS_DISABLED -> BroadcastRecordingMode.ALWAYS_DISABLED;
			case USER_OVERRIDE -> BroadcastRecordingMode.USER_OVERRIDE;
		};
	}

}
