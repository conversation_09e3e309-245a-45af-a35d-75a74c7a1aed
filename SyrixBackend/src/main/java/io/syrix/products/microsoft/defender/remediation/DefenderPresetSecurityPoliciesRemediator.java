package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

@PolicyRemediator("MS.DEFENDER.1.1v1")
public class DefenderPresetSecurityPoliciesRemediator extends RemediatorBase {

	private final PowerShellClient powershellClient;
	private final ObjectMapper objectMapper;
	private final PolicyType policyType;

	public DefenderPresetSecurityPoliciesRemediator(PowerShellClient powershellClient, PolicyType policyType) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
		this.policyType = policyType;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for preset security policies: {}", policyType);

		List<CompletableFuture<JsonNode>> futures = new ArrayList<>();

		if (policyType == PolicyType.STANDARD) {
			futures.add(applyPresetSecurityPolicy(DefenderConstants.STANDARD_PRESET_POLICY));
		}

		if (policyType == PolicyType.STRICT) {
			futures.add(applyPresetSecurityPolicy(DefenderConstants.STRICT_PRESET_POLICY));
		}

		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					ArrayNode results = objectMapper.createArrayNode();
					futures.forEach(future -> results.add(future.join()));
					return createSuccessResponse(results);
				})
				.exceptionally(ex -> {
					logger.error("Exception while configuring preset security policies", ex);
					return createErrorResponse(ex.getMessage());
				});
	}

	private CompletableFuture<JsonNode> applyPresetSecurityPolicy(String presetType) {
		logger.info("Applying preset security policy: {}", presetType);
		return DefenderHelpers.getEOPProtectionPolicyRule(powershellClient, presetType)
				.thenCompose(eopRules -> enableEOPRule(eopRules, presetType))
				.thenCompose(eopRules -> DefenderHelpers.getATPProtectionPolicyRule(powershellClient, presetType)
						.thenCompose(atpRules -> enableATPRule(atpRules, presetType))
						.thenCompose(atpResult -> updatePolicies(eopRules, presetType)));
	}

	private CompletableFuture<JsonNode> enableEOPRule(List<JsonNode> eopRules, String presetType) {
		if (eopRules == null || eopRules.isEmpty()) {
			logger.warn("No EOP Protection Policy Rule found for {} preset", presetType);
			return CompletableFuture.completedFuture(objectMapper.createObjectNode());
		}

		JsonNode rule = eopRules.getFirst();
		String ruleIdentity = rule.path(Constants.IDENTITY_FIELD).asText();

		// Check if the rule is already enabled
		String state = rule.path("State").asText("");
		if ("Enabled".equalsIgnoreCase(state)) {
			logger.info("EOP Protection Policy Rule is already enabled: {}", ruleIdentity);
			return CompletableFuture.completedFuture(rule);
		}

		logger.info("Enabling EOP Protection Policy Rule: {}", ruleIdentity);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.ENABLE_EOP_PROTECTION_POLICY_RULE,
						Map.of(Constants.IDENTITY_FIELD, ruleIdentity)
				)
		);
	}

	private CompletableFuture<JsonNode> enableATPRule(List<JsonNode> atpRules, String presetType) {
		if (atpRules == null || atpRules.isEmpty()) {
			logger.warn("No ATP Protection Policy Rule found for {} preset", presetType);
			return CompletableFuture.completedFuture(objectMapper.createObjectNode());
		}

		JsonNode rule = atpRules.getFirst();
		String ruleIdentity = rule.path(Constants.IDENTITY_FIELD).asText();

		// Check if the rule is already enabled
		String state = rule.path("State").asText("");
		if ("Enabled".equalsIgnoreCase(state)) {
			logger.info("ATP Protection Policy Rule is already enabled: {}", ruleIdentity);
			return CompletableFuture.completedFuture(rule);
		}

		logger.info("Enabling ATP Protection Policy Rule: {}", ruleIdentity);
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.ENABLE_ATP_PROTECTION_POLICY_RULE,
						Map.of(Constants.IDENTITY_FIELD, ruleIdentity)
				)
		);
	}

	private CompletableFuture<JsonNode> updatePolicies(JsonNode eopRule, String presetType) {
		if (eopRule == null || eopRule.isEmpty() || !eopRule.isArray() || eopRule.size() == 0) {
			return CompletableFuture.completedFuture(objectMapper.createObjectNode());
		}

		return CompletableFuture.allOf(
				getAntiPhishPolicy(presetType),
				getHostedContentFilterPolicy(presetType),
				getMalwareFilterPolicy(presetType),
				getSafeAttachmentPolicy(presetType),
				getSafeLinksPolicy(presetType)
		).thenCompose(v -> {
			CompletableFuture<JsonNode> antiPhishPolicyFuture = getAntiPhishPolicy(presetType);
			CompletableFuture<JsonNode> hostedContentFilterPolicyFuture = getHostedContentFilterPolicy(presetType);
			CompletableFuture<JsonNode> malwareFilterPolicyFuture = getMalwareFilterPolicy(presetType);
			CompletableFuture<JsonNode> safeAttachmentPolicyFuture = getSafeAttachmentPolicy(presetType);
			CompletableFuture<JsonNode> safeLinksPolicyFuture = getSafeLinksPolicy(presetType);

			return CompletableFuture.allOf(
					antiPhishPolicyFuture,
					hostedContentFilterPolicyFuture,
					malwareFilterPolicyFuture,
					safeAttachmentPolicyFuture,
					safeLinksPolicyFuture
			).thenCompose(v2 -> {
				JsonNode antiPhishPolicy = antiPhishPolicyFuture.join();
				JsonNode hostedContentFilterPolicy = hostedContentFilterPolicyFuture.join();
				JsonNode malwareFilterPolicy = malwareFilterPolicyFuture.join();
				JsonNode safeAttachmentPolicy = safeAttachmentPolicyFuture.join();
				JsonNode safeLinksPolicy = safeLinksPolicyFuture.join();

				String eopRuleId = eopRule.get(0).path(Constants.IDENTITY_FIELD).asText();

				// Update EOP Rule
				CompletableFuture<JsonNode> updateEopRuleFuture = updateEOPRule(
						eopRuleId,
						getFirstPolicyId(antiPhishPolicy),
						getFirstPolicyId(hostedContentFilterPolicy),
						getFirstPolicyId(malwareFilterPolicy)
				);

				// Update ATP Rule if applicable
				CompletableFuture<JsonNode> updateAtpRuleFuture = DefenderHelpers.getATPProtectionPolicyRule(powershellClient, presetType)
						.thenCompose(atpRule -> {
							if (atpRule != null && !atpRule.isEmpty()) {
								String atpRuleId = atpRule.getFirst().path(Constants.IDENTITY_FIELD).asText();
								return updateATPRule(
										atpRuleId,
										getFirstPolicyId(safeAttachmentPolicy),
										getFirstPolicyId(safeLinksPolicy)
								);
							}
							return CompletableFuture.completedFuture(objectMapper.createObjectNode());
						});

				return CompletableFuture.allOf(updateEopRuleFuture, updateAtpRuleFuture)
						.thenApply(v3 -> {
							ObjectNode result = objectMapper.createObjectNode();
							result.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
							result.put(Constants.MESSAGE_FIELD, String.format(DefenderConstants.PRESET_POLICIES_SUCCESS_MESSAGE_TEMPLATE, presetType));
							return result;
						});
			});
		});
	}

	private String getFirstPolicyId(JsonNode policies) {
		if (policies != null && !policies.isEmpty() && policies.isArray() && policies.size() > 0) {
			return policies.get(0).path(Constants.IDENTITY_FIELD).asText();
		}
		return null;
	}

	private CompletableFuture<JsonNode> getAntiPhishPolicy(String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_ANTI_PHISH_POLICY,
						Map.of()
				)
		).thenApply(policies -> filterPoliciesByRecommendedType(policies, presetType));
	}

	private CompletableFuture<JsonNode> getHostedContentFilterPolicy(String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_HOSTED_CONTENT_FILTER_POLICY,
						Map.of()
				)
		).thenApply(policies -> filterPoliciesByRecommendedType(policies, presetType));
	}

	private CompletableFuture<JsonNode> getMalwareFilterPolicy(String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_MALWARE_FILTER_POLICY,
						Map.of()
				)
		).thenApply(policies -> filterPoliciesByRecommendedType(policies, presetType));
	}

	private CompletableFuture<JsonNode> getSafeAttachmentPolicy(String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_SAFE_ATTACHMENT_POLICY,
						Map.of()
				)
		).thenApply(policies -> filterPoliciesByRecommendedType(policies, presetType));
	}

	private CompletableFuture<JsonNode> getSafeLinksPolicy(String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_SAFE_LINKS_POLICY,
						Map.of()
				)
		).thenApply(policies -> filterPoliciesByRecommendedType(policies, presetType));
	}

	private JsonNode filterPoliciesByRecommendedType(JsonNode policies, String presetType) {
		if (policies == null || policies.isEmpty()) {
			return objectMapper.createArrayNode();
		}

		List<JsonNode> filteredPolicies = StreamSupport.stream(policies.spliterator(), false)
				.filter(policy -> {
					String recommendedType = policy.path("RecommendedPolicyType").asText("");
					return recommendedType.equals(presetType);
				}).toList();

		ArrayNode result = objectMapper.createArrayNode();
		filteredPolicies.forEach(result::add);
		return result;
	}

	private CompletableFuture<JsonNode> updateEOPRule(
			String eopRuleId,
			String antiPhishPolicyId,
			String hostedContentFilterPolicyId,
			String malwareFilterPolicyId
	) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(Constants.IDENTITY_FIELD, eopRuleId);

		if (antiPhishPolicyId != null) {
			parameters.put(DefenderConstants.ANTI_PHISH_POLICY_PARAM, antiPhishPolicyId);
		}

		if (hostedContentFilterPolicyId != null) {
			parameters.put(DefenderConstants.HOSTED_CONTENT_FILTER_POLICY_PARAM, hostedContentFilterPolicyId);
		}

		if (malwareFilterPolicyId != null) {
			parameters.put(DefenderConstants.MALWARE_FILTER_POLICY_PARAM, malwareFilterPolicyId);
		}

		logger.info("Updating EOP Protection Policy Rule: {}", eopRuleId);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.SET_EOP_PROTECTION_POLICY_RULE,
						parameters
				)
		);
	}

	private CompletableFuture<JsonNode> updateATPRule(
			String atpRuleId,
			String safeAttachmentPolicyId,
			String safeLinksPolicyId
	) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(Constants.IDENTITY_FIELD, atpRuleId);

		if (safeAttachmentPolicyId != null) {
			parameters.put(DefenderConstants.SAFE_ATTACHMENT_POLICY_PARAM, safeAttachmentPolicyId);
		}

		if (safeLinksPolicyId != null) {
			parameters.put(DefenderConstants.SAFE_LINKS_POLICY_PARAM, safeLinksPolicyId);
		}

		logger.info("Updating ATP Protection Policy Rule: {}", atpRuleId);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.SET_ATP_PROTECTION_POLICY_RULE,
						parameters
				)
		);
	}

	private JsonNode createSuccessResponse(JsonNode results) {
		ObjectNode response = objectMapper.createObjectNode();
		response.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
		response.put(Constants.POLICY_ID_FIELD, getPolicyId());

		// More specific message about which policy type was set
		String policyTypeStr = (policyType == PolicyType.STANDARD) ? "Standard" : "Strict";
		response.put(Constants.MESSAGE_FIELD, String.format(
				"Successfully applied %s preset security policies for Microsoft Defender", policyTypeStr));

		// Add detailed information
		ObjectNode details = objectMapper.createObjectNode();
		details.put("policyType", policyTypeStr);
		details.put("timestamp", System.currentTimeMillis());

		// Include policy details
		ObjectNode policyDetails = objectMapper.createObjectNode();
		policyDetails.put("eopPolicyApplied", true);
		policyDetails.put("atpPolicyApplied", policyType == PolicyType.STRICT);

		// Add the specific protection types that were configured
		ArrayNode protectionTypes = objectMapper.createArrayNode();
		protectionTypes.add("AntiPhish");
		protectionTypes.add("HostedContentFilter");
		protectionTypes.add("MalwareFilter");

		if (policyType == PolicyType.STRICT) {
			protectionTypes.add("SafeAttachment");
			protectionTypes.add("SafeLinks");
		}

		policyDetails.set("configuredProtections", protectionTypes);
		details.set("policyDetails", policyDetails);

		// Add the complete results from all remediation operations
		details.set("remediationResults", results);

		response.set(Constants.DETAILS_FIELD, details);
		return response;
	}

	private JsonNode createErrorResponse(String errorMessage) {
		ObjectNode response = objectMapper.createObjectNode();
		response.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
		response.put(Constants.POLICY_ID_FIELD, getPolicyId());
		response.put(Constants.ERROR_FIELD, errorMessage != null ? errorMessage : DefenderConstants.ERROR_UNKNOWN);
		return response;
	}
}