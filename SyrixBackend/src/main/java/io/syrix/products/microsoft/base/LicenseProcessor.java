package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.protocols.client.MicrosoftGraphClient;

import static io.syrix.common.constants.Constants.VALUE_FIELD;

/**
 * Helper class to process license information.
 */
public class LicenseProcessor {
	private final ObjectMapper mapper = new ObjectMapper();
	private boolean hasAadPremiumP2;
	private static final String AAD_PREMIUM_P2 = "AAD_PREMIUM_P2";
	private static final String PROVISIONING_SUCCESS = "Success";
	protected final MicrosoftGraphClient graphClient;
	private boolean hasP5License;

	public LicenseProcessor(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.hasAadPremiumP2 = false;
	}

	public boolean isHasP5License() {
		return hasP5License;
	}

	/**
	 * Extracts license details from a SKU.
	 */
	ObjectNode extractLicenseDetails(JsonNode sku) {
		ObjectNode licenseDetails = mapper.createObjectNode();

		// Add SKU fields
		addSkuFields(sku, licenseDetails);

		// Add units information
		addUnitsInformation(sku, licenseDetails);

		return licenseDetails;
	}

	/**
	 * Adds fields that start with "sku" to license details.
	 */
	private void addSkuFields(JsonNode sku, ObjectNode licenseDetails) {
		sku.fields().forEachRemaining(entry -> {
			if (entry.getKey().toLowerCase().startsWith("sku")) {
				licenseDetails.set(entry.getKey(), entry.getValue());
			}
		});
	}

	/**
	 * Adds consumed and prepaid units information.
	 */
	private void addUnitsInformation(JsonNode sku, ObjectNode licenseDetails) {
		licenseDetails.set("ConsumedUnits", sku.get("consumedUnits"));
		licenseDetails.set("PrepaidUnits", sku.get("prepaidUnits"));
	}

	/**
	 * Processes service plans from a SKU.
	 */
	ArrayNode processServicePlans(JsonNode sku) {
		ArrayNode validServicePlans = mapper.createArrayNode();
		JsonNode skuServicePlans = sku.path("servicePlans");

		skuServicePlans.forEach(plan -> {
			if (isValidServicePlan(plan)) {
				validServicePlans.add(plan);
				checkForAadPremiumP2(plan);
				checkForP5License(plan);
			}
		});

		return validServicePlans;
	}

	/**
	 * Checks if a service plan is valid (provisioning status is "Success").
	 */
	private boolean isValidServicePlan(JsonNode plan) {
		JsonNode provisioningStatus = plan.path("provisioningStatus");
		return !provisioningStatus.isMissingNode() &&
				PROVISIONING_SUCCESS.equals(provisioningStatus.asText());
	}

	/**
	 * Checks if a service plan is AAD Premium P2.
	 */
	private void checkForAadPremiumP2(JsonNode plan) {
		if (!hasAadPremiumP2) {
			JsonNode servicePlanName = plan.path("servicePlanName");
			if (!servicePlanName.isMissingNode() &&
					AAD_PREMIUM_P2.equals(servicePlanName.asText())) {
				hasAadPremiumP2 = true;
			}
		}
	}

	private void checkForP5License(JsonNode plan) {
		if (!hasP5License) {
			String skuPartNumber = plan.path("SkuPartNumber").asText();
			int enabledUnits = plan.path("PrepaidUnits").path("Enabled").asInt(0);

			if ((skuPartNumber.equals("ENTERPRISEPREMIUM") || skuPartNumber.equals("SPE_E5")) && enabledUnits > 0) {
				hasP5License = true;
			}
		}
	}

	/**
	 * Processes SKU information to extract license details and check for AAD Premium P2 license.
	 */
	public SkuProcessingResult processSkus() {
		JsonNode skus = this.graphClient.getSubscribedSkus().join();

		ArrayNode licenseInfoArray = mapper.createArrayNode();
		ArrayNode servicePlans = mapper.createArrayNode();

		skus.path(VALUE_FIELD).forEach(sku -> {
			licenseInfoArray.add(extractLicenseDetails(sku));
			servicePlans.addAll(processServicePlans(sku));
		});

		ObjectNode licenseInfo = mapper.createObjectNode();
		licenseInfo.set("licenses", licenseInfoArray);
		return new SkuProcessingResult(licenseInfo, servicePlans, hasAadPremiumP2License(), isHasP5License());
	}
	/**
	 * Returns whether AAD Premium P2 license was found.
	 */
	boolean hasAadPremiumP2License() {
		return hasAadPremiumP2;
	}
}