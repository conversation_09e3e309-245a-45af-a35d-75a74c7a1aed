package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

public class GraphResourceMapper {
	private static final Logger logger = LoggerFactory.getLogger(GraphResourceMapper.class);
	private static final String NOT_INITIALIZED_LOG = "GraphResourceMapper not initialized. Call initialize() first.";
	private final MicrosoftGraphClient graphClient;
	private final Map<String, String> resourceMap = new ConcurrentHashMap<>();
	private final Map<String, String> roleMap = new ConcurrentHashMap<>();
	private boolean initialized = false;

	public GraphResourceMapper(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
	}

	/**
	 * Initializes the resource and role mappings by fetching data from Microsoft Graph.
	 * This should be called before using getResourceName or getRoleName.
	 */
	public void initialize() {
		if (initialized) {
			return ;
		}
		String filter = "servicePrincipalType eq 'Application' and (tags/any(t:t eq 'WindowsAzureActiveDirectoryIntegratedApp'))";
		Map<String, String> queryParams = Map.of(PARAM_SELECT, "id,appId,displayName,appRoles","$filter", filter);
		JsonNode principals = graphClient.getAzureServicePrincipals(queryParams, null).join();
		processServicePrincipals(principals);
		initialized = true;
	}

	/**
	 * Processes service principals to build resource and role mappings.
	 */
	private CompletableFuture<Void> processServicePrincipals(JsonNode response) {
		if (!response.has(Constants.VALUE_FIELD)) {
			logger.warn("No service principals found in response");
			return CompletableFuture.completedFuture(null);
		}

		JsonNode principals = response.get(Constants.VALUE_FIELD);
		for (JsonNode principal : principals) {
			String resourceId = principal.path(Constants.ID_FIELD).asText();
			String displayName = principal.path(Constants.DISPLAY_NAME_FIELD).asText();

			// Add to resource map
			resourceMap.put(resourceId, displayName);

			// Process app roles
			JsonNode appRoles = principal.path("appRoles");
			if (appRoles.isArray()) {
				for (JsonNode role : appRoles) {
					String roleId = role.path(Constants.ID_FIELD).asText();
					String roleName = role.path(Constants.VALUE_FIELD).asText();
					if (!roleId.isEmpty() && !roleName.isEmpty()) {
						roleMap.put(roleId, roleName);
					}
				}
			}
		}

		return CompletableFuture.completedFuture(null);
	}

	/**
	 * Gets the human-readable name for a resource ID.
	 */
	public String getResourceName(String resourceId) {
		if (!initialized) {
			throw new IllegalStateException();
		}
		return resourceMap.getOrDefault(resourceId, "Unknown Resource (" + resourceId + ")");
	}

	/**
	 * Gets the human-readable name for a role ID.
	 */
	public String getRoleName(String roleId) {
		if (!initialized) {
			throw new IllegalStateException(NOT_INITIALIZED_LOG);
		}
		return roleMap.getOrDefault(roleId, "Unknown Role (" + roleId + ")");
	}

	/**
	 * Gets all resource mappings.
	 */
	public Map<String, String> getResourceMap() {
		if (!initialized) {
			throw new IllegalStateException(NOT_INITIALIZED_LOG);
		}
		return new HashMap<>(resourceMap);
	}

	/**
	 * Gets all role mappings.
	 */
	public Map<String, String> getRoleMap() {
		if (!initialized) {
			throw new IllegalStateException(NOT_INITIALIZED_LOG);
		}
		return new HashMap<>(roleMap);
	}
}