package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;

import java.time.Instant;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;

import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsClientConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.ClientConfiguration;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.ArrayList;

@PolicyRemediator("MS.TEAMS.4.1v1")
public class TeamsAllowEmailIntoChannelRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {
	private static final List<String> govMarkers = List.of("GCC", "DOD");

	public TeamsAllowEmailIntoChannelRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowEmailIntoChannelRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		if (isGovTenant()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "The tenant belongs to the state environment"));
		}

		List<TeamsClientConfiguration> teamsClientConfigurations = getTeamsClientConfigurations();

		if (teamsClientConfigurations.isEmpty()) {
			logger.error("No Teams Client Configuration found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No Teams Client Configuration found in configuration"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();
		for (TeamsClientConfiguration conf : teamsClientConfigurations) {
			if (conf.allowEmailIntoChannel) {
				ClientConfiguration prevConfig = new ClientConfiguration();
				prevConfig.identity = conf.identity;
				prevConfig.allowEmailIntoChannel = conf.allowEmailIntoChannel;

				ClientConfiguration newConfig = new ClientConfiguration();
				newConfig.identity = conf.identity;
				newConfig.allowEmailIntoChannel = false;

				results.add(fixConfig(prevConfig, newConfig));
			}
		}

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(ClientConfiguration prevConfig, ClientConfiguration newConfig) {
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowEmailIntoChannel: " + prevConfig.identity)
				.prevValue(prevConfig.allowEmailIntoChannel)
				.newValue(newConfig.allowEmailIntoChannel);

		return client.execute(CsTeamsCommand.CsTeamsClientConfiguration.SET(newConfig))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Teams Client Configuration: " + newConfig.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Teams Client Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Teams Client Configuration: " + ex.getMessage(), List.of(paramChange));
				});
	}

	private boolean isGovTenant() {
		return getTeamsTenantInfos().stream()
				.flatMap(info -> info.assignedPlan.stream())
				.anyMatch(govMarkers::contains);
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "All configurations fixed successfully", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any configurations", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " configurations, failed to fix " + failedCount + " configurations",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean newValue = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean curValue = Boolean.parseBoolean(change.getNewValue().toString());

				ClientConfiguration prevConfig = new ClientConfiguration();
				prevConfig.identity = identity;
				prevConfig.allowEmailIntoChannel = curValue;

				ClientConfiguration newConfig = new ClientConfiguration();
				newConfig.identity = identity;
				newConfig.allowEmailIntoChannel = newValue;

				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					results.add(fixConfig(prevConfig, newConfig));
				} else {
						ParameterChangeResult paramChange = new ParameterChangeResult()
								.timeStamp(Instant.now())
								.parameter("allowEmailIntoChannel: " + prevConfig.identity)
								.prevValue(prevConfig.allowEmailIntoChannel)
								.newValue(newConfig.allowEmailIntoChannel)
								.status(ParameterChangeStatus.FAILED);

						logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
						results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
								"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
				}
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
