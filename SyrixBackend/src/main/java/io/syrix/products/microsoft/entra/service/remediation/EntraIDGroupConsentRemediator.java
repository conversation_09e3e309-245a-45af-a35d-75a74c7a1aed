package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.utils.ProtocolConstants.AUTHORIZATION_POLICY;

/**
 * Implements remediation for MS.AAD.5.4v1: Group owners SHALL NOT be allowed to consent to applications.

 * This remediation ensures that group owners cannot consent to applications accessing their group's data,
 * reducing the risk of unauthorized application access to group data.
 */
@PolicyRemediator("MS.AAD.5.4v1")
public class EntraIDGroupConsentRemediator extends RemediatorBase {
	//Consent property ManagePermissionGrantsForOwnedResource.microsoft-all-application-permissions-for-team
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDGroupConsentRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for group consent settings using MS Graph REST API");

		return updateGroupConsentPolicy()
				.exceptionally(ex -> {
					logger.error("Exception while configuring group consent settings", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> updateGroupConsentPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(AUTHORIZATION_POLICY)
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Group consent settings updated successfully");
					return IPolicyRemediator.success(getPolicyId(),
							"Group-specific consent disabled and application consent restricted");
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error updating group consent policy";
					logger.error("Failed to update group consent policy: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to update group consent policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		ObjectNode permissions = objectMapper.createObjectNode();
		ArrayNode policies = objectMapper.createArrayNode();
		permissions.set("permissionGrantPoliciesAssigned", policies);
		policyPayload.set("defaultUserRolePermissions", permissions);
		return policyPayload;
	}

	/**
	 * Checks the current status of group consent settings.
	 *
	 * @return CompletableFuture<Boolean> true if group-specific consent is disabled
	 */
	public CompletableFuture<Boolean> checkGroupConsentStatus() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.v1()
						.withEndpoint(AUTHORIZATION_POLICY)
						.withMethod(HttpMethod.GET)
						.build())
				.thenApply(response -> {
					JsonNode permissionGrantPolicies = response
							.path("defaultUserRolePermissions")
							.path("permissionGrantPoliciesAssigned");

					boolean hasGroupConsentPolicy = false;
					if (permissionGrantPolicies.isArray()) {
						for (JsonNode policy : permissionGrantPolicies) {
							if ("ManagePermissionGrantsForOwnedResource.microsoft-all-application-permissions-for-team"
									.equals(policy.asText())) {
								hasGroupConsentPolicy = true;
								break;
							}
						}
					}

					boolean isDisabled = !hasGroupConsentPolicy;
					logger.info("Group-specific consent is {} disabled", isDisabled ? "currently" : "not");
					return isDisabled;
				})
				.exceptionally(e -> {
					logger.error("Failed to check group consent settings: {}", e.getMessage(), e);
					return false;
				});
	}
}