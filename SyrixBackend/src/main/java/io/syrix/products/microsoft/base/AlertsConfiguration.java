package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Top-level configuration class that holds a list of alert definitions.
 * This class is designed to be deserialized from YAML configuration files.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlertsConfiguration {

	/**
	 * The list of all alert definitions in the configuration.
	 */
	@JsonProperty("alerts")
	private List<AlertDefinition> alerts;

	/**
	 * Default constructor required for Jackson deserialization.
	 */
	public AlertsConfiguration() {
	}

	/**
	 * Constructor with alerts list.
	 *
	 * @param alerts The list of alert definitions
	 */
	public AlertsConfiguration(List<AlertDefinition> alerts) {
		this.alerts = alerts;
	}

	/**
	 * Gets the list of alert definitions.
	 *
	 * @return The list of alert definitions
	 */
	public List<AlertDefinition> getAlerts() {
		return alerts;
	}

	/**
	 * Sets the list of alert definitions.
	 *
	 * @param alerts The list of alert definitions
	 */
	public void setAlerts(List<AlertDefinition> alerts) {
		this.alerts = alerts;
	}

	/**
	 * Returns the number of alert definitions in the configuration.
	 *
	 * @return The number of alert definitions
	 */
	public int getAlertCount() {
		return alerts != null ? alerts.size() : 0;
	}

	@Override
	public String toString() {
		return "AlertsConfiguration { alertCount = " + getAlertCount() + '}';
	}
}