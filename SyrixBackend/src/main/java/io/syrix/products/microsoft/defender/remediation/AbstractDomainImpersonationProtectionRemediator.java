package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Abstract base class for domain impersonation protection remediators.
 * Provides common functionality for both organization domain and partner domain protection.
 */
public abstract class AbstractDomainImpersonationProtectionRemediator extends RemediatorBase {
	protected final PowerShellClient powershellClient;
	protected final ObjectMapper objectMapper;

	// Parameter names for AntiPhishPolicy - from official documentation
	protected static final String ENABLE_ORG_DOMAINS_PROTECTION = "EnableOrganizationDomainsProtection";
	protected static final String ENABLE_TARGETED_DOMAINS_PROTECTION = "EnableTargetedDomainsProtection";
	protected static final String TARGETED_DOMAINS_TO_PROTECT = "TargetedDomainsToProtect";
	protected static final String IMPERSONATION_PROTECTION_STATE = "ImpersonationProtectionState";

	// Track the policy configuration status
	protected final Map<String, Boolean> policyConfigurationStatus = new HashMap<>();

	public AbstractDomainImpersonationProtectionRemediator(PowerShellClient powershellClient) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for {} - Domain impersonation protection", getPolicyId());

		// Perform initial validation specific to the implementation
		CompletableFuture<JsonNode> validationResult = validateBeforeRemediation();
		if (validationResult != null) {
			return validationResult;
		}

		// First check if domain impersonation protection is already enabled for both policies
		return checkBothPoliciesConfiguration()
				.thenCompose(alreadyConfigured -> {
					if (alreadyConfigured) {
						logger.info("Domain impersonation protection is already configured for both Standard and Strict policies");
						return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(
								getPolicyId(),
								getDomainsCount(),
								getRemediationType(),
								"Both",
								objectMapper,
								"Domain impersonation protection is already configured for both Standard and Strict policies"
						));
					}

					// Not configured yet, proceed with configuration
					// Configure both Standard and Strict policies in sequence
					return applyDomainImpersonationProtection(DefenderConstants.STANDARD_PRESET_POLICY)
							.thenCompose(standardResult -> {
								if (standardResult == null || standardResult.has(Constants.ERROR_FIELD)) {
									String error = standardResult != null ?
											standardResult.get(Constants.ERROR_FIELD).asText() :
											"Failed to configure Standard policy";
									logger.error("Failed to configure Standard policy: {}", error);
									return CompletableFuture.completedFuture(
											DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
								}

								// Now configure the Strict policy
								return applyDomainImpersonationProtection(DefenderConstants.STRICT_PRESET_POLICY)
										.thenCompose(strictResult -> {
											if (strictResult == null || strictResult.has(Constants.ERROR_FIELD)) {
												String error = strictResult != null ?
														strictResult.get(Constants.ERROR_FIELD).asText() :
														"Failed to configure Strict policy";
												logger.error("Failed to configure Strict policy: {}", error);
												return CompletableFuture.completedFuture(
														DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
											}

											// Both policies configured successfully
											logger.info("Successfully configured domain impersonation protection for both Standard and Strict policies");
											return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(
													getPolicyId(),
													getDomainsCount(),
													getRemediationType(),
													"Both",
													objectMapper,
													"Successfully configured domain impersonation protection for both Standard and Strict policies"
											));
										});
							});
				})
				.exceptionally(ex -> {
					logger.error("Exception during domain impersonation protection configuration", ex);
					return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), objectMapper);
				});
	}

	/**
	 * Perform validation before remediation.
	 * @return CompletableFuture<JsonNode> with error response if validation fails, null if validation passes
	 */
	protected abstract CompletableFuture<JsonNode> validateBeforeRemediation();

	/**
	 * Get the number of domains being protected.
	 * @return Count of domains
	 */
	protected abstract int getDomainsCount();

	/**
	 * Get the type of remediation being performed.
	 * @return String indicating remediation type (e.g., "DomainImpersonation", "PartnerDomainImpersonation")
	 */
	protected abstract String getRemediationType();

	/**
	 * Check if domain impersonation protection is already configured for both Standard and Strict policies
	 * @return CompletableFuture<Boolean> - true if both policies are already configured
	 */
	protected CompletableFuture<Boolean> checkBothPoliciesConfiguration() {
		return CompletableFuture.allOf(
				checkPolicyConfiguration(DefenderConstants.STANDARD_PRESET_POLICY),
				checkPolicyConfiguration(DefenderConstants.STRICT_PRESET_POLICY)
		).thenApply(v ->
				isPolicyAlreadyConfigured(DefenderConstants.STANDARD_PRESET_POLICY) &&
						isPolicyAlreadyConfigured(DefenderConstants.STRICT_PRESET_POLICY)
		);
	}

	/**
	 * Check if a specific policy is already configured for domain impersonation protection
	 */
	protected CompletableFuture<Void> checkPolicyConfiguration(String presetType) {
		return DefenderHelpers.getAntiPhishPolicies(powershellClient, presetType)
				.thenAccept(policyResult -> {
					if (policyResult == null || policyResult.has(Constants.ERROR_FIELD)) {
						// If we can't get the policy, assume it's not configured
						policyConfigurationStatus.put(presetType, false);
						return;
					}

					// Check if domain impersonation protection is already enabled
					boolean isConfigured = isDomainImpersonationAlreadyEnabled(policyResult);
					policyConfigurationStatus.put(presetType, isConfigured);

					logger.info("Domain impersonation protection for {} policy is {}",
							presetType, isConfigured ? "already configured" : "not configured");
				});
	}

	/**
	 * Get the status of a specific policy configuration
	 */
	protected boolean isPolicyAlreadyConfigured(String presetType) {
		return policyConfigurationStatus.getOrDefault(presetType, false);
	}

	/**
	 * Apply domain impersonation protection to a specific preset policy (Standard or Strict)
	 */
	protected CompletableFuture<JsonNode> applyDomainImpersonationProtection(String presetType) {
		logger.info("Applying domain impersonation protection for {} policy", presetType);

		// If this policy is already configured, just return success
		if (isPolicyAlreadyConfigured(presetType)) {
			logger.info("Domain impersonation protection for {} policy is already configured, skipping", presetType);
			return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(
					getPolicyId(),
					getDomainsCount(),
					getRemediationType(),
					presetType,
					objectMapper,
					"Domain impersonation protection for " + presetType + " policy is already configured"
			));
		}

		// Get all AntiPhish policies directly
		return DefenderHelpers.getAntiPhishPolicies(powershellClient, presetType)
				.thenCompose(policyResult -> {
					if (policyResult == null || policyResult.has(Constants.ERROR_FIELD)) {
						String error = policyResult != null ?
								policyResult.get(Constants.ERROR_FIELD).asText() :
								"Failed to retrieve AntiPhishPolicy for " + presetType;
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Extract the policy name
					String policyName = extractPolicyName(policyResult);
					if (policyName.isEmpty()) {
						String error = "No AntiPhishPolicy found for " + presetType;
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Configure domain impersonation protection
					return configureDomainImpersonationProtection(policyName);
				});
	}

	/**
	 * Extract policy name from the result
	 */
	protected String extractPolicyName(JsonNode policyResult) {
		if (policyResult == null) {
			return "";
		}

		if (policyResult.isArray() && policyResult.size() > 0) {
			return policyResult.get(0).path(Constants.IDENTITY_FIELD).asText("");
		} else {
			return policyResult.path(Constants.IDENTITY_FIELD).asText("");
		}
	}

	/**
	 * Determine if domain impersonation protection is already enabled in the policy
	 */
	protected abstract boolean isDomainImpersonationAlreadyEnabled(JsonNode policySettings);

	/**
	 * Configure domain impersonation protection for an anti-phish policy
	 */
	protected abstract CompletableFuture<JsonNode> configureDomainImpersonationProtection(String policyName);
}