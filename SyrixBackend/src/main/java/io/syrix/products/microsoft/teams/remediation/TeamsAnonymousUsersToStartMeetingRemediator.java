package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.2v1")
public class TeamsAnonymousUsersToStartMeetingRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAnonymousUsersToStartMeetingRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAnonymousUsersToStartMeetingRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			logger.error("No meeting policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found in configuration"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
				.filter(policy -> policy.allowAnonymousUsersToStartMeeting)
				.filter(policy -> !policy.identity.startsWith("Tag:")) // policy with the "Tag:" only read
				.map(this::fixPolicy_)
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy_(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.allowAnonymousUsersToStartMeeting = false;

		ParameterChangeResult anonymousStartParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowAnonymousUsersToStartMeeting: " + policy.identity)
				.prevValue(policy.allowAnonymousUsersToStartMeeting)
				.newValue(meetingPolicy.allowAnonymousUsersToStartMeeting);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(meetingPolicies -> {
					anonymousStartParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Meeting Policy fixed: " + policy.identity, List.of(anonymousStartParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Meeting Policy: {} , errMsg: {}", policy.identity, ex.getMessage());
					anonymousStartParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Meeting Policy: " + ex.getMessage(), List.of(anonymousStartParam));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "All meeting policies fixed successfully", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any meeting policies", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " policies, failed to fix " + failedCount + " policies",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean allowAnonymousUsersToStartMeeting = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowAnonymousUsersToStartMeeting = Boolean.parseBoolean(change.getNewValue().toString());

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.allowAnonymousUsersToStartMeeting = allowAnonymousUsersToStartMeeting;

				ParameterChangeResult anonymousStartParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowAnonymousUsersToStartMeeting: " + identity)
						.prevValue(prevAllowAnonymousUsersToStartMeeting)
						.newValue(allowAnonymousUsersToStartMeeting);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					anonymousStartParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(anonymousStartParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							anonymousStartParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back anonymous users to start meeting policy", List.of(anonymousStartParam));
						})
						.exceptionally(ex -> {
							anonymousStartParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during anonymous users to start meeting policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(anonymousStartParam));
						});

				results.add(result);
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
