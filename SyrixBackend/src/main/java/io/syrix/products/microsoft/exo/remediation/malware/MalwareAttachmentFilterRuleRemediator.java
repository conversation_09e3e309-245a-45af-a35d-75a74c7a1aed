package io.syrix.products.microsoft.exo.remediation.malware;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.AttachmentFilterConfig;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.ExchangeBaseRemediator;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for policy MS.EXO.9.1v2 which ensures that email attachment filtering
 * is configured to block potentially malicious file attachments.
 * <p>
 * This remediator checks if a malware filter policy exists for attachment filtering.
 * If no policy exists, it creates a new policy with appropriate settings.
 */

public class MalwareAttachmentFilterRuleRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	private static final String IDENTITY = "Identity";
	private static final String UNKNOWN_ERROR = "Unknown error";

	private final AttachmentFilterConfig config;
	private final String parentPolicyId;


	/**
	 * Constructs a new MalwareAttachmentFilterRemediator with the required dependencies.
	 *
	 * @param graphClient        GraphClient for Microsoft Graph operations
	 * @param exchangeClient     PowerShell client for Exchange operations
	 * @param configNode         JSON configuration node containing malware policy data
	 * @param remediationContext Context for the remediation operation
	 * @param remediationConfig  Configuration for the remediation
	 */
	public MalwareAttachmentFilterRuleRemediator(MicrosoftGraphClient graphClient,
												 PowerShellClient exchangeClient,
												 ObjectNode configNode,
												 ExchangeRemediationContext remediationContext,
												 ExchangeRemediationConfig remediationConfig,
												 String parentPolicyId) {
		super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
		this.config = remediationConfig.getAttachmentFilterConfig();
		this.parentPolicyId = parentPolicyId;
	}
	
	public MalwareAttachmentFilterRuleRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, String parentPolicyId) {
		super(graphClient, exchangeClient, null, null, null);
		this.config = null;
		this.parentPolicyId = parentPolicyId;
	}

	@Override
	public String getPolicyId() {
		return parentPolicyId;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} - Malware Attachment Filter Rule", getPolicyId());

		List<MalwareRule> existingRules = getMalwareRules();
		if (existingRules == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_MALWARE_RULE));
		}

		Optional<MalwareRule> syrixRule = existingRules.stream()
				.filter(rule -> config.getRuleName().equals(rule.name))
				.findFirst();

		if (syrixRule.isPresent()) {
			logger.info("Attachment filtering rule already configured: {}", syrixRule.get().name);
			return updateSyrixRule(syrixRule.get());
		} else {
			return createSyrixRule();
		}
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult policyChangeResult) {
		logger.info("Starting rollback for malware attachment filter rule: {}", policyChangeResult.getPolicyId());

		if (policyChangeResult.getChanges() == null || policyChangeResult.getChanges().isEmpty()) {
			logger.info("No parameter changes to rollback for rule: {}", policyChangeResult.getPolicyId());
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(policyChangeResult.getPolicyId(), "No changes to rollback"));
		}

		ParameterChangeResult ruleChange = null;
		for (ParameterChangeResult change : policyChangeResult.getChanges()) {
			String parameter = change.getParameter();
			if (parameter != null && parameter.startsWith("Rule-")) {
				ruleChange = change;
				break;
			}
		}

		if (ruleChange == null) {
			logger.info("No rule changes found in the fix result");
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(getPolicyId(), "No rule changes found"));
		}

		return rollbackChange(ruleChange);
	}

	private CompletableFuture<PolicyChangeResult> updateSyrixRule(MalwareRule syrixRule) {
		if (isRuleValid(syrixRule)) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Malware filter rule requirement met"));
		}

		Map<String, Object> parameters = buildRuleParameters();
		parameters.put(IDENTITY, syrixRule.identity);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Rule-" + ExoConstants.IDENTITY + ":" + syrixRule.identity + ",action:UPDATE")
				.prevValue(toRuleParameters(syrixRule))
				.newValue(parameters);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-MalwareFilterRule", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully updated malware filter rule: {}", syrixRule.identity);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully updated malware filter rule:" + syrixRule.identity, List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to update malware filter rule: {}, {}", syrixRule.identity, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to update malware filter rule:" + syrixRule.identity + "," + error);
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during malware filter rule update", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during malware filter rule update: " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> createSyrixRule() {
		logger.info("Creating malware filter rule: {}", config.getRuleName());
		Map<String, Object> parameters = buildRuleParameters();

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Rule-Name:" + config.getRuleName() + ",action:CREATE")
				.prevValue(null)
				.newValue(parameters);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("New-MalwareFilterRule", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully created malware filter rule: {}", config.getRuleName());
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully created malware filter rule:" + config.getRuleName(), List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to create malware filter rule: {}, {}", config.getRuleName(), error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to create malware filter rule:" + config.getRuleName() + "," + error);
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during malware filter rule creation", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during malware filter rule creation: " + ex.getMessage(), List.of(paramChange));
				});
	}

	@SuppressWarnings("RedundantIfStatement")
	private boolean isRuleValid(MalwareRule malwareRule) {
		if (!config.getRuleName().equals(malwareRule.name)) {
			return false;
		}

		if (!config.getPolicyName().equals(malwareRule.malwareFilterPolicy)) {
			return false;
		}

		if (config.getRecipientDomainIs() != null && !config.getRecipientDomainIs().equals(malwareRule.recipientDomainIs)) {
			return false;
		}

		if (config.getSentTo() != null && !config.getSentTo().equals(malwareRule.sentTo)) {
			return false;
		}

		if (config.getSentToMemberOf() != null && !config.getSentToMemberOf().equals(malwareRule.sentToMemberOf)) {
			return false;
		}

		return true;
	}

	private Map<String, Object> toRuleParameters(MalwareRule rule) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, rule.identity);
		parameters.put("Name", rule.name);
		parameters.put("Priority", rule.priority);
		parameters.put("MalwareFilterPolicy", rule.malwareFilterPolicy);
		parameters.put("RecipientDomainIs", rule.recipientDomainIs);
		parameters.put("SentTo", rule.sentTo);
		parameters.put("SentToMemberOf", rule.sentToMemberOf);
		return parameters;
	}

	private Map<String, Object> buildRuleParameters() {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Name", config.getRuleName()); //for new rule

		// Set the priority to 0 (only valid value for first rule)
		parameters.put("Priority", 0);
		parameters.put("MalwareFilterPolicy", config.getPolicyName());

		if (config.getRecipientDomainIs() != null && !config.getRecipientDomainIs().isEmpty()) {
			parameters.put("RecipientDomainIs", config.getRecipientDomainIs());
		} else {
			// If no RecipientDomainIs condition set, use the default "*"
			parameters.put("RecipientDomainIs", new String[]{"*"});
		}

		if (config.getSentTo() != null && !config.getSentTo().isEmpty()) {
			parameters.put("SentTo", config.getSentTo());
		}

		if (config.getSentToMemberOf() != null && !config.getSentToMemberOf().isEmpty()) {
			parameters.put("SentToMemberOf", config.getSentToMemberOf());
		}

		return parameters;
	}
	
	private CompletableFuture<PolicyChangeResult> rollbackChange(ParameterChangeResult change) {
		logger.info("Rolling back change: {}", change.getParameter());
		
		String paramInfo = change.getParameter();
		if (paramInfo == null || !paramInfo.contains("action:")) {
			logger.warn("Invalid parameter info format for rollback: {}", paramInfo);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Invalid parameter info for rollback"));
		}
		
		if (paramInfo.contains("action:CREATE")) {
			return rollbackCreation(change);
		} else if (paramInfo.contains("action:UPDATE")) {
			return rollbackUpdate(getPolicyId(), change);
		} else {
			logger.warn("Unsupported action type in parameter: {}", paramInfo);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Unsupported action type for rollback"));
		}
	}
	
	private CompletableFuture<PolicyChangeResult> rollbackCreation(ParameterChangeResult change) {
		String ruleName = extractRuleNameFromParam(change.getParameter());
		if (ruleName == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Failed to extract rule name for deletion"));
		}
		
		logger.info("Rolling back rule creation by removing rule: {}", ruleName);
		
		ParameterChangeResult rollbackChange = new ParameterChangeResult()
			.timeStamp(Instant.now())
			.parameter("Rule-Name:" + ruleName + ",action:REMOVE")
			.prevValue(change.getNewValue())
			.newValue(null);
		
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, ruleName);
		
		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Remove-MalwareFilterRule", parameters))
			.thenApply(result -> {
				if (result != null && !result.has(Constants.ERROR_FIELD)) {
					logger.info("Successfully removed malware filter rule during rollback: {}", ruleName);
					rollbackChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully removed malware filter rule during rollback: " + ruleName, List.of(rollbackChange));
				} else {
					String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
					logger.error("Failed to remove malware filter rule during rollback: {}, {}", ruleName, error);
					rollbackChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to remove malware filter rule during rollback: " + ruleName + ", " + error, List.of(rollbackChange));
				}
			})
			.exceptionally(ex -> {
				logger.error("Exception during removing malware filter rule in rollback", ex);
				rollbackChange.status(ParameterChangeStatus.FAILED);
				return IPolicyRemediator.failed_(getPolicyId(), "Exception during removing malware filter rule in rollback: " + ex.getMessage(), List.of(rollbackChange));
			});
	}
	
	private CompletableFuture<PolicyChangeResult> rollbackUpdate(String policyId, ParameterChangeResult change) {
		String ruleIdentity = extractRuleIdentityFromParam(change.getParameter());
		if (ruleIdentity == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(policyId, "Failed to extract rule identity for update rollback"));
		}
		
		logger.info("Rolling back rule update by restoring previous values: {}", ruleIdentity);
		
		ParameterChangeResult rollbackChange = new ParameterChangeResult()
			.timeStamp(Instant.now())
			.parameter("Rule-" + ExoConstants.IDENTITY + ":" + ruleIdentity + ",action:ROLLBACK")
			.prevValue(change.getNewValue())
			.newValue(change.getPrevValue());
		
		if (change.getPrevValue() == null || !(change.getPrevValue() instanceof Map)) {
			logger.warn("Invalid previous values format for rollback update");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(policyId, "Invalid previous values for rollback update"));
		}
		
		@SuppressWarnings("unchecked")
		Map<String, Object> parameters = (Map<String, Object>) change.getPrevValue();
		parameters.put(IDENTITY, ruleIdentity);
		
		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-MalwareFilterRule", parameters))
			.thenApply(result -> {
				if (result != null && !result.has(Constants.ERROR_FIELD)) {
					logger.info("Successfully rolled back malware filter rule update: {}", ruleIdentity);
					rollbackChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(policyId, "Successfully rolled back malware filter rule update: " + ruleIdentity, List.of(rollbackChange));
				} else {
					String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
					logger.error("Failed to roll back malware filter rule update: {}, {}", ruleIdentity, error);
					rollbackChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(policyId, "Failed to roll back malware filter rule update: " + ruleIdentity + ", " + error, List.of(rollbackChange));
				}
			})
			.exceptionally(ex -> {
				logger.error("Exception during rolling back malware filter rule update", ex);
				rollbackChange.status(ParameterChangeStatus.FAILED);
				return IPolicyRemediator.failed_(policyId, "Exception during rolling back malware filter rule update: " + ex.getMessage(), List.of(rollbackChange));
			});
	}
	
	private String extractRuleNameFromParam(String param) {
		if (param == null) return null;
		
		if (param.contains("Rule-Name:")) {
			int start = param.indexOf("Rule-Name:") + "Rule-Name:".length();
			int end = param.indexOf(",", start);
			if (end == -1) end = param.length();
			return param.substring(start, end);
		}
		
		return null;
	}
	
	private String extractRuleIdentityFromParam(String param) {
		if (param == null) return null;
		
		if (param.contains("Rule-Identity:")) {
			int start = param.indexOf("Rule-Identity:") + "Rule-Identity:".length();
			int end = param.indexOf(",", start);
			if (end == -1) end = param.length();
			return param.substring(start, end);
		}
		
		return null;
	}
}
