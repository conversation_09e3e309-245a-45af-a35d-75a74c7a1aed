package io.syrix.products.microsoft.teams.serialization;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomDateDeserializer extends JsonDeserializer<OffsetDateTime> {
    private static final Pattern DATE_PATTERN = Pattern.compile("\\\\/Date\\((\\d+)\\)\\\\/");

    @Override
    public OffsetDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String dateString = p.getText();
        Matcher matcher = DATE_PATTERN.matcher(dateString);
        if (matcher.matches()) {
            long milliseconds = Long.parseLong(matcher.group(1));
            return OffsetDateTime.ofInstant(Instant.ofEpochMilli(milliseconds), ZoneOffset.UTC);
        }
        throw new IOException("Invalid date format: " + dateString);
    }
}