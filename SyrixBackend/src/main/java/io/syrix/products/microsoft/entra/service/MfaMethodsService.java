package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for retrieving enabled MFA methods in Microsoft Entra ID.
 */
public class MfaMethodsService {
	private static final Logger logger = LoggerFactory.getLogger(MfaMethodsService.class);
	private static final String MFA_METHODS_ENDPOINT = "/policies/authenticationMethodsPolicy";
	private final MicrosoftGraphClient graphClient;

	public MfaMethodsService(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
	}

	/**
	 * Retrieves all enabled MFA methods in the tenant.
	 *
	 * @return CompletableFuture<List<MfaMethod>> List of enabled MFA methods
	 */
	public CompletableFuture<List<MfaMethod>> getEnabledMfaMethods() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta() // Using beta endpoint as this API is in beta
						.withEndpoint(MFA_METHODS_ENDPOINT)
						.withMethod(HttpMethod.GET)
						.build())
				.thenApply(response -> {
					List<MfaMethod> methods = new ArrayList<>();
					JsonNode authMethodsNode = response.path("authenticationMethods");
					if (authMethodsNode.isArray()) {
						for (JsonNode method : authMethodsNode) {
							if (method.has("state") && "enabled".equals(method.get("state").asText())) {
								methods.add(new MfaMethod(
										method.get("id").asText(),
										method.get("state").asText()
								));
							}
						}
						logger.info("Retrieved {} enabled MFA methods", methods.size());
					}
					return methods;
				})
				.exceptionally(e -> {
					logger.error("Failed to retrieve MFA methods: {}", e.getMessage(), e);
					return Collections.emptyList();
				});
	}

	/**
	 * Represents an MFA method configuration.
	 */
	public record MfaMethod(
			String id,
			String state
	) {
		@Override
		public String toString() {
			return "MfaMethod{id='" + id + "', state='" + state + "'}";
		}
	}
}
