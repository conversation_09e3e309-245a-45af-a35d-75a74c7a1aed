package io.syrix.products.microsoft.exo.remediation.malware;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.AttachmentFilterConfig;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.ExchangeBaseRemediator;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for policy MS.EXO.9.1v2 which ensures that email attachment filtering
 * is configured to block potentially malicious file attachments.
 * <p>
 * This remediator checks if a malware filter policy exists for attachment filtering.
 * If no policy exists, it creates a new policy with appropriate settings.
 */
public class MalwareAttachmentFilterPolicyRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	private static final String ENABLE_FILE_FILTER = "EnableFileFilter";
	private static final String FILE_TYPES = "FileTypes";
	private static final String FILE_TYPE_ACTION = "FileTypeAction";
	private static final String IDENTITY = "Identity";
	private static final String UNKNOWN_ERROR = "Unknown error";
	private static final String ADMIN_DISPLAY_NAME = "AdminDisplayName";

	private final AttachmentFilterConfig config;
	private final String parentPolicyId;


	/**
	 * Constructs a new MalwareAttachmentFilterRemediator with the required dependencies.
	 *
	 * @param graphClient        GraphClient for Microsoft Graph operations
	 * @param exchangeClient     PowerShell client for Exchange operations
	 * @param configNode         JSON configuration node containing malware policy data
	 * @param remediationContext Context for the remediation operation
	 * @param remediationConfig  Configuration for the remediation
	 */
	public MalwareAttachmentFilterPolicyRemediator(MicrosoftGraphClient graphClient,
												   PowerShellClient exchangeClient,
												   ObjectNode configNode,
												   ExchangeRemediationContext remediationContext,
												   ExchangeRemediationConfig remediationConfig,
												   String parentPolicyId) {
		super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
		this.config = remediationConfig.getAttachmentFilterConfig();
		this.parentPolicyId = parentPolicyId;
	}

	public MalwareAttachmentFilterPolicyRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, String parentPolicyId) {
		super(graphClient, exchangeClient, null, null, null);
		this.config = null;
		this.parentPolicyId = parentPolicyId;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} - Malware Attachment Filter Rule", getPolicyId());

		// Check if there are any malware policies from configuration
		List<MalwarePolicy> existingPolicies = getMalwarePolicies();

		if (existingPolicies == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_MALWARE_POLICY));
		}


		Optional<MalwarePolicy> syrixPolicy = existingPolicies.stream()
				.filter(policy -> config.getPolicyName().equals(policy.name))
				.findFirst();

		if (syrixPolicy.isPresent()) {
			logger.info("Attachment filtering already configured in policy: {}", syrixPolicy.get().name);
			return updateSyrixPolicyIfNeed(syrixPolicy.get());
		} else {
			return createSyrixPolicy();
		}
	}

	private CompletableFuture<PolicyChangeResult> updateSyrixPolicyIfNeed(MalwarePolicy syrixPolicy) {
		if (!isPolicyValid(syrixPolicy)) {
			return updateMalwarePolicy(syrixPolicy.identity, buildPolicyParameters(), toPolicyParameters(syrixPolicy));
		}
		return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Malware filter policy requirement met"));
	}

	private CompletableFuture<PolicyChangeResult> createSyrixPolicy() {
		return createNewMalwarePolicy(config.getPolicyName(), buildPolicyParameters());
	}


	private boolean isPolicyValid(MalwarePolicy malwarePolicy) {
		// Check if file filter is enabled
		if (malwarePolicy.enableFileFilter == null || !malwarePolicy.enableFileFilter) {
			return false;
		}
		// Check if file type action matches the configured action
		if (malwarePolicy.fileTypeAction == null || !malwarePolicy.fileTypeAction.equals(config.getAction().getAction())) {
			return false;
		}
		// Check if all required file types are included
		if (malwarePolicy.fileTypes == null || malwarePolicy.fileTypes.isEmpty()) {
			return false;
		}

		// Convert JsonNode array to a list of strings for easier comparison
		List<String> configuredFileTypes = config.getFilesTypeList().getFileTypeList();
		List<String> currentFileTypes = malwarePolicy.fileTypes;

		// Check if all required file types are in the current configuration
		for (String requiredType : configuredFileTypes) {
			if (!currentFileTypes.contains(requiredType)) {
				logger.info("Required file type {} is missing", requiredType);
				return false;
			}
		}

		// If ZapEnabled is specified in the config, check if it matches
		if (config.getZapEnabled() != null && (!config.getZapEnabled().equals(malwarePolicy.zapEnabled))) {
			logger.info("ZapEnabled setting doesn't match");
			return false;
		}

		// Policy is correctly configured
		return true;
	}

	private CompletableFuture<PolicyChangeResult> updateMalwarePolicy(String policyIdentity,
																	  Map<String, Object> newParameters,
																	  Map<String, Object> prevParameters) {
		logger.info("Updating malware filter policy: {}", policyIdentity);
		newParameters.put(IDENTITY, policyIdentity);

		// Ensure the identity parameter is set
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Policy-" + ExoConstants.IDENTITY + ":" + policyIdentity + ",action:UPDATE")
				.prevValue(prevParameters)
				.newValue(newParameters);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-MalwareFilterPolicy", newParameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully updated malware filter policy: {}", policyIdentity);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully updated malware filter policy:" + policyIdentity, List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to update malware filter policy: {}, {}", policyIdentity, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.success_(getPolicyId(), "Failed to update malware filter policy:" + policyIdentity + "," + error, List.of(paramChange));
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during malware filter policy update", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during malware filter policy update: " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> createNewMalwarePolicy(String policyName, Map<String, Object> parameters) {
		logger.info("Creating malware filter policy: {}", policyName);

		// Ensure the name parameter is set
		if (!parameters.containsKey("Name")) {
			parameters = new HashMap<>(parameters);
			parameters.put("Name", policyName);
		}

		// Ensure the identity parameter is set
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Policy-Name:" + policyName + ",action:CREATE")
				.prevValue(null)
				.newValue(parameters);


		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("New-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully created malware filter policy, name: {}", policyName);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully created malware filter policy, name :" + policyName, List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to create malware filter policy, name: {}, {}", policyName, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.success_(getPolicyId(), "Failed to create malware filter policy, name:" + policyName + "," + error, List.of(paramChange));
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during malware filter policy creation", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during malware filter policy creation: " + ex.getMessage(), List.of(paramChange));
				});
	}

	@Override
	public String getPolicyId() {
		return parentPolicyId;
	}

	private Map<String, Object> toPolicyParameters(MalwarePolicy policy) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, policy.identity);
		parameters.put(ADMIN_DISPLAY_NAME, policy.adminDisplayName);
		parameters.put(ENABLE_FILE_FILTER, policy.enableFileFilter);
		parameters.put(FILE_TYPE_ACTION, policy.fileTypeAction);
		parameters.put(FILE_TYPES, policy.fileTypes);

		return parameters;
	}

	/**
	 * Builds the parameters required for malware filter policy.
	 *
	 * @return Map of parameters for the PowerShell cmdlet
	 */
	private Map<String, Object> buildPolicyParameters() {
		// Build parameters for creating or updating the policy
		Map<String, Object> parameters = new HashMap<>();

		parameters.put(ENABLE_FILE_FILTER, true);
		parameters.put(FILE_TYPE_ACTION, remediationConfig.getAttachmentFilterConfig().getAction().getAction());
		parameters.put(FILE_TYPES, remediationConfig.getAttachmentFilterConfig().getFilesTypeList().getFileTypes());

		// Add admin display name if available
		if (remediationConfig.getAttachmentFilterConfig().getAdminDisplayName() != null) {
			parameters.put(ADMIN_DISPLAY_NAME, remediationConfig.getAttachmentFilterConfig().getAdminDisplayName());
		}
		if (remediationConfig.getAttachmentFilterConfig().getZapEnabled() != null) {
			parameters.put("ZapEnabled", remediationConfig.getAttachmentFilterConfig().getZapEnabled());
		}
		return parameters;
	}

	/**
	 * Implements the rollback method required by IPolicyRemediatorRollback interface.
	 * This method reverts changes made during the remediation process.
	 *
	 * @param fixResult The result of the remediation operation
	 * @return CompletableFuture containing the result of the rollback operation
	 */
	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} - Malware Attachment Filter Policy", getPolicyId());

		if (fixResult == null || fixResult.getResult() != RemediationResult.SUCCESS || 
			fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(getPolicyId(), "No suitable fix result provided for policy rollback"));
		}

		// Ищем изменения связанные с политикой
		ParameterChangeResult policyChange = null;
		for (ParameterChangeResult change : fixResult.getChanges()) {
			String parameter = change.getParameter();
			if (parameter != null && parameter.startsWith("Policy-")) {
				policyChange = change;
				break;
			}
		}

		if (policyChange == null) {
			logger.info("No policy changes found in the fix result");
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(getPolicyId(), "No policy changes found"));
		}

		String parameter = policyChange.getParameter();
		if (parameter.contains("action:CREATE")) {
			String policyName = extractValueFromParameter(parameter, "Name:");
			return removeMalwarePolicy(policyName, policyChange.getNewValue());
		} else if (parameter.contains("action:UPDATE")) {
			String policyIdentity = extractValueFromParameter(parameter, ExoConstants.IDENTITY + ":");
			@SuppressWarnings("unchecked")
			Map<String, Object> prevValue = (Map<String, Object>) policyChange.getPrevValue();
			return restorePolicySettings(policyIdentity, prevValue);
		} else {
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(getPolicyId(), "Unsupported action type for policy rollback:"+parameter));
		}
	}

	/**
	 * Extracts a value from a parameter string.
	 * For example, extracts "Default" from "Policy-Name:Default,action:CREATE"
	 *
	 * @param parameter The parameter string
	 * @param prefix    The prefix to look for
	 * @return The extracted value, or null if not found
	 */
	private String extractValueFromParameter(String parameter, String prefix) {
		int startIndex = parameter.indexOf(prefix);
		if (startIndex >= 0) {
			startIndex += prefix.length();
			int endIndex = parameter.indexOf(",", startIndex);
			if (endIndex < 0) {
				endIndex = parameter.length();
			}
			return parameter.substring(startIndex, endIndex);
		}
		return null;
	}

	/**
	 * Removes a malware policy that was created during remediation.
	 *
	 * @param policyName The name of the policy to remove
	 * @return CompletableFuture containing the result of the removal operation
	 */
	private CompletableFuture<PolicyChangeResult> removeMalwarePolicy(String policyName, Object prevValue) {
		logger.info("Rolling back by removing malware filter policy: {}", policyName);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Identity", policyName);
		parameters.put("Confirm", false);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Policy-Name:" + policyName + ",action:REMOVE")
				.prevValue(prevValue)
				.newValue(null);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Remove-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully removed malware filter policy: {}", policyName);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully removed malware filter policy: " + policyName, List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to remove malware filter policy: {}, {}", policyName, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to remove malware filter policy: " + policyName + ", " + error, List.of(paramChange));
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during malware filter policy removal", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during malware filter policy removal: " + ex.getMessage(), List.of(paramChange));
				});
	}

	/**
	 * Restores previous settings for a malware policy.
	 *
	 * @param policyIdentity The identity of the policy to restore
	 * @param prevParameters The previous parameters to restore
	 * @return CompletableFuture containing the result of the restore operation
	 */
	private CompletableFuture<PolicyChangeResult> restorePolicySettings(String policyIdentity, Map<String, Object> prevParameters) {
		logger.info("Rolling back by restoring previous settings for malware filter policy: {}", policyIdentity);

		// Make sure identity is set in parameters
		Map<String, Object> parameters = new HashMap<>(prevParameters);
		parameters.put(IDENTITY, policyIdentity);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("Policy-" + ExoConstants.IDENTITY + ":" + policyIdentity + ",action:RESTORE")
				.prevValue(null)
				.newValue(parameters);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully restored previous settings for malware filter policy: {}", policyIdentity);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully restored previous settings for malware filter policy: " + policyIdentity, List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to restore previous settings for malware filter policy: {}, {}", policyIdentity, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to restore previous settings for malware filter policy: " + policyIdentity + ", " + error, List.of(paramChange));
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during restoring previous settings for malware filter policy", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Exception during restoring previous settings for malware filter policy: " + ex.getMessage(), List.of(paramChange));
				});
	}
}
