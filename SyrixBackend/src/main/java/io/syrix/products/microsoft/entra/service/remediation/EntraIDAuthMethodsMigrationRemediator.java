package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.entra.service.EntraIDConstants.AUTH_METHODS_POLICY_ENDPOINT;

/**
 * Implements remediation for MS.AAD.3.4v1: Complete authentication methods migration.
 */
@PolicyRemediator("MS.AAD.3.4v1")
public class EntraIDAuthMethodsMigrationRemediator extends RemediatorBase {
	private static final String MIGRATION_COMPLETE = "migrationComplete";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDAuthMethodsMigrationRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for authentication methods migration");

		return checkCurrentMigrationState()
				.thenCompose(currentState -> {
					if (MIGRATION_COMPLETE.equals(currentState)) {
						logger.info("Migration is already complete, no action needed");
						return IPolicyRemediator.success(getPolicyId(),
								"Authentication Methods Migration was already complete");
					}
					return completeMigration();
				})
				.exceptionally(ex -> {
					logger.error("Exception while configuring authentication methods migration", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<String> checkCurrentMigrationState() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint(AUTH_METHODS_POLICY_ENDPOINT)
						.withMethod(HttpMethod.GET)
						.build())
				.thenApply(response -> {
					String state = response.path("policyMigrationState").asText();
					logger.info("Current migration state: {}", state);
					return state;
				});
	}

	private CompletableFuture<JsonNode> completeMigration() {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		policyPayload.put("policyMigrationState", MIGRATION_COMPLETE);

		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.beta()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(AUTH_METHODS_POLICY_ENDPOINT)
							.build()
			).thenCompose(policyResult -> {
				// PATCH returns 204 No Content on success, so a null result is expected
				if (policyResult == null || !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Successfully completed authentication methods migration");
					return IPolicyRemediator.success(getPolicyId(),
							"Authentication Methods Migration has been set to Migration Complete");
				} else {
					String error = policyResult.has(Constants.ERROR_FIELD) ?
							policyResult.get(Constants.ERROR_FIELD).asText() : "Unknown error";
					logger.error("Failed to complete authentication methods migration: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to complete authentication methods migration", e);
		}
	}

	/**
	 * Checks if the authentication methods migration is complete.
	 */
	public CompletableFuture<Boolean> checkMigrationComplete() {
		return checkCurrentMigrationState()
				.thenApply(state -> {
					boolean isComplete = MIGRATION_COMPLETE.equals(state);
					logger.info("Authentication methods migration is {}",
							isComplete ? "complete" : "not complete");
					return isComplete;
				})
				.exceptionally(e -> {
					logger.error("Failed to check migration state: {}", e.getMessage(), e);
					return false;
				});
	}
}