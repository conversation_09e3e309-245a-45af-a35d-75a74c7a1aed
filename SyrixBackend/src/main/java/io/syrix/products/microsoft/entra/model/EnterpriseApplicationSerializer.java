package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

/**
 * Serialization helpers for Enterprise Applications.
 */
public class EnterpriseApplicationSerializer {

	private static final ObjectMapper mapper = new ObjectMapper();

	private  EnterpriseApplicationSerializer() {}
	/**
	 * Serializes an EnterpriseApplication to JSON.
	 */
	public static JsonNode serialize(EnterpriseApplication app) {
		ObjectNode root = mapper.createObjectNode();

		// Basic details
		if (app.getBasic() != null) {
			root.set("basic", serializeBasicDetails(app.getBasic()));
		}

		// Permissions
		if (app.getPermissions() != null) {
			root.set("permissions", mapper.valueToTree(app.getPermissions()));
		}

		// Delegated permissions
		if (app.getDelegatedPermissions() != null) {
			root.set("delegatedPermissions", mapper.valueToTree(app.getDelegatedPermissions()));
		}

		// Branding
		if (app.getBranding() != null) {
			root.set("branding", mapper.valueToTree(app.getBranding()));
		}

		// Assigned users
		if (app.getAssignedUsers() != null) {
			root.set("assignedUsers", mapper.valueToTree(app.getAssignedUsers()));
		}

		// Risk level calculated field
		root.set("riskLevel", mapper.valueToTree(app.getRiskLevel()));
		root.set("justification", mapper.valueToTree(app.getJustification()));

		return root;
	}

	public static String serializeToString(EnterpriseApplication app) {
		JsonNode  root = serialize(app);
		try {
			return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	private static ObjectNode serializeBasicDetails(BasicDetails basic) {
		ObjectNode node = mapper.createObjectNode();
		node.set("id", mapper.valueToTree(basic.getId()));
		node.set("displayName", mapper.valueToTree(basic.getDisplayName()));
		node.set("publisherName", mapper.valueToTree(basic.getPublisherName()));

		if (basic.getAppRoles() != null) {
			node.set("appRoles", mapper.valueToTree(basic.getAppRoles()));
		}
		return node;
	}

	/**
	 * Custom deserializer for EnterpriseApplication.
	 */
	public static EnterpriseApplication deserialize(String json) throws Exception {
		return mapper.readValue(json, EnterpriseApplication.class);
	}

	/**
	 * Serializes a list of enterprise applications to JSON.
	 */
	public static String serializeList(List<EnterpriseApplication> apps) throws Exception {
		return mapper.writerWithDefaultPrettyPrinter()
				.writeValueAsString(mapper.valueToTree(apps));
	}

	/**
	 * Custom serialization mixin to handle enums.
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	abstract static class RiskLevelMixin {
		@JsonProperty
		abstract String name();
	}

	static {
		// Register mixins
		mapper.addMixIn(RiskLevel.class, RiskLevelMixin.class);
	}

	/**
	 * Example output format:
	 * {
	 *   "basic": {
	 *     "id": "...",
	 *     "displayName": "...",
	 *     "publisherName": "...",
	 *     "appRoles": [...]
	 *   },
	 *   "permissions": [
	 *     {
	 *       "permissionType": "...",
	 *       "appRoleId": "...",
	 *       "appRoleDisplayName": "...",
	 *       "appRoleDescription": "...",
	 *       "appRoleValue": "...",
	 *       "resourceId": "...",
	 *       "resourceDisplayName": "...",
	 *       "consentType": "..."
	 *     }
	 *   ],
	 *   "delegatedPermissions": [
	 *     {
	 *       "scope": "...",
	 *       "resourceId": "...",
	 *       "resourceDisplayName": "...",
	 *       "consentType": "..."
	 *     }
	 *   ],
	 *   "branding": {
	 *     "logoUrl": "..."
	 *   },
	 *   "assignedUsers": [
	 *     {
	 *       "principalType": "...",
	 *       "displayName": "...",
	 *       "userPrincipalName": "...",
	 *       "appRoleId": "...",
	 *       "appRoleDisplayName": "..."
	 *     }
	 *   ],
	 *   "riskLevel": "..."
	 * }
	 */
}