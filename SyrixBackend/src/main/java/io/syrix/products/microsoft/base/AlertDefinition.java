package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.HashMap;
import java.util.Map;

/**
 * Defines the configuration for a Microsoft 365 Protection Alert.
 * This class stores all properties in a single map for flexibility,
 * while providing type-safe access to common properties.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlertDefinition {

	// Constants for standard property names
	public static final String ID = "Id";
	public static final String NAME = "Name";
	public static final String DESCRIPTION = "Description";
	public static final String CATEGORY = "Category";
	public static final String SEVERITY = "Severity";
	public static final String OPERATION = "Operation";
	public static final String NOTIFICATION_ENABLED = "NotificationEnabled";
	public static final String AGGREGATION_TYPE = "AggregationType";
	public static final String THREAT_TYPE = "ThreatType";
	public static final String THRESHOLD = "Threshold";
	public static final String TIME_WINDOW = "TtimeWindow";
	public static final String FILTER = "Filter";

	// Store properties in a single map
	private final Map<String, Object> properties = new HashMap<>();

	/**
	 * Default constructor for Jackson deserialization.
	 */
	public AlertDefinition() {
	}

	/**
	 * Constructs a minimal AlertDefinition with just id and name.
	 */
	public AlertDefinition(String id, String name) {
		setId(id);
		setName(name);
	}

	/**
	 * Get the unique identifier of this alert configuration.
	 */
	@JsonProperty("id")
	public String getId() {
		return (String) properties.get(ID);
	}

	/**
	 * Set the unique identifier of this alert configuration.
	 */
	@JsonProperty("id")
	public void setId(String id) {
		properties.put(ID, id);
	}

	/**
	 * Get the name of the alert as it will appear in Microsoft 365.
	 */
	@JsonProperty("name")
	public String getName() {
		return (String) properties.get(NAME);
	}

	/**
	 * Set the name of the alert as it will appear in Microsoft 365.
	 */
	@JsonProperty("name")
	public void setName(String name) {
		properties.put(NAME, name);
	}

	/**
	 * Get the description of the alert.
	 */
	@JsonIgnore
	public String getDescription() {
		return (String) properties.get(DESCRIPTION);
	}

	/**
	 * Set the description of the alert.
	 */
	@JsonIgnore
	public void setDescription(String description) {
		properties.put(DESCRIPTION, description);
	}

	/**
	 * Get the category of the alert.
	 */
	@JsonIgnore
	public String getCategory() {
		return (String) properties.get(CATEGORY);
	}

	/**
	 * Set the category of the alert.
	 */
	@JsonIgnore
	public void setCategory(String category) {
		properties.put(CATEGORY, category);
	}

	/**
	 * Get the severity of the alert.
	 */
	@JsonIgnore
	public String getSeverity() {
		return (String) properties.get(SEVERITY);
	}

	/**
	 * Set the severity of the alert.
	 */
	@JsonIgnore
	public void setSeverity(String severity) {
		properties.put(SEVERITY, severity);
	}

	/**
	 * Get the operation that triggers the alert.
	 */
	@JsonIgnore
	public String getOperation() {
		return (String) properties.get(OPERATION);
	}

	/**
	 * Set the operation that triggers the alert.
	 */
	@JsonIgnore
	public void setOperation(String operation) {
		properties.put(OPERATION, operation);
	}

	/**
	 * Check if notifications are enabled for this alert.
	 */
	@JsonIgnore
	public Boolean isNotificationEnabled() {
		return (Boolean) properties.get(NOTIFICATION_ENABLED);
	}

	/**
	 * Set whether notifications are enabled for this alert.
	 */
	@JsonIgnore
	public void setNotificationEnabled(Boolean enabled) {
		properties.put(NOTIFICATION_ENABLED, enabled);
	}

	/**
	 * Get the aggregation type for this alert.
	 */
	@JsonIgnore
	public String getAggregationType() {
		return (String) properties.get(AGGREGATION_TYPE);
	}

	/**
	 * Set the aggregation type for this alert.
	 */
	@JsonIgnore
	public void setAggregationType(String aggregationType) {
		properties.put(AGGREGATION_TYPE, aggregationType);
	}

	/**
	 * Get the threat type for this alert.
	 */
	@JsonIgnore
	public String getThreatType() {
		return (String) properties.get(THREAT_TYPE);
	}

	/**
	 * Set the threat type for this alert.
	 */
	@JsonIgnore
	public void setThreatType(String threatType) {
		properties.put(THREAT_TYPE, threatType);
	}

	/**
	 * Get the threshold for this alert.
	 */
	@JsonIgnore
	public Integer getThreshold() {
		Object value = properties.get(THRESHOLD);
		if (value instanceof Number) {
			return ((Number) value).intValue();
		}
		return null;
	}

	/**
	 * Set the threshold for this alert.
	 */
	@JsonIgnore
	public void setThreshold(Integer threshold) {
		properties.put(THRESHOLD, threshold);
	}

	/**
	 * Get the time window for this alert.
	 */
	@JsonIgnore
	public Integer getTimeWindow() {
		Object value = properties.get(TIME_WINDOW);
		if (value instanceof Number) {
			return ((Number) value).intValue();
		}
		return null;
	}

	/**
	 * Set the time window for this alert.
	 */
	@JsonIgnore
	public void setTimeWindow(Integer timeWindow) {
		properties.put(TIME_WINDOW, timeWindow);
	}

	/**
	 * Get the filter for this alert.
	 */
	@JsonIgnore
	public String getFilter() {
		return (String) properties.get(FILTER);
	}

	/**
	 * Set the filter for this alert.
	 */
	@JsonIgnore
	public void setFilter(String filter) {
		properties.put(FILTER, filter);
	}

	/**
	 * Catch-all getter for any property.
	 */
	@JsonAnyGetter
	public Map<String, Object> getProperties() {
		return properties;
	}

	/**
	 * Get the parameter map, excluding id and name.
	 * This is for use with PowerShell parameter maps.
	 */
	@JsonIgnore
	public Map<String, Object> getParametersMap() {
		Map<String, Object> params = new HashMap<>(properties);
		params.remove(ID);
		params.remove(NAME);
		return params;
	}

	/**
	 * Get any property by its key.
	 */
	public Object getProperty(String key) {
		return properties.get(key);
	}

	/**
	 * Catch-all setter for any property.
	 */
	@JsonAnySetter
	public void setProperty(String key, Object value) {
		properties.put(key, value);
	}

	/**
	 * Remove a property by its key.
	 */
	public Object removeProperty(String key) {
		return properties.remove(key);
	}

	/**
	 * Check if a property exists.
	 */
	public boolean hasProperty(String key) {
		return properties.containsKey(key);
	}

	/**
	 * Builder-style method to add a property and return this instance.
	 */
	public AlertDefinition withProperty(String key, Object value) {
		setProperty(key, value);
		return this;
	}

	@Override
	public String toString() {
		return "AlertDefinition { id='" + getId() + "\', name='" + getName() + "\', properties=" + properties + '}';
	}
}