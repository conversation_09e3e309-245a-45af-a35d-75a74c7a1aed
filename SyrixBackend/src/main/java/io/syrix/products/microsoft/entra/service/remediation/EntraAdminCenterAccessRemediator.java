package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.DelegatedMicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;

/**
 * This remediator enforces restricting non-admin access to the Entra admin center
 * as per CIS Microsoft 365 Foundations Benchmark *******.
 * 
 * The restrictNonAdminAccess setting controls whether non-administrator users
 * can access the Entra admin center. When set to true, only users with admin
 * roles can access the admin center.
 */
@PolicyRemediator("MS.AAD.7.10v1")
public class EntraAdminCenterAccessRemediator extends RemediatorBase {
    private static final String UX_SETTING_ENDPOINT = "/admin/entra/uxSetting";
    private static final String RESTRICT_NON_ADMIN_ACCESS_FIELD = "restrictNonAdminAccess";
    private static final String REQUIRED_VALUE = "true";
    
    private final MicrosoftGraphClient graphClient;
    private final DelegatedMicrosoftGraphClient delegatedGraphClient;
    private final ObjectMapper objectMapper;

    public EntraAdminCenterAccessRemediator(MicrosoftGraphClient graphClient) {
        this.graphClient = graphClient;
        this.delegatedGraphClient = DelegatedMicrosoftGraphClient.fromApplicationClient(graphClient);
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        logger.info("Entra admin center access restriction remediation for {} - API not supported", getPolicyId());
        
        return IPolicyRemediator.notImplemented(getPolicyId());
//            "Microsoft Graph API does not support programmatic access to UX settings (/admin/entra/uxSetting). " +
//            "This configuration must be set manually through the Entra admin center. " +
//            "Manual steps: 1) Navigate to Entra admin center (https://entra.microsoft.com) " +
//            "2) Go to Settings > User settings " +
//            "3) Set 'Restrict access to Entra admin center' to 'Yes'");
    }

    public CompletableFuture<Boolean> validateConfiguration() {
        logger.info("Validation for {} - API not supported, returning false", getPolicyId());
        
        // Since we cannot access the API to validate the configuration,
        // we return false to indicate the configuration cannot be verified programmatically
        return CompletableFuture.completedFuture(false);
    }

    private CompletableFuture<JsonNode> getCurrentConfiguration() {
        logger.debug("Retrieving current Entra UX setting configuration using delegated authentication");
        
        return delegatedGraphClient.makeGraphRequest(
                GraphRequest.builder()
                        .beta()
                        .withEndpoint(UX_SETTING_ENDPOINT)
                        .withMethod(HttpMethod.GET)
                        .build()
        ).exceptionally(ex -> {
            logger.error("Failed to retrieve UX setting configuration", ex);
            return null;
        });
    }

    private CompletableFuture<JsonNode> performRemediation() {
        logger.info("Performing remediation to restrict non-admin access to Entra admin center");
        
        return getCurrentConfiguration()
                .thenCompose(currentConfig -> {
                    if (currentConfig == null) {
                        return IPolicyRemediator.failed(getPolicyId(), 
                            "Cannot retrieve current configuration to perform remediation");
                    }
                    
                    JsonNode currentRestriction = currentConfig.path(RESTRICT_NON_ADMIN_ACCESS_FIELD);
                    String previousValue = String.valueOf(currentRestriction.asBoolean(false));
                    
                    // Create the updated configuration
                    ObjectNode updatedConfig = objectMapper.createObjectNode();
                    updatedConfig.put(RESTRICT_NON_ADMIN_ACCESS_FIELD, true);
                    
                    return updateConfiguration(updatedConfig)
                            .thenApply(success -> {
                                if (Boolean.TRUE.equals(success)) {
                                    // Record parameter change for audit
                                    ParameterChangeResult paramChange = new ParameterChangeResult()
                                            .timeStamp(Instant.now())
                                            .parameter(RESTRICT_NON_ADMIN_ACCESS_FIELD)
                                            .prevValue(previousValue)
                                            .newValue(REQUIRED_VALUE)
                                            .status(ParameterChangeStatus.SUCCESS);
                                    
                                    logger.info("Successfully updated Entra admin center access restriction: {} -> {}", 
                                            previousValue, REQUIRED_VALUE);
                                    
                                    return IPolicyRemediator.success(getPolicyId(), 
                                        "Successfully restricted non-admin access to Entra admin center. " +
                                        "Parameter change: " + RESTRICT_NON_ADMIN_ACCESS_FIELD + 
                                        " changed from " + previousValue + " to " + REQUIRED_VALUE).join();
                                } else {
                                    return IPolicyRemediator.failed(getPolicyId(), 
                                        "Failed to update Entra admin center access restriction").join();
                                }
                            });
                })
                .exceptionally(ex -> {
                    logger.error("Exception during remediation performance", ex);
                    return IPolicyRemediator.failed(getPolicyId(), 
                        "Remediation failed with exception: " + ex.getMessage()).join();
                });
    }

    private CompletableFuture<Boolean> updateConfiguration(ObjectNode updatedConfig) {
        logger.debug("Updating Entra UX setting configuration using delegated authentication");
        
        try {
            String requestBody = objectMapper.writeValueAsString(updatedConfig);
            
            return delegatedGraphClient.makeGraphRequest(
                    GraphRequest.builder()
                            .beta()
                            .withEndpoint(UX_SETTING_ENDPOINT)
                            .withMethod(HttpMethod.PATCH)
                            .addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
                            .withBody(HttpRequest.BodyPublishers.ofString(requestBody))
                            .build()
            ).thenApply(response -> {
                logger.info("UX setting update response received");
                return true;
            }).exceptionally(ex -> {
                logger.error("Failed to update UX setting configuration", ex);
                return false;
            });
            
        } catch (Exception e) {
            logger.error("Failed to serialize request body for UX setting update", e);
            return CompletableFuture.completedFuture(false);
        }
    }

    public CompletableFuture<JsonNode> rollback() {
        logger.info("Rollback for {} - API not supported, manual intervention required", getPolicyId());
        
        return IPolicyRemediator.notImplemented(getPolicyId());
//            "Microsoft Graph API does not support programmatic rollback of UX settings. " +
//            "Manual rollback steps: 1) Navigate to Entra admin center (https://entra.microsoft.com) " +
//            "2) Go to Settings > User settings " +
//            "3) Set 'Restrict access to Entra admin center' to 'No'");
    }
}