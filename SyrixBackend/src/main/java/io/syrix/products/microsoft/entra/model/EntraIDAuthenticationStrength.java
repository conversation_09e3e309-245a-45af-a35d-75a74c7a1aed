package io.syrix.products.microsoft.entra.model;

/**
 * Enum representing different authentication strength options in Microsoft Entra ID.
 * Each option corresponds to a specific authentication strength policy.
 */
public enum EntraIDAuthenticationStrength {

	/**
	 * Passwordless authentication strength.
	 * Requires authentication methods that don't rely on passwords
	 * (e.g., FIDO2, Windows Hello for Business)
	 */
	PASSWORDLESS(
			"00000000-0000-0000-0000-000000000003",
			"Passwordless",
			"Requires authentication methods that don't rely on passwords"
	),

	/**
	 * Phishing-resistant MFA.
	 * Requires authentication methods that are resistant to phishing attacks
	 * (e.g., FIDO2, Windows Hello for Business, certificate-based authentication)
	 */
	PHISHING_RESISTANT_MFA(
			"00000000-0000-0000-0000-000000000004",
			"Phishing-resistant MFA",
			"Requires authentication methods that are resistant to phishing attacks"
	),

	/**
	 * Standard multifactor authentication.
	 * Requires MFA but allows more traditional methods
	 * (e.g., password + SMS, password + app notification)
	 */
	MFA(
			"00000000-0000-0000-0000-000000000002",
			"Multifactor authentication",
			"Combinations of methods that satisfy strong authentication, such as a password + SMS"
	);

	private final String id;
	private final String displayName;
	private final String description;

	/**
	 * Constructor for authentication strength enum values.
	 *
	 * @param id The unique identifier for the authentication strength policy
	 * @param displayName The human-readable name of the authentication strength
	 * @param description A brief description of the authentication strength
	 */
	EntraIDAuthenticationStrength(String id, String displayName, String description) {
		this.id = id;
		this.displayName = displayName;
		this.description = description;
	}

	/**
	 * Gets the unique identifier for the authentication strength policy.
	 * This ID should be used when referencing the authentication strength in API calls.
	 *
	 * @return The ID as a string
	 */
	public String getId() {
		return id;
	}

	/**
	 * Gets the display name of the authentication strength.
	 *
	 * @return The display name as a string
	 */
	public String getDisplayName() {
		return displayName;
	}

	/**
	 * Gets the description of the authentication strength.
	 *
	 * @return The description as a string
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Finds an authentication strength by its ID.
	 *
	 * @param id The ID to look for
	 * @return The matching authentication strength or null if not found
	 */
	public static EntraIDAuthenticationStrength findById(String id) {
		if (id == null) {
			return null;
		}

		for (EntraIDAuthenticationStrength strength : values()) {
			if (strength.getId().equals(id)) {
				return strength;
			}
		}

		return null;
	}

	/**
	 * Checks if this authentication strength is phishing-resistant.
	 *
	 * @return true if the authentication strength is phishing-resistant
	 */
	public boolean isPhishingResistant() {
		return this == PHISHING_RESISTANT_MFA;
	}

	/**
	 * Checks if this authentication strength satisfies MFA requirements.
	 *
	 * @return true if the authentication strength satisfies MFA
	 */
	public boolean satisfiesMfa() {
		return this == MFA || this == PHISHING_RESISTANT_MFA;
	}
}