package io.syrix.products.microsoft.sharepoint.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SharepointGuidSerializer extends JsonSerializer<String> {
    private static final String REGEX = "^/Guid\\(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})\\)/$";
    private static final Pattern PATTERN = Pattern.compile(REGEX);
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        Matcher matcher = PATTERN.matcher(value);
        if (matcher.matches()) {
            gen.writeString(matcher.group(1));
        } else {
            gen.writeString(value);
        }
    }
}