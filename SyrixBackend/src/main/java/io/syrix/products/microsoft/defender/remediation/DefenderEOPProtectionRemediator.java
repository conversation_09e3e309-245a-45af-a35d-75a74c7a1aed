package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;

import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@PolicyRemediator("MS.DEFENDER.1.2v1")
public class DefenderEOPProtectionRemediator extends RemediatorBase {

	private static final String SUCCESS_PRESET_USERS_APPLIED = "Successfully applied users to preset security policies";
	public static final String SUCCESSFULLY_ENABLED_ATP_RULE = "Successfully enabled ATP Rule: {}";

	private final PowerShellClient powershellClient;
	private final ObjectMapper objectMapper;

	// Configuration properties
	private final List<String> excludedUsers;
	private final List<String> excludedGroups;
	private final PolicyType policyType;
	private final boolean whatIf;

	public DefenderEOPProtectionRemediator(PowerShellClient powershellClient) {
		this(powershellClient, new ArrayList<>(), new ArrayList<>(), PolicyType.STANDARD, false);
	}

	public DefenderEOPProtectionRemediator(
			PowerShellClient powershellClient,
			List<String> excludedUsers,
			List<String> excludedGroups,
			PolicyType policyType,
			boolean whatIf) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
		this.excludedUsers = excludedUsers != null ? excludedUsers : new ArrayList<>();
		this.excludedGroups = excludedGroups != null ? excludedGroups : new ArrayList<>();
		this.policyType = policyType != null ? policyType : PolicyType.STANDARD;
		this.whatIf = whatIf;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting MS.DEFENDER.1.2v1 remediation - applying users to {} preset security policy", policyType.policyType);
		logger.info("Excluded users: {}", excludedUsers);
		logger.info("Excluded groups: {}", excludedGroups);
		logger.info("WhatIf mode: {}", whatIf);

		return DefenderHelpers.getAllMailboxes(powershellClient, logger)
				.thenCompose(users -> applyUsersToPresetSecurityPolicy(policyType.policyType, users))
				.exceptionally(ex -> {
					logger.error("Exception while applying users to preset security policies", ex);
					return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), objectMapper);
				});

	}

	private CompletableFuture<List<JsonNode>> excludeGroupMembers(List<JsonNode> users) {
		if (excludedGroups.isEmpty()) {
			return CompletableFuture.completedFuture(users);
		}

		// Process each excluded group
		List<CompletableFuture<Set<String>>> groupMemberFutures = new ArrayList<>();

		for (String groupName : excludedGroups) {
			groupMemberFutures.add(getGroupMembers(groupName));
		}

		return CompletableFuture.allOf(groupMemberFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					// Combine all excluded member UPNs
					Set<String> allExcludedMembers = new HashSet<>();
					groupMemberFutures.forEach(future -> allExcludedMembers.addAll(future.join()));

					logger.info("Excluding {} users who are members of excluded groups", allExcludedMembers.size());

					// Filter out users who are members of excluded groups
					return users.stream()
							.filter(user -> {
								String upn = user.path(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText("");
								String email = user.path(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD).asText("");
								return !allExcludedMembers.contains(upn) && !allExcludedMembers.contains(email);
							})
							.collect(Collectors.toList());
				});
	}

	private CompletableFuture<Set<String>> getGroupMembers(String groupName) {
		logger.info("Getting members of group: {}", groupName);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_UNIFIED_GROUP_CMDLET,
						Map.of(DefenderConstants.IDENTITY_PROPERTY, groupName)
				)
		).thenCompose(group -> {
			if (group == null || group.isEmpty()) {
				logger.warn("Group not found: {}", groupName);
				return CompletableFuture.completedFuture(new HashSet<>());
			}

			String groupIdentity = group.path(Constants.NAME_FIELD).asText("");
			logger.info("Found group: {}", groupIdentity);

			return powershellClient.executeCmdletCommand(
					new PowerShellClient.CommandRequest(
							DefenderConstants.GET_UNIFIED_GROUP_LINKS_CMDLET,
							Map.of(
									DefenderConstants.IDENTITY_PROPERTY, groupIdentity,
									DefenderConstants.LINK_TYPE_PARAM, Constants.MEMBERS_VALUE,
									DefenderConstants.RESULT_SIZE_PARAM, Constants.UNLIMITED_VALUE
							)
					)
			).thenApply(members -> {
				if (members == null || members.isEmpty()) {
					logger.info("No members found in group: {}", groupName);
					return new HashSet<String>();
				}

				Set<String> memberEmails = new HashSet<>();

				// Collect members' email addresses
				StreamSupport.stream(members.spliterator(), false)
						.forEach(member -> {
							String email = member.path(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD).asText("");
							if (!email.isEmpty()) {
								memberEmails.add(email);
							}
						});

				logger.info("Found {} members in group: {}", memberEmails.size(), groupName);
				return memberEmails;
			}).exceptionally(ex -> {
				logger.warn("Could not retrieve members of group '{}': {}", groupName, ex.getMessage());
				return new HashSet<>();
			});
		}).exceptionally(ex -> {
			logger.warn("Error processing group '{}': {}", groupName, ex.getMessage());
			return new HashSet<>();
		});
	}

	private CompletableFuture<JsonNode> applyUsersToPresetSecurityPolicy(String presetType, List<JsonNode> users) {
		logger.info("Applying {} users to {} Preset Security Policy", users.size(), presetType);

		if (users.isEmpty()) {
			logger.warn("No users to apply to {} policy", presetType);
			ObjectNode result = objectMapper.createObjectNode();
			result.put(Constants.STATUS_FIELD, Constants.WARNING_STATUS);
			result.put(Constants.MESSAGE_FIELD, "No users to apply to the policy");
			result.put(DefenderConstants.PRESET_TYPE_FIELD, presetType);
			return CompletableFuture.completedFuture((JsonNode) result);
		}

		// Step 1: Get the EOP Protection Policy Rule
		return DefenderHelpers.getEOPProtectionPolicyRule(powershellClient, presetType)
				.thenCompose(eopRules -> { // Use thenCompose here
					if (CollectionUtils.isEmpty(eopRules)) {
						logger.error("No EOP Protection Policy Rule found for {} Preset Security Policy", presetType);
						ObjectNode result = objectMapper.createObjectNode();
						result.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
						result.put(Constants.MESSAGE_FIELD, "No EOP Protection Policy Rule found for " + presetType + " Preset Security Policy");
						result.put(DefenderConstants.PRESET_TYPE_FIELD, presetType);
						return CompletableFuture.completedFuture((JsonNode) result);
					}

					JsonNode eopRule = eopRules.getFirst();
					String eopRuleId = eopRule.path(Constants.IDENTITY_FIELD).asText();
					String eopRuleName = eopRule.path(DefenderConstants.NAME_FIELD).asText();
					String eopRuleState = eopRule.path(DefenderConstants.STATE_FIELD).asText("Unknown");
					// Check if the rule is already enabled
					boolean isAlreadyEnabled = "Enabled".equalsIgnoreCase(eopRuleState);

					logger.info("Found EOP Rule: {}, Current State: {}", eopRuleName, eopRuleState);

					// If WhatIf is enabled, just log what would be done
					if (whatIf) {
						logger.info("WhatIf: Would enable EOP Rule and set recipients: {}", eopRuleName);
						logger.debug("WhatIf: Users count: {}", users.size());

						return CompletableFuture.completedFuture(
								createWhatIfResult(presetType, eopRuleName, users.size())
						);
					}

					CompletableFuture<JsonNode> eopFuture;

					if (isAlreadyEnabled) {
						// Rule is already enabled, no need to enable it again
						logger.info("EOP Rule is already enabled: {}", eopRuleName);
						eopFuture = CompletableFuture.completedFuture(eopRule);
					} else {
						// Step 2: Enable the EOP Protection Policy Rule
						// Enable-EOPProtectionPolicyRule only takes the Identity parameter
						Map<String, Object> enableParams = new HashMap<>();
						enableParams.put(Constants.IDENTITY_FIELD, eopRuleId);

						logger.info("Enabling EOP Rule: {}", eopRuleId);

						eopFuture = powershellClient.executeCmdletCommand(
								new PowerShellClient.CommandRequest(
										DefenderConstants.ENABLE_EOP_PROTECTION_POLICY_RULE,
										enableParams
								)
						).thenApply(enableResult -> {
							logger.info("Successfully enabled EOP Rule: {}", eopRuleName);
							return enableResult;
						});
					}

					return eopFuture.thenCompose(eopResult -> { //Use thenCompose here
						// Step 3: Enable the ATP Protection Policy Rule (if applicable)
						return enableATPRuleIfPresent(presetType)
								.thenApply(atpResult -> {
									ObjectNode result = objectMapper.createObjectNode();
									result.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
									result.put(Constants.MESSAGE_FIELD, "Successfully applied " + presetType +
											" Preset Security Policy to all users");
									result.put(DefenderConstants.PRESET_TYPE_FIELD, presetType);
									result.put(DefenderConstants.EOP_RULE_NAME_FIELD, eopRuleName);
									result.put(DefenderConstants.POLICY_APPLIED_FIELD, true);

									if (atpResult != null) {
										result.put(DefenderConstants.ATP_RULE_NAME_FIELD, atpResult.path(DefenderConstants.NAME_FIELD).asText(""));
										result.put(DefenderConstants.ATP_POLICY_APPLIED_FIELD,
												atpResult.path(Constants.STATUS_FIELD).asText("").equals(Constants.SUCCESS_STATUS));
									}

									ObjectNode details = objectMapper.createObjectNode();
									details.put(DefenderConstants.USER_COUNT_FIELD, users.size());
									details.put("ruleWasAlreadyEnabled", isAlreadyEnabled);
									result.set(Constants.DETAILS_FIELD, details);
									return (JsonNode) result;
								});
					}).exceptionally(ex -> {
						logger.error("Failed to enable EOP Protection Policy Rule: {}", ex.getMessage());
						ObjectNode result = objectMapper.createObjectNode();
						result.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
						result.put(Constants.MESSAGE_FIELD, "Failed to enable EOP Protection Policy Rule");
						result.put(DefenderConstants.PRESET_TYPE_FIELD, presetType);
						result.put(Constants.ERROR_FIELD, ex.getMessage());
						return (JsonNode) result;
					});
				});
	}


	private JsonNode createWhatIfResult(String presetType, String eopRuleName, int userCount) {
		ObjectNode result = objectMapper.createObjectNode();
		result.put(Constants.STATUS_FIELD, DefenderConstants.WHATIF_STATUS);
		result.put(Constants.MESSAGE_FIELD, "WhatIf simulation for " + presetType + " Preset Security Policy");
		result.put(DefenderConstants.PRESET_TYPE_FIELD, presetType);
		result.put(DefenderConstants.EOP_RULE_NAME_FIELD, eopRuleName);
		result.put("policyApplied", false);
		result.put(DefenderConstants.WHATIF_MODE_FIELD, true);

		ObjectNode details = objectMapper.createObjectNode();
		details.put(DefenderConstants.USER_COUNT_FIELD, userCount);
		result.set(Constants.DETAILS_FIELD, details);

		return result;
	}

	private CompletableFuture<JsonNode> enableATPRuleIfPresent(String presetType) {
		return DefenderHelpers.getATPProtectionPolicyRule(powershellClient, presetType)
				.thenCompose(rules -> {
					if (CollectionUtils.isEmpty(rules)) {
						if (presetType.equals(DefenderConstants.STRICT_PROTECTION)) {
							logger.warn("No ATP Protection Policy Rule found for Strict Preset Security Policy");
						}
						// No rule found, return a completed future with null
						return CompletableFuture.completedFuture(null);
					}

					JsonNode atpRule = rules.getFirst(); // Get the first rule
					String atpRuleName = atpRule.path(DefenderConstants.NAME_FIELD).asText();
					String atpRuleId = atpRule.path(Constants.IDENTITY_FIELD).asText();
					String atpRuleState = atpRule.path(DefenderConstants.STATE_FIELD).asText("Unknown");

					logger.info("Found ATP Rule: {}, Current State: {}", atpRuleName, atpRuleState);

					// Check if the rule is already enabled
					boolean isAlreadyEnabled = "Enabled".equalsIgnoreCase(atpRuleState);

					if (isAlreadyEnabled) {
						// Rule is already enabled, no need to enable it again
						logger.info("ATP Rule is already enabled: {}", atpRuleName);
						ObjectNode result = objectMapper.createObjectNode();
						result.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
						result.put(DefenderConstants.NAME_FIELD, atpRuleName);
						result.put("alreadyEnabled", true);
						return CompletableFuture.completedFuture((JsonNode) result); // Return the result
					}

					// Enable-ATPProtectionPolicyRule only takes the Identity parameter
					Map<String, Object> enableParams = new HashMap<>();
					enableParams.put(Constants.IDENTITY_FIELD, atpRuleId);

					logger.info("Enabling ATP Rule: {}", atpRuleId);

					return powershellClient.executeCmdletCommand(
							new PowerShellClient.CommandRequest(
									DefenderConstants.ENABLE_ATP_PROTECTION_POLICY_RULE,
									enableParams
							)
					).thenApply(enableResult -> {
						logger.info(SUCCESSFULLY_ENABLED_ATP_RULE, atpRuleName);
						ObjectNode result = objectMapper.createObjectNode();
						result.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
						result.put(DefenderConstants.NAME_FIELD, atpRuleName);
						result.put("alreadyEnabled", false);
						return (JsonNode) result; // Return the result
					}).exceptionally(ex -> {
						logger.error("Failed to enable ATP Protection Policy Rule: {}", ex.getMessage());
						ObjectNode result = objectMapper.createObjectNode();
						result.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
						result.put(DefenderConstants.NAME_FIELD, atpRuleName);
						result.put(Constants.ERROR_FIELD, ex.getMessage());
						return result; // Return the error result
					});
				});
	}

	/**
	 * Sanitizes and validates the recipient filter to ensure it's in the correct format.
	 * Handles cases where the filter might be too long or invalid.
	 */
	private String sanitizeRecipientFilter(String originalFilter, List<JsonNode> users) {
		// If we have no users or the filter is empty, use a default filter that matches no users
		if (users.isEmpty() || originalFilter == null || originalFilter.isEmpty()) {
			logger.warn("No valid users to include in filter, creating a filter that will match no users");
			return "(RecipientTypeDetails -eq 'NonExistentType')";
		}

		// Check if the filter is too long (Exchange has limits)
		if (originalFilter.length() > 8000) {
			logger.warn("RecipientFilter exceeds length limit ({}), truncating to first 10 users",
					originalFilter.length());

			// Take only the first 10 users to create a shorter filter
			List<JsonNode> limitedUsers = users.subList(0, Math.min(10, users.size()));
			List<String> userFilters = new ArrayList<>();

			for (JsonNode user : limitedUsers) {
				String recipientType = user.path(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText("");
				String objectId = user.path(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD).asText("");

				if (!recipientType.isEmpty() && !objectId.isEmpty()) {
					String filter = String.format(
							"(RecipientTypeDetails -eq '%s' -and (ExternalDirectoryObjectId -eq '%s'))",
							recipientType, objectId);
					userFilters.add(filter);
				}
			}

			// Create a simplified filter with fewer conditions
			return userFilters.isEmpty() ?
					"(RecipientTypeDetails -eq 'NonExistentType')" :
					"(" + String.join(" -or ", userFilters) + ")";
		}

		// The filter is within limits, return it as is
		return originalFilter;
	}

	private String buildRecipientFilter(List<JsonNode> users) {
		if (users.isEmpty()) {
			return "";
		}

		// Build the filter criteria for each user
		List<String> userFilters = new ArrayList<>();
		for (JsonNode user : users) {
			String recipientType = user.path(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText("");
			String objectId = user.path(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD).asText("");

			if (!recipientType.isEmpty() && !objectId.isEmpty()) {
				String filter = String.format(
						"(RecipientTypeDetails -eq '%s' -and -not(Name -like 'SystemMailbox{*') " +
								"-and -not(Name -like 'CAS_{*') -and (ExternalDirectoryObjectId -eq '%s'))",
						recipientType, objectId);
				userFilters.add(filter);
			}
		}

		// Join all user filters with " -or "
		return "(" + String.join(") -or (", userFilters) + ")";
	}
}