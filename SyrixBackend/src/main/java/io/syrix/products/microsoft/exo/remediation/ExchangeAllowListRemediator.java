package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.12.1v1 (No IP Allow Lists) and MS.EXO.12.2v1 (No Safe Lists)
 * in Exchange Online.

 * This class ensures compliance by:
 * - Clearing all IP allow lists (MS.EXO.12.1v1)
 * - Disabling safe lists (MS.EXO.12.2v1)
 * - Optionally managing IP block lists
 */
//@PolicyRemediator("MS.EXO.12.1v1, MS.EXO.12.2v1")
public class ExchangeAllowListRemediator extends RemediatorBase {

	private static final String DEFAULT_POLICY = "Default";
	private static final String UNKNOWN_ERROR = "Unknown error";

	private final PowerShellClient exchangeClient;
	private final String[] ipBlockList;
	private final boolean clearBlockList;

	/**
	 * Constructs a new ExchangeAllowListRemediator with the specified PowerShell client.
	 * This constructor will clear both allow lists and block lists.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 */
	public ExchangeAllowListRemediator(PowerShellClient exchangeClient) {
		this(exchangeClient, null, true);
	}

	/**
	 * Constructs a new ExchangeAllowListRemediator with the specified PowerShell client
	 * and optional IP block list.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 * @param ipBlockList Optional array of IPs to block. If null or empty, the block list will be cleared.
	 */
	public ExchangeAllowListRemediator(PowerShellClient exchangeClient, String[] ipBlockList) {
		this(exchangeClient, ipBlockList, ipBlockList == null || ipBlockList.length == 0);
	}

	/**
	 * Constructs a new ExchangeAllowListRemediator with the specified PowerShell client,
	 * IP block list, and flag to determine if block list should be cleared.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 * @param ipBlockList Optional array of IPs to block
	 * @param clearBlockList Whether to clear the block list if ipBlockList is null or empty
	 */
	public ExchangeAllowListRemediator(PowerShellClient exchangeClient, String[] ipBlockList, boolean clearBlockList) {
		this.exchangeClient = exchangeClient;
		this.ipBlockList = ipBlockList;
		this.clearBlockList = clearBlockList;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for MS.EXO.12.1v1 (No IP Allow Lists) and MS.EXO.12.2v1 (No Safe Lists)");

		// First verify the current configuration
		return getCurrentConfiguration()
				.thenCompose(this::updateConfiguration)
				.thenCompose(result -> validateConfiguration())
				.exceptionally(ex -> {
					logger.error("Exception during allow list remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Retrieves the current connection filter policy configuration.
	 */
	private CompletableFuture<JsonNode> getCurrentConfiguration() {
		logger.info("Retrieving current connection filter policy configuration");

		Map<String, Object> parameters = Map.of(
				"Identity", DEFAULT_POLICY
		);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"Get-HostedConnectionFilterPolicy",
								parameters
						))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.debug("Current configuration retrieved successfully");
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to retrieve current configuration: {}", error);
						throw new ExoRemediationException("Failed to retrieve current configuration: " + error);
					}
				});
	}

	/**
	 * Updates the connection filter policy to comply with requirements.
	 */
	private CompletableFuture<JsonNode> updateConfiguration(JsonNode currentConfig) {
		logger.info("Updating connection filter policy to clear IP allow lists and disable safe lists");

		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Identity", DEFAULT_POLICY);
		parameters.put("IPAllowList", new String[0]);  // Clear IP allow list (MS.EXO.12.1v1)
		parameters.put("EnableSafeList", false); // Disable safe list (MS.EXO.12.2v1)

		// Handle IP block list based on constructor parameters
		if (ipBlockList != null && ipBlockList.length > 0) {
			logger.info("Adding {} IPs to the block list", ipBlockList.length);
			parameters.put("IPBlockList", ipBlockList);
		} else if (clearBlockList) {
			logger.info("Clearing IP block list");
			parameters.put("IPBlockList", new String[0]);
		} else {
			logger.info("Keeping existing IP block list unchanged");
		}

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"Set-HostedConnectionFilterPolicy",
								parameters
						))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully updated connection filter policy");
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to update connection filter policy: {}", error);
						throw new ExoRemediationException("Failed to update connection filter policy: " + error);
					}
				});
	}

	/**
	 * Validates that the configuration was applied correctly.
	 */
	private CompletableFuture<JsonNode> validateConfiguration() {
		logger.info("Validating updated configuration");

		Map<String, Object> parameters = Map.of(
				"Identity", DEFAULT_POLICY
		);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"Get-HostedConnectionFilterPolicy",
								parameters
						))
				.thenCompose(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						// Verify IPAllowList is empty and EnableSafeList is false
						boolean isCompliant = true;
						StringBuilder nonComplianceReason = new StringBuilder();

						// Check IPAllowList (MS.EXO.12.1v1)
						if (result.has("IPAllowList") && !result.get("IPAllowList").isEmpty() &&
								result.get("IPAllowList").size() > 0) {
							isCompliant = false;
							nonComplianceReason.append("IP allow list is not empty");
						}

						// Check EnableSafeList (MS.EXO.12.2v1)
						if (result.has("EnableSafeList") && result.get("EnableSafeList").asBoolean()) {
							isCompliant = false;
							if (nonComplianceReason.length() > 0) {
								nonComplianceReason.append(" and ");
							}
							nonComplianceReason.append("safe list is still enabled");
						}

						// Optionally check IPBlockList if we're supposed to clear it
						if (clearBlockList && ipBlockList == null || ipBlockList != null && ipBlockList.length == 0) {
							if (result.has("IPBlockList") && !result.get("IPBlockList").isEmpty() &&
									result.get("IPBlockList").size() > 0) {
								isCompliant = false;
								if (nonComplianceReason.length() > 0) {
									nonComplianceReason.append(" and ");
								}
								nonComplianceReason.append("IP block list is not empty");
							}
						}

						if (isCompliant) {
							StringBuilder successMsg = new StringBuilder("IP allow lists cleared and safe lists disabled successfully");
							if (clearBlockList) {
								successMsg.append(", IP block list cleared");
							} else if (ipBlockList != null && ipBlockList.length > 0) {
								successMsg.append(", IP block list updated");
							}
							logger.info(successMsg.toString());
							return IPolicyRemediator.success(getPolicyId(), successMsg.toString());
						} else {
							logger.error("Validation failed: {}", nonComplianceReason);
							return IPolicyRemediator.failed(getPolicyId(), nonComplianceReason.toString());
						}
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to validate configuration: {}", error);
						return IPolicyRemediator.failed(getPolicyId(), error);
					}
				});
	}
}