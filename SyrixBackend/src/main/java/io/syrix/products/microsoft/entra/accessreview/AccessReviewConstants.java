package io.syrix.products.microsoft.entra.accessreview;

/**
 * Constants for Access Review operations
 */
public class AccessReviewConstants {
	// Graph API endpoints for access reviews
	public static final String ACCESS_REVIEWS_ENDPOINT = "/identityGovernance/accessReviews/definitions";
	public static final String ACCESS_REVIEW_INSTANCE_ENDPOINT = "/identityGovernance/accessReviews/definitions/%s/instances/%s";
	public static final String DECISIONS_ENDPOINT = "/identityGovernance/accessReviews/definitions/%s/instances/%s/decisions";
	public static final String BATCH_DECISIONS_ENDPOINT = "/identityGovernance/accessReviews/definitions/%s/instances/%s/batchRecordDecisions";
	public static final String APPLY_DECISIONS_ENDPOINT = "/identityGovernance/accessReviews/definitions/%s/instances/%s/applyDecisions";

	// Access review properties
	public static final String DISPLAY_NAME = "displayName";
	public static final String DESCRIPTION = "description";
	public static final String SCOPE = "scope";
	public static final String QUERY = "query";
	public static final String QUERY_TYPE = "queryType";
	public static final String MICROSOFT_GRAPH = "MicrosoftGraph";
	public static final String INSTANCE_ENUMERATION_SCOPE = "instanceEnumerationScope";
	public static final String SETTINGS = "settings";
	public static final String MAIL_NOTIFICATIONS_ENABLED = "mailNotificationsEnabled";
	public static final String REMINDERS_ENABLED = "remindersEnabled";
	public static final String JUSTIFICATION_REQUIRED = "justificationRequiredOnApproval";
	public static final String AUTO_APPLY_DECISIONS_ENABLED = "autoApplyDecisionsEnabled";
	public static final String ACTIVITY_DURATION = "activityDuration";
	public static final String AUTO_REVIEW = "autoReview";
	public static final String NOT_REVIEWED_RESULT = "notReviewedResult";
	public static final String RECURRENCE = "recurrence";
	public static final String PATTERN = "pattern";
	public static final String TYPE = "type";
	public static final String ABSOLUTE_MONTHLY = "absoluteMonthly";
	public static final String INTERVAL = "interval";
	public static final String DAY_OF_MONTH = "dayOfMonth";
	public static final String RANGE = "range";
	public static final String RANGE_TYPE = "type";
	public static final String NO_END = "noEnd";
	public static final String START_DATE = "startDate";
	public static final String APPLY_ACTIONS = "applyActions";
	public static final String OPERATION = "operation";
	public static final String REMOVE = "remove";
	public static final String REVIEWED_ENTITY = "reviewedEntity";
	public static final String EXTERNAL_USERS = "externalUsers";
	public static final String REVIEWERS = "reviewers";

	// Decision related constants
	public static final String DECISION = "decision";
	public static final String JUSTIFICATION = "justification";
	public static final String PRINCIPAL_ID = "principalId";
	public static final String RESOURCE_ID = "resourceId";
	public static final String ACCESS_REVIEW_ID = "accessReviewId";
	public static final String DECISIONS = "decisions";
	public static final String APPROVE = "Approve";
	public static final String DENY = "Deny";
	public static final String NOT_REVIEWED = "NotReviewed";

	// Monthly guest user review constants
	public static final String MONTHLY_GUEST_REVIEW_NAME = "Monthly Guest User Access Review";
	public static final String MONTHLY_GUEST_REVIEW_DESCRIPTION = "CIS 5.3.2 Compliance - Automated monthly guest access review";
	public static final String DEFAULT_ACTIVITY_DURATION = "P3D"; // 3 days in ISO 8601 duration format
	public static final String ALL_GUEST_USERS_QUERY = "/users?$filter=userType eq 'Guest'";
	public static final String SECURITY_TEAM_QUERY = "/users?$filter=userType eq 'Member' and department eq 'Information Security'";

	// Activity thresholds
	public static final int DEFAULT_INACTIVE_DAYS_THRESHOLD = 90;
}