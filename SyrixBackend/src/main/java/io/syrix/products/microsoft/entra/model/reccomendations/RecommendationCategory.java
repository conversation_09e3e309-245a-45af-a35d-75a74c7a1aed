package io.syrix.products.microsoft.entra.model.reccomendations;

// Enums for various types and statuses
public enum RecommendationCategory {
	identityBestPractice("Identity Best Practice"),
	identitySecureScore("Identity Secure Score"),
	unknownFutureValue("Unknown Future Value");

	private final String label;

	RecommendationCategory(String label) {
		this.label = label;
	}

	@Override
	public String toString() {
		return this.label;
	}
}
