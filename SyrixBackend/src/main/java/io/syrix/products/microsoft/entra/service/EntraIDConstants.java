package io.syrix.products.microsoft.entra.service;

public class EntraIDConstants {
	// Role assignment constants
	public static final String ALL = "All";
	public static final String REQUIRE_MFA = "mfa";
	public static final String ENFORCE = "Enforce";
	public static final String STATE_ENABLED = "enabled";

	// Authentication strength constants
	public static final String[] PHISHING_RESISTANT_MFA = {"windowsHelloForBusiness", "fido2", "x509CertificateMultiFactor"};

	// Password policy constants
	public static final String VALIDITY_PERIOD = "0";
	public static final String NOTIFICATION_DAYS = "0";

	// Guest access levels
	public static final String GUEST_LIMITED_ACCESS = "Guest users have limited access to properties and memberships of directory objects";
	public static final String GUEST_RESTRICTED_ACCESS = "Guest user access is restricted to properties and memberships of their own directory objects";
	public static final String USERS_PERMISSION_TO_READ_OTHER_USERS = "UsersPermissionToReadOtherUsersEnabled";
	public static final String GUEST_USER_ROLE = "GuestUserRole";
	public static final String RESTRICTED_ACCESS = "RestrictedAccess";

	// Common cmdlets
	public static final String SET_MSOL_PASSWORD_POLICY = "Set-MsolPasswordPolicy";
	public static final String NEW_AZURE_AD_CA_POLICY = "New-AzureADMSConditionalAccessPolicy";
	public static final String SET_MSOL_COMPANY_SETTINGS = "Set-MsolCompanySettings";
	public static final String SET_AZURE_AD_SETTINGS = "Set-AzureADSettings";

	public static final String ODATA_TYPE = "@odata.type";
	public static final String DISPLAY_NAME = "displayName";

	public static final String GROUP_MEMBERS_TYPE = "#microsoft.graph.groupMembers";
	public static final String GROUP_EXPAND_KEY = "$expand";
	
	// Graph API Endpoints
	public static final String AUTH_METHODS_POLICY_ENDPOINT = "/policies/authenticationMethodsPolicy";
	public static final String CA_POLICIES_ENDPOINT = "/identity/conditionalAccess/policies";
	public static final String ROLE_ASSIGNMENTS_ENDPOINT = "/roleManagement/directory/roleAssignments";

	public static final String ADMIN = "Admin";
	public static final String RECIPIENT_TYPE = "recipientType";
	public static final String LEVEL = "level";
	public static final String CALLER = "caller";
	public static final String TARGET = "target";
	public static final String ASSIGNMENT = "Assignment";
	public static final String INH_SETTINGS = "inheritableSettings";
	public static final String ENF_SETTINGS = "enforcedSettings";

	public static final String PRINCIPAL_ID = "principalId";
	public static final String ROLE_DEFINITION_ID = "roleDefinitionId";
	public static final String DIRECTORY_SCOPE_ID = "directoryScopeId";
	
	// Conditional Access Policy constants
	public static final String HIGH_RISK_SIGNIN_POLICY = "Block high-risk sign-ins";
	public static final String RISK_LEVEL_HIGH = "high";
	public static final String CLIENT_APP_TYPE_ALL = "all";
	public static final String OPERATOR_OR = "OR";
	public static final String BLOCK_ACTION = "block";
	public static final String SIGNIN_RISK_LEVELS = "signInRiskLevels";
	public static final String USER_RISK_LEVELS = "userRiskLevels";
	public static final String CLIENT_APP_TYPES = "clientAppTypes";
	
	// Guest Domain Restriction constants
	public static final String LEGACY_POLICIES_ENDPOINT = "/legacy/policies";
	public static final String B2B_MANAGEMENT_POLICY_TYPE = "B2BManagementPolicy";
	public static final String DEFINITION = "definition";
	public static final String B2B_MANAGEMENT_POLICY = "B2BManagementPolicy";
	public static final String INVITATIONS_ALLOWED_AND_BLOCKED_DOMAINS_POLICY = "InvitationsAllowedAndBlockedDomainsPolicy";
	public static final String ALLOWED_DOMAINS = "AllowedDomains";
	public static final String BLOCKED_DOMAINS = "BlockedDomains";
	public static final String TYPE_FIELD = "type";
	public static final String VISIBILITY_PROPERTY = "visibility";
	
	// Error messages
	public static final String ERROR_UNKNOWN = "Unknown error";
}
