package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.DEFENDER.2.1v1
 * Enables impersonation protection for sensitive accounts in both Standard and Strict preset policies.
 */
@PolicyRemediator("MS.DEFENDER.2.1v1")
public class DefenderUserImpersonationProtectionRemediator extends RemediatorBase {
	private final PowerShellClient powershellClient;
	private final ObjectMapper objectMapper;
	private final List<String> sensitiveAccounts;

	// Parameter names for AntiPhishPolicy - from official documentation
	private static final String ENABLE_TARGETED_USER_PROTECTION = "EnableTargetedUserProtection";
	private static final String TARGETED_USERS_TO_PROTECT = "TargetedUsersToProtect";
	private static final String ENABLE_ORG_DOMAINS_PROTECTION = "EnableOrganizationDomainsProtection";
	private static final String ENABLE_MAILBOX_INTELLIGENCE = "EnableMailboxIntelligence";
	private static final String ENABLE_MAILBOX_INTELLIGENCE_PROTECTION = "EnableMailboxIntelligenceProtection";

	public DefenderUserImpersonationProtectionRemediator(PowerShellClient powershellClient, List<String> sensitiveAccounts) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
		this.sensitiveAccounts = sensitiveAccounts != null ? sensitiveAccounts : new ArrayList<>();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for MS.DEFENDER.2.1v1 - Impersonation protection for sensitive accounts");

		if (sensitiveAccounts.isEmpty()) {
			logger.warn("No sensitive accounts provided for impersonation protection");
			return CompletableFuture.completedFuture(
					DefenderHelpers.createErrorResponse(getPolicyId(),
							"No sensitive accounts provided for impersonation protection",
							objectMapper));
		}

		// Configure both Standard and Strict policies in sequence
		return applyImpersonationProtection(DefenderConstants.STANDARD_PRESET_POLICY)
				.thenCompose(standardResult -> {
					if (standardResult == null || standardResult.has(Constants.ERROR_FIELD)) {
						String error = standardResult != null ?
								standardResult.get(Constants.ERROR_FIELD).asText() :
								"Failed to configure Standard policy";
						logger.error("Failed to configure Standard policy: {}", error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Now configure the Strict policy
					return applyImpersonationProtection(DefenderConstants.STRICT_PRESET_POLICY)
							.thenCompose(strictResult -> {
								if (strictResult == null || strictResult.has(Constants.ERROR_FIELD)) {
									String error = strictResult != null ?
											strictResult.get(Constants.ERROR_FIELD).asText() :
											"Failed to configure Strict policy";
									logger.error("Failed to configure Strict policy: {}", error);
									return CompletableFuture.completedFuture(
											DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
								}

								// Both policies configured successfully
								logger.info("Successfully configured impersonation protection for both Standard and Strict policies");
								return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(
										getPolicyId(),
										sensitiveAccounts.size(),
										"Impersonation", // Using "Impersonation" as the rule type
										"Both",          // Indicating both Standard and Strict were configured
										objectMapper,
										"User impersonation protection configured successfully"
								));
							});
				})
				.exceptionally(ex -> {
					logger.error("Exception during impersonation protection configuration", ex);
					return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), objectMapper);
				});
	}

	/**
	 * Apply impersonation protection to a specific preset policy (Standard or Strict)
	 */
	private CompletableFuture<JsonNode> applyImpersonationProtection(String presetType) {
		logger.info("Applying impersonation protection for {} policy", presetType);

		// Get all AntiPhish policies directly
		return DefenderHelpers.getAntiPhishPolicies(powershellClient, presetType)
				.thenCompose(policyResult -> {
					if (policyResult == null || policyResult.has(Constants.ERROR_FIELD)) {
						String error = policyResult != null ?
								policyResult.get(Constants.ERROR_FIELD).asText() :
								"Failed to retrieve AntiPhishPolicy for " + presetType;
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Extract the policy name
					String policyName = extractPolicyName(policyResult);
					if (policyName.isEmpty()) {
						String error = "No AntiPhishPolicy found for " + presetType;
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Check if impersonation protection is already enabled
					if (isImpersonationAlreadyEnabled(policyResult)) {
						logger.info("Impersonation protection is already enabled for {} policy", presetType);
						return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(
								getPolicyId(),
								sensitiveAccounts.size(),
								"Impersonation",
								presetType,
								objectMapper,
								"Impersonation protection already enabled"
						));
					}

					// Configure impersonation protection
					return configureImpersonationProtection(policyName);
				});
	}





	/**
	 * Extract policy name from the result
	 */
	private String extractPolicyName(JsonNode policyResult) {
		if (policyResult == null) {
			return "";
		}

		if (policyResult.isArray() && policyResult.size() > 0) {
			return policyResult.get(0).path(Constants.IDENTITY_FIELD).asText("");
		} else {
			return policyResult.path(Constants.IDENTITY_FIELD).asText("");
		}
	}

	/**
	 * Determine if impersonation protection is already enabled in the policy
	 */
	private boolean isImpersonationAlreadyEnabled(JsonNode policySettings) {
		if (policySettings == null) {
			return false;
		}

		// Handle array result
		JsonNode policy = policySettings;
		if (policySettings.isArray() && policySettings.size() > 0) {
			policy = policySettings.get(0);
		}

		// Check if targeted user protection is enabled
		boolean targetedUserProtectionEnabled = policy.path(ENABLE_TARGETED_USER_PROTECTION).asBoolean(false);

		// Check if the settings match what we want to configure
		boolean mailboxIntelligenceEnabled = policy.path(ENABLE_MAILBOX_INTELLIGENCE).asBoolean(false) &&
				policy.path(ENABLE_MAILBOX_INTELLIGENCE_PROTECTION).asBoolean(false);
		boolean orgDomainsProtected = policy.path(ENABLE_ORG_DOMAINS_PROTECTION).asBoolean(false);

		// If user protection is not enabled or other settings aren't as desired, we need to update
		if (!targetedUserProtectionEnabled || !mailboxIntelligenceEnabled || !orgDomainsProtected) {
			return false;
		}

		// Check if all sensitive accounts are already protected
		if (policy.has(TARGETED_USERS_TO_PROTECT) && policy.get(TARGETED_USERS_TO_PROTECT).isArray()) {
			JsonNode existingTargets = policy.get(TARGETED_USERS_TO_PROTECT);
			List<String> existingAccounts = new ArrayList<>();

			// Parse the "DisplayName;EmailAddress" format
			for (JsonNode target : existingTargets) {
				String targetStr = target.asText();
				if (targetStr.contains(";")) {
					String[] parts = targetStr.split(";");
					if (parts.length > 1) {
						existingAccounts.add(parts[1]); // Get the email address part
					}
				}
			}

			// Check if all sensitive accounts are already included
			return new HashSet<>(existingAccounts).containsAll(sensitiveAccounts);
		}

		return false;
	}

	/**
	 * Configure impersonation protection for an anti-phish policy
	 */
	private CompletableFuture<JsonNode> configureImpersonationProtection(String policyName) {
		logger.info("Configuring impersonation protection for policy: {}", policyName);

		// Convert sensitive accounts to the format required by the cmdlet: "DisplayName;EmailAddress"
		List<String> formattedAccounts = new ArrayList<>();
		for (String account : sensitiveAccounts) {
			// Extract username from email for display name if not available
			String displayName = account;
			if (account.contains("@")) {
				displayName = account.substring(0, account.indexOf('@'));
			}
			formattedAccounts.add(displayName + ";" + account);
		}

		Map<String, Object> params = new HashMap<>();
		params.put(Constants.IDENTITY_FIELD, policyName);
		params.put(ENABLE_TARGETED_USER_PROTECTION, true);
		params.put(TARGETED_USERS_TO_PROTECT, formattedAccounts);
		params.put(ENABLE_ORG_DOMAINS_PROTECTION, true);
		params.put(ENABLE_MAILBOX_INTELLIGENCE, true);
		params.put(ENABLE_MAILBOX_INTELLIGENCE_PROTECTION, true);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.SET_ANTIPHISHPOLICY_COMMAND,
						params
				));
	}
}