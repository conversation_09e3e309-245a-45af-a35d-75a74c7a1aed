package io.syrix.products.microsoft.DLP;


/**
 * Overall DLP configuration container.
 * Holds both policy and rule configurations aligned with PowerShell cmdlets.
 */
public class DlpConfiguration {
	private final DlpPolicyConfig policyConfig;
	private final DlpRuleConfig ruleConfig;

	/**
	 * Creates a new DLP configuration.
	 *
	 * @param policyConfig The policy configuration
	 * @param ruleConfig The rule configuration
	 */
	public DlpConfiguration(DlpPolicyConfig policyConfig, DlpRuleConfig ruleConfig) {
		this.policyConfig = policyConfig;
		this.ruleConfig = ruleConfig;
	}

	/**
	 * Gets the policy configuration.
	 *
	 * @return The policy configuration
	 */
	public DlpPolicyConfig getPolicyConfig() {
		return policyConfig;
	}

	/**
	 * Gets the rule configuration.
	 *
	 * @return The rule configuration
	 */
	public DlpRuleConfig getRuleConfig() {
		return ruleConfig;
	}
}