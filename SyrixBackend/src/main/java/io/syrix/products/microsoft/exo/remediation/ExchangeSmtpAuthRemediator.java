package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.EXO.5.1v1")
public class ExchangeSmtpAuthRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	public ExchangeSmtpAuthRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// constructor for Rollback interface
	public ExchangeSmtpAuthRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (SMTP Authentication)", getPolicyId());

		Boolean prevValue = getSmtpClientAuthenticationDisabled();
		if (prevValue == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_DOMAINS));
		}

		if (prevValue) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		boolean newValue = true;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.SMTP_CLIENT_AUTH_DISABLED)
				.prevValue(prevValue)
				.newValue(newValue);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								ExoConstants.SET_TRANSPORT_CONFIG,
								Map.of(ExoConstants.SMTP_CLIENT_AUTH_DISABLED, newValue)
						))
				.thenApply(result -> handleDomainUpdateResult(result, paramChange))
				.exceptionally(ex -> {
					logger.error("Exception during SMTP auth remediation", ex);
					return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (SMTP Authentication)", getPolicyId());

		ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
		if (changeResult.getStatus() != ParameterChangeStatus.SUCCESS) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy " + getPolicyId() + " skipped"));
		}

		Boolean newValue = Boolean.parseBoolean(changeResult.getPrevValue().toString());
		Boolean prevValue = Boolean.parseBoolean(changeResult.getNewValue().toString());

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.SMTP_CLIENT_AUTH_DISABLED)
				.prevValue(prevValue)
				.newValue(newValue);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								ExoConstants.SET_TRANSPORT_CONFIG,
								Map.of(ExoConstants.SMTP_CLIENT_AUTH_DISABLED, newValue)
						))
				.thenApply(result -> handleDomainUpdateResult(result, paramChange))
				.exceptionally(ex -> {
					logger.error("Exception during SMTP auth rollback", ex);
					return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
				});
	}

	private PolicyChangeResult handleDomainUpdateResult(JsonNode result, ParameterChangeResult changeResult) {
		String operation = Boolean.parseBoolean(changeResult.getNewValue().toString()) ? "enabled" : "disabled";

		if (result != null && !result.has(Constants.ERROR_FIELD)) {
			logger.info("Successfully "+operation+" SMTP authentication");
			changeResult.status(ParameterChangeStatus.SUCCESS);
			return IPolicyRemediator.success_(getPolicyId(), "Successfully "+operation+" SMTP authentication", List.of(changeResult));
		} else {
			String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
			logger.error("Failed to "+operation+" SMTP authentication: {}", error);
			changeResult.status(ParameterChangeStatus.FAILED);
			return IPolicyRemediator.failed_(getPolicyId(), "Failed to "+operation+" SMTP authentication: "+ error, List.of(changeResult));
		}
	}

	private Boolean getSmtpClientAuthenticationDisabled() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Remote Domains");
			return null;
		}

		JsonNode domains = configNode.get(ExoConstants.CONFIG_KEY_TRANSPORT);

		if (domains == null || !domains.isArray()) {
			logger.warn("Remote Domains '{}' node not found or not an array", ExoConstants.CONFIG_KEY_REMOTE_DOMAINS);
			return null;
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TransportConfig.class);
		List<TransportConfig> transportConfigs = jsonMapper.convertValue(domains, collectionType);

		return transportConfigs.getFirst().smtpClientAuthenticationDisabled;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	private static class TransportConfig {
		public Boolean smtpClientAuthenticationDisabled;
	}


}