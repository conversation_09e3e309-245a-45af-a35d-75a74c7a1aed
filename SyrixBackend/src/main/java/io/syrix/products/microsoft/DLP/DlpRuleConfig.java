package io.syrix.products.microsoft.DLP;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DLP Rule configuration aligned with New-DlpComplianceRule PowerShell cmdlet.
 */
public class DlpRuleConfig {
	// Basic rule properties (direct PowerShell parameters)
	private final String name;
	private final String policyId;
	private final String accessScope;
	private final Map<String, Object> addRecipients;  // PswsHashtable
	private final String advancedRule;
	private final Map<String, Object> alertProperties;  // PswsHashtable
	private final List<String> anyOfRecipientAddressContainsWords;  // MultiValuedProperty
	private final List<String> anyOfRecipientAddressMatchesPatterns;  // MultiValuedProperty
	private final String applyBrandingTemplate;
	private final Map<String, Object> applyHtmlDisclaimer;  // PswsHashtable
	private final Boolean attachmentIsNotLabeled;
	private final Boolean blockAccess;
	private final String blockAccessScope;
	private final String comment;
	private final List<String> contentCharacterSetContainsWords;  // MultiValuedProperty
	private final List<Map<String, Object>> contentContainsSensitiveInformation;  // PswsHashtable[]
	private final List<String> contentExtensionMatchesWords;  // MultiValuedProperty
	private final List<String> contentFileTypeMatches;  // MultiValuedProperty
	private final Boolean contentIsNotLabeled;
	private final Boolean contentIsShared;
	private final List<String> contentPropertyContainsWords;  // MultiValuedProperty
	private final Boolean disabled;
	private final List<String> documentContainsWords;  // MultiValuedProperty
	private final List<String> documentCreatedBy;  // MultiValuedProperty
	private final List<String> documentCreatedByMemberOf;  // RecipientIdParameter[]
	private final Boolean documentIsPasswordProtected;
	private final Boolean documentIsUnsupported;
	private final List<String> documentMatchesPatterns;  // MultiValuedProperty
	private final List<String> documentNameMatchesPatterns;  // MultiValuedProperty
	private final List<String> documentNameMatchesWords;  // MultiValuedProperty
	private final String documentSizeOver;  // ByteQuantifiedSize
	private final String encryptRMSTemplate;  // RmsTemplateIdParameter
	private final List<Map<String, Object>> endpointDlpBrowserRestrictions;  // PswsHashtable[]
	private final List<Map<String, Object>> endpointDlpRestrictions;  // PswsHashtable[]
	private final Boolean enforcePortalAccess;
	private final Boolean evaluateRulePerComponent;
	private final LocalDateTime expiryDate;
	private final List<String> from;  // RecipientIdParameter[]
	private final List<String> fromAddressContainsWords;  // MultiValuedProperty
	private final List<String> fromAddressMatchesPatterns;  // MultiValuedProperty
	private final List<String> fromMemberOf;  // SmtpAddress[]
	private final String fromScope;
	private final List<String> generateAlert;  // MultiValuedProperty
	private final List<String> generateIncidentReport;  // MultiValuedProperty
	private final Boolean hasSenderOverride;
	private final Map<String, String> headerContainsWords;  // PswsHashtable
	private final Map<String, String> headerMatchesPatterns;  // PswsHashtable
	private final List<String> incidentReportContent;  // ReportContentOption[]
	private final Boolean messageIsNotLabeled;
	private final String messageSizeOver;  // ByteQuantifiedSize
	private final String messageTypeMatches;
	private final List<Map<String, Object>> mipRestrictAccess;  // PswsHashtable[]
	private final Map<String, Object> moderate;  // PswsHashtable
	private final Map<String, String> modifySubject;  // PswsHashtable
	private final String nonBifurcatingAccessScope;
	private final List<String> notifyAllowOverride;  // OverrideOption[]
	private final String notifyEmailCustomSenderDisplayName;
	private final String notifyEmailCustomSubject;
	private final String notifyEmailCustomText;
	private final Boolean notifyEmailExchangeIncludeAttachment;
	private final Map<String, String> notifyEndpointUser;  // PswsHashtable
	private final String notifyOverrideRequirements;
	private final String notifyPolicyTipCustomDialog;
	private final String notifyPolicyTipCustomText;
	private final Map<String, String> notifyPolicyTipCustomTextTranslations;  // MultiValuedProperty
	private final String notifyPolicyTipDisplayOption;
	private final String notifyPolicyTipUrl;
	private final List<String> notifyUser;  // MultiValuedProperty
	private final String notifyUserType;
	private final List<Map<String, Object>> onPremisesScannerDlpRestrictions;  // PswsHashtable[]
	private final String prependSubject;
	private final Integer priority;
	private final Boolean processingLimitExceeded;
	private final Boolean quarantine;
	private final Map<String, List<String>> recipientADAttributeContainsWords;  // PswsHashtable
	private final Map<String, String> recipientADAttributeMatchesPatterns;  // PswsHashtable
	private final List<String> recipientDomainIs;  // MultiValuedProperty
	private final List<String> redirectMessageTo;  // RecipientIdParameter[]
	private final List<String> removeHeader;  // MultiValuedProperty
	private final Boolean removeRMSTemplate;
	private final String reportSeverityLevel;  // RuleSeverity
	private final List<Map<String, Object>> restrictAccess;  // System.Collections.Hashtable[]
	private final Boolean restrictBrowserAccess;
	private final String ruleErrorAction;
	private final Map<String, List<String>> senderADAttributeContainsWords;  // PswsHashtable
	private final Map<String, String> senderADAttributeMatchesPatterns;  // PswsHashtable
	private final String senderAddressLocation;
	private final List<String> senderDomainIs;  // MultiValuedProperty
	private final List<String> senderIPRanges;  // MultiValuedProperty
	private final List<String> sentTo;  // MultiValuedProperty
	private final List<String> sentToMemberOf;  // RecipientIdParameter[]
	private final Map<String, String> setHeader;  // PswsHashtable
	private final List<String> sharedByIRMUserRisk;  // MultiValuedProperty
	private final Boolean stopPolicyProcessing;
	private final List<String> subjectContainsWords;  // MultiValuedProperty
	private final List<String> subjectMatchesPatterns;  // MultiValuedProperty
	private final List<String> subjectOrBodyContainsWords;  // MultiValuedProperty
	private final List<String> subjectOrBodyMatchesPatterns;  // MultiValuedProperty
	private final List<Map<String, String>> thirdPartyAppDlpRestrictions;  // PswsHashtable[]
	private final String triggerPowerAutomateFlow;
	private final List<String> unscannableDocumentExtensionIs;  // MultiValuedProperty
	private final String withImportance;

	// Exception parameters omitted for brevity but would follow same pattern with ExceptIf prefix

	// Builder pattern for the comprehensive configuration
	private DlpRuleConfig(Builder builder) {
		this.name = builder.name;
		this.policyId = builder.policyId;
		this.accessScope = builder.accessScope;
		this.addRecipients = builder.addRecipients;
		this.advancedRule = builder.advancedRule;
		this.alertProperties = builder.alertProperties;
		this.anyOfRecipientAddressContainsWords = builder.anyOfRecipientAddressContainsWords;
		this.anyOfRecipientAddressMatchesPatterns = builder.anyOfRecipientAddressMatchesPatterns;
		this.applyBrandingTemplate = builder.applyBrandingTemplate;
		this.applyHtmlDisclaimer = builder.applyHtmlDisclaimer;
		this.attachmentIsNotLabeled = builder.attachmentIsNotLabeled;
		this.blockAccess = builder.blockAccess;
		this.blockAccessScope = builder.blockAccessScope;
		this.comment = builder.comment;
		this.contentCharacterSetContainsWords = builder.contentCharacterSetContainsWords;
		this.contentContainsSensitiveInformation = builder.contentContainsSensitiveInformation;
		this.contentExtensionMatchesWords = builder.contentExtensionMatchesWords;
		this.contentFileTypeMatches = builder.contentFileTypeMatches;
		this.contentIsNotLabeled = builder.contentIsNotLabeled;
		this.contentIsShared = builder.contentIsShared;
		this.contentPropertyContainsWords = builder.contentPropertyContainsWords;
		this.disabled = builder.disabled;
		this.documentContainsWords = builder.documentContainsWords;
		this.documentCreatedBy = builder.documentCreatedBy;
		this.documentCreatedByMemberOf = builder.documentCreatedByMemberOf;
		this.documentIsPasswordProtected = builder.documentIsPasswordProtected;
		this.documentIsUnsupported = builder.documentIsUnsupported;
		this.documentMatchesPatterns = builder.documentMatchesPatterns;
		this.documentNameMatchesPatterns = builder.documentNameMatchesPatterns;
		this.documentNameMatchesWords = builder.documentNameMatchesWords;
		this.documentSizeOver = builder.documentSizeOver;
		this.encryptRMSTemplate = builder.encryptRMSTemplate;
		this.endpointDlpBrowserRestrictions = builder.endpointDlpBrowserRestrictions;
		this.endpointDlpRestrictions = builder.endpointDlpRestrictions;
		this.enforcePortalAccess = builder.enforcePortalAccess;
		this.evaluateRulePerComponent = builder.evaluateRulePerComponent;
		this.expiryDate = builder.expiryDate;
		this.from = builder.from;
		this.fromAddressContainsWords = builder.fromAddressContainsWords;
		this.fromAddressMatchesPatterns = builder.fromAddressMatchesPatterns;
		this.fromMemberOf = builder.fromMemberOf;
		this.fromScope = builder.fromScope;
		this.generateAlert = builder.generateAlert;
		this.generateIncidentReport = builder.generateIncidentReport;
		this.hasSenderOverride = builder.hasSenderOverride;
		this.headerContainsWords = builder.headerContainsWords;
		this.headerMatchesPatterns = builder.headerMatchesPatterns;
		this.incidentReportContent = builder.incidentReportContent;
		this.messageIsNotLabeled = builder.messageIsNotLabeled;
		this.messageSizeOver = builder.messageSizeOver;
		this.messageTypeMatches = builder.messageTypeMatches;
		this.mipRestrictAccess = builder.mipRestrictAccess;
		this.moderate = builder.moderate;
		this.modifySubject = builder.modifySubject;
		this.nonBifurcatingAccessScope = builder.nonBifurcatingAccessScope;
		this.notifyAllowOverride = builder.notifyAllowOverride;
		this.notifyEmailCustomSenderDisplayName = builder.notifyEmailCustomSenderDisplayName;
		this.notifyEmailCustomSubject = builder.notifyEmailCustomSubject;
		this.notifyEmailCustomText = builder.notifyEmailCustomText;
		this.notifyEmailExchangeIncludeAttachment = builder.notifyEmailExchangeIncludeAttachment;
		this.notifyEndpointUser = builder.notifyEndpointUser;
		this.notifyOverrideRequirements = builder.notifyOverrideRequirements;
		this.notifyPolicyTipCustomDialog = builder.notifyPolicyTipCustomDialog;
		this.notifyPolicyTipCustomText = builder.notifyPolicyTipCustomText;
		this.notifyPolicyTipCustomTextTranslations = builder.notifyPolicyTipCustomTextTranslations;
		this.notifyPolicyTipDisplayOption = builder.notifyPolicyTipDisplayOption;
		this.notifyPolicyTipUrl = builder.notifyPolicyTipUrl;
		this.notifyUser = builder.notifyUser;
		this.notifyUserType = builder.notifyUserType;
		this.onPremisesScannerDlpRestrictions = builder.onPremisesScannerDlpRestrictions;
		this.prependSubject = builder.prependSubject;
		this.priority = builder.priority;
		this.processingLimitExceeded = builder.processingLimitExceeded;
		this.quarantine = builder.quarantine;
		this.recipientADAttributeContainsWords = builder.recipientADAttributeContainsWords;
		this.recipientADAttributeMatchesPatterns = builder.recipientADAttributeMatchesPatterns;
		this.recipientDomainIs = builder.recipientDomainIs;
		this.redirectMessageTo = builder.redirectMessageTo;
		this.removeHeader = builder.removeHeader;
		this.removeRMSTemplate = builder.removeRMSTemplate;
		this.reportSeverityLevel = builder.reportSeverityLevel;
		this.restrictAccess = builder.restrictAccess;
		this.restrictBrowserAccess = builder.restrictBrowserAccess;
		this.ruleErrorAction = builder.ruleErrorAction;
		this.senderADAttributeContainsWords = builder.senderADAttributeContainsWords;
		this.senderADAttributeMatchesPatterns = builder.senderADAttributeMatchesPatterns;
		this.senderAddressLocation = builder.senderAddressLocation;
		this.senderDomainIs = builder.senderDomainIs;
		this.senderIPRanges = builder.senderIPRanges;
		this.sentTo = builder.sentTo;
		this.sentToMemberOf = builder.sentToMemberOf;
		this.setHeader = builder.setHeader;
		this.sharedByIRMUserRisk = builder.sharedByIRMUserRisk;
		this.stopPolicyProcessing = builder.stopPolicyProcessing;
		this.subjectContainsWords = builder.subjectContainsWords;
		this.subjectMatchesPatterns = builder.subjectMatchesPatterns;
		this.subjectOrBodyContainsWords = builder.subjectOrBodyContainsWords;
		this.subjectOrBodyMatchesPatterns = builder.subjectOrBodyMatchesPatterns;
		this.thirdPartyAppDlpRestrictions = builder.thirdPartyAppDlpRestrictions;
		this.triggerPowerAutomateFlow = builder.triggerPowerAutomateFlow;
		this.unscannableDocumentExtensionIs = builder.unscannableDocumentExtensionIs;
		this.withImportance = builder.withImportance;
	}

	/**
	 * Basic constructor for simple rule configuration.
	 */
	public DlpRuleConfig(String name, List<Map<String, Object>> contentContainsSensitiveInformation,
						 Boolean blockAccess, List<String> notifyUser) {
		this.name = name;
		this.contentContainsSensitiveInformation = contentContainsSensitiveInformation;
		this.blockAccess = blockAccess;
		this.notifyUser = notifyUser;

		// Defaults for other properties
		this.policyId = null;
		this.accessScope = null;
		this.addRecipients = null;
		this.advancedRule = null;
		this.alertProperties = null;
		this.anyOfRecipientAddressContainsWords = null;
		this.anyOfRecipientAddressMatchesPatterns = null;
		this.applyBrandingTemplate = null;
		this.applyHtmlDisclaimer = null;
		this.attachmentIsNotLabeled = null;
		this.blockAccessScope = null;
		this.comment = null;
		this.contentCharacterSetContainsWords = null;
		this.contentExtensionMatchesWords = null;
		this.contentFileTypeMatches = null;
		this.contentIsNotLabeled = null;
		this.contentIsShared = null;
		this.contentPropertyContainsWords = null;
		this.disabled = false;
		this.documentContainsWords = null;
		this.documentCreatedBy = null;
		this.documentCreatedByMemberOf = null;
		this.documentIsPasswordProtected = null;
		this.documentIsUnsupported = null;
		this.documentMatchesPatterns = null;
		this.documentNameMatchesPatterns = null;
		this.documentNameMatchesWords = null;
		this.documentSizeOver = null;
		this.encryptRMSTemplate = null;
		this.endpointDlpBrowserRestrictions = null;
		this.endpointDlpRestrictions = null;
		this.enforcePortalAccess = null;
		this.evaluateRulePerComponent = null;
		this.expiryDate = null;
		this.from = null;
		this.fromAddressContainsWords = null;
		this.fromAddressMatchesPatterns = null;
		this.fromMemberOf = null;
		this.fromScope = null;
		this.generateAlert = null;
		this.generateIncidentReport = null;
		this.hasSenderOverride = null;
		this.headerContainsWords = null;
		this.headerMatchesPatterns = null;
		this.incidentReportContent = null;
		this.messageIsNotLabeled = null;
		this.messageSizeOver = null;
		this.messageTypeMatches = null;
		this.mipRestrictAccess = null;
		this.moderate = null;
		this.modifySubject = null;
		this.nonBifurcatingAccessScope = null;
		this.notifyAllowOverride = null;
		this.notifyEmailCustomSenderDisplayName = null;
		this.notifyEmailCustomSubject = null;
		this.notifyEmailCustomText = null;
		this.notifyEmailExchangeIncludeAttachment = null;
		this.notifyEndpointUser = null;
		this.notifyOverrideRequirements = null;
		this.notifyPolicyTipCustomDialog = null;
		this.notifyPolicyTipCustomText = null;
		this.notifyPolicyTipCustomTextTranslations = null;
		this.notifyPolicyTipDisplayOption = null;
		this.notifyPolicyTipUrl = null;
		this.notifyUserType = null;
		this.onPremisesScannerDlpRestrictions = null;
		this.prependSubject = null;
		this.priority = null;
		this.processingLimitExceeded = null;
		this.quarantine = null;
		this.recipientADAttributeContainsWords = null;
		this.recipientADAttributeMatchesPatterns = null;
		this.recipientDomainIs = null;
		this.redirectMessageTo = null;
		this.removeHeader = null;
		this.removeRMSTemplate = null;
		this.reportSeverityLevel = "Low";
		this.restrictAccess = null;
		this.restrictBrowserAccess = null;
		this.ruleErrorAction = null;
		this.senderADAttributeContainsWords = null;
		this.senderADAttributeMatchesPatterns = null;
		this.senderAddressLocation = null;
		this.senderDomainIs = null;
		this.senderIPRanges = null;
		this.sentTo = null;
		this.sentToMemberOf = null;
		this.setHeader = null;
		this.sharedByIRMUserRisk = null;
		this.stopPolicyProcessing = false;
		this.subjectContainsWords = null;
		this.subjectMatchesPatterns = null;
		this.subjectOrBodyContainsWords = null;
		this.subjectOrBodyMatchesPatterns = null;
		this.thirdPartyAppDlpRestrictions = null;
		this.triggerPowerAutomateFlow = null;
		this.unscannableDocumentExtensionIs = null;
		this.withImportance = null;
	}

	// Getters for all properties
	public String getName() {
		return name;
	}

	public Boolean getBlockAccess() {
		return blockAccess;
	}

	public List<String> getNotifyUser() {
		return notifyUser;
	}

	public List<Map<String, Object>> getContentContainsSensitiveInformation() {
		return contentContainsSensitiveInformation;
	}

	public String getPolicyId() {
		return policyId;
	}

	public Boolean getDisabled() {
		return disabled;
	}

	public Integer getPriority() {
		return priority;
	}

	public String getComment() {
		return comment;
	}

	public String getReportSeverityLevel() {
		return reportSeverityLevel;
	}

	public Boolean getStopPolicyProcessing() {
		return stopPolicyProcessing;
	}

	// Add these methods to the DlpRuleConfig class

	public String getAdvancedRule() {
		return advancedRule;
	}

	public List<String> getNotifyAllowOverride() {
		return notifyAllowOverride;
	}

	public String getNotifyEmailCustomSubject() {
		return notifyEmailCustomSubject;
	}

	public String getNotifyEmailCustomText() {
		return notifyEmailCustomText;
	}

	public String getNotifyPolicyTipCustomText() {
		return notifyPolicyTipCustomText;
	}

	public String getNotifyPolicyTipDisplayOption() {
		return notifyPolicyTipDisplayOption;
	}

	public String getAccessScope() {
		return accessScope;
	}

	public Boolean getDocumentIsPasswordProtected() {
		return documentIsPasswordProtected;
	}

	public Boolean getDocumentIsUnsupported() {
		return documentIsUnsupported;
	}

	public String getFromScope() {
		return fromScope;
	}

	// Additional getters that might be needed based on other properties used in the class
	public Map<String, Object> getAddRecipients() {
		return addRecipients;
	}

	public Map<String, Object> getAlertProperties() {
		return alertProperties;
	}

	public List<String> getAnyOfRecipientAddressContainsWords() {
		return anyOfRecipientAddressContainsWords;
	}

	public List<String> getAnyOfRecipientAddressMatchesPatterns() {
		return anyOfRecipientAddressMatchesPatterns;
	}

	public String getApplyBrandingTemplate() {
		return applyBrandingTemplate;
	}

	public Map<String, Object> getApplyHtmlDisclaimer() {
		return applyHtmlDisclaimer;
	}

	public Boolean getAttachmentIsNotLabeled() {
		return attachmentIsNotLabeled;
	}

	public String getBlockAccessScope() {
		return blockAccessScope;
	}

	public List<String> getContentCharacterSetContainsWords() {
		return contentCharacterSetContainsWords;
	}

	public List<String> getContentExtensionMatchesWords() {
		return contentExtensionMatchesWords;
	}

	public List<String> getContentFileTypeMatches() {
		return contentFileTypeMatches;
	}

	public Boolean getContentIsNotLabeled() {
		return contentIsNotLabeled;
	}

	public Boolean getContentIsShared() {
		return contentIsShared;
	}

	public List<String> getContentPropertyContainsWords() {
		return contentPropertyContainsWords;
	}

	public List<String> getDocumentContainsWords() {
		return documentContainsWords;
	}

	public List<String> getDocumentCreatedBy() {
		return documentCreatedBy;
	}

	public List<String> getDocumentCreatedByMemberOf() {
		return documentCreatedByMemberOf;
	}

	public List<String> getDocumentMatchesPatterns() {
		return documentMatchesPatterns;
	}

	public List<String> getDocumentNameMatchesPatterns() {
		return documentNameMatchesPatterns;
	}

	public List<String> getDocumentNameMatchesWords() {
		return documentNameMatchesWords;
	}

	public String getDocumentSizeOver() {
		return documentSizeOver;
	}

	public String getEncryptRMSTemplate() {
		return encryptRMSTemplate;
	}

	public List<Map<String, Object>> getEndpointDlpBrowserRestrictions() {
		return endpointDlpBrowserRestrictions;
	}

	public List<Map<String, Object>> getEndpointDlpRestrictions() {
		return endpointDlpRestrictions;
	}

	public Boolean getEnforcePortalAccess() {
		return enforcePortalAccess;
	}

	public Boolean getEvaluateRulePerComponent() {
		return evaluateRulePerComponent;
	}

	public LocalDateTime getExpiryDate() {
		return expiryDate;
	}

	public List<String> getFrom() {
		return from;
	}

	public List<String> getFromAddressContainsWords() {
		return fromAddressContainsWords;
	}

	public List<String> getFromAddressMatchesPatterns() {
		return fromAddressMatchesPatterns;
	}

	public List<String> getFromMemberOf() {
		return fromMemberOf;
	}

	public List<String> getGenerateAlert() {
		return generateAlert;
	}

	public List<String> getGenerateIncidentReport() {
		return generateIncidentReport;
	}

	public Boolean getHasSenderOverride() {
		return hasSenderOverride;
	}

	public Map<String, String> getHeaderContainsWords() {
		return headerContainsWords;
	}

	public Map<String, String> getHeaderMatchesPatterns() {
		return headerMatchesPatterns;
	}

	public List<String> getIncidentReportContent() {
		return incidentReportContent;
	}

	public Boolean getMessageIsNotLabeled() {
		return messageIsNotLabeled;
	}

	public String getMessageSizeOver() {
		return messageSizeOver;
	}

	public String getMessageTypeMatches() {
		return messageTypeMatches;
	}

	public List<Map<String, Object>> getMipRestrictAccess() {
		return mipRestrictAccess;
	}

	public Map<String, Object> getModerate() {
		return moderate;
	}

	public Map<String, String> getModifySubject() {
		return modifySubject;
	}

	public String getNonBifurcatingAccessScope() {
		return nonBifurcatingAccessScope;
	}

	public String getNotifyEmailCustomSenderDisplayName() {
		return notifyEmailCustomSenderDisplayName;
	}

	public Boolean getNotifyEmailExchangeIncludeAttachment() {
		return notifyEmailExchangeIncludeAttachment;
	}

	public Map<String, String> getNotifyEndpointUser() {
		return notifyEndpointUser;
	}

	public String getNotifyOverrideRequirements() {
		return notifyOverrideRequirements;
	}

	public String getNotifyPolicyTipCustomDialog() {
		return notifyPolicyTipCustomDialog;
	}

	public Map<String, String> getNotifyPolicyTipCustomTextTranslations() {
		return notifyPolicyTipCustomTextTranslations;
	}

	public String getNotifyPolicyTipUrl() {
		return notifyPolicyTipUrl;
	}

	public String getNotifyUserType() {
		return notifyUserType;
	}

	public List<Map<String, Object>> getOnPremisesScannerDlpRestrictions() {
		return onPremisesScannerDlpRestrictions;
	}

	public String getPrependSubject() {
		return prependSubject;
	}

	public Boolean getProcessingLimitExceeded() {
		return processingLimitExceeded;
	}

	public Boolean getQuarantine() {
		return quarantine;
	}

	public Map<String, List<String>> getRecipientADAttributeContainsWords() {
		return recipientADAttributeContainsWords;
	}

	public Map<String, String> getRecipientADAttributeMatchesPatterns() {
		return recipientADAttributeMatchesPatterns;
	}

	public List<String> getRecipientDomainIs() {
		return recipientDomainIs;
	}

	public List<String> getRedirectMessageTo() {
		return redirectMessageTo;
	}

	public List<String> getRemoveHeader() {
		return removeHeader;
	}

	public Boolean getRemoveRMSTemplate() {
		return removeRMSTemplate;
	}

	public List<Map<String, Object>> getRestrictAccess() {
		return restrictAccess;
	}

	public Boolean getRestrictBrowserAccess() {
		return restrictBrowserAccess;
	}

	public String getRuleErrorAction() {
		return ruleErrorAction;
	}

	public Map<String, List<String>> getSenderADAttributeContainsWords() {
		return senderADAttributeContainsWords;
	}

	public Map<String, String> getSenderADAttributeMatchesPatterns() {
		return senderADAttributeMatchesPatterns;
	}

	public String getSenderAddressLocation() {
		return senderAddressLocation;
	}

	public List<String> getSenderDomainIs() {
		return senderDomainIs;
	}

	public List<String> getSenderIPRanges() {
		return senderIPRanges;
	}

	public List<String> getSentTo() {
		return sentTo;
	}

	public List<String> getSentToMemberOf() {
		return sentToMemberOf;
	}

	public Map<String, String> getSetHeader() {
		return setHeader;
	}

	public List<String> getSharedByIRMUserRisk() {
		return sharedByIRMUserRisk;
	}

	public List<String> getSubjectContainsWords() {
		return subjectContainsWords;
	}

	public List<String> getSubjectMatchesPatterns() {
		return subjectMatchesPatterns;
	}

	public List<String> getSubjectOrBodyContainsWords() {
		return subjectOrBodyContainsWords;
	}

	public List<String> getSubjectOrBodyMatchesPatterns() {
		return subjectOrBodyMatchesPatterns;
	}

	public List<Map<String, String>> getThirdPartyAppDlpRestrictions() {
		return thirdPartyAppDlpRestrictions;
	}

	public String getTriggerPowerAutomateFlow() {
		return triggerPowerAutomateFlow;
	}

	public List<String> getUnscannableDocumentExtensionIs() {
		return unscannableDocumentExtensionIs;
	}

	public String getWithImportance() {
		return withImportance;
	}
	// Additional getters for all other properties would go here

	/**
	 * Helper method to create a ContentContainsSensitiveInformation parameter from SensitiveInfoTypeConfig objects.
	 *
	 * @param sensitiveInfoTypes List of sensitive information type configurations
	 * @return A list of maps formatted for the ContentContainsSensitiveInformation parameter
	 */
	public static List<Map<String, Object>> createSensitiveInfoTypeParameter(List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
		if (sensitiveInfoTypes == null || sensitiveInfoTypes.isEmpty()) {
			return Collections.emptyList();
		}

		return hasGroupConfiguration(sensitiveInfoTypes) ?
				createComplexSensitiveInfo(sensitiveInfoTypes) :
				createSimpleSensitiveInfoList(sensitiveInfoTypes);
	}

	private static boolean hasGroupConfiguration(List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
		return sensitiveInfoTypes.stream()
				.anyMatch(type -> type.getGroupName() != null);
	}

	private static List<Map<String, Object>> createSimpleSensitiveInfoList(List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
		List<Map<String, Object>> result = new ArrayList<>();
		sensitiveInfoTypes.forEach(infoType -> result.add(createTypeMapFromConfig(infoType)));
		return result;
	}

	private static Map<String, Object> createTypeMapFromConfig(SensitiveInfoTypeConfig infoType) {
		Map<String, Object> typeMap = new HashMap<>();
		typeMap.put("Name", infoType.getName());

		addConfigValueIfPresent(typeMap, "minCount", infoType.getMinCount());
		addConfigValueIfPresent(typeMap, "maxCount", infoType.getMaxCount());
		addConfigValueIfPresent(typeMap, "minConfidence", infoType.getMinConfidence());
		addConfigValueIfPresent(typeMap, "maxConfidence", infoType.getMaxConfidence());

		return typeMap;
	}

	private static void addConfigValueIfPresent(Map<String, Object> map, String key, Integer value) {
		if (value != null) {
			map.put(key, value.toString());
		}
	}

	/**
	 * Creates a complex format for ContentContainsSensitiveInformation with groups and operators.
	 *
	 * @param sensitiveInfoTypes List of sensitive information types
	 * @return A list with a single map containing the complex structure
	 */
	private static List<Map<String, Object>> createComplexSensitiveInfo(
			List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
		Map<String, List<SensitiveInfoTypeConfig>> typesByGroup = groupSensitiveInfoTypes(sensitiveInfoTypes);
		Map<String, Object> complexStructure = createBaseStructure();
		List<Map<String, Object>> groups = createGroups(typesByGroup);

		complexStructure.put("groups", groups);

		return wrapInList(complexStructure);
	}

	private static Map<String, List<SensitiveInfoTypeConfig>> groupSensitiveInfoTypes(
			List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
		Map<String, List<SensitiveInfoTypeConfig>> typesByGroup = new HashMap<>();

		for (SensitiveInfoTypeConfig infoType : sensitiveInfoTypes) {
			String groupName = infoType.getGroupName() != null ? infoType.getGroupName() : "Default";
			typesByGroup.computeIfAbsent(groupName, k -> new ArrayList<>()).add(infoType);
		}

		return typesByGroup;
	}

	private static Map<String, Object> createBaseStructure() {
		Map<String, Object> complexStructure = new HashMap<>();
		complexStructure.put("operator", "And");
		return complexStructure;
	}

	private static List<Map<String, Object>> createGroups(Map<String, List<SensitiveInfoTypeConfig>> typesByGroup) {
		List<Map<String, Object>> groups = new ArrayList<>();

		for (Map.Entry<String, List<SensitiveInfoTypeConfig>> entry : typesByGroup.entrySet()) {
			Map<String, Object> group = createGroup(entry.getKey(), entry.getValue());
			groups.add(group);
		}

		return groups;
	}

	private static Map<String, Object> createGroup(String groupName, List<SensitiveInfoTypeConfig> groupTypes) {
		Map<String, Object> group = new HashMap<>();
		String operator = groupTypes.getFirst().getGroupOperator();

		group.put("operator", operator != null ? operator : "Or");
		group.put("name", groupName);
		group.put("sensitivetypes", createSensitiveTypes(groupTypes));

		return group;
	}

	private static List<Map<String, Object>> createSensitiveTypes(List<SensitiveInfoTypeConfig> infoTypes) {
		List<Map<String, Object>> sensitivetypes = new ArrayList<>();

		for (SensitiveInfoTypeConfig infoType : infoTypes) {
			sensitivetypes.add(createTypeInfo(infoType));
		}

		return sensitivetypes;
	}

	private static Map<String, Object> createTypeInfo(SensitiveInfoTypeConfig infoType) {
		Map<String, Object> typeInfo = new HashMap<>();
		typeInfo.put("name", infoType.getName());

		addIfNotNull(typeInfo, "minconfidence", infoType.getMinConfidence());
		addIfNotNull(typeInfo, "maxconfidence", infoType.getMaxConfidence());
		addIfNotNull(typeInfo, "mincount", infoType.getMinCount());
		addIfNotNull(typeInfo, "maxcount", infoType.getMaxCount());

		return typeInfo;
	}

	private static void addIfNotNull(Map<String, Object> map, String key, Object value) {
		if (value != null) {
			map.put(key, value);
		}
	}

	private static List<Map<String, Object>> wrapInList(Map<String, Object> structure) {
		List<Map<String, Object>> result = new ArrayList<>();
		result.add(structure);
		return result;
	}

	/**
	 * Builder for DlpRuleConfig aligned with PowerShell parameters.
	 */
	public static class Builder {
		private final String name; // Required
		private String policyId;
		private String accessScope;
		private Map<String, Object> addRecipients;
		private String advancedRule;
		private Map<String, Object> alertProperties;
		private List<String> anyOfRecipientAddressContainsWords;
		private List<String> anyOfRecipientAddressMatchesPatterns;
		private String applyBrandingTemplate;
		private Map<String, Object> applyHtmlDisclaimer;
		private Boolean attachmentIsNotLabeled;
		private Boolean blockAccess;
		private String blockAccessScope;
		private String comment;
		private List<String> contentCharacterSetContainsWords;
		private List<Map<String, Object>> contentContainsSensitiveInformation;
		private List<String> contentExtensionMatchesWords;
		private List<String> contentFileTypeMatches;
		private Boolean contentIsNotLabeled;
		private Boolean contentIsShared;
		private List<String> contentPropertyContainsWords;
		private Boolean disabled;
		private List<String> documentContainsWords;
		private List<String> documentCreatedBy;
		private List<String> documentCreatedByMemberOf;
		private Boolean documentIsPasswordProtected;
		private Boolean documentIsUnsupported;
		private List<String> documentMatchesPatterns;
		private List<String> documentNameMatchesPatterns;
		private List<String> documentNameMatchesWords;
		private String documentSizeOver;
		private String encryptRMSTemplate;
		private List<Map<String, Object>> endpointDlpBrowserRestrictions;
		private List<Map<String, Object>> endpointDlpRestrictions;
		private Boolean enforcePortalAccess;
		private Boolean evaluateRulePerComponent;
		private LocalDateTime expiryDate;
		private List<String> from;
		private List<String> fromAddressContainsWords;
		private List<String> fromAddressMatchesPatterns;
		private List<String> fromMemberOf;
		private String fromScope;
		private List<String> generateAlert;
		private List<String> generateIncidentReport;
		private Boolean hasSenderOverride;
		private Map<String, String> headerContainsWords;
		private Map<String, String> headerMatchesPatterns;
		private List<String> incidentReportContent;
		private Boolean messageIsNotLabeled;
		private String messageSizeOver;
		private String messageTypeMatches;
		private List<Map<String, Object>> mipRestrictAccess;
		private Map<String, Object> moderate;
		private Map<String, String> modifySubject;
		private String nonBifurcatingAccessScope;
		private List<String> notifyAllowOverride;
		private String notifyEmailCustomSenderDisplayName;
		private String notifyEmailCustomSubject;
		private String notifyEmailCustomText;
		private Boolean notifyEmailExchangeIncludeAttachment;
		private Map<String, String> notifyEndpointUser;
		private String notifyOverrideRequirements;
		private String notifyPolicyTipCustomDialog;
		private String notifyPolicyTipCustomText;
		private Map<String, String> notifyPolicyTipCustomTextTranslations;
		private String notifyPolicyTipDisplayOption;
		private String notifyPolicyTipUrl;
		private List<String> notifyUser;
		private String notifyUserType;
		private List<Map<String, Object>> onPremisesScannerDlpRestrictions;
		private String prependSubject;
		private Integer priority;
		private Boolean processingLimitExceeded;
		private Boolean quarantine;
		private Map<String, List<String>> recipientADAttributeContainsWords;
		private Map<String, String> recipientADAttributeMatchesPatterns;
		private List<String> recipientDomainIs;
		private List<String> redirectMessageTo;
		private List<String> removeHeader;
		private Boolean removeRMSTemplate;
		private String reportSeverityLevel;
		private List<Map<String, Object>> restrictAccess;
		private Boolean restrictBrowserAccess;
		private String ruleErrorAction;
		private Map<String, List<String>> senderADAttributeContainsWords;
		private Map<String, String> senderADAttributeMatchesPatterns;
		private String senderAddressLocation;
		private List<String> senderDomainIs;
		private List<String> senderIPRanges;
		private List<String> sentTo;
		private List<String> sentToMemberOf;
		private Map<String, String> setHeader;
		private List<String> sharedByIRMUserRisk;
		private Boolean stopPolicyProcessing;
		private List<String> subjectContainsWords;
		private List<String> subjectMatchesPatterns;
		private List<String> subjectOrBodyContainsWords;
		private List<String> subjectOrBodyMatchesPatterns;
		private List<Map<String, String>> thirdPartyAppDlpRestrictions;
		private String triggerPowerAutomateFlow;
		private List<String> unscannableDocumentExtensionIs;
		private String withImportance;

		public Builder(String name) {
			this.name = name;
		}

		public Builder policyId(String policyId) {
			this.policyId = policyId;
			return this;
		}

		public Builder accessScope(String accessScope) {
			this.accessScope = accessScope;
			return this;
		}

		public Builder addRecipients(Map<String, Object> addRecipients) {
			this.addRecipients = addRecipients;
			return this;
		}

		public Builder advancedRule(String advancedRule) {
			this.advancedRule = advancedRule;
			return this;
		}

		public Builder alertProperties(Map<String, Object> alertProperties) {
			this.alertProperties = alertProperties;
			return this;
		}

		public Builder anyOfRecipientAddressContainsWords(List<String> anyOfRecipientAddressContainsWords) {
			this.anyOfRecipientAddressContainsWords = anyOfRecipientAddressContainsWords;
			return this;
		}

		public Builder anyOfRecipientAddressMatchesPatterns(List<String> anyOfRecipientAddressMatchesPatterns) {
			this.anyOfRecipientAddressMatchesPatterns = anyOfRecipientAddressMatchesPatterns;
			return this;
		}

		public Builder applyBrandingTemplate(String applyBrandingTemplate) {
			this.applyBrandingTemplate = applyBrandingTemplate;
			return this;
		}

		public Builder applyHtmlDisclaimer(Map<String, Object> applyHtmlDisclaimer) {
			this.applyHtmlDisclaimer = applyHtmlDisclaimer;
			return this;
		}

		public Builder attachmentIsNotLabeled(Boolean attachmentIsNotLabeled) {
			this.attachmentIsNotLabeled = attachmentIsNotLabeled;
			return this;
		}

		public Builder blockAccess(Boolean blockAccess) {
			this.blockAccess = blockAccess;
			return this;
		}

		public Builder blockAccessScope(String blockAccessScope) {
			this.blockAccessScope = blockAccessScope;
			return this;
		}

		public Builder comment(String comment) {
			this.comment = comment;
			return this;
		}

		public Builder contentCharacterSetContainsWords(List<String> contentCharacterSetContainsWords) {
			this.contentCharacterSetContainsWords = contentCharacterSetContainsWords;
			return this;
		}

		public Builder contentContainsSensitiveInformation(List<Map<String, Object>> contentContainsSensitiveInformation) {
			this.contentContainsSensitiveInformation = contentContainsSensitiveInformation;
			return this;
		}

		/**
		 * Convenience method to set contentContainsSensitiveInformation from SensitiveInfoTypeConfig objects.
		 */
		public Builder sensitiveInfoTypes(List<SensitiveInfoTypeConfig> sensitiveInfoTypes) {
			this.contentContainsSensitiveInformation = createSensitiveInfoTypeParameter(sensitiveInfoTypes);
			return this;
		}

		public Builder contentExtensionMatchesWords(List<String> contentExtensionMatchesWords) {
			this.contentExtensionMatchesWords = contentExtensionMatchesWords;
			return this;
		}

		public Builder contentFileTypeMatches(List<String> contentFileTypeMatches) {
			this.contentFileTypeMatches = contentFileTypeMatches;
			return this;
		}

		public Builder contentIsNotLabeled(Boolean contentIsNotLabeled) {
			this.contentIsNotLabeled = contentIsNotLabeled;
			return this;
		}

		public Builder contentIsShared(Boolean contentIsShared) {
			this.contentIsShared = contentIsShared;
			return this;
		}

		public Builder contentPropertyContainsWords(List<String> contentPropertyContainsWords) {
			this.contentPropertyContainsWords = contentPropertyContainsWords;
			return this;
		}

		public Builder disabled(Boolean disabled) {
			this.disabled = disabled;
			return this;
		}

		public Builder documentContainsWords(List<String> documentContainsWords) {
			this.documentContainsWords = documentContainsWords;
			return this;
		}

		public Builder documentCreatedBy(List<String> documentCreatedBy) {
			this.documentCreatedBy = documentCreatedBy;
			return this;
		}

		public Builder documentCreatedByMemberOf(List<String> documentCreatedByMemberOf) {
			this.documentCreatedByMemberOf = documentCreatedByMemberOf;
			return this;
		}

		public Builder documentIsPasswordProtected(Boolean documentIsPasswordProtected) {
			this.documentIsPasswordProtected = documentIsPasswordProtected;
			return this;
		}

		public Builder documentIsUnsupported(Boolean documentIsUnsupported) {
			this.documentIsUnsupported = documentIsUnsupported;
			return this;
		}

		public Builder documentMatchesPatterns(List<String> documentMatchesPatterns) {
			this.documentMatchesPatterns = documentMatchesPatterns;
			return this;
		}

		public Builder documentNameMatchesPatterns(List<String> documentNameMatchesPatterns) {
			this.documentNameMatchesPatterns = documentNameMatchesPatterns;
			return this;
		}

		public Builder documentNameMatchesWords(List<String> documentNameMatchesWords) {
			this.documentNameMatchesWords = documentNameMatchesWords;
			return this;
		}

		public Builder documentSizeOver(String documentSizeOver) {
			this.documentSizeOver = documentSizeOver;
			return this;
		}

		public Builder encryptRMSTemplate(String encryptRMSTemplate) {
			this.encryptRMSTemplate = encryptRMSTemplate;
			return this;
		}

		public Builder endpointDlpBrowserRestrictions(List<Map<String, Object>> endpointDlpBrowserRestrictions) {
			this.endpointDlpBrowserRestrictions = endpointDlpBrowserRestrictions;
			return this;
		}

		public Builder endpointDlpRestrictions(List<Map<String, Object>> endpointDlpRestrictions) {
			this.endpointDlpRestrictions = endpointDlpRestrictions;
			return this;
		}

		public Builder enforcePortalAccess(Boolean enforcePortalAccess) {
			this.enforcePortalAccess = enforcePortalAccess;
			return this;
		}

		public Builder evaluateRulePerComponent(Boolean evaluateRulePerComponent) {
			this.evaluateRulePerComponent = evaluateRulePerComponent;
			return this;
		}

		public Builder expiryDate(LocalDateTime expiryDate) {
			this.expiryDate = expiryDate;
			return this;
		}

		public Builder from(List<String> from) {
			this.from = from;
			return this;
		}

		public Builder fromAddressContainsWords(List<String> fromAddressContainsWords) {
			this.fromAddressContainsWords = fromAddressContainsWords;
			return this;
		}

		public Builder fromAddressMatchesPatterns(List<String> fromAddressMatchesPatterns) {
			this.fromAddressMatchesPatterns = fromAddressMatchesPatterns;
			return this;
		}

		public Builder fromMemberOf(List<String> fromMemberOf) {
			this.fromMemberOf = fromMemberOf;
			return this;
		}

		public Builder fromScope(String fromScope) {
			this.fromScope = fromScope;
			return this;
		}

		public Builder generateAlert(List<String> generateAlert) {
			this.generateAlert = generateAlert;
			return this;
		}

		public Builder generateIncidentReport(List<String> generateIncidentReport) {
			this.generateIncidentReport = generateIncidentReport;
			return this;
		}

		public Builder hasSenderOverride(Boolean hasSenderOverride) {
			this.hasSenderOverride = hasSenderOverride;
			return this;
		}

		public Builder headerContainsWords(Map<String, String> headerContainsWords) {
			this.headerContainsWords = headerContainsWords;
			return this;
		}

		public Builder headerMatchesPatterns(Map<String, String> headerMatchesPatterns) {
			this.headerMatchesPatterns = headerMatchesPatterns;
			return this;
		}

		public Builder incidentReportContent(List<String> incidentReportContent) {
			this.incidentReportContent = incidentReportContent;
			return this;
		}

		public Builder messageIsNotLabeled(Boolean messageIsNotLabeled) {
			this.messageIsNotLabeled = messageIsNotLabeled;
			return this;
		}

		public Builder messageSizeOver(String messageSizeOver) {
			this.messageSizeOver = messageSizeOver;
			return this;
		}

		public Builder messageTypeMatches(String messageTypeMatches) {
			this.messageTypeMatches = messageTypeMatches;
			return this;
		}

		public Builder mipRestrictAccess(List<Map<String, Object>> mipRestrictAccess) {
			this.mipRestrictAccess = mipRestrictAccess;
			return this;
		}

		public Builder moderate(Map<String, Object> moderate) {
			this.moderate = moderate;
			return this;
		}

		public Builder modifySubject(Map<String, String> modifySubject) {
			this.modifySubject = modifySubject;
			return this;
		}

		public Builder nonBifurcatingAccessScope(String nonBifurcatingAccessScope) {
			this.nonBifurcatingAccessScope = nonBifurcatingAccessScope;
			return this;
		}

		public Builder notifyAllowOverride(List<String> notifyAllowOverride) {
			this.notifyAllowOverride = notifyAllowOverride;
			return this;
		}

		public Builder notifyEmailCustomSenderDisplayName(String notifyEmailCustomSenderDisplayName) {
			this.notifyEmailCustomSenderDisplayName = notifyEmailCustomSenderDisplayName;
			return this;
		}

		public Builder notifyEmailCustomSubject(String notifyEmailCustomSubject) {
			this.notifyEmailCustomSubject = notifyEmailCustomSubject;
			return this;
		}

		public Builder notifyEmailCustomText(String notifyEmailCustomText) {
			this.notifyEmailCustomText = notifyEmailCustomText;
			return this;
		}

		public Builder notifyEmailExchangeIncludeAttachment(Boolean notifyEmailExchangeIncludeAttachment) {
			this.notifyEmailExchangeIncludeAttachment = notifyEmailExchangeIncludeAttachment;
			return this;
		}

		public Builder notifyEndpointUser(Map<String, String> notifyEndpointUser) {
			this.notifyEndpointUser = notifyEndpointUser;
			return this;
		}

		public Builder notifyOverrideRequirements(String notifyOverrideRequirements) {
			this.notifyOverrideRequirements = notifyOverrideRequirements;
			return this;
		}

		public Builder notifyPolicyTipCustomDialog(String notifyPolicyTipCustomDialog) {
			this.notifyPolicyTipCustomDialog = notifyPolicyTipCustomDialog;
			return this;
		}

		public Builder notifyPolicyTipCustomText(String notifyPolicyTipCustomText) {
			this.notifyPolicyTipCustomText = notifyPolicyTipCustomText;
			return this;
		}

		public Builder notifyPolicyTipCustomTextTranslations(Map<String, String> notifyPolicyTipCustomTextTranslations) {
			this.notifyPolicyTipCustomTextTranslations = notifyPolicyTipCustomTextTranslations;
			return this;
		}

		public Builder notifyPolicyTipDisplayOption(String notifyPolicyTipDisplayOption) {
			this.notifyPolicyTipDisplayOption = notifyPolicyTipDisplayOption;
			return this;
		}

		public Builder notifyPolicyTipUrl(String notifyPolicyTipUrl) {
			this.notifyPolicyTipUrl = notifyPolicyTipUrl;
			return this;
		}

		public Builder notifyUser(List<String> notifyUser) {
			this.notifyUser = notifyUser;
			return this;
		}

		public Builder notifyUserType(String notifyUserType) {
			this.notifyUserType = notifyUserType;
			return this;
		}

		public Builder onPremisesScannerDlpRestrictions(List<Map<String, Object>> onPremisesScannerDlpRestrictions) {
			this.onPremisesScannerDlpRestrictions = onPremisesScannerDlpRestrictions;
			return this;
		}

		public Builder prependSubject(String prependSubject) {
			this.prependSubject = prependSubject;
			return this;
		}

		public Builder priority(Integer priority) {
			this.priority = priority;
			return this;
		}

		public Builder processingLimitExceeded(Boolean processingLimitExceeded) {
			this.processingLimitExceeded = processingLimitExceeded;
			return this;
		}

		public Builder quarantine(Boolean quarantine) {
			this.quarantine = quarantine;
			return this;
		}

		public Builder recipientADAttributeContainsWords(Map<String, List<String>> recipientADAttributeContainsWords) {
			this.recipientADAttributeContainsWords = recipientADAttributeContainsWords;
			return this;
		}

		public Builder recipientADAttributeMatchesPatterns(Map<String, String> recipientADAttributeMatchesPatterns) {
			this.recipientADAttributeMatchesPatterns = recipientADAttributeMatchesPatterns;
			return this;
		}

		public Builder recipientDomainIs(List<String> recipientDomainIs) {
			this.recipientDomainIs = recipientDomainIs;
			return this;
		}

		public Builder redirectMessageTo(List<String> redirectMessageTo) {
			this.redirectMessageTo = redirectMessageTo;
			return this;
		}

		public Builder removeHeader(List<String> removeHeader) {
			this.removeHeader = removeHeader;
			return this;
		}

		public Builder removeRMSTemplate(Boolean removeRMSTemplate) {
			this.removeRMSTemplate = removeRMSTemplate;
			return this;
		}

		public Builder reportSeverityLevel(String reportSeverityLevel) {
			this.reportSeverityLevel = reportSeverityLevel;
			return this;
		}

		public Builder restrictAccess(List<Map<String, Object>> restrictAccess) {
			this.restrictAccess = restrictAccess;
			return this;
		}

		public Builder restrictBrowserAccess(Boolean restrictBrowserAccess) {
			this.restrictBrowserAccess = restrictBrowserAccess;
			return this;
		}

		public Builder ruleErrorAction(String ruleErrorAction) {
			this.ruleErrorAction = ruleErrorAction;
			return this;
		}

		public Builder senderADAttributeContainsWords(Map<String, List<String>> senderADAttributeContainsWords) {
			this.senderADAttributeContainsWords = senderADAttributeContainsWords;
			return this;
		}

		public Builder senderADAttributeMatchesPatterns(Map<String, String> senderADAttributeMatchesPatterns) {
			this.senderADAttributeMatchesPatterns = senderADAttributeMatchesPatterns;
			return this;
		}

		public Builder senderAddressLocation(String senderAddressLocation) {
			this.senderAddressLocation = senderAddressLocation;
			return this;
		}

		public Builder senderDomainIs(List<String> senderDomainIs) {
			this.senderDomainIs = senderDomainIs;
			return this;
		}

		public Builder senderIPRanges(List<String> senderIPRanges) {
			this.senderIPRanges = senderIPRanges;
			return this;
		}

		public Builder sentTo(List<String> sentTo) {
			this.sentTo = sentTo;
			return this;
		}

		public Builder sentToMemberOf(List<String> sentToMemberOf) {
			this.sentToMemberOf = sentToMemberOf;
			return this;
		}

		public Builder setHeader(Map<String, String> setHeader) {
			this.setHeader = setHeader;
			return this;
		}

		public Builder sharedByIRMUserRisk(List<String> sharedByIRMUserRisk) {
			this.sharedByIRMUserRisk = sharedByIRMUserRisk;
			return this;
		}

		public Builder stopPolicyProcessing(Boolean stopPolicyProcessing) {
			this.stopPolicyProcessing = stopPolicyProcessing;
			return this;
		}

		public Builder subjectContainsWords(List<String> subjectContainsWords) {
			this.subjectContainsWords = subjectContainsWords;
			return this;
		}

		public Builder subjectMatchesPatterns(List<String> subjectMatchesPatterns) {
			this.subjectMatchesPatterns = subjectMatchesPatterns;
			return this;
		}

		public Builder subjectOrBodyContainsWords(List<String> subjectOrBodyContainsWords) {
			this.subjectOrBodyContainsWords = subjectOrBodyContainsWords;
			return this;
		}

		public Builder subjectOrBodyMatchesPatterns(List<String> subjectOrBodyMatchesPatterns) {
			this.subjectOrBodyMatchesPatterns = subjectOrBodyMatchesPatterns;
			return this;
		}

		public Builder thirdPartyAppDlpRestrictions(List<Map<String, String>> thirdPartyAppDlpRestrictions) {
			this.thirdPartyAppDlpRestrictions = thirdPartyAppDlpRestrictions;
			return this;
		}

		public Builder triggerPowerAutomateFlow(String triggerPowerAutomateFlow) {
			this.triggerPowerAutomateFlow = triggerPowerAutomateFlow;
			return this;
		}

		public Builder unscannableDocumentExtensionIs(List<String> unscannableDocumentExtensionIs) {
			this.unscannableDocumentExtensionIs = unscannableDocumentExtensionIs;
			return this;
		}

		public Builder withImportance(String withImportance) {
			this.withImportance = withImportance;
			return this;
		}

		public DlpRuleConfig build() {
			return new DlpRuleConfig(this);
		}
	}
}
