package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediates MS.EXO.1.1v1 policy by disabling auto-forwarding for all remote domains.
 * This class first retrieves all remote domains and then updates each one to disable
 * the automatic forwarding feature.
 */
@PolicyRemediator("MS.EXO.1.1v1")
public class ExchangeAutoForwardRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	public ExchangeAutoForwardRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// constructor for Rollback interface
	public ExchangeAutoForwardRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (Auto-Forwarding Policy)", getPolicyId());

		List<RemoteDomain> domains = getRemoteDomains();
		if (domains.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_DOMAINS));
		}

		List<CompletableFuture<PolicyChangeResult>> results = domains.stream()
				.filter(domain -> domain.autoForwardEnabled)
				.map(domain -> updateDomain(domain, false, domain.autoForwardEnabled))
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (Auto-Forwarding Policy)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
					continue;
				}

				String[] parts = change.getParameter().split(":");
				String identity = parts[1].split(",")[0];
				boolean prevValue = (Boolean) change.getPrevValue();
				boolean newValue = (Boolean) change.getNewValue();

				results.add(updateDomain(new RemoteDomain(identity, prevValue), prevValue, newValue));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> updateDomain(RemoteDomain remoteDomain, boolean newValue, boolean prevValue) {
		logger.trace("Preparing to update remote domain: {}", remoteDomain.identity);

		Map<String, Object> parameters = Map.of(
				ExoConstants.IDENTITY, remoteDomain.identity,
				ExoConstants.AUTO_FORWARD_ENABLED, newValue
		);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.IDENTITY + ":" + remoteDomain.identity + ",params:" + ExoConstants.AUTO_FORWARD_ENABLED)
				.prevValue(prevValue)
				.newValue(newValue);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(ExoConstants.SET_REMOTE_DOMAIN, parameters))
				.thenApply(result -> handleDomainUpdateResult(result, paramChange, remoteDomain))
				.exceptionally(ex -> {
					logger.warn("Failed to update Remote Domain: {} , errMsg: {}", remoteDomain.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Remote Domain '" + remoteDomain.identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "Automatic email forwarding disabled for all " + successCount + " remote domains", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to disable automatic forwarding for all " + failedCount + " domains: ", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " disable automatic forwarding, failed to fix " + failedCount + " domains",
								allChanges);
					}
				});
	}

	private List<RemoteDomain> getRemoteDomains() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Remote Domains");
			return Collections.emptyList();
		}

		JsonNode domains = configNode.get(ExoConstants.CONFIG_KEY_REMOTE_DOMAINS);

		if (domains == null || !domains.isArray()) {
			logger.warn("Remote Domains '{}' node not found or not an array", ExoConstants.CONFIG_KEY_REMOTE_DOMAINS);
			return Collections.emptyList();
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, RemoteDomain.class);
		return jsonMapper.convertValue(domains, collectionType);
	}

	private PolicyChangeResult handleDomainUpdateResult(JsonNode result, ParameterChangeResult changeResult, RemoteDomain remoteDomain) {
		if (result != null && !result.has(Constants.ERROR_FIELD)) {
			//TODO Artur can we get correlation id?
			//TODO Artur yes, but need change PowerShellClient, added question to the class
			logger.trace("Successfully disabled automatic forwarding for identity: {}", remoteDomain.identity);
			changeResult.status(ParameterChangeStatus.SUCCESS);
			return IPolicyRemediator.success_(getPolicyId(), "Successfully disabled automatic forwarding for identity: " + remoteDomain.identity, List.of(changeResult));
		} else {
			String error = result != null && result.has(Constants.ERROR_FIELD) ?
					result.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
			logger.error("Failed to disable automatic forwarding for identity {}: {}", remoteDomain.identity, error);
			changeResult.status(ParameterChangeStatus.FAILED);
			return IPolicyRemediator.failed_(getPolicyId(), "Failed to disable automatic forwarding for identity " + remoteDomain.identity, List.of(changeResult));
		}
	}


	@JsonIgnoreProperties(ignoreUnknown = true)
	private static class RemoteDomain {
		public String identity;
		public Boolean autoForwardEnabled;

		//for JsonMapper
		@SuppressWarnings("unused")
		public RemoteDomain() {
		}

		public RemoteDomain(String identity, Boolean autoForwardEnabled) {
			this.identity = identity;
			this.autoForwardEnabled = autoForwardEnabled;
		}
	}
}