package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.ERROR_FIELD;
import static io.syrix.common.constants.Constants.NAME_FIELD;

/**
 * Base class for enabling security alerts in Microsoft 365.
 * <p>
 * This class duplicates required built-in alerts and enables them. It uses the approach of
 * creating custom versions of built-in alerts as Microsoft does not allow direct
 * modification of built-in alerts via PowerShell.

 * Required alerts include:
 * a. Suspicious email sending patterns detected
 * b. Suspicious Connector Activity
 * c. Suspicious Email Forwarding Activity
 * d. Messages have been delayed
 * e. Tenant restricted from sending unprovisioned email
 * f. Tenant restricted from sending email
 * g. A potentially malicious URL click was detected

 * MITRE ATT&CK TTP Mapping:
 * - T1562: Impair Defenses
 *   - T1562.006: Indicator Blocking
 */
public abstract class SecurityAlertRemediatorBase extends MicrosoftAlertBaseRemediator {

	/**
	 * Constructs a new SecurityAlertRemediatorBase with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 * @param tenantId The tenant ID to use for the hash suffix
	 */
	public SecurityAlertRemediatorBase(PowerShellClient powershellClient, String tenantId, List<String> additionalAlerts) {
		super(powershellClient, tenantId, additionalAlerts);
	}

	@Override
	protected boolean checkPreconditions() {
		return true;
	}

	/**
	 * Enables required alerts by creating custom duplicate versions that are enabled.
	 */
	protected CompletableFuture<JsonNode> configureAlerts(JsonNode allAlerts, Map<String, AlertDefinition> alertsDefintion) {
		if (!isValidAlertResponse(allAlerts)) {
			return CompletableFuture.completedFuture(
					createFailureNode("No protection alerts found.")
			);
		}

		// Lists to track existing alerts and alert statuses
		List<String> existingAlertNames = new ArrayList<>();
		List<JsonNode> requiredAlerts = identifyRequiredAlerts(allAlerts, existingAlertNames);

		if (requiredAlerts.isEmpty()) {
			return CompletableFuture.completedFuture(
					createFailureNode("No matching built-in alert policies found.")
			);
		}

		// Setup the parameters for the enabled alert
		Map<String, Object> customParams = new HashMap<>();
		customParams.put(DISABLED, false);
		customParams.put(NOTIFICATION_ENABLED, true);

		// Create an alert enablement checker
		AlertConfigChecker enablementChecker = new AlertConfigChecker() {
			@Override
			public boolean isAlertProperlyConfigured(JsonNode alert, JsonNode alerts) {
				// Check if alert is enabled
				return !alert.path(DISABLED).asBoolean(false) && alert.path(NOTIFICATION_ENABLED).asBoolean(false);
			}
		};

		// Use the common method to process the alerts
		return processAlerts(
				alertsDefintion,
				requiredAlerts,
				allAlerts,
				customParams,
				enablementChecker,
				"Successfully created and enabled all required alerts",
				"Failed to create some alerts: ",
				"All required alerts are already duplicated and enabled"
		);
	}

	/**
	 * Creates new alerts from YAML definitions with default "TenantAdmins" notification.
	 * This is used for alerts that were not found in the existing alerts but are required.
	 *
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> createNewAlerts(Map<String, AlertDefinition> alertDefinition) {
		if (newAlerts.isEmpty()) {
			logger.info("No new alerts to create");
			return CompletableFuture.completedFuture(createSuccessNode("No new alerts to create"));
		}

		logger.info("Creating {} new enabled alerts: {}", newAlerts.size(), newAlerts);

		// Use the default TenantAdmins recipient
		List<String> defaultRecipients = Collections.singletonList(DEFAULT_NOTIFY_USER);

		return createAlertsFromDefinitions(alertDefinition, newAlerts, defaultRecipients);
	}

	/**
	 * Create duplicate alerts with enabled status.
	 */
	private CompletableFuture<JsonNode> duplicateAndEnableAlerts(List<JsonNode> alertsToDuplicate, JsonNode allAlerts) {
		List<CompletableFuture<JsonNode>> duplicationTasks = new ArrayList<>();
		List<String> processedAlerts = new ArrayList<>();
		List<String> failedAlerts = new ArrayList<>();

		// First, deduplicate alerts by their name to ensure we don't process the same alert twice
		Map<String, JsonNode> uniqueAlerts = new HashMap<>();
		for (JsonNode alert : alertsToDuplicate) {
			String alertName = alert.path(NAME_FIELD).asText();
			if (!uniqueAlerts.containsKey(alertName)) {
				uniqueAlerts.put(alertName, alert);
			}
		}

		logger.info("Processing {} unique alerts after deduplication", uniqueAlerts.size());

		for (JsonNode alert : uniqueAlerts.values()) {
			String alertName = alert.path(NAME_FIELD).asText();

			// Setup the parameters for the enabled alert
			Map<String, Object> customParams = new HashMap<>();
			customParams.put(DISABLED, false);
			customParams.put(NOTIFICATION_ENABLED, true);

			duplicationTasks.add(duplicateOrUpdateAlert(alert, allAlerts, tenantHashSuffix, customParams)
					.thenApply(result -> {
						if (result != null && !result.has(ERROR_FIELD)) {
							processedAlerts.add(alertName);
						} else {
							failedAlerts.add(alertName);
						}
						return result;
					}));
		}

		if (duplicationTasks.isEmpty()) {
			logger.info("All required alerts are already duplicated and enabled");
			return CompletableFuture.completedFuture(
					createSuccessNode("All required alerts are already duplicated and enabled")
			);
		}

		return CompletableFuture.allOf(duplicationTasks.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					if (failedAlerts.isEmpty()) {
						return createSuccessNode("Successfully created and enabled all required alerts");
					} else {
						StringBuilder errorMessage = new StringBuilder("Failed to create some alerts: ");
						for (String failed : failedAlerts) {
							errorMessage.append(failed).append(", ");
						}
						// If we've processed some alerts successfully but not all
						if (!processedAlerts.isEmpty()) {
							return createPartialSuccessNode(errorMessage.toString());
						} else {
							return createFailureNode(errorMessage.toString());
						}
					}
				});
	}
}