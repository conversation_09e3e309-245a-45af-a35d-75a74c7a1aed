package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * Implements remediation for MS.EXO.18.1v1: Ensure shared mailboxes do not have sign-in enabled.
 * <p>
 * This remediation identifies shared mailboxes with sign-in enabled and disables them.
 */
@PolicyRemediator("MS.EXO.18.1v1")
public class ExchangeSharedMailboxSignInRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	public ExchangeSharedMailboxSignInRemediator(MicrosoftGraphClient graphClient,
												 PowerShellClient exchangeClient,
												 ObjectNode configNode,
												 ExchangeRemediationContext exchangeRemediationContext,
												 ExchangeRemediationConfig remediationConfig) {
		super(graphClient,exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	public ExchangeSharedMailboxSignInRemediator(MicrosoftGraphClient graphClient,
												 PowerShellClient exchangeClient) {
		this(graphClient,exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (Shared Mailbox Sign-In)", getPolicyId());

		List<SharedMailbox> mailboxes = loadSharedMailbox();
		if (mailboxes.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_SHARED_MAILBOX));
		}

		List<CompletableFuture<PolicyChangeResult>> results = mailboxes.stream()
				.filter(mailbox -> mailbox.accountEnabled)
				.map(mailbox -> updateMailbox(mailbox.id, mailbox.displayName, false, true))
				.toList();
		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (Shared Mailbox Sign-In)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} for mailbox: {} skipped", getPolicyId(), change.getParameter());
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " for mailbox: " + change.getParameter() + " skipped", List.of(change))));
					continue;
				}

				String parameter = change.getParameter();
				// Parse MailboxId and MailboxName from the parameter
				String[] parts = parameter.split(",");
				String mailboxId = parts[0].split(":")[1];
				String mailboxName = parts[1].split(":")[1];
				boolean prevValue = (Boolean) change.getPrevValue();
				boolean newValue = (Boolean) change.getNewValue();

				// Roll back to the previous value
				results.add(updateMailbox(mailboxId, mailboxName, prevValue, newValue));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	public CompletableFuture<PolicyChangeResult> updateMailbox(String mailboxId, String mailboxName, Boolean newValue, Boolean prevValue) {
		logger.info("Updating sign-in for shared mailbox {}", mailboxName);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("MailboxId:" + mailboxId + ",MailboxName:" + mailboxName)
				.prevValue(prevValue)
				.newValue(newValue);

		try {
			// Create the JSON payload to disable the account
			ObjectNode payload = jsonMapper.createObjectNode();
			payload.put("accountEnabled", newValue);

			String payloadStr = jsonMapper.writeValueAsString(payload);
			HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(payloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(body)
							.withEndpoint("/users/" + mailboxId)
							.build()
			).thenApply(result -> {
				if (result == null || result.has(Constants.ERROR_FIELD)) {
					String errorMsg = result != null ? result.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
					logger.error("Failed to update {} to {} sign-in for shared mailbox {}: {}", prevValue, newValue, mailboxName, errorMsg);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update sign-in for shared mailbox " + mailboxName + " : " + errorMsg, List.of(paramChange));
				} else {
					logger.info("Successfully updated {} to {} sign-in for shared mailbox {}", prevValue, newValue, mailboxName);
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully updated sign-in for shared mailbox " + mailboxName, List.of(paramChange));
				}
			}).exceptionally(ex -> {
				logger.error("Failed to update {} to {} sign-in for shared mailbox {}", prevValue, newValue, mailboxName, ex);
				paramChange.status(ParameterChangeStatus.FAILED);
				return IPolicyRemediator.failed_(getPolicyId(), "Failed to update sign-in for shared mailbox " + mailboxName, List.of(paramChange));
			});
		} catch (Exception e) {
			logger.error("Failed to update {} to {} sign-in for shared mailbox {}", prevValue, newValue, mailboxName, e);
			paramChange.status(ParameterChangeStatus.FAILED);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Failed to update sign-in for shared mailbox " + mailboxName + ":" + e.getMessage(), List.of(paramChange)));
		}
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						// Corrected text related to shared mailboxes
						return IPolicyRemediator.success_(getPolicyId(), "Sign-in disabled for all " + successCount + " shared mailboxes", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to disable sign-in for all " + failedCount + " shared mailboxes", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Disabled sign-in for " + successCount + " shared mailboxes, failed for " + failedCount + " shared mailboxes",
								allChanges);
					}
				});
	}

}