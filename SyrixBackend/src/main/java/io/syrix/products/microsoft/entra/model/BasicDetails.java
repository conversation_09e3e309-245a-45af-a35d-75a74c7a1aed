package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Basic details of the application.
 */
public class BasicDetails extends EnterpriseAppBaseModel {
	@JsonProperty("id")
	private String id;

	@JsonProperty("displayName")
	private String displayName;

	@JsonProperty("publisherName")
	private String publisherName;

	@JsonProperty("appRoles")
	private List<AppRole> appRoles;

	// Getters and setters
	public String getId() { return id; }
	public void setId(String id) { this.id = id; }
	public String getDisplayName() { return displayName; }
	public void setDisplayName(String displayName) { this.displayName = displayName; }
	public String getPublisherName() { return publisherName; }
	public void setPublisherName(String publisherName) { this.publisherName = publisherName; }
	public List<AppRole> getAppRoles() { return appRoles; }
	public void setAppRoles(List<AppRole> appRoles) { this.appRoles = appRoles; }
}