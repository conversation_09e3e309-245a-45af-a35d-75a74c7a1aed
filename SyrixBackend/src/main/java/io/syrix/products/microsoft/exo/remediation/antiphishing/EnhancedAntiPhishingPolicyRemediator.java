package io.syrix.products.microsoft.exo.remediation.antiphishing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.datamodel.task.remediation.exchange.AntiPhishingRemediationConfig;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.ExchangeBaseRemediator;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Enhanced remediator that implements anti-phishing and impersonation protection policies
 * with configurable settings through AntiPhishingRemediationConfig.
 * <p>
 * - MS.EXO.11.1v1: Impersonation Protection
 * - MS.EXO.11.2v1: User Safety Tips
 * - MS.EXO.11.3v1: AI-based Detection (Mailbox Intelligence)
 *
 */
//TODO Artur need to check the updates + requirement met flow (create/delete flow checked)
@PolicyRemediator("MS.EXO.11.1v1") //, MS.EXO.11.2v1, MS.EXO.11.3v1
public class EnhancedAntiPhishingPolicyRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	private static final String CREATE_RULE = "CreateRule:";
	private static final String UPDATE_RULE = "UpdateRule:";
	private static final String CREATE_POLICY = "CreatePolicy:";
	private static final String UPDATE_POLICY = "UpdatePolicy:";
	private static final String ENABLE_RULE = "EnabledRule:";

	// Policy name moved to config
	// Rule name now dynamically created based on policy name
	private static final String IDENTITY = "Identity";
	public static final String QUARANTINE = "Quarantine";
	public static final String ADMIN_DISPLAY_NAME = "MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1 - Impersonation Protection";

	private final AntiPhishingRemediationConfig config;

	/**
	 * Constructs a new EnhancedAntiPhishingPolicyRemediator with the specified dependencies.
	 *
	 * @param graphClient          The Microsoft Graph client for Graph API operations
	 * @param exchangeOnlineClient The PowerShell client for Exchange Online operations
	 * @param configNode           The configuration node containing Exchange data
	 * @param remediationContext   The context for remediation operations
	 * @param remediationConfig    The configuration for remediation
	 * @throws ExoRemediationException if anti-phishing configuration is missing
	 */
	public EnhancedAntiPhishingPolicyRemediator(
			MicrosoftGraphClient graphClient,
			PowerShellClient exchangeOnlineClient,
			ObjectNode configNode,
			ExchangeRemediationContext remediationContext,
			ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeOnlineClient, configNode, remediationContext, remediationConfig);

		if (remediationConfig == null) {
			String errorMsg = "ExchangeRemediationConfig is required for anti-phishing policy remediation";
			logger.error(errorMsg);
			throw new ExoRemediationException(errorMsg);
		}

		if (remediationConfig.getAntiPhishingConfig() == null) {
			String errorMsg = "AntiPhishingRemediationConfig is missing in ExchangeRemediationConfig";
			logger.error(errorMsg);
			throw new ExoRemediationException(errorMsg);
		}

		this.config = remediationConfig.getAntiPhishingConfig();
	}

	/**
	 * Constructs a new EnhancedAntiPhishingPolicyRemediator with the specified clients.
	 * This constructor is typically used for simpler scenarios or testing.
	 *
	 * @param graphClient          The Microsoft Graph client for Graph API operations
	 * @param exchangeOnlineClient The PowerShell client for Exchange Online operations
	 * @throws ExoRemediationException if required configuration cannot be initialized
	 */
	public EnhancedAntiPhishingPolicyRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeOnlineClient) {
		super(graphClient, exchangeOnlineClient, null, null, null);
		this.config = null;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation_ for anti-phishing policies (MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1)");

		return configurePolicy().thenCompose(policyResult -> {
					if (policyResult.getResult() != RemediationResult.FAILED) {
						return configureRule()
								.thenApply(ruleResult -> combineResults_(List.of(policyResult, ruleResult)));
					}
					return CompletableFuture.completedFuture(policyResult);
				}
		);
	}

	//-----------Policy remediate
	private CompletableFuture<PolicyChangeResult> configurePolicy() {
		try {
			AntiPhishingPolicy foundPolicy = findSyrixPolicy();
			if (foundPolicy == null) {
				return createPolicy();
			}

			if (isValidPolicy(foundPolicy)) {
				return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Anti-phishing policy is already configured"));
			}
			return updatePolicy(foundPolicy);
		} catch (Exception ex) {
			logger.error("Exception during anti-phishing policy configure", ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}


	private CompletableFuture<PolicyChangeResult> createPolicy() {
		logger.info("Creating anti-phishing policy: {}", config.getPolicyName());
		Map<String, Object> parameters = buildAntiPhishPolicyParameters();
		parameters.put("Name", config.getPolicyName());

		ParameterChangeResult change = new ParameterChangeResult().parameter(CREATE_POLICY+ config.getPolicyName());
		return runCommand("New-AntiPhishPolicy", parameters, change);
	}

	private CompletableFuture<PolicyChangeResult> updatePolicy(AntiPhishingPolicy foundPolicy) {
		logger.info("Updating anti-phishing policy: {}", config.getPolicyName());

		Map<String, Object> parameters = buildAntiPhishPolicyParameters();
		parameters.put(IDENTITY, foundPolicy.identity);

		ParameterChangeResult change = new ParameterChangeResult()
				.parameter(UPDATE_POLICY + config.getPolicyName())
				.prevValue(toParameters(foundPolicy));

		return runCommand("Set-AntiPhishPolicy", parameters, change);
	}


//-----------Rule remediate

	private CompletableFuture<PolicyChangeResult> configureRule() {
		try {
			AntiPhishingRule foundRule = findSyrixRole();
			if (foundRule == null) {
				return createRule();
			}

			if (isValidRule(foundRule)) {
				return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Anti-phishing rule is already configured"));
			}

			return updateRule(foundRule).thenCompose(updateResult -> {
				if (updateResult.getResult() != RemediationResult.FAILED) {
					return enableRule(foundRule).thenApply(enableResult -> combineResults_(List.of(updateResult, enableResult)));
				}
				return CompletableFuture.completedFuture(updateResult);
			});

		} catch (Exception ex) {
			logger.error("Exception during anti-phishing policy configure", ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> createRule() {
		logger.info("Creating anti-phishing rule: {}", config.getRuleName());

		Map<String, Object> parameters = buildAntiPhishRuleParameters();
		parameters.put("Enabled", true);
		parameters.put("Name", config.getRuleName());
		parameters.put("AntiPhishPolicy", config.getPolicyName());

		ParameterChangeResult change = new ParameterChangeResult().parameter(CREATE_RULE + config.getRuleName());

		return runCommand("New-AntiPhishRule", parameters, change);
	}

	private CompletableFuture<PolicyChangeResult> updateRule(AntiPhishingRule foundRule) {
		if (!isRuleUpdateNeeded(foundRule)) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Anti-phishing rule parameters is already valid"));
		}

		logger.info("Updating anti-phishing rule: {}", config.getRuleName());
		Map<String, Object> parameters = buildAntiPhishRuleParameters();
		parameters.put(IDENTITY, config.getRuleName());

		ParameterChangeResult change = new ParameterChangeResult()
				.parameter(UPDATE_RULE + config.getRuleName())
				.prevValue(toParameters(foundRule));

		return runCommand("Set-AntiPhishRule", parameters, change);
	}

	private CompletableFuture<PolicyChangeResult> enableRule(AntiPhishingRule foundRule) {
		if (!isRuleEnableNeeded(foundRule)) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Anti-phishing rule parameters is already valid"));
		}

		logger.info("Enabling anti-phishing rule: {}", config.getRuleName());
		Map<String, Object> parameters = Map.of(IDENTITY, config.getRuleName());
		ParameterChangeResult change = new ParameterChangeResult()
				.parameter(ENABLE_RULE + config.getRuleName())
				.prevValue(toParameters(foundRule));


		return runCommand("Enable-AntiPhishRule", parameters, change);
	}

//----------- Command

	private CompletableFuture<PolicyChangeResult> runCommand(String command, Map<String, Object> parameters, ParameterChangeResult change) {
		logger.info("run command: {}", command);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(command, parameters))
				.thenApply(result -> {
					logger.info("Successfully command: {}", command);
					logger.trace("Successfully command: {}, result:{}", command, result);
					timeOutIfNewCommand(command);
					change.status(ParameterChangeStatus.SUCCESS).newValue(parameters);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully command: " + command, List.of(change));
				}).exceptionally(ex -> {
					logger.error("Failed to execute command: {}", command, ex);
					change.status(ParameterChangeStatus.FAILED).newValue(parameters);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to execute command: " + command + " " + ex.getMessage(), List.of(change));
				});
	}


	/**
	 * Combines multiple PolicyChangeResult objects into a single result with combined status and changes.
	 * Status logic:
	 * - SUCCESS: If all changes are successful or some didn't need changes
	 * - PARTIAL_SUCCESS: If there were changes, but not all were successful
	 * - REQUIREMENT_MET: If all parameters didn't require changes
	 * - FAILED: If all policies failed
	 * - UNKNOWN: In all other cases
	 *
	 * @param results List of PolicyChangeResult objects to combine
	 * @return A combined PolicyChangeResult
	 */
	private PolicyChangeResult combineResults_(List<PolicyChangeResult> results) {
		List<ParameterChangeResult> allChanges = results.stream()
				.map(PolicyChangeResult::getChanges)
				.filter(Objects::nonNull)
				.flatMap(Collection::stream)
				.filter(Objects::nonNull)
				.toList();

		// Count results by type
		long successCount = results.stream()
				.filter(r -> r.getResult() == RemediationResult.SUCCESS)
				.count();
		long failedCount = results.stream()
				.filter(r -> r.getResult() == RemediationResult.FAILED)
				.count();
		long requirementMetCount = results.stream()
				.filter(r -> r.getResult() == RemediationResult.REQUIREMENT_MET)
				.count();
		long partialSuccessCount = results.stream()
				.filter(r -> r.getResult() == RemediationResult.PARTIAL_SUCCESS)
				.count();
		
		// Requirement Met: If all parameters didn't require changes
		if (requirementMetCount == results.size()) {
			return IPolicyRemediator.requirementMet_(getPolicyId(), "No changes were required");
		} 
		// Failed: If all policies failed
		else if (failedCount == results.size()) {
			return IPolicyRemediator.failed_(getPolicyId(), "All changes failed", allChanges);
		} 
		// Success: If all changes are successful or some didn't need changes
		else if (failedCount == 0 && partialSuccessCount == 0 && 
			(successCount > 0) && 
			(successCount + requirementMetCount == results.size())) {
			return IPolicyRemediator.success_(getPolicyId(), "All changes successful or not required", allChanges);
		} 
		// Partial Success: If there were changes, but not all were successful
		else if (successCount > 0 && (failedCount > 0 || partialSuccessCount > 0)) {
			return IPolicyRemediator.partial_success_(getPolicyId(), "Some changes were successful, but not all", allChanges);
		} 
		// Unknown: In all other cases
		else {
			return IPolicyRemediator.unknown_(getPolicyId(), "Unable to determine overall status", allChanges);
		}
	}

	private AntiPhishingPolicy findSyrixPolicy() {
		return loadAntiPhishingPolicies().stream()
				.filter(policy -> config.getPolicyName().equals(policy.name))
				.findFirst()
				.orElse(null);
	}

	private AntiPhishingRule findSyrixRole() {
		return loadAntiPhishingRules().stream()
				.filter(rule -> rule.name.equals(config.getRuleName()))
				.filter(rule -> rule.antiPhishPolicy.equals(config.getPolicyName()))
				.findFirst()
				.orElse(null);
	}

	/**
	 * Implements the rollback functionality for anti-phishing policy changes.
	 * This method follows the reverse order of the remediation process:
	 * first rolling back rules, then policies.
	 *
	 * @param fixResult The result of the original remediation operation containing the changes to roll back
	 * @return A CompletableFuture containing the result of the rollback operation
	 */
	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for anti-phishing policy changes (MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1)");

		if (fixResult == null) {
			logger.warn("Cannot rollback null policy changes");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Cannot rollback null policy changes"));
		}

		List<ParameterChangeResult> changes = fixResult.getChanges();
		if (changes == null || changes.isEmpty()) {
			logger.warn("No parameter changes found in the fixResult, nothing to rollback");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No parameter changes found in the fixResult, nothing to rollback"));
		}


		final ParameterChangeResult enableRuleChange = changes.stream()
				.filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
				.filter(changeResult -> changeResult.getParameter().startsWith(ENABLE_RULE))
				.findFirst()
				.orElse(null);


		final ParameterChangeResult ruleChange = changes.stream()
				.filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
				.filter(changeResult -> changeResult.getParameter().startsWith(CREATE_RULE) || changeResult.getParameter().startsWith(UPDATE_RULE))
				.findFirst()
				.orElse(null);


		final ParameterChangeResult policyChange = changes.stream()
				.filter(changeResult -> changeResult.getStatus() == ParameterChangeStatus.SUCCESS)
				.filter(changeResult -> changeResult.getParameter().startsWith(CREATE_POLICY) || changeResult.getParameter().startsWith(UPDATE_POLICY))
				.findFirst()
				.orElse(null);


		return disableAntiPhishingRule(enableRuleChange).thenCompose(disableRusult -> {
			if (disableRusult.getResult() != RemediationResult.FAILED) {
				return rollbackRule(ruleChange).thenCompose(ruleResult -> {
					if (ruleResult.getResult() != RemediationResult.FAILED) {
						return rollbackPolicy(policyChange).thenCompose(policyResult ->
								CompletableFuture.completedFuture(combineResults_(List.of(disableRusult, ruleResult, policyResult))));
					} else {
						return CompletableFuture.completedFuture(combineResults_(List.of(disableRusult, ruleResult)));
					}
				});
			}
			return CompletableFuture.completedFuture(disableRusult);
		});
	}

	private CompletableFuture<PolicyChangeResult> disableAntiPhishingRule(ParameterChangeResult originalChange) {
		if (originalChange == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}
		String ruleName = originalChange.getParameter().substring(ENABLE_RULE.length());
		logger.info("Rolling back anti-phishing policy change:"+ruleName);

		Map<String, Object> parameters = Map.of(IDENTITY, ruleName);
		ParameterChangeResult change = new ParameterChangeResult()
				.parameter("DisabledRule:" + ruleName)
				.prevValue(originalChange.getNewValue());

		return runCommand("Disable-AntiPhishRule", parameters, change);
	}

	private boolean isValidRule(AntiPhishingRule rule) {
		boolean isValid = "Enabled".equals(rule.state);
		isValid &= rule.priority == 0;
		isValid &= CollectionUtils.isEqualCollection(rule.recipientDomainIs, config.getRecipientDomain());

		return isValid;
	}

	/**
	 * Checks if rule parameters need to be updated (except state).
	 *
	 * @param rule The rule to check
	 * @return true if parameter update is needed
	 */
	private boolean isRuleUpdateNeeded(AntiPhishingRule rule) {
		return !rule.antiPhishPolicy.equals(config.getPolicyName())
			   || rule.priority != 0
			   || !CollectionUtils.isEqualCollection(rule.recipientDomainIs, config.getRecipientDomain());
	}

	/**
	 * Checks if the rule needs to be enabled.
	 *
	 * @param rule The rule to check
	 * @return true if the rule needs to be enabled
	 */
	private boolean isRuleEnableNeeded(AntiPhishingRule rule) {
		return !"Enabled".equals(rule.state);
	}

	private boolean isValidPolicy(AntiPhishingPolicy policy) {
		boolean isValid = policy.enableSpoofIntelligence;
		isValid &= QUARANTINE.equals(policy.authenticationFailAction);

		if (config.getEnableMailboxIntelligence() != null) {
			isValid &= policy.enableMailboxIntelligence == config.getEnableMailboxIntelligence();
			if (config.getEnableMailboxIntelligence()) {
				isValid &= policy.enableMailboxIntelligenceProtection;
				isValid &= policy.mailboxIntelligenceProtectionAction.equals(config.getMailboxIntelligenceAction());
			}
		}


		isValid &= policy.enableUnauthenticatedSender;
		isValid &= policy.enableViaTag;
		if (config.getEnableFirstContactSafetyTips() != null)
			isValid &= policy.enableFirstContactSafetyTips == config.getEnableFirstContactSafetyTips();
		if (config.getEnableSimilarUsersSafetyTips() != null)
			isValid &= policy.enableSimilarUsersSafetyTips == config.getEnableSimilarUsersSafetyTips();
		if (config.getEnableSimilarDomainsSafetyTips() != null)
			isValid &= policy.enableSimilarDomainsSafetyTips == config.getEnableSimilarDomainsSafetyTips();
		if (config.getEnableUnusualCharactersSafetyTips() != null)
			isValid &= policy.enableUnusualCharactersSafetyTips == config.getEnableUnusualCharactersSafetyTips();


		// Domain impersonation protection
		if (!config.getProtectedDomains().isEmpty()) {
			isValid &= policy.enableTargetedDomainsProtection;
			isValid &= policy.targetedDomainsToProtect.equals(config.getProtectedDomains());
			isValid &= policy.targetedDomainProtectionAction.equals(QUARANTINE);
		} else {
			// If no specific domains are provided, protect all organization domains
			isValid &= policy.enableOrganizationDomainsProtection;
		}

		// User impersonation protection
		if (!config.getProtectedUsers().isEmpty()) {
			isValid &= policy.enableTargetedUserProtection;
			isValid &= policy.targetedUsersToProtect.equals(config.getProtectedUsers());
			isValid &= policy.targetedUserProtectionAction.equals(QUARANTINE);
		}

		// Advanced settings
		if (config.getPhishThresholdLevel() != null) isValid &= policy.phishThresholdLevel.equals(config.getPhishThresholdLevel());

		// Add admin display name for policy identification
		isValid &= policy.adminDisplayName.equals(ADMIN_DISPLAY_NAME);

		return isValid;
	}

	/**
	 * Converts an AntiPhishingPolicy object to a map of parameters for PowerShell commands.
	 *
	 * @param policy The anti-phishing policy to convert
	 * @return Map of parameter name to parameter value
	 */
	private Map<String, Object> toParameters(AntiPhishingPolicy policy) {
		Map<String, Object> parameters = new HashMap<>();

		// Add basic policy parameters
//		parameters.put("Name", policy.name);
		parameters.put(IDENTITY, policy.identity);

		// MS.EXO.11.1v1: Configure impersonation protection
		parameters.put("EnableSpoofIntelligence", policy.enableSpoofIntelligence);
		parameters.put("AuthenticationFailAction", policy.authenticationFailAction);

		// MS.EXO.11.3v1: Configure AI-based detection
		parameters.put("EnableMailboxIntelligence", policy.enableMailboxIntelligence);

		parameters.put("EnableMailboxIntelligenceProtection", policy.enableMailboxIntelligenceProtection);
		parameters.put("MailboxIntelligenceProtectionAction", policy.mailboxIntelligenceProtectionAction);

		// MS.EXO.11.2v1: Configure user safety tips
		parameters.put("EnableUnauthenticatedSender", policy.enableUnauthenticatedSender);
		parameters.put("EnableViaTag", policy.enableViaTag);
		parameters.put("EnableFirstContactSafetyTips", policy.enableFirstContactSafetyTips);
		parameters.put("EnableSimilarUsersSafetyTips", policy.enableSimilarUsersSafetyTips);
		parameters.put("EnableSimilarDomainsSafetyTips", policy.enableSimilarDomainsSafetyTips);
		parameters.put("EnableUnusualCharactersSafetyTips", policy.enableUnusualCharactersSafetyTips);

		// Domain impersonation protection
		parameters.put("EnableTargetedDomainsProtection", policy.enableTargetedDomainsProtection);
		parameters.put("TargetedDomainsToProtect", policy.targetedDomainsToProtect);
		parameters.put("TargetedDomainProtectionAction", policy.targetedDomainProtectionAction);
		// If no specific domains are provided, protect all organization domains
		parameters.put("EnableOrganizationDomainsProtection", policy.enableOrganizationDomainsProtection);


		// User impersonation protection
		parameters.put("EnableTargetedUserProtection", policy.enableTargetedUserProtection);
		parameters.put("TargetedUsersToProtect", policy.targetedUsersToProtect);
		parameters.put("TargetedUserProtectionAction", policy.targetedUserProtectionAction);

		// Advanced settings
		parameters.put("PhishThresholdLevel", policy.phishThresholdLevel);

		// Admin display name
		parameters.put("AdminDisplayName", policy.adminDisplayName);

		return parameters;
	}

	/**
	 * Builds the parameters for the anti-phishing policy based on the configuration.
	 */
	private Map<String, Object> buildAntiPhishPolicyParameters() {
		Map<String, Object> parameters = new HashMap<>();

		// MS.EXO.11.1v1: Configure impersonation protection
		parameters.put("EnableSpoofIntelligence", true);
		parameters.put("AuthenticationFailAction", QUARANTINE);

		// MS.EXO.11.3v1: Configure AI-based detection
		if (config.getEnableMailboxIntelligence() != null) {
			parameters.put("EnableMailboxIntelligence", config.getEnableMailboxIntelligence());
			if (config.getEnableMailboxIntelligence()) {
				parameters.put("EnableMailboxIntelligenceProtection", true);
				parameters.put("MailboxIntelligenceProtectionAction", config.getMailboxIntelligenceAction());
			}
		}


		// MS.EXO.11.2v1: Configure user safety tips
		parameters.put("EnableUnauthenticatedSender", true);
		parameters.put("EnableViaTag", true);
		if (config.getEnableFirstContactSafetyTips() != null)
			parameters.put("EnableFirstContactSafetyTips", config.getEnableFirstContactSafetyTips());
		if (config.getEnableSimilarUsersSafetyTips() != null)
			parameters.put("EnableSimilarUsersSafetyTips", config.getEnableSimilarUsersSafetyTips());
		if (config.getEnableSimilarDomainsSafetyTips() != null)
			parameters.put("EnableSimilarDomainsSafetyTips", config.getEnableSimilarDomainsSafetyTips());
		if (config.getEnableUnusualCharactersSafetyTips() != null)
			parameters.put("EnableUnusualCharactersSafetyTips", config.getEnableUnusualCharactersSafetyTips());

		// Domain impersonation protection
		if (!config.getProtectedDomains().isEmpty()) {
			parameters.put("EnableTargetedDomainsProtection", true);
			parameters.put("TargetedDomainsToProtect", config.getProtectedDomains());
			parameters.put("TargetedDomainProtectionAction", QUARANTINE);
		} else {
			// If no specific domains are provided, protect all organization domains
			parameters.put("EnableOrganizationDomainsProtection", true);
		}

		// User impersonation protection
		if (!config.getProtectedUsers().isEmpty()) {
			parameters.put("EnableTargetedUserProtection", true);
			parameters.put("TargetedUsersToProtect", config.getProtectedUsers());
			parameters.put("TargetedUserProtectionAction", QUARANTINE);
		}

		// Advanced settings
		if (config.getPhishThresholdLevel() != null) parameters.put("PhishThresholdLevel", config.getPhishThresholdLevel());

		// Add admin display name for policy identification
		parameters.put("AdminDisplayName", "MS.EXO.11.1v1, MS.EXO.11.2v1, MS.EXO.11.3v1 - Impersonation Protection");

		return parameters;
	}

	private Map<String, Object> toParameters(AntiPhishingRule rule) {
		Map<String, Object> parameters = new HashMap<>();

		// Set basic rule properties
		parameters.put("Priority", rule.priority);
		parameters.put("RecipientDomainIs", rule.recipientDomainIs);

		return parameters;
	}

	/**
	 * Builds the parameters for the anti-phishing rule based on the configuration.
	 */
	private Map<String, Object> buildAntiPhishRuleParameters() {
		Map<String, Object> parameters = new HashMap<>();

		// Set basic rule properties
		parameters.put("Priority", 0);

		// Always use RecipientDomainIs, defaulting to the primary domain if none specified
		if (config.getRecipientDomain() != null && !config.getRecipientDomain().isEmpty()) {
			parameters.put("RecipientDomainIs", config.getRecipientDomain());
		} else if (!config.getProtectedDomains().isEmpty()) {
			// Use the first protected domain if recipient domain not specified
			parameters.put("RecipientDomainIs", config.getProtectedDomains().getFirst());
		} else {
			// If all else fails, we need to have a default domain
			throw new ExoRemediationException("Cannot create anti-phishing rule: no domain specified");
		}
		return parameters;
	}

	/**
	 * Loads anti-phishing policies from configuration JSON.
	 *
	 * @return List of anti-phishing policies or empty list if none found
	 */
	private List<AntiPhishingPolicy> loadAntiPhishingPolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Anti-Phishing Policies");
			throw new ExoRemediationException("Config node is null, returning empty list of Anti-Phishing Policies");
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_ANTI_PHISH_POLICY);
		if (config == null || !config.isArray()) {
			logger.warn("Anti-phishing policy '{}' node not found or has invalid format", ExoConstants.CONFIG_KEY_ANTI_PHISH_POLICY);
			throw new ExoRemediationException("Anti-phishing policy '" + ExoConstants.CONFIG_KEY_ANTI_PHISH_POLICY + "' node not found or has invalid format");
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, AntiPhishingPolicy.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	/**
	 * Loads anti-phishing rules from configuration JSON.
	 *
	 * @return List of anti-phishing rules or empty list if none found
	 */
	protected List<AntiPhishingRule> loadAntiPhishingRules() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Anti-Phishing Rules");
			throw new ExoRemediationException("Config node is null, returning empty list of Anti-Phishing Policies");
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_ANTI_PHISH_RULE);
		if (config == null || !config.isArray()) {
			logger.warn("Anti-phishing rule '{}' node not found or has invalid format", ExoConstants.CONFIG_KEY_ANTI_PHISH_RULE);
			throw new ExoRemediationException("Anti-phishing rule '" + ExoConstants.CONFIG_KEY_ANTI_PHISH_RULE + "' node not found or has invalid format");
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, AntiPhishingRule.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	private CompletableFuture<PolicyChangeResult> rollbackPolicy(ParameterChangeResult originalChange) {
		if (originalChange == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}
		String parameter = originalChange.getParameter();

		if (parameter.startsWith(CREATE_POLICY)) {
			return removePolicy(originalChange);
		} else if (parameter.startsWith(UPDATE_POLICY)) {
			return rollbackUpdatePolicy(originalChange);
		}
		return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Unknown rule change operation: " + parameter));
	}


	private CompletableFuture<PolicyChangeResult> rollbackRule(ParameterChangeResult originalChange) {
		if (originalChange == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		if (originalChange.getParameter().startsWith(CREATE_RULE)) {
			return removeRule(originalChange);
		} else if (originalChange.getParameter().startsWith(UPDATE_RULE)) {
			return rollbackUpdateRule(originalChange);
		}
		return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Unknown rule change operation: " + originalChange.getParameter()));
	}


	private CompletableFuture<PolicyChangeResult> rollbackUpdatePolicy(ParameterChangeResult originalChange) {
		String policyName = originalChange.getParameter().substring(UPDATE_POLICY.length());
		logger.info("Rolling back policy change for: {}", policyName);

		ParameterChangeResult rollbackChange = new ParameterChangeResult()
				.parameter(UPDATE_POLICY + policyName)
				.prevValue(originalChange.getNewValue());
		@SuppressWarnings("unchecked")
		Map<String, Object> parameters = (Map<String, Object>) originalChange.getPrevValue();

		return runCommand("Set-AntiPhishPolicy", parameters, rollbackChange);
	}

	private CompletableFuture<PolicyChangeResult> removePolicy(ParameterChangeResult originalChange) {
		String policyName = originalChange.getParameter().substring(CREATE_POLICY.length());

		logger.info("Rolling back by creating anti-phishing policy: {}", policyName);

		ParameterChangeResult rollbackChange = new ParameterChangeResult()
				.parameter("RemovePolicy:" + policyName)
				.prevValue(originalChange.getNewValue());

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, policyName);
		parameters.put("Confirm", false);

		return runCommand("Remove-AntiPhishPolicy", parameters, rollbackChange);
	}

	private CompletableFuture<PolicyChangeResult> removeRule(ParameterChangeResult originalChange) {
		String ruleName = originalChange.getParameter().substring(CREATE_RULE.length());
		logger.info("Rolling back by removing anti-phishing rule: {}", ruleName);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, ruleName);
		parameters.put("Confirm", false);

		ParameterChangeResult rollbackChange = new ParameterChangeResult().prevValue(originalChange.getNewValue());

		return runCommand("Remove-AntiPhishRule", parameters, rollbackChange);
	}

	private CompletableFuture<PolicyChangeResult> rollbackUpdateRule(ParameterChangeResult originalChange) {
		String ruleName = originalChange.getParameter().substring(UPDATE_RULE.length());
		logger.info("Rolling back by update anti-phishing rule: {}", ruleName);

		//noinspection unchecked
		Map<String, Object> parameters = (Map<String, Object>) originalChange.getPrevValue();
		parameters.put(IDENTITY, ruleName);
		ParameterChangeResult rollbackChange = new ParameterChangeResult()
				.parameter(UPDATE_RULE + ruleName)
				.prevValue(originalChange.getNewValue());

		return runCommand("Set-AntiPhishRule", parameters, rollbackChange);
	}

	private void timeOutIfNewCommand(String command) {
		try {
			if (command.startsWith("New-")) {
				logger.info("Time out for 30 seconds");
				Thread.sleep(30 * 1000);
				logger.info("Time out for 30 seconds done");
			}
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * Helper class to represent an anti-phishing policy in JSON configuration.
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class AntiPhishingPolicy {
		public String name;
		public String identity;

		// MS.EXO.11.1v1: Impersonation Protection
		public Boolean enableSpoofIntelligence;
		public String authenticationFailAction;

		// Domain impersonation protection
		public Boolean enableTargetedDomainsProtection;
		public List<String> targetedDomainsToProtect;
		public String targetedDomainProtectionAction;
		public Boolean enableOrganizationDomainsProtection;

		// User impersonation protection
		public Boolean enableTargetedUserProtection;
		public List<String> targetedUsersToProtect;
		public String targetedUserProtectionAction;

		// MS.EXO.11.2v1: User Safety Tips
		public Boolean enableUnauthenticatedSender;
		public Boolean enableViaTag;
		public Boolean enableFirstContactSafetyTips;
		public Boolean enableSimilarUsersSafetyTips;
		public Boolean enableSimilarDomainsSafetyTips;
		public Boolean enableUnusualCharactersSafetyTips;

		// MS.EXO.11.3v1: AI-based Detection
		public Boolean enableMailboxIntelligence;
		public Boolean enableMailboxIntelligenceProtection;
		public String mailboxIntelligenceProtectionAction;

		// Advanced settings
		public Integer phishThresholdLevel;
		public String adminDisplayName;
	}

	/**
	 * Helper class to represent an anti-phishing rule in JSON configuration.
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class AntiPhishingRule {
		public String name;
		public String antiPhishPolicy;

		public String state;
		public int priority;
		public List<String> recipientDomainIs;
	}
}