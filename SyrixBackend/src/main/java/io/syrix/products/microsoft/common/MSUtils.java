package io.syrix.products.microsoft.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.model.TenantDetails;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MSUtils {
	private static final Logger logger = LoggerFactory.getLogger(MSUtils.class);
	public static TenantDetails getTenantDetails(MicrosoftGraphClient graphClient) {
		try {
			// Use Graph API to get organization details
			GraphRequest request = GraphRequest.builder()
					.withEndpoint("/organization")
					.withMethod(HttpMethod.GET)
					.build();

			JsonNode orgResponse = graphClient.makeGraphRequest(request).get();

			if (orgResponse != null && orgResponse.has(Constants.VALUE_FIELD) && orgResponse.get(Constants.VALUE_FIELD).size() > 0) {
				JsonNode org = orgResponse.get(Constants.VALUE_FIELD).get(0);

				// Get initial domain from verified domains
				String domainName = "";
				String tenantId = org.path(Constants.ID_FIELD).asText();

				JsonNode verifiedDomains = org.path("verifiedDomains");
				if (verifiedDomains.isArray()) {
					for (JsonNode domain : verifiedDomains) {
						if (domain.path("isInitial").asBoolean()) {
							domainName = domain.path("name").asText();
							break;
						}
					}
				}

				ObjectMapper mapper = new ObjectMapper();
				ObjectNode additionalData = mapper.createObjectNode();

				// Copy all fields from org to additionalData
				org.fields().forEachRemaining(field ->
						additionalData.set(field.getKey(), field.getValue())
				);

				return TenantDetails.builder()
						.displayName(org.path("displayName").asText())
						.domainName(domainName)
						.additionalData(additionalData)
						.tenantId(tenantId)
						.build();

			} else {
				throw new IllegalStateException("No organization data found in Graph API response");
			}

		} catch (Exception e) {
			logger.error("Error retrieving Tenant details from Graph API", e);
			return TenantDetails.builder()
					.displayName("Error retrieving Display name")
					.domainName("Error retrieving Domain name")
					.tenantId("Error retrieving Tenant ID")
					.build();
		}
	}
}
