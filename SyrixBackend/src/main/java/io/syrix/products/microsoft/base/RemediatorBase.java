package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.exo.ExoConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static io.syrix.common.constants.Constants.MESSAGE_FIELD;
import static io.syrix.common.constants.Constants.POLICY_ID_FIELD;
import static io.syrix.common.constants.Constants.STATUS_FIELD;

public abstract class RemediatorBase implements IPolicyRemediator {
	protected final Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public String getPolicyId() {
		PolicyRemediator annotation = getClass().getAnnotation(PolicyRemediator.class);
		if (annotation == null) {
			throw new IllegalStateException("Class " + getClass().getSimpleName() + " must be annotated with @PolicyRemediator");
		}
		return annotation.value();
	}

	/**
	 * Static method to get policy ID from the calling class's annotation.
	 * This method uses reflection to determine which subclass called it.
	 */
	public static String getPolicyName() {
		// Get the class that called this method
		Class<?> callingClass = StackWalker.getInstance(StackWalker.Option.RETAIN_CLASS_REFERENCE)
				.getCallerClass();

		PolicyRemediator annotation = callingClass.getAnnotation(PolicyRemediator.class);
		if (annotation == null) {
			throw new IllegalStateException("Class " + callingClass.getSimpleName() + " must be annotated with @PolicyRemediator");
		}
		return annotation.value();
	}

	/**
	 * Creates a success JsonNode for successful remediation.
	 *
	 * @param policyId The policy ID
	 * @param message  The success message
	 * @return A JsonNode representing the success
	 */
	protected JsonNode createSuccessNode(ObjectMapper mapper, String message) {
		ObjectNode node = mapper.createObjectNode();
		node.put(STATUS_FIELD, ExoConstants.STATUS_SUCCESS);
		node.put(POLICY_ID_FIELD, getPolicyId());
		node.put(MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Creates an error JsonNode for failed remediation.
	 *
	 * @param policyId The policy ID
	 * @param message  The error message
	 * @return A JsonNode representing the failure
	 */
	protected JsonNode createFailureNode(ObjectMapper mapper, String message) {
		ObjectNode node = mapper.createObjectNode();
		node.put(STATUS_FIELD, ExoConstants.STATUS_FAILED);
		node.put(POLICY_ID_FIELD, getPolicyId());
		node.put(MESSAGE_FIELD, message);
		return node;
	}
}
