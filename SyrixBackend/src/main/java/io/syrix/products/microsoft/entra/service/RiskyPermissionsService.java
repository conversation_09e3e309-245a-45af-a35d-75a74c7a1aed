package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static io.syrix.common.constants.Constants.DESCRIPTION_FIELD;
import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.ROLE_ID;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.RISKY_APPLICATIONS;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.RISKY_THIRD_PARTY_SERVICE_PRINCIPALS;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.SUCCESSFUL_COMMANDS;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.UNSUCCESSFUL_COMMANDS;

class RiskyPermissionsService extends BaseConfigurationService {
	private static final String ROLE_DISPLAY_NAME = "RoleDisplayName";
	private static final String APPLICATION_DISPLAY_NAME = "ApplicationDisplayName";
	private static final String REQUIRED_RESOURCE_ACCESS = "requiredResourceAccess";
	private static final String AUDIENCES = "audiences";
	private static final String OBJECT_ID = "ObjectId";
	private static final String KEY_CREDENTIALS = "KeyCredentials";
	private static final String PASSWORD_CREDENTIALS = "PasswordCredentials";
	private static final String FEDERATED_CREDENTIALS = "FederatedCredentials";
	private static final String IS_MULTI_TENANT_ENABLED = "IsMultiTenantEnabled";
	private static final String APP_ID = "appId";
	private static final String RISKY_PERMISSIONS_KEY = "RiskyPermissions";
	private static final String IS_ADMIN_CONSENTED = "IsAdminConsented";
	public static final String RISKY_PERMISSIONS = "risky_permissions";
	public static final String APPLICATIONS = "riskyApplications";
	public static final String THIRD_PARTY_SERVICE_PRINCIPALS = "riskyThirdPartyServicePrincipals";


	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final Logger logger;

	RiskyPermissionsService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
		this.objectMapper = objectMapper;
		this.logger = LoggerFactory.getLogger(getClass());
	}

	/**
	 * Gets applications and service principals with risky permissions.
	 * This is the Java equivalent of the PowerShell implementation that identifies applications
	 * and service principals with potentially risky API permissions.
	 *
	 * @return Future containing riskyApplications and riskyThirdPartyServicePrincipals as JsonNode
	 */
	public CompletableFuture<JsonNode> getRiskyPermissions() {
		return withRetry(() -> {
			try {
				// Load risky permissions configuration
				JsonNode riskyPermissionsJson = loadRiskyPermissionsJson();

				// Get applications with risky permissions
				CompletableFuture<JsonNode> riskyAppsFuture = getApplicationsWithRiskyPermissions(riskyPermissionsJson);

				// Get service principals with risky permissions
				CompletableFuture<JsonNode> riskySPsFuture = getServicePrincipalsWithRiskyPermissions(riskyPermissionsJson);

				return CompletableFuture.allOf(riskyAppsFuture, riskySPsFuture)
						.thenApply(v -> {
							JsonNode riskyApps = riskyAppsFuture.join();
							JsonNode riskySPs = riskySPsFuture.join();

							ObjectNode result = objectMapper.createObjectNode();

							if (!riskyApps.isEmpty() && !riskySPs.isEmpty()) {
								// Format results similar to PowerShell's Format-RiskyApplications and Format-RiskyThirdPartyServicePrincipals
								result.set(APPLICATIONS, formatRiskyApplications(riskyApps, riskySPs));
								result.set(THIRD_PARTY_SERVICE_PRINCIPALS, formatRiskyThirdPartyServicePrincipals(riskyApps, riskySPs));
							} else {
								result.set(APPLICATIONS, objectMapper.createObjectNode());
								result.set(THIRD_PARTY_SERVICE_PRINCIPALS, objectMapper.createObjectNode());
							}

							return result;
						});
			} catch (Exception e) {
				logger.error("Failed to get risky permissions", e);
				throw new CompletionException(e);
			}
		}, "Get-RiskyPermissions");
	}

	/**
	 * Gets applications with risky permissions.
	 * Equivalent to PowerShell's Get-ApplicationsWithRiskyPermissions function.
	 */
	private CompletableFuture<JsonNode> getApplicationsWithRiskyPermissions(JsonNode riskyPermissionsJson) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/applications")
						.build())
				.thenCompose(applications -> {
					ArrayNode result = objectMapper.createArrayNode();

					if (applications.has(VALUE_FIELD) && applications.get(VALUE_FIELD).isArray()) {
						List<CompletableFuture<JsonNode>> futures = new ArrayList<>();

						for (JsonNode app : applications.get(VALUE_FIELD)) {
							futures.add(processApplication(app, riskyPermissionsJson));
						}

						return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
								.thenApply(v -> {
									for (CompletableFuture<JsonNode> future : futures) {
										JsonNode appNode = future.join();
										if (appNode != null) {
											result.add(appNode);
										}
									}
									return result;
								});
					}

					return CompletableFuture.completedFuture(result);
				});
	}


	/**
	 * Gets service principals with risky permissions.
	 * Equivalent to PowerShell's Get-ServicePrincipalsWithRiskyPermissions function.
	 */
	private CompletableFuture<JsonNode> getServicePrincipalsWithRiskyPermissions(JsonNode riskyPermissionsJson) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/servicePrincipals")
						.build())
				.thenCompose(servicePrincipals -> {
					List<CompletableFuture<JsonNode>> servicePrincipalFutures = new ArrayList<>();

					if (servicePrincipals.has(VALUE_FIELD) && servicePrincipals.get(VALUE_FIELD).isArray()) {
						for (JsonNode sp : servicePrincipals.get(VALUE_FIELD)) {
							servicePrincipalFutures.add(processServicePrincipal(sp, riskyPermissionsJson));
						}
					}

					return CompletableFuture.allOf(servicePrincipalFutures.toArray(new CompletableFuture[0]))
							.thenApply(v -> {
								ArrayNode result = objectMapper.createArrayNode();
								for (CompletableFuture<JsonNode> future : servicePrincipalFutures) {
									JsonNode spNode = future.join();
									if (spNode != null) {
										result.add(spNode);
									}
								}
								return result;
							});
				});
	}

	/**
	 * Formats risky applications by merging data from applications and service principals.
	 * Equivalent to PowerShell's Format-RiskyApplications function.
	 */
	private JsonNode formatRiskyApplications(JsonNode riskyApps, JsonNode riskySPs) {
		ArrayNode result = objectMapper.createArrayNode();

		for (JsonNode app : riskyApps) {
			JsonNode matchedSP = findServicePrincipalByAppId(riskySPs, app.get(APP_ID).asText());

			if (matchedSP != null) {
				updateAdminConsentStatus(app, matchedSP);
				ObjectNode mergedApp = createMergedApp(app, matchedSP);
				result.add(mergedApp);
			} else {
				result.add(app);
			}
		}

		return result;
	}


	private void updateAdminConsentStatus(JsonNode app, JsonNode matchedSP) {
		ArrayNode appPermissions = (ArrayNode) app.get(RISKY_PERMISSIONS_KEY);
		ArrayNode spPermissions = (ArrayNode) matchedSP.get(RISKY_PERMISSIONS_KEY);

		for (JsonNode permission : appPermissions) {
			String roleId = permission.get(ROLE_ID).asText();
			for (JsonNode spPermission : spPermissions) {
				if (spPermission.get(ROLE_ID).asText().equals(roleId)) {
					((ObjectNode) permission).put(IS_ADMIN_CONSENTED, true);
					break;
				}
			}
		}
	}

	private ObjectNode createMergedApp(JsonNode app, JsonNode matchedSP) {
		ObjectNode mergedApp = objectMapper.createObjectNode();

		ObjectNode objectIds = createObjectIds(app, matchedSP);
		mergedApp.set(OBJECT_ID, objectIds);
		mergedApp.put(APP_ID, app.get(APP_ID).asText());
		mergedApp.put(DISPLAY_NAME_FIELD, app.get(DISPLAY_NAME_FIELD).asText());
		mergedApp.put(IS_MULTI_TENANT_ENABLED, app.get(IS_MULTI_TENANT_ENABLED).asBoolean());

		mergedApp.set(KEY_CREDENTIALS, mergeCredentials(app.get(KEY_CREDENTIALS), matchedSP.get(KEY_CREDENTIALS)));
		mergedApp.set(PASSWORD_CREDENTIALS, mergeCredentials(app.get(PASSWORD_CREDENTIALS), matchedSP.get(PASSWORD_CREDENTIALS)));
		mergedApp.set(FEDERATED_CREDENTIALS, mergeCredentials(app.get(FEDERATED_CREDENTIALS), matchedSP.get(FEDERATED_CREDENTIALS)));

		mergedApp.set(RISKY_PERMISSIONS_KEY, app.get(RISKY_PERMISSIONS_KEY));

		return mergedApp;
	}


	private ObjectNode createObjectIds(JsonNode app, JsonNode matchedSP) {
		ObjectNode objectIds = objectMapper.createObjectNode();
		objectIds.put(EntraConfigurationService.APPLICATION, app.get(OBJECT_ID).asText());
		objectIds.put("ServicePrincipal", matchedSP.get(OBJECT_ID).asText());
		return objectIds;
	}


	/**
	 * Formats risky third-party service principals that don't have corresponding applications.
	 * Equivalent to PowerShell's Format-RiskyThirdPartyServicePrincipals function.
	 */
	private JsonNode formatRiskyThirdPartyServicePrincipals(JsonNode riskyApps, JsonNode riskySPs) {
		ArrayNode result = objectMapper.createArrayNode();

		for (JsonNode sp : riskySPs) {
			boolean foundMatch = false;
			for (JsonNode app : riskyApps) {
				if (app.get(APP_ID).asText().equals(sp.get(APP_ID).asText())) {
					foundMatch = true;
					break;
				}
			}

			if (!foundMatch) {
				result.add(sp);
			}
		}

		return result;
	}


	// Helper methods
	private CompletableFuture<JsonNode> processApplication(JsonNode app, JsonNode riskyPermissionsJson) {
		List<ObjectNode> riskyPermissions = findRiskyPermissions(app, riskyPermissionsJson);

		if (!riskyPermissions.isEmpty()) {
			ObjectNode appNode = objectMapper.createObjectNode();
			appNode.put(OBJECT_ID, app.get(ID_FIELD).asText());
			appNode.put(APP_ID, app.get(APP_ID).asText());
			appNode.put(DISPLAY_NAME_FIELD, app.get(DISPLAY_NAME_FIELD).asText());
			appNode.put(IS_MULTI_TENANT_ENABLED, app.has("signInAudience") &&
					app.get("signInAudience").asText().equals("AzureADMultipleOrgs"));

			// Format credentials
			appNode.set(KEY_CREDENTIALS, formatCredentials(app.path("keyCredentials"), true));
			appNode.set(PASSWORD_CREDENTIALS, formatCredentials(app.path("passwordCredentials"), true));

			// Get federated credentials
			return getFederatedCredentials(app.get(ID_FIELD).asText())
					.thenApply(fedCreds -> {
						appNode.set(FEDERATED_CREDENTIALS, fedCreds);

						ArrayNode permissionsArray = objectMapper.createArrayNode();
						riskyPermissions.forEach(permissionsArray::add);
						appNode.set(RISKY_PERMISSIONS_KEY, permissionsArray);

						return appNode;
					});
		}

		return CompletableFuture.completedFuture(null);
	}



	private CompletableFuture<JsonNode> processServicePrincipal(JsonNode sp, JsonNode riskyPermissionsJson) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/servicePrincipals/" + sp.get(ID_FIELD).asText() + "/appRoleAssignments")
						.build())
				.thenApply(appRoleAssignments -> {
					List<ObjectNode> riskyPermissions = new ArrayList<>();

					if (appRoleAssignments.has(VALUE_FIELD) && appRoleAssignments.get(VALUE_FIELD).isArray()) {
						for (JsonNode assignment : appRoleAssignments.get(VALUE_FIELD)) {
							String resourceDisplayName = assignment.get("resourceDisplayName").asText();
							String roleId = assignment.get(EntraConfigurationService.APP_ROLE_ID).asText();

							if (riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS).has(resourceDisplayName) &&
									riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS).get(resourceDisplayName).has(roleId)) {

								ObjectNode permission = objectMapper.createObjectNode();
								permission.put(ROLE_ID, roleId);
								permission.put(ROLE_DISPLAY_NAME, riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS)
										.get(resourceDisplayName).get(roleId).asText());
								permission.put(APPLICATION_DISPLAY_NAME, resourceDisplayName);
								permission.put(IS_ADMIN_CONSENTED, true); // appRoleAssignments are admin consented

								riskyPermissions.add(permission);
							}
						}
					}

					if (!riskyPermissions.isEmpty()) {
						ObjectNode spNode = objectMapper.createObjectNode();
						spNode.put(OBJECT_ID, sp.get(ID_FIELD).asText());
						spNode.put(APP_ID, sp.get(APP_ID).asText());
						spNode.put(DISPLAY_NAME_FIELD, sp.get(DISPLAY_NAME_FIELD).asText());

						spNode.set(KEY_CREDENTIALS, formatCredentials(sp.path("keyCredentials"), false));
						spNode.set(PASSWORD_CREDENTIALS, formatCredentials(sp.path("passwordCredentials"), false));

						ArrayNode permissionsArray = objectMapper.createArrayNode();
						riskyPermissions.forEach(permissionsArray::add);
						spNode.set(RISKY_PERMISSIONS_KEY, permissionsArray);

						return spNode;
					}

					return null;
				});
	}


	private void processResourceAccess(JsonNode resourceAccess, String resourceDisplayName, JsonNode riskyPermissionsJson, List<ObjectNode> riskyPermissions) {
		for (JsonNode access : resourceAccess) {
			if (access.has("type") && access.get("type").asText().equals("Role") && access.has(ID_FIELD)) {
				String roleId = access.get(ID_FIELD).asText();

				if (riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS).has(resourceDisplayName) &&
						riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS).get(resourceDisplayName).has(roleId)) {

					ObjectNode permission = createPermissionNode(roleId, resourceDisplayName, riskyPermissionsJson);
					riskyPermissions.add(permission);
				}
			}
		}
	}


	private ObjectNode createPermissionNode(String roleId, String resourceDisplayName, JsonNode riskyPermissionsJson) {
		ObjectNode permission = objectMapper.createObjectNode();
		permission.put(ROLE_ID, roleId);
		permission.put(ROLE_DISPLAY_NAME, riskyPermissionsJson.get(EntraConfigurationService.PERMISSIONS)
				.get(resourceDisplayName).get(roleId).asText());
		permission.put(APPLICATION_DISPLAY_NAME, resourceDisplayName);
		permission.put(IS_ADMIN_CONSENTED, false); // Will be updated later if needed
		return permission;
	}


	private JsonNode formatCredentials(JsonNode credentials, boolean isFromApplication) {
		ArrayNode result = objectMapper.createArrayNode();

		if (credentials != null && !credentials.isNull() && credentials.isArray()) {
			for (JsonNode cred : credentials) {
				if (cred.has("keyId") && cred.has(DISPLAY_NAME_FIELD) && cred.has("startDateTime") && cred.has("endDateTime")) {
					ObjectNode credential = objectMapper.createObjectNode();
					credential.put("KeyId", cred.get("keyId").asText());
					credential.put(DISPLAY_NAME_FIELD, cred.get(DISPLAY_NAME_FIELD).asText());
					credential.put("StartDateTime", cred.get("startDateTime").asText());
					credential.put("EndDateTime", cred.get("endDateTime").asText());
					credential.put("IsFromApplication", isFromApplication);

					result.add(credential);
				}
			}
		}

		return result.size() > 0 ? result : null;
	}


	private CompletableFuture<JsonNode> getFederatedCredentials(String appId) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/applications/" + appId + "/federatedIdentityCredentials")
						.build())
				.thenApply(this::processFederatedCredentials);
	}


	private JsonNode processFederatedCredentials(JsonNode credentials) {
		ArrayNode result = objectMapper.createArrayNode();

		if (credentials.has(VALUE_FIELD) && credentials.get(VALUE_FIELD).isArray()) {
			for (JsonNode cred : credentials.get(VALUE_FIELD)) {
				result.add(createFederatedCredential(cred));
			}
		}

		return result.size() > 0 ? result : null;
	}


	private ObjectNode createFederatedCredential(JsonNode cred) {
		ObjectNode federatedCredential = objectMapper.createObjectNode();
		federatedCredential.put(ID_FIELD, cred.get(ID_FIELD).asText());
		federatedCredential.put("Name", cred.get("name").asText());

		if (cred.has(DESCRIPTION_FIELD)) {
			federatedCredential.put("Description", cred.get(DESCRIPTION_FIELD).asText());
		}

		federatedCredential.put("Issuer", cred.get("issuer").asText());
		federatedCredential.put("Subject", cred.get("subject").asText());
		federatedCredential.put("Audiences", getAudiences(cred));

		return federatedCredential;
	}


	private String getAudiences(JsonNode cred) {
		StringBuilder audiences = new StringBuilder();
		if (cred.has(AUDIENCES) && cred.get(AUDIENCES).isArray()) {
			for (int i = 0; i < cred.get(AUDIENCES).size(); i++) {
				if (i > 0) audiences.append(", ");
				audiences.append(cred.get(AUDIENCES).get(i).asText());
			}
		}
		return audiences.toString();
	}


	private JsonNode findServicePrincipalByAppId(JsonNode servicePrincipals, String appId) {
		for (JsonNode sp : servicePrincipals) {
			if (sp.has(APP_ID) && sp.get(APP_ID).asText().equals(appId)) {
				return sp;
			}
		}
		return null;
	}


	private JsonNode mergeCredentials(JsonNode appCredentials, JsonNode spCredentials) {
		if (appCredentials == null && spCredentials == null) {
			return null;
		}

		ArrayNode merged = objectMapper.createArrayNode();

		if (appCredentials != null && appCredentials.isArray()) {
			for (JsonNode cred : appCredentials) {
				merged.add(cred);
			}
		}

		if (spCredentials != null && spCredentials.isArray()) {
			for (JsonNode cred : spCredentials) {
				merged.add(cred);
			}
		}

		return merged.size() > 0 ? merged : null;
	}





	private void processResource(JsonNode resource, JsonNode riskyPermissionsJson, List<ObjectNode> riskyPermissions) {
		if (resource.has("resourceAppId") && resource.has("resourceAccess")) {
			String resourceAppId = resource.get("resourceAppId").asText();

			if (riskyPermissionsJson.get("resources").has(resourceAppId)) {
				String resourceDisplayName = riskyPermissionsJson.get("resources").get(resourceAppId).asText();
				processResourceAccess(resource.get("resourceAccess"), resourceDisplayName, riskyPermissionsJson, riskyPermissions);
			}
		}
	}



	private List<ObjectNode> findRiskyPermissions(JsonNode app, JsonNode riskyPermissionsJson) {
		List<ObjectNode> riskyPermissions = new ArrayList<>();

		if (app.has(REQUIRED_RESOURCE_ACCESS) && app.get(REQUIRED_RESOURCE_ACCESS).isArray()) {
			for (JsonNode resource : app.get(REQUIRED_RESOURCE_ACCESS)) {
				processResource(resource, riskyPermissionsJson, riskyPermissions);
			}
		}

		return riskyPermissions;
	}



	/**
	 * Loads the RiskyPermissions.json file from resources.
	 */
	private JsonNode loadRiskyPermissionsJson() throws IOException {
		try (InputStream is = getClass().getClassLoader().getResourceAsStream("RiskyPermissions.json")) {
			if (is == null) {
				throw new IOException("RiskyPermissions.json not found in resources");
			}
			return objectMapper.readTree(is);
		}
	}

	/**
	 * Exports the risky permissions configuration.
	 * This method retrieves both risky applications and risky third-party service principals.
	 *
	 * @return A ConfigurationResult containing the risky permissions data
	 */
	@Override
	public ConfigurationResult exportConfiguration() {
		logger.info("Exporting risky permissions configuration");

		try {
			// Get risky permissions data
			CompletableFuture<JsonNode> riskyPermissionsFuture = getRiskyPermissions();

			// Build the result data map
			Map<String, CompletableFuture<?>> resultData = new HashMap<>();
			resultData.put(RISKY_PERMISSIONS, riskyPermissionsFuture);

			// Wait for all futures to complete and then build the result
			return waitForFutures(resultData)
					.thenApply(map -> {
						JsonNode riskyPermissions = (JsonNode) map.get(RISKY_PERMISSIONS);

						// Split the risky permissions into separate fields for applications and service principals
						if (riskyPermissions != null) {
							map.remove(RISKY_PERMISSIONS);

							if (riskyPermissions.has(APPLICATIONS)) {
								map.put(RISKY_APPLICATIONS, riskyPermissions.get(APPLICATIONS));
							}

							if (riskyPermissions.has(THIRD_PARTY_SERVICE_PRINCIPALS)) {
								map.put(RISKY_THIRD_PARTY_SERVICE_PRINCIPALS, riskyPermissions.get(THIRD_PARTY_SERVICE_PRINCIPALS));
							}
						}

						// Add successful and unsuccessful commands
						map.put(SUCCESSFUL_COMMANDS, getSuccessfulCommands());
						map.put(UNSUCCESSFUL_COMMANDS, getUnsuccessfulCommands());

						// Build and return the result
						return ConfigurationResult.builder()
								.withData(objectMapper.valueToTree(map))
								.withTimestamp(Instant.now())
								.withMetadata(buildMetadata(SERVICE_VERSION))
								.build();
					})
					.join();  // Wait for the final result

		} catch (Exception e) {
			logger.error("Failed to export risky permissions configuration", e);
			throw new ConfigurationExportException("Failed to export risky permissions", e);
		}
	}
}