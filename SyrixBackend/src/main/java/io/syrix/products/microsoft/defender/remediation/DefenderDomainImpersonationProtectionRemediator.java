package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.DEFENDER.2.2v1
 * Enables domain impersonation protection for agency domains in both Standard and Strict preset policies.
 */
@PolicyRemediator("MS.DEFENDER.2.2v1")
public class DefenderDomainImpersonationProtectionRemediator extends AbstractDomainImpersonationProtectionRemediator {

	/**
	 * Settings for enabling organization domain impersonation protection
	 */
	private static final Map<String, Object> DOMAIN_IMPERSONATION_SETTINGS = Map.of(
			ENABLE_ORG_DOMAINS_PROTECTION, true,
			IMPERSONATION_PROTECTION_STATE, "Automatic"
	);

	public DefenderDomainImpersonationProtectionRemediator(PowerShellClient powershellClient) {
		super(powershellClient);
	}

	@Override
	protected CompletableFuture<JsonNode> validateBeforeRemediation() {
		// No validation needed for organization domains
		return null;
	}

	@Override
	protected int getDomainsCount() {
		// We don't track specific domain count for organization domains
		return 0;
	}

	@Override
	protected String getRemediationType() {
		return "DomainImpersonation";
	}

	@Override
	protected boolean isDomainImpersonationAlreadyEnabled(JsonNode policySettings) {
		if (policySettings == null) {
			return false;
		}

		// Handle array result
		JsonNode policy = policySettings;
		if (policySettings.isArray() && policySettings.size() > 0) {
			policy = policySettings.get(0);
		}

		// Check if organization domains protection is enabled
		boolean orgDomainsProtectionEnabled = policy.path(ENABLE_ORG_DOMAINS_PROTECTION).asBoolean(false);

		// Check impersonation state - should be "Automatic" or "Manual" but not "Off"
		String impersonationState = policy.path(IMPERSONATION_PROTECTION_STATE).asText("");
		boolean impersonationEnabled = !"Off".equalsIgnoreCase(impersonationState);

		// Both settings need to be enabled for domain impersonation protection
		return orgDomainsProtectionEnabled && impersonationEnabled;
	}

	@Override
	protected CompletableFuture<JsonNode> configureDomainImpersonationProtection(String policyName) {
		logger.info("Configuring domain impersonation protection for policy: {}", policyName);

		// Create a mutable copy of the settings
		Map<String, Object> params = new HashMap<>(DOMAIN_IMPERSONATION_SETTINGS);

		// Add the policy Identity parameter
		params.put(Constants.IDENTITY_FIELD, policyName);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.SET_ANTIPHISHPOLICY_COMMAND,
						params
				));
	}
}