package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.*;

/**
 * Implements remediation for MS.AAD.2.3v1: Block access for high-risk sign-ins.

 * This remediation creates a Conditional Access Policy that blocks access
 * when a sign-in attempt is flagged as high risk by Microsoft Entra ID Protection.
 */
@PolicyRemediator("MS.AAD.2.3v1")
public class EntraIDHighRiskSignInRemediator extends RemediatorBase {
	private static final String POLICY_NAME = HIGH_RISK_SIGNIN_POLICY;

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final ConditionalAccessUserConfiguration userConfiguration;
	private final ConditionalPolisyState policyState;

	/**
	 * Constructor with custom user configuration.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 */
	public EntraIDHighRiskSignInRemediator(MicrosoftGraphClient graphClient,
										   ConditionalAccessUserConfiguration userConfiguration,
										   ConditionalPolisyState policyState) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.userConfiguration = userConfiguration;
		this.policyState = policyState;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for high-risk sign-in blocking policy using MS Graph REST API");

		return checkExistingPolicy()
				.thenCompose(exists -> {
					if (Boolean.TRUE.equals(exists)) {
						logger.info("High-risk sign-in blocking policy already exists. Skipping creation.");
						return CompletableFuture.completedFuture(
								IPolicyRemediator.success(getPolicyId(),
										"High-risk sign-in blocking policy already exists").join());
					}

					return createHighRiskSignInPolicy();
				})
				.exceptionally(ex -> {
					logger.error("Exception while creating high-risk sign-in blocking policy", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if a high-risk sign-in blocking policy already exists.
	 *
	 * @return CompletableFuture<Boolean> - true if policy exists, false otherwise
	 */
	private CompletableFuture<Boolean> checkExistingPolicy() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "displayName eq '" + POLICY_NAME + "'")
						.build()
		).thenApply(response ->
			response.has(Constants.VALUE_FIELD) && response.get(Constants.VALUE_FIELD).size() > 0
		);
	}

	private CompletableFuture<JsonNode> createHighRiskSignInPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.POST)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(CA_POLICIES_ENDPOINT)
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("High-risk sign-in blocking policy created successfully");
					return IPolicyRemediator.success(getPolicyId(),
							"High-risk sign-in blocking policy has been configured successfully");
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error creating high-risk sign-in blocking policy";
					logger.error("Failed to create high-risk sign-in blocking policy: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to create high-risk sign-in blocking policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();

		// Basic policy settings
		policyPayload.put(DISPLAY_NAME, POLICY_NAME);
		policyPayload.put(STATE_ENABLED, policyState.getState());

		// Create conditions object
		ObjectNode conditions = objectMapper.createObjectNode();

		// Sign-in risk levels
		ArrayNode signInRiskLevels = objectMapper.createArrayNode();
		signInRiskLevels.add(RISK_LEVEL_HIGH);
		conditions.set(SIGNIN_RISK_LEVELS, signInRiskLevels);

		// User risk levels (empty array as per the example)
		conditions.set(USER_RISK_LEVELS, objectMapper.createArrayNode());

		// Client app types
		ArrayNode clientAppTypes = objectMapper.createArrayNode();
		clientAppTypes.add(CLIENT_APP_TYPE_ALL);
		conditions.set(CLIENT_APP_TYPES, clientAppTypes);

		// Applications condition
		ObjectNode applications = objectMapper.createObjectNode();
		ArrayNode includeApps = objectMapper.createArrayNode();
		includeApps.add(ALL);
		applications.set("includeApplications", includeApps);
		applications.set("excludeApplications", objectMapper.createArrayNode());
		applications.set("includeUserActions", objectMapper.createArrayNode());
		applications.set("includeAuthenticationContextClassReferences", objectMapper.createArrayNode());
		applications.putNull("applicationFilter");
		conditions.set("applications", applications);

		// Users condition - use the provided userConfiguration
		conditions.set("users", userConfiguration.toJsonNode(objectMapper));

		// Set remaining condition fields to null
		conditions.putNull("platforms");
		conditions.putNull("locations");
		conditions.putNull("times");
		conditions.putNull("deviceStates");
		conditions.putNull("devices");
		conditions.putNull("clientApplications");

		policyPayload.set("conditions", conditions);

		// Grant controls
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", OPERATOR_OR);

		ArrayNode builtInControls = objectMapper.createArrayNode();
		builtInControls.add(BLOCK_ACTION);
		grantControls.set("builtInControls", builtInControls);

		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());
		grantControls.putNull("authenticationStrength");

		policyPayload.set("grantControls", grantControls);

		// Set session controls to null
		policyPayload.putNull("sessionControls");
		policyPayload.putNull("templateId");
		policyPayload.putNull("partialEnablementStrategy");

		return policyPayload;
	}

	/**
	 * Checks if a high-risk sign-in blocking policy exists and is properly configured.
	 */
	private CompletableFuture<JsonNode> fetchPolicies() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint(CA_POLICIES_ENDPOINT)
				.withMethod(HttpMethod.GET)
				.build());
	}

	private boolean isPolicyConfiguredCorrectly(JsonNode policy) {
		JsonNode conditions = policy.path("conditions");
		boolean hasHighRiskLevel = conditions.path(SIGNIN_RISK_LEVELS).toString().contains(RISK_LEVEL_HIGH);
		boolean hasCorrectControls = policy.path("grantControls").path("builtInControls").toString().contains(BLOCK_ACTION);
		return hasHighRiskLevel && hasCorrectControls;
	}

	private boolean checkPolicies(JsonNode response) {
		if (response.has(Constants.VALUE_FIELD) && response.get(Constants.VALUE_FIELD).isArray()) {
			for (JsonNode policy : response.get(Constants.VALUE_FIELD)) {
				if (POLICY_NAME.equals(policy.path("displayName").asText())) {
					boolean isConfiguredCorrectly = isPolicyConfiguredCorrectly(policy);
					logger.info("High-risk sign-in blocking policy is {} configured correctly",
							isConfiguredCorrectly ? "" : "not");
					return isConfiguredCorrectly;
				}
			}
		}
		logger.warn("No high-risk sign-in blocking policy found");
		return false;
	}

	public CompletableFuture<Boolean> checkHighRiskSignInPolicy() {
		return fetchPolicies()
				.thenApply(this::checkPolicies)
				.exceptionally(e -> {
					logger.error("Failed to check high-risk sign-in blocking policy: {}", e.getMessage(), e);
					return false;
				});
	}
}