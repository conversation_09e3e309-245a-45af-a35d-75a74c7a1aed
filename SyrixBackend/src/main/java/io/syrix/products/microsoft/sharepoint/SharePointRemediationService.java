package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.storage.Storage;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.main.Context;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.service.ServiceTypeRemediationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.CONFIG_KEY_SPO_TENANT;

public class SharePointRemediationService implements ServiceTypeRemediationService {
	private static final Logger logger = LoggerFactory.getLogger(SharePointRemediationService.class);
	private final Storage storage;
	private final ObjectMapper mapper;
	private final Context context;

	public SharePointRemediationService(Context context, Storage storage) {
		this.context = context;
		this.storage = storage;
		this.mapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	public CompletableFuture<JsonNode> remediate(RemediationTask task) {
		try {
			JsonNode jsonConfig = loadConfig(task);

			SharePointTenantProperties tenant = readSharepointTenantProperties(jsonConfig);

			List<PolicyChangeResult> result = new ArrayList<>();
			try (PowerShellSharepointClient client = initClient()) {
				List<IPolicyRemediator> iPolicyRemediators = makeRemediators(task, tenant, client);
				int ix = 0;
				for (IPolicyRemediator remediator : iPolicyRemediators) {
					PolicyChangeResult changeResult = remediator.remediate_().join();
					changeResult.executeOrder(ix++);
					result.add(changeResult);
				}
			}
			return CompletableFuture.completedFuture(mapper.valueToTree(result));
		} catch (IOException e) {
			return CompletableFuture.failedFuture(e);
		}

	}

	private List<IPolicyRemediator> makeRemediators(RemediationTask task, SharePointTenantProperties tenant, PowerShellSharepointClient client) throws IOException {
		//It may be necessary to add a special order
		SharepointRemediationConfig remediationConfig = task.getSpRemediationTask().getRemediationConfig();
		List<String> policyIds = task.getSpRemediationTask().getPolicyIds();

		return policyIds.stream()
				.map(policyId -> (IPolicyRemediator)PolicyRemediatorRegistry.getPolicyRemediator(policyId, client, tenant, remediationConfig))
				.toList();
	}

	private JsonNode loadConfig(Task task) throws IOException {
		Path configPath = storage.loadConfigPath(task);
		return mapper.readTree(configPath.toFile());
	}

	private PowerShellSharepointClient initClient() {
		String domain;
		String site;
		try (MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context)) {
			domain = graphClient.getDomain().join();
			site = graphClient.getSite();
		}

		if (domain == null || site == null) {
			logger.error("Domain or site not init");
			throw new SyrixRuntimeException("Domain or site not init");
		}


		String adminDomain = site.replace(".sharepoint.com", "-admin.sharepoint.com").replace("https://", "");

		return ClientFactory.initSharepointPowerShellClient(context, domain, adminDomain);
	}

	private SharePointTenantProperties readSharepointTenantProperties(JsonNode jsonConfig) throws IOException {
		JsonNode policiesNode = jsonConfig.get(CONFIG_KEY_SPO_TENANT);

		if (policiesNode == null || !policiesNode.isArray()) {
			throw new IOException("Can not found key 'teams_tenant_info'");
		}
		CollectionType collectionType = mapper.getTypeFactory().constructCollectionType(List.class, SharePointTenantProperties.class);
		List<SharePointTenantProperties> tenantProperties = mapper.convertValue(policiesNode, collectionType);

		return tenantProperties.getFirst();
	}

}
