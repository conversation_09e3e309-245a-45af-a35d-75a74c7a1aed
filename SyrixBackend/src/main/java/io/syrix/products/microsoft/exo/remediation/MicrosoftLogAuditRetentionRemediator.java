package io.syrix.products.microsoft.exo.remediation;

import io.syrix.products.microsoft.base.CommonAuditRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.PowerShellClient;

/**
 * Microsoft Purview Audit Logging and Retention Remediator for Exchange Online
 * <p>
 * Implements three controls for Microsoft Purview Audit:
 * <ul>
 *     <li>MS.EXO.17.1v1: Enable Microsoft Purview Audit (Standard)</li>
 *     <li>MS.EXO.17.2v1: Enable Microsoft Purview Audit (Premium)</li>
 *     <li>MS.EXO.17.3v1: Configure Audit Log Retention for OMB M-21-31 compliance</li>
 * </ul>
 * <p>
 * Ensures audit logs are retained for 12 months (active storage) as required by OMB M-21-31.
 * Note: The 18-month cold storage is recommended to be implemented via log export to external
 * storage and is not handled by this remediator.
 */
@PolicyRemediator("MS.EXO.17.1v1, MS.EXO.17.2v1, MS.EXO.17.3v1")
public class MicrosoftLogAuditRetentionRemediator extends CommonAuditRemediator {

	/**
	 * Constructs a new MicrosoftLogAuditRetentionRemediator with the specified PowerShell client.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 */
	public MicrosoftLogAuditRetentionRemediator(PowerShellClient exchangeClient) {
		super(exchangeClient);
	}
}