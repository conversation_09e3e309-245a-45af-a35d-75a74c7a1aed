package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.13.1v1 that ensures mailbox auditing is enabled organization-wide.
 * <p>
 * This class implements the following security controls:
 * - Enables organization-wide mailbox auditing to track critical actions
 * - Verifies the current audit configuration before applying changes
 * - Validates that the configuration was successfully applied
 */
@PolicyRemediator("MS.EXO.13.1v1")
public class ExchangeMailboxAuditRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	public ExchangeMailboxAuditRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeMailboxAuditRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (Mailbox Audit)", getPolicyId());

		OrganizationConfig orgConfig = loadOrganizationConfig();
		if (orgConfig == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_ORGANIZATION_CONFIG));
		}

		if (orgConfig.auditDisabled) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return updateConfiguration(false, true);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (Mailbox Audit)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			ParameterChangeResult change = changes.getFirst();
			if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
				logger.error("Rollback skipped for policy: {}", getPolicyId());
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback skipped", changes));
			}

			boolean prevValue = Boolean.parseBoolean(change.getPrevValue().toString());
			boolean newValue = Boolean.parseBoolean(change.getNewValue().toString());

			return updateConfiguration(prevValue, newValue);
		} catch (Exception ex) {
			logger.error("Rollback failed for policy {}", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> updateConfiguration(boolean newValue, boolean prevValue) {
		logger.info("Updating mailbox audit configuration to {}", newValue);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.AUDIT_DISABLED)
				.prevValue(prevValue)
				.newValue(newValue);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(ExoConstants.AUDIT_DISABLED, newValue);
		parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, parameters))
				.thenApply(result -> {
					logger.info("Successfully updated mailbox audit configuration");
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully updated mailbox audit configuration", List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.error("Failed to update mailbox audit configuration", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update mailbox audit configuration: " + ex.getMessage(), List.of(paramChange));
				});
	}
} 