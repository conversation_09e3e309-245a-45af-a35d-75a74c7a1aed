package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.utils.ProtocolConstants.AUTHORIZATION_POLICY;

/**
 * Implements remediation for MS.AAD.5.1v1: Only administrators SHALL be allowed to register applications.
 */
@PolicyRemediator("MS.AAD.5.1v1")
public class EntraIDAppRegistrationRemediator extends RemediatorBase {
	//private static final String BETA_AUTH_POLICY_ENDPOINT = "/policies/authorizationPolicy/authorizationPolicy";
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDAppRegistrationRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for application registration settings using MS Graph REST API");

		return updateAppRegistrationPolicy()
				.exceptionally(ex -> {
					logger.error("Exception while configuring application registration settings", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> updateAppRegistrationPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(AUTHORIZATION_POLICY)
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Application registration settings updated successfully");
					return IPolicyRemediator.success(getPolicyId(), "Application registration restricted to administrators only");
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error updating application registration policy";
					logger.error("Failed to update application registration policy: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to update application registration policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		ObjectNode permissions = objectMapper.createObjectNode();
		permissions.put("allowedToCreateApps", false);
		policyPayload.set("defaultUserRolePermissions", permissions);
		return policyPayload;
	}
}