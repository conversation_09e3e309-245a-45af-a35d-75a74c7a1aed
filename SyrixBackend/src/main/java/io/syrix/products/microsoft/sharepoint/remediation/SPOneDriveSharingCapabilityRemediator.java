package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingCapability;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.ODB_SHARING_CAPABILITY_PROPERTY;

@PolicyRemediator("MS.SHAREPOINT.1.2v1")
public class SPOneDriveSharingCapabilityRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
    private static final Logger log = LoggerFactory.getLogger(SPOneDriveSharingCapabilityRemediator.class);

    private final SharingCapability capability;

    public SPOneDriveSharingCapabilityRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
        super(client, tenant, spConfig);
        this.capability = SharingCapability.fromInt(spConfig.odbSharingCapability.asInt());
        if (!SharingCapability.DISABLE.equals(capability) && !SharingCapability.EXTERNAL_USER_SHARING_ONLY.equals(capability) ) {
            throw new IllegalArgumentException("Capability should be DISABLE or EXTERNAL_USER_SHARING_ONLY");
        }
    }

    // constructor for Rollback interface
    public SPOneDriveSharingCapabilityRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        super(client, tenant, null);
        this.capability = null;
    }

    private CompletableFuture<PolicyChangeResult> runCommand(SharingCapability value, SharingCapability prevValue) {
        try {

            SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, ODB_SHARING_CAPABILITY_PROPERTY, value, prevValue);
            return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
                    .thenApply(this::checkResult);
        } catch (Exception ex) {
            log.error("Run command for the policy {} failed", getPolicyId(), ex);
            return CompletableFuture.failedFuture(ex);
        }
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        SharingCapability prevValue = SharingCapability.fromInt(tenant.oneDriveSharingCapability);
        return runCommand(capability, prevValue)
                .exceptionally(ex -> {
                    log.error("Remediate the policy {} finished with exception", getPolicyId(), ex);
                    return IPolicyRemediator.failed_(getPolicyId(), "Remediate the policy finished with exception:" + ex.getMessage());
                });
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        try {
            ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
            SharingCapability newValue = SharingCapability.valueOf(changeResult.getPrevValue().toString());
            SharingCapability prevValue = SharingCapability.valueOf(changeResult.getNewValue().toString());

            return runCommand(newValue, prevValue)
                    .exceptionally(ex -> {
                        log.error("Rollback the policy {} finished with exception", getPolicyId(), ex);
                        return IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy failed");
                    });
        } catch (Exception ex) {
            log.error("Rollback the policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }
}
