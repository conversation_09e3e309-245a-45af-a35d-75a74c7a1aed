package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService.IS_ENABLED;

/**
 * Remediator for MS.EXO.19.1v1 that ensures Customer Lockbox is enabled organization-wide.
 * <p>
 * This class implements the security requirement for enabling Customer Lockbox
 * to require approval for all data access requests.
 * <p>
 * Customer Lockbox provides:
 * - Explicit approval process for Microsoft engineers accessing tenant data
 * - Transparency into when, why, and who accesses data
 * - Compliance auditing and control over Microsoft support access
 * <p>
 * Customer Lockbox requires a Microsoft 365 E5 or Office 365 E5 license, or can be purchased separately.
 */
//@PolicyRemediator("MS.EXO.19.1v1")
public class ExchangeCustomerLockboxRemediator extends RemediatorBase { //TODO : Not yet fully implemented, at this stage need to be done manually
	public static final String FAILED_TO_EXTRACT_ORGANIZATION_CONFIG_FROM_RESPONSE = "Failed to extract organization config from response";
	private final ObjectMapper mapper = new ObjectMapper();
	private final PowerShellClient exchangeClient;
	private final ExchangeOnlineConfigurationService configurationService;

	/**
	 * Constructs a new ExchangeCustomerLockboxRemediator with the specified PowerShell client.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 * @param metrics
	 */
	public ExchangeCustomerLockboxRemediator(PowerShellClient exchangeClient, MicrosoftGraphClient graphClient, MetricsCollector metrics) {
		this.exchangeClient = exchangeClient;
		this.configurationService = new ExchangeOnlineConfigurationService(graphClient, exchangeClient, this.mapper, metrics, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting Customer Lockbox remediation for MS.EXO.19.1v1");

		// First check the current Customer Lockbox configuration
		return configurationService.auditCustomerLockbox(configurationService.getOrganizationConfig())
				.thenCompose(this::evaluateAndUpdateConfiguration)
				.exceptionally(ex -> {
					logger.error("Exception during Customer Lockbox remediation", ex);
					return createFailureNode(mapper, ex.getMessage() != null ? ex.getMessage() : ExoConstants.UNKNOWN_ERROR);
				});
	}

	/**
	 * Evaluates the current configuration and updates it if needed.
	 *
	 * @param config The current organization configuration
	 * @return CompletableFuture containing the remediation result
	 */
	private CompletableFuture<JsonNode> evaluateAndUpdateConfiguration(JsonNode config) {
		try {
			// Check if Customer Lockbox is already enabled
			if (config.has(IS_ENABLED)) {
				boolean lockboxEnabled = config.get(IS_ENABLED).asBoolean();
				if (lockboxEnabled) {
					logger.info("Customer Lockbox is already enabled for the organization");
					return CompletableFuture.completedFuture(createSuccessNode(mapper, ExoConstants.SUCCESS_CUSTOMER_LOCKBOX_ALREADY_ENABLED));
				}
			}

			// If we reach here, we need to enable Customer Lockbox
			logger.info("Customer Lockbox is currently disabled. Enabling...");
			return enableCustomerLockbox();
		} catch (Exception e) {
			logger.error("Error while evaluating configuration", e);
			return CompletableFuture.completedFuture(createFailureNode(mapper, "Error evaluating configuration: " + e.getMessage()));
		}
	}

	/**
	 * Enables Customer Lockbox by setting CustomerLockBoxEnabled to true.
	 *
	 * @return CompletableFuture containing the remediation result
	 */
	private CompletableFuture<JsonNode> enableCustomerLockbox() {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(ExoConstants.CUSTOMER_LOCKBOX_ENABLED, true);  // Use Java boolean, not PowerShell string
		parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

		PowerShellClient.CommandRequest request = new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, parameters);

		return exchangeClient.executeCmdletCommand(request)
				.thenCompose(result -> {
					// Set-OrganizationConfig typically returns empty when successful
					// or error details when failed
					if (result == null || (result.isArray() && result.size() == 0) || (result.isObject() && result.isEmpty())) {
						logger.info("Set-OrganizationConfig command executed successfully");
						return configurationService.auditCustomerLockbox(configurationService.getOrganizationConfig());
					} else if (result.has(ExoConstants.ERROR_FIELD)) {
						String error = result.get(ExoConstants.ERROR_FIELD).asText();
						logger.error("Failed to enable Customer Lockbox: {}", error);
						return CompletableFuture.completedFuture(createFailureNode(mapper, error));
					} else {
						// Assume success if we get a response that doesn't indicate error
						logger.info("Set-OrganizationConfig command completed, validating changes...");
						return configurationService.auditCustomerLockbox(configurationService.getOrganizationConfig());
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during enableCustomerLockbox", ex);
					return createFailureNode(mapper, "Failed to enable Customer Lockbox: " + ex.getMessage());
				});
	}
}