package io.syrix.products.microsoft.teams.model;

import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;

import java.util.List;

@SuppressWarnings({"CommentedOutCode", "unused"})
public class TeamAppPermissionPolicy {
//    public String configId;
//    public ConfigMetadata configMetadata;
//    public String dataSource;
    public List<String> defaultCatalogApps;
    public String defaultCatalogAppsType;
    public String description;
    public List<String> globalCatalogApps;
    public String globalCatalogAppsType;
    public String identity;
//    public Key key;
    public List<String> privateCatalogApps;
    public String privateCatalogAppsType;

//    public static class ConfigMetadata {
//        public String authority;
//
//        public ConfigMetadata() {}
//        public ConfigMetadata(AppPermissionPolicy.ConfigMetadata configMetadata) {
//            if (configMetadata == null) return;
//            this.authority = configMetadata.authority;
//        }
//    }

//    public static class Key {
//        public AuthorityId authorityId;
//        public DefaultXml defaultXml;
//        public SchemaId schemaId;
//        public String scopeClass;
//        public XmlRoot xmlRoot;
//
//        public Key(){} //for serialization from json
//        public Key(AppPermissionPolicy.Key key) {
//            if (key == null) return;
//
//            this.authorityId = key.authorityId == null ? null : new AuthorityId(key.authorityId);
//            this.defaultXml = key.defaultXml == null ? null : new DefaultXml(key.defaultXml);
//            this.schemaId = key.schemaId == null ? null : new SchemaId(key.schemaId);
//            this.scopeClass = key.scopeClass;
//            this.xmlRoot = key.xmlRoot == null ? null : new XmlRoot(key.xmlRoot);
//        }
//    }

//    public static class AuthorityId {
//        @JsonProperty(value = "Class")
//        public String classType;
//        public String instanceId;
//        public XmlRoot xmlRoot;
//
//        public AuthorityId() {} //for serialization from json
//        public AuthorityId(AppPermissionPolicy.AuthorityId authorityId) {
//            if (authorityId == null) return;
//            this.classType = authorityId.classType;
//            this.instanceId = authorityId.instanceId;
//            this.xmlRoot = authorityId.xmlRoot == null ? null : new XmlRoot(authorityId.xmlRoot);
//        }
//    }
//
//    public static class DefaultXml {
//        public Object configObject;
//        public Data data;
//        public Boolean isModified;
//        public SchemaId schemaId;
//        public String signature;
//
//        public DefaultXml() { } //for serialization from json
//        public DefaultXml(AppPermissionPolicy.DefaultXml defaultXml) {
//            if (defaultXml == null) return;
//
//            this.configObject = defaultXml.configObject;
//            this.data = defaultXml.data == null ? null : new Data(defaultXml.data);
//            this.isModified = defaultXml.isModified;
//            this.schemaId = defaultXml.schemaId == null ? null : new SchemaId(defaultXml.schemaId);
//            this.signature = defaultXml.signature;
//        }
//    }
//
//    public static class Data {
//        public TeamsAppPermissionPolicyData teamsAppPermissionPolicy;
//
//        public Data() {}//for serialization from json
//        public Data(AppPermissionPolicy.Data data) {
//            if (data == null) return;
//            this.teamsAppPermissionPolicy = data.teamsAppPermissionPolicy == null ? null : new TeamsAppPermissionPolicyData(data.teamsAppPermissionPolicy);
//        }
//    }
//
//    public static class TeamsAppPermissionPolicyData {
//        @JsonProperty(value = "@xmlns")
//        public String xmlns;
//        public List<String> defaultCatalogApps;
//        public List<String> globalCatalogApps;
//        public List<String> privateCatalogApps;
//
//        public TeamsAppPermissionPolicyData() {}//for serialization from json
//        public TeamsAppPermissionPolicyData(AppPermissionPolicy.TeamsAppPermissionPolicyData data) {
//            if (data == null) return;
//
//            this.defaultCatalogApps = data.defaultCatalogApps == null ? null : new ArrayList<>(data.defaultCatalogApps);
//            this.globalCatalogApps = data.globalCatalogApps == null ? null : new ArrayList<>(data.globalCatalogApps);
//            this.privateCatalogApps = data.privateCatalogApps == null ? null : new ArrayList<>(data.privateCatalogApps);
//        }
//    }
//
//    public static class SchemaId {
//        public XName xName;
//
//        public SchemaId() {} //for serialization from json
//        public SchemaId(AppPermissionPolicy.SchemaId schemaId) {
//            if (schemaId == null) return;
//            this.xName = schemaId.xName == null ? null : new XName(schemaId.xName);
//        }
//    }
//
//    public static class XName {
//        public String name;
//
//        public XName() {}//for serialization from json
//        public XName(AppPermissionPolicy.XName xName) {
//            if (xName == null) return;
//            this.name = xName.name;
//        }
//    }
//
//    public static class XmlRoot {
//        public String name;
//
//        public XmlRoot() {} //for serialization from json
//        public XmlRoot(AppPermissionPolicy.XmlRoot xmlRoot) {
//            if (xmlRoot == null) return;
//            this.name = xmlRoot.name;
//        }
//    }

    public TeamAppPermissionPolicy() {}
    public TeamAppPermissionPolicy(AppPermissionPolicy permissionPolicy) {
//        this.configId = permissionPolicy.configId;
//        this.configMetadata = permissionPolicy.configMetadata == null ? null : new ConfigMetadata(permissionPolicy.configMetadata);
//        this.dataSource = permissionPolicy.dataSource;
        this.defaultCatalogApps = permissionPolicy.defaultCatalogApps;
        this.defaultCatalogAppsType = permissionPolicy.defaultCatalogAppsType == null ? null : permissionPolicy.defaultCatalogAppsType.asString();
        this.description = permissionPolicy.description;
        this.globalCatalogApps = permissionPolicy.globalCatalogApps;
        this.globalCatalogAppsType = permissionPolicy.globalCatalogAppsType == null ? null : permissionPolicy.globalCatalogAppsType.asString();
        this.identity = permissionPolicy.identity;
//        this.key = permissionPolicy.key == null ? null : new Key(permissionPolicy.key);
        this.privateCatalogApps = permissionPolicy.privateCatalogApps;
        this.privateCatalogAppsType = permissionPolicy.privateCatalogAppsType == null ? null : permissionPolicy.privateCatalogAppsType.asString();
    }

}

