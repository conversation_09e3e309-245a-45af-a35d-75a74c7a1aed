package io.syrix.products.microsoft.exo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.storage.Storage;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.datamodel.task.Task;
import io.syrix.domain.RollbackTask;
import io.syrix.main.Context;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.service.RollbackService;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class ExchangeRollbackService implements RollbackService {
	private static final Logger logger = LoggerFactory.getLogger(ExchangeRollbackService.class);
	private final Storage storage;
	private final ObjectMapper mapper;
	private final Context context;

	public ExchangeRollbackService(Context context, Storage storage) {
		this.context = context;
		this.storage = storage;
		this.mapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	@Override
	public CompletableFuture<JsonNode> rollback(RollbackTask task) {
		try {
			List<PolicyChangeResult> remediationResult = loadRemediationResult(task);

			List<PolicyChangeResult> resList = new ArrayList<>();
			try (MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context);
				 PowerShellClient client = initClient(graphClient)) {
				List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> iPolicyRollback = makeRollbacks(graphClient, client, remediationResult);
				int ix = 0;
				for (Pair<IPolicyRemediatorRollback, PolicyChangeResult> pair : iPolicyRollback) {
					PolicyChangeResult res = runRollback(pair);
					res.executeOrder(ix++);
					resList.add(res);
				}
			}
			return CompletableFuture.completedFuture(mapper.valueToTree(resList));
		} catch (IOException e) {
			return CompletableFuture.failedFuture(e);
		}
	}

	private PolicyChangeResult runRollback(Pair<IPolicyRemediatorRollback, PolicyChangeResult> pair) {
		IPolicyRemediatorRollback remediatorRollback = pair.getKey();
		PolicyChangeResult policyChangeResult = pair.getValue();
		try {
			return remediatorRollback.rollback(policyChangeResult).get();
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", policyChangeResult.getPolicyId(), ex);
			return IPolicyRemediator.failed_(policyChangeResult.getPolicyId(), "Rollback the policy failed:" + ex.getMessage());
		}
	}

	private List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> makeRollbacks(MicrosoftGraphClient graphClient , PowerShellClient client, List<PolicyChangeResult> remediationResult) throws IOException {
		Map<String, PolicyChangeResult> changeResultMap = remediationResult.stream()
				.filter(res -> res.getResult() == RemediationResult.SUCCESS || res.getResult() == RemediationResult.PARTIAL_SUCCESS)
				.collect(Collectors.toMap(PolicyChangeResult::getPolicyId, res -> res));

		List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> remediators = new ArrayList<>();

		List<String> policyIds = changeResultMap.keySet().stream().sorted(Comparator.reverseOrder()).toList();

		for (String policyId : policyIds) {
			IPolicyRemediatorRollback rollback = PolicyRemediatorRegistry.getPolicyRemediator(policyId, graphClient, client);
			remediators.add(Pair.of(rollback, changeResultMap.get(policyId)));
		}

		return remediators;
	}

	private List<PolicyChangeResult> loadRemediationResult(Task task) throws IOException {
		Path resPath = storage.loadRemediationResult(task);

		JsonNode node = mapper.readTree(resPath.toFile());
		JsonNode exoNode = node.get(ConfigurationServiceType.EXCHANGE_ONLINE.getResultName());

		CollectionType collectionType = mapper.getTypeFactory().constructCollectionType(List.class, PolicyChangeResult.class);

		return mapper.treeToValue(exoNode, collectionType);
	}

	private PowerShellClient initClient(MicrosoftGraphClient graphClient) {
		String tenantId = graphClient.getTenantId();
		if (tenantId == null) {
			logger.error("Tenant ID not initialized");
			throw new SyrixRuntimeException("Tenant ID not initialized");
		}

		return ClientFactory.initPowerShellClient(context, tenantId, "Exchange");
	}
} 