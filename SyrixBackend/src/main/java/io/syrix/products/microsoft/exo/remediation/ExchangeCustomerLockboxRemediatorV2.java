package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.19.1v1 that ensures Customer Lockbox is enabled organization-wide.
 * <p>
 * This class implements the security requirement for enabling Customer Lockbox
 * to require approval for all data access requests.
 * <p>
 * Customer Lockbox provides:
 * - Explicit approval process for Microsoft engineers accessing tenant data
 * - Transparency into when, why, and who accesses data
 * - Compliance auditing and control over Microsoft support access
 * <p>
 * Customer Lockbox requires a Microsoft 365 E5 or Office 365 E5 license, or can be purchased separately.
 */
//TODO : Not yet fully implemented, at this stage need to be done manually
@SuppressWarnings("unused")
@PolicyRemediator("MS.EXO.19.1v1")
public class ExchangeCustomerLockboxRemediatorV2 extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

    private static final String UNKNOWN_ERROR = "Unknown error";

    public ExchangeCustomerLockboxRemediatorV2(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
    }

    // Constructor for Rollback interface
    public ExchangeCustomerLockboxRemediatorV2(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
        this(graphClient, exchangeClient, null, null, null);
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(jsonMapper::valueToTree);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        logger.info("Starting remediation for {} (Customer Lockbox)", getPolicyId());

        OrganizationConfig orgConfig = loadOrganizationConfig();
        if (orgConfig == null) {
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_ORGANIZATION_CONFIG));
        }

        if (orgConfig.customerLockboxEnabled) {
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
        }

        return updateConfiguration(false, true);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        logger.info("Starting rollback for {} (Customer Lockbox)", getPolicyId());
        try {
            List<ParameterChangeResult> changes = fixResult.getChanges();
            if (changes == null || changes.isEmpty()) {
                return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
            }

            ParameterChangeResult change = changes.getFirst();
            if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
                logger.error("Rollback skipped for policy: {}", getPolicyId());
                return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback skipped", changes));
            }

            boolean prevValue = Boolean.parseBoolean(change.getPrevValue().toString());
            boolean newValue = Boolean.parseBoolean(change.getNewValue().toString());

            return updateConfiguration(prevValue, newValue);
        } catch (Exception ex) {
            logger.error("Rollback failed for policy {}", getPolicyId(), ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }

    private CompletableFuture<PolicyChangeResult> updateConfiguration(boolean newValue, boolean prevValue) {
        logger.info("Updating Customer Lockbox configuration to {}", newValue);

        ParameterChangeResult paramChange = new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(ExoConstants.CUSTOMER_LOCKBOX_ENABLED)
                .prevValue(prevValue)
                .newValue(newValue);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(ExoConstants.CUSTOMER_LOCKBOX_ENABLED, newValue);
        parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

        return exchangeClient.executeCmdletCommand(
                        new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, parameters)
                )
                .thenApply(result -> {
                    if (result != null && !result.has(Constants.ERROR_FIELD)) {
                        logger.info("Successfully updated Customer Lockbox configuration");
                        paramChange.status(ParameterChangeStatus.SUCCESS);
                        return IPolicyRemediator.success_(getPolicyId(), "Successfully updated Customer Lockbox configuration", List.of(paramChange));
                    } else {
                        String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
                        logger.error("Failed to update mailbox audit configuration: {}", error);
                        paramChange.status(ParameterChangeStatus.FAILED);
                        return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Customer Lockbox configuration: " + error, List.of(paramChange));
                    }
                })
                .exceptionally(ex -> {
                    logger.error("Failed to update Customer Lockbox configuration", ex);
                    paramChange.status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Customer Lockbox configuration: " + ex.getMessage(), List.of(paramChange));
                });
    }
} 