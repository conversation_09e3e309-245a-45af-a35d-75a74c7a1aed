package io.syrix.products.microsoft.entra.model.reccomendations;

import java.time.LocalDateTime;
import java.util.List;

// Impacted resource class
public class ImpactedResource {
	private String id;
	private String subjectId;
	private String recommendationId;
	private String resourceType;
	private LocalDateTime addedDateTime;
	private LocalDateTime postponeUntilDateTime;
	private LocalDateTime lastModifiedDateTime;
	private String lastModifiedBy;
	private String displayName;
	private String owner;
	private int rank;
	private String portalUrl;
	private String apiUrl;
	private RecommendationStatus status;
	private List<KeyValue> additionalDetails;

	// Getters and setters
	public String getId() { return id; }
	public void setId(String id) { this.id = id; }

	public String getSubjectId() {
		return subjectId;
	}

	public void setSubjectId(String subjectId) {
		this.subjectId = subjectId;
	}

	public String getRecommendationId() {
		return recommendationId;
	}

	public void setRecommendationId(String recommendationId) {
		this.recommendationId = recommendationId;
	}

	public String getResourceType() {
		return resourceType;
	}

	public void setResourceType(String resourceType) {
		this.resourceType = resourceType;
	}

	public LocalDateTime getAddedDateTime() {
		return addedDateTime;
	}

	public void setAddedDateTime(LocalDateTime addedDateTime) {
		this.addedDateTime = addedDateTime;
	}

	public LocalDateTime getPostponeUntilDateTime() {
		return postponeUntilDateTime;
	}

	public void setPostponeUntilDateTime(LocalDateTime postponeUntilDateTime) {
		this.postponeUntilDateTime = postponeUntilDateTime;
	}

	public LocalDateTime getLastModifiedDateTime() {
		return lastModifiedDateTime;
	}

	public void setLastModifiedDateTime(LocalDateTime lastModifiedDateTime) {
		this.lastModifiedDateTime = lastModifiedDateTime;
	}

	public String getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(String lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public String getPortalUrl() {
		return portalUrl;
	}

	public void setPortalUrl(String portalUrl) {
		this.portalUrl = portalUrl;
	}

	public String getApiUrl() {
		return apiUrl;
	}

	public void setApiUrl(String apiUrl) {
		this.apiUrl = apiUrl;
	}

	public RecommendationStatus getStatus() {
		return status;
	}

	public void setStatus(RecommendationStatus status) {
		this.status = status;
	}

	public List<KeyValue> getAdditionalDetails() {
		return additionalDetails;
	}

	public void setAdditionalDetails(List<KeyValue> additionalDetails) {
		this.additionalDetails = additionalDetails;
	}
}