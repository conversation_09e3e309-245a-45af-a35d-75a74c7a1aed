package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Represents a privileged user in Microsoft Entra ID (formerly Azure AD).
 * This class is thread-safe and designed for concurrent access in multi-threaded environments.
 * It maintains a set of roles assigned to the user and provides synchronized access to this data.
 */
public class PrivilegedUser {
	// Unique identifier for the user in Entra ID
	@JsonIgnore
	private final String id;

	// Display name of the user as shown in Entra ID
	private final String displayName;

	// Immutable ID used for hybrid environments with on-premises Active Directory
	private final String onPremisesImmutableId;

	// Set of role names assigned to this user, using ConcurrentHashMap for thread safety
	private final Set<String> roles;

	// Timestamp of when this user object was last updated
	private final Instant lastUpdated;

	// Lock for synchronizing access to mutable state
	@JsonIgnore  // Prevents the lock from being serialized
	private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

	/**
	 * Creates a new privileged user instance.
	 *
	 * @param id The unique identifier of the user in Entra ID
	 * @param displayName The user's display name
	 * @param onPremisesImmutableId The user's on-premises immutable ID (can be null)
	 */
	public PrivilegedUser(String id, String displayName, String onPremisesImmutableId) {
		if (id == null || id.trim().isEmpty()) {
			throw new IllegalArgumentException("User ID cannot be null or empty");
		}
		if (displayName == null || displayName.trim().isEmpty()) {
			throw new IllegalArgumentException("Display name cannot be null or empty");
		}

		this.id = id;
		this.displayName = displayName;
		this.onPremisesImmutableId = onPremisesImmutableId;
		// Using ConcurrentHashMap.newKeySet() for a thread-safe Set implementation
		this.roles = ConcurrentHashMap.newKeySet();
		this.lastUpdated = Instant.now();
	}

	/**
	 * Adds a role to the user's set of roles.
	 * This operation is thread-safe.
	 *
	 * @param role The role name to add
	 * @throws IllegalArgumentException if the role is null or empty
	 */
	public void addRole(String role) {
		if (role == null || role.trim().isEmpty()) {
			throw new IllegalArgumentException("Role cannot be null or empty");
		}

		lock.writeLock().lock();
		try {
			roles.add(role.trim());
		} finally {
			lock.writeLock().unlock();
		}
	}

	/**
	 * Removes a role from the user's set of roles.
	 * This operation is thread-safe.
	 *
	 * @param role The role name to remove
	 * @return true if the role was present and removed, false otherwise
	 */
	public boolean removeRole(String role) {
		if (role == null || role.trim().isEmpty()) {
			return false;
		}

		lock.writeLock().lock();
		try {
			return roles.remove(role.trim());
		} finally {
			lock.writeLock().unlock();
		}
	}

	/**
	 * Checks if the user has a specific role.
	 * This operation is thread-safe.
	 *
	 * @param role The role name to check
	 * @return true if the user has the role, false otherwise
	 */
	public boolean hasRole(String role) {
		if (role == null || role.trim().isEmpty()) {
			return false;
		}

		lock.readLock().lock();
		try {
			return roles.contains(role.trim());
		} finally {
			lock.readLock().unlock();
		}
	}

	/**
	 * Returns an unmodifiable copy of the user's roles.
	 * This operation is thread-safe and prevents external modification of the roles set.
	 *
	 * @return An unmodifiable Set containing the user's roles
	 */
	@JsonProperty("roles")
	public Set<String> getRoles() {
		lock.readLock().lock();
		try {
			return Collections.unmodifiableSet(Set.copyOf(roles));
		} finally {
			lock.readLock().unlock();
		}
	}

	/**
	 * Returns the number of roles assigned to the user.
	 * This operation is thread-safe.
	 *
	 * @return The number of roles
	 */
	@JsonIgnore
	public int getRoleCount() {
		lock.readLock().lock();
		try {
			return roles.size();
		} finally {
			lock.readLock().unlock();
		}
	}

	// Getters for immutable fields don't need synchronization
	//@JsonProperty("id")
	public String getId() {
		return id;
	}

	@JsonProperty("displayName")
	public String getDisplayName() {
		return displayName;
	}

	@JsonProperty("onPremisesImmutableId")
	public String getOnPremisesImmutableId() {
		return onPremisesImmutableId;
	}

	@JsonProperty("lastUpdated")
	public Instant getLastUpdated() {
		return lastUpdated;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		PrivilegedUser that = (PrivilegedUser) o;
		return id.equals(that.id);
	}

	@Override
	public int hashCode() {
		return id.hashCode();
	}

	@Override
	public String toString() {
		return "PrivilegedUser{id='" + id + "', displayName='" + displayName + "', roleCount=" + getRoleCount() + ", lastUpdated=" + lastUpdated + "}";
	}
}