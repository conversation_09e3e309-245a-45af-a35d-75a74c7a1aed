package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

/**
 * Implements remediation for MS.AAD.8.2v1: Restrict Guest Invitations to Specific Roles.

 * This remediator restricts guest invitation privileges to users with the Guest Inviter role
 * by setting the allowInvitesFrom policy to "adminsAndGuestInviters".
 */
@PolicyRemediator("MS.AAD.8.2v1")
public class EntraIDGuestInvitationRemediator extends BaseGuestAccessRemediator {
	/**
	 * Constructor for Guest Invitation Remediator.
	 *
	 * @param graphClient Microsoft Graph API client
	 */
	public EntraIDGuestInvitationRemediator(MicrosoftGraphClient graphClient) {
		super(graphClient);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to restrict guest invitations to specific roles");

		return getCurrentAuthorizationPolicy()
				.thenCompose(this::checkAndUpdatePolicy)
				.exceptionally(ex -> {
					logger.error("Exception during guest invitation restriction remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if the policy needs updating and updates it if needed.
	 *
	 * @param currentPolicy The current authorization policy
	 * @return CompletableFuture with remediation result
	 */
	private CompletableFuture<JsonNode> checkAndUpdatePolicy(JsonNode currentPolicy) {
		if (isStringPropertySetCorrectly(currentPolicy, "allowInvitesFrom", ALLOW_INVITES_ADMINS_AND_GUEST_INVITERS)) {
			logger.info("Guest invitation restriction is already properly configured");
			return CompletableFuture.completedFuture(
					IPolicyRemediator.success(getPolicyId(),
							"Guest invitation restriction is already properly configured").join());
		}

		return updatePolicy();
	}

	/**
	 * Updates the authorization policy to restrict guest invitations.
	 *
	 * @return CompletableFuture with remediation result
	 */
	private CompletableFuture<JsonNode> updatePolicy() {
		// Create minimal update payload
		ObjectNode updatePayload = objectMapper.createObjectNode();
		updatePayload.put("allowInvitesFrom", ALLOW_INVITES_ADMINS_AND_GUEST_INVITERS);

		return updateAuthorizationPolicy(
				updatePayload,
				"Guest invitations are now restricted to admins and users with the Guest Inviter role");
	}

	/**
	 * Helper method to assign the Guest Inviter role to a user or group (not part of the remediation).
	 * This can be called separately to assign the role to specific users who should be able to invite guests.
	 *
	 * @param principalId The ID of the user or group to assign the role to
	 * @return CompletableFuture with the result of the role assignment
	 */
	public CompletableFuture<JsonNode> assignGuestInviterRole(String principalId) {
		try {
			ObjectNode roleAssignment = objectMapper.createObjectNode();
			roleAssignment.put(EntraIDConstants.PRINCIPAL_ID, principalId);
			roleAssignment.put(EntraIDConstants.ROLE_DEFINITION_ID, GUEST_INVITER_ROLE_ID);
			roleAssignment.put(EntraIDConstants.DIRECTORY_SCOPE_ID, "/");

			String payloadJson = objectMapper.writeValueAsString(roleAssignment);
			HttpRequest.BodyPublisher requestBody = HttpRequest.BodyPublishers.ofString(payloadJson);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.POST)
							.withEndpoint(EntraIDConstants.ROLE_ASSIGNMENTS_ENDPOINT)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(requestBody)
							.build()
			);
		} catch (Exception e) {
			throw new GraphClientException("Failed to create role assignment payload", e);
		}
	}
}