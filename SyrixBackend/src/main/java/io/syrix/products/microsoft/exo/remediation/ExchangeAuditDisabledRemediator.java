package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.13.1v2 that ensures Exchange Online audit logging is enabled.
 * <p>
 * This class implements the following security controls:
 * - Verifies that the AuditDisabled setting is set to 'False' (audit logging enabled)
 * - Remediates by setting AuditDisabled to False if it's currently True
 * - Validates the remediation was successful
 * <p>
 * Audit logging is essential for security monitoring, threat detection, and compliance.
 * It provides an audit trail for all Exchange Online activities.
 */
@PolicyRemediator("MS.EXO.13.1v2")
public class ExchangeAuditDisabledRemediator extends RemediatorBase implements IPolicyRemediator {

    private final ObjectMapper mapper = new ObjectMapper();
    private final PowerShellClient exchangeClient;

    /**
     * Constructs a new ExchangeAuditDisabledRemediator with the specified PowerShell client.
     *
     * @param exchangeClient The Exchange Online PowerShell client
     */
    public ExchangeAuditDisabledRemediator(PowerShellClient exchangeClient) {
        this.exchangeClient = exchangeClient;
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        logger.info("Starting audit configuration remediation for MS.EXO.13.1v2");

        // First check the current audit configuration
        return getCurrentAuditConfiguration()
                .thenCompose(this::evaluateAndUpdateConfiguration)
                .exceptionally(ex -> {
                    Throwable cause = ex.getCause() != null ? ex.getCause() : ex;
                    logger.error("Exception during audit configuration remediation: {}", cause.getMessage());
                    if (logger.isDebugEnabled()) {
                        logger.debug("Detailed exception stacktrace:", ex);
                    }
                    return createFailureNode(mapper, 
                            cause.getMessage() != null ? cause.getMessage() : ExoConstants.UNKNOWN_ERROR);
                });
    }

    /**
     * Retrieves the current organization audit configuration.
     *
     * @return CompletableFuture containing the organization configuration
     */
    private CompletableFuture<JsonNode> getCurrentAuditConfiguration() {
        logger.info("Retrieving current audit configuration");

        Map<String, Object> parameters = new HashMap<>(PowerShellClient.DEFAULT_PARAMETERS);
        PowerShellClient.CommandRequest request = new PowerShellClient.CommandRequest(
                ExoConstants.GET_ORGANIZATION_CONFIG, parameters);

        return exchangeClient.executeCmdletCommand(request)
                .thenApply(result -> {
                    if (result == null) {
                        String error = "Failed to retrieve organization configuration: Empty or invalid response";
                        logger.debug(error);
                        throw new ExoRemediationException(error);
                    } else if (result.isEmpty()) {
                        String error = "Failed to extract organization config";
                        logger.debug(error);
                        throw new ExoRemediationException(error);
                    } else {
                        logger.debug("Successfully retrieved organization configuration");
                        return result;
                    }
                });
    }

    /**
     * Evaluates the current configuration and updates it if needed.
     *
     * @param config The current organization configuration
     * @return CompletableFuture containing the remediation result
     */
    private CompletableFuture<JsonNode> evaluateAndUpdateConfiguration(JsonNode config) {
        try {
            // In PowerShell response, we directly work with the array
            JsonNode orgConfig = null;

            // Check if the result is directly an array
            if (config.isArray() && config.size() > 0) {
                orgConfig = config.get(0);
            }
            // If the result is just a single object
            else if (config.isObject()) {
                orgConfig = config;
            }

            if (orgConfig == null) {
                String error = "Failed to extract organization config from response";
                logger.debug(error);
                return CompletableFuture.completedFuture(createFailureNode(mapper, error));
            }

            // Check if auditing is already enabled (AuditDisabled is false)
            if (orgConfig.has(ExoConstants.AUDIT_DISABLED)) {
                boolean auditDisabled = orgConfig.get(ExoConstants.AUDIT_DISABLED).asBoolean();

                if (!auditDisabled) {
                    logger.info("Audit logging is already enabled for the organization (AuditDisabled is False)");
                    return CompletableFuture.completedFuture(createSuccessNode(mapper,
                            "Compliance check passed: AuditDisabled is already set to False"));
                }
            }

            // If we reach here, we need to enable auditing (set AuditDisabled to false)
            logger.info("Audit logging is currently disabled (AuditDisabled is True). Enabling...");
            return enableAuditLogging();
        } catch (Exception e) {
            String errorMsg = "Error evaluating configuration: " + e.getMessage();
            logger.debug(errorMsg, e);
            return CompletableFuture.completedFuture(createFailureNode(mapper, errorMsg));
        }
    }

    /**
     * Enables audit logging by setting AuditDisabled to false.
     *
     * @return CompletableFuture containing the remediation result
     */
    private CompletableFuture<JsonNode> enableAuditLogging() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(ExoConstants.AUDIT_DISABLED, false);  // Set AuditDisabled to false
        parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

        PowerShellClient.CommandRequest request = new PowerShellClient.CommandRequest(
                ExoConstants.SET_ORGANIZATION_CONFIG, parameters);

        return exchangeClient.executeCmdletCommand(request)
                .thenCompose(result -> {
                    // Set-OrganizationConfig typically returns empty when successful
                    // or error details when failed
                    if (result == null || (result.isArray() && result.size() == 0) || (result.isObject() && result.isEmpty())) {
                        logger.info("Set-OrganizationConfig command executed successfully");
                        return validateConfiguration();
                    } else if (result.has(ExoConstants.ERROR_FIELD)) {
                        String error = result.get(ExoConstants.ERROR_FIELD).asText();
                        logger.debug("Failed to enable audit logging: {}", error);
                        return CompletableFuture.completedFuture(createFailureNode(mapper, error));
                    } else {
                        // Assume success if we get a response that doesn't indicate error
                        logger.info("Set-OrganizationConfig command completed, validating changes...");
                        return validateConfiguration();
                    }
                })
                .exceptionally(ex -> {
                    Throwable cause = ex.getCause() != null ? ex.getCause() : ex;
                    String errorMsg = "Failed to enable audit logging: " + cause.getMessage();
                    logger.debug("Exception during enableAuditLogging: {}", errorMsg);
                    if (logger.isDebugEnabled()) {
                        logger.debug("Detailed exception stacktrace:", ex);
                    }
                    return createFailureNode(mapper, errorMsg);
                });
    }

    /**
     * Validates that the configuration was successfully applied.
     *
     * @return CompletableFuture containing the final validation result
     */
    private CompletableFuture<JsonNode> validateConfiguration() {
        logger.info("Validating audit configuration");

        Map<String, Object> parameters = new HashMap<>(PowerShellClient.DEFAULT_PARAMETERS);
        PowerShellClient.CommandRequest request = new PowerShellClient.CommandRequest(
                ExoConstants.GET_ORGANIZATION_CONFIG, parameters);

        return exchangeClient.executeCmdletCommand(request)
                .thenApply(result -> {
                    JsonNode orgConfig = null;

                    // Check if the result is directly an array
                    if (result.isArray() && result.size() > 0) {
                        orgConfig = result.get(0);
                    }
                    // If the result is just a single object
                    else if (result.isObject()) {
                        orgConfig = result;
                    }

                    if (orgConfig == null) {
                        String error = "Failed to validate configuration: Invalid response format";
                        logger.debug(error);
                        return createFailureNode(mapper, error);
                    }

                    // Check if AuditDisabled is now false
                    if (orgConfig.has(ExoConstants.AUDIT_DISABLED) && !orgConfig.get(ExoConstants.AUDIT_DISABLED).asBoolean()) {
                        logger.info("Validation successful: Audit logging is enabled for the organization");
                        return createSuccessNode(mapper,
                                "Successfully set AuditDisabled to False, audit logging is now enabled");
                    } else {
                        String error = "Failed to enable audit logging: Configuration change did not take effect";
                        logger.debug("Validation failed: Audit logging is still disabled");
                        return createFailureNode(mapper, error);
                    }
                })
                .exceptionally(ex -> {
                    Throwable cause = ex.getCause() != null ? ex.getCause() : ex;
                    String errorMsg = "Failed to validate configuration: " + cause.getMessage();
                    logger.debug("Exception during validateConfiguration: {}", errorMsg);
                    if (logger.isDebugEnabled()) {
                        logger.debug("Detailed exception stacktrace:", ex);
                    }
                    return createFailureNode(mapper, errorMsg);
                });
    }
}