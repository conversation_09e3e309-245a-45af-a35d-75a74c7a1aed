package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.*;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.protocols.client.PowerShellTeamsClient;

import java.util.Collections;
import java.util.List;

public abstract class TeamsRemediatorBase extends RemediatorBase {
	protected final PowerShellTeamsClient client;
	protected final ObjectMapper jsonMapper;
	protected final JsonNode configNode;
	protected final TeamsRemediationConfig remediationConfig;

	public TeamsRemediatorBase(PowerShellTeamsClient client, JsonNode configNode, TeamsRemediationConfig remediationConfig) {
		this.client = client;
		this.configNode = configNode;
		this.remediationConfig = remediationConfig;
		this.jsonMapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	/**
	 * Extracts Teams meeting policies list from the configuration JsonNode
	 *
	 * @return List of Teams meeting policies
	 */
	protected List<TeamsMeetingPolicy> getTeamsMeetingPolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of meeting policies");
			return Collections.emptyList();
		}

		JsonNode policiesNode = configNode.get(TeamsConstants.CONFIG_KEY_MEETING_POLICIES);

		if (policiesNode == null || !policiesNode.isArray()) {
			logger.warn("Meeting policies node not found or not an array");
			return Collections.emptyList();
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamsMeetingPolicy.class);
		return jsonMapper.convertValue(policiesNode, collectionType);
	}

	/**
	 * Extracts Teams tenant information list from the configuration JsonNode
	 *
	 * @return List of Teams tenant information
	 */
	protected List<TeamsTenantInfo> getTeamsTenantInfos() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of tenant info");
			return Collections.emptyList();
		}

		JsonNode tenantInfoNode = configNode.get(TeamsConstants.CONFIG_KEY_TEAMS_TENANT_INFO);

		if (tenantInfoNode == null || !tenantInfoNode.isArray()) {
			logger.warn("Tenant info node not found or not an array");
			return Collections.emptyList();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamsTenantInfo.class);
		return jsonMapper.convertValue(tenantInfoNode, collectionType);
	}

	/**
	 * Extracts Teams client configuration list from the configuration JsonNode
	 *
	 * @return List of Teams client configurations
	 */
	protected List<TeamsClientConfiguration> getTeamsClientConfigurations() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of client configurations");
			return Collections.emptyList();
		}

		JsonNode clientConfigNode = configNode.get(TeamsConstants.CONFIG_KEY_CLIENT_CONFIGURATION);

		if (clientConfigNode == null || !clientConfigNode.isArray()) {
			logger.warn("Client configuration node not found or not an array");
			return Collections.emptyList();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamsClientConfiguration.class);
		return jsonMapper.convertValue(clientConfigNode, collectionType);
	}

	/**
	 * Extracts Teams tenant federation configurations list from the configuration JsonNode
	 *
	 * @return List of Teams tenant federation configurations
	 */
	protected List<TeamsTenantFederationConfiguration> getFederationConfigurations() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of federation configurations");
			return Collections.emptyList();
		}

		JsonNode federationNode = configNode.get(TeamsConstants.CONFIG_KEY_FEDERATION_CONFIGURATION);

		if (federationNode == null || !federationNode.isArray()) {
			logger.warn("Federation configuration node not found or not an array");
			return Collections.emptyList();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamsTenantFederationConfiguration.class);
		return jsonMapper.convertValue(federationNode, collectionType);
	}

	/**
	 * Extracts Teams meeting broadcast policies list from the configuration JsonNode
	 *
	 * @return List of Teams meeting broadcast policies
	 */
	protected List<TeamsMeetingBroadcastPolicy> getBroadcastPolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of broadcast policies");
			return List.of();
		}

		JsonNode policiesNode = configNode.get(TeamsConstants.CONFIG_KEY_BROADCAST_POLICIES);

		if (policiesNode == null || !policiesNode.isArray()) {
			logger.warn("Broadcast policies node not found or not an array");
			return List.of();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamsMeetingBroadcastPolicy.class);
		return jsonMapper.convertValue(policiesNode, collectionType);
	}

	/**
	 * Extracts Teams app permission policies list from the configuration JsonNode
	 *
	 * @return List of Teams app permission policies
	 */
	protected List<TeamAppPermissionPolicy> getTeamsAppPermissionPolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of app permission policies");
			return Collections.emptyList();
		}

		JsonNode appPermissionNode = configNode.get(TeamsConstants.CONFIG_KEY_APP_POLICIES);

		if (appPermissionNode == null || !appPermissionNode.isArray()) {
			logger.warn("App permission policies node not found or not an array");
			return Collections.emptyList();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TeamAppPermissionPolicy.class);
		return jsonMapper.convertValue(appPermissionNode, collectionType);
	}
}