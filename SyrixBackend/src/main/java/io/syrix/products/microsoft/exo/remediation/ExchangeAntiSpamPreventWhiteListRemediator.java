package io.syrix.products.microsoft.exo.remediation;

import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import java.util.Collections;
import java.util.Map;

@PolicyRemediator("MS.EXO.14.3v1")
public class ExchangeAntiSpamPreventWhiteListRemediator extends ExchangeAntiSpamRemediatorBase {
    public ExchangeAntiSpamPreventWhiteListRemediator(MicrosoftGraphClient graphClient,
													  PowerShellClient exchangeClient,
													  ObjectNode configNode,
													  ExchangeRemediationContext remediationContext,
													  ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
    }
    public ExchangeAntiSpamPreventWhiteListRemediator(MicrosoftGraphClient graphClient,
													  PowerShellClient exchangeClient) {
        super(graphClient, exchangeClient);
    }
    @Override
    protected Map<String, Object> getAntispamPolicy() {
        return Map.of(
            ALLOWED_SENDER_DOMAINS, Collections.emptyList()
        );
    }
}

