package io.syrix.products.microsoft.exo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import static io.syrix.common.constants.Constants.VALUE_FIELD;

public class JsonDomainProcessor {

	/**
	 * Parses JSON string into JsonNode.
	 *
	 * @param jsonString The JSON string to parse
	 * @return Parsed JsonNode
	 * @throws IOException if parsing fails
	 */
	public static JsonNode parseJson(String jsonString, ObjectMapper objectMapper) throws IOException {
		return objectMapper.readTree(jsonString);
	}

	/**
	 * Filters domains based on a property predicate.
	 *
	 * @param jsonNode The JsonNode containing domain data
	 * @param propertyName The name of the property to filter on
	 * @param predicate The predicate to apply for filtering
	 * @return Filtered JsonNode containing matching domains
	 */
	public static JsonNode filterByProperty(JsonNode jsonNode,
											String propertyName,
											Predicate<JsonNode> predicate,
											ObjectMapper objectMapper) {
		if (!jsonNode.has(VALUE_FIELD) || !jsonNode.get(VALUE_FIELD).isArray()) {
			throw new IllegalArgumentException("JSON must contain a 'value' array");
		}

		ArrayNode valueArray = (ArrayNode) jsonNode.get(VALUE_FIELD);
		ArrayNode filteredArray = objectMapper.createArrayNode();

		for (JsonNode domain : valueArray) {
			if (domain.has(propertyName) && predicate.test(domain.get(propertyName))) {
				filteredArray.add(domain);
			}
		}

		return filteredArray;
	}

	/**
	 * Filters domains by exact property value.
	 *
	 * @param jsonNode The JsonNode containing domain data
	 * @param propertyName The name of the property to filter on
	 * @param propertyValue The exact value to match
	 * @return Filtered JsonNode containing matching domains
	 */
	public static JsonNode filterByExactValue(JsonNode jsonNode,
									   String propertyName,
									   String propertyValue,
									   ObjectMapper objectMapper) {
		return filterByProperty(jsonNode, propertyName,
				propNode -> propNode.isTextual() && propNode.asText().equals(propertyValue), objectMapper);
	}

	/**
	 * Filters domains by boolean property value.
	 *
	 * @param jsonNode The JsonNode containing domain data
	 * @param propertyName The name of the boolean property
	 * @param value The boolean value to match
	 * @return Filtered JsonNode containing matching domains
	 */
	public static JsonNode filterByBoolean(JsonNode jsonNode,
									String propertyName,
									boolean value,
									ObjectMapper objectMapper) {
		return filterByProperty(jsonNode, propertyName,
				propNode -> propNode.isBoolean() && propNode.asBoolean() == value, objectMapper);
	}

	/**
	 * Gets all unique values for a specific property across all domains.
	 *
	 * @param jsonNode The JsonNode containing domain data
	 * @param propertyName The name of the property to collect values from
	 * @return List of unique values for the specified property
	 */
	public static List<String> getUniquePropertyValues(JsonNode jsonNode, String propertyName) {
		List<String> values = new ArrayList<>();
		JsonNode valueArray = jsonNode.get("value");

		if (valueArray != null && valueArray.isArray()) {
			for (JsonNode domain : valueArray) {
				JsonNode propertyNode = domain.get(propertyName);
				if (propertyNode != null && !propertyNode.isNull()) {
					String value = propertyNode.asText();
					if (!values.contains(value)) {
						values.add(value);
					}
				}
			}
		}

		return values;
	}
}