package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.15.1v1, MS.EXO.15.2v1, and MS.EXO.15.3v1 which configures Exchange Online
 * Safe Links policies according to the CISA M365 Secure Configuration Baseline.

 * This class implements the following security controls:
 * - MS.EXO.15.1v1: Enable URL comparison with block-list
 * - MS.EXO.15.2v1: Enable scanning of direct download links for malware
 * - MS.EXO.15.3v1: Enable user click tracking

 * These controls mitigate MITRE ATT&CK T1566 Phishing attacks, specifically T1566.002 (Spearphishing Link).
 */
@PolicyRemediator("MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1")
public class ExchangeSafeLinksRemediator extends RemediatorBase {
	public static final String MESSAGE = "message";
	public static final String DETAILS = "details";
	public static final String GET_SAFE_LINKS_POLICY = "Get-SafeLinksPolicy";
	private final PowerShellClient exchangeClient;
	private final ObjectMapper objectMapper = new ObjectMapper();

	// The name to use for our Safe Links policy
	private static final String POLICY_NAME = "SecurityBaselinePolicy";
	public static final String NAME = "Name";
	public static final String IDENTITY = "Identity";
	public static final String ADMIN_DISPLAY_NAME = "AdminDisplayName";
	public static final String RECIPIENT_DOMAIN_IS = "RecipientDomainIs";
	public static final String ENABLED = "Enabled";
	public static final String PRIORITY = "Priority";

	public static final String ERROR = "Error";
	public static final String IS_CLICK_THROUGH_ALLOWED = "IsClickThroughAllowed";
	public static final String ENABLE_SAFE_LINKS_FOR_TEAMS = "EnableSafeLinksForTeams";
	public static final String ENABLE_SAFE_LINKS_FOR_OFFICE = "EnableSafeLinksForOffice";
	public static final String ENABLE_SAFE_LINKS_FOR_EMAIL = "EnableSafeLinksForEmail";
	public static final String DO_NOT_TRACK_USER_CLICKS = "DoNotTrackUserClicks";
	public static final String ENABLE_FILE_SCANNING = "EnableFileScanning";
	public static final String SCAN_URLS = "ScanUrls";

	public static final String ALLOW_CLICK_THROUGH = "AllowClickThrough";
	public static final String ENABLE_FOR_INTERNAL_SENDERS = "EnableForInternalSenders";
	public static final String TRACK_CLICKS = "TrackClicks";

	// Configure Safe Links policy settings according to security baseline
	private static final Map<String, Object> SAFELINK_POLICY_CREATE = Map.of(
			NAME, POLICY_NAME, // Use consistent policy name
			ALLOW_CLICK_THROUGH, false, // Block access to malicious URLs (MS.EXO.15.1v1) - updated parameter name
			ENABLE_SAFE_LINKS_FOR_TEAMS, true, // Enable for Teams
			ENABLE_SAFE_LINKS_FOR_OFFICE, true, // Enable for Office
			ENABLE_FOR_INTERNAL_SENDERS, true, // Enable for Email - updated parameter name
			TRACK_CLICKS, true, // Enable user click tracking (MS.EXO.15.3v1) - updated parameter name
			SCAN_URLS, true, // Enable URL scanning against block list (MS.EXO.15.1v1)
			ADMIN_DISPLAY_NAME, "Security Baseline Safe Links Policy"
	);

	private static final String RULE_NAME =  POLICY_NAME + "Rule";

	// Rule doesn't exist, create it
	private static final Map<String, Object> RULE_PARAMS = Map.of(
			NAME, RULE_NAME,
			"SafeLinksPolicy", POLICY_NAME,
			RECIPIENT_DOMAIN_IS, "*", // Apply to all recipients
			ENABLED, true,
			PRIORITY, 0); // High priority

	/**
	 * Constructs a new ExchangeSafeLinksRemediator with the specified PowerShell client.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 */
	public ExchangeSafeLinksRemediator(PowerShellClient exchangeClient) {
		this.exchangeClient = exchangeClient;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for Safe Links policies (MS.EXO.15.1v1, MS.EXO.15.2v1, MS.EXO.15.3v1)");

		// First check if any Safe Links policies exist
		return getAllSafeLinksPolicies()
				.thenCompose(policies -> {
					if (policies == null || policies.isEmpty() || policies.size() == 0) {
						logger.info("No Safe Links policies found. Creating a new policy.");
						return createSafeLinksPolicy();
					}

					// Check if our specific policy exists
					JsonNode existingPolicy = findPolicyByName(policies, POLICY_NAME);
					if (existingPolicy != null) {
						logger.info("Found existing '{}' policy. Checking compliance.", POLICY_NAME);
						if (isAlreadyCompliant(existingPolicy)) {
							logger.info("Safe Links policy already complies with requirements");
							return CompletableFuture.completedFuture(
									createSuccessNode("Safe Links policy already meets requirements"));
						}
						// Policy exists but needs updates
						return updateSafeLinksPolicy(POLICY_NAME);
					}

					// Our policy doesn't exist - create a new one
					logger.info("Security baseline policy not found. Creating a new policy.");
					return createSafeLinksPolicy();
				})
				.exceptionally(ex -> {
					logger.error("Exception during Safe Links policy remediation", ex);
					return createFailureNode("Failed to remediate Safe Links policies: " + ex.getMessage());
				});
	}

	/**
	 * Finds a policy by name in an array of policies
	 */
	private JsonNode findPolicyByName(JsonNode policies, String name) {
		if (policies.isArray()) {
			for (JsonNode policy : policies) {
				if ((policy.has(NAME) && policy.get(NAME).asText().equals(name)) ||
						(policy.has(IDENTITY) && policy.get(IDENTITY).asText().equals(name))) {
					return policy;
				}
			}
		}
		return null;
	}

	/**
	 * Check if policy is already compliant to avoid unnecessary changes
	 */
	private boolean isAlreadyCompliant(JsonNode policy) {
		if (policy == null) {
			return false;
		}

		PolicyValidationResult validation = checkPolicyCompliance(policy);
		return validation.isCompliant();
	}

	/**
	 * Creates a new Safe Links policy with security settings
	 */
	private CompletableFuture<JsonNode> createSafeLinksPolicy() {
		logger.info("Creating new Safe Links policy with security settings");

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"New-SafeLinksPolicy",
								SAFELINK_POLICY_CREATE
						))
				.thenCompose(result -> {
					logger.debug("New-SafeLinksPolicy result: {}", result);

					// Check if we received a successful response
					if (result == null || (result.has(ERROR) && !result.get(ERROR).isNull())) {
						String errorMsg = extractErrorMessage(result);
						logger.error("Failed to create Safe Links policy: {}", errorMsg);
						return CompletableFuture.completedFuture(createFailureNode(errorMsg));
					}

					logger.info("Successfully created Safe Links policy, checking if it exists before proceeding");

					// Verify the policy was actually created before continuing
					return doesPolicyExist(POLICY_NAME)
							.thenCompose(exists -> {
								if (Boolean.FALSE.equals(exists)) {
									String msg = "Policy creation appeared to succeed but policy not found in Exchange. This may be due to replication delay or a silent failure.";
									logger.error(msg);
									return CompletableFuture.completedFuture(createFailureNode(msg));
								}

								logger.info("Policy confirmed to exist, creating rule to apply it");

								// Also create a rule to apply the policy to all recipients
								return createSafeLinksRule()
										.thenCompose(ruleResult -> verifySafeLinksConfiguration());
							});
				})
				.exceptionally(ex -> {
					logger.error("Exception during Safe Links policy creation", ex);
					return createFailureNode("Failed to create Safe Links policy: " + ex.getMessage());
				});
	}

	private CompletableFuture<Boolean> doesRuleExist(String ruleName) {
		Map<String, Object> params = new HashMap<>();
		params.put(IDENTITY, ruleName);
		params.put("ErrorAction", "SilentlyContinue");

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"Get-SafeLinksRule",
								params
						))
				.thenApply(result ->
						result != null && !result.isEmpty() && (result.isArray() && result.size() > 0)
								&& (!result.has(ERROR) || result.get(ERROR).isNull())
				)
				.exceptionally(ex -> {
					logger.warn("Error checking if rule exists: {}", ex.getMessage());
					return false;
				});
	}

	private CompletableFuture<JsonNode> createSafeLinksRule() {
		// First check if the rule already exists
		return doesRuleExist(RULE_NAME)
				.thenCompose(exists -> {
					if (Boolean.TRUE.equals(exists)) {
						logger.info("Safe Links rule '{}' already exists, skipping creation", RULE_NAME);
						return CompletableFuture.completedFuture(null);
					}

					return exchangeClient.executeCmdletCommand(
							new PowerShellClient.CommandRequest(
									"New-SafeLinksRule",
									RULE_PARAMS
							));
				})
				.exceptionally(ex -> {
					logger.error("Failed to create Safe Links rule: {}", ex.getMessage());
					// We don't fail the entire process if just the rule creation fails
					return null;
				});
	}

	/**
	 * Updates an existing Safe Links policy
	 */
	private CompletableFuture<JsonNode> updateSafeLinksPolicy(String policyName) {
		logger.info("Updating Safe Links policy '{}' to meet security requirements", policyName);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(IDENTITY, policyName);
		parameters.put(ALLOW_CLICK_THROUGH, false);  // Updated parameter name
		parameters.put(ENABLE_SAFE_LINKS_FOR_TEAMS, true);
		parameters.put(ENABLE_SAFE_LINKS_FOR_OFFICE, true);
		parameters.put(ENABLE_FOR_INTERNAL_SENDERS, true);  // Updated parameter name
		parameters.put(TRACK_CLICKS, true);  // Updated parameter name
		parameters.put(SCAN_URLS, true);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								"Set-SafeLinksPolicy",
								parameters
						))
				.thenCompose(result -> {
					if (result != null && (result.has(ERROR))) {
						String errorMsg = extractErrorMessage(result);
						logger.error("Failed to update Safe Links policy: {}", errorMsg);
						return CompletableFuture.completedFuture(createFailureNode(errorMsg));
					}

					logger.info("Successfully updated Safe Links policy, verifying changes");

					// Wait a moment for changes to propagate
					try {
						Thread.sleep(3000);
					} catch (InterruptedException e) {
						Thread.currentThread().interrupt();
					}

					return verifySafeLinksConfiguration();
				})
				.exceptionally(ex -> {
					logger.error("Exception during Safe Links policy update", ex);
					return createFailureNode("Failed to update Safe Links policy: " + ex.getMessage());
				});
	}

	/**
	 * Extracts the error message from the result, handling different possible structures
	 */
	private String extractErrorMessage(JsonNode result) {
		if (result == null) {
			return "No response received";
		}

		if (result.has(ERROR)) {
			JsonNode error = result.get(ERROR);
			if (error.isTextual()) {
				return error.asText();
			} else if (error.isObject()) {
				if (error.has(MESSAGE)) {
					return error.get(MESSAGE).asText();
				} else if (error.has(DETAILS) && error.get(DETAILS).isArray() && error.get(DETAILS).size() > 0) {
					JsonNode details = error.get(DETAILS).get(0);
					if (details.has(MESSAGE)) {
						return details.get(MESSAGE).asText();
					}
				}
			}
		}

		return "Unknown error: " + (result.toString().length() > 100 ?
				result.toString().substring(0, 100) + "..." : result.toString());
	}

	/**
	 * Helper method to check if a policy exists
	 */
	private CompletableFuture<Boolean> doesPolicyExist(String policyName) {
		Map<String, Object> params = new HashMap<>();
		params.put(IDENTITY, policyName);

		// Add ErrorAction parameter to prevent exception when policy doesn't exist
		params.put("ErrorAction", "SilentlyContinue");

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								GET_SAFE_LINKS_POLICY,
								params
						))
				.thenApply(result ->
					result != null && !result.isEmpty() && (result.isArray() && result.size() > 0)
							&& (!result.has(ERROR) || result.get(ERROR).isNull())
				)
				.exceptionally(ex -> {
					logger.warn("Error checking if policy exists: {}", ex.getMessage());
					return false;
				});
	}


	/**
	 * Verifies that the Safe Links configuration was applied correctly.
	 */
	private CompletableFuture<JsonNode> verifySafeLinksConfiguration() {
		logger.info("Verifying Safe Links policy configuration");

		return doesPolicyExist(POLICY_NAME)
				.thenCompose(exists -> {
					if (Boolean.FALSE.equals(exists)) {
						return CompletableFuture.completedFuture(
								createFailureNode("Safe Links policy not found during verification - creation may have failed silently")
						);
					}

					// Policy exists, now get its details
					return getSafeLinksPolicy(POLICY_NAME)
							.thenApply(this::validatePolicy);
				})
				.exceptionally(this::handleVerificationException);
	}

	/**
	 * Gets all Safe Links policies
	 */
	private CompletableFuture<JsonNode> getAllSafeLinksPolicies() {
		return exchangeClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						GET_SAFE_LINKS_POLICY,
						new HashMap<>() // Empty parameters to get all policies
				));
	}

	/**
	 * Gets a specific Safe Links policy by name
	 */
	private CompletableFuture<JsonNode> getSafeLinksPolicy(String name) {
		Map<String, Object> params = new HashMap<>();
		params.put(IDENTITY, name);

		return exchangeClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						GET_SAFE_LINKS_POLICY,
						params
				));
	}

	private JsonNode validatePolicy(JsonNode result) {
		if (result == null || result.isEmpty()) {
			return createFailureNode("Could not verify Safe Links policy configuration");
		}

		logger.debug("Retrieved policy for validation: {}", result);
		JsonNode policy = result.isArray() && result.size() > 0 ? result.get(0) : result;
		PolicyValidationResult validation = checkPolicyCompliance(policy);

		return validation.isCompliant()
				? createSuccessNode(getSuccessMessage())
				: createFailureNode(validation.getErrorMessage());
	}

	private PolicyValidationResult checkPolicyCompliance(JsonNode policy) {
		ValidationBuilder builder = new ValidationBuilder();

		builder.checkUrlBlockList(policy)
				.checkFileScanning(policy)
				.checkUserClickTracking(policy);

		return builder.build();
	}

	private String getSuccessMessage() {
		return "Successfully configured Safe Links policies to meet MS.EXO.15.1v1, " +
				"MS.EXO.15.2v1, and MS.EXO.15.3v1 requirements";
	}

	private JsonNode handleVerificationException(Throwable ex) {
		if (ex.getCause() != null && ex.getCause().getMessage() != null &&
				ex.getCause().getMessage().contains("couldn't be found")) {
			logger.error("Policy not found during verification", ex);
			return createFailureNode("Safe Links policy not found - creation may have failed");
		}

		logger.error("Exception during Safe Links policy verification", ex);
		return createFailureNode("Failed to verify Safe Links policies: " + ex.getMessage());
	}

	private static class PolicyValidationResult {
		private final boolean compliant;
		private final String errorMessage;

		PolicyValidationResult(boolean compliant, String errorMessage) {
			this.compliant = compliant;
			this.errorMessage = errorMessage;
		}

		boolean isCompliant() {
			return compliant;
		}

		String getErrorMessage() {
			return errorMessage;
		}
	}

	private static class ValidationBuilder {
		private boolean isCompliant = true;
		private final StringBuilder errors = new StringBuilder("Safe Links policy configuration issues: ");

		ValidationBuilder checkUrlBlockList(JsonNode policy) {
			boolean validUrlBlocking = false;

			// Check if URL scanning is enabled (MS.EXO.15.1v1)
			if (policy.has(SCAN_URLS) && policy.get(SCAN_URLS).asBoolean() &&
					policy.has(ALLOW_CLICK_THROUGH) && !policy.get(ALLOW_CLICK_THROUGH).asBoolean()) {
				// Check if click-through is disallowed for malicious URLs
				validUrlBlocking = true;
			}

			if (!validUrlBlocking) {
				isCompliant = false;
				errors.append("URL comparison with block-list not properly configured. ");
			}

			return this;
		}

		/**
		 * Validates that file scanning is properly configured according to MS.EXO.15.2v1
		 * which requires scanning of direct download links for malware.
		 
		 * Based on current Exchange Online parameters, this checks both:
		 * - DeliverMessageAfterScan: Controls if messages are delivered after links are scanned
		 * - ScanUrls: Controls if URLs in messages are scanned for malicious content
		 */
		ValidationBuilder checkFileScanning(JsonNode policy) {
			// Check for DeliverMessageAfterScan which is the current parameter for file scanning
			boolean deliverAfterScan = policy.has("DeliverMessageAfterScan") &&
					policy.get("DeliverMessageAfterScan").asBoolean();

			// Check ScanUrls which is also related to scanning content
			boolean scanUrlsEnabled = policy.has(SCAN_URLS) &&
					policy.get(SCAN_URLS).asBoolean();

			// Both parameters should be enabled for proper file scanning protection
			boolean validFileScanning = deliverAfterScan && scanUrlsEnabled;

			if (!validFileScanning) {
				isCompliant = false;
				StringBuilder detail = new StringBuilder("File scanning for direct download links not properly configured: ");

				if (!deliverAfterScan) {
					detail.append("DeliverMessageAfterScan is disabled. ");
				}

				if (!scanUrlsEnabled) {
					detail.append("ScanUrls is disabled. ");
				}

				errors.append(detail);
			}

			return this;
		}

		ValidationBuilder checkUserClickTracking(JsonNode policy) {
			boolean validClickTracking = policy.has(TRACK_CLICKS) && policy.get(TRACK_CLICKS).asBoolean();

			if (!validClickTracking) {
				isCompliant = false;
				errors.append("User click tracking not enabled. ");
			}

			return this;
		}

		PolicyValidationResult build() {
			return new PolicyValidationResult(isCompliant, errors.toString());
		}
	}

	/**
	 * Creates a success JsonNode for successful remediation.
	 */
	private JsonNode createSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put("status", "success");
		node.put("policyId", getPolicyId());
		node.put(MESSAGE, message);
		return node;
	}

	/**
	 * Creates an error JsonNode for failed remediation.
	 */
	private JsonNode createFailureNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put("status", "failed");
		node.put("policyId", getPolicyId());
		node.put(MESSAGE, message);
		return node;
	}
}