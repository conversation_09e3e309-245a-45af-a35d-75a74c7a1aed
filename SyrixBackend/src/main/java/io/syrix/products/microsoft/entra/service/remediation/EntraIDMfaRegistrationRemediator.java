package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.protocols.client.MicrosoftGraphClient;

/**
 * Implements remediation for MS.AAD.3.8v1: Managed Devices SHOULD be required to register MFA.

 * This remediation creates a Conditional Access Policy that requires devices to be
 * managed for security information registration operations.
 */
@PolicyRemediator("MS.AAD.3.8v1")
public class EntraIDMfaRegistrationRemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Managed Device for MFA Registration";

	/**
	 * Constructor with custom user configuration.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 * @param policyState The state of the policy
	 */
	public EntraIDMfaRegistrationRemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);
	}

	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Get the conditions object and configure it for MFA registration
		ObjectNode conditions = (ObjectNode) policyPayload.get("conditions");

		// Configure platforms - all platforms
		ObjectNode platforms = objectMapper.createObjectNode();
		ArrayNode includePlatforms = objectMapper.createArrayNode();
		includePlatforms.add("all");
		platforms.set("includePlatforms", includePlatforms);
		platforms.set("excludePlatforms", objectMapper.createArrayNode());
		conditions.set("platforms", platforms);

		// Configure applications for user actions
		ObjectNode applications = (ObjectNode) conditions.get("applications");
		applications.remove("includeApplications");
		ArrayNode includeApps = objectMapper.createArrayNode();
		applications.set("includeApplications", includeApps);

		// Target security info registration specifically
		ArrayNode includeUserActions = objectMapper.createArrayNode();
		includeUserActions.add("urn:user:registerSecurityInfo");
		applications.set("includeUserActions", includeUserActions);

		// Configure grant controls to require device compliance
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "AND");

		// Require managed device or Hybrid Azure AD join
		ArrayNode builtInControls = objectMapper.createArrayNode();
		builtInControls.add("compliantDevice");
		builtInControls.add("domainJoinedDevice");
		grantControls.set("builtInControls", builtInControls);

		// Empty arrays for other controls
		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}
}