package io.syrix.products.microsoft.teams.model;

import io.syrix.protocols.client.teams.powershell.command.types.TeamsPowerShellCommandResult;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.util.List;
import java.util.Map;

@SuppressWarnings({"CommentedOutCode,unused"})
public class TeamsTenantFederationConfiguration implements TeamsPowerShellCommandResult {
    public Boolean allowFederatedUsers;
    public Boolean allowPublicUsers;
    public Boolean allowTeamsConsumer;
    public Boolean allowTeamsConsumerInbound;
    public Boolean allowTeamsSms;
    public Map<String, Object> allowedDomains;
    public List<Object> allowedTrialTenantDomains;
    public Boolean blockAllSubdomains;
    public List<Object> blockedDomains;
    public String configId;
    public ConfigMetadata configMetadata;
    public Boolean customizeFederation;
    public String dataSource;
    public String externalAccessWithTrialTenants;
    public String domainBlockingForMDOAdminsInTeams;
    public String identity;
    public Key key;
    public Boolean restrictTeamsConsumerToExternalUserProfiles;
    public Boolean sharedSipAddressSpace;
    public Boolean treatDiscoveredPartnersAsUnverified;

    public static class ConfigMetadata {
        public String authority;

        public ConfigMetadata() {}
        public ConfigMetadata(TenantFederationConfiguration.ConfigMetadata configMetadata) {
            if (configMetadata == null) return;
            this.authority = configMetadata.authority;
        }
    }

    public static class Key {
        public String authorityId;
        public String defaultXml;
        public String schemaId;
        public String scopeClass;
        public String xmlRoot;

        public Key(){} //for serialization from json
        public Key(TenantFederationConfiguration.Key key) {
            if (key == null) return;

            this.authorityId = key.authorityId == null ? null : String.format("Class=%s;InstanceId=%s;XmlRoot=", key.authorityId.classType, key.authorityId.instanceId);

            this.defaultXml = key.defaultXml == null ? null : String.format("SchemaId=;Data=;ConfigObject=;Signature=%s;IsModified=%s",key.defaultXml.signature, key.defaultXml.isModified ? "True" : "False");
            this.schemaId = key.schemaId == null ? null : "XName="; //TODO Artur recheck
            this.scopeClass = key.scopeClass;
            this.xmlRoot = key.xmlRoot == null ? null : String.format("name=%s", key.xmlRoot.name);
        }

    }

//    public static class AuthorityId {
//        @JsonProperty(value = "Class")
//        public String classType;
//        public String instanceId;
//        public XmlRoot xmlRoot;
//
//        public AuthorityId() {} //for serialization from json
//        public AuthorityId(TenantFederationConfiguration.AuthorityId authorityId) {
//            if (authorityId == null) return;
//            this.classType = authorityId.classType;
//            this.instanceId = authorityId.instanceId;
//            this.xmlRoot = authorityId.xmlRoot == null ? null : new XmlRoot(authorityId.xmlRoot);
//        }
//
//    }

//    public static class DefaultXml {
//        public Object configObject;
//        public Data data;
//        public Boolean isModified;
//        public SchemaId schemaId;
//        public String signature;
//
//        public DefaultXml() { } //for serialization from json
//        public DefaultXml(TenantFederationConfiguration.DefaultXml defaultXml) {
//            if (defaultXml == null) return;
//            this.configObject = defaultXml.configObject;
//            this.data = defaultXml.data == null ? null : new Data(defaultXml.data);
//            this.isModified = defaultXml.isModified;
//            this.schemaId = defaultXml.schemaId == null ? null : new SchemaId(defaultXml.schemaId);
//            this.signature = defaultXml.signature;
//        }
//
//    }
//
//    public static class Data {
//        public TenantFederationSettingsData tenantFederationSettings;
//
//        public Data() {}//for serialization from json
//        public Data(TenantFederationConfiguration.Data data) {
//            if (data == null) return;
//            this.tenantFederationSettings = data.tenantFederationSettings == null ? null : new TenantFederationSettingsData(data.tenantFederationSettings);
//        }
//
//    }

//    public static class TenantFederationSettingsData {
//        @JsonProperty(value = "@xmlns")
//        public String xmlns;
//        public Map<String, Object> allowedDomains;
//        public List<String> allowedTrialTenantDomains;
//        public List<String> blockedDomains;
//
//        public TenantFederationSettingsData() {}//for serialization from json
//        public TenantFederationSettingsData(TenantFederationConfiguration.TenantFederationSettingsData settingsData) {
//            if (settingsData == null) return;
//            this.xmlns = settingsData.xmlns;
//            this.allowedDomains = settingsData.allowedDomains == null ? null : new HashMap<>(settingsData.allowedDomains);
//            this.allowedTrialTenantDomains = settingsData.allowedTrialTenantDomains == null ? null : new ArrayList<>(settingsData.allowedTrialTenantDomains);
//            this.blockedDomains = settingsData.blockedDomains == null ? null : new ArrayList<>(settingsData.blockedDomains);
//        }
//    }
//
//    public static class SchemaId {
//        public XName xName;
//        public SchemaId() {} //for serialization from json
//        public SchemaId(TenantFederationConfiguration.SchemaId schemaId) {
//            if (schemaId == null) return;
//            this.xName = schemaId.xName == null ? null : new XName(schemaId.xName);
//        }
//
//    }
//
//    public static class XName {
//        public String name;
//
//        public XName() {}//for serialization from json
//        public XName(TenantFederationConfiguration.XName xName) {
//            if (xName == null) return;
//            this.name = xName.name;
//        }
//
//    }
//
//    public static class XmlRoot {
//        public String name;
//
//        public XmlRoot() {} //for serialization from json
//        public XmlRoot(TenantFederationConfiguration.XmlRoot xmlRoot) {
//            if (xmlRoot == null) return;
//            this.name = xmlRoot.name;
//        }
//
//    }

    public TeamsTenantFederationConfiguration() {}
    public TeamsTenantFederationConfiguration(TenantFederationConfiguration configuration) {
        this.allowFederatedUsers = configuration.allowFederatedUsers;
        this.allowPublicUsers = configuration.allowPublicUsers;
        this.allowTeamsConsumer = configuration.allowTeamsConsumer;
        this.allowTeamsConsumerInbound = configuration.allowTeamsConsumerInbound;
        this.allowTeamsSms = configuration.allowTeamsSms;
        this.allowedDomains = configuration.allowedDomains;
        this.allowedTrialTenantDomains = configuration.allowedTrialTenantDomains;
        this.blockAllSubdomains = configuration.blockAllSubdomains;
        this.blockedDomains = configuration.blockedDomains;
        this.configId = configuration.configId;
        this.configMetadata = configuration.configMetadata == null ? null : new ConfigMetadata(configuration.configMetadata);
        this.customizeFederation = configuration.customizeFederation;
        this.dataSource = configuration.dataSource;
        this.externalAccessWithTrialTenants = configuration.externalAccessWithTrialTenants;
        this.domainBlockingForMDOAdminsInTeams = configuration.domainBlockingForMDOAdminsInTeams;
        this.identity = configuration.identity;
        this.key = configuration.key == null ? null : new Key(configuration.key);
        this.restrictTeamsConsumerToExternalUserProfiles = configuration.restrictTeamsConsumerToExternalUserProfiles;
        this.sharedSipAddressSpace = configuration.sharedSipAddressSpace;
        this.treatDiscoveredPartnersAsUnverified = configuration.treatDiscoveredPartnersAsUnverified;
    }
}

