package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.products.microsoft.entra.model.EntraIDAuthenticationStrength;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;

import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.entra.model.EntraIDAuthenticationStrength.*;

/**
 * Implements remediation for MS.AAD.3.1v1: Configure phishing-resistant MFA.
 * This remediation creates a Conditional Access Policy that requires phishing-resistant
 * authentication methods for specified users and applications.
 */
@PolicyRemediator("MS.AAD.3.1v1")
public class EntraIDPhishingResistantMfaRemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Phishing-Resistant MFA Policy";
	private EntraIDAuthenticationStrength authenticationStrength = PHISHING_RESISTANT_MFA;

	/**
	 * Constructor with custom user configuration, authentication strength, and policy state.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 * @param authenticationStrength The authentication strength to enforce
	 * @param policyState The state of the policy (enabled, disabled, or audit mode)
	 */
	public EntraIDPhishingResistantMfaRemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);
		this.authenticationStrength = PHISHING_RESISTANT_MFA;
	}

	/**
	 * Creates the complete policy payload with authentication strength settings.
	 */
	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Grant controls with authentication strength for phishing-resistant MFA
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "OR");

		// Empty arrays for other controls
		grantControls.set("builtInControls", objectMapper.createArrayNode());
		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		// Simplified authentication strength reference using enum
		ObjectNode authStrength = objectMapper.createObjectNode();
		authStrength.put("id", authenticationStrength.getId());
		grantControls.set("authenticationStrength", authStrength);

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}

	/**
	 * Verifies the authentication strength policy exists.
	 * This would typically be called before creating a policy that references it.
	 *
	 * @return CompletableFuture<Boolean> - true if the auth strength policy exists, false otherwise
	 */
	public CompletableFuture<Boolean> verifyAuthStrengthPolicyExists() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AUTH_STRENGTH_ENDPOINT + "/" + authenticationStrength.getId())
						.build()
		).thenApply(response -> {
			if (response != null && response.has("id")) {
				logger.info("Verified {} policy exists", authenticationStrength.getDisplayName());
				return true;
			}
			logger.warn("{} policy not found", authenticationStrength.getDisplayName());
			return false;
		}).exceptionally(ex -> {
			logger.error("Error verifying authentication strength policy", ex);
			return false;
		});
	}
}