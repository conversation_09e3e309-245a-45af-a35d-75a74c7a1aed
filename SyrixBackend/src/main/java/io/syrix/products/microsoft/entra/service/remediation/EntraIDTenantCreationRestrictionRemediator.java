package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.utils.ProtocolConstants.AUTHORIZATION_POLICY;


/**
 * Implements remediation for MS.AAD.9.1v1: Restrict non-admin users from creating tenants.
 * <p>
 * This remediation ensures that only administrators can create new Azure AD tenants
 * by updating the 'allowedToCreateTenants' setting in the authorization policy.
 */
@PolicyRemediator("MS.AAD.9.1v1")
public class EntraIDTenantCreationRestrictionRemediator extends RemediatorBase {
	public static final String DEFAULT_USER_ROLE_PERMISSIONS = "defaultUserRolePermissions";
	public static final String ALLOWED_TO_CREATE_TENANTS = "allowedToCreateTenants";
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDTenantCreationRestrictionRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for tenant creation restriction using MS Graph REST API");

		return updateTenantCreationPolicy()
				.exceptionally(ex -> {
					logger.error("Exception while configuring tenant creation restriction policy", ex);
					return createFailureNode(this.objectMapper, ex.getMessage());
				});
	}

	private CompletableFuture<JsonNode> updateTenantCreationPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
							GraphRequest.builder()
									.v1()
									.withMethod(HttpMethod.PATCH)
									.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
									.withBody(policyBody)
									.withEndpoint(AUTHORIZATION_POLICY)
									.build()
					)
					.thenCompose(policyResult -> {
						if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
							logger.info("Tenant creation restriction policy updated successfully");
							return checkPolicyStatus().thenCompose(isRestricted -> {
								if (isRestricted) {
									return CompletableFuture.completedFuture(createSuccessNode(this.objectMapper,
											"Tenant creation has been restricted to admin users only"));
								} else {
									return CompletableFuture.completedFuture(createFailureNode(this.objectMapper,
											"Policy update was accepted but verification failed"));
								}
							});
						} else {
							String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
									? policyResult.get(Constants.ERROR_FIELD).asText()
									: "Unknown error updating tenant creation restriction policy";
							logger.error("Failed to update tenant creation restriction policy: {}", error);
							return CompletableFuture.completedFuture(createFailureNode(this.objectMapper, error));
						}
					});
		} catch (Exception e) {
			throw new GraphClientException("Failed to update tenant creation restriction policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode userPermissionsNode = objectMapper.createObjectNode();
		userPermissionsNode.put(ALLOWED_TO_CREATE_TENANTS, false);

		ObjectNode policyPayload = objectMapper.createObjectNode();
		policyPayload.set(DEFAULT_USER_ROLE_PERMISSIONS, userPermissionsNode);
		return policyPayload;
	}

	/**
	 * Checks the current status of tenant creation restriction policy.
	 *
	 * @return CompletableFuture<Boolean> true if tenant creation is restricted to admins
	 */
	public CompletableFuture<Boolean> checkPolicyStatus() {
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.v1()
								.withEndpoint(AUTHORIZATION_POLICY)
								.build()
				)
				.thenApply(response -> {
					boolean isRestricted = !response.path(DEFAULT_USER_ROLE_PERMISSIONS)
							.path(ALLOWED_TO_CREATE_TENANTS).asBoolean();
					logger.info("Tenant creation is {} restricted to admins", isRestricted ? "currently" : "not");
					return isRestricted;
				})
				.exceptionally(e -> {
					logger.error("Failed to check tenant creation restriction settings: {}", e.getMessage(), e);
					return false;
				});
	}
}