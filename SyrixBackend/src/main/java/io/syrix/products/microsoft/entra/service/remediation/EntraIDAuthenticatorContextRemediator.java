package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.DISABLED_VALUE;
import static io.syrix.common.constants.Constants.ENABLED_VALUE;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.STATE;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ALL;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.CA_POLICIES_ENDPOINT;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.STATE_ENABLED;

/**
 * Implements remediation for MS.AAD.3.3v1: If phishing-resistant MFA has not been enforced
 * and Microsoft Authenticator is enabled, it SHALL be configured to show login context information.
 * <p>
 * This remediation configures Microsoft Authenticator to show context information during login,
 * which helps users identify phishing attempts.
 */
@PolicyRemediator("MS.AAD.3.3v1")
public class EntraIDAuthenticatorContextRemediator extends RemediatorBase {
	private static final String AUTH_METHODS_POLICY_AUTHENTICATOR_ENDPOINT =
			"/policies/authenticationMethodsPolicy/authenticationMethodConfigurations/microsoftAuthenticator";
	private static final String PHISHING_RESISTANT_POLICY_NAME = "Phishing-Resistant MFA Policy";
	private static final String DISP_APP_INFO = "displayAppInformationRequiredState";
	private static final String FEATURE_SETTINGS = "featureSettings";
	private static final String INCLUDE_TARGETS = "includeTargets";
	private static final String TARGET_TYPE = "targetType";
	private static final String GROUP = "group";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	/**
	 * Constructor for the Authenticator Context remediation.
	 *
	 * @param graphClient Microsoft Graph API client
	 */
	public EntraIDAuthenticatorContextRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to configure Microsoft Authenticator to show login context");

		return isRemediationNeeded()
				.thenCompose(remediationNeeded -> {
					if (Boolean.FALSE.equals(remediationNeeded)) {
						logger.info("Remediation not needed: Either phishing-resistant MFA is already enforced " +
								"or Microsoft Authenticator is not enabled");
						return CompletableFuture.completedFuture(
								IPolicyRemediator.success(getPolicyId(),
										"No need to configure Authenticator context: phishing-resistant MFA " +
												"is already enforced or Authenticator is not enabled").join());
					}

					logger.info("Phishing-resistant MFA not enforced and Microsoft Authenticator is enabled. " +
							"Proceeding with Authenticator context configuration.");
					return enableAuthenticatorContext();
				})
				.exceptionally(ex -> {
					logger.error("Exception during Microsoft Authenticator context configuration", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if the remediation is needed by verifying:
	 * 1. Phishing-resistant MFA is not enforced
	 * 2. Microsoft Authenticator is enabled
	 *
	 * @return CompletableFuture<Boolean> - true if remediation is needed, false otherwise
	 */
	private CompletableFuture<Boolean> isRemediationNeeded() {
		// First check if phishing-resistant MFA is not enforced
		return isPhishingResistantMfaEnforced()
				.thenCompose(phishingResistantMfaEnforced -> {
					if (Boolean.TRUE.equals(phishingResistantMfaEnforced)) {
						logger.info("Phishing-resistant MFA is already enforced. No need to configure Authenticator context.");
						return CompletableFuture.completedFuture(false);
					}

					// Then check if Microsoft Authenticator is enabled
					return isMicrosoftAuthenticatorEnabled()
							.thenApply(authenticatorEnabled -> {
								if (Boolean.FALSE.equals(authenticatorEnabled)) {
									logger.info("Microsoft Authenticator is not enabled. No need to configure context.");
									return false;
								}

								logger.info("Phishing-resistant MFA is not enforced and Microsoft Authenticator is enabled. " +
										"Remediation is needed.");
								return true;
							});
				});
	}

	/**
	 * Checks if phishing-resistant MFA is enforced via a conditional access policy.
	 *
	 * @return CompletableFuture<Boolean> - true if enforced, false otherwise
	 */
	private CompletableFuture<Boolean> isPhishingResistantMfaEnforced() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.addQueryParam("$filter", "displayName eq '" + PHISHING_RESISTANT_POLICY_NAME + "'")
						.build()
		).thenApply(response -> {
			if (response.has(VALUE_FIELD) && response.get(VALUE_FIELD).isArray() && response.get(VALUE_FIELD).size() > 0) {
				// Check if the policy is enabled
				JsonNode policy = response.get(VALUE_FIELD).get(0);
				String state = policy.path(STATE).asText();

				// If the policy is enabled, consider phishing-resistant MFA enforced
				if (ENABLED_VALUE.equals(state)) {
					logger.info("Found enabled phishing-resistant MFA policy");
					return true;
				}

				logger.info("Found phishing-resistant MFA policy in state: {}", state);
				return false;
			}

			logger.info("No phishing-resistant MFA policy found");
			return false;
		});
	}

	/**
	 * Checks if Microsoft Authenticator is enabled in the tenant.
	 *
	 * @return CompletableFuture<Boolean> - true if enabled, false otherwise
	 */
	private CompletableFuture<Boolean> isMicrosoftAuthenticatorEnabled() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AUTH_METHODS_POLICY_AUTHENTICATOR_ENDPOINT)
						.build()
		).thenApply(response -> {
			if (response != null && response.has(STATE)) {
				String state = response.get(STATE).asText();
				boolean isEnabled = ENABLED_VALUE.equalsIgnoreCase(state);
				logger.info("Microsoft Authenticator is {}", isEnabled ? ENABLED_VALUE : DISABLED_VALUE);
				return isEnabled;
			}

			logger.warn("Could not determine Microsoft Authenticator state");
			return false;
		});
	}

	/**
	 * Enables the context-based authentication feature in Microsoft Authenticator.
	 *
	 * @return CompletableFuture<JsonNode> with the remediation result
	 */
	private CompletableFuture<JsonNode> enableAuthenticatorContext() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AUTH_METHODS_POLICY_AUTHENTICATOR_ENDPOINT)
						.build()
		).thenCompose(currentConfig -> {
			// Check if already configured correctly
			if (currentConfig.has(FEATURE_SETTINGS) &&
					currentConfig.get(FEATURE_SETTINGS).has(DISP_APP_INFO) &&
					isContextInformationRequired(currentConfig.get(FEATURE_SETTINGS).get(DISP_APP_INFO))) {

				logger.info("Context information already enabled");
				return CompletableFuture.completedFuture(
						IPolicyRemediator.success(getPolicyId(),
								"Microsoft Authenticator context information is already required").join());
			}

			// Create minimal configuration - only include what we need to change
			ObjectNode updatedConfig = objectMapper.createObjectNode();

			// Configure feature settings with the proper structure for displayAppInformationRequiredState
			ObjectNode featureSettings = objectMapper.createObjectNode();
			// According to Microsoft documentation, this should be the string "required"
			featureSettings.put(DISP_APP_INFO, "required");
			updatedConfig.set(FEATURE_SETTINGS, featureSettings);

			// Preserve essential fields from current config
			if (currentConfig.has(INCLUDE_TARGETS)) {
				updatedConfig.set(INCLUDE_TARGETS, currentConfig.get(INCLUDE_TARGETS));
			} else {
				// Add include targets if missing
				ArrayNode includeTargets = objectMapper.createArrayNode();
				ObjectNode target = objectMapper.createObjectNode();
				target.put(ID_FIELD, ALL);
				target.put(TARGET_TYPE, GROUP);
				includeTargets.add(target);
				updatedConfig.set(INCLUDE_TARGETS, includeTargets);
			}

			return updateAuthenticatorConfig(updatedConfig);
		});
	}

	/**
	 * Checks if context information is configured as required.
	 *
	 * @param node JsonNode representing displayAppInformationRequiredState
	 * @return true if required, false otherwise
	 */
	private boolean isContextInformationRequired(JsonNode node) {
		// Handle both string value and object with state/value
		if (node.isTextual()) {
			return "required".equals(node.asText());
		} else if (node.isObject() && node.has(STATE) && STATE_ENABLED.equals(node.get(STATE).asText())) {
			return true;
		}

		return false;
	}

	/**
	 * Updates the Microsoft Authenticator configuration.
	 *
	 * @param updatedConfig The updated configuration JSON
	 * @return CompletableFuture<JsonNode> with the remediation result
	 */
	private CompletableFuture<JsonNode> updateAuthenticatorConfig(ObjectNode updatedConfig) {
		try {
			String configJson = objectMapper.writeValueAsString(updatedConfig);
			HttpRequest.BodyPublisher requestBody = HttpRequest.BodyPublishers.ofString(configJson);

			logger.info("Sending authentication method configuration update: {}", configJson);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.PATCH)
							.withEndpoint(AUTH_METHODS_POLICY_AUTHENTICATOR_ENDPOINT)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(requestBody)
							.build()
			).thenApply(response -> {
				logger.info("Successfully enabled Microsoft Authenticator context information");
				return IPolicyRemediator.success(getPolicyId(),
						"Microsoft Authenticator has been configured to show login context information").join();
			}).exceptionally(ex -> {
				logger.error("Failed to update Microsoft Authenticator configuration: {}", ex.getMessage());
				return IPolicyRemediator.failed(getPolicyId(),
						"Failed to enable context information in Microsoft Authenticator: " +
								ex.getMessage()).join();
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to serialize Microsoft Authenticator configuration", e);
		}
	}
}