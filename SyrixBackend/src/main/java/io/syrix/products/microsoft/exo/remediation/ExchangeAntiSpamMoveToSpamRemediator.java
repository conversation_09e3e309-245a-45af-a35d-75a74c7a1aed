package io.syrix.products.microsoft.exo.remediation;

import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;


import java.util.Map;

@PolicyRemediator("MS.EXO.14.2v1")
public class ExchangeAntiSpamMoveToSpamRemediator extends ExchangeAntiSpamRemediatorBase {
    public ExchangeAntiSpamMoveToSpamRemediator(MicrosoftGraphClient graphClient,
                                                PowerShellClient exchangeClient,
                                                ObjectNode configNode,
                                                ExchangeRemediationContext remediationContext,
                                                ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
    }
    public ExchangeAntiSpamMoveToSpamRemediator(MicrosoftGraphClient graphClient,
                                                PowerShellClient exchangeClient) {
        super(graphClient, exchangeClient);
    }
    @Override
    protected Map<String, Object> getAntispamPolicy() {
        return Map.of(
            ExoConstants.IDENTITY, "Default",
            SPAM_ACTION, QUARANTINE,
            HIGH_CONFIDENCE_SPAM_ACTION, QUARANTINE
        );
    }
}

