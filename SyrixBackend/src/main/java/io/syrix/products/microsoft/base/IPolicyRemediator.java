package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.POLICY_ID_FIELD;

public interface IPolicyRemediator {

    String RESULT = "result";
    String DESC = "desc";

    /**
     * Gets the policy ID from the annotation.
     */
    String getPolicyId();

    /**
     * Remediates a specific policy violation.
     */
    CompletableFuture<JsonNode> remediate();

    default CompletableFuture<PolicyChangeResult> remediate_() {
        return null;
    }

    /**
     * Helper method to get policy ID from annotation.
     */
//    static String getPolicyIdFromAnnotation(Class<?> clazz) {
//        PolicyRemediator annotation = clazz.getAnnotation(PolicyRemediator.class);
//        if (annotation == null) {
//            throw new IllegalStateException("Remediator must be annotated with @PolicyRemediator");
//        }
//        // Try policyId first, fall back to value if policyId is empty
//        String policyId = annotation.policyId();
//        return !policyId.isEmpty() ? policyId : "";
//    }

    static CompletableFuture<JsonNode> requirementMet(String policyId) {
        return requirementMet(policyId, "requirementMet");
    }
    static PolicyChangeResult requirementMet_(String policyId) {
        return requirementMet_(policyId, "requirementMet");
    }

    static CompletableFuture<JsonNode> requirementMet(String policyId, String desc) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.REQUIREMENT_MET.name());
        response.put(DESC, desc);
        return CompletableFuture.completedFuture(response);
    }

    static PolicyChangeResult requirementMet_(String policyId, String desc) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc(desc)
                .result(RemediationResult.REQUIREMENT_MET);
    }

    static CompletableFuture<JsonNode> notImplemented(String policyId) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.NOT_IMPLEMENT.name());
        response.put(DESC, "notImplement");
        return CompletableFuture.completedFuture(response);
    }
    static PolicyChangeResult notImplement_(String policyId) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc("notImplement")
                .result(RemediationResult.NOT_IMPLEMENT);
    }

    static CompletableFuture<JsonNode> success(String policyId, String desc) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.SUCCESS.name());
        response.put(DESC, desc);
        return CompletableFuture.completedFuture(response);
    }

    static PolicyChangeResult success_(String policyId, String desc) {
        return success_(policyId, desc, Collections.emptyList());
    }

    static PolicyChangeResult success_(String policyId, String desc, List<ParameterChangeResult> parameterChangeResults) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc(desc)
                .result(RemediationResult.SUCCESS)
                .changes(parameterChangeResults);
    }


    static CompletableFuture<JsonNode> partial_success(String policyId, String desc) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.PARTIAL_SUCCESS.name());
        response.put(DESC, desc);
        return CompletableFuture.completedFuture(response);
    }

    static PolicyChangeResult partial_success_(String policyId, String desc) {
        return partial_success_(policyId, desc, Collections.emptyList());
    }

    static PolicyChangeResult partial_success_(String policyId, String desc, List<ParameterChangeResult> parameterChangeResult) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc(desc)
                .result(RemediationResult.PARTIAL_SUCCESS)
                .changes(parameterChangeResult);
    }

    static CompletableFuture<JsonNode> unknown(String policyId, String desc) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.UNKNOWN.name());
        response.put(DESC, desc);
        return CompletableFuture.completedFuture(response);
    }

    static PolicyChangeResult unknown_(String policyId, String desc) {
        return unknown_(policyId, desc, Collections.emptyList());
    }

    static PolicyChangeResult unknown_(String policyId, String desc, List<ParameterChangeResult> parameterChangeResult) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc(desc)
                .result(RemediationResult.UNKNOWN)
                .changes(parameterChangeResult);
    }


    static CompletableFuture<JsonNode> failed(String policyId, String desc) {
        ObjectNode response = JsonNodeFactory.instance.objectNode();
        response.put(POLICY_ID_FIELD, policyId);
        response.put(RESULT, RemediationResult.FAILED.name());
        response.put(DESC, desc);
        return CompletableFuture.completedFuture(response);
    }

    static PolicyChangeResult failed_(String policyId, String desc) {
        return failed_(policyId, desc, Collections.emptyList());
    }

    static PolicyChangeResult failed_(String policyId, String desc, List<ParameterChangeResult> changeResults) {
        return PolicyChangeResult.newIns()
                .policyId(policyId)
                .desc(desc)
                .result(RemediationResult.FAILED)
                .changes(changeResults);
    }


}
