package io.syrix.products.microsoft.powerplatform;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.MSEnvironment;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

/**
 * Client for Microsoft Power Platform API interactions.
 * Handles authentication and API calls for PowerApps and Power Automate.
 */
public class PowerPlatformClient implements AutoCloseable {
	private static final String API_VERSION = "2022-03-01-preview";
	private static final int MAX_CONCURRENT_REQUESTS = 50;

	private final HttpClient httpClient;
	private final ObjectMapper objectMapper;
	private final String accessToken;
	private final MSEnvironment environment;
	private final Duration requestTimeout;
	private final ExecutorService executor;
	private final Semaphore requestSemaphore;

	private PowerPlatformClient(Builder builder) {
		this.accessToken = Objects.requireNonNull(builder.accessToken, "Access token cannot be null");
		this.environment = Objects.requireNonNull(builder.environment, "Environment cannot be null");
		this.objectMapper = new ObjectMapper();
		this.requestTimeout = builder.requestTimeout;
		this.executor = Executors.newVirtualThreadPerTaskExecutor();
		this.requestSemaphore = new Semaphore(MAX_CONCURRENT_REQUESTS);

		this.httpClient = HttpClient.newBuilder()
				.executor(executor)
				.connectTimeout(builder.connectTimeout)
				.version(HttpClient.Version.HTTP_2)
				.build();
	}

	/**
	 * Gets environment creation settings.
	 */
	public CompletableFuture<JsonNode> getEnvironmentCreationSettings() {
		return makeRequest("/providers/Microsoft.PowerPlatform/environmentCreationSettings");
	}

	/**
	 * Gets DLP policies.
	 */
	public CompletableFuture<JsonNode> getDlpPolicies() {
		return makeRequest("/providers/Microsoft.PowerPlatform/policies");
	}

	/**
	 * Gets tenant isolation settings.
	 */
	public CompletableFuture<JsonNode> getTenantIsolationSettings() {
		return makeRequest("/providers/Microsoft.PowerPlatform/tenantSettings/isolation");
	}

	/**
	 * Gets list of environments.
	 */
	public CompletableFuture<JsonNode> getEnvironments() {
		return makeRequest("/providers/Microsoft.PowerPlatform/environments");
	}

	/**
	 * Makes an authenticated request to the Power Platform API.
	 */
	private CompletableFuture<JsonNode> makeRequest(String endpoint) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				// Acquire semaphore before making request
				requestSemaphore.acquire();

				String baseUrl = environment.getMSPowerEndpoint();
				String url = String.format("%s%s?api-version=%s", baseUrl, endpoint, API_VERSION);

				HttpRequest request = HttpRequest.newBuilder()
						.uri(URI.create(url))
						.header("Authorization", "Bearer " + accessToken)
						.header("Content-Type", "application/json")
						.timeout(requestTimeout)
						.GET()
						.build();

				HttpResponse<String> response = httpClient.send(
						request,
						HttpResponse.BodyHandlers.ofString()
				);

				if (response.statusCode() == 429) {
					handleRateLimit(response);
					throw new GraphClientException("Rate limit hit, retrying");
				}

				if (response.statusCode() != 200) {
					throw new GraphClientException("Request failed with status code: " + response.statusCode());
				}

				return objectMapper.readTree(response.body());

			} catch (Exception e) {
				throw new ConfigurationExportException("Failed to make Power Platform API request", e);
			} finally {
				requestSemaphore.release();
			}
		}, executor);
	}

	private void handleRateLimit(HttpResponse<String> response) throws InterruptedException {
		long retryAfter = Long.parseLong(
				response.headers()
						.firstValue("Retry-After")
						.orElse("5")
		);
		Thread.sleep(retryAfter * 1000);
	}

	@Override
	public void close() {
		executor.shutdown();
	}

	public static Builder builder() {
		return new Builder();
	}

	/**
	 * Builder for PowerPlatformClient configuration.
	 */
	public static class Builder {
		private String accessToken;
		private MSEnvironment environment = MSEnvironment.COMMERCIAL;
		private Duration requestTimeout = Duration.ofMinutes(5);
		private Duration connectTimeout = Duration.ofSeconds(30);
		private String clientId;
		private String tenantId;
		private String certificatePath;
		private char[] certificatePassword;

		public Builder withAccessToken(String accessToken) {
			this.accessToken = accessToken;
			return this;
		}

		public Builder withEnvironment(MSEnvironment environment) {
			this.environment = environment;
			return this;
		}

		public Builder withRequestTimeout(Duration timeout) {
			this.requestTimeout = timeout;
			return this;
		}

		public Builder withConnectTimeout(Duration timeout) {
			this.connectTimeout = timeout;
			return this;
		}

		public PowerPlatformClient build() {
			return new PowerPlatformClient(this);
		}

		public Builder withAppId(String clientId) {
			this.clientId = clientId;
			return this;
		}

		public Builder withTenantId(String tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder withCertificatePath(String certificatePath) {
			this.certificatePath = certificatePath;
			return this;
		}

		public Builder withCertificatePassword(char[] certificatePassword) {
			this.certificatePassword = certificatePassword;
			return this;
		}
	}
}