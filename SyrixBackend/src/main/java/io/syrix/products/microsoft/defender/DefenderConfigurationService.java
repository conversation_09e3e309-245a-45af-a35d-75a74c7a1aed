package io.syrix.products.microsoft.defender;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.products.microsoft.base.BaseConfigurationService;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static io.syrix.products.microsoft.defender.DefenderConstants.GET_ATP_POLICY_FOR_O365;

public class DefenderConfigurationService extends BaseConfigurationService {
	private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);
	private final PowerShellClient powerShellClient;

	public DefenderConfigurationService(MicrosoftGraphClient graphClient, PowerShellClient powershellClient) {
		this(graphClient, powershellClient, new ObjectMapper(), new MetricsCollector());
	}

	public DefenderConfigurationService(
			MicrosoftGraphClient graphClient,
			PowerShellClient powershellClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.powerShellClient = powershellClient;
	}

	/**
	 * Exports the complete Microsoft 365 Defender configuration.
	 */
	public ConfigurationResult exportConfiguration() {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting Defender configuration export at {}", startTime);

		try {
			Map<String, CompletableFuture<?>> futures = new HashMap<>();
			CompletableFuture<String> cpps = getCPPSEndpoint();
			futures.put("protection_policy_rules", getProtectionPolicyRule());
			CompletableFuture<JsonNode> deffender = getAdvancedThreatProtectionPolicies(cpps);
			futures.put("atp_policy_for_o365", deffender);
			futures.put("atp_policy_rules", getATPProtectionPolicyRule(deffender));
			futures.put("dlp_compliance_policies", getDLPCompliancePolicy(cpps));
			CompletableFuture<JsonNode> dlpComplianceRules = getDLPComplianceRules(cpps);
			futures.put("dlp_compliance_rules",dlpComplianceRules);
			futures.put("defender_dlp_license", getDLPLicense(dlpComplianceRules));
			futures.put("anti_phish_policies", getAntiPhishingPolicies());
			futures.put("protection_alerts", getProtectionAlerts(cpps));
			futures.put("admin_audit_log_config", getAdminAuditConfig());
			futures.put("total_users_without_advanced_audit", getUsersWithoutAdvancedAuditCount());
			futures.put("defender_license", getDefenderLicense(deffender));

			ConfigurationResult result = waitForFutures(futures)
					.thenApply(map -> {
						map.put("defender_successful_commands", getSuccessfulCommands());
						map.put("defender_unsuccessful_commands", getUnsuccessfulCommands());
						return buildConfigurationResult(map, SERVICE_VERSION, ConfigurationServiceType.DEFENDER);
					}).get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordExportSuccess(duration);
			logger.info("Defender configuration export completed in {} seconds", duration.toSeconds());

			return result;
		} catch (Exception e) {
			metrics.recordExportFailure();
			logger.error("Defender configuration export failed", e);
			throw new ConfigurationExportException("Defender configuration export failed", e);
		}
	}

	private CompletableFuture<String> getCPPSEndpoint() {
		return this.powerShellClient.getCPPSEndpoint();
	}

	private CompletableFuture<JsonNode> getDLPLicense(CompletableFuture<JsonNode> dlpComplianceRules) {
		return dlpComplianceRules.thenCompose(data -> {
			if (!data.isEmpty()) {
				return CompletableFuture.completedFuture(objectMapper.valueToTree("true"));
			}
			return CompletableFuture.completedFuture(objectMapper.valueToTree("false"));
		});
	}

	private CompletableFuture<JsonNode> getDefenderLicense(CompletableFuture<JsonNode> deffender) {
		return deffender.thenCompose(data -> {
			if (!data.isEmpty()) {
				return CompletableFuture.completedFuture(objectMapper.valueToTree("true"));
			}
			return CompletableFuture.completedFuture(objectMapper.valueToTree("false"));
		});
	}

	private CompletableFuture<Integer> getUsersWithoutAdvancedAuditCount() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/users/$count")
						.addQueryParam("$filter",
								"not assignedPlans/any(a:a/servicePlanId eq 2f442157-a11c-46b9-ae5b-6e39ff4e5849 and a/capabilityStatus eq 'Enabled')")
						.withMethod(HttpMethod.GET)
						.build()).thenApply(JsonNode::asInt), "Get-MgBetaUser");
	}

	private CompletableFuture<JsonNode> getDLPComplianceRules(CompletableFuture<String> cpps) {
		return cpps.thenCompose(cppsurl -> {
			PowerShellClient.CommandRequest remoteDomainRequest =
					new PowerShellClient.CommandRequest("Get-DlpComplianceRule",
							PowerShellClient.DEFAULT_PARAMETERS, cppsurl);
			return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
					remoteDomainRequest.getCmdletName())
					.exceptionally(e -> {
						logger.error("Failed to call DlpComplianceRule {}", e.getMessage());
						return objectMapper.createArrayNode();
					});
		});
	}

	private CompletableFuture<?> getDLPCompliancePolicy(CompletableFuture<String> cpps) {
		return cpps.thenCompose(cppsurl -> {
			PowerShellClient.CommandRequest remoteDomainRequest =
					new PowerShellClient.CommandRequest("Get-DlpCompliancePolicy",
							PowerShellClient.DEFAULT_PARAMETERS, cppsurl);
			return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
					remoteDomainRequest.getCmdletName())
					.exceptionally(e -> {
						logger.error("Failed to call getDLPCompliancePolicy {}", e.getMessage());
						return objectMapper.createArrayNode();
					});
		});
	}

	private CompletableFuture<JsonNode> getATPProtectionPolicyRule(CompletableFuture<JsonNode> deffender) {
		return deffender.thenCompose(data -> {
			if (!data.isEmpty()) {
				PowerShellClient.CommandRequest remoteDomainRequest =
						new PowerShellClient.CommandRequest("Get-ATPProtectionPolicyRule",
								PowerShellClient.DEFAULT_PARAMETERS);
				return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
						remoteDomainRequest.getCmdletName())
						.exceptionally(e -> {
							logger.error("Failed to call getATPProtectionPolicyRule {}", e.getMessage());
							return objectMapper.createArrayNode();
						});
			}
			return CompletableFuture.completedFuture(objectMapper.createArrayNode());
		});
	}

	private CompletableFuture<JsonNode> getProtectionPolicyRule() {
		PowerShellClient.CommandRequest remoteDomainRequest =
				new PowerShellClient.CommandRequest(DefenderConstants.GET_EOP_PROTECTION_POLICY_RULE,
						PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
				remoteDomainRequest.getCmdletName()).exceptionally(e -> {
			logger.error("Failed to call getProtectionPolicyRule {}", e.getMessage());
			return objectMapper.createArrayNode();
		});
	}

	private CompletableFuture<JsonNode> getAntiPhishingPolicies() {
		PowerShellClient.CommandRequest remoteDomainRequest =
				new PowerShellClient.CommandRequest("Get-AntiPhishPolicy",
						PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
				remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call AntiPhishPolicy {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getAdvancedThreatProtectionPolicies(CompletableFuture<String> cpps) {
		return cpps.thenCompose( cppsurl -> {
			PowerShellClient.CommandRequest remoteDomainRequest =
					new PowerShellClient.CommandRequest(GET_ATP_POLICY_FOR_O365,
							PowerShellClient.DEFAULT_PARAMETERS);
			return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
					remoteDomainRequest.getCmdletName())
					.exceptionally(e -> {
						logger.error("Failed to call getAdvancedThreatProtectionPolicies {}", e.getMessage());
						return objectMapper.createArrayNode();
					});
		});
	}

	private CompletableFuture<JsonNode> getProtectionAlerts(CompletableFuture<String> cpps) {
		return cpps.thenCompose(cppsurl -> {
			PowerShellClient.CommandRequest remoteDomainRequest =
					new PowerShellClient.CommandRequest("Get-ProtectionAlert",
							PowerShellClient.DEFAULT_PARAMETERS, cppsurl);
			return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
					remoteDomainRequest.getCmdletName())
					.exceptionally(e -> {
						logger.error("Failed to call getProtectionAlerts {}", e.getMessage());
						return objectMapper.createArrayNode();
					});

		});
	}

	private CompletableFuture<JsonNode> getAdminAuditConfig() {
		PowerShellClient.CommandRequest remoteDomainRequest =
				new PowerShellClient.CommandRequest("Get-AdminAuditLogConfig",
						PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest),
				remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getAdminAuditConfig {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private ObjectNode buildMetadata() {
		ObjectNode metadata = objectMapper.createObjectNode();
		metadata.put("version", "1.0");
		metadata.put("generated_at", Instant.now().toString());
		metadata.put("environment", graphClient.getEnvironment().toString());
		return metadata;
	}
}