package io.syrix.products.microsoft.exo.remediation.context;

import java.util.List;

//Here we will store data from the configuration file for transfer between the remediators for transferring changed data (those for synchronization).
public class ExchangeRemediationContext {
	private List<SharingPolicy> sharingPolicies;

	public List<SharingPolicy> getSharingPolicies() {
		return sharingPolicies;
	}

	public void setSharingPolicies(List<SharingPolicy> sharingPolicies) {
		this.sharingPolicies = sharingPolicies;
	}
}
