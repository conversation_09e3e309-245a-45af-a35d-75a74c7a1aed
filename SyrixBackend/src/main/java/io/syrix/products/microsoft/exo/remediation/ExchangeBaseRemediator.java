package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.util.Collections;
import java.util.List;

public abstract class ExchangeBaseRemediator extends RemediatorBase {
	protected final PowerShellClient exchangeClient;
	protected final ObjectNode configNode;
	protected final ExchangeRemediationConfig remediationConfig;
	protected final ObjectMapper jsonMapper;
	protected ExchangeRemediationContext remediationContext;
	protected MicrosoftGraphClient graphClient;

	public ExchangeBaseRemediator(MicrosoftGraphClient graphClient,
								  PowerShellClient exchangeClient,
								  ObjectNode configNode,
								  ExchangeRemediationContext remediationContext,
								  ExchangeRemediationConfig remediationConfig) {
		this.graphClient = graphClient;
		this.exchangeClient = exchangeClient;
		this.configNode = configNode;
		this.remediationConfig = remediationConfig;
		this.remediationContext = remediationContext;
		this.jsonMapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	protected List<ConnectionPolicy> loadConnectionPolicy() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Connection Policies");
			return Collections.emptyList();
		}

		JsonNode policies = configNode.get(ExoConstants.CONFIG_KEY_CONN_FILTER);

		if (policies == null || !policies.isArray()) {
			logger.warn("Connection filter policy '{}' node not found or not an array", ExoConstants.CONFIG_KEY_CONN_FILTER);
			return Collections.emptyList();
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, ConnectionPolicy.class);
		return jsonMapper.convertValue(policies, collectionType);
	}

	protected OrganizationConfig loadOrganizationConfig() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Organization Config");
			return null;
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_ORGANIZATION);

		if (config == null) {
			logger.warn("Organization Configs '{}' node not found", ExoConstants.CONFIG_KEY_ORGANIZATION);
			return null;
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, OrganizationConfig.class);
		List<OrganizationConfig> configs = jsonMapper.convertValue(config, collectionType);
		return configs.getFirst();
	}

	protected List<SharedMailbox> loadSharedMailbox() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Organization Config");
			return Collections.emptyList();
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_SHARED_MAILBOX);

		if (config == null) {
			logger.warn("Shared Mailbox with sign-in '{}' node not found", ExoConstants.CONFIG_KEY_SHARED_MAILBOX);
			return Collections.emptyList();
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, SharedMailbox.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	/**
	 * Loads malware policies from configuration JSON.
	 *
	 * @return List of malware policies
	 */
	protected List<MalwarePolicy> getMalwarePolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Malware Policies");
			return null;
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_MALWARE_POLICY);
		if (config == null || !config.isArray()) {
			logger.warn("Malware filter policy '{}' node not found or not an array", ExoConstants.CONFIG_KEY_MALWARE_POLICY);
			return null;
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, MalwarePolicy.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	/**
	 * Loads malware rules from configuration JSON.
	 *
	 * @return List of malware rules
	 */
	protected List<MalwareRule> getMalwareRules() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Malware Policies");
			return null;
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_MALWARE_RULE);
		if (config == null || !config.isArray()) {
			logger.warn("Malware filter policy '{}' node not found or not an array", ExoConstants.CONFIG_KEY_MALWARE_RULE);
			return null;
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, MalwareRule.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class SharedMailbox {
		@JsonProperty("id")
		public String id;
		@JsonProperty("displayName")
		public String displayName;
		@JsonProperty("accountEnabled")
		public Boolean accountEnabled;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class OrganizationConfig {
		public Boolean auditDisabled;
		public Boolean customerLockboxEnabled;
		public Boolean oauth2ClientProfileEnabled;
		public Boolean mailTipsAllTipsEnabled;
		public Boolean mailTipsExternalRecipientsTipsEnabled;
		public Boolean mailTipsGroupMetricsEnabled;
		public Boolean mailTipsMailboxSourcedTipsEnabled;
		public Integer mailTipsLargeAudienceThreshold;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class ConnectionPolicy {
		public String identity;
		public boolean enableSafeList;
		@JsonProperty("IPAllowList")
		public String[] ipAllowList;
	}

	/**
	 * Helper class to represent a malware policy configuration.
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class MalwarePolicy {
		public String identity;
		public String name;
		public String adminDisplayName;
		public Boolean enableFileFilter;
		public Boolean zapEnabled;
		public String fileTypeAction;
		public List<String> fileTypes;
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class MalwareRule {
		public String identity;
		public String name;
		public Integer priority;
		public String malwareFilterPolicy;
		public List<String> recipientDomainIs;
		public List<String>  sentTo;
		public List<String>  sentToMemberOf;
	}
}