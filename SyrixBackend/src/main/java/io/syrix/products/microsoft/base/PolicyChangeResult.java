package io.syrix.products.microsoft.base;

import java.util.List;

public class PolicyChangeResult {
	private Integer executeOrder;
	private String policyId;
	private RemediationResult result;
	private String desc;
	private List<ParameterChangeResult> changes;

	public static PolicyChangeResult newIns() {
		return new PolicyChangeResult();
	}

	public Integer getExecuteOrder() {
		return executeOrder;
	}

	public PolicyChangeResult executeOrder(Integer executeOrder) {
		this.executeOrder = executeOrder;
		return this;
	}

	public String getPolicyId() {
		return policyId;
	}

	public PolicyChangeResult policyId(String policyId) {
		this.policyId = policyId;
		return this;
	}

	public RemediationResult getResult() {
		return result;
	}

	public PolicyChangeResult result(RemediationResult result) {
		this.result = result;
		return this;
	}

	public String getDesc() {
		return desc;
	}

	public PolicyChangeResult desc(String desc) {
		this.desc = desc;
		return this;
	}

	public List<ParameterChangeResult> getChanges() {
		return changes;
	}

	public PolicyChangeResult changes(List<ParameterChangeResult> changes) {
		this.changes = changes;
		return this;
	}
}
