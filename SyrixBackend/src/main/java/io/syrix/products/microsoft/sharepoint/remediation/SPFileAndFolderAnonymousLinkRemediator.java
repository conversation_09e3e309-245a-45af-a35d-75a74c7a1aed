package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AnonymousLinkType;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingCapability;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.FILE_ANONYMOUS_LINK_TYPE_PROPERTY;
import static io.syrix.products.microsoft.sharepoint.SharepointConstants.FOLDER_ANONYMOUS_LINK_TYPE_PROPERTY;

/*
    This policy only applies if Anonymous access links are enabled for the organization.
    tenant.sharingCapability == ExternalUserAndGuestSharing
 */
@PolicyRemediator("MS.SHAREPOINT.3.2v1")
public class SPFileAndFolderAnonymousLinkRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
	private static final Logger log = LoggerFactory.getLogger(SPFileAndFolderAnonymousLinkRemediator.class);

	public SPFileAndFolderAnonymousLinkRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
		super(client, tenant, spConfig);
	}
	// constructor for Rollback interface
	public SPFileAndFolderAnonymousLinkRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
		this(client, tenant, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		try {
			var validResult = validateTenant();
			if (validResult != null) {
				return CompletableFuture.completedFuture(validResult);
			}

			AnonymousLinkType prevToFile = AnonymousLinkType.fromInt(tenant.fileAnonymousLinkType);
			AnonymousLinkType prevToFolder = AnonymousLinkType.fromInt(tenant.folderAnonymousLinkType);
			return runCommand(AnonymousLinkType.VIEW, prevToFile, AnonymousLinkType.VIEW, prevToFolder);
		} catch (Exception ex) {
			log.error("Remediate the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Remediate the policy {} failed:"+ ex.getMessage()));
		}
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			Map<String, ParameterChangeResult> changeResults = fixResult.getChanges().stream().collect(Collectors.toMap(ParameterChangeResult::getParameter, v -> v));

			AnonymousLinkType toFile = AnonymousLinkType.valueOf(changeResults.get(FILE_ANONYMOUS_LINK_TYPE_PROPERTY).getNewValue().toString());
			AnonymousLinkType prevToFile = AnonymousLinkType.valueOf(changeResults.get(FILE_ANONYMOUS_LINK_TYPE_PROPERTY).getPrevValue().toString());

			AnonymousLinkType toFolder = AnonymousLinkType.valueOf(changeResults.get(FOLDER_ANONYMOUS_LINK_TYPE_PROPERTY).getNewValue().toString());
			AnonymousLinkType prevToFolder = AnonymousLinkType.valueOf(changeResults.get(FOLDER_ANONYMOUS_LINK_TYPE_PROPERTY).getPrevValue().toString());
			var validResult = validateTenant();
			if (validResult != null) {
				return CompletableFuture.completedFuture(validResult);
			}

			return runCommand(prevToFile, toFile, prevToFolder, toFolder);
		} catch (Exception ex) {
			log.error("Remediate the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),"Remediate the policy failed:"+ ex.getMessage()));
		}
	}

	private PolicyChangeResult validateTenant() {
		SharingCapability sharing = SharingCapability.fromInt(tenant.sharingCapability);
		if (!SharingCapability.EXTERNAL_USER_AND_GUEST_SHARING.equals(sharing)) {
			String msg = MessageFormat.format("Remediate the policy {0} is not accepted. sharingCapability should be {1} but currently, it has the value {2}",
					getPolicyId(), SharingCapability.EXTERNAL_USER_AND_GUEST_SHARING.name(), sharing.name());

			log.warn(msg);
			return IPolicyRemediator.requirementMet_(getPolicyId(), msg);
		}
		return null;
	}

	private CompletableFuture<PolicyChangeResult> runCommand(AnonymousLinkType toFile,AnonymousLinkType prevToFile, AnonymousLinkType toFolder, AnonymousLinkType prevToFolder) {
		SPShellCommand<GeneralResult> fileCommand = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, FILE_ANONYMOUS_LINK_TYPE_PROPERTY, toFile, prevToFile);
		SPShellCommand<GeneralResult> folderCommand = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, FOLDER_ANONYMOUS_LINK_TYPE_PROPERTY, toFolder, prevToFolder);
		return Retry.executeWithRetry(() -> client.execute_(fileCommand), MAX_RETRY)
				.thenApply(this::checkResult)
				.thenApply(res -> updateTenant(res, t -> t.fileAnonymousLinkType = toFile.asInt()))
				.thenCompose(fileResult ->
						Retry.executeWithRetry(() -> client.execute_(folderCommand), MAX_RETRY)
								.thenApply(this::checkResult)
								.thenApply(res -> updateTenant(res, t -> t.folderAnonymousLinkType = toFolder.asInt()))
								.thenApply(folderResult -> mergeResult(fileResult, folderResult, toFile, toFolder))
								.exceptionally(folderEx ->
										mergeResult(fileResult, IPolicyRemediator.failed_(getPolicyId(), "", Collections.emptyList()), toFile, toFolder)
								)
				)
				.exceptionally(fileEx -> {
					log.error("First command failed for policy {}", getPolicyId(), fileEx);
					return IPolicyRemediator.failed_(getPolicyId(), "First command failed: " + fileEx.getMessage(), null);
				});
	}

	protected PolicyChangeResult updateTenant(PolicyChangeResult res, Consumer<SharePointTenantProperties> action) {
		Optional.of(res)
				.filter(r -> res.getResult() == RemediationResult.SUCCESS)
				.ifPresent(r -> action.accept(tenant));
		return res;
	}


	private PolicyChangeResult mergeResult(PolicyChangeResult fileResult, PolicyChangeResult folderResult, AnonymousLinkType toFile, AnonymousLinkType toFolder) {
		RemediationResult fileStatus = fileResult.getResult();
		RemediationResult folderStatus = folderResult.getResult();

		List<ParameterChangeResult> changeResults = new ArrayList<>();
		changeResults.addAll(fileResult.getChanges());
		changeResults.addAll(folderResult.getChanges());

		if (fileStatus == folderStatus) {
			return switch (fileStatus) {
				case SUCCESS ->
						IPolicyRemediator.success_(getPolicyId(), "File and Folder AnonymousLinkType set:" + toFile.name() + " and " + toFolder.name(), changeResults);
				case FAILED ->
						IPolicyRemediator.failed_(getPolicyId(), "Cannot set File and Folder AnonymousLinkType " + toFile.name() + " and " + toFolder.name(), changeResults);
				case UNKNOWN ->
						IPolicyRemediator.unknown_(getPolicyId(), "Unknown result while setting File and Folder AnonymousLinkType " + toFile.name() + " and " + toFolder.name(), changeResults);
				default -> throw new IllegalStateException("Unexpected value: " + fileStatus);
			};
		}

		if (fileStatus == RemediationResult.SUCCESS || folderStatus == RemediationResult.SUCCESS) {
			String message = String.format(
					"%s AnonymousLinkType set to VIEW and %s result set for %s AnonymousLinkType ",
					fileStatus == RemediationResult.SUCCESS ? "File" : "Folder",
					fileStatus == RemediationResult.SUCCESS ? folderStatus.name() : fileStatus.name(),
					fileStatus == RemediationResult.SUCCESS ? "Folder" : "File"
			) + toFile.name() + " and " + toFolder.name();
			return IPolicyRemediator.partial_success_(getPolicyId(), message, changeResults);
		}

		return IPolicyRemediator.unknown_(getPolicyId(), "Empty or unrecognized result", changeResults);
	}


}
