package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.5.2v1 that enforces Modern Authentication for Exchange Online.
 * <p>
 * This class implements the following security controls:
 * - Enables OAuth2ClientProfileEnabled to enforce Modern Authentication
 * - Disables legacy authentication protocols (Basic Auth) for Outlook clients
 * - Verifies the current authentication configuration before applying changes
 * - Validates that the configuration was successfully applied
 * <p>
 * Modern Authentication provides:
 * - OAuth 2.0 and multi-factor authentication support
 * - Enhanced security over legacy Basic Authentication
 * - Compliance with CIS Microsoft 365 Benchmark 6.5.1
 * - Protection against credential theft and replay attacks
 */
@PolicyRemediator("MS.EXO.5.2v1")
public class ExchangeModernAuthRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	public ExchangeModernAuthRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeModernAuthRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (Modern Authentication)", getPolicyId());

		OrganizationConfig orgConfig = loadOrganizationConfig();
		if (orgConfig == null) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_ORGANIZATION_CONFIG));
		}

		// Check if Modern Authentication is already enabled
		if (orgConfig.oauth2ClientProfileEnabled != null && orgConfig.oauth2ClientProfileEnabled) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		// Enable Modern Authentication
		return updateConfiguration(true, orgConfig.oauth2ClientProfileEnabled != null ? orgConfig.oauth2ClientProfileEnabled : false);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (Modern Authentication)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			ParameterChangeResult change = changes.getFirst();
			if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
				logger.error("Rollback skipped for policy: {}", getPolicyId());
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback skipped", changes));
			}

			boolean prevValue = Boolean.parseBoolean(change.getPrevValue().toString());
			boolean newValue = Boolean.parseBoolean(change.getNewValue().toString());

			return updateConfiguration(prevValue, newValue);
		} catch (Exception ex) {
			logger.error("Rollback failed for policy {}", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> updateConfiguration(boolean newValue, boolean prevValue) {
		logger.info("Updating Modern Authentication configuration to {}", newValue);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED)
				.prevValue(prevValue)
				.newValue(newValue);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED, newValue);
		parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(ExoConstants.SET_ORGANIZATION_CONFIG, parameters))
				.thenApply(result -> {
					logger.info("Successfully {} Modern Authentication", newValue ? "enabled" : "disabled");
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), 
						"Successfully " + (newValue ? "enabled" : "disabled") + " Modern Authentication", 
						List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.error("Failed to {} Modern Authentication", newValue ? "enable" : "disable", ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), 
						"Failed to " + (newValue ? "enable" : "disable") + " Modern Authentication: " + ex.getMessage(), 
						List.of(paramChange));
				});
	}

	@Override
	protected OrganizationConfig loadOrganizationConfig() {
		OrganizationConfig orgConfig = super.loadOrganizationConfig();
		if (orgConfig == null) {
			return null;
		}

		// If the base config doesn't have OAuth2ClientProfileEnabled, try to load it from raw JSON
		if (orgConfig.oauth2ClientProfileEnabled == null && configNode != null) {
			JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_ORGANIZATION);
			if (config != null && config.isArray() && !config.isEmpty()) {
				JsonNode firstConfig = config.get(0);
				if (firstConfig.has(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED)) {
					orgConfig.oauth2ClientProfileEnabled = firstConfig.get(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED).asBoolean();
				}
			}
		}

		return orgConfig;
	}
}

