package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Base class for permissions with common fields.
 */
public abstract class Permission extends EnterpriseAppBaseModel {
	@JsonProperty("resourceId")
	protected String resourceId;

	@JsonProperty("resourceDisplayName")
	protected String resourceDisplayName;

	@JsonProperty("consentType")
	protected String consentType;

	// Getters and setters
	public String getResourceId() { return resourceId; }
	public void setResourceId(String resourceId) { this.resourceId = resourceId; }
	public String getResourceDisplayName() { return resourceDisplayName; }
	public void setResourceDisplayName(String resourceDisplayName) { this.resourceDisplayName = resourceDisplayName; }
	public String getConsentType() { return consentType; }
	public void setConsentType(String consentType) { this.consentType = consentType; }
}