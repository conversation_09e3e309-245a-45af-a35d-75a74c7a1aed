package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.BadRequestException;
import io.syrix.protocols.exception.ResourceNotFoundException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.PaginatedResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.StreamSupport;

import static io.syrix.common.constants.Constants.VALUE_FIELD;

/**
 * Base class for Microsoft 365 configuration services.
 * Provides common functionality for configuration export and management.
 */
public abstract class BaseService implements AutoCloseable {
	protected static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);
	protected static final int DEFAULT_BATCH_SIZE = 50;
	protected static final int MAX_RETRIES = 3;
	protected static final Duration RETRY_DELAY = Duration.ofSeconds(2);
	private static final String EXCEPTION_MESSAGE = "Attempt {} failed, with error: {} url: {}";
	protected static final String SERVICE_VERSION = "1.0";
	protected final Logger logger = LoggerFactory.getLogger(getClass());
	protected final MicrosoftGraphClient graphClient;
	protected final ObjectMapper objectMapper;
	protected final MetricsCollector metrics;
	protected final ExecutorService executor;
	protected final Set<String> successfulCommands = new HashSet<>();
	protected final Set<String> unsuccessfulCommands = new HashSet<>();

	protected BaseService(
			MicrosoftGraphClient graphClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {

		this.graphClient = Objects.requireNonNull(graphClient, "Graph client cannot be null");
		this.objectMapper = Objects.requireNonNull(objectMapper, "Object mapper cannot be null");
		this.metrics = Objects.requireNonNull(metrics, "Metrics collector cannot be null");
		this.executor = Executors.newVirtualThreadPerTaskExecutor();
	}

	/**
	 * Gets the list of successfully executed commands as a JsonNode
	 */
	protected JsonNode getSuccessfulCommands() {
		ArrayNode commandArray = objectMapper.createArrayNode();
		successfulCommands.forEach(commandArray::add);
		return commandArray;
	}

	/**
	 * Gets the list of unsuccessfully executed commands as a JsonNode
	 */
	protected JsonNode getUnsuccessfulCommands() {
		ArrayNode commandArray = objectMapper.createArrayNode();
		unsuccessfulCommands.forEach(commandArray::add);
		return commandArray;
	}

	/**
	 * Helper method to check if a JsonNode array contains a specific value.
	 */
	protected boolean containsValue(JsonNode array, String value) {
		if (array.isArray()) {
			return StreamSupport.stream(array.spliterator(), false)
					.anyMatch(element -> element.isTextual() && element.asText().equals(value));
		}
		return false;
	}
	/**
	 * Helper method to retry operations with exponential backoff.
	 */
	protected <T> CompletableFuture<T> withRetry(Supplier<CompletableFuture<T>> operation, String commandName) {
		return CompletableFuture.supplyAsync(() -> {
			Exception lastException = null;
			for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
				try {
					// Guard against null future returned from operation
					CompletableFuture<T> future = operation.get();
					if (future == null) {
						throw new CompletionException(new NullPointerException("Operation returned null CompletableFuture"));
					}
					T result = future.join();
					successfulCommands.add(commandName);
					traceResponse(commandName, result);
					return result;
				} catch (Exception e) {
					Throwable cause = e.getCause();
					if (handleSpecificExceptions(attempt, cause)) {
						unsuccessfulCommands.add(commandName);
						throw new CompletionException(cause);
					}
					lastException = e;
					metrics.recordRetryAttempt(e.getMessage());
					logger.warn("Attempt {} failed, retrying in {} seconds", attempt, RETRY_DELAY.toSeconds(), e);
					if (attempt < MAX_RETRIES) {
						sleepBeforeRetry();
					} else {
						unsuccessfulCommands.add(commandName);
					}
				}
			}
			throw new CompletionException(lastException);
		}, executor);
	}

	private boolean handleSpecificExceptions(int attempt, Throwable cause) {
		if (cause instanceof ResourceNotFoundException rne) {
			logger.error("Attempt {} failed, API not found {}", attempt, rne.getUrl());
			return true;
		} else if (cause instanceof AccessForbiddenException afe) {
			logger.error(EXCEPTION_MESSAGE, attempt, afe.getMessage(), afe.getUrl());
			return true;
		} else if (cause instanceof BadRequestException bre) {
			logger.error(EXCEPTION_MESSAGE, attempt, bre.getMessage(), bre.getUrl());
			return true;
		}
		return false;
	}

	private void sleepBeforeRetry() {
		try {
			Thread.sleep(RETRY_DELAY.toMillis());
		} catch (InterruptedException ie) {
			Thread.currentThread().interrupt();
			throw new CompletionException(ie);
		}
	}

	protected <T> CompletableFuture<Map<String, T>> waitForFutures(
			Map<String, CompletableFuture<?>> futures) {

		Map<String, T> results = new HashMap<>();

		List<CompletableFuture<Void>> wrappedFutures = futures.entrySet().stream()
				.filter(entry -> entry.getValue() != null) // Filter out null futures
				.map(entry -> entry.getValue()
						.thenAccept(result -> {
							if (result != null) { // Only add non-null results
								@SuppressWarnings("unchecked")
								T castedResult = (T) result;
								synchronized (results) {
									results.put(entry.getKey(), castedResult);
								}
							}
						})
						.exceptionally(ex -> {
							// Log the error if needed (optional)
							logger.error("Future failed for key '{}' : {}", entry.getKey(), ex.getMessage());
							return null;
						})).toList();
		return CompletableFuture.allOf(wrappedFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> results);
	}


	/**
	 * Helper method to build configuration result with metadata.
	 */
	protected ConfigurationResult buildConfigurationResult(
			Map<String, ?> results,
			String version,
			ConfigurationServiceType serviceType) {
		ObjectNode configData = objectMapper.createObjectNode();
		results.forEach((key, value) -> {
			if (value instanceof JsonNode jsonNode) {
				configData.set(key, jsonNode);
			} else {
				configData.set(key, objectMapper.valueToTree(value));
			}
		});

		return ConfigurationResult.builder()
				.withData(configData)
				.withTimestamp(Instant.now())
				.withServiceType(serviceType)
				.withMetadata(buildMetadata(version))
				.build();
	}

	/**
	 * Helper method to build metadata for configuration results.
	 */
	protected ObjectNode buildMetadata(String version) {
		ObjectNode metadata = objectMapper.createObjectNode();
		metadata.put("version", version);
		metadata.put("generated_at", Instant.now().toString());
		metadata.put("environment", graphClient.getEnvironment() != null ? graphClient.getEnvironment().toString() : "unknown");
		metadata.put("client_id", graphClient.getClientId() != null ? graphClient.getClientId() : "unknown");
		return metadata;
	}

	private <T> void traceResponse(String commandName, T result) {
		if (logger.isTraceEnabled()) {
			logger.trace("[{}] : {}", commandName, result);
		}
	}

	@Override
	public void close() {
		try {
			executor.shutdown();
			if (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
				executor.shutdownNow();
			}
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			executor.shutdownNow();
		}
	}

	protected CompletableFuture<JsonNode> listWithFilterByDate(String endPoint, Instant fromDateTimeVal, String filterFieldName, String commandName) {
		Instant startTime = Instant.now();
		String endpointSuffix = fromDateTimeVal == null ? endPoint
				: endPoint + "?$filter=" + URLEncoder.encode(String.format("%s ge %s", filterFieldName, fromDateTimeVal.toString()));

		return withRetry(() ->
						CompletableFuture.supplyAsync(() -> {
							try {
								List<JsonNode> retValue = new ArrayList<>();
								String nextLink = endpointSuffix;

								// Continue fetching pages until there are no more
								while (nextLink != null) {
									PaginatedResult result = graphClient.makePaginatedRequest(GraphRequest.builder()
											.beta()
											.withEndpoint(nextLink)
											.withMethod(HttpMethod.GET)
											.build());
									retValue.addAll(result.getValues());
									nextLink = result.getNextLink();

									metrics.recordApiCall(endpointSuffix,
											Duration.between(startTime, Instant.now()),
											true);
								}

								// Here's where we make the type handling explicit
								// Create a combined result as JsonNode
								JsonNode combined = objectMapper.createObjectNode()
										.set(VALUE_FIELD, objectMapper.valueToTree(retValue));

								logger.debug("Retrieved {} events ", retValue.size());

								return combined;

							} catch (Exception e) {
								metrics.recordApiCall(endpointSuffix,
										Duration.between(startTime, Instant.now()),
										false);

								logger.error("Failed to retrieve data from command {}, error : {}}", commandName, e.getMessage());

								throw new CompletionException(new ConfigurationExportException(
										String.format("Failed to retrieve data from command %s", commandName), e));
							}
						}, executor),

				commandName);
	}
}