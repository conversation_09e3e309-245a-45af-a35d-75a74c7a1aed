package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.sharepoint.SharepointConstants;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.Parameter;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.ShellCommandResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public abstract class SPRemediatorBase extends RemediatorBase {
	protected SharePointTenantProperties tenant;
	protected PowerShellSharepointClient client;
	protected static final int MAX_RETRY = 3;
	protected SharepointRemediationConfig spConfig;
	protected ObjectMapper jsonMapper = new ObjectMapper();

	public SPRemediatorBase(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
		this.client = client;
		this.tenant = tenant;
		this.spConfig = spConfig;
	}

	protected CompletableFuture<JsonNode> checkResult(List<GeneralResult> generalResults) {
		return CompletableFuture.completedFuture(jsonMapper.valueToTree(checkResult_(generalResults)));
	}

	protected PolicyChangeResult checkResult_(List<GeneralResult> generalResults) {
		if (generalResults == null || generalResults.isEmpty()) {
			logger.error("Empty general result");
			return IPolicyRemediator.unknown_(getPolicyId(), "empty result");
		}

		GeneralResult result = generalResults.getFirst();
		if (result.errorInfo == null) {
			return IPolicyRemediator.success_(getPolicyId(), SharepointConstants.DEFAULT_SUCCESS_MESSAGE);
		} else {
			logger.error("Command finished with error: {}", result.errorInfo.errorMessage);
			return IPolicyRemediator.failed_(getPolicyId(), result.errorInfo.errorMessage);
		}
	}

	protected PolicyChangeResult checkResult(ShellCommandResult<GeneralResult> commandResult) {
		Instant timeStamp = Instant.now();
		List<GeneralResult> generalResults = commandResult.getData();
		if (generalResults == null || generalResults.isEmpty()) {
			logger.error("Empty general result");
			List<ParameterChangeResult> changeResults = convert(commandResult.getCommand().getParameters(), timeStamp, "", ParameterChangeStatus.UNKNOWN);
			return IPolicyRemediator.unknown_(getPolicyId(), "empty result", changeResults);
		}

		GeneralResult result = generalResults.getFirst();
		if (result.errorInfo == null) {
			List<ParameterChangeResult> changeResults = convert(commandResult.getCommand().getParameters(), timeStamp, result.traceCorrelationId, ParameterChangeStatus.SUCCESS);
			return IPolicyRemediator.success_(getPolicyId(), SharepointConstants.DEFAULT_SUCCESS_MESSAGE, changeResults);
		} else {
			logger.error("Command finished with error: {}", result.errorInfo.errorMessage);
			List<ParameterChangeResult> changeResults = convert(commandResult.getCommand().getParameters(), timeStamp, result.errorInfo.traceCorrelationId, ParameterChangeStatus.FAILED);
			return IPolicyRemediator.failed_(getPolicyId(), result.errorInfo.errorMessage, changeResults);
		}
	}

	protected CompletableFuture<JsonNode> checkResult(List<GeneralResult> generalResults, String successMsg) {
		return CompletableFuture.completedFuture(jsonMapper.valueToTree(checkResult_(generalResults, successMsg)));
	}

	protected PolicyChangeResult checkResult_(List<GeneralResult> generalResults, String successMsg) {
		if (generalResults == null || generalResults.isEmpty()) {
			logger.error("Policy id {} command return empty result", getPolicyId());
			return IPolicyRemediator.unknown_(getPolicyId(), "empty result");
		}

		GeneralResult result = generalResults.getFirst();
		if (result.errorInfo == null) {
			return IPolicyRemediator.success_(getPolicyId(), successMsg);
		} else {
			logger.error("Policy id {} Command finished with error: {}", getPolicyId(), result.errorInfo.errorMessage);
			return IPolicyRemediator.failed_(getPolicyId(), result.errorInfo.errorMessage);
		}
	}

	private static List<ParameterChangeResult> convert(List<Parameter> parameter, Instant curTime, String correlationId, ParameterChangeStatus status) {
		if (parameter == null) {
			return Collections.emptyList();
		}
		return parameter.stream().map(SPRemediatorBase::convert)
				.map(r -> r.correlationId(correlationId))
				.map(r -> r.timeStamp(curTime))
				.map(r -> r.status(status))
				.toList();
	}

	private static ParameterChangeResult convert(Parameter parameter) {
		return new ParameterChangeResult()
				.parameter(parameter.name())
				.prevValue(parameter.prevValue())
				.newValue(parameter.newValue());
	}

}
