package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.AAD.8.1v2")
public class EntraIDGuestAccessRemediator extends RemediatorBase {
	private final PowerShellClient powershellClient;

	private static final Map<String, Object> LIMITED_ACCESS_SETTINGS = Map.of(
			EntraIDConstants.USERS_PERMISSION_TO_READ_OTHER_USERS, Constants.FALSE_VALUE,
			EntraIDConstants.GUEST_USER_ROLE, EntraIDConstants.RESTRICTED_ACCESS
	);

	public EntraIDGuestAccessRemediator(PowerShellClient powershellClient) {
		this.powershellClient = powershellClient;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for guest user access restrictions");
		return powershellClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								EntraIDConstants.SET_AZURE_AD_SETTINGS,
								LIMITED_ACCESS_SETTINGS
						))
				.thenCompose(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully restricted guest user access");
						return IPolicyRemediator.success(getPolicyId(),
								"Guest user access has been limited to restricted properties");
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : "Unknown error";
						logger.error("Failed to restrict guest user access: {}", error);
						return IPolicyRemediator.failed(getPolicyId(), error);
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception while restricting guest access", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}
}