package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellClient.CommandRequest;
import io.syrix.common.utils.Utils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Base class for working with Microsoft 365 alerts.
 * <p>
 * This class provides common functionality for interacting with Microsoft 365 alerts,
 * including creating, duplicating, and configuring alerts. It uses the approach of
 * creating custom versions of built-in alerts because Microsoft does not allow direct
 * modification of built-in alerts via PowerShell.
 * <p>
 * This class handles the complexities of interacting with the Microsoft 365 alert system,
 * including:
 * <ul>
 *     <li>Retrieving existing alerts.</li>
 *     <li>Identifying required alerts based on predefined criteria.</li>
 *     <li>Duplicating alerts with custom settings.</li>
 *     <li>Updating existing custom alerts.</li>
 *     <li>Generating unique alert names with tenant-specific suffixes.</li>
 *     <li>Handling errors and providing informative error messages.</li>
 * </ul>
 * <p>
 * It is designed to be extended by specific alert remediators that define the exact
 * alerts to be managed and the custom settings to be applied.
 * <p>
 * <b>Important Note on Alert Creation:</b>
 * When creating new alert of "Messages have been delayed with message" is failing due to source allert is missing in Office 365
 */
public abstract class MicrosoftAlertBaseRemediator extends RemediatorBase {
	// Constants for field names and commands
	protected static final String GET_PROTECTION_ALERT = "Get-ProtectionAlert";
	protected static final String NEW_PROTECTION_ALERT = "New-ProtectionAlert";

	// Default settings
	protected static final int MAX_ALERT_NAME_LENGTH = 63;
	protected static final String DEFAULT_NOTIFY_USER = "TenantAdmins";

	// Alert properties
	protected static final String NOTIFY_USER = "NotifyUser";
	protected static final String OPERATION = "Operation";
	protected static final String NOTIFICATION_ENABLED = "NotificationEnabled";
	protected static final String SEVERITY = "Severity";
	protected static final String CATEGORY = "Category";
	protected static final String COMMENT = "Comment";
	protected static final String THREAT_TYPE = "ThreatType";
	protected static final String AGGREGATION_TYPE = "AggregationType";
	protected static final String DISABLED = "Disabled";
	protected static final String DESCRIPTION = Constants.DESCRIPTION_FIELD;
	protected static final String CONDITION = "Condition";

	protected static final String ALERTS_RESOURCES_NAME = "alertsconfig/microsoft-alerts-config.yml";
	// Map of required alerts with their display names and internal identifiers
	protected static final List<String> REQUIRED_ALERTS = List.of(
			"Suspicious email sending patterns detected",
            "Suspicious Connector Activity",
            "Suspicious Email Forwarding Activity",
            "Messages have been delayed",
            "Tenant restricted from sending unprovisioned email",
            "Tenant restricted from sending email",
            "A potentially malicious URL click was detected"
	);

	protected final PowerShellClient powershellClient;
	protected final ObjectMapper objectMapper;
	private final ArrayList<String> alertsToConfigure;
	protected String csspEndpoint;
	protected final String tenantHashSuffix;
	protected final Set<String> newAlerts = new HashSet<>();


	/**
	 * Constructs a new MicrosoftAlertBaseRemediator with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 * @param tenantId The tenant ID to use for creating the hash suffix
	 */
	public MicrosoftAlertBaseRemediator(PowerShellClient powershellClient, String tenantId, List<String> additionalAlerts) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
		try {
			this.tenantHashSuffix = Utils.generateMD5Hash(tenantId);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
		this.alertsToConfigure = new ArrayList<>(REQUIRED_ALERTS);
		if(!CollectionUtils.isEmpty(additionalAlerts)) {
			this.alertsToConfigure.addAll(additionalAlerts);
		}
		newAlerts.addAll(alertsToConfigure);
	}


	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting notification configuration for {} - Required Alerts", getPolicyId());

		if(!checkPreconditions()) {
			return IPolicyRemediator.failed(getPolicyId(), "Preconditions are not met. Skipping remediation");
		}

		// Load alert configurations
		final Map<String, AlertDefinition> alertsDefintion;
		try {
			alertsDefintion = loadAlertConfigurationsMap(ALERTS_RESOURCES_NAME);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		return powershellClient.getCPPSEndpoint()
				.thenCompose(cppsEndpoint -> {
					this.csspEndpoint = cppsEndpoint;
					return getAllAlerts()
							.thenCompose(allAlerts -> configureAlerts(allAlerts, alertsDefintion))
							.thenCompose(result -> {
								// After configuring existing alerts, check if there are new alerts to create
								if (newAlerts.isEmpty()) {
									logger.info("All required alerts were found and configured");
									return CompletableFuture.completedFuture(result);
								}

								logger.info("Some required alerts were not found, creating them from definitions");
								return createNewAlerts(alertsDefintion)
										.thenApply(createResult -> {
											// Combine results if both operations were successful
											if (isSuccessResult(result) && isSuccessResult(createResult)) {
												return createSuccessNode("Successfully configured existing alerts and created missing alerts");
											} else if (isSuccessResult(result)) {
												// Configuration succeeded but creation had issues
												return createResult;
											} else if (isSuccessResult(createResult)) {
												// Creation succeeded but configuration had issues
												return result;
											} else {
												// Both had issues, report both
												return createFailureNode("Issues occurred during alert configuration and creation");
											}
										});
							})
							.exceptionally(ex -> {
								logger.error("Exception during alert notification configuration", ex);
								return createFailureNode("Failed to configure alert notifications: " + ex.getMessage());
							});
				});
	}

	abstract protected CompletableFuture<JsonNode> createNewAlerts(Map<String, AlertDefinition> alertsDefintion);
	abstract protected boolean checkPreconditions();
	abstract protected CompletableFuture<JsonNode> configureAlerts(JsonNode allAlerts, Map<String, AlertDefinition> alertsDefintion);

	/**
	 * Helper method to check if a result indicates success.
	 */
	private boolean isSuccessResult(JsonNode result) {
		return result != null &&
				result.has(Constants.STATUS_FIELD) &&
				result.get(Constants.STATUS_FIELD).asText().equals(Constants.SUCCESS_STATUS);
	}

	/**
	 * Retrieves all protection alerts.
	 */
	protected CompletableFuture<JsonNode> getAllAlerts() {
		logger.info("Retrieving all protection alerts");
		return powershellClient.executeCmdletCommand(new CommandRequest(GET_PROTECTION_ALERT, new HashMap<>(), this.csspEndpoint));
	}

	/**
	 * Checks if the alert response is valid and contains alerts.
	 */
	protected boolean isValidAlertResponse(JsonNode allAlerts) {
		return allAlerts != null && allAlerts.isArray() && allAlerts.size() > 0;
	}

	/**
	 * Identifies required alerts from all alerts.
	 *
	 * @param allAlerts The JSON response containing all alerts
	 * @param existingAlertNames List to populate with all alert names (for duplicate checking)
	 * @return List of required alerts that match our criteria
	 */
	protected List<JsonNode> identifyRequiredAlerts(JsonNode allAlerts, List<String> existingAlertNames) {
		List<JsonNode> requiredAlerts = new ArrayList<>();

		// Collect all alert names first to check for duplicates
		if (existingAlertNames != null) {
			for (JsonNode alert : allAlerts) {
				String alertName = alert.path(Constants.NAME_FIELD).asText();
				existingAlertNames.add(alertName);
			}
		}

		// Find the required alerts
		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			boolean isDisabled = alert.path(DISABLED).asBoolean(false);

			// Check if this is one of our required alerts
			for (String requiredAlertName : alertsToConfigure) {
				if (alertName.contains(requiredAlertName)) {
					// Found a required alert
					requiredAlerts.add(alert);
					this.newAlerts.remove(requiredAlertName);
					break;
				}
			}
		}

		return requiredAlerts;
	}

	/**
	 * Loads alert configurations from a YAML file and builds a mapping of alert IDs to their configurations.
	 * This allows quick lookup of alert configurations by ID.
	 *
	 * @param resourceName The name of the YAML resource file in src/main/resources
	 * @return A Map of alert IDs to their AlertDefinition objects
	 * @throws IOException If there's an error reading or parsing the configuration file
	 */
	protected Map<String, AlertDefinition> loadAlertConfigurationsMap(String resourceName) throws IOException {
		logger.info("Loading alert configurations from resource: {}", resourceName);

		// Read the YAML configuration from src/main/resources
		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourceName);
		if (inputStream == null) {
			throw new IOException("Resource not found in src/main/resources: " + resourceName);
		}

		try {
			ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
			// Configure the mapper for proper deserialization
			yamlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

			// Deserialize the YAML content into AlertsConfiguration
			AlertsConfiguration config = yamlMapper.readValue(inputStream, AlertsConfiguration.class);

			// Build a map of alert ID to AlertDefinition
			Map<String, AlertDefinition> alertsMap = new HashMap<>();
			if (config.getAlerts() != null) {
				for (AlertDefinition alert : config.getAlerts()) {
					if (alert.getId() != null) {
						alertsMap.put(alert.getName(), alert);
						logger.debug("Loaded alert definition: {}", alert.getId());
					} else {
						logger.warn("Skipping alert definition with null ID: {}", alert.getName());
					}
				}
			}

			logger.info("Loaded {} alert configurations from {}", alertsMap.size(), resourceName);
			return alertsMap;
		} catch (Exception e) {
			logger.error("Error parsing YAML configuration from {}: {}", resourceName, e.getMessage());
			throw new IOException("Failed to parse alert configuration: " + e.getMessage(), e);
		} finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				logger.warn("Error closing input stream", e);
			}
		}
	}

	/**
	 * Creates new alerts from YAML alert definitions.
	 * This method handles the creation of alerts that don't exist in the system but are required.
	 *
	 * @param resourceName The name of the YAML resource file in src/main/resources
	 * @param alertsToCreate Set of alert names to create
	 * @param notifyUsers List of notification recipients (uses "TenantAdmins" if empty)
	 * @return CompletableFuture with the creation results
	 */
	protected CompletableFuture<JsonNode> createAlertsFromDefinitions(
			Map<String, AlertDefinition> alertsMap,
			Set<String> alertsToCreate,
			List<String> notifyUsers) {

		if (alertsToCreate.isEmpty()) {
			logger.info("No new alerts to create");
			return CompletableFuture.completedFuture(
					createSuccessNode("No new alerts to create")
			);
		}

		logger.info("Creating {} new alerts from definitions: {}",
				alertsToCreate.size(), alertsToCreate);

		try {
			List<CompletableFuture<JsonNode>> creationTasks = new ArrayList<>();
			List<String> createdAlerts = new ArrayList<>();
			List<String> failedAlerts = new ArrayList<>();
			List<String> skippedAlerts = new ArrayList<>();

			// Use default recipient if notifyUsers is empty
			List<String> recipients = notifyUsers;
			if (CollectionUtils.isEmpty(recipients)) {
				recipients = Collections.singletonList(DEFAULT_NOTIFY_USER);
				logger.info("Using default notification recipient: {}", DEFAULT_NOTIFY_USER);
			}

			// Convert to comma-separated string for PowerShell
			String notifyUsersString = String.join(",", recipients);

			// Process each alert to create
			for (String alertName : alertsToCreate) {
				// Find the alert definition by name or matching pattern
				AlertDefinition alertDef = findAlertDefinitionByName(alertsMap, alertName);

				if (alertDef == null) {
					logger.warn("No definition found for alert '{}'", alertName);
					skippedAlerts.add(alertName);
					continue;
				}

				// Get parameters and add required fields
				Map<String, Object> parameters = new HashMap<>(alertDef.getParametersMap());
				parameters.put("Name", alertDef.getName() + tenantHashSuffix);
				parameters.put("NotifyUser", notifyUsersString);
				parameters.put("NotificationEnabled", true);
				parameters.put("Disabled", false);

				// Create the alert
				creationTasks.add(powershellClient.executeCmdletCommand(
								new CommandRequest(NEW_PROTECTION_ALERT, parameters, this.csspEndpoint))
						.thenApply(result -> {
							String displayName = alertDef.getName();
							if (result != null && !result.has(Constants.ERROR_FIELD)) {
								logger.info("Successfully created alert: {}", displayName);
								createdAlerts.add(displayName);
							} else {
								String errorMessage = result != null ?
										extractErrorMessage(result) : "Unknown error";
								logger.error("Failed to create alert '{}': {}", displayName, errorMessage);
								failedAlerts.add(displayName);
							}
							return result;
						})
				);
			}

			if (creationTasks.isEmpty()) {
				if (!skippedAlerts.isEmpty()) {
					return CompletableFuture.completedFuture(
							createPartialSuccessNode("Could not find definitions for some alerts: " +
									String.join(", ", skippedAlerts))
					);
				} else {
					return CompletableFuture.completedFuture(
							createSuccessNode("No new alerts to create")
					);
				}
			}

			return CompletableFuture.allOf(creationTasks.toArray(new CompletableFuture[0]))
					.thenApply(v -> {
						if (failedAlerts.isEmpty()) {
							if (skippedAlerts.isEmpty()) {
								return createSuccessNode("Successfully created all new alerts");
							} else {
								return createPartialSuccessNode("Created some alerts but could not find definitions for: " +
										String.join(", ", skippedAlerts));
							}
						} else {
							StringBuilder errorMessage = new StringBuilder("Failed to create some alerts: ");
							for (String failed : failedAlerts) {
								errorMessage.append(failed).append(", ");
							}
							if (!createdAlerts.isEmpty() || !skippedAlerts.isEmpty()) {
								return createPartialSuccessNode(errorMessage.toString());
							} else {
								return createFailureNode(errorMessage.toString());
							}
						}
					});

		} catch (Exception ex) {
			logger.error("Error creating alerts from definitions", ex);
			return CompletableFuture.completedFuture(
					createFailureNode("Failed to create alerts from definitions: " + ex.getMessage())
			);
		}
	}

	/**
	 * Finds an alert definition by matching the name or ID.
	 *
	 * @param alertsMap Map of alert definitions
	 * @param alertName The alert name to find
	 * @return The matching AlertDefinition or null if not found
	 */
	private AlertDefinition findAlertDefinitionByName(Map<String, AlertDefinition> alertsMap, String alertName) {
		// First try direct match by ID
		if (alertsMap.containsKey(alertName)) {
			return alertsMap.get(alertName);
		}

		// Convert to lowercase for case-insensitive matching
		String lowerAlertName = alertName.toLowerCase();

		// Then try to match by name (full or partial)
		for (AlertDefinition def : alertsMap.values()) {
			String defName = def.getName().toLowerCase();

			// Try exact name match
			if (defName.equals(lowerAlertName)) {
				return def;
			}

			// Try partial name match (either way)
			if (defName.contains(lowerAlertName) || lowerAlertName.contains(defName)) {
				return def;
			}
		}

		return null;
	}

	/**
	 * Find existing custom alert that matches the source alert.
	 *
	 * @param allAlerts All existing alerts
	 * @param sourceAlert The source alert to match
	 * @param suffix The suffix to check for
	 * @return The matching alert or null if not found
	 */
	protected JsonNode findExistingCustomAlert(JsonNode allAlerts, JsonNode sourceAlert, String suffix) {
		if (allAlerts == null || !allAlerts.isArray() || sourceAlert == null) {
			return null;
		}

		String sourceAlertName = sourceAlert.path(Constants.NAME_FIELD).asText();
		String sourceAlertBase = getBaseAlertName(sourceAlertName);
		String newAlertName = generateNewAlertName(sourceAlertName, suffix);

		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			// Check if this alert exactly matches our target name
			if (alertName.equals(newAlertName)) {
				logger.info("Found alert with exact name '{}'", alertName);
				return alert;
			}

			// Check if this alert has our suffix and matches the source alert base name
			if (alertName.endsWith(suffix) && alertName.startsWith(sourceAlertBase)) {
				logger.info("Found alert with matching base name and suffix '{}'", alertName);
				return alert;
			}
		}

		// Also check explicitly if the alert name appears in the existing alert names
		// by iterating through allAlerts and extracting just the names
		List<String> allAlertNames = new ArrayList<>();
		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			allAlertNames.add(alertName);
		}

		if (allAlertNames.contains(newAlertName)) {
			logger.info("Alert name '{}' exists in the list of alerts but wasn't matched in previous search", newAlertName);
			// Find and return the matched alert
			for (JsonNode alert : allAlerts) {
				if (alert.path(Constants.NAME_FIELD).asText().equals(newAlertName)) {
					return alert;
				}
			}
		}

		return null;
	}

	/**
	 * Get the base name of an alert without any suffixes
	 */
	protected String getBaseAlertName(String alertName) {
		// Remove common suffixes if present
		if (alertName.contains("-")) {
			return alertName.substring(0, alertName.lastIndexOf("-"));
		}
		return alertName;
	}

	/**
	 * Duplicate a single alert with custom settings or update an existing one.
	 *
	 * @param sourceAlert The source alert to duplicate
	 * @param allAlerts All existing alerts for checking duplicates
	 * @param suffix The suffix to use for the alert name
	 * @param parameters Custom parameters to apply (can be null)
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> duplicateOrUpdateAlert(JsonNode sourceAlert, JsonNode allAlerts,
																 String suffix, Map<String, Object> customParameters) {
		String sourceAlertName = sourceAlert.path(Constants.NAME_FIELD).asText();
		String newAlertName = generateNewAlertName(sourceAlertName, suffix);

		logger.info("Checking for existing custom alert for '{}'", sourceAlertName);

		// First check if we already have a custom alert for this source alert
		JsonNode existingCustomAlert = findExistingCustomAlert(allAlerts, sourceAlert, suffix);

		if (existingCustomAlert != null) {
			String existingAlertName = existingCustomAlert.path(Constants.NAME_FIELD).asText();
			logger.info("Found existing custom alert '{}', using it instead of creating a new one", existingAlertName);

			// TODO: If needed, update the existing alert with new parameters
			// For now, we're just returning the existing alert as success

			return CompletableFuture.completedFuture(existingCustomAlert);
		}

		// Extra safety check - check all alert names directly
		List<String> allAlertNames = new ArrayList<>();
		for (JsonNode alert : allAlerts) {
			allAlertNames.add(alert.path(Constants.NAME_FIELD).asText());
		}

		if (allAlertNames.contains(newAlertName)) {
			logger.info("Alert with name '{}' already exists but wasn't matched by our custom alert finder", newAlertName);
			// Find the alert and return it
			for (JsonNode alert : allAlerts) {
				if (alert.path(Constants.NAME_FIELD).asText().equals(newAlertName)) {
					return CompletableFuture.completedFuture(alert);
				}
			}
		}

		// No existing custom alert found, create a new one
		return duplicateAlert(sourceAlert, newAlertName, customParameters);
	}


	/**
	 * Duplicate a single alert with custom settings.
	 *
	 * @param sourceAlert The source alert to duplicate
	 * @param targetName The name for the duplicate alert
	 * @param parameters Custom parameters to apply (can be null)
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> duplicateAlert(JsonNode sourceAlert, String targetName, Map<String, Object> customParameters) {
		logger.info("Duplicating alert '{}' as '{}'", sourceAlert.path(Constants.NAME_FIELD).asText(), targetName);

		// Base parameters from the source alert
		Map<String, Object> parameters = new HashMap<>();
		parameters.put(Constants.NAME_FIELD, targetName);
		parameters.put(NOTIFICATION_ENABLED, true);
		parameters.put(AGGREGATION_TYPE, "None");
		parameters.put(DISABLED, false);

		// Copy all relevant properties from the source alert
		copyAlertProperty(sourceAlert, parameters, OPERATION);
		copyAlertProperty(sourceAlert, parameters, SEVERITY);
		copyAlertProperty(sourceAlert, parameters, CATEGORY);
		copyAlertProperty(sourceAlert, parameters, COMMENT);
		copyAlertProperty(sourceAlert, parameters, THREAT_TYPE);
		copyAlertProperty(sourceAlert, parameters, DESCRIPTION);
		copyAlertProperty(sourceAlert, parameters, CONDITION);
		copyAlertProperty(sourceAlert, parameters, "Filter");

		String alertName = sourceAlert.path(Constants.NAME_FIELD).asText();

		// Override with custom parameters if provided
		if (customParameters != null) {
			parameters.putAll(customParameters);
		}

		for (Map.Entry<String, Object> entry : customParameters.entrySet()) {
			if(entry.getValue() instanceof String value && StringUtils.isEmpty(value)) {
				parameters.remove(entry.getKey());
			}
		}
		return powershellClient.executeCmdletCommand( new CommandRequest(NEW_PROTECTION_ALERT, parameters, this.csspEndpoint))
				.thenApply(result -> {
			if (result == null || result.has(Constants.ERROR_FIELD)) {
				String errorMsg = result != null ?
						extractErrorMessage(result) :
						"Failed to create custom version of alert: " + sourceAlert.path(Constants.NAME_FIELD).asText();
				logger.error(errorMsg);
				return result != null ? result : createErrorNode(errorMsg);
			}

			logger.info("Successfully created custom version of alert '{}' as '{}'", sourceAlert.path(Constants.NAME_FIELD).asText(), targetName);
			return result;
		}).exceptionally(ex -> {
			logger.error("Exception creating custom version of alert: {} with message {}", alertName, ex.getMessage());
			String errorMsg = "Failed to create custom version of alert : " + alertName + " with message : " + ex.getMessage();
			return createErrorNode(errorMsg);
		});
	}

	/**
	 * Helper to copy a property from source alert to parameters map if it exists
	 */
	protected void copyAlertProperty(JsonNode sourceAlert, Map<String, Object> parameters, String propertyName) {
		JsonNode propertyNode = sourceAlert.path(propertyName);
		if (!propertyNode.isMissingNode() && !propertyNode.isNull()) {
			// Convert JsonNode to appropriate Java object based on type
			if (propertyNode.isBoolean()) {
				parameters.put(propertyName, propertyNode.asBoolean());
			} else if (propertyNode.isInt()) {
				parameters.put(propertyName, propertyNode.asInt());
			} else if (propertyNode.isTextual()) {
				parameters.put(propertyName, propertyNode.asText());
			} else if (propertyNode.isArray()) {
				List<Object> list = new ArrayList<>();
				propertyNode.forEach(item -> {
					if (item.isTextual()) {
						list.add(item.asText());
					} else if (item.isInt()) {
						list.add(item.asInt());
					} else if (item.isBoolean()) {
						list.add(item.asBoolean());
					} else {
						// For complex objects, pass the raw JsonNode
						list.add(item);
					}
				});
				parameters.put(propertyName, list);
			} else {
				// For complex objects, pass the raw JsonNode
				parameters.put(propertyName, propertyNode);
			}
		}
	}

	/**
	 * Generate a new alert name with suffix, ensuring it doesn't exceed the maximum length.
	 */
	protected String generateNewAlertName(String originalName, String suffix) {
		int maxBaseLength = MAX_ALERT_NAME_LENGTH - suffix.length();
		String baseName = originalName.length() > maxBaseLength ?
				originalName.substring(0, maxBaseLength) : originalName;
		return baseName + suffix;
	}

	/**
	 * Extract error message from the result.
	 */
	protected String extractErrorMessage(JsonNode result) {
		if (result == null) {
			return "No response received";
		}

		if (result.has(Constants.ERROR_FIELD)) {
			JsonNode error = result.get(Constants.ERROR_FIELD);
			if (error.isTextual()) {
				return error.asText();
			} else if (error.isObject()) {
				if (error.has(Constants.MESSAGE_FIELD)) {
					return error.get(Constants.MESSAGE_FIELD).asText();
				} else if (error.has(Constants.DETAILS_FIELD)
						&& error.get(Constants.DETAILS_FIELD).isArray()
						&& error.get(Constants.DETAILS_FIELD).size() > 0) {
					JsonNode details = error.get(Constants.DETAILS_FIELD).get(0);
					if (details.has(Constants.MESSAGE_FIELD)) {
						return details.get(Constants.MESSAGE_FIELD).asText();
					}
				}

				// Check for a nested error structure
				if (error.has("innererror")) {
					JsonNode innerError = error.get("innererror");
					if (innerError.has(Constants.MESSAGE_FIELD)) {
						return innerError.get(Constants.MESSAGE_FIELD).asText();
					}

					// Check for an even deeper nested exception
					if (innerError.has("internalexception")) {
						JsonNode internalException = innerError.get("internalexception");
						if (internalException.has(Constants.MESSAGE_FIELD)) {
							return internalException.get(Constants.MESSAGE_FIELD).asText();
						}
					}
				}
			}
		}

		return "Unknown error: " + (result.toString().length() > 100 ?
				result.toString().substring(0, 100) + "..." : result.toString());
	}

	/**
	 * Processes a list of alerts by either duplicating them or ensuring they have the correct configuration.
	 * This common method handles deduplication, checking for existing configurations, and creating custom
	 * versions with specified parameters.
	 *
	 * @param alertsToProcess The list of alerts to process
	 * @param allAlerts All existing alerts for reference and checking
	 * @param customParameters Custom parameters to apply to the alerts
	 * @param configChecker Function to check if an alert is already properly configured
	 * @param successMessage Success message when all alerts are processed correctly
	 * @param partialSuccessPrefix Prefix for partial success message
	 * @param skipMessage Message for when alerts are skipped (already configured)
	 * @return CompletableFuture with the processing result
	 */
	protected CompletableFuture<JsonNode> processAlerts(
			Map<String, AlertDefinition> alertsDefintion,
			List<JsonNode> alertsToProcess,
			JsonNode allAlerts,
			Map<String, Object> customParameters,
			AlertConfigChecker configChecker,
			String successMessage,
			String partialSuccessPrefix,
			String skipMessage) {

		List<CompletableFuture<JsonNode>> processingTasks = new ArrayList<>();
		List<String> processedAlerts = new ArrayList<>();
		List<String> failedAlerts = new ArrayList<>();
		List<String> skippedAlerts = new ArrayList<>();

		// First, deduplicate alerts by their name to ensure we don't process the same alert twice
		Map<String, JsonNode> uniqueAlerts = new HashMap<>();
		for (JsonNode alert : alertsToProcess) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			if (!uniqueAlerts.containsKey(alertName)) {
				uniqueAlerts.put(alertName, alert);
			}
		}

		logger.info("Processing {} unique alerts after deduplication", uniqueAlerts.size());

		for (JsonNode alert : uniqueAlerts.values()) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			// Check if alert is already properly configured (if a checker is provided)
			if (configChecker != null && configChecker.isAlertProperlyConfigured(alert, allAlerts)) {
				logger.info("Alert '{}' is already enabled with correct configuration", alertName);
				skippedAlerts.add(alertName);
				processedAlerts.add(alertName);
				continue;
			}

			// Check if there's an existing custom version with the tenant suffix
			JsonNode existingCustomAlert = findExistingCustomAlert(allAlerts, alert, tenantHashSuffix);
			if (existingCustomAlert != null) {
				String existingAlertName = existingCustomAlert.path(Constants.NAME_FIELD).asText();

				// Check if existing custom alert already has correct settings
				if (configChecker != null && configChecker.isAlertProperlyConfigured(existingCustomAlert, null)) {
					logger.info("Existing custom alert '{}' already has correct configuration", existingAlertName);
					processedAlerts.add(alertName);
					continue;
				}

				// TODO: If needed, update existing alert settings
				// For now, we'll log it and consider it processed
				logger.info("Found existing custom alert '{}' but configuration needs updating", existingAlertName);
				processedAlerts.add(alertName);
				continue;
			}

			Map<String, Object> customParametersTmp = new HashMap<String, Object>(customParameters);
			AlertDefinition tmpAlertDefintion = alertsDefintion.get(alertName);
			customParametersTmp.putAll(tmpAlertDefintion.getParametersMap());
			// No existing properly configured alert, create a new one
			processingTasks.add(duplicateOrUpdateAlert(alert, allAlerts, tenantHashSuffix, customParametersTmp)
					.thenApply(result -> {
						if (result != null && !result.has(Constants.ERROR_FIELD)) {
							processedAlerts.add(alertName);
						} else {
							failedAlerts.add(alertName);
						}
						return result;
					}));
		}

		if (processingTasks.isEmpty()) {
			if (!skippedAlerts.isEmpty()) {
				logger.info("All required alerts are already correctly configured or have custom versions");
				return CompletableFuture.completedFuture(
						createSuccessNode(skipMessage)
				);
			} else {
				logger.info("All required alerts already have custom versions with the correct configuration");
				return CompletableFuture.completedFuture(
						createSuccessNode("All required alerts already have custom versions with the correct configuration")
				);
			}
		}

		return CompletableFuture.allOf(processingTasks.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					if (failedAlerts.isEmpty()) {
						if (skippedAlerts.size() == processedAlerts.size()) {
							// All alerts were skipped because they were already properly configured
							return createSuccessNode(skipMessage);
						} else if (!skippedAlerts.isEmpty()) {
							// Some alerts were skipped, some were created
							return createSuccessNode(successMessage + " Some alerts were already correctly configured.");
						} else {
							// All alerts were newly configured
							return createSuccessNode(successMessage);
						}
					} else {
						StringBuilder errorMessage = new StringBuilder(partialSuccessPrefix);
						for (String failed : failedAlerts) {
							errorMessage.append(failed).append(", ");
						}
						// If we've processed some alerts successfully but not all
						if (!processedAlerts.isEmpty()) {
							return createPartialSuccessNode(errorMessage.toString());
						} else {
							return createFailureNode(errorMessage.toString());
						}
					}
				});
	}

	/**
	 * Interface for checking if an alert is properly configured.
	 * This allows different implementations for different configuration requirements.
	 */
	protected interface AlertConfigChecker {
		/**
		 * Check if an alert is properly configured according to specific requirements.
		 *
		 * @param alert The alert to check
		 * @param allAlerts All alerts (may be null if not needed)
		 * @return true if the alert is properly configured
		 */
		boolean isAlertProperlyConfigured(JsonNode alert, JsonNode allAlerts);
	}

	/**
	 * Create an error node
	 */
	protected JsonNode createErrorNode(String error) {
		ObjectNode errorNode = objectMapper.createObjectNode();
		errorNode.put(Constants.ERROR_FIELD, error);
		return errorNode;
	}

	/**
	 * Create a success response node.
	 */
	protected JsonNode createSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Create a partial success response node.
	 */
	protected JsonNode createPartialSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, "partial");
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Create a failure response node.
	 */
	protected JsonNode createFailureNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}
}