package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.products.microsoft.entra.service.EntraIDRemediationException;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static io.syrix.common.constants.Constants.DESCRIPTION_FIELD;
import static io.syrix.common.constants.Constants.GROUP_ID;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.GROUP_EXPAND_KEY;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.GROUP_MEMBERS_TYPE;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ODATA_TYPE;

@PolicyRemediator("MS.AAD.7.6v1")
public class GlobalAdminPIMRemediator extends RemediatorBase {
	private static final String POLICY_ASSIGNMENTS_ENDPOINT = "/policies/roleManagementPolicyAssignments";
	private static final String POLICIES_ENDPOINT = "/policies/roleManagementPolicies";
	private static final String GROUPS_ENDPOINT = "/groups";
	private static final String ROLE_ASSIGNMENTS_ENDPOINT = "/roleManagement/directory/roleAssignments";
	private static final String APPROVER_GROUP_NICKNAME = "globalAdminApprovers";
	private static final String IS_APPROVAL_REQIRED = "isApprovalRequired";
	private static final String APPROVAL_STAGES = "approvalStages";
	private static final String SETTING = "setting";
	private static final String PRIMARY_APPROVERS = "primaryApprovers";
	private static final String GROUP_DESCRIPTION = "Global Administrator Approvers";
	private static final String TARGET = "target";
	private static final String RULES = "rules";
	private static final String CALLER = "caller";
	private static final String LEVEL = "level";
	private static final String SINGLE_STAGE = "SingleStage";
	private static final String OPERATIONS = "operations";
	private static final String APPROVAL_MODE = "approvalMode";
	private static final String APPROVAL_TIME_OUT = "approvalStageTimeOutInDays";
	private static final String IS_APPR_JUSTIFICATION_REQ = "isApproverJustificationRequired";
	private static final String IS_ESCALATION_ENABLED = "isEscalationEnabled";
	private static final String ESCALATION_TIMEOUT = "escalationTimeOutInMinutes";
	private static final String IS_BACKUP = "isBackup";
	private static final String INHERITABLE_SETTINGS = "inheritableSettings";
	private static final String ENFORCE_SETTINGS = "enforcedSettings";
	private static final String ESCALATION_APPROVERS = "escalationApprovers";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final String roleId;

	public GlobalAdminPIMRemediator(MicrosoftGraphClient graphClient, String globalAdminRoleId) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.roleId = globalAdminRoleId;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to enforce approval requirement for role {}", roleId);

		return findExistingConfiguration()
				.thenCompose(this::handleExistingConfiguration)
				.exceptionally(ex -> {
					logger.error("Exception during role approval configuration remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<ExistingConfiguration> findExistingConfiguration() {
		return getPolicyAssignment()
				.thenCompose(policyAssignment -> {
					String policyId = extractPolicyId(policyAssignment);
					return getRoleManagementPolicy(policyId)
							.thenApply(policy -> new ExistingConfiguration(policyId, parseRuleJson(policy))
							);
				});
	}

	private CompletableFuture<String> findExistingApproverGroup() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(GROUPS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "displayName eq '" + GROUP_DESCRIPTION + "'")
						.build()
		).thenApply(response -> {
			JsonNode groups = response.get(VALUE_FIELD);
			return groups != null && groups.size() > 0 ? groups.get(0).get("id").asText() : null;
		});
	}

	private boolean isAnyApprover(ExistingConfiguration config) {
		return config.getRule().getSetting().getApprovalStages().stream()
				.anyMatch(stage -> CollectionUtils.isNotEmpty(stage.getPrimaryApprovers()));
	}

	/**
	 * Handles the existing configuration, updating it if necessary.
	 *
	 * @param config The existing configuration to process
	 * @return CompletableFuture with the remediation result
	 */
	private CompletableFuture<JsonNode> handleExistingConfiguration(ExistingConfiguration config) {
		if (config.rule.getSetting().isApprovalRequired() && isAnyApprover(config)) {
			// Rule is already properly configured with approval and has a group approver
			logger.info("Role activation approval already configured for role : {}", roleId);
			return IPolicyRemediator.success(getPolicyId(),
					"Role activation approval already configured for role : " + roleId);
		}
		// Check if approval is not required or if approver group is missing
		ApproverGroupInfo group = extractApproverGroupId(config.rule);
		if (group != null) {
			// Rule has a group in our config, verify it still exists
			return verifyExistingGroup(group.groupId())
					.thenCompose(isValid ->
							createAndUpdateConfiguration(config.policyId,
									config.rule,
									Boolean.TRUE.equals(isValid) ? group.groupId() : null));
		}

		// No group in rule, find or create one
		return findExistingApproverGroup()
				.thenCompose(existingGroupJson -> {
					if (existingGroupJson == null) {
						// No existing group, create a new one
						return createApproverGroup()
								.thenCompose(newGroupId -> createAndUpdateConfiguration(
										config.policyId,
										config.rule,
										newGroupId));
					} else {
						// Use existing group
						return createAndUpdateConfiguration(
								config.policyId,
								config.rule,
								existingGroupJson);
					}
				});
	}

	private CompletableFuture<Boolean> verifyExistingGroup(String groupId) {
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.withEndpoint(GROUPS_ENDPOINT + "/" + groupId)
								.build()
				).thenApply(group -> true)
				.exceptionally(e -> false);
	}

	private CompletableFuture<JsonNode> createAndUpdateConfiguration(String policyId,
																	 Models.UnifiedRoleManagementPolicyRule rule,
																	 String groupId) {
		if (StringUtils.isEmpty(groupId)) {
			// Create group and populate it before updating the rule
			return createApproverGroup()
					.thenCompose(newGroupId -> populateApproverGroup(newGroupId)
							.thenCompose(v -> updateApprovalRule(policyId, rule, newGroupId)));
		} else {
			// Use existing group ID
			return updateApprovalRule(policyId, rule, groupId);
		}
	}

	private CompletableFuture<String> createApproverGroup() {
		ObjectNode groupRequest = objectMapper.createObjectNode();
		groupRequest.put("displayName", GROUP_DESCRIPTION);
		groupRequest.put("mailEnabled", false);
		groupRequest.put("mailNickname", APPROVER_GROUP_NICKNAME);
		groupRequest.put("securityEnabled", true);
		groupRequest.put(DESCRIPTION_FIELD, GROUP_DESCRIPTION);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withMethod(HttpMethod.POST)
						.withEndpoint(GROUPS_ENDPOINT)
						.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
						.withBody(HttpRequest.BodyPublishers.ofString(groupRequest.toString()))
						.build()
		).thenApply(response -> response.get("id").asText());
	}

	private CompletableFuture<Void> populateApproverGroup(String approverGroupId) {
		return getCurrentGlobalAdmins()
				.thenCompose(
						adminIds -> addMembersToGroup(approverGroupId, adminIds)
				);
	}

	private CompletableFuture<List<String>> getCurrentGlobalAdmins() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(ROLE_ASSIGNMENTS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "roleDefinitionId eq '" + roleId + "'")
						.build()
		).thenApply(response -> {
			List<String> adminIds = new ArrayList<>();
			JsonNode assignments = response.get(VALUE_FIELD);
			for (JsonNode assignment : assignments) {
				adminIds.add(assignment.get("principalId").asText());
			}
			return adminIds;
		});
	}

	private CompletableFuture<Void> addMembersToGroup(String groupId, List<String> memberIds) {
		List<CompletableFuture<Void>> addFutures = new ArrayList<>();

		for (String memberId : memberIds) {
			ObjectNode memberRef = objectMapper.createObjectNode();
			memberRef.put("@odata.id", "https://graph.microsoft.com/v1.0/directoryObjects/" + memberId);

			CompletableFuture<Void> addFuture = graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.POST)
							.withEndpoint(GROUPS_ENDPOINT + "/" + groupId + "/members/$ref")
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(HttpRequest.BodyPublishers.ofString(memberRef.toString()))
							.build()
			).thenAccept(response -> {
			});

			addFutures.add(addFuture);
		}

		return CompletableFuture.allOf(addFutures.toArray(new CompletableFuture[0]));
	}

	private CompletableFuture<JsonNode> getPolicyAssignment() {
		String filter = String.format("scopeId eq '/' and scopeType eq 'DirectoryRole' and roleDefinitionId eq '%s'", roleId);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(POLICY_ASSIGNMENTS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, filter)
						.build()
		).thenApply(response -> {
			if (!response.has(VALUE_FIELD) || response.get(VALUE_FIELD).size() == 0) {
				throw new GraphClientException("No policy assignment found for role: " + roleId);
			}
			return response.get(VALUE_FIELD).get(0);
		});
	}

	private String extractPolicyId(JsonNode policyAssignment) {
		if (!policyAssignment.has("policyId")) {
			throw new GraphClientException("Policy assignment does not contain policyId");
		}
		return policyAssignment.get("policyId").asText();
	}

	private CompletableFuture<JsonNode> getRoleManagementPolicy(String policyId) {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(POLICIES_ENDPOINT + "/" + policyId)
						.addQueryParam(GROUP_EXPAND_KEY, RULES)
						.build()
		);
	}

	private JsonNode findApprovalRule(JsonNode policy) {
		JsonNode rules = policy.path(RULES);
		if (rules.isArray()) {
			for (JsonNode rule : rules) {
				if (isEndUserApprovalRule(rule)) {
					return rule;
				}
			}
		}
		return null;
	}

	private boolean isEndUserApprovalRule(JsonNode rule) {
		return "#microsoft.graph.unifiedRoleManagementPolicyApprovalRule".equals(rule.path(ODATA_TYPE).asText()) &&
				"EndUser".equals(rule.path(TARGET).path(CALLER).asText()) &&
				"Assignment".equals(rule.path(TARGET).path(LEVEL).asText());
	}

	private CompletableFuture<JsonNode> updateApprovalRule(String policyId,
														   Models.UnifiedRoleManagementPolicyRule rule,
														   String approverGroupId) {
		try {
			if(rule.getSetting() == null) {
				// Create the approval rule settings
				Models.ApprovalSettings settings = new Models.ApprovalSettings();
				settings.setApprovalRequiredForExtension(false);
				settings.setRequestorJustificationRequired(true);
				settings.setApprovalMode(SINGLE_STAGE);
				rule.setSetting(settings);
			}

			rule.getSetting().setApprovalRequired(true);

			if(CollectionUtils.isEmpty(rule.getSetting().getApprovalStages())) {
				// Create the approval stage
				Models.ApprovalStage stage = new Models.ApprovalStage();
				stage.setApprovalStageTimeOutInDays(1);
				stage.setApproverJustificationRequired(true);
				stage.setEscalationTimeInMinutes(0);
				stage.setEscalationEnabled(false);
				rule.getSetting().setApprovalStages(List.of(stage));
			}

			if(CollectionUtils.isEmpty(rule.getSetting().getApprovalStages().getFirst().getPrimaryApprovers())) {
				// Create the approver
				Models.Approver approver = new Models.Approver();
				approver.setOdataType(GROUP_MEMBERS_TYPE);
				approver.setBackup(false);
				approver.setId(approverGroupId);
				approver.setDescription(GROUP_DESCRIPTION);

				// Set up the stage with approvers
				rule.getSetting().getApprovalStages().getFirst().setPrimaryApprovers(List.of(approver));
			}

			if(CollectionUtils.isEmpty(rule.getSetting().getApprovalStages().getFirst().getEscalationApprovers())) {
				rule.getSetting().getApprovalStages().getFirst().setEscalationApprovers(new ArrayList<>());
			}

			// Convert to JSON for the API request
			JsonNode requestBody = ruleToJson(rule);
			String jsonString = objectMapper.writeValueAsString(requestBody);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.PATCH)
							.withEndpoint(POLICIES_ENDPOINT + "/" + policyId + "/rules/" + rule.id)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(HttpRequest.BodyPublishers.ofString(jsonString))
							.build()
			).thenCompose(result -> handleRuleUpdateResult(result, approverGroupId));
		} catch (Exception e) {
			return CompletableFuture.failedFuture(
					new GraphClientException("Failed to update approval rule", e));
		}
	}

	private CompletableFuture<JsonNode> handleRuleUpdateResult(JsonNode result, String approverGroupId) {
		if (result != null && !result.has(Constants.ERROR_FIELD)) {
			logger.info("Successfully configured approval requirement for role {}", roleId);
			return verifyRoleSettings(approverGroupId)
					.thenCompose(verified ->
							Boolean.TRUE.equals(verified) ?
									IPolicyRemediator.success(getPolicyId(),
											"Role activation now requires approval for role: " + roleId) :
									IPolicyRemediator.failed(getPolicyId(),
											"Verification failed for role: " + roleId)
					);
		} else {
			String error = result != null && result.has(Constants.ERROR_FIELD)
					? result.get(Constants.ERROR_FIELD).asText()
					: "Unknown error configuring approval requirement for role: " + roleId;
			logger.error("Failed to configure approval: {}", error);
			return IPolicyRemediator.failed(getPolicyId(), error);
		}
	}

	private CompletableFuture<Boolean> verifyRoleSettings(String approverGroupId) {
		return getPolicyAssignment()
				.thenCompose(assignment -> getRoleManagementPolicy(extractPolicyId(assignment)))
				.thenApply(policy -> {
					JsonNode approvalRule = findApprovalRule(policy);
					return approvalRule != null &&
							verifyApprovalSettings(approvalRule.path(SETTING), approverGroupId);
				})
				.exceptionally(ex -> {
					logger.error("Failed to verify role settings: {}", ex.getMessage());
					return false;
				});
	}

	private boolean verifyApprovalSettings(JsonNode setting, String approverGroupId) {
		return setting.path(IS_APPROVAL_REQIRED).asBoolean(false) &&
				SINGLE_STAGE.equals(setting.path(APPROVAL_MODE).asText()) &&
				setting.path(APPROVAL_STAGES).size() > 0 &&
				verifyApprovalStage(setting.path(APPROVAL_STAGES).get(0), approverGroupId);
	}

	private boolean verifyApprovalStage(JsonNode stage, String approverGroupId) {
		return stage.path(APPROVAL_TIME_OUT).asInt() == 1 &&
				stage.path(IS_APPR_JUSTIFICATION_REQ).asBoolean(true) &&
				stage.path(IS_ESCALATION_ENABLED).asBoolean(false) &&
				stage.path(ESCALATION_TIMEOUT).asInt() == 60 &&
				verifyPrimaryApprovers(stage.path(PRIMARY_APPROVERS), approverGroupId);
	}

	private boolean verifyPrimaryApprovers(JsonNode approvers, String approverGroupId) {
		if (approvers.isArray() && approvers.size() > 0) {
			JsonNode approver = approvers.get(0);
			return GROUP_MEMBERS_TYPE.equals(approver.path(ODATA_TYPE).asText()) &&
					!approver.path(IS_BACKUP).asBoolean(true) &&
					approverGroupId.equals(approver.path(ID_FIELD).asText());
		}
		return false;
	}

	public static JsonNode findNodeById(ArrayNode arrayNode, String targetId) {
		if (arrayNode == null || targetId == null) {
			return null;
		}
		Iterator<JsonNode> elements = arrayNode.elements();
		while (elements.hasNext()) {
			JsonNode element = elements.next();
			if (element.isObject()) {
				ObjectNode objectNode = (ObjectNode) element;
				if (objectNode.has(ID_FIELD)) {
					JsonNode idNode = objectNode.get(ID_FIELD);
					if (idNode.isTextual() && idNode.asText().equals(targetId)) {
						return objectNode;
					}
				}
			}
		}
		return null;
	}

	/**
	 * Parses a rule JSON into a UnifiedRoleManagementPolicyRule object
	 *
	 * @param policyJson The JSON node representing the policy with rules
	 * @return A UnifiedRoleManagementPolicyRule object parsed from the JSON
	 */
	public Models.UnifiedRoleManagementPolicyRule parseRuleJson(JsonNode policyJson) {
		try {
			JsonNode ruleJson = findNodeById(policyJson.withArray(RULES), "Approval_EndUser_Assignment");
			if (ruleJson == null) {
				return null;
			}

			Models.UnifiedRoleManagementPolicyRule rule = new Models.UnifiedRoleManagementPolicyRule();
			rule.setOdataType(ruleJson.path(ODATA_TYPE).asText());
			rule.setId(ruleJson.path(ID_FIELD).asText());

			// Parse target and settings
			Optional.ofNullable(ruleJson.get(TARGET)).ifPresent(targetNode ->
					rule.setTarget(parseTarget(targetNode)));

			Optional.ofNullable(ruleJson.get(SETTING)).ifPresent(settingNode ->
					rule.setSetting(parseApprovalSettings(settingNode)));

			return rule;
		} catch (Exception e) {
			logger.error("Error parsing rule JSON: {}", e.getMessage(), e);
			throw new EntraIDRemediationException("Failed to parse rule JSON : " + e.getMessage());
		}
	}

	/**
	 * Parses the target section of a rule
	 */
	private Models.Target parseTarget(JsonNode targetNode) {
		Models.Target target = new Models.Target();
		target.setCaller(targetNode.path(CALLER).asText());
		target.setLevel(targetNode.path(LEVEL).asText());

		// Parse arrays using helper methods
		target.setOperations(parseStringArray(targetNode, OPERATIONS));
		target.setInheritableSettings(parseObjectArray(targetNode, INHERITABLE_SETTINGS));
		target.setEnforcedSettings(parseObjectArray(targetNode, ENFORCE_SETTINGS));

		return target;
	}

	/**
	 * Parses the approval settings section of a rule
	 */
	private Models.ApprovalSettings parseApprovalSettings(JsonNode settingNode) {
		Models.ApprovalSettings settings = new Models.ApprovalSettings();
		settings.setApprovalRequired(settingNode.path(IS_APPROVAL_REQIRED).asBoolean());
		settings.setApprovalRequiredForExtension(settingNode.path("isApprovalRequiredForExtension").asBoolean());
		settings.setRequestorJustificationRequired(settingNode.path("isRequestorJustificationRequired").asBoolean());
		settings.setApprovalMode(settingNode.path(APPROVAL_MODE).asText());

		// Parse approval stages if they exist
		if (settingNode.has(APPROVAL_STAGES) && settingNode.get(APPROVAL_STAGES).isArray()) {
			settings.setApprovalStages(parseApprovalStages(settingNode.get(APPROVAL_STAGES)));
		}

		return settings;
	}

	/**
	 * Parses an array of approval stages
	 */
	private List<Models.ApprovalStage> parseApprovalStages(JsonNode stagesNode) {
		List<Models.ApprovalStage> stages = new ArrayList<>();

		for (JsonNode stageNode : stagesNode) {
			Models.ApprovalStage stage = new Models.ApprovalStage();
			stage.setApprovalStageTimeOutInDays(stageNode.path(APPROVAL_TIME_OUT).asInt());
			stage.setApproverJustificationRequired(stageNode.path(IS_APPR_JUSTIFICATION_REQ).asBoolean());
			stage.setEscalationTimeInMinutes(stageNode.path("escalationTimeInMinutes").asInt());
			stage.setEscalationEnabled(stageNode.path(IS_ESCALATION_ENABLED).asBoolean());

			// Parse approvers
			stage.setPrimaryApprovers(parseApprovers(stageNode, PRIMARY_APPROVERS));
			stage.setEscalationApprovers(parseApprovers(stageNode, ESCALATION_APPROVERS));

			stages.add(stage);
		}

		return stages;
	}

	/**
	 * Parses an array of approvers
	 */
	private List<Models.Approver> parseApprovers(JsonNode stageNode, String approversField) {
		if (!stageNode.has(approversField) || !stageNode.get(approversField).isArray()) {
			return new ArrayList<>();
		}

		return StreamSupport.stream(stageNode.get(approversField).spliterator(), false)
				.map(this::parseApprover).toList();
	}

	/**
	 * Parses a single approver
	 */
	private Models.Approver parseApprover(JsonNode approverNode) {
		Models.Approver approver = new Models.Approver();
		approver.setOdataType(approverNode.path(ODATA_TYPE).asText());
		approver.setBackup(approverNode.path(IS_BACKUP).asBoolean());

		// Handle both id and groupId fields
		String id = Optional.ofNullable(approverNode.get(ID_FIELD))
				.or(() -> Optional.ofNullable(approverNode.get(GROUP_ID)))
				.map(JsonNode::asText)
				.orElse(null);
		approver.setId(id);

		approver.setDescription(approverNode.path(DESCRIPTION_FIELD).asText());
		return approver;
	}

	/**
	 * Utility method to parse a string array from a node
	 */
	private List<String> parseStringArray(JsonNode node, String fieldName) {
		if (!node.has(fieldName) || !node.get(fieldName).isArray()) {
			return new ArrayList<>();
		}

		List<String> items = new ArrayList<>();
		node.get(fieldName).forEach(item -> items.add(item.asText()));
		return items;
	}

	/**
	 * Utility method to parse an object array from a node
	 */
	private List<Object> parseObjectArray(JsonNode node, String fieldName) {
		if (!node.has(fieldName) || !node.get(fieldName).isArray()) {
			return new ArrayList<>();
		}

		List<Object> items = new ArrayList<>();
		node.get(fieldName).forEach(items::add);
		return items;
	}

	/**
	 * Convenience method to extract the approver group ID from a rule
	 *
	 * @param rule The rule object to extract the group ID from
	 * @return The group ID of the first primary approver, or null if not found
	 */
	public ApproverGroupInfo extractApproverGroupId(Models.UnifiedRoleManagementPolicyRule rule) {
		if (rule == null || rule.getSetting() == null || rule.getSetting().getApprovalStages() == null
				|| rule.getSetting().getApprovalStages().isEmpty()) {
			return null;
		}

		for (Models.ApprovalStage stage : rule.getSetting().getApprovalStages()) {
			if (CollectionUtils.isNotEmpty(stage.getPrimaryApprovers())) {
				for (Models.Approver approver : stage.getPrimaryApprovers()) {
					if (GROUP_MEMBERS_TYPE.equals(approver.getOdataType())) {
						return new ApproverGroupInfo(approver.getId(), approver.getDescription());
					}
				}
			}
		}
		return null;
	}

	/**
	 * Convert a rule model back to a JsonNode for API operations
	 *
	 * @param rule The rule object to convert
	 * @return A JsonNode representation of the rule
	 */
	public JsonNode ruleToJson(Models.UnifiedRoleManagementPolicyRule rule) {
		if (rule == null) {
			return null;
		}

		ObjectNode ruleNode = objectMapper.createObjectNode();

		// Add basic properties
		ruleNode.put(ODATA_TYPE, rule.getOdataType());
		ruleNode.put(ID_FIELD, rule.getId());

		// Add target and settings
		Optional.ofNullable(rule.getTarget()).ifPresent(target ->
				ruleNode.set(TARGET, createTargetNode(target)));

		Optional.ofNullable(rule.getSetting()).ifPresent(setting ->
				ruleNode.set(SETTING, createSettingNode(setting)));

		return ruleNode;
	}

	/**
	 * Creates JSON representation of a target
	 */
	private ObjectNode createTargetNode(Models.Target target) {
		ObjectNode targetNode = objectMapper.createObjectNode();
		targetNode.put(CALLER, target.getCaller());
		targetNode.put(LEVEL, target.getLevel());

		// Add arrays using helper methods
		addStringArrayToNode(targetNode, OPERATIONS, target.getOperations());
		addPojoArrayToNode(targetNode, INHERITABLE_SETTINGS, target.getInheritableSettings());
		addPojoArrayToNode(targetNode, ENFORCE_SETTINGS, target.getEnforcedSettings());

		return targetNode;
	}

	/**
	 * Creates JSON representation of approval settings
	 */
	private ObjectNode createSettingNode(Models.ApprovalSettings setting) {
		ObjectNode settingNode = objectMapper.createObjectNode();
		settingNode.put(IS_APPROVAL_REQIRED, setting.isApprovalRequired());
		settingNode.put("isApprovalRequiredForExtension", setting.isApprovalRequiredForExtension());
		settingNode.put("isRequestorJustificationRequired", setting.isRequestorJustificationRequired());
		settingNode.put(APPROVAL_MODE, setting.getApprovalMode());

		// Add approval stages
		addApprovalStagesToNode(settingNode, setting.getApprovalStages());

		return settingNode;
	}

	/**
	 * Adds approval stages to a setting node
	 */
	private void addApprovalStagesToNode(ObjectNode settingNode, List<Models.ApprovalStage> stages) {
		if (stages == null || stages.isEmpty()) {
			return;
		}

		ArrayNode stagesNode = settingNode.putArray(APPROVAL_STAGES);
		for (Models.ApprovalStage stage : stages) {
			ObjectNode stageNode = stagesNode.addObject();
			stageNode.put(APPROVAL_TIME_OUT, stage.getApprovalStageTimeOutInDays());
			stageNode.put(IS_APPR_JUSTIFICATION_REQ, stage.isApproverJustificationRequired());
			stageNode.put("escalationTimeInMinutes", stage.getEscalationTimeInMinutes());
			stageNode.put(IS_ESCALATION_ENABLED, stage.isEscalationEnabled());

			// Add approvers
			addApproversToNode(stageNode, PRIMARY_APPROVERS, stage.getPrimaryApprovers());
			addApproversToNode(stageNode, ESCALATION_APPROVERS, stage.getEscalationApprovers());
		}
	}

	/**
	 * Adds approvers to a stage node
	 */
	private void addApproversToNode(ObjectNode stageNode, String approversField, List<Models.Approver> approvers) {
		if (approvers == null || approvers.isEmpty()) {
			return;
		}

		ArrayNode approversNode = stageNode.putArray(approversField);
		for (Models.Approver approver : approvers) {
			addApproverToNode(approversNode, approver);
		}
	}

	/**
	 * Adds a single approver to an approvers array node
	 */
	private void addApproverToNode(ArrayNode approversNode, Models.Approver approver) {
		ObjectNode approverNode = approversNode.addObject();
		approverNode.put(ODATA_TYPE, approver.getOdataType());
		approverNode.put(IS_BACKUP, approver.isBackup());

		// Use groupId for groupMembers type, otherwise use id
		if (GROUP_MEMBERS_TYPE.equals(approver.getOdataType())) {
			approverNode.put(GROUP_ID, approver.getId());
		} else {
			approverNode.put(ID_FIELD, approver.getId());
		}

		Optional.ofNullable(approver.getDescription())
				.ifPresent(desc -> approverNode.put(DESCRIPTION_FIELD, desc));
	}

	/**
	 * Utility method to add a string array to a node
	 */
	private void addStringArrayToNode(ObjectNode parentNode, String fieldName, List<String> items) {
		if (items == null || items.isEmpty()) {
			parentNode.putArray(fieldName);
			return;
		}

		ArrayNode arrayNode = parentNode.putArray(fieldName);
		items.forEach(arrayNode::add);
	}

	/**
	 * Utility method to add an object array to a node
	 */
	private void addPojoArrayToNode(ObjectNode parentNode, String fieldName, List<Object> items) {
		ArrayNode arrayNode = parentNode.putArray(fieldName);

		if (items != null) {
			items.forEach(arrayNode::addPOJO);
		}
	}

	/**
	 * Represents the existing configuration for role management policy.
	 * Encapsulates policy ID, parsed rule object, and derived approval information.
	 */
	private static class ExistingConfiguration {
		final String policyId;
		final Models.UnifiedRoleManagementPolicyRule rule;

		/**
		 * Creates a new ExistingConfiguration from policy ID and approval rule.
		 *
		 * @param policyId     The policy ID
		 * @param approvalRule The JSON representation of the approval rule
		 */
		ExistingConfiguration(String policyId, Models.UnifiedRoleManagementPolicyRule approvalRule) {
			this.policyId = policyId;
			this.rule = approvalRule;
		}

		public String getPolicyId() {
			return policyId;
		}

		public Models.UnifiedRoleManagementPolicyRule getRule() {
			return rule;
		}

		public boolean hasRule() {
			return rule != null;
		}

		/**
		 * Checks if the rule has a group-based approver and returns the group information if found.
		 *
		 * @return The ApproverGroupInfo if a group approver exists, null otherwise
		 */
		public ApproverGroupInfo findApproverGroup() {
			return Optional.ofNullable(rule)
					.map(Models.UnifiedRoleManagementPolicyRule::getSetting)
					.map(Models.ApprovalSettings::getApprovalStages)
					.filter(stages -> !stages.isEmpty())
					.flatMap(stages -> stages.stream()
							.flatMap(stage -> Optional.ofNullable(stage.getPrimaryApprovers())
									.filter(approvers -> !approvers.isEmpty())
									.map(Collection::stream)
									.orElseGet(Stream::empty))
							.filter(approver -> EntraIDConstants.GROUP_MEMBERS_TYPE.equals(approver.getOdataType()))
							.findFirst())
					.map(approver -> new ApproverGroupInfo(approver.getId(), approver.getDescription()))
					.orElse(null);
		}
	}

	private record ApproverGroupInfo(String groupId, String description) {
	}

	/**
	 * Models for rule parsing
	 */
	public static class Models {
		private Models() {
		}
		// Main rule model
		public static class UnifiedRoleManagementPolicyRule {
			private String odataType;
			private String id;
			private Target target;
			private ApprovalSettings setting;

			// Getters and setters
			public String getOdataType() {
				return odataType;
			}

			public void setOdataType(String odataType) {
				this.odataType = odataType;
			}

			public String getId() {
				return id;
			}

			public void setId(String id) {
				this.id = id;
			}

			public Target getTarget() {
				return target;
			}

			public void setTarget(Target target) {
				this.target = target;
			}

			public ApprovalSettings getSetting() {
				return setting;
			}

			public void setSetting(ApprovalSettings setting) {
				this.setting = setting;
			}
		}

		// Target model
		public static class Target {
			private String caller;
			private List<String> operations;
			private String level;
			private List<Object> inheritableSettings;
			private List<Object> enforcedSettings;

			// Getters and setters
			public String getCaller() {
				return caller;
			}

			public void setCaller(String caller) {
				this.caller = caller;
			}

			public List<String> getOperations() {
				return operations;
			}

			public void setOperations(List<String> operations) {
				this.operations = operations;
			}

			public String getLevel() {
				return level;
			}

			public void setLevel(String level) {
				this.level = level;
			}

			public List<Object> getInheritableSettings() {
				return inheritableSettings;
			}

			public void setInheritableSettings(List<Object> inheritableSettings) {
				this.inheritableSettings = inheritableSettings;
			}

			public List<Object> getEnforcedSettings() {
				return enforcedSettings;
			}

			public void setEnforcedSettings(List<Object> enforcedSettings) {
				this.enforcedSettings = enforcedSettings;
			}
		}

		// ApprovalSettings model
		public static class ApprovalSettings {
			private boolean isApprovalRequired;
			private boolean isApprovalRequiredForExtension;
			private boolean isRequestorJustificationRequired;
			private String approvalMode;
			private List<ApprovalStage> approvalStages;

			// Getters and setters
			public boolean isApprovalRequired() {
				return isApprovalRequired;
			}

			public void setApprovalRequired(boolean approvalRequired) {
				isApprovalRequired = approvalRequired;
			}

			public boolean isApprovalRequiredForExtension() {
				return isApprovalRequiredForExtension;
			}

			public void setApprovalRequiredForExtension(boolean approvalRequiredForExtension) {
				isApprovalRequiredForExtension = approvalRequiredForExtension;
			}

			public boolean isRequestorJustificationRequired() {
				return isRequestorJustificationRequired;
			}

			public void setRequestorJustificationRequired(boolean requestorJustificationRequired) {
				isRequestorJustificationRequired = requestorJustificationRequired;
			}

			public String getApprovalMode() {
				return approvalMode;
			}

			public void setApprovalMode(String approvalMode) {
				this.approvalMode = approvalMode;
			}

			public List<ApprovalStage> getApprovalStages() {
				return approvalStages;
			}

			public void setApprovalStages(List<ApprovalStage> approvalStages) {
				this.approvalStages = approvalStages;
			}
		}

		// ApprovalStage model
		public static class ApprovalStage {
			private int approvalStageTimeOutInDays;
			private boolean isApproverJustificationRequired;
			private int escalationTimeInMinutes;
			private boolean isEscalationEnabled;
			private List<Approver> primaryApprovers;
			private List<Approver> escalationApprovers;

			// Getters and setters
			public int getApprovalStageTimeOutInDays() {
				return approvalStageTimeOutInDays;
			}

			public void setApprovalStageTimeOutInDays(int approvalStageTimeOutInDays) {
				this.approvalStageTimeOutInDays = approvalStageTimeOutInDays;
			}

			public boolean isApproverJustificationRequired() {
				return isApproverJustificationRequired;
			}

			public void setApproverJustificationRequired(boolean approverJustificationRequired) {
				isApproverJustificationRequired = approverJustificationRequired;
			}

			public int getEscalationTimeInMinutes() {
				return escalationTimeInMinutes;
			}

			public void setEscalationTimeInMinutes(int escalationTimeInMinutes) {
				this.escalationTimeInMinutes = escalationTimeInMinutes;
			}

			public boolean isEscalationEnabled() {
				return isEscalationEnabled;
			}

			public void setEscalationEnabled(boolean escalationEnabled) {
				isEscalationEnabled = escalationEnabled;
			}

			public List<Approver> getPrimaryApprovers() {
				return primaryApprovers;
			}

			public void setPrimaryApprovers(List<Approver> primaryApprovers) {
				this.primaryApprovers = primaryApprovers;
			}

			public List<Approver> getEscalationApprovers() {
				return escalationApprovers;
			}

			public void setEscalationApprovers(List<Approver> escalationApprovers) {
				this.escalationApprovers = escalationApprovers;
			}
		}

		// Approver model
		public static class Approver {
			private String odataType;
			private boolean isBackup;
			private String id;
			private String description;

			// Getters and setters
			public String getOdataType() {
				return odataType;
			}

			public void setOdataType(String odataType) {
				this.odataType = odataType;
			}

			public boolean isBackup() {
				return isBackup;
			}

			public void setBackup(boolean backup) {
				isBackup = backup;
			}

			public String getId() {
				return id;
			}

			public void setId(String id) {
				this.id = id;
			}

			public String getDescription() {
				return description;
			}

			public void setDescription(String description) {
				this.description = description;
			}
		}
	}
}