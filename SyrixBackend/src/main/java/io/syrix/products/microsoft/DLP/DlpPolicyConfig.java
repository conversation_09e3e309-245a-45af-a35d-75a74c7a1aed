package io.syrix.products.microsoft.DLP;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Configuration class for DLP policies.
 * Aligns with New-DlpCompliancePolicy PowerShell cmdlet.
 */
public class DlpPolicyConfig {
	private final String name;
	private final List<String> exchangeLocation;
	private final List<String> sharePointLocation;
	private final List<String> oneDriveLocation;
	private final List<String> teamsLocation;
	private final List<String> endpointDlpLocation;
	private final List<String> thirdPartyAppDlpLocation;
	private final List<String> excludedExchangeLocation;
	private final List<String> excludedOneDriveLocation;
	private final List<String> excludedSharePointLocation;
	private final List<String> excludedTeamsLocation;
	private final String comment;
	private final LocalDateTime activationDate;
	private final LocalDateTime expiryDate;
	private final Integer priority;
	private DlpPolicyMode mode;

	private DlpPolicyConfig(Builder builder) {
		this.name = builder.name;
		this.exchangeLocation = builder.exchangeLocation;
		this.sharePointLocation = builder.sharePointLocation;
		this.oneDriveLocation = builder.oneDriveLocation;
		this.teamsLocation = builder.teamsLocation;
		this.endpointDlpLocation = builder.endpointDlpLocation;
		this.thirdPartyAppDlpLocation = builder.thirdPartyAppDlpLocation;
		this.excludedExchangeLocation = builder.excludedExchangeLocation;
		this.excludedOneDriveLocation = builder.excludedOneDriveLocation;
		this.excludedSharePointLocation = builder.excludedSharePointLocation;
		this.excludedTeamsLocation = builder.excludedTeamsLocation;
		this.mode = builder.mode;
		this.comment = builder.comment;
		this.activationDate = builder.activationDate;
		this.expiryDate = builder.expiryDate;
		this.priority = builder.priority;
	}

	/**
	 * Basic constructor for simple policy configuration.
	 */
	public DlpPolicyConfig(String name, String exchangeLocation, String sharePointLocation,
						   String oneDriveLocation, String teamsLocation, DlpPolicyMode mode) {
		this.name = name;
		this.exchangeLocation = exchangeLocation != null ? List.of(exchangeLocation) : null;
		this.sharePointLocation = sharePointLocation != null ? List.of(sharePointLocation) : null;
		this.oneDriveLocation = oneDriveLocation != null ? List.of(oneDriveLocation) : null;
		this.teamsLocation = teamsLocation != null ? List.of(teamsLocation) : null;
		this.endpointDlpLocation = null;
		this.thirdPartyAppDlpLocation = null;
		this.excludedExchangeLocation = null;
		this.excludedOneDriveLocation = null;
		this.excludedSharePointLocation = null;
		this.excludedTeamsLocation = null;
		this.mode = mode; // Default to enforcement mode
		this.comment = null;
		this.activationDate = null;
		this.expiryDate = null;
		this.priority = null;
	}

	public String getName() {
		return name;
	}

	public DlpPolicyMode getMode() {
		return mode;
	}

	public List<String> getExchangeLocation() {
		return exchangeLocation;
	}

	public List<String> getSharePointLocation() {
		return sharePointLocation;
	}

	public List<String> getOneDriveLocation() {
		return oneDriveLocation;
	}

	public List<String> getTeamsLocation() {
		return teamsLocation;
	}

	public List<String> getEndpointDlpLocation() {
		return endpointDlpLocation;
	}

	public List<String> getThirdPartyAppDlpLocation() {
		return thirdPartyAppDlpLocation;
	}

	public List<String> getExcludedExchangeLocation() {
		return excludedExchangeLocation;
	}

	public List<String> getExcludedOneDriveLocation() {
		return excludedOneDriveLocation;
	}

	public List<String> getExcludedSharePointLocation() {
		return excludedSharePointLocation;
	}

	public List<String> getExcludedTeamsLocation() {
		return excludedTeamsLocation;
	}

	public String getComment() {
		return comment;
	}

	public LocalDateTime getActivationDate() {
		return activationDate;
	}

	public LocalDateTime getExpiryDate() {
		return expiryDate;
	}

	public Integer getPriority() {
		return priority;
	}

	/**
	 * Builder for DlpPolicyConfig aligned with PowerShell parameters.
	 */
	public static class Builder {
		private final String name; // Required
		private List<String> exchangeLocation = null;
		private List<String> sharePointLocation = null;
		private List<String> oneDriveLocation = null;
		private List<String> teamsLocation = null;
		private List<String> endpointDlpLocation = null;
		private List<String> thirdPartyAppDlpLocation = null;
		private List<String> excludedExchangeLocation = null;
		private List<String> excludedOneDriveLocation = null;
		private List<String> excludedSharePointLocation = null;
		private List<String> excludedTeamsLocation = null;
		private String comment = null;
		private LocalDateTime activationDate = null;
		private LocalDateTime expiryDate = null;
		private Integer priority = null;
		private DlpPolicyMode mode = DlpPolicyMode.Enable; // Default value

		public Builder(String name) {
			this.name = name;
		}

		public Builder exchangeLocation(List<String> exchangeLocation) {
			this.exchangeLocation = exchangeLocation;
			return this;
		}

		public Builder mode(DlpPolicyMode mode) {
			this.mode = mode;
			return this;
		}

		public Builder exchangeLocation(String exchangeLocation) {
			if (exchangeLocation != null) {
				this.exchangeLocation = List.of(exchangeLocation);
			}
			return this;
		}

		public Builder sharePointLocation(List<String> sharePointLocation) {
			this.sharePointLocation = sharePointLocation;
			return this;
		}

		public Builder sharePointLocation(String sharePointLocation) {
			if (sharePointLocation != null) {
				this.sharePointLocation = List.of(sharePointLocation);
			}
			return this;
		}

		public Builder oneDriveLocation(List<String> oneDriveLocation) {
			this.oneDriveLocation = oneDriveLocation;
			return this;
		}

		public Builder oneDriveLocation(String oneDriveLocation) {
			if (oneDriveLocation != null) {
				this.oneDriveLocation = List.of(oneDriveLocation);
			}
			return this;
		}

		public Builder teamsLocation(List<String> teamsLocation) {
			this.teamsLocation = teamsLocation;
			return this;
		}

		public Builder teamsLocation(String teamsLocation) {
			if (teamsLocation != null) {
				this.teamsLocation = List.of(teamsLocation);
			}
			return this;
		}

		public Builder endpointDlpLocation(List<String> endpointDlpLocation) {
			this.endpointDlpLocation = endpointDlpLocation;
			return this;
		}

		public Builder endpointDlpLocation(String endpointDlpLocation) {
			if (endpointDlpLocation != null) {
				this.endpointDlpLocation = List.of(endpointDlpLocation);
			}
			return this;
		}

		public Builder thirdPartyAppDlpLocation(List<String> thirdPartyAppDlpLocation) {
			this.thirdPartyAppDlpLocation = thirdPartyAppDlpLocation;
			return this;
		}

		public Builder excludedExchangeLocation(List<String> excludedExchangeLocation) {
			this.excludedExchangeLocation = excludedExchangeLocation;
			return this;
		}

		public Builder excludedOneDriveLocation(List<String> excludedOneDriveLocation) {
			this.excludedOneDriveLocation = excludedOneDriveLocation;
			return this;
		}

		public Builder excludedSharePointLocation(List<String> excludedSharePointLocation) {
			this.excludedSharePointLocation = excludedSharePointLocation;
			return this;
		}

		public Builder excludedTeamsLocation(List<String> excludedTeamsLocation) {
			this.excludedTeamsLocation = excludedTeamsLocation;
			return this;
		}

		public Builder comment(String comment) {
			this.comment = comment;
			return this;
		}

		public Builder activationDate(LocalDateTime activationDate) {
			this.activationDate = activationDate;
			return this;
		}

		public Builder expiryDate(LocalDateTime expiryDate) {
			this.expiryDate = expiryDate;
			return this;
		}

		public Builder priority(Integer priority) {
			this.priority = priority;
			return this;
		}

		public DlpPolicyConfig build() {
			return new DlpPolicyConfig(this);
		}
	}
}