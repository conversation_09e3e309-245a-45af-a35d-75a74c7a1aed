package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.model.DynamicGroupConfiguration;
import io.syrix.products.microsoft.entra.service.DynamicGroupService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Policy remediator for MS.AAD.10.1v1 - Dynamic security groups SHALL be configured for guest user management.

 * This remediator checks for the existence of properly configured dynamic groups for guest users
 * and can automatically create or fix them if needed.
 */
@PolicyRemediator("MS.AAD.10.1v1")
public class EntraIDDynamicGroupRemediator extends RemediatorBase {
    
    private static final Logger logger = LoggerFactory.getLogger(EntraIDDynamicGroupRemediator.class);
    private static final String POLICY_ID = "MS.AAD.10.1v1";
    private static final String DEFAULT_GROUP_NAME = "All Guest Users (Dynamic)";
    private static final String DEFAULT_GROUP_DESCRIPTION = "Dynamic security group for all guest users - auto-managed by Syrix compliance";
    
    private final MicrosoftGraphClient graphClient;
    private final DynamicGroupService dynamicGroupService;

    public EntraIDDynamicGroupRemediator(MicrosoftGraphClient graphClient) {
        this.graphClient = graphClient;
        this.dynamicGroupService = new DynamicGroupService(graphClient);
    }
    
    @Override
    public String getPolicyId() {
        return POLICY_ID;
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        logger.info("Starting remediation for policy: {}", POLICY_ID);
        
        return dynamicGroupService.getAllDynamicGroups()
                .thenCompose(allGroups -> performRemediation(allGroups, dynamicGroupService))
                .exceptionally(ex -> {
                    logger.error("Failed to remediate policy: {}", POLICY_ID, ex);
                    return IPolicyRemediator.failed(POLICY_ID,
                            "Failed to remediate dynamic group configuration: " + ex.getMessage()).join();
                });
    }
    
    /**
     * Performs remediation by creating or fixing dynamic guest user groups
     * 
     * @param allGroups List of all dynamic groups in the tenant
     * @param dynamicGroupService Service for dynamic group operations
     * @return CompletableFuture with remediation result
     */
    private CompletableFuture<JsonNode> performRemediation(List<DynamicGroupConfiguration> allGroups, 
                                                          DynamicGroupService dynamicGroupService) {
        List<DynamicGroupConfiguration> properlyConfigured = allGroups.stream()
                .filter(DynamicGroupConfiguration::isProperlyConfiguredForGuestUsers)
                .toList();
        
        List<DynamicGroupConfiguration> misconfigured = allGroups.stream()
                .filter(DynamicGroupConfiguration::isMisconfiguredGuestGroup)
                .toList();
        
        // If no properly configured groups exist, create one
        if (properlyConfigured.isEmpty()) {
            return createNewDynamicGuestGroup(misconfigured, dynamicGroupService);
        }
        
        // If we have properly configured groups but also misconfigured ones, fix the misconfigured ones
        if (!misconfigured.isEmpty()) {
            return fixMisconfiguredGroups(misconfigured, dynamicGroupService);
        }
        
        // Already compliant
        String groupNames = properlyConfigured.stream()
                .map(DynamicGroupConfiguration::getDisplayName)
                .collect(Collectors.joining(", "));
        return IPolicyRemediator.success(POLICY_ID,
                String.format("Dynamic guest user groups are already properly configured. Found %d valid group(s): %s",
                        properlyConfigured.size(), groupNames));
    }
    
    /**
     * Creates a new dynamic guest user group
     * 
     * @param existingMisconfigured List of existing misconfigured groups
     * @param dynamicGroupService Service for dynamic group operations
     * @return CompletableFuture with creation result
     */
    private CompletableFuture<JsonNode> createNewDynamicGuestGroup(List<DynamicGroupConfiguration> existingMisconfigured,
                                                                  DynamicGroupService dynamicGroupService) {
        logger.info("Creating new dynamic guest user group");
        
        // If there are misconfigured groups, try to fix the first one instead of creating a new one
        if (!existingMisconfigured.isEmpty()) {
            DynamicGroupConfiguration firstMisconfigured = existingMisconfigured.getFirst();
            logger.info("Fixing existing misconfigured group: {} ({})", 
                    firstMisconfigured.getDisplayName(), firstMisconfigured.getId());
            
            return dynamicGroupService.fixGuestGroupConfiguration(firstMisconfigured.getId())
                    .thenApply(result -> IPolicyRemediator.success(POLICY_ID,
                            String.format("Fixed existing dynamic group '%s' to properly manage guest users", 
                                    firstMisconfigured.getDisplayName())).join());
        }
        
        // Create a completely new group
        return dynamicGroupService.createDynamicGuestGroup(DEFAULT_GROUP_NAME, DEFAULT_GROUP_DESCRIPTION)
                .thenApply(createdGroup -> {
                    String message = String.format("Created new dynamic guest user group '%s' (ID: %s)", 
                            createdGroup.getDisplayName(), createdGroup.getId());
                    logger.info("Successfully created dynamic guest user group: {}", message);
                    return IPolicyRemediator.success(POLICY_ID, message).join();
                });
    }
    
    /**
     * Fixes misconfigured dynamic groups
     * 
     * @param misconfiguredGroups List of misconfigured groups to fix
     * @param dynamicGroupService Service for dynamic group operations
     * @return CompletableFuture with fix result
     */
    private CompletableFuture<JsonNode> fixMisconfiguredGroups(List<DynamicGroupConfiguration> misconfiguredGroups,
                                                              DynamicGroupService dynamicGroupService) {
        logger.info("Fixing {} misconfigured dynamic group(s)", misconfiguredGroups.size());
        
        // Fix the first misconfigured group
        DynamicGroupConfiguration groupToFix = misconfiguredGroups.getFirst();
        
        return dynamicGroupService.fixGuestGroupConfiguration(groupToFix.getId())
                .thenApply(result -> {
                    String message = String.format("Fixed misconfigured dynamic group '%s' to properly manage guest users", 
                            groupToFix.getDisplayName());
                    if (misconfiguredGroups.size() > 1) {
                        message += String.format(". Note: %d additional misconfigured group(s) remain", 
                                misconfiguredGroups.size() - 1);
                    }
                    logger.info("Successfully fixed dynamic group: {}", message);
                    return IPolicyRemediator.success(POLICY_ID, message).join();
                });
    }
    
}