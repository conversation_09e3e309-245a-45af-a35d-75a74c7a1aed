package io.syrix.products.microsoft.exo.remediation;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.HttpPipeline;
import com.azure.core.http.HttpPipelineBuilder;
import com.azure.core.http.policy.BearerTokenAuthenticationPolicy;
import com.azure.core.http.policy.RetryPolicy;
import com.azure.core.http.policy.UserAgentPolicy;
import com.azure.resourcemanager.dns.fluent.DnsManagementClient;
import com.azure.resourcemanager.dns.fluent.RecordSetsClient;
import com.azure.resourcemanager.dns.fluent.models.RecordSetInner;
import com.azure.resourcemanager.dns.models.RecordType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

import static io.syrix.common.constants.Constants.APPLICATION_JSON;
import static io.syrix.common.constants.Constants.AUTHORIZATION_HEADER;
import static io.syrix.common.constants.Constants.BEARER_PREFIX;
import static io.syrix.common.constants.Constants.CONTENT_TYPE_HEADER;
import static io.syrix.common.constants.Constants.DOMAIN_FIELD;

//TODO Class is not complete and not checked.
/**
 * Service for updating SPF records across various DNS providers.
 * Supports multiple DNS providers with provider-specific implementations.
 */
public class SpfRecordUpdater {
	private static final Logger logger = LoggerFactory.getLogger(SpfRecordUpdater.class);
	private static final String AZURE_MANAGEMENT_SCOPE = "https://management.azure.com/.default";
	private static final Pattern SPF_PATTERN = Pattern.compile("v=spf1\\s+.*");
	public static final String AZURE_DNS = "Azure DNS";
	public static final String CLOUDFLARE = "Cloudflare";
	public static final String AWS_ROUTE_53 = "AWS Route 53";
	public static final String GO_DADDY = "GoDaddy";
	public static final String STATUS_FIELD_NAME = "status";
	public static final String SUCCESS = "success";
	public static final String PROVIDER = "provider";
	public static final String RECORD = "record";
	public static final String OPERATION = "operation";
	public static final String UPDATED = "updated";

	private final DnsProviderDetector providerDetector;
	private final ObjectMapper objectMapper;
	private final Map<String, Object> credentials;
	private final HttpClient httpClient;

	/**
	 * Creates a new SpfRecordUpdater.
	 *
	 * @param providerDetector DNS provider detector
	 * @param objectMapper ObjectMapper for JSON processing
	 * @param credentials Map of provider-specific credentials
	 */
	public SpfRecordUpdater(DnsProviderDetector providerDetector, ObjectMapper objectMapper, Map<String, Object> credentials) {
		this.providerDetector = providerDetector;
		this.objectMapper = objectMapper;
		this.credentials = credentials;
		this.httpClient = HttpClient.newBuilder().build();
	}

	/**
	 * Updates the SPF record for a domain.
	 *
	 * @param domain Domain name
	 * @param newSpfRecord New SPF record to set
	 * @return CompletableFuture with result of update operation
	 */
	public CompletableFuture<JsonNode> updateSpfRecord(String domain, String newSpfRecord) {
		logger.info("Updating SPF record for domain {}: {}", domain, newSpfRecord);

		// First detect the DNS provider
		DnsProviderDetector.DnsProviderInfo providerInfo = providerDetector.detectProvider(domain);
		logger.info("Detected provider for {}: {}", domain, providerInfo.getProviderName());

		// Select the appropriate provider-specific implementation
		return switch (providerInfo.getProviderName()) {
			case AZURE_DNS -> updateAzureDnsSpfRecord(domain, newSpfRecord);
			case CLOUDFLARE -> updateCloudflareSpfRecord(domain, newSpfRecord);
			case AWS_ROUTE_53 -> updateAwsSpfRecord(domain, newSpfRecord);
			case GO_DADDY -> updateGoDaddySpfRecord(domain, newSpfRecord);
			default -> {
				logger.warn("Unsupported DNS provider for {}: {}", domain, providerInfo.getProviderName());
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, "error");
				result.put("message", "Unsupported DNS provider: " + providerInfo.getProviderName());
				yield CompletableFuture.completedFuture(result);
			}
		};
	}

	/**
	 * Updates SPF record in Azure DNS.
	 */
	private CompletableFuture<JsonNode> updateAzureDnsSpfRecord(String domain, String newSpfRecord) {
		logger.info("Updating Azure DNS SPF record for domain: {}", domain);

		return CompletableFuture.supplyAsync(() -> {
			try {
				// Extract Azure credentials
				TokenCredential credential = (TokenCredential) credentials.get("azureCredential");
				String subscriptionId = (String) credentials.get("azureSubscriptionId");
				String resourceGroup = (String) credentials.get("azureResourceGroup");

				if (credential == null || subscriptionId == null || resourceGroup == null) {
					throw new IllegalArgumentException("Missing required Azure credentials");
				}

				// Create Azure DNS client
				DnsManagementClient dnsClient = createAzureDnsClient(credential, subscriptionId);
				RecordSetsClient recordSetsClient = dnsClient.getRecordSets();

				// Extract zone name (e.g., example.com from sub.example.com)
				String zoneName = extractZoneName(domain);

				// Get existing TXT records
				List<RecordSetInner> txtRecords = recordSetsClient.listByType(resourceGroup, zoneName, RecordType.TXT).stream().toList();

				// Find and update SPF record (typically at root "@")
				boolean updated = false;
				for (RecordSetInner recordSet : txtRecords) {
					if ("@".equals(recordSet.name()) || recordSet.name() == null || recordSet.name().isEmpty()) {
						// Found root TXT records, update them
						List<String> updatedValues = new ArrayList<>();
						boolean hadSpf = false;

						// Replace existing SPF record or keep other TXT records
						if (recordSet.txtRecords() != null) {
							for (var txtRecord : recordSet.txtRecords()) {
								String txtValue = String.join("", txtRecord.value());
								if (SPF_PATTERN.matcher(txtValue).matches()) {
									updatedValues.add(newSpfRecord);
									hadSpf = true;
								} else {
									updatedValues.add(txtValue);
								}
							}
						}

						// If no SPF record existed, add it
						if (!hadSpf) {
							updatedValues.add(newSpfRecord);
						}

						// Update the record set
						// (Implementation would use recordSetsClient.createOrUpdate)
						updated = true;
						break;
					}
				}

				// If no root TXT record found, create one
				if (!updated) {
					// Create new TXT record with SPF
					// (Implementation would use recordSetsClient.createOrUpdate)
				}

				// Return success result
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, SUCCESS);
				result.put(PROVIDER, AZURE_DNS);
				result.put(RECORD, newSpfRecord);
				return result;

			} catch (Exception e) {
				logger.error("Error updating Azure DNS SPF record for {}: {}", domain, e.getMessage());
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, "error");
				result.put(PROVIDER, AZURE_DNS);
				result.put("message", e.getMessage());
				return result;
			}
		});
	}

	/**
	 * Updates SPF record in Cloudflare.
	 */
	private CompletableFuture<JsonNode> updateCloudflareSpfRecord(String domain, String newSpfRecord) {
		logger.info("Updating Cloudflare SPF record for domain: {}", domain);

		return CompletableFuture.supplyAsync(() -> {
			try {
				// Extract Cloudflare credentials
				String apiToken = (String) credentials.get("cloudflareApiToken");
				String zoneId = (String) credentials.get("cloudflareZoneId");

				if (apiToken == null) {
					throw new IllegalArgumentException("Missing Cloudflare API token");
				}

				// If zone ID is not provided, we would need to look it up
				if (zoneId == null) {
					zoneId = lookupCloudflareZoneId(domain, apiToken);
				}

				// First, check for existing SPF record
				String endpoint = "https://api.cloudflare.com/client/v4/zones/" + zoneId + "/dns_records?type=TXT";

				HttpRequest listRequest = HttpRequest.newBuilder()
						.uri(URI.create(endpoint))
						.header(AUTHORIZATION_HEADER, BEARER_PREFIX + apiToken)
						.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
						.GET()
						.build();

				HttpResponse<String> listResponse = httpClient.send(listRequest, HttpResponse.BodyHandlers.ofString());

				if (listResponse.statusCode() != 200) {
					throw new RuntimeException("Failed to list Cloudflare DNS records: " + listResponse.body());
				}

				// Parse response to find existing SPF record
				JsonNode recordsResponse = objectMapper.readTree(listResponse.body());
				JsonNode records = recordsResponse.path("result");

				String recordId = null;
				boolean foundSpf = false;

				for (JsonNode sfpRecord : records) {
					if (sfpRecord.path("type").asText().equals("TXT")) {
						String content = sfpRecord.path("content").asText();
						if (content.startsWith("v=spf1")) {
							foundSpf = true;
							recordId = sfpRecord.path("id").asText();
							break;
						}
					}
				}

				// Build request based on whether we're creating or updating
				String requestBody;
				HttpRequest updateRequest;

				if (foundSpf) {
					// Update existing record
					requestBody = String.format(
							"{\"content\":\"%s\"}",
							newSpfRecord
					);

					updateRequest = HttpRequest.newBuilder()
							.uri(URI.create("https://api.cloudflare.com/client/v4/zones/" + zoneId + "/dns_records/" + recordId))
							.header(AUTHORIZATION_HEADER, BEARER_PREFIX + apiToken)
							.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
							.method("PATCH", HttpRequest.BodyPublishers.ofString(requestBody))
							.build();
				} else {
					// Create new record
					requestBody = String.format(
							"{\"type\":\"TXT\",\"name\":\"@\",\"content\":\"%s\",\"ttl\":3600}",
							newSpfRecord
					);

					updateRequest = HttpRequest.newBuilder()
							.uri(URI.create("https://api.cloudflare.com/client/v4/zones/" + zoneId + "/dns_records"))
							.header(AUTHORIZATION_HEADER, BEARER_PREFIX + apiToken)
							.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
							.POST(HttpRequest.BodyPublishers.ofString(requestBody))
							.build();
				}

				// Send update request
				HttpResponse<String> updateResponse = httpClient.send(updateRequest, HttpResponse.BodyHandlers.ofString());

				if (updateResponse.statusCode() != 200) {
					throw new RuntimeException("Failed to update Cloudflare DNS record: " + updateResponse.body());
				}

				// Return success result
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, SUCCESS);
				result.put(PROVIDER, CLOUDFLARE);
				result.put(RECORD, newSpfRecord);
				result.put(OPERATION, foundSpf ? UPDATED : "created");
				return result;

			} catch (Exception e) {
				logger.error("Error updating Cloudflare SPF record for {}: {}", domain, e.getMessage());
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, "error");
				result.put(PROVIDER, CLOUDFLARE);
				result.put("message", e.getMessage());
				return result;
			}
		});
	}

	/**
	 * Updates SPF record in AWS Route 53.
	 */
	private CompletableFuture<JsonNode> updateAwsSpfRecord(String domain, String newSpfRecord) {
		logger.info("Updating AWS Route 53 SPF record for domain: {}", domain);

		return CompletableFuture.supplyAsync(() -> {
			try {
				// AWS implementation would typically use the AWS SDK (software.amazon.awssdk:route53)
				// For brevity, just including the outline of implementation

				// 1. Get AWS credentials
				String accessKey = (String) credentials.get("awsAccessKey");
				String secretKey = (String) credentials.get("awsSecretKey");

				if (accessKey == null || secretKey == null) {
					throw new IllegalArgumentException("Missing AWS credentials");
				}

				// 2. Create Route 53 client
				// Route53Client route53 = Route53Client.builder()
				//     .credentialsProvider(StaticCredentialsProvider.create(
				//         AwsBasicCredentials.create(accessKey, secretKey)))
				//     .build();

				// 3. Get hosted zone ID
				// String hostedZoneId = getHostedZoneId(route53, domain);

				// 4. List existing records to find SPF
				// ListResourceRecordSetsResponse records = route53.listResourceRecordSets(r -> r
				//     .hostedZoneId(hostedZoneId)
				//     .startRecordType("TXT"));

				// 5. Create change batch to update or create SPF record
				// ChangeResourceRecordSetsResponse response = route53.changeResourceRecordSets(r -> r
				//     .hostedZoneId(hostedZoneId)
				//     .changeBatch(b -> b
				//         .changes(c -> c
				//             .action("UPSERT")
				//             .resourceRecordSet(s -> s
				//                 .name(domain + ".")
				//                 .type("TXT")
				//                 .ttl(3600L)
				//                 .resourceRecords(rr -> rr
				//                     .value("\"" + newSpfRecord + "\"")
				//                 )
				//             )
				//         )
				//     )
				// );

				// For this example, we'll just return a mock success
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, SUCCESS);
				result.put(PROVIDER, AWS_ROUTE_53);
				result.put(RECORD, newSpfRecord);
				result.put(OPERATION, UPDATED);  // Or "created" based on actual operation
				return result;

			} catch (Exception e) {
				logger.error("Error updating AWS Route 53 SPF record for {}: {}", domain, e.getMessage());
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, "error");
				result.put(PROVIDER, AWS_ROUTE_53);
				result.put("message", e.getMessage());
				return result;
			}
		});
	}

	/**
	 * Updates SPF record in GoDaddy.
	 */
	private CompletableFuture<JsonNode> updateGoDaddySpfRecord(String domain, String newSpfRecord) {
		logger.info("Updating GoDaddy SPF record for domain: {}", domain);

		return CompletableFuture.supplyAsync(() -> {
			try {
				// Get GoDaddy credentials
				String apiKey = (String) credentials.get("godaddyApiKey");
				String apiSecret = (String) credentials.get("godaddyApiSecret");

				if (apiKey == null || apiSecret == null) {
					throw new IllegalArgumentException("Missing GoDaddy API credentials");
				}

				// First get existing records
				String endpoint = "https://api.godaddy.com/v1/domains/" + domain + "/records/TXT/@";

				HttpRequest getRequest = HttpRequest.newBuilder()
						.uri(URI.create(endpoint))
						.header(AUTHORIZATION_HEADER, "sso-key " + apiKey + ":" + apiSecret)
						.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
						.GET()
						.build();

				HttpResponse<String> getResponse = httpClient.send(getRequest, HttpResponse.BodyHandlers.ofString());

				if (getResponse.statusCode() != 200) {
					throw new RuntimeException("Failed to get GoDaddy DNS records: " + getResponse.statusCode() + " " + getResponse.body());
				}

				// Parse response to find existing records
				JsonNode existingRecords = objectMapper.readTree(getResponse.body());
				List<ObjectNode> updatedRecords = new ArrayList<>();
				boolean foundSpf = false;

				// Update SPF record or keep other TXT records
				for (JsonNode record : existingRecords) {
					ObjectNode updatedRecord = objectMapper.createObjectNode();
					String data = record.path("data").asText();

					if (SPF_PATTERN.matcher(data).matches()) {
						updatedRecord.put("data", newSpfRecord);
						foundSpf = true;
					} else {
						updatedRecord.put("data", data);
					}

					updatedRecord.put("name", "@");
					updatedRecord.put("ttl", 3600);
					updatedRecord.put("type", "TXT");

					updatedRecords.add(updatedRecord);
				}

				// If no SPF record existed, add it
				if (!foundSpf) {
					ObjectNode newRecord = objectMapper.createObjectNode();
					newRecord.put("data", newSpfRecord);
					newRecord.put("name", "@");
					newRecord.put("ttl", 3600);
					newRecord.put("type", "TXT");
					updatedRecords.add(newRecord);
				}

				// Send update
				String updateBody = objectMapper.writeValueAsString(updatedRecords);

				HttpRequest putRequest = HttpRequest.newBuilder()
						.uri(URI.create(endpoint))
						.header(AUTHORIZATION_HEADER, "sso-key " + apiKey + ":" + apiSecret)
						.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
						.PUT(HttpRequest.BodyPublishers.ofString(updateBody))
						.build();

				HttpResponse<String> putResponse = httpClient.send(putRequest, HttpResponse.BodyHandlers.ofString());

				if (putResponse.statusCode() != 200) {
					throw new RuntimeException("Failed to update GoDaddy DNS records: " + putResponse.statusCode() + " " + putResponse.body());
				}

				// Return success result
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, SUCCESS);
				result.put(PROVIDER, GO_DADDY);
				result.put(RECORD, newSpfRecord);
				result.put(OPERATION, foundSpf ? UPDATED : "created");
				return result;

			} catch (Exception e) {
				logger.error("Error updating GoDaddy SPF record for {}: {}", domain, e.getMessage());
				ObjectNode result = objectMapper.createObjectNode();
				result.put(DOMAIN_FIELD, domain);
				result.put(STATUS_FIELD_NAME, "error");
				result.put(PROVIDER, GO_DADDY);
				result.put("message", e.getMessage());
				return result;
			}
		});
	}

	/**
	 * Creates an Azure DNS client.
	 */
	private DnsManagementClient createAzureDnsClient(TokenCredential credential, String subscriptionId) {
		HttpPipeline pipeline = new HttpPipelineBuilder()
				.policies(
						new UserAgentPolicy("ExchangeSpfRemediator"),
						new RetryPolicy(),
						new BearerTokenAuthenticationPolicy(credential, AZURE_MANAGEMENT_SCOPE)
				)
				.build();

		return new com.azure.resourcemanager.dns.implementation.DnsManagementClientBuilder()
				.pipeline(pipeline)
				.subscriptionId(subscriptionId)
				.buildClient();
	}

	/**
	 * Extracts the zone name from a domain (e.g., example.com from sub.example.com).
	 */
	private String extractZoneName(String domain) {
		// For simplicity, assume second-level domain
		// In a production environment, would need to handle more complex cases
		int lastDot = domain.lastIndexOf('.');
		if (lastDot > 0) {
			int secondLastDot = domain.lastIndexOf('.', lastDot - 1);
			if (secondLastDot > 0) {
				return domain.substring(secondLastDot + 1);
			}
		}
		return domain;
	}

	/**
	 * Looks up a Cloudflare zone ID for a domain.
	 */
	private String lookupCloudflareZoneId(String domain, String apiToken) throws Exception {
		String endpoint = "https://api.cloudflare.com/client/v4/zones?name=" + domain;

		HttpRequest request = HttpRequest.newBuilder()
				.uri(URI.create(endpoint))
				.header(AUTHORIZATION_HEADER, BEARER_PREFIX + apiToken)
				.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
				.GET()
				.build();

		HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

		if (response.statusCode() != 200) {
			throw new RuntimeException("Failed to lookup Cloudflare zone: " + response.body());
		}

		JsonNode responseJson = objectMapper.readTree(response.body());
		JsonNode results = responseJson.path("result");

		if (results.isEmpty()) {
			throw new RuntimeException("No Cloudflare zone found for domain: " + domain);
		}

		return results.path(0).path("id").asText();
	}
}