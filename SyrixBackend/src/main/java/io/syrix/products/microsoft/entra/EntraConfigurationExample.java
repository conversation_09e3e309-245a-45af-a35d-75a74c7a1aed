package io.syrix.products.microsoft.entra;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.entra.service.EntraConfigurationService;
import io.syrix.protocols.model.MSEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Demonstration of EntraConfigurationService usage with various scenarios.
 * This class shows best practices for configuration export and error handling.
 */
public class EntraConfigurationExample {
	private static final Logger logger = LoggerFactory.getLogger(EntraConfigurationExample.class);
	private static final ObjectMapper objectMapper = new ObjectMapper()
			.enable(SerializationFeature.INDENT_OUTPUT);

	public static void main(String[] args) {
		// Get configuration from environment variables or command line arguments
		String accessToken = getRequiredEnvVar("ENTRA_ACCESS_TOKEN");
		MSEnvironment environment = MSEnvironment.valueOf(
				getRequiredEnvVar("ENTRA_ENVIRONMENT", "COMMERCIAL")
		);
		String outputPath = getRequiredEnvVar("OUTPUT_PATH", "./config-exports");

		// Create the output directory if it doesn't exist
		try {
			Files.createDirectories(Path.of(outputPath));
		} catch (Exception e) {
			logger.error("Failed to create output directory", e);
			System.exit(1);
		}

		// Initialize the Graph client with retry and timeout configurations
		MicrosoftGraphClient client = MicrosoftGraphClient.builder()
				.withRefreshToken(accessToken)
				.withEnvironment(environment)
				.withMaxRetries(3)
				.withRequestTimeout(Duration.ofMinutes(5))
				.build();

		// Demonstrate different usage scenarios
		try {
			// Scenario 1: Basic synchronous export
			demonstrateSyncExport(client, outputPath);

			// Scenario 2: Asynchronous export with custom processing
			demonstrateAsyncExport(client, outputPath);
		} catch (Exception e) {
			logger.error("Application failed", e);
			System.exit(1);
		}
	}

	/**
	 * Demonstrates basic synchronous configuration export.
	 */
	private static void demonstrateSyncExport(MicrosoftGraphClient client, String outputPath) {
		logger.info("Starting synchronous export demonstration");
		Instant startTime = Instant.now();

		try (EntraConfigurationService service = new EntraConfigurationService(client)) {
			// Perform the export
			ConfigurationResult result = service.exportConfiguration();

			// Save the result to file
			String filename = String.format("sync-export-%s.json", result.getExportId());
			objectMapper.writeValue(
					new File(outputPath, filename),
					result
			);

			Duration duration = Duration.between(startTime, Instant.now());
			logger.info("Synchronous export completed in {} seconds", duration.toSeconds());

			// Example of accessing specific configuration aspects
			JsonNode configData = result.getData();
			int policyCount = configData.get("policies").size();
			int userCount = configData.get("users").size();

			logger.info("Exported {} policies and {} privileged users",
					policyCount, userCount);

		} catch (ConfigurationExportException e) {
			logger.error("Configuration export failed", e);
		} catch (Exception e) {
			logger.error("Unexpected error during sync export", e);
		}
	}

	/**
	 * Demonstrates asynchronous configuration export with custom processing.
	 */
	private static void demonstrateAsyncExport(MicrosoftGraphClient client, String outputPath) {
		logger.info("Starting asynchronous export demonstration");

		try (EntraConfigurationService service = new EntraConfigurationService(client)) {
			// Create a non-blocking future for the export
			CompletableFuture<ConfigurationResult> futureResult = CompletableFuture
					.supplyAsync(service::exportConfiguration)
					.thenApply(result -> {
						// Perform additional processing
						processPrivilegedUsers(result);
						return result;
					})
					.thenApply(result -> {
						// Save to file asynchronously
						try {
							String filename = String.format("async-export-%s.json",
									result.getExportId());
							objectMapper.writeValue(
									new File(outputPath, filename),
									result
							);
						} catch (Exception e) {
							logger.error("Failed to save async export", e);
						}
						return result;
					});

			// Add error handling
			futureResult.exceptionally(throwable -> {
				logger.error("Async export failed", throwable);
				return null;
			});

			// Wait for completion if needed
			ConfigurationResult result = futureResult.join();
			logger.info("Asynchronous export completed with ID: {}", result.getExportId());

		} catch (Exception e) {
			logger.error("Error during async export", e);
		}
	}

	/**
	 * Demonstrates export of specific configuration aspects.
	 */

	/**
	 * Processes privileged users from the configuration result.
	 */
	private static void processPrivilegedUsers(ConfigurationResult result) {
		JsonNode usersNode = result.getData().get("users");
		if (usersNode == null || usersNode.isEmpty()) {
			logger.warn("No privileged users found in export");
			return;
		}

		// Example processing: Count users by role
		Map<String, Integer> usersByRole = new HashMap<>();
		usersNode.fields().forEachRemaining(entry -> {
			JsonNode roles = entry.getValue().get("roles");
			roles.forEach(role -> {
				String roleName = role.asText();
				usersByRole.merge(roleName, 1, Integer::sum);
			});
		});

		// Log the results
		logger.info("Users by role:");
		usersByRole.forEach((role, count) ->
				logger.info("  {}: {} users", role, count)
		);
	}

	/**
	 * Gets a required environment variable with error handling.
	 */
	private static String getRequiredEnvVar(String name) {
		return getRequiredEnvVar(name, null);
	}

	private static String getRequiredEnvVar(String name, String defaultValue) {
		String value = System.getenv(name);
		if (value == null || value.trim().isEmpty()) {
			if (defaultValue != null) {
				return defaultValue;
			}
			throw new IllegalStateException(
					String.format("Required environment variable %s is not set", name)
			);
		}
		return value;
	}
}