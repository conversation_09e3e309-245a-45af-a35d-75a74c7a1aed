package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.EMAIL_ATTESTATION_REAUTH_DAYS_PROPERTY;
import static io.syrix.products.microsoft.sharepoint.SharepointConstants.EMAIL_ATTESTATION_REQUIRED_PROPERTY;

@PolicyRemediator("MS.SHAREPOINT.3.3v1")
public class SPEmailAttestationRequiredAndReAuthRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
    private static final Logger log = LoggerFactory.getLogger(SPEmailAttestationRequiredAndReAuthRemediator.class);
    private static final Integer MAX_RE_AUTH_DAYS = 30;

    public SPEmailAttestationRequiredAndReAuthRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
        super(client, tenant, spConfig);
    }
    // constructor for Rollback interface
    public SPEmailAttestationRequiredAndReAuthRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        this(client, tenant, null);
    }


    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        boolean prevAttestationRequired = tenant.emailAttestationRequired;
        int prevReAuthDays = tenant.emailAttestationReAuthDays;
        return runCommands(true, prevAttestationRequired,  MAX_RE_AUTH_DAYS, prevReAuthDays);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        Map<String, ParameterChangeResult> changeResults = fixResult.getChanges().stream().collect(Collectors.toMap(ParameterChangeResult::getParameter, v -> v));
        boolean attestationRequired = Boolean.parseBoolean(changeResults.get(EMAIL_ATTESTATION_REQUIRED_PROPERTY).getNewValue().toString());
        boolean prevAttestationRequired = Boolean.parseBoolean(changeResults.get(EMAIL_ATTESTATION_REQUIRED_PROPERTY).getPrevValue().toString());

        int reAuthDays = (Integer)changeResults.get(EMAIL_ATTESTATION_REAUTH_DAYS_PROPERTY).getNewValue();
        int prevReAuthDays = (Integer)changeResults.get(EMAIL_ATTESTATION_REAUTH_DAYS_PROPERTY).getPrevValue();

        return runCommands(prevAttestationRequired, attestationRequired, prevReAuthDays, reAuthDays);
    }

    public CompletableFuture<PolicyChangeResult> runCommands(boolean attestationRequired, boolean prevAttestationRequired,
                                                             int reAuthDays, int prevReAuthDays) {
        try {
            SPShellCommand<GeneralResult> commandAttestation = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, EMAIL_ATTESTATION_REQUIRED_PROPERTY, attestationRequired, prevAttestationRequired);
            SPShellCommand<GeneralResult> commandReAuth = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, EMAIL_ATTESTATION_REAUTH_DAYS_PROPERTY, reAuthDays, prevReAuthDays);

            return Retry.executeWithRetry(() -> client.execute_(commandAttestation), MAX_RETRY)
                    .thenApply(this::checkResult)
                    .thenApply(res -> {
                        if (res.getResult() == RemediationResult.SUCCESS) {
                            tenant.emailAttestationRequired = attestationRequired;
                        }
                        return res;
                    })
                    .thenCompose(fileResult ->
                            Retry.executeWithRetry(() -> client.execute_(commandReAuth), MAX_RETRY)
                                    .thenApply(this::checkResult)
                                    .thenApply(res ->  {
                                        if (res.getResult() == RemediationResult.SUCCESS) {
                                            tenant.emailAttestationReAuthDays = reAuthDays;
                                        }
                                        return res;
                                    })
                                    .thenApply(folderResult -> mergeResult(fileResult, folderResult, attestationRequired, reAuthDays))
                                    .exceptionally(folderEx ->
                                            mergeResult(fileResult, IPolicyRemediator.failed_(getPolicyId(), ""), attestationRequired, reAuthDays)
                                    )
                    )
                    .exceptionally(fileEx -> {
                        log.error("First command failed for policy {}", getPolicyId(), fileEx);
                        return IPolicyRemediator.failed_(getPolicyId(), "First command failed: " + fileEx.getMessage());
                    });

        } catch (Exception ex) {
            log.error("Remediate the policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }


    private PolicyChangeResult mergeResult(PolicyChangeResult fileResult, PolicyChangeResult folderResult, boolean attestationRequired, int reAuthDays) {
        RemediationResult fileStatus = fileResult.getResult();
        RemediationResult folderStatus = folderResult.getResult();

        List<ParameterChangeResult> changeResults = new ArrayList<>();
        changeResults.addAll(fileResult.getChanges());
        changeResults.addAll(folderResult.getChanges());

        if (fileStatus == folderStatus) {
            return switch (fileStatus) {
                case SUCCESS -> IPolicyRemediator.success_(getPolicyId(), "EmailAttestationRequired set "+attestationRequired+" and EmailAttestationReAuthDays set "+reAuthDays, changeResults);
                case FAILED -> IPolicyRemediator.failed_(getPolicyId(), "Cannot set EmailAttestationRequired and and EmailAttestationReAuthDays", changeResults);
                case UNKNOWN -> IPolicyRemediator.unknown_(getPolicyId(), "Unknown result while setting EmailAttestationRequired and and EmailAttestationReAuthDays", changeResults);
                default -> throw new IllegalStateException("Unexpected value: " + fileStatus);
            };
        }

        if (fileStatus == RemediationResult.SUCCESS || folderStatus == RemediationResult.SUCCESS) {
            return IPolicyRemediator.partial_success_(getPolicyId(), "EmailAttestationRequired set "+attestationRequired+" and EmailAttestationReAuthDays set "+reAuthDays, changeResults);
        }

        return IPolicyRemediator.unknown_(getPolicyId(), "Empty or unrecognized result");
    }


}
