package io.syrix.products.microsoft.defender;

/**
 * Constants for Defender Configuration Service
 */
public class DefenderConstants {
    // Configuration keys
    public static final String CONFIG_KEY_POLICY_RULES = "protection_policy_rules";
    public static final String CONFIG_KEY_ATP_POLICY_FOR_O365 = "atp_policy_for_o365";
    public static final String CONFIG_KEY_ATP_POLICY_RULES = "atp_policy_rules";
    public static final String CONFIG_KEY_DLP_COMPLIANCE_POLICIES = "dlp_compliance_policies";
    public static final String CONFIG_KEY_DLP_COMPLIANCE_RULES = "dlp_compliance_rules";
    public static final String CONFIG_KEY_DEFENDER_DLP_LICENSE = "defender_dlp_license";
    public static final String CONFIG_KEY_ANTI_PHISH_POLICIES = "anti_phish_policies";
    public static final String CONFIG_KEY_PROTECTION_ALERTS = "protection_alerts";
    public static final String CONFIG_KEY_ADMIN_AUDIT_LOG_CONFIG = "admin_audit_log_config";
    public static final String CONFIG_KEY_TOTAL_USERS_WITHOUT_ADVANCED_AUDIT = "total_users_without_advanced_audit";
    public static final String CONFIG_KEY_DEFENDER_LICENSE = "defender_license";


    // Policy protection levels
    public static final String STANDARD_PROTECTION = "Standard protection";
    public static final String STRICT_PROTECTION = "Strict protection";
    public static final String STANDARD_PRESET_POLICY = "Standard";
    public static final String STRICT_PRESET_POLICY = "Strict";
    
    // Protection policy parameters
    public static final String POLICY_TYPE = "PolicyType";
    public static final String RECIPIENTS = "Recipients";
    
    // Recipient scopes
    public static final String ALL_RECIPIENTS = "All recipients";
    public static final String SPECIFIC_RECIPIENTS = "Specific recipients";
    
    // Protection types
    public static final String EXCHANGE_ONLINE_PROTECTION = "Exchange Online Protection";
    public static final String DEFENDER_OFFICE_PROTECTION = "Defender for Office 365 protection";
    public static final String SAFE_ATTACHMENTS = "Safe Attachments";
    
    // DLP constants
    public static final String DLP_ENABLED = "Enabled";
    public static final String NOTIFICATION_TYPE = "NotificationType";
    public static final String POLICY_TIPS = "PolicyTips";
    public static final String BLOCK_ACCESS = "Block";
    public static final String DLP_DEFAULT_POLICY_NAME = "Agency DLP Policy";
    public static final String DLP_TEMPLATE_ID = "TemplateId";
    public static final String CONTENT_CONTAINS_SENSITIVE_INFO = "ContentContainsSensitiveInformation";
    public static final String BLOCK_RESTRICTED_APPS = "BlockRestrictedApps";
    public static final String BLOCK_UNWANTED_BLUETOOTH = "BlockUnwantedBluetooth";
    
    // Audit constants
    public static final String AUDIT_ENABLED = "Enabled";
    public static final String RETENTION_DURATION = "RetentionDuration";
    
    // Impersonation protection
    public static final String TARGET_TYPE = "TargetType";
    public static final String TARGET_DOMAINS = "TargetDomains";
    public static final String ENABLED_DOMAIN_IMPERSONATION = "EnabledDomainImpersonation";
    public static final String ACTION = "Action";
    public static final String QUARANTINE = "Quarantine";
    public static final String IMPERSONATION_TYPE = "ImpersonationType";
    public static final String DOMAIN_TYPE = "Domain";
    public static final String DOMAIN_SCOPE = "DomainScope";
    public static final String AGENCY_DOMAINS = "AgencyDomains";
    public static final String POLICY_SCOPE = "PolicyScope";
    public static final String POLICY_SCOPE_BOTH = "Both";
    public static final String SET_DEFENDER_IMPERSONATION_PROTECTION_POLICY = "Set-DefenderImpersonationProtectionPolicy";
    public static final String DOMAIN_IMPERSONATION_SUCCESS_MESSAGE = "Domain impersonation protection has been enabled for agency domains";
    
    // Policy identifiers
    public static final String GLOBAL_POLICY_IDENTITY = "Global";
    public static final String IDENTITY_PROPERTY = "Identity";
    
    // PowerShell commands
    public static final String NEW_DLP_POLICY_CMDLET = "New-DlpCompliancePolicy";
    public static final String SET_DLP_POLICY_CMDLET = "Set-DlpCompliancePolicy";
    public static final String DLP_POLICY_RULE_CMDLET = "Set-DlpCompliancePolicyRule";
    public static final String SET_ANTIPHISHPOLICY_COMMAND = "Set-AntiPhishPolicy";
    public static final String SET_ATPPOLICY_COMMAND = "Set-AtpPolicyForO365";
    public static final String SET_DEFENDERSAFEATTACHMENTSPOLICY_COMMAND = "Set-DefenderSafeAttachmentsPolicy";
    public static final String SET_DEFENDER_PRESET_SECURITY_POLICIES = "Set-DefenderPresetSecurityPolicies";
    public static final String SET_DEFENDER_PROTECTION_POLICY = "Set-DefenderProtectionPolicy";
    public static final String GET_MAILBOX_CMDLET = "Get-Mailbox";
    public static final String GET_UNIFIED_GROUP_CMDLET = "Get-UnifiedGroup";
    public static final String GET_UNIFIED_GROUP_LINKS_CMDLET = "Get-UnifiedGroupLinks";
    public static final String GET_EOP_PROTECTION_POLICY_RULE = "Get-EOPProtectionPolicyRule";
    public static final String SET_EOP_PROTECTION_POLICY_RULE = "Set-EOPProtectionPolicyRule";
    public static final String ENABLE_EOP_PROTECTION_POLICY_RULE = "Enable-EOPProtectionPolicyRule";
    public static final String GET_ATP_PROTECTION_POLICY_RULE = "Get-ATPProtectionPolicyRule";
    public static final String SET_ATP_PROTECTION_POLICY_RULE = "Set-ATPProtectionPolicyRule";
    public static final String ENABLE_ATP_PROTECTION_POLICY_RULE = "Enable-ATPProtectionPolicyRule";
    public static final String GET_ATP_POLICY_FOR_O365 = "Get-AtpPolicyForO365";
    // Security & Compliance PowerShell cmdlets
    public static final String GET_ACTIVITY_ALERT = "Get-ActivityAlert";
    public static final String NEW_ACTIVITY_ALERT = "New-ActivityAlert";

    
    // Status messages
    public static final String SUCCESS_CONFIGURATION_MESSAGE = "Successfully configured";
    public static final String FAILED_CONFIGURATION_MESSAGE = "Failed to configure";
    public static final String PRESET_POLICIES_SUCCESS_MESSAGE = "Standard and strict preset security policies have been enabled";
    public static final String SUCCESS_EOP_PROTECTION_ENABLED = "EOP protection has been enabled for all users";
    public static final String ERROR_UNKNOWN = "Unknown error";
    
    // DLP Policy Rule Properties
    public static final String POLICY_NAME = "PolicyName";
    public static final String ENABLE_NOTIFICATIONS = "EnableNotifications";
    public static final String NOTIFICATION_ENABLED = "NotificationEnabled";
    public static final String NOTIFICATION_TEXT = "NotificationText";
    public static final String DEFAULT_NOTIFICATION_TEXT = "This item contains sensitive information and requires proper handling.";

    // Command constants
    public static final String GET_ANTI_PHISH_POLICY = "Get-AntiPhishPolicy";
    public static final String GET_HOSTED_CONTENT_FILTER_POLICY = "Get-HostedContentFilterPolicy";
    public static final String GET_MALWARE_FILTER_POLICY = "Get-MalwareFilterPolicy";
    public static final String GET_SAFE_ATTACHMENT_POLICY = "Get-SafeAttachmentPolicy";
    public static final String GET_SAFE_LINKS_POLICY = "Get-SafeLinksPolicy";
    public static final String GET_MALWARE_RULES = "Get-MalwareFilterRule";
    // Parameter constants
    public static final String FILTER_PARAM = "Filter";
    public static final String ANTI_PHISH_POLICY_PARAM = "AntiPhishPolicy";
    public static final String HOSTED_CONTENT_FILTER_POLICY_PARAM = "HostedContentFilterPolicy";
    public static final String MALWARE_FILTER_POLICY_PARAM = "MalwareFilterPolicy";
    public static final String SAFE_ATTACHMENT_POLICY_PARAM = "SafeAttachmentPolicy";
    public static final String SAFE_LINKS_POLICY_PARAM = "SafeLinksPolicy";
    public static final String RECIPIENT_FILTER_PARAM = "RecipientFilter";

    // Field names
    public static final String NAME_FIELD = "Name";
    public static final String STATE_FIELD = "State";
    public static final String USER_PRINCIPAL_NAME_FIELD = "UserPrincipalName";
    public static final String PRIMARY_SMTP_ADDRESS_FIELD = "PrimarySmtpAddress";
    public static final String RECIPIENT_TYPE_DETAILS_FIELD = "RecipientTypeDetails";
    public static final String EXTERNAL_DIRECTORY_OBJECT_ID_FIELD = "ExternalDirectoryObjectId";

    // Message templates
    public static final String PRESET_POLICIES_SUCCESS_MESSAGE_TEMPLATE = "Successfully configured %s preset security policies";
    public static final String RESULT_SIZE_PARAM = "ResultSize";
    public static final String DISCOVERY_MAILBOX_TYPE = "DiscoveryMailbox";
    public static final String LINK_TYPE_PARAM = "LinkType";
    public static final String WHATIF_STATUS = "whatif";

    // Response field names
    public static final String PRESET_TYPE_FIELD = "presetType";
    public static final String EOP_RULE_NAME_FIELD = "eopRuleName";
    public static final String ATP_RULE_NAME_FIELD = "atpRuleName";
    public static final String RECIPIENT_FILTER_FIELD = "recipientFilter";
    public static final String RECIPIENT_FILTER_APPLIED_FIELD = "recipientFilterApplied";
    public static final String ATP_RECIPIENT_FILTER_APPLIED_FIELD = "atpRecipientFilterApplied";
    public static final String WHATIF_MODE_FIELD = "whatIfMode";
    public static final String USER_COUNT_FIELD = "userCount";
    public static final String TIMESTAMP_FIELD = "timestamp";
    public static final String EXCLUDED_USER_COUNT_FIELD = "excludedUserCount";
    public static final String EXCLUDED_GROUP_COUNT_FIELD = "excludedGroupCount";
    public static final String STANDARD_POLICY_APPLIED_FIELD = "standardPolicyApplied";
    public static final String STRICT_POLICY_APPLIED_FIELD = "strictPolicyApplied";
    public static final String POLICY_RESULT_FIELD = "policyResult";
    public static final String POLICY_APPLIED_FIELD = "policyApplied";
    public static final String ATP_POLICY_APPLIED_FIELD = "atpPolicyApplied";
    public static final String DEFENDER_POLICY_ID_FIELD = "Id";
    public static final String HIGH_VALUE = "High";
    public static final String MEDIUM_VALUE = "Medium";
    public static final String CUSTOM_VALUE = "Custom";
}