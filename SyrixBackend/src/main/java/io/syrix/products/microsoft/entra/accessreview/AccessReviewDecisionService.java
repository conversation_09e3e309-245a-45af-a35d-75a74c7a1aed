package io.syrix.products.microsoft.entra.accessreview;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.utils.ProtocolConstants.PARAM_FILTER;

/**
 * Service to automate guest user access review decisions
 */
public class AccessReviewDecisionService {
	private static final Logger logger = LoggerFactory.getLogger(AccessReviewDecisionService.class);

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final List<String> trustedDomains;
	private final int inactiveDaysThreshold;

	/**
	 * Creates a new AccessReviewDecisionService with default settings
	 */
	public AccessReviewDecisionService(MicrosoftGraphClient graphClient) {
		this(graphClient, new ArrayList<>(), AccessReviewConstants.DEFAULT_INACTIVE_DAYS_THRESHOLD);
	}

	/**
	 * Creates a new AccessReviewDecisionService with custom settings
	 */
	public AccessReviewDecisionService(
			MicrosoftGraphClient graphClient,
			List<String> trustedDomains,
			int inactiveDaysThreshold) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.trustedDomains = trustedDomains;
		this.inactiveDaysThreshold = inactiveDaysThreshold;
	}

	/**
	 * Processes active access reviews for guest users
	 */
	public CompletableFuture<Void> processActiveGuestAccessReviews() {
		logger.info("Processing active guest access reviews");

		// Get active access reviews for guest users
		return getActiveAccessReviews()
				.thenCompose(reviews -> {
					List<CompletableFuture<Void>> futures = new ArrayList<>();

					if (reviews.isEmpty()) {
						logger.info("No active guest access reviews found");
						return CompletableFuture.completedFuture(null);
					}

					// Process each active review
					for (JsonNode review : reviews) {
						String reviewId = review.get(Constants.ID_FIELD).asText();

						// Get instances of this review definition
						ArrayNode instances = (ArrayNode) review.get("instances");
						for (JsonNode instance : instances) {
							String instanceId = instance.get(Constants.ID_FIELD).asText();
							String status = instance.get("status").asText();

							if ("InProgress".equals(status)) {
								logger.info("Processing review: {} [{}]", review.get(AccessReviewConstants.DISPLAY_NAME).asText(), instanceId);
								futures.add(processReviewDecisions(reviewId, instanceId));
							}
						}
					}

					// Wait for all processing to complete
					return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
				});
	}

	/**
	 * Gets all active access reviews for guest users
	 */
	private CompletableFuture<List<JsonNode>> getActiveAccessReviews() {
		String filter = String.format("%s eq '%s'",
				AccessReviewConstants.DISPLAY_NAME,
				AccessReviewConstants.MONTHLY_GUEST_REVIEW_NAME);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AccessReviewConstants.ACCESS_REVIEWS_ENDPOINT)
						.addQueryParam(PARAM_FILTER, filter)
						.addQueryParam("$expand", "instances")
						.build()
		).thenApply(response -> {
			List<JsonNode> activeReviews = new ArrayList<>();

			if (response.has(Constants.VALUE_FIELD) &&
					response.get(Constants.VALUE_FIELD).isArray()) {

				ArrayNode values = (ArrayNode) response.get(Constants.VALUE_FIELD);
				for (JsonNode review : values) {
					activeReviews.add(review);
				}
			}

			return activeReviews;
		});
	}

	/**
	 * Processes decisions for a specific review instance
	 */
	private CompletableFuture<Void> processReviewDecisions(String reviewId, String instanceId) {
		// Get all decisions that need reviewing
		String decisionsEndpoint = String.format(
				AccessReviewConstants.DECISIONS_ENDPOINT,
				reviewId,
				instanceId
		);

		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.withEndpoint(decisionsEndpoint + "?$expand=insights")
								.build()
				)
				.thenCompose(decisionsResponse -> {
					List<CompletableFuture<JsonNode>> userInfoFutures = new ArrayList<>();
					List<JsonNode> decisions = new ArrayList<>();

					if (decisionsResponse.has(Constants.VALUE_FIELD) &&
							decisionsResponse.get(Constants.VALUE_FIELD).isArray()) {

						ArrayNode decisionItems = (ArrayNode) decisionsResponse.get(Constants.VALUE_FIELD);
						for (JsonNode decisionItem : decisionItems) {
							decisions.add(decisionItem);

							// Get user details for each decision
							String principalId = decisionItem.get("principal").get(Constants.ID_FIELD).asText();
							userInfoFutures.add(getUserDetails(principalId));
						}
					}

					// Once we have all user details, make decisions
					return CompletableFuture.allOf(userInfoFutures.toArray(new CompletableFuture[0]))
							.thenCompose(v -> {
								try {
									ObjectNode batchDecisionsPayload = createBatchDecisionsPayload(
											decisions,
											userInfoFutures,
											instanceId
									);

									// Submit batch decisions
									return submitBatchDecisions(reviewId, instanceId, batchDecisionsPayload);
								} catch (Exception e) {
									logger.error("Error creating batch decisions payload", e);
									return CompletableFuture.failedFuture(e);
								}
							});
				});
	}

	/**
	 * Gets user details for a specific user
	 */
	private CompletableFuture<JsonNode> getUserDetails(String userId) {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint("/users/" + userId + "?$select=id,userPrincipalName,signInActivity,userType")
						.build()
		);
	}

	/**
	 * Creates the payload for batch decisions
	 */
	private ObjectNode createBatchDecisionsPayload(
			List<JsonNode> decisions,
			List<CompletableFuture<JsonNode>> userDetails,
			String instanceId) throws Exception {

		ObjectNode payload = objectMapper.createObjectNode();
		ArrayNode decisionsArray = objectMapper.createArrayNode();

		for (int i = 0; i < decisions.size(); i++) {
			JsonNode decision = decisions.get(i);
			JsonNode userInfo = userDetails.get(i).get();

			String userId = userInfo.get(Constants.ID_FIELD).asText();
			String decisionResult;
			String justification;

			// Apply decision logic
			if (isFromTrustedDomain(userInfo)) {
				decisionResult = AccessReviewConstants.APPROVE;
				justification = "Approved domain: " + extractDomain(userInfo);
			} else if (isUserActive(userInfo)) {
				decisionResult = AccessReviewConstants.APPROVE;
				justification = "Active within " + inactiveDaysThreshold + " days";
			} else {
				decisionResult = AccessReviewConstants.DENY;
				justification = "Inactive for more than " + inactiveDaysThreshold + " days";

				// Add more specific justification if last sign-in data is available
				if (userInfo.has("signInActivity") &&
						!userInfo.get("signInActivity").isNull() &&
						userInfo.get("signInActivity").has("lastSignInDateTime")) {

					String lastSignIn = userInfo.get("signInActivity").get("lastSignInDateTime").asText();
					LocalDateTime lastSignInDate = LocalDateTime.parse(lastSignIn, DateTimeFormatter.ISO_DATE_TIME);
					long daysSinceLastSignIn = ChronoUnit.DAYS.between(lastSignInDate, LocalDateTime.now());

					justification = "Inactive for " + daysSinceLastSignIn + " days";
				}
			}

			// Create decision item
			ObjectNode decisionItem = objectMapper.createObjectNode();
			decisionItem.put(AccessReviewConstants.ACCESS_REVIEW_ID, instanceId);
			decisionItem.put(AccessReviewConstants.DECISION, decisionResult);
			decisionItem.put(AccessReviewConstants.PRINCIPAL_ID, userId);
			decisionItem.put(AccessReviewConstants.JUSTIFICATION, justification);

			decisionsArray.add(decisionItem);
		}

		payload.set(AccessReviewConstants.DECISIONS, decisionsArray);
		return payload;
	}

	/**
	 * Submits batch decisions for a review instance
	 */
	private CompletableFuture<Void> submitBatchDecisions(
			String reviewId,
			String instanceId,
			ObjectNode decisionsPayload) throws Exception {

		String batchEndpoint = String.format(
				AccessReviewConstants.BATCH_DECISIONS_ENDPOINT,
				reviewId,
				instanceId
		);

		String payloadJson = objectMapper.writeValueAsString(decisionsPayload);
		HttpRequest.BodyPublisher bodyPublisher = HttpRequest.BodyPublishers.ofString(payloadJson);

		logger.debug("Submitting batch decisions: {}", payloadJson);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(batchEndpoint)
						.withMethod(HttpMethod.POST)
						.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
						.withBody(bodyPublisher)
						.build()
		).thenApply(response -> null);
	}

	/**
	 * Applies decisions for a review instance
	 */
	public CompletableFuture<Void> applyDecisions(String reviewId, String instanceId) {
		String applyEndpoint = String.format(
				AccessReviewConstants.APPLY_DECISIONS_ENDPOINT,
				reviewId,
				instanceId
		);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(applyEndpoint)
						.withMethod(HttpMethod.POST)
						.build()
		).thenApply(response -> null);
	}

	/**
	 * Checks if a user is from a trusted domain
	 */
	private boolean isFromTrustedDomain(JsonNode userInfo) {
		if (trustedDomains.isEmpty()) {
			return false;
		}

		String domain = extractDomain(userInfo);
		return domain != null && trustedDomains.contains(domain.toLowerCase());
	}

	/**
	 * Extracts the domain from a user's UPN
	 */
	private String extractDomain(JsonNode userInfo) {
		if (userInfo.has("userPrincipalName")) {
			String upn = userInfo.get("userPrincipalName").asText();
			int atIndex = upn.indexOf('@');
			if (atIndex > 0 && atIndex < upn.length() - 1) {
				return upn.substring(atIndex + 1);
			}
		}
		return null;
	}

	/**
	 * Checks if a user is active based on last sign-in
	 */
	private boolean isUserActive(JsonNode userInfo) {
		if (!userInfo.has("signInActivity") ||
				userInfo.get("signInActivity").isNull() ||
				!userInfo.get("signInActivity").has("lastSignInDateTime")) {
			return false;
		}

		String lastSignIn = userInfo.get("signInActivity").get("lastSignInDateTime").asText();
		try {
			LocalDateTime lastSignInDate = LocalDateTime.parse(lastSignIn, DateTimeFormatter.ISO_DATE_TIME);
			long daysSinceLastSignIn = ChronoUnit.DAYS.between(lastSignInDate, LocalDateTime.now());
			return daysSinceLastSignIn <= inactiveDaysThreshold;
		} catch (Exception e) {
			logger.warn("Failed to parse last sign-in date: {}", lastSignIn, e);
			return false;
		}
	}
}