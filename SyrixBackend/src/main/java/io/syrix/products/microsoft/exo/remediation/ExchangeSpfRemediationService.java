package io.syrix.products.microsoft.exo.remediation;

import com.azure.core.credential.TokenCredential;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.dns.DnsService;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import io.syrix.products.microsoft.exo.ExoConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.products.microsoft.exo.ExoConstants.DEFAULT_SPF_RECORD;

//TODO This class is not complete. It is missing exact fix for SPF in different domains.
/**
 * Service that remediates SPF record issues for MS.EXO.2.2v2 compliance.
 * This service can both check for compliance and update DNS records to fix issues.
 */
@PolicyRemediator("MS.EXO.2.2v2")
public class ExchangeSpfRemediationService extends RemediatorBase {
	private static final Pattern SPF_ALL_PATTERN = Pattern.compile("^v=spf1\\s+(.+?)\\s+([\\-~\\?]all)$");

	private final DnsService dnsService;
	private final DnsProviderDetector dnsProviderDetector;
	private final SpfRecordUpdater spfRecordUpdater;
	private final ObjectMapper objectMapper;
	private final ExchangeOnlineConfigurationService exchangeService;

	/**
	 * Creates a new Exchange SPF remediation service.
	 *
	 * @param graphClient Microsoft Graph client for accessing domain information
	 * @param dnsService DNS service for record lookups
	 * @param exchangeService ExchangeOnlineConfigurationService for accessing Exchange Online configuration
	 * @param azureCredential (Optional) Azure credential for Azure DNS updates
	 * @param azureSubscriptionId (Optional) Azure subscription ID
	 */
	public ExchangeSpfRemediationService(
			DnsService dnsService,
			ExchangeOnlineConfigurationService exchangeService,
			TokenCredential azureCredential,
			String azureSubscriptionId,
			String azureResourceGroup) {

		this.dnsService = dnsService;
		this.objectMapper = new ObjectMapper();
		this.exchangeService = exchangeService;

		// Initialize provider detector
		this.dnsProviderDetector = new DnsProviderDetector(dnsService);

		// Configure provider credentials
		Map<String, Object> providerCredentials = new HashMap<>();
		if (azureCredential != null && azureSubscriptionId != null) {
			providerCredentials.put("azureCredential", azureCredential);
			providerCredentials.put("azureSubscriptionId", azureSubscriptionId);
			providerCredentials.put("azureResourceGroup", azureResourceGroup);
		}

		// Additional credentials would be added here (Cloudflare, AWS, etc.)
		// providerCredentials.put("cloudflareApiToken", cloudflareApiToken);

		// Initialize SPF updater
		this.spfRecordUpdater = new SpfRecordUpdater(dnsProviderDetector, objectMapper, providerCredentials);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting SPF record remediation");

		// Get domains and check SPF records
		return this.exchangeService.getAcceptedDomains()
				.thenCompose(this::analyzeAndRemediateDomains)
				.exceptionally(ex -> {
					logger.error("Exception during SPF remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Analyzes domains and remediates SPF issues.
	 */
	private CompletableFuture<JsonNode> analyzeAndRemediateDomains(JsonNode domainsResponse) {
		if (isInvalidResponse(domainsResponse)) {
			return IPolicyRemediator.failed(getPolicyId(), ExoConstants.ERROR_NO_DOMAINS);
		}

		List<String> domainsList = extractDomainNames((ArrayNode) domainsResponse);

		if (domainsList.isEmpty()) {
			return IPolicyRemediator.failed(getPolicyId(), ExoConstants.ERROR_NO_VERIFIED_DOMAINS);
		}

		logger.info("Analyzing and remediating SPF records for {} verified domains", domainsList.size());

		List<CompletableFuture<SpfCheckResult>> spfCheckFutures = domainsList.stream()
				.map(this::checkSpfRecord)
				.toList();

		return CompletableFuture.allOf(spfCheckFutures.toArray(new CompletableFuture[0]))
				.thenCompose(v -> processCheckResults(spfCheckFutures, domainsList));
	}

	private boolean isInvalidResponse(JsonNode domainsResponse) {
		return domainsResponse == null || !(domainsResponse instanceof ArrayNode array && !array.isEmpty());
	}

	private List<String> extractDomainNames(ArrayNode domains) {
		List<String> domainsList = new ArrayList<>();
		for (JsonNode domain : domains) {
			if (domain.has(ExoConstants.DOMAIN_NAME)) {
				domainsList.add(domain.get(ExoConstants.DOMAIN_NAME).asText());
			} else if (domain.has(ExoConstants.FIELD_NAME)) {
				domainsList.add(domain.get(ExoConstants.FIELD_NAME).asText());
			} else if (domain.has(ID_FIELD)) {
				domainsList.add(domain.get(ID_FIELD).asText());
			}
		}
		return domainsList;
	}

	private CompletableFuture<JsonNode> processCheckResults(
			List<CompletableFuture<SpfCheckResult>> spfCheckFutures, List<String> domainsList) {
		List<String> domainsToFix = new ArrayList<>();
		List<String> compliantDomains = new ArrayList<>();
		List<String> unreachableDomains = new ArrayList<>();
		Map<String, String> domainIssues = new HashMap<>();

		for (CompletableFuture<SpfCheckResult> future : spfCheckFutures) {
			SpfCheckResult result = future.join();
			String domain = result.getDomain();

			if (result.isCompliant()) {
				compliantDomains.add(domain);
			} else if (result.isNonCompliant()) {
				domainsToFix.add(domain);
				domainIssues.put(domain, result.getIssue());
			} else if (result.isUnreachable()) {
				unreachableDomains.add(domain);
			}
		}

		if (domainsToFix.isEmpty() && unreachableDomains.isEmpty()) {
			return IPolicyRemediator.success(
					getPolicyId(),
					ExoConstants.SUCCESS_ALL_DOMAINS_COMPLIANT
			);
		}

		if (!domainsToFix.isEmpty()) {
			return remediateNonCompliantDomains(domainsToFix, compliantDomains, unreachableDomains);
		}

		return handleUnreachableDomains(compliantDomains, domainsList, unreachableDomains);
	}

	private CompletableFuture<JsonNode> handleUnreachableDomains(
			List<String> compliantDomains, List<String> domainsList, List<String> unreachableDomains) {
		String message = String.format(
				"%d of %d domains have compliant SPF records. %d domains are unreachable.",
				compliantDomains.size(), domainsList.size(), unreachableDomains.size()
		);

		ObjectNode resultData = objectMapper.createObjectNode();
		resultData.put("totalDomains", domainsList.size());
		resultData.put("compliantDomains", compliantDomains.size());
		resultData.put("unreachableDomains", unreachableDomains.size());

		ArrayNode unreachableList = resultData.putArray("unreachableDomainsList");
		unreachableDomains.forEach(unreachableList::add);

		return IPolicyRemediator.partial_success(getPolicyId(), message);
	}

	
	private CompletableFuture<JsonNode> remediateNonCompliantDomains(
			List<String> domainsToFix,
			List<String> compliantDomains,
			List<String> unreachableDomains) {

		List<CompletableFuture<JsonNode>> fixFutures = createFixFutures(domainsToFix);

		return CompletableFuture.allOf(fixFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> processFixResults(fixFutures, compliantDomains, domainsToFix, unreachableDomains));
	}

	private List<CompletableFuture<JsonNode>> createFixFutures(List<String> domainsToFix) {
		List<CompletableFuture<JsonNode>> fixFutures = new ArrayList<>();

		for (String domain : domainsToFix) {
			CompletableFuture<JsonNode> fixFuture = checkSpfRecord(domain)
					.thenCompose(checkResult -> {
						if (checkResult.isNonCompliant()) {
							String newSpfRecord = checkResult.getRecommendation() != null ?
									checkResult.getRecommendation() : DEFAULT_SPF_RECORD;
							return spfRecordUpdater.updateSpfRecord(domain, newSpfRecord);
						} else {
							return CompletableFuture.completedFuture(checkResult.toJsonNode(objectMapper));
						}
					});

			fixFutures.add(fixFuture);
		}

		return fixFutures;
	}

	private JsonNode processFixResults(
			List<CompletableFuture<JsonNode>> fixFutures,
			List<String> compliantDomains,
			List<String> domainsToFix,
			List<String> unreachableDomains) {

		List<String> successfulFixes = new ArrayList<>();
		List<String> failedFixes = new ArrayList<>();

		for (CompletableFuture<JsonNode> future : fixFutures) {
			JsonNode result = future.join();
			String domain = result.get("domain").asText();
			String status = result.get("status").asText();

			if ("success".equals(status)) {
				successfulFixes.add(domain);
			} else {
				failedFixes.add(domain);
			}
		}

		return buildResultSummary(compliantDomains, domainsToFix, unreachableDomains, successfulFixes, failedFixes);
	}

	private JsonNode buildResultSummary(
			List<String> compliantDomains,
			List<String> domainsToFix,
			List<String> unreachableDomains,
			List<String> successfulFixes,
			List<String> failedFixes) {

		int totalDomains = compliantDomains.size() + domainsToFix.size() + unreachableDomains.size();
		int nowCompliant = compliantDomains.size() + successfulFixes.size();

		ObjectNode resultData = objectMapper.createObjectNode();
		resultData.put("totalDomains", totalDomains);
		resultData.put("initiallyCompliant", compliantDomains.size());
		resultData.put("successfullyFixed", successfulFixes.size());
		resultData.put("failedToFix", failedFixes.size());
		resultData.put("unreachable", unreachableDomains.size());
		resultData.put("nowCompliant", nowCompliant);

		ArrayNode successList = resultData.putArray("successfulFixesList");
		successfulFixes.forEach(successList::add);

		ArrayNode failureList = resultData.putArray("failedFixesList");
		failedFixes.forEach(failureList::add);

		return determineOverallStatus(compliantDomains, successfulFixes, failedFixes, unreachableDomains);
	}

	private JsonNode determineOverallStatus(
			List<String> compliantDomains,
			List<String> successfulFixes,
			List<String> failedFixes,
			List<String> unreachableDomains) {

		if (failedFixes.isEmpty() && unreachableDomains.isEmpty()) {
			String message = String.format(
					"SPF remediation successful. %d domains were already compliant, %d domains were fixed.",
					compliantDomains.size(), successfulFixes.size()
			);
			return IPolicyRemediator.success(getPolicyId(), message).join();
		} else if (successfulFixes.isEmpty() && compliantDomains.isEmpty()) {
			String message = String.format(
					"SPF remediation failed. Unable to fix %d domains and %d domains are unreachable.",
					failedFixes.size(), unreachableDomains.size()
			);
			return IPolicyRemediator.failed(getPolicyId(), message).join();
		} else {
			String message = String.format(
					"SPF remediation partially successful. %d domains were already compliant, " +
							"%d domains were fixed, %d domains could not be fixed, and %d domains are unreachable.",
					compliantDomains.size(), successfulFixes.size(), failedFixes.size(), unreachableDomains.size()
			);
			return IPolicyRemediator.partial_success(getPolicyId(), message).join();
		}
	}

	/**
	 * Checks the SPF record for a single domain.
	 */
	private CompletableFuture<SpfCheckResult> checkSpfRecord(String domain) {
		logger.debug("Checking SPF record for domain: {}", domain);

		return CompletableFuture.supplyAsync(() -> {
			SpfCheckResult.Builder resultBuilder = SpfCheckResult.builder().domain(domain);

			try {
				// Look up TXT records
				var txtRecords = dnsService.lookupTxtRecords(domain);

				// Look for SPF record
				boolean hasSpfRecord = false;
				boolean isCompliant = false;
				String spfRecord = null;

				for (String tmpRecord : txtRecords.records()) {
					if (tmpRecord.startsWith("v=spf1")) {
						hasSpfRecord = true;
						spfRecord = tmpRecord;

						// Check if the record has the required hard fail (-all)
						if (tmpRecord.contains(" -all")) {
							isCompliant = true;
						}

						break;
					}
				}

				if (!hasSpfRecord) {
					return resultBuilder
							.status(SpfCheckResult.SpfStatus.NON_COMPLIANT)
							.issue("No SPF record found")
							.recommendation(DEFAULT_SPF_RECORD)
							.build();
				} else if (!isCompliant) {
					// Suggest a corrected version of their existing SPF record
					String correctedSpf = correctSpfRecord(spfRecord);

					return resultBuilder
							.status(SpfCheckResult.SpfStatus.NON_COMPLIANT)
							.issue("SPF record does not use hard fail (-all)")
							.currentRecord(spfRecord)
							.recommendation(correctedSpf)
							.build();
				} else {
					return resultBuilder
							.status(SpfCheckResult.SpfStatus.COMPLIANT)
							.currentRecord(spfRecord)
							.build();
				}
			} catch (Exception e) {
				logger.error("Error checking SPF for domain {}: {}", domain, e.getMessage());
				return resultBuilder
						.status(SpfCheckResult.SpfStatus.UNREACHABLE)
						.error(e.getMessage())
						.build();
			}
		});
	}

	/**
	 * Corrects an SPF record to use hard fail (-all) instead of soft fail (~all) or neutral (?all).
	 */
	private String correctSpfRecord(String currentSpf) {
		var matcher = SPF_ALL_PATTERN.matcher(currentSpf);
		if (matcher.find()) {
			String mechanisms = matcher.group(1);
			String allDirective = matcher.group(2);

			if (!allDirective.equals("-all")) {
				// Replace with hard fail
				return "v=spf1 " + mechanisms + " -all";
			}
		}

		// If pattern not matched or already has -all, return original
		return currentSpf;
	}
}