package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.entra.model.DynamicGroupConfiguration;
import io.syrix.protocols.client.MicrosoftGraphClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Configuration export service for dynamic group compliance checking.
 * Follows the same pattern as EnterpriseApplicationService - exports configuration data
 * for dynamic guest user groups and their compliance status.
 */
class DynamicGroupComplianceService extends BaseConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicGroupComplianceService.class);
    private static final String POLICY_ID = "MS.AAD.10.1v1";
    
    private final DynamicGroupService dynamicGroupService;
    
    /**
     * Constructor for DynamicGroupComplianceService
     * 
     * @param graphClient Microsoft Graph client
     * @param objectMapper JSON object mapper
     * @param metrics Metrics collector
     */
    DynamicGroupComplianceService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
        this.dynamicGroupService = new DynamicGroupService(graphClient);
    }
    
    /**
     * Exports the dynamic group compliance configuration.
     * Required implementation of the ConfigurationService interface.
     * 
     * @return ConfigurationResult containing compliance data
     */
    @Override
    public ConfigurationResult exportConfiguration() {
        logger.info("Exporting dynamic group compliance configuration for policy: {}", POLICY_ID);
        
        try {
            // Get dynamic groups and evaluate compliance
            CompletableFuture<List<DynamicGroupConfiguration>> groupsFuture = 
                withRetry(() -> dynamicGroupService.getAllDynamicGroups(), "Get-MgGroup");
            
            CompletableFuture<JsonNode> complianceFuture = groupsFuture
                .thenCompose(this::evaluateCompliance);
            
            // Build the result data futures map
            Map<String, CompletableFuture<?>> resultData = new HashMap<>();
            resultData.put("all_dynamic_groups", groupsFuture);
            resultData.put("dynamic_group_compliance", complianceFuture);
            
            // Wait for all futures to complete and then build the result
            return waitForFutures(resultData)
                    .thenApply(map -> {
                        // Add metadata after all operations complete
                        map.put("policy_id", POLICY_ID);
                        map.put("total_groups_analyzed", 
                            ((List<?>) map.get("all_dynamic_groups")).size());
                        
                        // Add successful and unsuccessful commands after all operations complete
                        map.put("successful_commands", getSuccessfulCommands());
                        map.put("unsuccessful_commands", getUnsuccessfulCommands());
                        
                        // Build and return the result
                        return buildConfigurationResult(map, SERVICE_VERSION, ConfigurationServiceType.ENTRA);
                    })
                    .join();  // Wait for the final result
                    
        } catch (Exception e) {
            logger.error("Failed to export dynamic group compliance configuration", e);
            throw new ConfigurationExportException("Failed to export dynamic group compliance", e);
        }
    }
    
    /**
     * Evaluates compliance for dynamic guest user groups
     * 
     * @param allGroups List of all dynamic groups in the tenant
     * @return CompletableFuture with compliance result
     */
    private CompletableFuture<JsonNode> evaluateCompliance(List<DynamicGroupConfiguration> allGroups) {
        logger.info("Evaluating compliance for policy: {}", POLICY_ID);
        
        return CompletableFuture.supplyAsync(() -> {
            List<DynamicGroupConfiguration> properlyConfigured = allGroups.stream()
                    .filter(DynamicGroupConfiguration::isProperlyConfiguredForGuestUsers)
                    .toList();
            
            List<DynamicGroupConfiguration> misconfigured = allGroups.stream()
                    .filter(DynamicGroupConfiguration::isMisconfiguredGuestGroup)
                    .toList();
            
            boolean isCompliant = !properlyConfigured.isEmpty();
            
            String message = buildComplianceMessage(properlyConfigured, misconfigured, isCompliant);
            
            if (isCompliant) {
                logger.debug("Policy {} is compliant: {}", POLICY_ID, message);
                return IPolicyRemediator.success(POLICY_ID, message).join();
            } else {
                logger.debug("Policy {} is not compliant: {}", POLICY_ID, message);
                return IPolicyRemediator.failed(POLICY_ID, message).join();
            }
        }, executor);
    }
    
    /**
     * Builds a compliance message based on the current state
     * 
     * @param properlyConfigured List of properly configured groups
     * @param misconfigured List of misconfigured groups
     * @param isCompliant Whether the current state is compliant
     * @return Descriptive compliance message
     */
    private String buildComplianceMessage(List<DynamicGroupConfiguration> properlyConfigured, 
                                        List<DynamicGroupConfiguration> misconfigured, 
                                        boolean isCompliant) {
        StringBuilder message = new StringBuilder();
        
        if (isCompliant) {
            message.append(String.format("Found %d properly configured dynamic guest user group(s)", 
                    properlyConfigured.size()));
            
            if (!misconfigured.isEmpty()) {
                message.append(String.format(" and %d misconfigured group(s) that could be improved", 
                        misconfigured.size()));
            }
        } else {
            message.append("No properly configured dynamic guest user groups found");
            
            if (!misconfigured.isEmpty()) {
                message.append(String.format(", but found %d misconfigured group(s) that could be fixed", 
                        misconfigured.size()));
            }
            
            message.append(". Dynamic groups are required for automated guest user management and compliance");
        }
        
        return message.toString();
    }
}