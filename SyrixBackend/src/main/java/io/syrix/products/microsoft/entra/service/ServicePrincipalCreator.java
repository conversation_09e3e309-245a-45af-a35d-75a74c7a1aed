package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

/**
 * Service for creating service principals in Microsoft Entra ID.
 * Supports creating service principals for existing applications, particularly
 * for multi-tenant applications.
 */
public class ServicePrincipalCreator {
	private static final Logger logger = LoggerFactory.getLogger(ServicePrincipalCreator.class);
	private static final String SERVICE_PRINCIPALS_ENDPOINT = "/servicePrincipals";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public ServicePrincipalCreator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	/**
	 * Creates a service principal for an existing application.
	 * This is particularly useful for multi-tenant applications where the application
	 * exists in another tenant.
	 *
	 * @param appId The application ID (client ID) of the existing application
	 * @return CompletableFuture containing the created service principal details
	 * @throws GraphClientException if the operation fails
	 */
	public CompletableFuture<JsonNode> createServicePrincipal(String appId) {
		logger.info("Creating service principal for application ID: {}", appId);

		try {
			ObjectNode requestBody = objectMapper.createObjectNode();
			requestBody.put("appId", appId);

			String requestBodyStr = objectMapper.writeValueAsString(requestBody);
			HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(requestBodyStr);

			return graphClient.makeGraphRequest(GraphRequest.builder()
					.v1()
					.withEndpoint(SERVICE_PRINCIPALS_ENDPOINT)
					.withMethod(HttpMethod.POST)
					.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
					.withBody(body)
					.build()
			).thenApply(response -> {
				if (response != null && !response.has(Constants.ERROR_FIELD)) {
					logger.info("Successfully created service principal for application ID: {}", appId);
					return response;
				} else {
					String error = response != null && response.has(Constants.ERROR_FIELD) ?
							response.get(Constants.ERROR_FIELD).asText() :
							"Unknown error creating service principal";
					throw new GraphClientException(error);
				}
			}).exceptionally(ex -> {
				String errorMessage = String.format("Failed to create service principal for app ID %s: %s",
						appId, ex.getMessage());
				logger.error(errorMessage);
				throw new GraphClientException(errorMessage, ex);
			});
		} catch (Exception e) {
			String errorMessage = String.format("Error preparing request for app ID %s: %s",
					appId, e.getMessage());
			logger.error(errorMessage);
			throw new GraphClientException(errorMessage, e);
		}
	}

	/**
	 * Checks if a service principal already exists for the given application ID.
	 *
	 * @param appId The application ID to check
	 * @return CompletableFuture<Boolean> true if service principal exists
	 */
	public CompletableFuture<Boolean> checkServicePrincipalExists(String appId) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.v1()
				.withEndpoint(SERVICE_PRINCIPALS_ENDPOINT +
						"?$filter=appId eq '" + appId + "'")
				.build()
		).thenApply(response -> {
			if (response != null && response.has("value")) {
				boolean exists = response.get("value").size() > 0;
				logger.info("Service principal for app ID {} {} exist",
						appId, exists ? "does" : "does not");
				return exists;
			}
			return false;
		}).exceptionally(ex -> {
			logger.error("Error checking service principal existence for app ID {}: {}",
					appId, ex.getMessage());
			return false;
		});
	}

	/**
	 * Creates a service principal if it doesn't already exist.
	 *
	 * @param appId The application ID to create the service principal for
	 * @return CompletableFuture<JsonNode> The service principal details
	 */
	public CompletableFuture<JsonNode> createServicePrincipalIfNotExists(String appId) {
		return checkServicePrincipalExists(appId)
				.thenCompose(exists -> {
					if (!exists) {
						return createServicePrincipal(appId);
					} else {
						logger.info("Service principal already exists for app ID: {}", appId);
						return getExistingServicePrincipal(appId);
					}
				});
	}

	private CompletableFuture<JsonNode> getExistingServicePrincipal(String appId) {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.v1()
				.withEndpoint(SERVICE_PRINCIPALS_ENDPOINT +
						"?$filter=appId eq '" + appId + "'")
				.build()
		).thenApply(response -> {
			if (response != null && response.has("value") && response.get("value").size() > 0) {
				return response.get("value").get(0);
			}
			throw new GraphClientException("Could not retrieve existing service principal");
		});
	}
}