package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.3.1v1")
public class TeamsAllowPublicUsersRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowPublicUsersRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowPublicUsersRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> configs = getFederationConfigurations();

		if (configs.isEmpty()) {
			logger.error("No federation configurations found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No federation configurations found"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();
		for (TeamsTenantFederationConfiguration configuration : configs) {
			if (configuration.allowPublicUsers) {
				TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
				prevConfig.identity = configuration.identity;
				prevConfig.allowPublicUsers = configuration.allowPublicUsers;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = configuration.identity;
				newConfig.allowPublicUsers = false;

				results.add(fixConfig(prevConfig, newConfig));
			}
		}

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "No configurations with allowPublicUsers=true found"));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(TenantFederationConfiguration prevConfig, TenantFederationConfiguration newConfig) {
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowPublicUsers: " + prevConfig.identity)
				.prevValue(prevConfig.allowPublicUsers)
				.newValue(newConfig.allowPublicUsers);

		return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(newConfig))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration: " + newConfig.identity + " fixed", List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "All federation configurations fixed successfully", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any federation configurations", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " configurations, failed to fix " + failedCount + " configurations",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean newValue = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean curValue = Boolean.parseBoolean(change.getNewValue().toString());

				TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
				prevConfig.identity = identity;
				prevConfig.allowPublicUsers = curValue;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = identity;
				newConfig.allowPublicUsers = newValue;

				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					results.add(fixConfig(prevConfig, newConfig));
				} else {
					ParameterChangeResult paramChange = new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter("allowPublicUsers: " + prevConfig.identity)
							.prevValue(prevConfig.allowPublicUsers)
							.newValue(newConfig.allowPublicUsers)
							.status(ParameterChangeStatus.FAILED);

					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
				}
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
