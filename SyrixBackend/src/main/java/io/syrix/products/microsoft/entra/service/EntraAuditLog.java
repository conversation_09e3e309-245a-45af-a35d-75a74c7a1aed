package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.products.microsoft.base.BaseService;
import io.syrix.protocols.client.MicrosoftGraphClient;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

public class EntraAuditLog extends BaseService {

    public EntraAuditLog(MicrosoftGraphClient graphClient, ObjectMapper objectMapper,
                         MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
    }

    public CompletableFuture<JsonNode> getEntraDirectoryAudit(Instant fromDateTime) {
        return listWithFilterByDate("/auditLogs/directoryAudits", fromDateTime, "activityDateTime", "GetEntraDirectoryAudit");
    }

    public CompletableFuture<JsonNode> getEntraSignInAudit(Instant fromDateTime) {
        return listWithFilterByDate("/auditLogs/signIns", fromDateTime, "createdDateTime", "GetEntraSignInAudit");
    }

    public CompletableFuture<JsonNode> getEntraCustomSecurityAttributeAudits(Instant fromDateTime) {
        return listWithFilterByDate("/auditLogs/customSecurityAttributeAudits", fromDateTime, "activityDateTime", "GetEntraCustomSecurityAttributeAudits");
    }

    public CompletableFuture<JsonNode> getEntraProvisioningObjectSummary(Instant fromDateTime) {
        return listWithFilterByDate("/auditLogs/provisioning", fromDateTime, "activityDateTime", "GetEntraProvisioningObjectSummary");
    }

}
