package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

@SuppressWarnings("unused")
@PolicyRemediator("MS.EXO.6.1v1")
public class ExchangeContactsSharingPolicyRemediator extends ExchangeSharingPolicyRemediator {
	private static final Logger logger = LoggerFactory.getLogger(ExchangeContactsSharingPolicyRemediator.class);

	public static final String NO_SHARING_POLICIES_FOUND = "No sharing policies found";

	public ExchangeContactsSharingPolicyRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// constructor for Rollback interface
	@SuppressWarnings("unused")
	public ExchangeContactsSharingPolicyRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	protected boolean isDataAllowedAllDomains(String domain) {
		return domain.contains("*") && domain.contains("Contacts");
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (Contact Sharing Policy)", getPolicyId());
		return super.remediate_();
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (Contact Sharing Policy)", getPolicyId());
		return super.rollback(fixResult);
	}

}