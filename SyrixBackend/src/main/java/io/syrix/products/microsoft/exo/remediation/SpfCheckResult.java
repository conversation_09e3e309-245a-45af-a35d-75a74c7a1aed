package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class SpfCheckResult {
	private final String domain;
	private final SpfStatus status;
	private final String issue;
	private final String recommendation;
	private final String currentRecord;
	private final String error;

	public enum SpfStatus {
		COMPLIANT,
		NON_COMPLIANT,
		UNREACHABLE
	}

	private SpfCheckResult(Builder builder) {
		this.domain = builder.domain;
		this.status = builder.status;
		this.issue = builder.issue;
		this.recommendation = builder.recommendation;
		this.currentRecord = builder.currentRecord;
		this.error = builder.error;
	}

	public String getDomain() {
		return domain;
	}

	public SpfStatus getStatus() {
		return status;
	}

	public String getIssue() {
		return issue;
	}

	public String getRecommendation() {
		return recommendation;
	}

	public String getCurrentRecord() {
		return currentRecord;
	}

	public String getError() {
		return error;
	}

	public boolean isCompliant() {
		return status == SpfStatus.COMPLIANT;
	}

	public boolean isNonCompliant() {
		return status == SpfStatus.NON_COMPLIANT;
	}

	public boolean isUnreachable() {
		return status == SpfStatus.UNREACHABLE;
	}

	public JsonNode toJsonNode(ObjectMapper objectMapper) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put("domain", domain);
		node.put("status", status.toString().toLowerCase());

		if (issue != null) {
			node.put("issue", issue);
		}

		if (recommendation != null) {
			node.put("recommendation", recommendation);
		}

		if (currentRecord != null) {
			node.put("current", currentRecord);
		}

		if (error != null) {
			node.put("error", error);
		}

		return node;
	}

	public static class Builder {
		private String domain;
		private SpfStatus status;
		private String issue;
		private String recommendation;
		private String currentRecord;
		private String error;

		public Builder domain(String domain) {
			this.domain = domain;
			return this;
		}

		public Builder status(SpfStatus status) {
			this.status = status;
			return this;
		}

		public Builder issue(String issue) {
			this.issue = issue;
			return this;
		}

		public Builder recommendation(String recommendation) {
			this.recommendation = recommendation;
			return this;
		}

		public Builder currentRecord(String currentRecord) {
			this.currentRecord = currentRecord;
			return this;
		}

		public Builder error(String error) {
			this.error = error;
			return this;
		}

		public SpfCheckResult build() {
			if (domain == null) {
				throw new IllegalStateException("Domain cannot be null");
			}
			if (status == null) {
				throw new IllegalStateException("Status cannot be null");
			}
			return new SpfCheckResult(this);
		}
	}

	public static Builder builder() {
		return new Builder();
	}
}