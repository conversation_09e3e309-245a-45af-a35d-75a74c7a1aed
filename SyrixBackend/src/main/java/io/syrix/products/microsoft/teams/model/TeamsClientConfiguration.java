package io.syrix.products.microsoft.teams.model;

import io.syrix.protocols.client.teams.powershell.command.types.ClientConfiguration;

@SuppressWarnings({"CommentedOutCode,unused"})
public class TeamsClientConfiguration {
    public Boolean allowEmailIntoChannel;
    public String restrictedSenderList;
    public Boolean allowDropBox;
    public Boolean allowBox;
    public Boolean allowGoogleDrive;
    public Boolean allowShareFile;
    public Boolean allowEgnyte;
    public Boolean allowOrganizationTab;
    public Boolean allowSkypeBusinessInterop;
    public String contentPin;
    public Boolean allowResourceAccountSendMessage;
    public String resourceAccountContentAccess;
    public Boolean allowGuestUser;
    public Boolean allowScopedPeopleSearchandAccess;
    public Boolean allowRoleBasedChatPermissions;
    public Boolean extendedWorkInfoInPeopleSearch;
    public String dataSource;
    public Key key;
    public String identity;
    public ConfigMetadata configMetadata;
    public String configId;

    public static class ConfigMetadata {
        public String authority;

        public ConfigMetadata() {}
        public ConfigMetadata(ClientConfiguration.ConfigMetadata configMetadata) {
            if (configMetadata == null) return;
            this.authority = configMetadata.authority;
        }
    }

    public static class Key {
        public String scopeClass;
        public String schemaId;
        public String authorityId;
        public String defaultXml;
        public String xmlRoot;

        public Key(){} //for serialization from json
        public Key(ClientConfiguration.Key key) {
            if (key == null) return;

            this.authorityId = key.authorityId == null ? null : String.format("Class=%s;InstanceId=%s;XmlRoot=", key.authorityId.classType, key.authorityId.instanceId);
            this.defaultXml = key.defaultXml == null ? null : String.format("SchemaId=;Data=;ConfigObject=;Signature=%s;IsModified=%s",key.defaultXml.signature, key.defaultXml.isModified ? "True" : "False");
            this.schemaId = key.schemaId == null ? null : "XName="; //TODO Artur recheck
            this.scopeClass = key.scopeClass;
            this.xmlRoot = key.xmlRoot == null ? null : String.format("name=%s", key.xmlRoot.name);
        }
    }

//    public static class SchemaId {
//        public XName xName;
//
//        public SchemaId() {} //for serialization from json
//        public SchemaId(ClientConfiguration.SchemaId schemaId) {
//            if (schemaId == null) return;
//            this.xName = schemaId.xName == null ? null : new XName(schemaId.xName);
//        }
//    }
//
//    public static class XName {
//        public String name;
//
//        public XName() {}//for serialization from json
//        public XName(ClientConfiguration.XName xName) {
//            if (xName == null) return;
//            this.name = xName.name;
//        }
//
//    }
//
//    public static class AuthorityId {
//        @JsonProperty(value = "Class")
//        public String classType;
//        public String instanceId;
//        public XmlRoot xmlRoot;
//
//        public AuthorityId() {} //for serialization from json
//        public AuthorityId(ClientConfiguration.AuthorityId authorityId) {
//            if (authorityId == null) return;
//            this.classType = authorityId.classType;
//            this.instanceId = authorityId.instanceId;
//            this.xmlRoot = authorityId.xmlRoot == null ? null : new XmlRoot(authorityId.xmlRoot);
//        }
//
//    }
//
//    public static class DefaultXml {
//        public SchemaId schemaId;
//        public Data data;
//        public String configObject;
//        public String signature;
//        public Boolean isModified;
//
//        public DefaultXml() { } //for serialization from json
//        public DefaultXml(ClientConfiguration.DefaultXml defaultXml) {
//            if (defaultXml == null) return;
//            this.configObject = defaultXml.configObject;
//            this.data = defaultXml.data == null ? null : new Data(defaultXml.data);
//            this.isModified = defaultXml.isModified;
//            this.schemaId = defaultXml.schemaId == null ? null : new SchemaId(defaultXml.schemaId);
//            this.signature = defaultXml.signature;
//        }
//
//    }
//
//    public static class Data {
//        public TeamsClientConfigurationData teamsClientConfiguration;
//
//        public Data() {}//for serialization from json
//        public Data(ClientConfiguration.Data data) {
//            if (data == null) return;
//            this.teamsClientConfiguration = data.teamsClientConfiguration == null ? null : new TeamsClientConfigurationData(data.teamsClientConfiguration);
//        }
//
//    }
//
//    public static class TeamsClientConfigurationData {
//        @JsonProperty(value = "@xmlns")
//        public String xmlns;
//
//        public TeamsClientConfigurationData() {} //for serialization from json
//        public TeamsClientConfigurationData(ClientConfiguration.TeamsClientConfigurationData teamsClientConfigurationData) {
//            if (teamsClientConfigurationData.xmlns == null) return;
//            this.xmlns = teamsClientConfigurationData.xmlns;
//        }
//    }
//
//    public static class XmlRoot {
//        public String name;
//
//        public XmlRoot() {} //for serialization from json
//        public XmlRoot(ClientConfiguration.XmlRoot xmlRoot) {
//            if (xmlRoot == null) return;
//            this.name = xmlRoot.name;
//        }
//    }


    public TeamsClientConfiguration() {}  //for serialization from json

    public TeamsClientConfiguration(ClientConfiguration config) {
        this.allowEmailIntoChannel = config.allowEmailIntoChannel;
        this.restrictedSenderList = config.restrictedSenderList;
        this.allowDropBox = config.allowDropBox;
        this.allowBox = config.allowBox;
        this.allowGoogleDrive = config.allowGoogleDrive;
        this.allowShareFile = config.allowShareFile;
        this.allowEgnyte = config.allowEgnyte;
        this.allowOrganizationTab = config.allowOrganizationTab;
        this.allowSkypeBusinessInterop = config.allowSkypeBusinessInterop;
        this.contentPin = config.contentPin;
        this.allowResourceAccountSendMessage = config.allowResourceAccountSendMessage;
        this.resourceAccountContentAccess = config.resourceAccountContentAccess;
        this.allowGuestUser = config.allowGuestUser;
        this.allowScopedPeopleSearchandAccess = config.allowScopedPeopleSearchandAccess;
        this.allowRoleBasedChatPermissions = config.allowRoleBasedChatPermissions;
        this.extendedWorkInfoInPeopleSearch = config.extendedWorkInfoInPeopleSearch;
        this.dataSource = config.dataSource;
        this.key = config.key == null ? null : new Key(config.key);
        this.identity = config.identity;
        this.configMetadata = config.configMetadata == null ? null : new ConfigMetadata(config.configMetadata) ;
        this.configId = config.configId;
    }

}
