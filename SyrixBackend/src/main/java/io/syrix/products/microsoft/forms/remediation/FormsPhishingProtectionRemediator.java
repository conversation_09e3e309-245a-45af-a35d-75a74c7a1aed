package io.syrix.products.microsoft.forms.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.forms.FormsConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static io.syrix.common.constants.Constants.*;

@PolicyRemediator("MS.FORMS.1.1v1")
public class FormsPhishingProtectionRemediator extends RemediatorBase {

    private static final String FORMS_SETTINGS_ENDPOINT = "/admin/forms/settings";
    private static final String POLICY_ID = "MS.FORMS.1.1v1";
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;

    private final MicrosoftGraphClient graphClient;
    private final FormsConfigurationService formsConfigurationService;
    private final ObjectMapper objectMapper;
    private final MetricsCollector metrics;

    public FormsPhishingProtectionRemediator(MicrosoftGraphClient graphClient,
                                           FormsConfigurationService formsConfigurationService,
                                           ObjectMapper objectMapper,
                                           MetricsCollector metrics) {
        this.graphClient = graphClient;
        this.formsConfigurationService = formsConfigurationService;
        this.objectMapper = objectMapper;
        this.metrics = metrics;
    }

    /**
     * Enables Microsoft Forms phishing protection according to CIS Microsoft 365 Foundations Benchmark 1.3.5
     *
     * @return CompletableFuture containing remediation result
     */
    @Override
    public CompletableFuture<JsonNode> remediate() {
        logger.info("Starting remediation for MS.FORMS.1.1v1 - Enable Forms Phishing Protection");
        
        long startTime = System.currentTimeMillis();
        return validateCurrentConfiguration()
                .thenCompose(this::performRemediation)
                .thenApply(result -> {
                    // Record successful remediation
                    Duration duration = Duration.ofMillis(System.currentTimeMillis() - startTime);
                    metrics.recordApiCall("remediation_" + POLICY_ID, duration, true);
                    return logRemediationSuccess(result);
                })
                .exceptionally(throwable -> {
                    // Record failed remediation
                    Duration duration = Duration.ofMillis(System.currentTimeMillis() - startTime);
                    metrics.recordApiCall("remediation_" + POLICY_ID, duration, false);
                    return handleRemediationFailure(throwable);
                });
    }

    /**
     * Validates the current Forms phishing protection configuration
     *
     * @return CompletableFuture containing current configuration state
     */
    private CompletableFuture<JsonNode> validateCurrentConfiguration() {
        logger.debug("Validating current Forms phishing protection configuration");

        return formsConfigurationService.getFormsPhishingProtectionConfiguration()
                .exceptionally(throwable -> {
                    logger.error("Failed to retrieve current Forms configuration for validation: {}", throwable.getMessage());
                    throw new RuntimeException("Configuration validation failed", throwable);
                });
    }

    /**
     * Performs the actual remediation by enabling phishing protection
     *
     * @param currentConfig Current Forms configuration
     * @return CompletableFuture containing remediation result
     */
    private CompletableFuture<JsonNode> performRemediation(JsonNode currentConfig) {
        try {
            // Check if phishing protection is already enabled
            boolean isCurrentlyEnabled = currentConfig.path("isInOrgFormsPhishingScanEnabled").asBoolean(false);
            
            if (isCurrentlyEnabled) {
                logger.info("Forms phishing protection is already enabled - no remediation needed");
                return CompletableFuture.completedFuture(
                        createSuccessResponse("Forms phishing protection already enabled", ExoConstants.STATUS_SUCCESS)
                );
            }

            logger.info("Forms phishing protection is disabled - enabling now");
            return enablePhishingProtection();

        } catch (Exception e) {
            logger.error("Error during remediation analysis: {}", e.getMessage(), e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Enables Forms phishing protection via Microsoft Graph API
     *
     * @return CompletableFuture containing the API response
     */
    private CompletableFuture<JsonNode> enablePhishingProtection() {
        try {
            // Create the request body to enable phishing protection
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("isInOrgFormsPhishingScanEnabled", true);
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            logger.debug("Sending PATCH request to enable Forms phishing protection");

            GraphRequest request = GraphRequest.builder()
                    .beta()
                    .withEndpoint(FORMS_SETTINGS_ENDPOINT)
                    .withMethod(HttpMethod.PATCH)
                    .withBody(HttpRequest.BodyPublishers.ofString(jsonBody))
                    .addHeader("Content-Type", "application/json")
                    .build();

            return graphClient.makeGraphRequest(request)
                    .orTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                    .thenApply(response -> {
                        logger.info("Successfully enabled Forms phishing protection");
                        return createSuccessResponse("Forms phishing protection successfully enabled", ExoConstants.STATUS_SUCCESS);
                    })
                    .exceptionally(throwable -> {
                        logger.error("Failed to enable Forms phishing protection: {}", throwable.getMessage(), throwable);
                        throw new RuntimeException("Failed to enable Forms phishing protection", throwable);
                    });

        } catch (Exception e) {
            logger.error("Error creating request for Forms phishing protection: {}", e.getMessage(), e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Creates a success response for successful remediation
     *
     * @param message Success message
     * @param status Remediation status
     * @return JsonNode containing the success response
     */
    private JsonNode createSuccessResponse(String message, String status) {
        ObjectNode response = objectMapper.createObjectNode();
        response.put(STATUS_FIELD, status);
        response.put(POLICY_ID_FIELD, POLICY_ID);
        response.put(MESSAGE_FIELD, message);
        response.put("timestamp", Instant.now().toString());
        response.put("isInOrgFormsPhishingScanEnabled", true);
        return response;
    }

    /**
     * Logs successful remediation and returns the result
     *
     * @param result Remediation result
     * @return Same result for method chaining
     */
    private JsonNode logRemediationSuccess(JsonNode result) {
        logger.info("Forms phishing protection remediation completed successfully for {}", POLICY_ID);
        // TODO: Add storage service for saving remediation results
        return result;
    }

    /**
     * Handles remediation failures with proper logging and response creation
     *
     * @param throwable The exception that occurred
     * @return JsonNode containing the failure response
     */
    private JsonNode handleRemediationFailure(Throwable throwable) {
        String errorMessage = "Failed to remediate Forms phishing protection: " + throwable.getMessage();
        logger.error(errorMessage, throwable);

        ObjectNode failureResponse = objectMapper.createObjectNode();
        failureResponse.put(STATUS_FIELD, ExoConstants.STATUS_FAILED);
        failureResponse.put(POLICY_ID_FIELD, POLICY_ID);
        failureResponse.put(MESSAGE_FIELD, errorMessage);
        failureResponse.put("timestamp", Instant.now().toString());
        failureResponse.put("error", throwable.getMessage());

        // TODO: Add storage service for saving remediation results
        return failureResponse;
    }

    /**
     * Returns the policy ID for this remediator
     *
     * @return The policy ID string
     */
    @Override
    public String getPolicyId() {
        return POLICY_ID;
    }
}