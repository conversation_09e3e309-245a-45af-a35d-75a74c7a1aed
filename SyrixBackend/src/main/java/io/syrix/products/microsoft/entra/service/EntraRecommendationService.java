package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.products.microsoft.entra.model.reccomendations.ImpactedResource;
import io.syrix.products.microsoft.entra.model.reccomendations.Recommendation;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpRequest;
import java.time.LocalDateTime;
import java.util.List;

public class EntraRecommendationService {
	private static final Logger logger = LoggerFactory.getLogger(EntraRecommendationService.class);
	private static final String RECOMMENDATIONS_ENDPOINT = "/directory/recommendations";
	private static final String IMPACTED_RESOURCES = "/impactedResources/";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraRecommendationService(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
	}

	public List<Recommendation> listRecommendations() {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT)
						.addHeader("Prefer", "include-unknown-enum-members")
						.withMethod(HttpMethod.GET)
						.build())
				.join();

		try {
			JsonNode valueNode = response.get("value");
			return objectMapper.convertValue(valueNode, new TypeReference<List<Recommendation>>() {});
		} catch (Exception e) {
			logger.error("Failed to parse recommendations list", e);
			throw new RuntimeException("Failed to parse recommendations", e);
		}
	}

	public Recommendation getRecommendation(String id) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + id)
						.withMethod(HttpMethod.GET)
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, Recommendation.class);
		} catch (Exception e) {
			logger.error("Failed to parse recommendation", e);
			throw new RuntimeException("Failed to parse recommendation", e);
		}
	}

	public Recommendation postponeRecommendation(String id, LocalDateTime untilDateTime) {
		String body = String.format("{\"postponeUntilDateTime\": \"%s\"}", untilDateTime.toString());

		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + id + "/postpone")
						.withMethod(HttpMethod.POST)
						.withBody(HttpRequest.BodyPublishers.ofString(body))
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, Recommendation.class);
		} catch (Exception e) {
			logger.error("Failed to parse postponed recommendation", e);
			throw new RuntimeException("Failed to parse postponed recommendation", e);
		}
	}

	public Recommendation dismissRecommendation(String id, String dismissReason) {
		String body = String.format("{\"dismissReason\": \"%s\"}", dismissReason);

		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + id + "/dismiss")
						.withMethod(HttpMethod.POST)
						.withBody(HttpRequest.BodyPublishers.ofString(body))
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, Recommendation.class);
		} catch (Exception e) {
			logger.error("Failed to parse dismissed recommendation", e);
			throw new RuntimeException("Failed to parse dismissed recommendation", e);
		}
	}

	public Recommendation completeRecommendation(String id) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + id + "/complete")
						.withMethod(HttpMethod.POST)
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, Recommendation.class);
		} catch (Exception e) {
			logger.error("Failed to parse completed recommendation", e);
			throw new RuntimeException("Failed to parse completed recommendation", e);
		}
	}

	public Recommendation reactivateRecommendation(String id) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + id + "/reactivate")
						.withMethod(HttpMethod.POST)
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, Recommendation.class);
		} catch (Exception e) {
			logger.error("Failed to parse reactivated recommendation", e);
			throw new RuntimeException("Failed to parse reactivated recommendation", e);
		}
	}

	public List<ImpactedResource> listImpactedResources(String recommendationId) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + recommendationId + "/impactedResources")
						.withMethod(HttpMethod.GET)
						.build())
				.join();

		try {
			JsonNode valueNode = response.get("value");
			return objectMapper.convertValue(valueNode, new TypeReference<List<ImpactedResource>>() {});
		} catch (Exception e) {
			logger.error("Failed to parse impacted resources list", e);
			throw new RuntimeException("Failed to parse impacted resources", e);
		}
	}

	public ImpactedResource getImpactedResource(String recommendationId, String resourceId) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + recommendationId +
								IMPACTED_RESOURCES + resourceId)
						.withMethod(HttpMethod.GET)
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, ImpactedResource.class);
		} catch (Exception e) {
			logger.error("Failed to parse impacted resource", e);
			throw new RuntimeException("Failed to parse impacted resource", e);
		}
	}

	public ImpactedResource completeImpactedResource(String recommendationId, String resourceId) {
		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + recommendationId +
								IMPACTED_RESOURCES + resourceId + "/complete")
						.withMethod(HttpMethod.POST)
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, ImpactedResource.class);
		} catch (Exception e) {
			logger.error("Failed to parse completed impacted resource", e);
			throw new RuntimeException("Failed to parse completed impacted resource", e);
		}
	}

	public ImpactedResource dismissImpactedResource(String recommendationId, String resourceId, String dismissReason) {
		String body = String.format("{\"dismissReason\": \"%s\"}", dismissReason);

		JsonNode response = graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(RECOMMENDATIONS_ENDPOINT + "/" + recommendationId +
								IMPACTED_RESOURCES + resourceId + "/dismiss")
						.withMethod(HttpMethod.POST)
						.withBody(HttpRequest.BodyPublishers.ofString(body))
						.build())
				.join();

		try {
			return objectMapper.convertValue(response, ImpactedResource.class);
		} catch (Exception e) {
			logger.error("Failed to parse dismissed impacted resource", e);
			throw new RuntimeException("Failed to parse dismissed impacted resource", e);
		}
	}
}