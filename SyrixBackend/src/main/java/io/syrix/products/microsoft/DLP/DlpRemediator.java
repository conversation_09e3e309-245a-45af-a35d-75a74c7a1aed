package io.syrix.products.microsoft.DLP;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.base.SkuProcessingResult;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static io.syrix.protocols.client.PowerShellClient.DEFAULT_PARAMETERS;

/**
 * Remediator for MS.EXO.8.1v2 that ensures a Data Loss Prevention (DLP) solution is in place
 * to detect and block sensitive information in Exchange Online communications.
 */
@PolicyRemediator("DLP - MS.EXO.8.1v2")
public class DlpRemediator extends RemediatorBase {
	private static final String NO_DLP_POLICIES_FOUND = "No DLP policies found";
	private static final String ERROR = "Error";
	public static final String EXCHANGE_LOCATION = "ExchangeLocation";
	public static final String SHARE_POINT_LOCATION = "SharePointLocation";
	public static final String ONE_DRIVE_LOCATION = "OneDriveLocation";
	public static final String TEAMS_LOCATION = "TeamsLocation";
	public static final String EXCLUDED_EXCHANGE_LOCATION = "ExcludedExchangeLocation";
	public static final String EXCLUDED_SHARE_POINT_LOCATION = "ExcludedSharePointLocation";
	public static final String EXCLUDED_ONE_DRIVE_LOCATION = "ExcludedOneDriveLocation";
	public static final String EXCLUDED_TEAMS_LOCATION = "ExcludedTeamsLocation";

	private final PowerShellClient complianceClient;
	private final ObjectMapper objectMapper;
	private final SkuProcessingResult skus;
	private final DlpConfiguration config;

	/**
	 * Creates a new DLP Remediator with custom configuration.
	 *
	 * @param complianceClient PowerShell client for compliance operations
	 * @param skus License processing result
	 * @param config DLP configuration
	 */
	public DlpRemediator(PowerShellClient complianceClient,
						 SkuProcessingResult skus,
						 DlpConfiguration config) {
		this.complianceClient = complianceClient;
		this.objectMapper = new ObjectMapper();
		this.skus = skus;
		this.config = config;
	}

	/**
	 * Creates a new DLP Remediator with default configuration.
	 *
	 * @param complianceClient PowerShell client for compliance operations
	 * @param skus License processing result
	 */
	public DlpRemediator(PowerShellClient complianceClient, SkuProcessingResult skus) {
		this(complianceClient, skus, createDefaultConfig());
	}

	/**
	 * Creates the default DLP configuration.
	 *
	 * @return A default DLP configuration
	 */
	public static DlpConfiguration createDefaultConfig() {
		// Create default policy config using the new Builder pattern
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("Agency Sensitive Data DLP Policy")
				.exchangeLocation("All")
				.sharePointLocation("All")
				.oneDriveLocation("All")
				.teamsLocation("All")
				.build();

		// Create default sensitive info types
		List<SensitiveInfoTypeConfig> sensitiveInfoTypes = new ArrayList<>();
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("Credit Card Number"));
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("U.S. Individual Taxpayer Identification Number (ITIN)"));
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("U.S. Social Security Number (SSN)"));

		// Create default rule config using the new Builder pattern
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("Block Sensitive Content")
				.sensitiveInfoTypes(sensitiveInfoTypes)
				.blockAccess(true)
				.notifyUser(Collections.singletonList("SiteAdmin"))
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting DLP policy remediation for Exchange Online (MS.EXO.8.1v2)");

// Step 1: Check if there's a license for DLP using LicenseProcessor
//boolean hasLicense = skus.hasP5License();  // Direct use of licenseProcessor
//if (!hasLicense) {
//    return IPolicyRemediator.failed(getPolicyId(),
//            "E5/G5 license required for Microsoft Defender DLP not found");
//}

		// Step 2: Check for existing DLP policies
		return complianceClient.getCPPSEndpoint().thenCompose(this::processRemediation);
	}

	private CompletableFuture<JsonNode> processRemediation(String cppsEndpoint) {
		return complianceClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Get-DlpCompliancePolicy",
								DEFAULT_PARAMETERS,
								cppsEndpoint))
				.thenCompose(policies -> processExistingPolicies(policies, cppsEndpoint))
				.exceptionally(ex -> {
					logger.error("Failed to remediate DLP policy", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> processExistingPolicies(JsonNode policies, String cppsEndpoint) {
		// Check if our required policy exists
		if (policies != null && (policies instanceof ArrayNode array && !array.isEmpty())) {
			for (JsonNode policy : array) {
				String policyName = policy.path("Name").asText();
				if (policyName.equals(config.getPolicyConfig().getName())) {
					logger.info("Found existing DLP policy: {}. Verifying configuration...", policyName);
					return verifyDlpPolicy(policy, cppsEndpoint);
				}
			}
		}

		// No policy with our name exists, create a new one
		logger.info(NO_DLP_POLICIES_FOUND + ". Creating new DLP policy.");
		return createDlpPolicy(cppsEndpoint);
	}

	private CompletableFuture<JsonNode> createDlpPolicy(String cppsEndpoint) {
		// Step 1: Create the policy
		Map<String, Object> policyParams = new HashMap<>();
		DlpPolicyConfig policyConfig = config.getPolicyConfig();

		policyParams.put("Name", policyConfig.getName());

		// Handle the new collection-based location parameters
		if (policyConfig.getExchangeLocation() != null) {
			policyParams.put(EXCHANGE_LOCATION, policyConfig.getExchangeLocation());
		}

		if (policyConfig.getSharePointLocation() != null) {
			policyParams.put(SHARE_POINT_LOCATION, policyConfig.getSharePointLocation());
		}

		if (policyConfig.getOneDriveLocation() != null) {
			policyParams.put(ONE_DRIVE_LOCATION, policyConfig.getOneDriveLocation());
		}

		if (policyConfig.getTeamsLocation() != null) {
			policyParams.put(TEAMS_LOCATION, policyConfig.getTeamsLocation());
		}

		// Add any excluded locations if they exist
		if (policyConfig.getExcludedExchangeLocation() != null) {
			policyParams.put(EXCLUDED_EXCHANGE_LOCATION, policyConfig.getExcludedExchangeLocation());
		}

		if (policyConfig.getExcludedSharePointLocation() != null) {
			policyParams.put(EXCLUDED_SHARE_POINT_LOCATION, policyConfig.getExcludedSharePointLocation());
		}

		if (policyConfig.getExcludedOneDriveLocation() != null) {
			policyParams.put(EXCLUDED_ONE_DRIVE_LOCATION, policyConfig.getExcludedOneDriveLocation());
		}

		if (policyConfig.getExcludedTeamsLocation() != null) {
			policyParams.put(EXCLUDED_TEAMS_LOCATION, policyConfig.getExcludedTeamsLocation());
		}

		// Add optional policy parameters if they exist
		if (policyConfig.getComment() != null) {
			policyParams.put("Comment", policyConfig.getComment());
		}

		if (policyConfig.getMode() != null) {
			policyParams.put("Mode", policyConfig.getMode());
		}

		if (policyConfig.getPriority() != null) {
			policyParams.put("Priority", policyConfig.getPriority());
		}

		if (policyConfig.getActivationDate() != null) {
			policyParams.put("ActivationDate", policyConfig.getActivationDate());
		}

		if (policyConfig.getExpiryDate() != null) {
			policyParams.put("ExpiryDate", policyConfig.getExpiryDate());
		}

		return complianceClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("New-DlpCompliancePolicy",
								policyParams,
								cppsEndpoint))
				.thenCompose(policyResult -> {
					if (policyResult.has(ERROR) && !policyResult.get(ERROR).isNull()) {
						return IPolicyRemediator.failed(getPolicyId(), "Failed to create DLP policy: " +
								policyResult.get(ERROR).asText());
					}

					// Step 2: Create the rule
					return createDlpRule(config.getPolicyConfig().getName(), cppsEndpoint);
				});
	}

	private CompletableFuture<JsonNode> createDlpRule(String policyName, String cppsEndpoint) {
		Map<String, Object> ruleParams = new HashMap<>();
		DlpRuleConfig ruleConfig = config.getRuleConfig();

		// Add required parameters
		ruleParams.put("Name", ruleConfig.getName());
		ruleParams.put("Policy", policyName);

		// Add sensitive info detection
		if (ruleConfig.getContentContainsSensitiveInformation() != null) {
			ruleParams.put("ContentContainsSensitiveInformation", ruleConfig.getContentContainsSensitiveInformation());
		}

		// Add actions and notifications
		if (ruleConfig.getBlockAccess() != null) {
			ruleParams.put("BlockAccess", ruleConfig.getBlockAccess());
		}

		if (ruleConfig.getBlockAccessScope() != null) {
			ruleParams.put("BlockAccessScope", ruleConfig.getBlockAccessScope());
		}

		if (ruleConfig.getNotifyUser() != null) {
			ruleParams.put("NotifyUser", ruleConfig.getNotifyUser());
		}

		if (ruleConfig.getNotifyUserType() != null) {
			ruleParams.put("NotifyUserType", ruleConfig.getNotifyUserType());
		}

		// Add rule configuration options
		if (ruleConfig.getComment() != null) {
			ruleParams.put("Comment", ruleConfig.getComment());
		}

		if (ruleConfig.getDisabled() != null) {
			ruleParams.put("Disabled", ruleConfig.getDisabled());
		}

		if (ruleConfig.getPriority() != null) {
			ruleParams.put("Priority", ruleConfig.getPriority());
		}

		if (ruleConfig.getStopPolicyProcessing() != null) {
			ruleParams.put("StopPolicyProcessing", ruleConfig.getStopPolicyProcessing());
		}

		if (ruleConfig.getReportSeverityLevel() != null) {
			ruleParams.put("ReportSeverityLevel", ruleConfig.getReportSeverityLevel());
		}

		// Add advanced rule if present
		if (ruleConfig.getAdvancedRule() != null) {
			ruleParams.put("AdvancedRule", ruleConfig.getAdvancedRule());
		}

		// Add notification settings if present
		if (ruleConfig.getNotifyAllowOverride() != null) {
			ruleParams.put("NotifyAllowOverride", ruleConfig.getNotifyAllowOverride());
		}

		if (ruleConfig.getNotifyEmailCustomSubject() != null) {
			ruleParams.put("NotifyEmailCustomSubject", ruleConfig.getNotifyEmailCustomSubject());
		}

		if (ruleConfig.getNotifyEmailCustomText() != null) {
			ruleParams.put("NotifyEmailCustomText", ruleConfig.getNotifyEmailCustomText());
		}

		if (ruleConfig.getNotifyPolicyTipCustomText() != null) {
			ruleParams.put("NotifyPolicyTipCustomText", ruleConfig.getNotifyPolicyTipCustomText());
		}

		if (ruleConfig.getNotifyPolicyTipDisplayOption() != null) {
			ruleParams.put("NotifyPolicyTipDisplayOption", ruleConfig.getNotifyPolicyTipDisplayOption());
		}

		// Add other conditions if present
		if (ruleConfig.getAccessScope() != null) {
			ruleParams.put("AccessScope", ruleConfig.getAccessScope());
		}

		if (ruleConfig.getDocumentIsPasswordProtected() != null) {
			ruleParams.put("DocumentIsPasswordProtected", ruleConfig.getDocumentIsPasswordProtected());
		}

		if (ruleConfig.getDocumentIsUnsupported() != null) {
			ruleParams.put("DocumentIsUnsupported", ruleConfig.getDocumentIsUnsupported());
		}

		if (ruleConfig.getFromScope() != null) {
			ruleParams.put("FromScope", ruleConfig.getFromScope());
		}

		// Add additional condition parameters...
		// Note: We could add more parameters here based on what's available in the ruleConfig

		return complianceClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("New-DlpComplianceRule",
								ruleParams,
								cppsEndpoint))
				.thenCompose(ruleResult -> {
					if (ruleResult.has(ERROR) && !ruleResult.get(ERROR).isNull()) {
						return IPolicyRemediator.failed(getPolicyId(), "Failed to create DLP rule: " +
								ruleResult.get(ERROR).asText());
					}
					return IPolicyRemediator.success(getPolicyId(), "Successfully created DLP policy and rule");
				});
	}

	private CompletableFuture<JsonNode> verifyDlpPolicy(JsonNode policy, String cppsEndpoint) {
		String policyId = policy.path("Id").asText();
		List<CompletableFuture<JsonNode>> updates = new ArrayList<>();
		Map<String, Object> params = checkAndUpdatePolicyLocations(policy);

		if (!params.isEmpty()) {
			params.put("Identity", policyId);
			updates.add(complianceClient.executeCmdletCommand(
					new PowerShellClient.CommandRequest("Set-DlpCompliancePolicy", params, cppsEndpoint)));
		}
		return verifyRules(policy, cppsEndpoint, updates);
	}

	private boolean isValuePresent(JsonNode properties, List<String> values) {
		if (properties == null || !(properties instanceof ArrayNode)) {
			return false;
		}

		ArrayNode array = (ArrayNode) properties;
		if (array.isEmpty()) {
			return false;
		}

		// If the configuration values are null or empty, any value is considered present
		if (values == null || values.isEmpty()) {
			return true;
		}

		// For single value "All", check if it exists
		if (values.size() == 1 && "All".equals(values.get(0))) {
			for (JsonNode element : array) {
				if ("All".equals(element.asText())) {
					return true;
				}
			}
			return false;
		}

		// Otherwise, verify all config values are present in the array
		for (String value : values) {
			boolean found = false;
			for (JsonNode element : array) {
				if (value.equals(element.asText())) {
					found = true;
					break;
				}
			}
			if (!found) {
				return false;
			}
		}

		return true;
	}

	private Map<String, Object> checkAndUpdatePolicyLocations(JsonNode policy) {
		Map<String, Object> updateParams = new HashMap<>();
		DlpPolicyConfig policyConfig = config.getPolicyConfig();

		if (!isValuePresent(policy.path(EXCHANGE_LOCATION), policyConfig.getExchangeLocation())) {
			updateParams.put(EXCHANGE_LOCATION, policyConfig.getExchangeLocation());
		}

		if (!isValuePresent(policy.path(SHARE_POINT_LOCATION), policyConfig.getSharePointLocation())) {
			updateParams.put(SHARE_POINT_LOCATION, policyConfig.getSharePointLocation());
		}

		if (!isValuePresent(policy.path(ONE_DRIVE_LOCATION), policyConfig.getOneDriveLocation())) {
			updateParams.put(ONE_DRIVE_LOCATION, policyConfig.getOneDriveLocation());
		}

		if (!isValuePresent(policy.path(TEAMS_LOCATION), policyConfig.getTeamsLocation())) {
			updateParams.put(TEAMS_LOCATION, policyConfig.getTeamsLocation());
		}

		// Check for excluded locations too
		if (policyConfig.getExcludedExchangeLocation() != null &&
				!isValuePresent(policy.path(EXCLUDED_EXCHANGE_LOCATION), policyConfig.getExcludedExchangeLocation())) {
			updateParams.put(EXCLUDED_EXCHANGE_LOCATION, policyConfig.getExcludedExchangeLocation());
		}

		if (policyConfig.getExcludedSharePointLocation() != null &&
				!isValuePresent(policy.path(EXCLUDED_SHARE_POINT_LOCATION), policyConfig.getExcludedSharePointLocation())) {
			updateParams.put(EXCLUDED_SHARE_POINT_LOCATION, policyConfig.getExcludedSharePointLocation());
		}

		if (policyConfig.getExcludedOneDriveLocation() != null &&
				!isValuePresent(policy.path(EXCLUDED_ONE_DRIVE_LOCATION), policyConfig.getExcludedOneDriveLocation())) {
			updateParams.put(EXCLUDED_ONE_DRIVE_LOCATION, policyConfig.getExcludedOneDriveLocation());
		}

		if (policyConfig.getExcludedTeamsLocation() != null &&
				!isValuePresent(policy.path(EXCLUDED_TEAMS_LOCATION), policyConfig.getExcludedTeamsLocation())) {
			updateParams.put(EXCLUDED_TEAMS_LOCATION, policyConfig.getExcludedTeamsLocation());
		}

		return updateParams;
	}

	private CompletableFuture<JsonNode> verifyRules(JsonNode policy,
													String cppsEndpoint,
													List<CompletableFuture<JsonNode>> updates) {
		return complianceClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Get-DlpComplianceRule",
								Map.of("Policy", policy.path("Name").asText()),
								cppsEndpoint))
				.thenCompose(rules -> {
					if (rules != null && (rules instanceof ArrayNode array && !array.isEmpty())) {
						boolean hasRequiredRule = checkRulesContainRequiredProtections(rules);

						if (!hasRequiredRule) {
							return createDlpRule(policy.path("Name").asText(), cppsEndpoint);
						}

						if (!updates.isEmpty()) {
							return handlePolicyUpdates(updates);
						}

						return IPolicyRemediator.success(getPolicyId(),
								"DLP policy and rules already correctly configured");
					} else {
						return createDlpRule(policy.path("Name").asText(), cppsEndpoint);
					}
				});
	}

	private boolean checkRulesContainRequiredProtections(JsonNode rules) {
		boolean hasRequiredRule = false;
		ArrayNode rulesArray = (ArrayNode) rules.get(Constants.VALUE_FIELD);

		for (JsonNode rule : rulesArray) {
			JsonNode contentSettings = rule.path("ContentContainsSensitiveInformation");
			if (!contentSettings.isMissingNode() && !contentSettings.isEmpty()) {
				hasRequiredRule = true;
				break;
			}
		}

		return hasRequiredRule;
	}

	private CompletableFuture<JsonNode> handlePolicyUpdates(List<CompletableFuture<JsonNode>> updates) {
		return CompletableFuture.allOf(updates.toArray(new CompletableFuture[0]))
				.thenCompose(v -> {
					boolean anyFailures = updates.stream()
							.map(CompletableFuture::join)
							.anyMatch(result -> result.has(ERROR) && !result.get(ERROR).isNull());

					if (anyFailures) {
						return IPolicyRemediator.failed(getPolicyId(),
								"Failed to update DLP policy locations");
					} else {
						return IPolicyRemediator.success(getPolicyId(),
								"Successfully verified and updated DLP policy");
					}
				});
	}
}