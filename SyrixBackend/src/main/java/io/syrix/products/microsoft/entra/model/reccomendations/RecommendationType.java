package io.syrix.products.microsoft.entra.model.reccomendations;

public enum RecommendationType {
	adfsAppsMigration,
	enableDesktopSSO,
	enablePHS,
	enableProvisioning,
	switchFromPerUserMFA,
	tenantMFA,
	thirdPartyApps,
	turnOffPerUserMFA,
	useAuthenticatorApp,
	useMyApps,
	staleApps,
	staleAppCreds,
	applicationCredentialExpiry,
	servicePrincipalKeyExpiry,
	adminMFAV2,
	blockLegacyAuthentication,
	integratedApps,
	mfaRegistrationV2,
	pwagePolicyNew,
	passwordHashSync,
	oneAdmin,
	roleOverlap,
	selfServicePasswordReset,
	signinRiskPolicy,
	userRiskPolicy,
	verifyAppPublisher,
	privateLinkForAAD,
	appRoleAssignmentsGroups,
	appRoleAssignmentsUsers,
	managedIdentity,
	overprivilegedApps,
	unknownFutureValue,
	longLivedCredentials,
	aadConnectDeprecated,
	adalToMsalMigration,
	ownerlessApps,
	inactiveGuests,
	adGraphDeprecationApplication,
	aadGraphDeprecationServicePrincipal,
	mfaServerDeprecation,
	insiderRiskPolicy,
	aadGraphDeprecationApplication
}
