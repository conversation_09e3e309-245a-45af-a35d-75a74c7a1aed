package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.6v1")
public class TeamsAllowCloudRecordingRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowCloudRecordingRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowCloudRecordingRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			logger.error("No meeting policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found in configuration"));
		}

		TeamsMeetingPolicy globalPolicy = getGlobalTeamsMeeting(policies);
		if (globalPolicy == null) {
			logger.error("Global policy not found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Global policy not found"));
		}

		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
		meetingPolicy.allowCloudRecording = false;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("AllowCloudRecording")
				.prevValue(globalPolicy.allowCloudRecording)
				.newValue(meetingPolicy.allowCloudRecording);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(jsonNode -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.CLOUD_RECORDING_SUCCESS_MESSAGE, List.of(paramChange));
				})
				.exceptionally(ex -> {
					paramChange.status(ParameterChangeStatus.FAILED);
					logger.error("Exception during allow cloud recording remediation", ex);
					return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
			boolean allowCloudRecording = Boolean.parseBoolean(changeResult.getPrevValue().toString());
			boolean prevAllowCloudRecording = Boolean.parseBoolean(changeResult.getNewValue().toString());

			MeetingPolicy meetingPolicy = new MeetingPolicy();
			meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
			meetingPolicy.allowCloudRecording = allowCloudRecording;

			ParameterChangeResult paramChange = new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter("AllowCloudRecording")
					.prevValue(prevAllowCloudRecording)
					.newValue(allowCloudRecording);

			return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
					.thenApply(jsonNode -> {
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back cloud recording policy", List.of(paramChange));
					})
					.exceptionally(ex -> {
						paramChange.status(ParameterChangeStatus.FAILED);
						logger.error("Exception during cloud recording policy rollback", ex);
						return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
					});
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	/**
	 * Gets global Teams meeting policy from the list of policies
	 *
	 * @param policies List of Teams meeting policies
	 * @return Global Teams meeting policy or null if not found
	 */
	private TeamsMeetingPolicy getGlobalTeamsMeeting(List<TeamsMeetingPolicy> policies) {
		return policies.stream()
				.filter(p -> TeamsConstants.GLOBAL_POLICY_IDENTITY.equals(p.identity))
				.findFirst()
				.orElse(null);
	}


}
