package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.MESSAGE_FIELD;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;

/**
 * This remediator enforces the use of Privileged Identity Management (PIM) for
 * highly privileged roles by removing direct role assignments and replacing them
 * with PIM-managed eligible assignments.
 */
@PolicyRemediator("MS.AAD.7.5v1")
public class DirectAssignmentRemediator extends RemediatorBase {
	private static final String ROLE_DEFINITIONS_ENDPOINT = "/roleManagement/directory/roleDefinitions";
	private static final String DIRECTORY_ROLES_ENDPOINT = "/directoryRoles";
	private static final String ROLE_ASSIGNMENT_SCHEDULES_ENDPOINT = "/roleManagement/directory/roleAssignmentSchedules";
	private static final String ROLE_ELIGIBILITY_SCHEDULE_REQUESTS_ENDPOINT = "/roleManagement/directory/roleEligibilityScheduleRequests";
	private static final String ADMIN_ASSIGN = "adminAssign";
	private static final String JUSTIFICATION = "Provisioning users to highly privileged roles SHALL NOT occur outside of a PAM system";
	private static final String ROOT_SCOPE = "/";
	private static final String NO_EXPIRATION = "noExpiration";
	private static final String GRANTED = "Granted";
	private static final String GLOBAL_ADMIN_ROLE_NAME = "Global Administrator";
	private static final String ACTION = "action";
	private static final String JUSTIFICATION_FIELD = "justification";
	private static final String PRINCIPAL_ID = "principalId";
	private static final String ROLE_DEFINITION_ID = "roleDefinitionId";
	private static final String DIRECTORY_SCOPE_ID = "directoryScopeId";
	private static final String SCHEDULE_INFO = "scheduleInfo";
	private static final String START_DATE_TIME = "startDateTime";
	private static final String EXPIRATION = "expiration";
	private static final String TYPE = "type";
	private static final String STATUS = "status";
	private static final String ROLE_TEMPLATE_ID = "roleTemplateId";
	private static final String SERVICE_PRINCIPAL_TYPE = "#microsoft.graph.servicePrincipal";
	private static final String SUBSCRIBED_SKUS_ENDPOINT = "/subscribedSkus";
	private static final String AAD_P2_SKU_PART_NUMBER = "AAD_PREMIUM_P2";
	private static final String ENABLED_STATUS = "Enabled";
	private static final String IN_ROLE_TEXT = " in role: ";
	

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final List<String> privilegedRoleIds;

	public DirectAssignmentRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.privilegedRoleIds = new ArrayList<>();
		// Global Admin ID - well-known role for high privilege
		this.privilegedRoleIds.add("62e90394-69f5-4237-9190-012177145e10");
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to enforce PAM for privileged role assignments");
		return IPolicyRemediator.notImplemented(getPolicyId());
		// First check if tenant has P2 license before proceeding
//		return checkTenantHasP2License()
//				.thenCompose(hasP2 -> {
//					if (Boolean.FALSE.equals(hasP2)) {
//						logger.warn("Tenant does not have AAD Premium P2 license required for PIM. Aborting remediation.");
//						return CompletableFuture.completedFuture(
//								IPolicyRemediator.failed(getPolicyId(),
//										"Tenant does not have AAD Premium P2 license required for PIM. Remediation aborted.").join());
//					}
//
//					logger.info("Tenant has required P2 license. Proceeding with remediation.");
//					return getPrivilegedRoles()
//							.thenCompose(this::processPrivilegedRoles);
//				})
//				.exceptionally(ex -> {
//					logger.error("Exception during PAM enforcement remediation", ex);
//					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
//				});
	}

	/**
	 * Gets the list of privileged role IDs to check for direct assignments.
	 * Either returns predefined list or retrieves them from the API.
	 */
	private CompletableFuture<List<String>> getPrivilegedRoles() {
		// If privileged roles are already defined, return them
		if (!privilegedRoleIds.isEmpty()) {
			return CompletableFuture.completedFuture(privilegedRoleIds);
		}

		// Otherwise, retrieve built-in roles from the API
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(ROLE_DEFINITIONS_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "isBuiltIn eq true")
						.build()
		).thenApply(response -> {
			List<String> roleIds = new ArrayList<>();
			if (response.has(Constants.VALUE_FIELD)) {
				for (JsonNode role : response.get(Constants.VALUE_FIELD)) {
					String roleId = role.get(Constants.ID_FIELD).asText();
					String displayName = role.get(Constants.DISPLAY_NAME_FIELD).asText();

					// Here we could add logic to determine which roles are considered "privileged"
					// For simplicity, we'll just add Global Administrator
					if (GLOBAL_ADMIN_ROLE_NAME.equals(displayName)) {
						roleIds.add(roleId);
					}
				}
			}
			return roleIds;
		});
	}

	/**
	 * Processes each privileged role to find and remediate direct assignments.
	 */
	private CompletableFuture<JsonNode> processPrivilegedRoles(List<String> roleIds) {
		if (roleIds.isEmpty()) {
			logger.warn("No privileged roles found to process");
			return IPolicyRemediator.failed(getPolicyId(), "No privileged roles found to process");
		}

		List<CompletableFuture<JsonNode>> roleFutures = new ArrayList<>();

		for (String roleId : roleIds) {
			CompletableFuture<JsonNode> feat = processRole(roleId);
			feat.join();
			roleFutures.add(feat);
		}

		return CompletableFuture.allOf(roleFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					boolean allSucceeded = true;
					StringBuilder messages = new StringBuilder("Results: ");

					for (CompletableFuture<JsonNode> future : roleFutures) {
						JsonNode result = future.join();
						if (result.has(STATUS) && "failed".equals(result.get(STATUS).asText())) {
							allSucceeded = false;
							messages.append("[").append(result.get(MESSAGE_FIELD).asText()).append("] ");
						} else if (result.has(MESSAGE_FIELD)) {
							messages.append("[").append(result.get(MESSAGE_FIELD).asText()).append("] ");
						}
					}

					if (allSucceeded) {
						return IPolicyRemediator.success(getPolicyId(), messages.toString()).join();
					} else {
						return IPolicyRemediator.failed(getPolicyId(), messages.toString()).join();
					}
				});
	}

	/**
	 * Processes a single role to find and remediate direct assignments.
	 */
	private CompletableFuture<JsonNode> processRole(String roleId) {
		logger.info("Processing role: {}", roleId);
		return getDirectAssignedMembers(roleId)
				.thenCompose(members -> convertDirectAssignmentsToPIM(roleId, members));
	}

	/**
	 * Gets directly assigned members (not through PIM) for a role.
	 */
	private CompletableFuture<List<Member>> getDirectAssignedMembers(String roleId) {
		return getDirectoryRoleId(roleId)
				.thenCompose(directoryRoleId -> {
					if (directoryRoleId == null) {
						// No active directory role means no direct assignments
						logger.info("No active directory role found for role template: {}", roleId);
						return CompletableFuture.completedFuture(new ArrayList<>());
					}

					return graphClient.makeGraphRequest(
							GraphRequest.builder()
									.withEndpoint(DIRECTORY_ROLES_ENDPOINT + "/" + directoryRoleId + "/members")
									.build()
					).thenCompose(members -> filterDirectAssignments(roleId, members));
				});
	}

	/**
	 * Gets the directory role ID from a role template ID.
	 */
	private CompletableFuture<String> getDirectoryRoleId(String roleTemplateId) {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(DIRECTORY_ROLES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, ROLE_TEMPLATE_ID + " eq '" + roleTemplateId + "'")
						.build()
		).thenApply(response -> {
			if (response.has(Constants.VALUE_FIELD) && response.get(Constants.VALUE_FIELD).size() > 0) {
				return response.get(Constants.VALUE_FIELD).get(0).get(Constants.ID_FIELD).asText();
			}
			return null;
		});
	}

	/**
	 * Filters members to find those with direct assignments (not PIM-managed).
	 * Excludes service principals.
	 */
	private CompletableFuture<List<Member>> filterDirectAssignments(String roleId, JsonNode membersResponse) {
		List<Member> allMembers = new ArrayList<>();

		if (!membersResponse.has(Constants.VALUE_FIELD)) {
			logger.info("No members found for role: {}", roleId);
			return CompletableFuture.completedFuture(allMembers);
		}

		JsonNode members = membersResponse.get(Constants.VALUE_FIELD);
		List<CompletableFuture<Member>> memberFutures = new ArrayList<>();

		for (JsonNode member : members) {
			String principalId = member.get(Constants.ID_FIELD).asText();
			String principalType = member.path(EntraIDConstants.ODATA_TYPE).asText();
			String displayName = member.path(EntraIDConstants.DISPLAY_NAME).asText();
			// Skip service principals as requested
			if (SERVICE_PRINCIPAL_TYPE.equals(principalType)) {
				logger.info("Skipping service principal: {} in role: {}",
						principalId, roleId);
				continue;
			}

			CompletableFuture<Member> memberFuture = isPIMAssignment(roleId, principalId)
					.thenApply(isPIM -> {
						if (Boolean.FALSE.equals(isPIM)) {
							logger.info("Found direct assignment for principal: {} in role: {}",
									principalId, roleId);
							return new Member(principalId, principalType, roleId, displayName);
						}
						logger.info("Principal: {} already has PIM assignment for role: {}",
								principalId, roleId);
						return null;
					});

			memberFutures.add(memberFuture);
		}

		return CompletableFuture.allOf(memberFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<Member> directMembers = new ArrayList<>();
					for (CompletableFuture<Member> future : memberFutures) {
						Member member = future.join();
						if (member != null) {
							directMembers.add(member);
						}
					}
					return directMembers;
				});
	}

	/**
	 * Checks if a principal has a PIM assignment for a role.
	 */
	private CompletableFuture<Boolean> isPIMAssignment(String roleId, String principalId) {
		String filter = String.format(
				"principalId eq '%s' and roleDefinitionId eq '%s' and status eq '%s'",
				principalId,
				roleId,
				GRANTED
		);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(ROLE_ASSIGNMENT_SCHEDULES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, filter)
						.build()
		).thenApply(response -> {
			if (response.has(Constants.VALUE_FIELD)) {
				return response.get(Constants.VALUE_FIELD).size() > 0;
			}
			return false;
		});
	}

	/**
	 * Checks if the tenant has the AAD Premium P2 license required for PIM at the organization level.
	 */
	private CompletableFuture<Boolean> checkTenantHasP2License() {
		logger.info("Checking tenant for AAD Premium P2 license");

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(SUBSCRIBED_SKUS_ENDPOINT)
						.build()
		).thenApply(response -> {
			if (response.has(Constants.VALUE_FIELD)) {
				JsonNode skus = response.get(Constants.VALUE_FIELD);

				for (JsonNode sku : skus) {
					if (checkSKU(sku)) return true;
				}
			}

			logger.warn("AAD Premium P2 license not found or not enabled for tenant");
			return false;
		}).exceptionally(ex -> {
			logger.error("Error checking tenant P2 license", ex);
			return false;
		});
	}

	private boolean checkSKU(JsonNode sku) {
		String skuPartNumber = sku.path("skuPartNumber").asText();
		String capabilityStatus = sku.path("capabilityStatus").asText();

		if (AAD_P2_SKU_PART_NUMBER.equalsIgnoreCase(skuPartNumber) &&
				ENABLED_STATUS.equalsIgnoreCase(capabilityStatus)) {

			// Also check if it has available units
			int consumedUnits = sku.path("consumedUnits").asInt(0);
			int prepaidUnits = sku.path("prepaidUnits").path("enabled").asInt(0);

			if (prepaidUnits > consumedUnits) {
				logger.info("Tenant has AAD Premium P2 license with available units: {}/{}",
						(prepaidUnits - consumedUnits), prepaidUnits);
				return true;
			} else {
				logger.warn("Tenant has AAD Premium P2 license but all units are consumed: {}/{}",
						consumedUnits, prepaidUnits);
			}
		}
		return false;
	}

	/**
	 * Converts direct assignments to PIM-managed eligible assignments.
	 */
	private CompletableFuture<JsonNode> convertDirectAssignmentsToPIM(String roleId, List<Member> directMembers) {
		if (directMembers.isEmpty()) {
			logger.info("No direct assignments found for role: {}", roleId);
			return CompletableFuture.completedFuture(
					IPolicyRemediator.success(getPolicyId(),
							"No direct assignments found for role: " + roleId).join()
			);
		}

		logger.info("Converting {} direct assignments to PIM for role: {}",
				directMembers.size(), roleId);

		List<CompletableFuture<JsonNode>> memberFutures = new ArrayList<>();

		for (Member member : directMembers) {
			CompletableFuture<JsonNode> memberFuture = convertMemberAssignmentIfPossible(member)
					.exceptionally(ex -> {
						logger.error("Failed to convert assignment for member: {} in role: {}",
								member.principalId, member.roleId, ex);
						return IPolicyRemediator.failed(getPolicyId(),
								"Failed to convert assignment for member: " + member.principalId
										+ IN_ROLE_TEXT + member.roleId + ". Error: " + ex.getMessage()).join();
					});

			memberFutures.add(memberFuture);
		}

		return CompletableFuture.allOf(memberFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					boolean allSucceeded = true;
					StringBuilder messages = new StringBuilder();

					for (CompletableFuture<JsonNode> future : memberFutures) {
						JsonNode result = future.join();
						if (result.has(STATUS) && "failed".equals(result.get(STATUS).asText())) {
							allSucceeded = false;
							messages.append(result.get(MESSAGE_FIELD).asText()).append("; ");
						}
					}

					if (allSucceeded) {
						return IPolicyRemediator.success(getPolicyId(),
								"Successfully converted " + directMembers.size()
										+ " direct assignments to PIM for role: " + roleId).join();
					} else {
						return IPolicyRemediator.failed(getPolicyId(),
								"Failed to convert some assignments: " + messages).join();
					}
				});
	}

	/**
	 * Converts a single member assignment from direct to PIM-managed.
	 * The tenant-level P2 license has already been verified before this point.
	 */
	private CompletableFuture<JsonNode> convertMemberAssignmentIfPossible(Member member) {
		// First attempt to create PIM assignment
		logger.info("Converting direct assignment to PIM for principal: {} in role: {}",
				member.principalId, member.roleId);

		return createPIMEligibleAssignment(member)
				.thenCompose(v -> {
					// Only remove direct assignment if PIM assignment was successful
					logger.info("PIM assignment created successfully. Removing direct assignment for: {}",
							member.principalId);
					return removeDirectAssignment(member);
				})
				.thenApply(v -> IPolicyRemediator.success(getPolicyId(),
						"Successfully converted assignment for principal: " + member.principalId
								+ IN_ROLE_TEXT + member.roleId).join())
				.exceptionally(ex -> {
					logger.warn("Failed to convert assignment: {} in role: {}. Not removing direct assignment.",
							member.principalId, member.roleId, ex);
					return IPolicyRemediator.failed(getPolicyId(),
							"Failed to convert principal: " + member.principalId +
									IN_ROLE_TEXT + member.roleId + ". Error: " + ex.getMessage()).join();
				});
	}

	/**
	 * Removes a direct role assignment.
	 */
	private CompletableFuture<Void> removeDirectAssignment(Member member) {
		return getDirectoryRoleId(member.roleId)
				.thenCompose(directoryRoleId -> {
					if (directoryRoleId == null) {
						logger.warn("No directory role found for role: {}", member.roleId);
						return CompletableFuture.completedFuture(null);
					}

					logger.info("Removing direct assignment for principal: {} from role: {}",
							member.principalId, member.roleId);

					return graphClient.makeGraphRequest(
							GraphRequest.builder()
									.withMethod(HttpMethod.DELETE)
									.withEndpoint(DIRECTORY_ROLES_ENDPOINT + "/" + directoryRoleId +
											"/members/" + member.principalId + "/$ref")
									.build()
					).thenAccept(response ->
							logger.info("Successfully removed direct assignment for principal: {} from role: {}",
									member.principalId, member.roleId)
					);
				});
	}

	/**
	 * Creates a PIM-managed eligible assignment.
	 */
	private CompletableFuture<Void> createPIMEligibleAssignment(Member member) {
		logger.info("Creating PIM eligible assignment for principal: {} for role: {}",
				member.principalId, member.roleId);

		ObjectNode requestBody = objectMapper.createObjectNode();
		requestBody.put(ACTION, ADMIN_ASSIGN);
		requestBody.put(JUSTIFICATION_FIELD, JUSTIFICATION);
		requestBody.put(PRINCIPAL_ID, member.principalId);
		requestBody.put(ROLE_DEFINITION_ID, member.roleId);
		requestBody.put(DIRECTORY_SCOPE_ID, ROOT_SCOPE);

		ObjectNode scheduleInfo = requestBody.putObject(SCHEDULE_INFO);
		scheduleInfo.put(START_DATE_TIME, Instant.now().toString());

		ObjectNode expiration = scheduleInfo.putObject(EXPIRATION);
		expiration.put(TYPE, NO_EXPIRATION);

		String requestBodyStr;
		try {
			requestBodyStr = objectMapper.writeValueAsString(requestBody);
		} catch (Exception e) {
			logger.error("Failed to serialize request body", e);
			return CompletableFuture.failedFuture(
					new GraphClientException("Failed to serialize request body", e));
		}

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withMethod(HttpMethod.POST)
						.withEndpoint(ROLE_ELIGIBILITY_SCHEDULE_REQUESTS_ENDPOINT)
						.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
						.withBody(HttpRequest.BodyPublishers.ofString(requestBodyStr))
						.build()
		).thenAccept(response -> {
			// Validate the response
			if (response.has(STATUS)) {
				String status = response.get(STATUS).asText();
				if (!"PendingApproval".equals(status) && !"Provisioned".equals(status)) {
					throw new GraphClientException("Failed to create PIM eligible assignment. Status: " + status);
				}
			} else {
				throw new GraphClientException("Invalid response when creating PIM eligible assignment");
			}

			logger.info("Successfully created PIM eligible assignment for principal: {} for role: {}",
					member.principalId, member.roleId);
		});
	}

	/**
	 * Represents a member with a direct role assignment.
	 */
		private record Member(String principalId, String principalType, String roleId, String displayName) {
	}
}