package io.syrix.products.microsoft.teams;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.storage.Storage;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.main.Context;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.service.ServiceTypeRemediationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

public class TeamsRemediationService implements ServiceTypeRemediationService {
	private static final Logger logger = LoggerFactory.getLogger(TeamsRemediationService.class);
	private final Storage storage;
	private final ObjectMapper mapper;
	private final Context context;

	public TeamsRemediationService(Context context, Storage storage) {
		this.context = context;
		this.storage = storage;
		this.mapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	public CompletableFuture<JsonNode> remediate(RemediationTask task) {
		try {
			JsonNode jsonConfig = loadConfig(task);

			List<PolicyChangeResult> result = new ArrayList<>();
			try (PowerShellTeamsClient client = initClient()) {
				List<IPolicyRemediator> iPolicyRemediators = makeRemediators(task, jsonConfig, client);
				int ix = 0;
				for (IPolicyRemediator remediator : iPolicyRemediators) {
					PolicyChangeResult changeResult = remediator.remediate_().join();
					changeResult.executeOrder(ix++);
					result.add(changeResult);
				}
			}
			return CompletableFuture.completedFuture(mapper.valueToTree(result));
		} catch (IOException e) {
			return CompletableFuture.failedFuture(e);
		}
	}

	private List<IPolicyRemediator> makeRemediators(RemediationTask task, JsonNode jsonConfig, PowerShellTeamsClient client) throws IOException {
		TeamsRemediationConfig remediationConfig = task.getTeamsRemediationTask().getRemediationConfig();
		List<String> policyIds = task.getTeamsRemediationTask().getPolicyIds();

		return policyIds.stream()
				.map(policyId -> (IPolicyRemediator)PolicyRemediatorRegistry.getPolicyRemediator(policyId, client, jsonConfig, remediationConfig))
				.toList();
	}

	private JsonNode loadConfig(Task task) throws IOException {
		Path configPath = storage.loadConfigPath(task);
		return mapper.readTree(configPath.toFile());
	}

	private PowerShellTeamsClient initClient() {
		String tenantId;
		try (MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context)) {
			tenantId = graphClient.getTenantId();
		}

		if (tenantId == null) {
			logger.error("Tenant ID not initialized");
			throw new SyrixRuntimeException("Tenant ID not initialized");
		}

		return ClientFactory.initPowerShellTeamsClient(context, tenantId);
	}
}
