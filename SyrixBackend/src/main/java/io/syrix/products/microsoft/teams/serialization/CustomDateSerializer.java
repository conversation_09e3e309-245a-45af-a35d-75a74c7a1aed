package io.syrix.products.microsoft.teams.serialization;

import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.OffsetDateTime;

public class CustomDateSerializer extends JsonSerializer<OffsetDateTime> {
    @Override
    public void serialize(OffsetDateTime value, com.fasterxml.jackson.core.JsonGenerator gen, SerializerProvider serializers) throws IOException {
        long milliseconds = value.toInstant().toEpochMilli();
        String res = String.format("\\/Date(%s)\\/", milliseconds);
        gen.writeString(res);
    }
}