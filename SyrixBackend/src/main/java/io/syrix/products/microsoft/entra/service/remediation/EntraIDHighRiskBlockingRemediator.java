package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.CA_POLICIES_ENDPOINT;

/**
 * Implements remediation for MS.AAD.2.1v1: Block access for high-risk users.
 *
 * This remediation creates a Conditional Access Policy that blocks access
 * when user risk level is high.
 */
@PolicyRemediator("MS.AAD.2.1v1")
public class EntraIDHighRiskBlockingRemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Block high-risk users ";

	/**
	 * Constructor with custom user configuration and policy state.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 * @param policyState The state of the policy
	 */
	public EntraIDHighRiskBlockingRemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);
	}

	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Get the conditions object and add user risk levels
		ObjectNode conditions = (ObjectNode) policyPayload.get("conditions");

		// User risk levels
		ArrayNode userRiskLevels = objectMapper.createArrayNode();
		userRiskLevels.add("high");
		conditions.set("userRiskLevels", userRiskLevels);

		// Grant controls with block action
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "OR");

		// Configure to block access
		ArrayNode builtInControls = objectMapper.createArrayNode();
		builtInControls.add("block");
		grantControls.set("builtInControls", builtInControls);

		// Empty arrays for other controls
		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}

	/**
	 * Checks if a high-risk users blocking policy exists and is properly configured.
	 */
	private CompletableFuture<JsonNode> fetchPolicies() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint(CA_POLICIES_ENDPOINT)
				.withMethod(HttpMethod.GET)
				.build());
	}

	private boolean isPolicyConfiguredCorrectly(JsonNode policy) {
		JsonNode conditions = policy.path("conditions");
		boolean hasHighRiskLevel = conditions.path("userRiskLevels").toString().contains("high");
		boolean hasCorrectControls = policy.path("grantControls").path("builtInControls").toString().contains("block");

		if (hasHighRiskLevel && hasCorrectControls) {
			logger.info("High-risk users blocking policy is configured correctly");
			return true;
		}
		logger.info("High-risk users blocking policy is not configured correctly");
		return false;
	}

	private boolean findAndCheckPolicy(JsonNode response) {
		if (!response.has(VALUE_FIELD) || !response.get(VALUE_FIELD).isArray()) {
			logger.warn("No policies found in response");
			return false;
		}

		return StreamSupport.stream(response.get(VALUE_FIELD).spliterator(), false)
				.filter(policy -> policyName.equals(policy.path("displayName").asText()))
				.findFirst()
				.map(this::isPolicyConfiguredCorrectly)
				.orElseGet(() -> {
					logger.warn("No high-risk users blocking policy found");
					return false;
				});
	}

	public CompletableFuture<Boolean> checkHighRiskBlockingPolicy() {
		return fetchPolicies()
				.thenApply(this::findAndCheckPolicy)
				.exceptionally(e -> {
					logger.error("Failed to check high-risk users blocking policy: {}", e.getMessage(), e);
					return false;
				});
	}
}