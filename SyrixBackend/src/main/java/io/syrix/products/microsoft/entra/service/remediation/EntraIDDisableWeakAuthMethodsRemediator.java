package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.entra.service.EntraIDConstants.AUTH_METHODS_POLICY_ENDPOINT;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.ODATA_TYPE;

/**
 * Implements remediation for MS.AAD.3.5v1: Disable weak authentication methods (SMS, Voice, Email OTP).
 */
@PolicyRemediator("MS.AAD.3.5v1")
public class EntraIDDisableWeakAuthMethodsRemediator extends RemediatorBase {
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDDisableWeakAuthMethodsRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to disable weak authentication methods");

		return checkCurrentAuthMethodStates()
				.thenCompose(this::disableWeakAuthMethods)
				.exceptionally(ex -> {
					logger.error("Exception while disabling weak authentication methods", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> checkCurrentAuthMethodStates() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.v1()
				.withEndpoint(AUTH_METHODS_POLICY_ENDPOINT)
				.withMethod(HttpMethod.GET)
				.build());
	}

	private CompletableFuture<JsonNode> disableWeakAuthMethods(JsonNode currentConfig) {
		ObjectNode policyPayload = createPolicyPayload();

		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(AUTH_METHODS_POLICY_ENDPOINT)
							.build()
			).thenCompose(policyResult -> {
				// PATCH returns 204 No Content on success
				if (policyResult == null || !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Successfully disabled weak authentication methods");
					return IPolicyRemediator.success(getPolicyId(),
							"SMS, Voice Call, and Email OTP authentication methods have been disabled");
				} else {
					String error = policyResult.has(Constants.ERROR_FIELD) ?
							policyResult.get(Constants.ERROR_FIELD).asText() : "Unknown error";
					logger.error("Failed to disable weak authentication methods: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to disable weak authentication methods", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		ArrayNode authMethodConfigs = objectMapper.createArrayNode();

		// Configure SMS authentication
		ObjectNode smsConfig = objectMapper.createObjectNode();
		smsConfig.put(ODATA_TYPE, "#microsoft.graph.smsAuthenticationMethodConfiguration");
		smsConfig.put("id", "Sms");
		smsConfig.put(Constants.STATE, Constants.DISABLED_VALUE);
		authMethodConfigs.add(smsConfig);

		// Configure Voice authentication
		ObjectNode voiceConfig = objectMapper.createObjectNode();
		voiceConfig.put(ODATA_TYPE, "#microsoft.graph.voiceAuthenticationMethodConfiguration");
		voiceConfig.put("id", "Voice");
		voiceConfig.put(Constants.STATE, Constants.DISABLED_VALUE);
		authMethodConfigs.add(voiceConfig);

		// Configure Email OTP authentication
		ObjectNode emailConfig = objectMapper.createObjectNode();
		emailConfig.put(ODATA_TYPE, "#microsoft.graph.emailAuthenticationMethodConfiguration");
		emailConfig.put("id", "Email");
		emailConfig.put(Constants.STATE, Constants.DISABLED_VALUE);
		authMethodConfigs.add(emailConfig);

		policyPayload.set("authenticationMethodConfigurations", authMethodConfigs);
		return policyPayload;
	}

	/**
	 * Checks if weak authentication methods are properly disabled.
	 */
	public CompletableFuture<Boolean> checkWeakAuthMethodsDisabled() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.v1()
						.withEndpoint(AUTH_METHODS_POLICY_ENDPOINT)
						.withMethod(HttpMethod.GET)
						.build())
				.thenApply(response -> {
					JsonNode configs = response.path("authenticationMethodConfigurations");
					boolean smsDisabled = isMethodDisabled(configs, "Sms");
					boolean voiceDisabled = isMethodDisabled(configs, "Voice");
					boolean emailDisabled = isMethodDisabled(configs, "Email");

					boolean allDisabled = smsDisabled && voiceDisabled && emailDisabled;
					logger.info("Weak authentication methods are {} disabled",
							allDisabled ? "all" : "not all");
					return allDisabled;
				})
				.exceptionally(e -> {
					logger.error("Failed to check weak authentication methods state: {}", e.getMessage(), e);
					return false;
				});
	}

	private boolean isMethodDisabled(JsonNode configs, String methodId) {
		if (configs.isArray()) {
			for (JsonNode config : configs) {
				if (methodId.equals(config.path("id").asText()) &&
						Constants.DISABLED_VALUE.equals(config.path(Constants.STATE).asText())) {
					return true;
				}
			}
		}
		return false;
	}
}