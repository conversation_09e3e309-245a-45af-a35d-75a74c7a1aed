package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.products.microsoft.exo.remediation.context.SharingPolicy;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

//Base class for ExchangeContactsSharingPolicyRemediator and ExchangeCalendarSharingPolicyRemediator
public abstract class ExchangeSharingPolicyRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	public static final String NO_SHARING_POLICIES_FOUND = "No sharing policies found";

	public ExchangeSharingPolicyRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.debug("Starting remediation for {} (Sharing Policy)", getPolicyId());

		List<SharingPolicy> sharingPolicies = remediationContext.getSharingPolicies();
		if (sharingPolicies.isEmpty()) {
			logger.warn(ExoConstants.ERROR_NO_SHARING_POLICIES_FOUND);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_SHARING_POLICIES_FOUND));
		}

		List<CompletableFuture<PolicyChangeResult>> results = sharingPolicies.stream()
				.filter(this::isDataAllowedAllDomains)
				.map(this::updateSharingPolicy)
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.debug("Starting rollback for {} (Sharing Policy)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
					continue;
				}

				String[] parts = change.getParameter().split(":");
				String identity = parts[1].split(",")[0];
				@SuppressWarnings("unchecked")
				List<String> prevValue = (List<String>) change.getPrevValue();
				@SuppressWarnings("unchecked")
				List<String> newValue = (List<String>) change.getNewValue();

				results.add(updateDomains(identity, prevValue, newValue));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	protected abstract boolean isDataAllowedAllDomains(String domain);

	private boolean isDataAllowedAllDomains(SharingPolicy sharingPolicy) {
		return sharingPolicy.domains.stream().anyMatch(this::isDataAllowedAllDomains);
	}


	private CompletableFuture<PolicyChangeResult> updateSharingPolicy(SharingPolicy sharingPolicy) {
		List<String> newValue =
				sharingPolicy.domains.stream()
				.filter(domain -> !isDataAllowedAllDomains(domain))
				.toList();

		List<String> prevValue = sharingPolicy.domains;
		return updateDomains(sharingPolicy.identity, newValue, prevValue)
				.thenApply(result -> {
					//update current sharing policy
					sharingPolicy.domains = new ArrayList<>(newValue);
					return result;
				});
	}

	private CompletableFuture<PolicyChangeResult> updateDomains(String identity, List<String> newDomains, List<String> oldDomains) {
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.IDENTITY + ":" + identity + ",params:" + ExoConstants.PARAM_DOMAINS)
				.prevValue(oldDomains)
				.newValue(newDomains);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Set-SharingPolicy",
								Map.of(Constants.IDENTITY_FIELD, identity,
										"Domains", newDomains
								)
						))
				.thenApply(result -> handleDomainUpdateResult(result, identity, paramChange))
				.exceptionally(ex -> {
							logger.error("Error:", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage());
						}
				);
	}


	private PolicyChangeResult handleDomainUpdateResult(JsonNode result, String identity, ParameterChangeResult changeResult) {
		if (result != null && !result.has(Constants.ERROR_FIELD)) {
			logger.trace("Successfully update domains for Sharing Policy : {}", identity);
			changeResult.status(ParameterChangeStatus.SUCCESS);
			return IPolicyRemediator.success_(getPolicyId(), "Successfully disabled automatic forwarding for identity", List.of(changeResult));
		} else {
			String error = result != null && result.has(Constants.ERROR_FIELD) ?
					result.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
			logger.error("Failed to update domains for Sharing Policy for identity {}: {}", "", error);
			changeResult.status(ParameterChangeStatus.FAILED);
			return IPolicyRemediator.failed_(getPolicyId(), "Failed to update domains for Sharing Policy for identity: " + identity, List.of(changeResult));
		}
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "Automatic email forwarding disabled for all " + successCount + " remote domains", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to disable automatic forwarding for all " + failedCount + " domains: ", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " disable automatic forwarding, failed to fix " + failedCount + " domains",
								allChanges);
					}
				});
	}
}