package io.syrix.products.microsoft.powerplatform;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.products.microsoft.base.BaseConfigurationService;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing and exporting Microsoft Power Platform configurations.
 * Provides functionality for retrieving environment settings, DLP policies,
 * and tenant isolation configurations.
 */
public class PowerPlatformConfigurationService extends BaseConfigurationService {

	private static final String SERVICE_VERSION = "1.0";
	private final PowerPlatformClient ppClient;

	public PowerPlatformConfigurationService(MicrosoftGraphClient graphClient, PowerPlatformClient ppClient) {
		this(graphClient, ppClient, new ObjectMapper(), new MetricsCollector());
	}

	public PowerPlatformConfigurationService(
			MicrosoftGraphClient graphClient,
			PowerPlatformClient ppClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.ppClient = ppClient;
	}

	@Override
	public ConfigurationResult exportConfiguration() {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting Power Platform configuration export at {}", startTime);

		try {
			// Get tenant details from Graph API first
			CompletableFuture<JsonNode> tenantDetailsFuture = withRetry(() ->
					graphClient.makeGraphRequest(GraphRequest.builder()
							.withEndpoint("/organization")
							.withMethod(HttpMethod.GET)
							.build()), ""
			);

			// Create futures for Power Platform configurations
			Map<String, CompletableFuture<?>> futures = new HashMap<>();
			futures.put("tenant_details", tenantDetailsFuture);
			futures.put("environment_creation", getEnvironmentCreationSettings());
			futures.put("dlp_policies", getDLPPolicies());
			futures.put("tenant_isolation", getTenantIsolationSettings());
			futures.put("environment_list", getEnvironments());

			// Wait for all futures to complete with timeout
			ConfigurationResult result = waitForFutures(futures)
					.thenApply(results -> buildConfigurationResult(results,
							SERVICE_VERSION,
							ConfigurationServiceType.POWER_PLATFORM))
					.get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordExportSuccess(duration);
			logger.info("Power Platform configuration export completed in {} seconds", duration.toSeconds());

			return result;

		} catch (Exception e) {
			metrics.recordExportFailure();
			logger.error("Power Platform configuration export failed", e);
			throw new ConfigurationExportException("Power Platform configuration export failed", e);
		}
	}

	private CompletableFuture<JsonNode> getEnvironmentCreationSettings() {
		return withRetry(ppClient::getEnvironmentCreationSettings,"")
				.exceptionally(e -> {
					logger.error("Failed to get environment creation settings", e);
					throw new ConfigurationExportException("Failed to get environment creation settings", e);
				});
	}

	private CompletableFuture<JsonNode> getDLPPolicies() {
		return withRetry(ppClient::getDlpPolicies,"")
				.exceptionally(e -> {
					logger.error("Failed to get DLP policies", e);
					throw new ConfigurationExportException("Failed to get DLP policies", e);
				});
	}

	private CompletableFuture<JsonNode> getTenantIsolationSettings() {
		return withRetry(ppClient::getTenantIsolationSettings,"")
				.exceptionally(e -> {
					logger.error("Failed to get tenant isolation settings", e);
					throw new ConfigurationExportException("Failed to get tenant isolation settings", e);
				});
	}

	private CompletableFuture<JsonNode> getEnvironments() {
		return withRetry(ppClient::getEnvironments,"")
				.exceptionally(e -> {
					logger.error("Failed to get environments", e);
					throw new ConfigurationExportException("Failed to get environments", e);
				});
	}

	@Override
	public void close() {
		try {
			super.close();
		} finally {
			if (ppClient != null) {
				ppClient.close();
			}
		}
	}

	/**
	 * Builder for PowerPlatformConfigurationService
	 */
	public static class Builder {
		private MicrosoftGraphClient graphClient;
		private PowerPlatformClient ppClient;
		private ObjectMapper objectMapper = new ObjectMapper();
		private MetricsCollector metrics = new MetricsCollector();

		public Builder withGraphClient(MicrosoftGraphClient graphClient) {
			this.graphClient = graphClient;
			return this;
		}

		public Builder withPowerPlatformClient(PowerPlatformClient ppClient) {
			this.ppClient = ppClient;
			return this;
		}

		public Builder withObjectMapper(ObjectMapper objectMapper) {
			this.objectMapper = objectMapper;
			return this;
		}

		public Builder withMetricsCollector(MetricsCollector metrics) {
			this.metrics = metrics;
			return this;
		}

		public PowerPlatformConfigurationService build() {
			if (graphClient == null) {
				throw new IllegalStateException("Graph client must be provided");
			}
			if (ppClient == null) {
				throw new IllegalStateException("Power Platform client must be provided");
			}
			return new PowerPlatformConfigurationService(graphClient, ppClient, objectMapper, metrics);
		}
	}
}