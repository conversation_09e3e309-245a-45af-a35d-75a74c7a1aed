package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.products.microsoft.entra.model.EntraIDAuthenticationStrength;
import io.syrix.protocols.client.MicrosoftGraphClient;

import java.util.Arrays;
import java.util.List;

/**
 * Implements remediation for MS.AAD.3.6v1: Phishing-resistant MFA SHALL be required for highly privileged roles.

 * This remediation creates a Conditional Access Policy that requires phishing-resistant
 * authentication methods for highly privileged roles.
 */
@PolicyRemediator("MS.AAD.3.6v1")
public class EntraIDPrivilegedMfaRemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Privileged Roles Phishing-Resistant MFA";

	// List of highly privileged roles to protect
	private static final List<String> PRIVILEGED_ROLES = Arrays.asList(
			"62e90394-69f5-4237-9190-012177145e10", // Global Administrator
			"194ae4cb-b126-40b2-bd5b-6091b380977d", // Security Administrator
			"f28a1f50-f6e7-4571-818b-6a12f2af6b6c", // SharePoint Administrator
			"729827e3-9c14-49f7-bb1b-9608f156bbb8", // Helpdesk Administrator
			"b1be1c3e-b65d-4f19-8427-f6fa0d97feb9", // Conditional Access Administrator
			"c4e39bd9-1100-46d3-8c65-fb160da0071f", // Authentication Administrator
			"9b895d92-2cd3-44c7-9d02-a6ac2d5ea5c3", // Application Administrator
			"158c047a-c907-4556-b7ef-446551a6b5f7", // Cloud Application Administrator
			"966707d0-3269-4727-9be2-8c3a10f19b9d", // Password Administrator
			"7be44c8a-adaf-4e2a-84d6-ab2649e08a13"  // Privileged Authentication Administrator
	);

	private final EntraIDAuthenticationStrength authenticationStrength;

	/**
	 * Constructor with custom authentication strength and policy state.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 * @param authenticationStrength The authentication strength to enforce
	 * @param policyState The state of the policy
	 */
	public EntraIDPrivilegedMfaRemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);
		this.authenticationStrength = EntraIDAuthenticationStrength.PHISHING_RESISTANT_MFA;
		addRolesToConfig(userConfiguration);
	}

	/**
	 * Creates a user configuration that targets highly privileged roles.
	 *
	 * @return ConditionalAccessUserConfiguration configured for privileged roles
	 */
	private static void addRolesToConfig(ConditionalAccessUserConfiguration userConfig) {
		for (String roleId : PRIVILEGED_ROLES) {
			userConfig.withIncludedRoles(roleId);
		}
	}

	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Grant controls with phishing-resistant MFA
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "OR");

		// Empty arrays for other controls
		grantControls.set("builtInControls", objectMapper.createArrayNode());
		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		// Authentication strength reference
		ObjectNode authStrength = objectMapper.createObjectNode();
		authStrength.put("id", authenticationStrength.getId());
		grantControls.set("authenticationStrength", authStrength);

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}
}