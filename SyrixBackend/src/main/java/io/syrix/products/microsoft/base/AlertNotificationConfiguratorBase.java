package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.PowerShellClient;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Base class for configuring alert notifications in Microsoft 365.
 * <p>
 * This class configures notification recipients for the following required alerts:
 * a. Suspicious email sending patterns detected
 * b. Suspicious Connector Activity
 * c. Suspicious Email Forwarding Activity
 * d. Messages have been delayed
 * e. <PERSON>ant restricted from sending unprovisioned email
 * f. Tenant restricted from sending email
 * g. A potentially malicious URL click was detected

 * The alerts SHOULD be sent to a monitored address or incorporated into a Security
 * Information and Event Management (SIEM).

 * Rationale: Suspicious or malicious events, if not resolved promptly, may have a greater
 * impact to users and the agency. Sending alerts to a monitored email address or SIEM system
 * helps ensure events are acted upon in a timely manner to limit overall impact.

 * MITRE ATT&CK TTP Mapping:
 * - T1562: Impair Defenses
 *   - T1562.006: Indicator Blocking
 */
public abstract class AlertNotificationConfiguratorBase extends MicrosoftAlertBaseRemediator {

	// Custom suffix for alert notification policies
	protected static final String NOTIFICATION_SUFFIX = "-NOTIFY";

	protected final List<String> notificationRecipients;

	/**
	 * Constructs a new AlertNotificationConfiguratorBase with the specified PowerShell client
	 * and notification recipient.
	 *
	 * @param powershellClient The PowerShell client
	 * @param notificationRecipients The email address(es) to notify for alerts
	 * @param tenantId The tenant ID to use for hash suffix generation
	 */
	public AlertNotificationConfiguratorBase(PowerShellClient powershellClient,
											 List<String> notificationRecipients,
											 String tenantId,
											 List<String> additionalAlerts) {
		super(powershellClient, tenantId, additionalAlerts);
		this.notificationRecipients = notificationRecipients;
	}

	@Override
	protected boolean checkPreconditions() {
		if (CollectionUtils.isEmpty(notificationRecipients)) {
			logger.error("Notification recipients not specified");
			return false;
		}
		return true;
	}

	/**
	 * Checks if an alert is already properly configured with notifications enabled
	 * and the correct recipients.
	 *
	 * @param alert The alert to check
	 * @param allAlerts All existing alerts (to check for custom versions)
	 * @return true if the alert is already properly configured
	 */
	protected boolean isAlertProperlyConfigured(JsonNode alert, JsonNode allAlerts) {
		// First check if the original alert already has correct settings
		if (alert.path(NOTIFICATION_ENABLED).asBoolean(false) && hasCorrectNotificationRecipients(alert)) {
			return true;
		}

		// Then check if there's a custom version with correct settings
		JsonNode existingCustom = findExistingCustomAlert(allAlerts, alert, tenantHashSuffix);
		if (existingCustom != null) {
			return existingCustom.path(NOTIFICATION_ENABLED).asBoolean(false) &&
					hasCorrectNotificationRecipients(existingCustom);
		}

		return false;
	}

	/**
	 * Checks if an alert has the correct notification recipients.
	 *
	 * @param alert The alert to check
	 * @return true if the alert contains all required notification recipients
	 */
	protected boolean hasCorrectNotificationRecipients(JsonNode alert) {
		JsonNode notifyUsers = alert.path(NOTIFY_USER);

		// If there are no notification users in the alert, it's incorrect
		if (notifyUsers.isMissingNode() || notifyUsers.isNull()) {
			return false;
		}

		// Handle both array and single string cases
		List<String> existingRecipients = new ArrayList<>();

		if (notifyUsers.isArray()) {
			notifyUsers.forEach(user -> {
				if (user.isTextual()) {
					existingRecipients.add(user.asText().toLowerCase());
				}
			});
		} else if (notifyUsers.isTextual()) {
			existingRecipients.add(notifyUsers.asText().toLowerCase());
		}

		// Check if all required recipients are included
		for (String recipient : notificationRecipients) {
			if (!existingRecipients.contains(recipient.toLowerCase())) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Creates new alerts from YAML definitions with the configured notification recipients.
	 * This is used for alerts that were not found in the existing alerts but are required.
	 *
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> createNewAlerts(Map<String, AlertDefinition> alertsDefintion) {
		if (newAlerts.isEmpty()) {
			logger.info("No new alerts to create");
			return CompletableFuture.completedFuture(createSuccessNode("No new alerts to create"));
		}

		logger.info("Creating {} new alerts with notifications: {}", newAlerts.size(), newAlerts);

		// Use the notification recipients configured for this class
		return createAlertsFromDefinitions(alertsDefintion, newAlerts, notificationRecipients);
	}

	/**
	 * Configure notifications for required alerts by creating duplicates with custom notification settings.
	 */
	protected CompletableFuture<JsonNode> configureAlerts(JsonNode allAlerts, Map<String, AlertDefinition> alertsDefintion) {
		if (!isValidAlertResponse(allAlerts)) {
			return CompletableFuture.completedFuture(
					createFailureNode("No protection alerts found.")
			);
		}

		// Lists to track existing alerts and alert statuses
		List<String> existingAlertNames = new ArrayList<>();
		List<JsonNode> requiredAlerts = identifyRequiredAlerts(allAlerts, existingAlertNames);

		if (requiredAlerts.isEmpty()) {
			return CompletableFuture.completedFuture(
					createFailureNode("No matching built-in alert policies found.")
			);
		}

		// Setup the parameters with notification recipients
		Map<String, Object> customParams = new HashMap<>();
		customParams.put(NOTIFY_USER, notificationRecipients);
		customParams.put(NOTIFICATION_ENABLED, true);

		// Create a notification configuration checker
		AlertConfigChecker notificationChecker = new AlertConfigChecker() {
			@Override
			public boolean isAlertProperlyConfigured(JsonNode alert, JsonNode alerts) {
				// First check if the original alert already has correct settings
				if (alert.path(NOTIFICATION_ENABLED).asBoolean(false) && hasCorrectNotificationRecipients(alert)) {
					return true;
				}

				// Then check if there's a custom version with correct settings (if alerts are provided)
				if (alerts != null) {
					JsonNode existingCustom = findExistingCustomAlert(alerts, alert, tenantHashSuffix);
					if (existingCustom != null) {
						return existingCustom.path(NOTIFICATION_ENABLED).asBoolean(false) &&
								hasCorrectNotificationRecipients(existingCustom);
					}
				}

				return false;
			}
		};

		// Use the common method to process the alerts
		return processAlerts(
				alertsDefintion,
				requiredAlerts,
				allAlerts,
				customParams,
				notificationChecker,
				"Successfully created custom versions of all required alerts with appropriate notification recipients",
				"Failed to create custom versions for some alerts: ",
				"All required alerts were already properly configured with appropriate notification recipients"
		);
	}

	/**
	 * Duplicate alerts with custom notification recipients.
	 */
	private CompletableFuture<JsonNode> duplicateAlertsWithNotifications(List<JsonNode> alertsToDuplicate, JsonNode allAlerts) {
		List<CompletableFuture<JsonNode>> duplicationTasks = new ArrayList<>();
		List<String> processedAlerts = new ArrayList<>();
		List<String> failedAlerts = new ArrayList<>();
		List<String> skippedAlerts = new ArrayList<>();

		// First, deduplicate alerts by their name to ensure we don't process the same alert twice
		Map<String, JsonNode> uniqueAlerts = new HashMap<>();
		for (JsonNode alert : alertsToDuplicate) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			if (!uniqueAlerts.containsKey(alertName)) {
				uniqueAlerts.put(alertName, alert);
			}
		}


		logger.info("Processing {} unique alerts after deduplication", uniqueAlerts.size());

		for (JsonNode alert : uniqueAlerts.values()) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			// Check if alert is already properly configured
			if (isAlertProperlyConfigured(alert, allAlerts)) {
				logger.info("Alert '{}' is already enabled with correct notification recipients", alertName);
				skippedAlerts.add(alertName);
				processedAlerts.add(alertName);
				continue;
			}

			// Check if there's an existing custom version with the tenant suffix
			JsonNode existingCustomAlert = findExistingCustomAlert(allAlerts, alert, tenantHashSuffix);
			if (existingCustomAlert != null) {
				String existingAlertName = existingCustomAlert.path(Constants.NAME_FIELD).asText();

				// Check if existing custom alert already has correct settings
				if (hasCorrectNotificationRecipients(existingCustomAlert) &&
						existingCustomAlert.path(NOTIFICATION_ENABLED).asBoolean(false)) {
					logger.info("Existing custom alert '{}' already has correct notification settings", existingAlertName);
					processedAlerts.add(alertName);
					continue;
				}

				// TODO: If needed, update existing alert settings
				// For now, we'll log it and consider it processed
				logger.info("Found existing custom alert '{}' but notification settings need updating", existingAlertName);
				processedAlerts.add(alertName);
				continue;
			}

			// Setup the parameters with notification recipients
			Map<String, Object> customParams = new HashMap<>();
			customParams.put(NOTIFY_USER, notificationRecipients);
			customParams.put(NOTIFICATION_ENABLED, true);


			duplicationTasks.add(duplicateOrUpdateAlert(alert, allAlerts, tenantHashSuffix, customParams)
					.thenApply(result -> {
						if (result != null && !result.has(Constants.ERROR_FIELD)) {
							processedAlerts.add(alertName);
						} else {
							failedAlerts.add(alertName);
						}
						return result;
					}));
		}

		if (duplicationTasks.isEmpty()) {
			if (!skippedAlerts.isEmpty()) {
				logger.info("All required alerts are already correctly configured or have custom versions");
				return CompletableFuture.completedFuture(
						createSuccessNode("All required alerts are already correctly configured or have custom versions")
				);
			} else {
				logger.info("All required alerts already have custom versions with the correct notification configuration");
				return CompletableFuture.completedFuture(
						createSuccessNode("All required alerts already have custom versions with the correct notification configuration")
				);
			}
		}

		return CompletableFuture.allOf(duplicationTasks.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					if (failedAlerts.isEmpty()) {
						if (skippedAlerts.size() == processedAlerts.size()) {
							// All alerts were skipped because they were already properly configured
							return createSuccessNode("All required alerts were already properly configured with appropriate notification recipients");
						} else if (!skippedAlerts.isEmpty()) {
							// Some alerts were skipped, some were created
							return createSuccessNode("Successfully configured all required alerts with appropriate notification recipients. Some alerts were already correctly configured.");
						} else {
							// All alerts were newly configured
							return createSuccessNode("Successfully created custom versions of all required alerts with appropriate notification recipients");
						}
					} else {
						StringBuilder errorMessage = new StringBuilder("Failed to create custom versions for some alerts: ");
						for (String failed : failedAlerts) {
							errorMessage.append(failed).append(", ");
						}
						// If we've processed some alerts successfully but not all
						if (!processedAlerts.isEmpty()) {
							return createPartialSuccessNode(errorMessage.toString());
						} else {
							return createFailureNode(errorMessage.toString());
						}
					}
				});
	}
}