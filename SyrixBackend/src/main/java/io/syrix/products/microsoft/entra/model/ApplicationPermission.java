package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApplicationPermission extends Permission {
	@JsonProperty("permissionType")
	private String permissionType;

	@JsonProperty("appRoleId")
	private String appRoleId;

	@JsonProperty("appRoleDisplayName")
	private String appRoleDisplayName;

	@JsonProperty("appRoleDescription")
	private String appRoleDescription;

	@JsonProperty("appRoleValue")
	private String appRoleValue;

	public String getPermissionType() {
		return permissionType;
	}

	public void setPermissionType(String permissionType) {
		this.permissionType = permissionType;
	}

	public String getAppRoleId() {
		return appRoleId;
	}

	public void setAppRoleId(String appRoleId) {
		this.appRoleId = appRoleId;
	}

	public String getAppRoleDisplayName() {
		return appRoleDisplayName;
	}

	public void setAppRoleDisplayName(String appRoleDisplayName) {
		this.appRoleDisplayName = appRoleDisplayName;
	}

	public String getAppRoleDescription() {
		return appRoleDescription;
	}

	public void setAppRoleDescription(String appRoleDescription) {
		this.appRoleDescription = appRoleDescription;
	}

	public String getAppRoleValue() {
		return appRoleValue;
	}

	public void setAppRoleValue(String appRoleValue) {
		this.appRoleValue = appRoleValue;
	}

	@Override
	public String toString() {
		return String.format("""
            Application Permission:
                Resource: %s (%s)
                Type: %s
                Role: %s (%s)
                Description: %s
                Value: %s
                Consent Type: %s""",
				resourceDisplayName,
				resourceId,
				permissionType,
				appRoleDisplayName,
				appRoleId,
				appRoleDescription,
				appRoleValue,
				consentType);
	}
}