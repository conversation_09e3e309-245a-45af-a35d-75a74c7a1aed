package io.syrix.products.microsoft.exo.remediation.malwareFilter;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Base class for malware filter policy remediators.
 * Provides common functionality for policies that configure malware filtering settings.
 */
public abstract class MalwareFilterRemediatorBase extends RemediatorBase {
	public static final String IDENTITY = "Identity";
	public static final String UNKNOWN_ERROR = "Unknown error";
	protected final PowerShellClient exchangeClient;

	/**
	 * The name of the default malware filter policy
	 */
	protected static final String DEFAULT_POLICY_NAME = "Default";

	/**
	 * Constructs a new MalwareFilterRemediatorBase with the specified PowerShell client.
	 *
	 * @param exchangeClient The PowerShell client for Exchange Online operations
	 */
	protected MalwareFilterRemediatorBase(PowerShellClient exchangeClient) {
		this.exchangeClient = exchangeClient;
	}

	/**
	 * Checks if the malware filter policy exists
	 *
	 * @param policyName The name of the policy to check
	 * @return A CompletableFuture containing a boolean indicating if the policy exists
	 */
	protected CompletableFuture<Boolean> checkPolicyExists(String policyName) {
		logger.info("Checking if malware filter policy exists: {}", policyName);

		Map<String, Object> parameters = Map.of(IDENTITY, policyName);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Get-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					boolean exists = (result != null && !result.has(Constants.ERROR_FIELD));
					logger.info("Policy {} {}", policyName, exists ? "exists" : "does not exist");
					return exists;
				})
				.exceptionally(ex -> {
					logger.debug("Policy {} does not exist or error checking", policyName, ex);
					return false;
				});
	}

	/**
	 * Gets the malware filter policy configuration
	 *
	 * @param policyName The name of the policy to retrieve
	 * @return A CompletableFuture containing the policy configuration
	 */
	protected CompletableFuture<JsonNode> getMalwareFilterPolicy(String policyName) {
		logger.info("Getting malware filter policy: {}", policyName);

		Map<String, Object> parameters = Map.of(IDENTITY, policyName);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Get-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully retrieved malware filter policy: {}", policyName);
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to retrieve malware filter policy: {}", error);
						throw new ExoRemediationException("Failed to retrieve malware filter policy: " + error);
					}
				});

	}

	/**
	 * Creates a new malware filter policy
	 *
	 * @param policyName The name of the policy to create
	 * @param parameters The parameters for the policy
	 * @return A CompletableFuture containing the result of the policy creation
	 */
	protected CompletableFuture<JsonNode> createMalwareFilterPolicy(String policyName, Map<String, Object> parameters) {
		logger.info("Creating malware filter policy: {}", policyName);

		// Ensure the name parameter is set
		if (!parameters.containsKey("Name")) {
			parameters = new HashMap<>(parameters);
			parameters.put("Name", policyName);
		}

		logger.info("Using {} parameters for policy creation", parameters.size());

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("New-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully created malware filter policy: {}", policyName);
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to create malware filter policy: {}", error);
						throw new ExoRemediationException("Failed to create malware filter policy: " + error);
					}
				});
	}

	/**
	 * Updates an existing malware filter policy
	 *
	 * @param policyName The name of the policy to update
	 * @param parameters The parameters to update
	 * @return A CompletableFuture containing the result of the policy update
	 */
	protected CompletableFuture<JsonNode> updateMalwareFilterPolicy(String policyName, Map<String, Object> parameters) {
		logger.info("Updating malware filter policy: {}", policyName);

		// Ensure the identity parameter is set
		if (!parameters.containsKey(IDENTITY)) {
			parameters = new HashMap<>(parameters);
			parameters.put(IDENTITY, policyName);
		}

		logger.info("Using {} parameters for policy update", parameters.size());

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Set-MalwareFilterPolicy", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully updated malware filter policy: {}", policyName);
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to update malware filter policy: {}", error);
						throw new ExoRemediationException("Failed to update malware filter policy: " + error);
					}
				});
	}

	/**
	 * Determines if the malware filter policy is correctly configured based on the specific
	 * policy requirements. This method should be implemented by each specific remediator.
	 *
	 * @param policyConfig The policy configuration
	 * @return true if the policy is correctly configured, false otherwise
	 */
	protected abstract boolean isPolicyCorrectlyConfigured(JsonNode policyConfig);

	/**
	 * Builds the parameters required to update the malware filter policy based on the specific
	 * policy requirements. This method should be implemented by each specific remediator.
	 *
	 * @return Map of parameters for the PowerShell cmdlet
	 */
	protected abstract Map<String, Object> buildPolicyParameters();
}