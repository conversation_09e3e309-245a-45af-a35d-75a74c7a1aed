package io.syrix.products.microsoft.entra.model.reccomendations;

import java.time.LocalDateTime;
import java.util.List;

// Main recommendation class that inherits from base
public class Recommendation {
	private RecommendationType recommendationType;
	private ReleaseType releaseType;
	private String id;
	private List<ActionStep> actionSteps;
	private String benefits;
	private RecommendationCategory category;
	private LocalDateTime createdDateTime;
	private Double currentScore;
	private String displayName;
	private List<RecommendationFeatureArea> featureAreas;
	private ImpactType impactType;
	private LocalDateTime impactStartDateTime;
	private String insights;
	private LocalDateTime lastCheckedDateTime;
	private LocalDateTime lastModifiedDateTime;
	private String lastModifiedBy;
	private Double maxScore;
	private LocalDateTime postponeUntilDateTime;
	private RecommendationPriority priority;
	private RecommendationStatus status;
	private String remediationImpact;
	private List<ImpactedResource> impactedResources;
	private String requiredLicenses;

	// Getters and setters
	public String getId() { return id; }
	public void setId(String id) { this.id = id; }

	public List<ActionStep> getActionSteps() { return actionSteps; }
	public void setActionSteps(List<ActionStep> actionSteps) { this.actionSteps = actionSteps; }

	public String getBenefits() {
		return benefits;
	}

	public void setBenefits(String benefits) {
		this.benefits = benefits;
	}

	public RecommendationCategory getCategory() {
		return category;
	}

	public void setCategory(RecommendationCategory category) {
		this.category = category;
	}

	public LocalDateTime getCreatedDateTime() {
		return createdDateTime;
	}

	public void setCreatedDateTime(LocalDateTime createdDateTime) {
		this.createdDateTime = createdDateTime;
	}

	public Double getCurrentScore() {
		return currentScore;
	}

	public void setCurrentScore(Double currentScore) {
		this.currentScore = currentScore;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public List<RecommendationFeatureArea> getFeatureAreas() {
		return featureAreas;
	}

	public void setFeatureAreas(List<RecommendationFeatureArea> featureAreas) {
		this.featureAreas = featureAreas;
	}

	public ImpactType getImpactType() {
		return impactType;
	}

	public void setImpactType(ImpactType impactType) {
		this.impactType = impactType;
	}

	public LocalDateTime getImpactStartDateTime() {
		return impactStartDateTime;
	}

	public void setImpactStartDateTime(LocalDateTime impactStartDateTime) {
		this.impactStartDateTime = impactStartDateTime;
	}

	public String getInsights() {
		return insights;
	}

	public void setInsights(String insights) {
		this.insights = insights;
	}

	public LocalDateTime getLastCheckedDateTime() {
		return lastCheckedDateTime;
	}

	public void setLastCheckedDateTime(LocalDateTime lastCheckedDateTime) {
		this.lastCheckedDateTime = lastCheckedDateTime;
	}

	public LocalDateTime getLastModifiedDateTime() {
		return lastModifiedDateTime;
	}

	public void setLastModifiedDateTime(LocalDateTime lastModifiedDateTime) {
		this.lastModifiedDateTime = lastModifiedDateTime;
	}

	public String getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(String lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public Double getMaxScore() {
		return maxScore;
	}

	public void setMaxScore(Double maxScore) {
		this.maxScore = maxScore;
	}

	public LocalDateTime getPostponeUntilDateTime() {
		return postponeUntilDateTime;
	}

	public void setPostponeUntilDateTime(LocalDateTime postponeUntilDateTime) {
		this.postponeUntilDateTime = postponeUntilDateTime;
	}

	public RecommendationPriority getPriority() {
		return priority;
	}

	public void setPriority(RecommendationPriority priority) {
		this.priority = priority;
	}

	public RecommendationStatus getStatus() {
		return status;
	}

	public void setStatus(RecommendationStatus status) {
		this.status = status;
	}

	public String getRemediationImpact() {
		return remediationImpact;
	}

	public void setRemediationImpact(String remediationImpact) {
		this.remediationImpact = remediationImpact;
	}

	public List<ImpactedResource> getImpactedResources() {
		return impactedResources;
	}

	public void setImpactedResources(List<ImpactedResource> impactedResources) {
		this.impactedResources = impactedResources;
	}

	public RecommendationType getRecommendationType() { return recommendationType; }
	public void setRecommendationType(RecommendationType recommendationType) {
		this.recommendationType = recommendationType;
	}

	public ReleaseType getReleaseType() { return releaseType; }
	public void setReleaseType(ReleaseType releaseType) { this.releaseType = releaseType; }

	public String getRequiredLicenses() {
		return requiredLicenses;
	}

	public void setRequiredLicenses(String requiredLicenses) {
		this.requiredLicenses = requiredLicenses;
	}
}
