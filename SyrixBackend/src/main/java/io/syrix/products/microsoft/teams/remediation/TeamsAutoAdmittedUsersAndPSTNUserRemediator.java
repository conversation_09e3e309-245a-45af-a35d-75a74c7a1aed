package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationStrategy;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.AutoAdmittedUsers;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

//In accordance with the policy MS.TEAMS.1.4v1 user have 3 options:
//        "EveryoneInCompany",
//        "EveryoneInSameAndFederatedCompany",
//        "EveryoneInCompanyExcludingGuests"
@PolicyRemediator("MS.TEAMS.1.3v1")
public class TeamsAutoAdmittedUsersAndPSTNUserRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAutoAdmittedUsersAndPSTNUserRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAutoAdmittedUsersAndPSTNUserRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			logger.error("No meeting policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found in configuration"));
		}

		TeamsMeetingPolicy globalPolicy = policies.stream()
				.filter(p -> TeamsConstants.GLOBAL_POLICY_IDENTITY.equals(p.identity))
				.findFirst()
				.orElse(null);

		if (globalPolicy == null) {
			logger.error("Global policy not found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Global policy not found"));
		}

		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;
		meetingPolicy.autoAdmittedUsers = selectautoAdmittedUsers(remediationConfig.getAutoAdmittedUsersStrategy());
		meetingPolicy.allowPSTNUsersToBypassLobby = false;

		ParameterChangeResult autoAdmitChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("AutoAdmittedUsers")
				.prevValue(globalPolicy.autoAdmittedUsers)
				.newValue(meetingPolicy.autoAdmittedUsers.asString());

		ParameterChangeResult pstnLobbyChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("AllowPSTNUsersToBypassLobby")
				.prevValue(meetingPolicy.allowPSTNUsersToBypassLobby)
				.newValue(meetingPolicy.allowPSTNUsersToBypassLobby);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(jsonNode -> {
					autoAdmitChange.status(ParameterChangeStatus.SUCCESS);
					pstnLobbyChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.AUTO_ADMITTED_USERS_COMPANY_SUCCESS_MESSAGE, List.of(autoAdmitChange, pstnLobbyChange));
				})
				.exceptionally(ex -> {
					autoAdmitChange.status(ParameterChangeStatus.FAILED);
					pstnLobbyChange.status(ParameterChangeStatus.FAILED);
					logger.error("Exception during auto admitted users remediation", ex);
					return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(autoAdmitChange, pstnLobbyChange));
				});
	}

	private static AutoAdmittedUsers selectautoAdmittedUsers(TeamsRemediationStrategy strategy) {
		return switch (strategy) {
			case EVERYONE_IN_COMPANY -> AutoAdmittedUsers.EVERYONE_IN_COMPANY;
			case EVERYONE_IN_SAME_AND_FEDERATED_COMPANY -> AutoAdmittedUsers.EVERYONE_IN_SAME_AND_FEDERATED_COMPANY;
			case EVERYONE_IN_COMPANY_EXCLUDING_GUESTS -> AutoAdmittedUsers.EVERYONE_IN_COMPANY_EXCLUDING_GUESTS;
		};
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			MeetingPolicy meetingPolicy = new MeetingPolicy();
			meetingPolicy.identity = TeamsConstants.GLOBAL_POLICY_IDENTITY;

			for (ParameterChangeResult change : changes) {
				switch (change.getParameter()) {
					case "AutoAdmittedUsers" -> meetingPolicy.autoAdmittedUsers = AutoAdmittedUsers.fromString(change.getPrevValue().toString());
					case "AllowPSTNUsersToBypassLobby" -> meetingPolicy.allowPSTNUsersToBypassLobby = Boolean.parseBoolean(change.getPrevValue().toString());
				}
			}

			List<ParameterChangeResult> rollbackChanges = changes.stream()
					.map(change -> new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter(change.getParameter())
							.prevValue(change.getNewValue())
							.newValue(change.getPrevValue()))
					.toList();

			return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
					.thenApply(jsonNode -> {
						rollbackChanges.forEach(change -> change.status(ParameterChangeStatus.SUCCESS));
						return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back auto admitted users and PSTN users policy", rollbackChanges);
					})
					.exceptionally(ex -> {
						rollbackChanges.forEach(change -> change.status(ParameterChangeStatus.FAILED));
						logger.error("Exception during auto admitted users and PSTN users policy rollback", ex);
						return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), rollbackChanges);
					});
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

}
