package io.syrix.products.microsoft.defender.remediation;

import io.syrix.products.microsoft.base.CommonAuditRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.PowerShellClient;

/**
 * Microsoft Purview Audit Logging and Retention Remediator for Microsoft Defender
 * <p>
 * Implements three controls for Microsoft Purview Audit:
 * <ul>
 *     <li>MS.DEFENDER.6.1v1: Enable Microsoft Purview Audit (Standard)</li>
 *     <li>MS.DEFENDER.6.2v1: Enable Microsoft Purview Audit (Premium)</li>
 *     <li>MS.DEFENDER.6.3v1: Configure Audit Log Retention for OMB M-21-31 compliance</li>
 * </ul>
 * <p>
 * Ensures audit logs are retained for 12 months (active storage) as required by OMB M-21-31.
 * <p>
 * MS.DEFENDER.6.1v1 Rationale: Responding to incidents without detailed information about
 * activities that took place slows response actions. Enabling Microsoft Purview Audit (Standard)
 * helps ensure agencies have visibility into user actions. Furthermore, enabling the unified
 * audit log is required for government agencies by OMB M-21-31 (referred to therein by its
 * former name, Unified Audit Logs).
 * <p>
 * MS.DEFENDER.6.2v1 Rationale: Standard logging may not include relevant details necessary
 * for visibility into user actions during an incident. Enabling Microsoft Purview Audit (Premium)
 * captures additional event types not included with Standard. Furthermore, it is required for
 * government agencies by OMB M-21-31 (referred to therein as by its former name, Unified Audit
 * Logs w/Advanced Features).
 * <p>
 * MS.DEFENDER.6.3v1 Rationale: Audit logs may no longer be available when needed if they are
 * not retained for a sufficient time. Increased log retention time gives an agency the necessary
 * visibility to investigate incidents that occurred some time ago.
 */
@PolicyRemediator("MS.DEFENDER.6.1v1, MS.DEFENDER.6.2v1, MS.DEFENDER.6.3v1")
public class DefenderAuditRemediator extends CommonAuditRemediator {

	/**
	 * Constructs a new DefenderAuditRemediator with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 */
	public DefenderAuditRemediator(PowerShellClient powershellClient) {
		super(powershellClient);
	}
}