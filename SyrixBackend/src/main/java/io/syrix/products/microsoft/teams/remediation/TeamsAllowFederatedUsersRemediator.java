package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;
import org.apache.commons.collections4.MapUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

//TODO Artur need check how the reset allowedDomains
@PolicyRemediator("MS.TEAMS.2.1v1")
public class TeamsAllowFederatedUsersRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowFederatedUsersRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowFederatedUsersRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> federationConfigurations = getFederationConfigurations();

		if (federationConfigurations == null || federationConfigurations.isEmpty()) {
			logger.error("No federation configurations found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No federation configurations found"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();
		for (TeamsTenantFederationConfiguration configuration : federationConfigurations) {
			if (configuration.allowFederatedUsers && MapUtils.isEmpty(configuration.allowedDomains)) {
				TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
				prevConfig.identity = configuration.identity;
				prevConfig.allowFederatedUsers = configuration.allowFederatedUsers;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = configuration.identity;
				newConfig.allowFederatedUsers = false;

				results.add(fixConfig(prevConfig, newConfig));
			}
		}

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(TenantFederationConfiguration prevConfig, TenantFederationConfiguration newConfig) {
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowFederatedUsers: " + prevConfig.identity)
				.prevValue(prevConfig.allowFederatedUsers)
				.newValue(newConfig.allowFederatedUsers);

		return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(newConfig))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Federation configuration updated: " + newConfig.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), TeamsConstants.FEDERATED_USERS_SUCCESS_MESSAGE, allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any federation configurations", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " configurations, failed to fix " + failedCount + " configurations",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();
			
			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean newValue = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean curValue = Boolean.parseBoolean(change.getNewValue().toString());

				TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
				prevConfig.identity = identity;
				prevConfig.allowFederatedUsers = curValue;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = identity;
				newConfig.allowFederatedUsers = newValue;

				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					results.add(fixConfig(prevConfig, newConfig));
				} else {
					ParameterChangeResult paramChange = new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter("allowFederatedUsers: " + prevConfig.identity)
							.prevValue(prevConfig.allowFederatedUsers)
							.newValue(newConfig.allowFederatedUsers)
							.status(ParameterChangeStatus.FAILED);

					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
				}
				results.add(fixConfig(prevConfig, newConfig));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

}
