package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.APPLICATION_JSON;
import static io.syrix.common.constants.Constants.CONTENT_TYPE_HEADER;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.*;

/**
 * Implements remediation for MS.AAD.8.3v1: Restrict Guest Invitations to Authorized Domains.
 *
 * This remediator restricts guest invitations by configuring the B2BManagementPolicy
 * with allowed domains (allowlist) or blocked domains (blocklist), but not both.
 */
@PolicyRemediator("MS.AAD.8.3v1")
public class EntraIDGuestDomainRestrictionRemediator extends BaseGuestAccessRemediator {

	private final List<String> allowedDomains;
	private final List<String> blockedDomains;
	private final boolean overrideDomains;

	/**
	 * Constructor for Guest Domain Restriction Remediator with allowed and blocked domains.
	 * Only one list (allowed or blocked) can have values, but not both. Both can be empty.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param allowedDomains List of domains that are allowed for guest invitations (can be empty)
	 * @param blockedDomains List of domains that are blocked for guest invitations (can be empty)
	 * @param overrideDomains If true, completely replace existing domains; if false, merge with existing domains
	 * @throws IllegalArgumentException if both lists contain values
	 */
	public EntraIDGuestDomainRestrictionRemediator(
			MicrosoftGraphClient graphClient,
			List<String> allowedDomains,
			List<String> blockedDomains,
			boolean overrideDomains) {
		super(graphClient);
		this.allowedDomains = allowedDomains != null ? new ArrayList<>(allowedDomains) : new ArrayList<>();
		this.blockedDomains = blockedDomains != null ? new ArrayList<>(blockedDomains) : new ArrayList<>();
		this.overrideDomains = overrideDomains;

		// Validate that only one list can have values, but not both
		if (!this.allowedDomains.isEmpty() && !this.blockedDomains.isEmpty()) {
			throw new IllegalArgumentException("Both allowed and blocked domain lists cannot have values simultaneously. Only one list can be used at a time.");
		}
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		// Additional validation to ensure the validation rule is respected
		if (!allowedDomains.isEmpty() && !blockedDomains.isEmpty()) {
			return CompletableFuture.completedFuture(
					IPolicyRemediator.failed(getPolicyId(),
							"Both allowed and blocked domain lists cannot have values simultaneously. Only one list can be used at a time.").join());
		}

		String modeDescription = overrideDomains ? "override" : "merge with existing";
		if (!allowedDomains.isEmpty()) {
			logger.info("Starting remediation to {} allowed domains for guest invitations: {}", modeDescription, allowedDomains);
		} else if (!blockedDomains.isEmpty()) {
			logger.info("Starting remediation to {} blocked domains for guest invitations: {}", modeDescription, blockedDomains);
		} else {
			logger.info("Starting remediation to reset guest invitation domain restrictions");
		}

		return findB2BManagementPolicy()
				.thenCompose(policy -> {
					if (overrideDomains) {
						// Simply update with the provided domains
						return updateB2BManagementPolicy(policy, allowedDomains, blockedDomains);
					} else {
						// Merge with existing domains before updating
						return mergeAndUpdateDomains(policy);
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during guest domain restriction remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Finds the B2BManagementPolicy in the tenant.
	 *
	 * @return CompletableFuture with the policy information
	 */
	private CompletableFuture<JsonNode> findB2BManagementPolicy() {
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.beta()
								.withEndpoint(LEGACY_POLICIES_ENDPOINT)
								.build())
				.thenApply(policies -> {
					logger.debug("Found {} legacy policies", policies.get(VALUE_FIELD).size());

					// Find the B2BManagementPolicy
					for (JsonNode policy : policies.get(VALUE_FIELD)) {
						if (B2B_MANAGEMENT_POLICY_TYPE.equals(policy.get(TYPE_FIELD).asText())) {
							logger.info("Found B2BManagementPolicy with ID: {}", policy.get(ID_FIELD).asText());
							return policy;
						}
					}

					throw new IllegalStateException("B2BManagementPolicy not found in tenant");
				});
	}

	public static <T> void addIfNotExists(Collection<T> collection, T value) {
		if (!collection.contains(value)) {
			collection.add(value);
		}
	}
	/**
	 * Extracts existing domain lists from policy and merges with the new domains.
	 *
	 * @param policy The existing B2BManagementPolicy
	 * @return CompletableFuture with the remediation result
	 */
	private CompletableFuture<JsonNode> mergeAndUpdateDomains(JsonNode policy) {
		try {
			// Extract existing domains from policy definition
			Set<String> existingAllowedDomains = new HashSet<>();
			Set<String> existingBlockedDomains = new HashSet<>();

			mergeDomainsForPolicy(policy, existingAllowedDomains, existingBlockedDomains);

			// Merge existing with new domains
			List<String> mergedAllowedDomains = new ArrayList<>();
			List<String> mergedBlockedDomains = new ArrayList<>();

			if (!allowedDomains.isEmpty()) {
				// We're adding to allowed domains
				mergedAllowedDomains.addAll(existingAllowedDomains);
				allowedDomains.forEach(domain -> addIfNotExists(mergedAllowedDomains, domain));
				// Use existing blocked domains
				mergedBlockedDomains.addAll(existingBlockedDomains);
			} else if (!blockedDomains.isEmpty()) {
				// We're adding to blocked domains
				mergedBlockedDomains.addAll(existingBlockedDomains);
				blockedDomains.forEach(domain -> addIfNotExists(mergedBlockedDomains, domain));
				// Use existing allowed domains
				mergedAllowedDomains.addAll(existingAllowedDomains);
			} else {
				// If both are empty, we'll keep the existing lists
				mergedAllowedDomains.addAll(existingAllowedDomains);
				mergedBlockedDomains.addAll(existingBlockedDomains);
			}

			// Validate that we don't have both allowed and blocked domains
			if (!mergedAllowedDomains.isEmpty() && !mergedBlockedDomains.isEmpty()) {
				logger.warn("Cannot have both allowed and blocked domains. Prioritizing allowed domains.");
				return IPolicyRemediator.failed(getPolicyId(),
						"Cannot have both allowed and blocked domains. Prioritizing allowed domains.");
			}

			logger.info("Merged domains - Allowed: {}, Blocked: {}", mergedAllowedDomains, mergedBlockedDomains);

			// Update the policy with merged domains
			return updateB2BManagementPolicy(policy, mergedAllowedDomains, mergedBlockedDomains);
		} catch (Exception e) {
			logger.error("Error merging domains", e);
			return CompletableFuture.failedFuture(e);
		}
	}

	private void mergeDomainsForPolicy(JsonNode policy, Set<String> existingAllowedDomains,
							  Set<String> existingBlockedDomains) throws JsonProcessingException {
		if (policy.has(DEFINITION) && policy.get(DEFINITION).isArray()) {
			JsonNode definitionsArray = policy.get(DEFINITION);
			for (JsonNode defNode : definitionsArray) {
				// Parse the definition JSON string
				JsonNode definitionObj = objectMapper.readTree(defNode.asText());
				if (definitionObj.has(B2B_MANAGEMENT_POLICY)) {
					JsonNode b2bPolicy = definitionObj.get(B2B_MANAGEMENT_POLICY);
					if (b2bPolicy.has(INVITATIONS_ALLOWED_AND_BLOCKED_DOMAINS_POLICY)) {
						JsonNode domainsPolicy = b2bPolicy.get(INVITATIONS_ALLOWED_AND_BLOCKED_DOMAINS_POLICY);

						// Extract allowed domains
						mergeDomains(existingAllowedDomains, domainsPolicy, ALLOWED_DOMAINS);

						// Extract blocked domains
						mergeDomains(existingBlockedDomains, domainsPolicy, BLOCKED_DOMAINS);
					}
				}
			}
		}
		logger.info("Found existing domains - Allowed: {}, Blocked: {}", existingAllowedDomains, existingBlockedDomains);
	}

	private static void mergeDomains(Set<String> existingDomains, JsonNode domainsPolicy, String domainType) {
		if (domainsPolicy.has(domainType) && domainsPolicy.get(domainType).isArray()) {
			for (JsonNode domain : domainsPolicy.get(domainType)) {
				existingDomains.add(domain.asText());
			}
		}
	}

	/**
	 * Updates the B2BManagementPolicy with the specified allowed and blocked domains.
	 *
	 * @param policy The existing B2BManagementPolicy
	 * @param finalAllowedDomains Final list of allowed domains to set
	 * @param finalBlockedDomains Final list of blocked domains to set
	 * @return CompletableFuture with the remediation result
	 */
	private CompletableFuture<JsonNode> updateB2BManagementPolicy(
			JsonNode policy,
			List<String> finalAllowedDomains,
			List<String> finalBlockedDomains) {

		String policyId = policy.get(ID_FIELD).asText();

		// Create the policy structure following the exact payload format
		ObjectNode b2bPolicyNode = objectMapper.createObjectNode();
		ObjectNode invitationsPolicyNode = objectMapper.createObjectNode();

		// Create and populate allowed domains array
		ArrayNode allowedDomainsArray = objectMapper.createArrayNode();
		for (String domain : finalAllowedDomains) {
			allowedDomainsArray.add(domain);
		}
		invitationsPolicyNode.set(ALLOWED_DOMAINS, allowedDomainsArray);

		// Create and populate blocked domains array
		ArrayNode blockedDomainsArray = objectMapper.createArrayNode();
		for (String domain : finalBlockedDomains) {
			blockedDomainsArray.add(domain);
		}
		invitationsPolicyNode.set(BLOCKED_DOMAINS, blockedDomainsArray);

		// Build the B2BManagementPolicy
		b2bPolicyNode.set(INVITATIONS_ALLOWED_AND_BLOCKED_DOMAINS_POLICY, invitationsPolicyNode);

		// Create the full policy definition
		ObjectNode definitionNode = objectMapper.createObjectNode();
		definitionNode.set(B2B_MANAGEMENT_POLICY, b2bPolicyNode);

		// Create payload for updating the policy
		ObjectNode payload = objectMapper.createObjectNode();
		ArrayNode definitionArray = objectMapper.createArrayNode();
		definitionArray.add(definitionNode.toString());
		payload.set(DEFINITION, definitionArray);

		// Make request to update the policy
		return graphClient.makeGraphRequest(
						GraphRequest.builder()
								.beta()
								.withMethod(HttpMethod.PATCH)
								.withEndpoint(LEGACY_POLICIES_ENDPOINT + "/" + policyId)
								.withBody(HttpRequest.BodyPublishers.ofString(payload.toString()))
								.addHeader(CONTENT_TYPE_HEADER, APPLICATION_JSON)
								.build())
				.thenApply(response -> {
					String message;

					if (!finalAllowedDomains.isEmpty()) {
						message = "Guest invitations allowed only from domains: " + finalAllowedDomains;
					} else if (!finalBlockedDomains.isEmpty()) {
						message = "Guest invitations blocked from domains: " + finalBlockedDomains;
					} else {
						message = "Guest invitation domain restrictions reset (no restrictions applied)";
					}

					String operationType = overrideDomains ? "overridden" : "merged";
					logger.info("Successfully {} B2BManagementPolicy: {}", operationType, message);
					return IPolicyRemediator.success(getPolicyId(), message).join();
				});
	}

	/**
	 * Factory method to create a remediator with allowed domains only (override mode).
	 *
	 * @param graphClient The Microsoft Graph client
	 * @param allowedDomains List of domains that should be allowed for guest invitations
	 * @return EntraIDGuestDomainRestrictionRemediator configured with the specified allowed domains
	 */
	public static EntraIDGuestDomainRestrictionRemediator withAllowedDomains(
			MicrosoftGraphClient graphClient,
			List<String> allowedDomains) {
		return new EntraIDGuestDomainRestrictionRemediator(graphClient, allowedDomains, null, true);
	}

	/**
	 * Factory method to create a remediator with allowed domains (merge mode).
	 *
	 * @param graphClient The Microsoft Graph client
	 * @param allowedDomains List of domains to add to allowed domains for guest invitations
	 * @return EntraIDGuestDomainRestrictionRemediator configured to merge with existing allowed domains
	 */
	public static EntraIDGuestDomainRestrictionRemediator withAllowedDomainsMerge(
			MicrosoftGraphClient graphClient,
			List<String> allowedDomains) {
		return new EntraIDGuestDomainRestrictionRemediator(graphClient, allowedDomains, null, false);
	}

	/**
	 * Factory method to create a remediator with blocked domains only (override mode).
	 *
	 * @param graphClient The Microsoft Graph client
	 * @param blockedDomains List of domains that should be blocked for guest invitations
	 * @return EntraIDGuestDomainRestrictionRemediator configured with the specified blocked domains
	 */
	public static EntraIDGuestDomainRestrictionRemediator withBlockedDomains(
			MicrosoftGraphClient graphClient,
			List<String> blockedDomains) {
		return new EntraIDGuestDomainRestrictionRemediator(graphClient, null, blockedDomains, true);
	}

	/**
	 * Factory method to create a remediator with blocked domains (merge mode).
	 *
	 * @param graphClient The Microsoft Graph client
	 * @param blockedDomains List of domains to add to blocked domains for guest invitations
	 * @return EntraIDGuestDomainRestrictionRemediator configured to merge with existing blocked domains
	 */
	public static EntraIDGuestDomainRestrictionRemediator withBlockedDomainsMerge(
			MicrosoftGraphClient graphClient,
			List<String> blockedDomains) {
		return new EntraIDGuestDomainRestrictionRemediator(graphClient, null, blockedDomains, false);
	}

	/**
	 * Factory method to create a remediator with no domain restrictions (override mode).
	 *
	 * @param graphClient The Microsoft Graph client
	 * @return EntraIDGuestDomainRestrictionRemediator configured to clear all domain restrictions
	 */
	public static EntraIDGuestDomainRestrictionRemediator withNoDomainRestrictions(
			MicrosoftGraphClient graphClient) {
		return new EntraIDGuestDomainRestrictionRemediator(graphClient, null, null, true);
	}
}