package io.syrix.products.microsoft.forms;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Configuration service for Microsoft Forms security settings.

 * Provides comprehensive functionality for retrieving Forms security configurations,
 * including phishing protection settings with proper error handling and metrics collection.

 * SYRIX METHODOLOGY: Generated following approved design document for SYRIX-51
 * Based on: EntraConfigurationService and other existing configuration service patterns
 * Related JIRA: SYRIX-51 - Enable Forms Phishing Protection
 * CIS Reference: Microsoft 365 benchmark 1.3.5
 */
public class FormsConfigurationService extends BaseConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(FormsConfigurationService.class);
    
    // Microsoft Graph API endpoints
    private static final String FORMS_SETTINGS_ENDPOINT = "/admin/forms/settings";
    
    // Configuration keys for rego policy integration
    private static final String FORMS_PHISHING_PROTECTION_KEY = "forms_phishing_protection";
    
    // Timeouts and configuration
    private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(2);
    
    /**
     * Creates a new Forms configuration service with default settings.
     */
    public FormsConfigurationService(MicrosoftGraphClient graphClient) {
        this(graphClient, new ObjectMapper(), new MetricsCollector());
    }
    
    /**
     * Creates a new Forms configuration service with custom dependencies.
     */
    public FormsConfigurationService(
            MicrosoftGraphClient graphClient,
            ObjectMapper objectMapper,
            MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
    }
    
    @Override
    public ConfigurationResult exportConfiguration() {
        Instant startTime = Instant.now();
        metrics.recordExportStart();
        logger.info("Starting Forms configuration export at {}", startTime);

        try {
            // Create futures for all configuration components
            Map<String, CompletableFuture<?>> futures = new HashMap<>();
            
            // CRITICAL: Configuration for MS.FORMS.1.1v1 policy
            futures.put(FORMS_PHISHING_PROTECTION_KEY, getFormsPhishingProtectionConfiguration());
            
            return processConfigurationFutures(futures, startTime);
            
        } catch (Exception e) {
            logger.error("Exception during Forms configuration export for MS.FORMS.1.1v1 policy: {}", e.getMessage(), e);
            throw new ConfigurationExportException("Forms configuration export failed for policy MS.FORMS.1.1v1 - " + e.getMessage(), e);
        }
    }
    
    /**
     * Retrieves Forms phishing protection configuration for MS.FORMS.1.1v1
     * CRITICAL: Places data under key "forms_phishing_protection" for rego policy evaluation
     * Related to: CIS Microsoft 365 benchmark 1.3.5
     */
    public CompletableFuture<JsonNode> getFormsPhishingProtectionConfiguration() {
        logger.info("Retrieving Forms phishing protection configuration for MS.FORMS.1.1v1");
        
        return withRetry(() -> 
            graphClient.makeGraphRequest(
                GraphRequest.builder()
                    .beta()
                    .withEndpoint(FORMS_SETTINGS_ENDPOINT)
                    .build()
            ), "Get-FormsSettings")
            .thenApply(response -> {
                logger.debug("Forms settings retrieved successfully");
                return processFormsSettingsResponse(response);
            })
            .exceptionally(ex -> {
                logger.error("Failed to retrieve Forms settings for MS.FORMS.1.1v1: {}", ex.getMessage(), ex);
                return handleGraphApiError("forms_phishing_protection", ex);
            });
    }
    
    /**
     * Processes the Forms settings response from Microsoft Graph API.
     * Ensures the response format is compatible with rego policy expectations.
     * 
     * @param response The raw response from Graph API
     * @return Processed JsonNode with Forms configuration data
     */
    private JsonNode processFormsSettingsResponse(JsonNode response) {
        if (response == null) {
            logger.warn("Received null response from Forms settings API");
            return objectMapper.createObjectNode();
        }
        
        // Log the phishing protection status for audit purposes
        boolean isPhishingProtectionEnabled = response
            .path("isInOrgFormsPhishingScanEnabled")
            .asBoolean(false);
            
        logger.info("Forms phishing protection status: {}", 
            isPhishingProtectionEnabled ? "ENABLED" : "DISABLED");
        
        return response;
    }
    
    /**
     * Handles Graph API errors for Forms configuration retrieval.
     * Creates appropriate error response nodes for failed API calls.
     * 
     * @param configKey The configuration key that failed
     * @param exception The exception that occurred
     * @return Error response node
     */
    private JsonNode handleGraphApiError(String configKey, Throwable exception) {
        var errorNode = objectMapper.createObjectNode();
        errorNode.put("error", "Failed to retrieve " + configKey);
        errorNode.put("message", exception.getMessage());
        errorNode.put("timestamp", Instant.now().toString());
        
        // Log specific error types for troubleshooting
        if (exception.getMessage().contains("Forbidden") || exception.getMessage().contains("403")) {
            logger.error("Insufficient permissions for Forms configuration. Required scope: OrgSettings-Forms.Read.All");
        } else if (exception.getMessage().contains("NotFound") || exception.getMessage().contains("404")) {
            logger.error("Forms settings endpoint not found. Verify Microsoft Graph API version and feature availability");
        }
        
        return errorNode;
    }
    
    /**
     * Handles configuration errors during export process.
     * Creates appropriate ConfigurationResult for failed exports.
     * 
     * @param message Error message
     * @param exception The exception that occurred  
     * @param startTime Export start time for metrics
     * @return ConfigurationResult with error details
     */
    private ConfigurationResult handleConfigurationError(String message, Exception exception, Instant startTime) {
        Duration exportDuration = Duration.between(startTime, Instant.now());
        metrics.recordExportFailure();
        
        logger.error("{}: {}", message, exception.getMessage(), exception);
        
        var errorData = objectMapper.createObjectNode();
        errorData.put("error", message);
        errorData.put("details", exception.getMessage());
        errorData.put("timestamp", Instant.now().toString());
        
        var errorJsonData = objectMapper.createObjectNode();
        errorJsonData.set("error", errorData);
        
        var metadata = objectMapper.createObjectNode();
        metadata.put("export_duration_ms", exportDuration.toMillis());
        metadata.put("success", false);
        
        return ConfigurationResult.builder()
            .withData(errorJsonData)
            .withMetadata(metadata)
            .withTimestamp(Instant.now())
            .build();
    }
    
    /**
     * Processes configuration futures and creates final ConfigurationResult.
     * Handles both successful and failed configuration retrievals.
     * 
     * @param futures Map of configuration futures to process
     * @param startTime Export start time for metrics
     * @return Final ConfigurationResult
     */
    private ConfigurationResult processConfigurationFutures(
            Map<String, CompletableFuture<?>> futures, 
            Instant startTime) {
        
        try {
            // Wait for all futures to complete
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.values().toArray(new CompletableFuture[0])
            );
            
            allFutures.join(); // Wait for completion
            
            // Collect results
            Map<String, Object> configData = new HashMap<>();
            Map<String, Integer> metrics = new HashMap<>();
            boolean allSuccessful = true;
            
            for (Map.Entry<String, CompletableFuture<?>> entry : futures.entrySet()) {
                try {
                    Object result = entry.getValue().get();
                    configData.put(entry.getKey(), result);
                    metrics.put(entry.getKey() + "_success", 1);
                } catch (Exception e) {
                    logger.error("Failed to retrieve configuration for {}: {}", entry.getKey(), e.getMessage());
                    allSuccessful = false;
                    metrics.put(entry.getKey() + "_failure", 1);
                }
            }
            
            Duration exportDuration = Duration.between(startTime, Instant.now());
            
            if (allSuccessful) {
                this.metrics.recordExportSuccess(exportDuration);
                logger.info("Forms configuration export completed successfully in {} ms", exportDuration.toMillis());
            } else {
                this.metrics.recordExportFailure();
                logger.warn("Forms configuration export completed with some failures in {} ms", exportDuration.toMillis());
            }
            
            return waitForFutures(futures)
                .thenApply(map -> {
                    map.put("forms_successful_commands", getSuccessfulCommands());
                    map.put("forms_unsuccessful_commands", getUnsuccessfulCommands());
                    return buildConfigurationResult(map, "1.0", ConfigurationServiceType.FORMS);
                })
                .join();
            
        } catch (Exception e) {
            return handleConfigurationError("Failed to process configuration futures", e, startTime);
        }
    }
}