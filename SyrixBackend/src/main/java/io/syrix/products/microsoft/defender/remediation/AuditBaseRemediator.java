package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellClient.CommandRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Base class for Microsoft Purview Audit remediations.
 * <p>
 * This class provides common functionality for managing Microsoft Purview Audit settings,
 * including standard and premium audit logging and retention policies.
 *
 * Common functionality includes:
 * - Checking audit log configuration
 * - Enabling standard audit logging
 * - Creating and managing audit log retention policies
 */
public abstract class AuditBaseRemediator extends RemediatorBase {

	// PowerShell commands
	protected static final String GET_ADMIN_AUDIT_LOG_CONFIG = "Get-AdminAuditLogConfig";
	protected static final String SET_ADMIN_AUDIT_LOG_CONFIG = "Set-AdminAuditLogConfig";
	protected static final String GET_RETENTION_COMPLIANCE_POLICY = "Get-RetentionCompliancePolicy";
	protected static final String NEW_RETENTION_COMPLIANCE_POLICY = "New-RetentionCompliancePolicy";
	protected static final String GET_RETENTION_COMPLIANCE_RULE = "Get-RetentionComplianceRule";
	protected static final String NEW_RETENTION_COMPLIANCE_RULE = "New-RetentionComplianceRule";

	// Configuration properties
	protected static final String UNIFIED_AUDIT_LOG_INGESTION_ENABLED = "UnifiedAuditLogIngestionEnabled";

	// Retention policy properties
	protected static final String NAME = "Name";
	protected static final String COMMENT = "Comment";
	protected static final String RETENTION_DURATION = "RetentionDuration";
	protected static final String RETENTION_TYPE = "RetentionType";
	protected static final String WORKLOAD = "Workload";
	protected static final String POLICY = "Policy";
	protected static final String RECORD_TYPE = "RecordType";

	// Retention values
	protected static final String AUDIT_LOG = "AuditLog";
	protected static final String ALL_WORKLOADS = "All";

	protected final PowerShellClient powershellClient;
	protected final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * Constructs a new AuditBaseRemediator with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 */
	public AuditBaseRemediator(PowerShellClient powershellClient) {
		this.powershellClient = powershellClient;
	}

	/**
	 * Gets the current audit log configuration.
	 *
	 * @return CompletableFuture with the audit log configuration
	 */
	protected CompletableFuture<JsonNode> getAuditLogConfig() {
		logger.info("Retrieving current audit log configuration");
		return powershellClient.executeCmdletCommand(
				new CommandRequest(GET_ADMIN_AUDIT_LOG_CONFIG, new HashMap<>())
		);
	}

	/**
	 * Checks if standard audit logging is enabled.
	 *
	 * @param auditConfig The audit configuration to check
	 * @return true if standard audit logging is enabled, false otherwise
	 */
	protected boolean isStandardAuditEnabled(JsonNode auditConfig) {
		if (auditConfig == null) {
			return false;
		}

		// Check the UnifiedAuditLogIngestionEnabled property
		return auditConfig.path(UNIFIED_AUDIT_LOG_INGESTION_ENABLED).asBoolean(false);
	}

	/**
	 * Enables standard audit logging.
	 *
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> enableStandardAudit() {
		logger.info("Enabling standard audit logging");

		Map<String, Object> params = new HashMap<>();
		params.put(UNIFIED_AUDIT_LOG_INGESTION_ENABLED, true);

		return powershellClient.executeCmdletCommand(
				new CommandRequest(SET_ADMIN_AUDIT_LOG_CONFIG, params)
		);
	}

	/**
	 * Gets all current retention compliance policies.
	 *
	 * @return CompletableFuture with the list of retention policies
	 */
	protected CompletableFuture<JsonNode> getRetentionPolicies() {
		logger.info("Retrieving current retention policies");
		return powershellClient.executeCmdletCommand(
				new CommandRequest(GET_RETENTION_COMPLIANCE_POLICY, new HashMap<>())
		);
	}

	/**
	 * Gets all current retention compliance rules.
	 *
	 * @return CompletableFuture with the list of retention rules
	 */
	protected CompletableFuture<JsonNode> getRetentionRules() {
		logger.info("Retrieving current retention rules");
		return powershellClient.executeCmdletCommand(
				new CommandRequest(GET_RETENTION_COMPLIANCE_RULE, new HashMap<>())
		);
	}

	/**
	 * Creates a new audit log retention policy.
	 *
	 * @param policyName The name of the policy
	 * @param comment A description of the policy
	 * @param retentionDays The number of days to retain audit logs
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> createRetentionPolicy(String policyName, String comment, int retentionDays) {
		logger.info("Creating retention policy '{}' with retention period of {} days", policyName, retentionDays);

		Map<String, Object> params = new HashMap<>();
		params.put(NAME, policyName);
		params.put(COMMENT, comment);
		params.put(RETENTION_TYPE, AUDIT_LOG);
		params.put(WORKLOAD, ALL_WORKLOADS);

		return powershellClient.executeCmdletCommand(
				new CommandRequest(NEW_RETENTION_COMPLIANCE_POLICY, params)
		);
	}

	/**
	 * Creates a new audit log retention rule.
	 *
	 * @param ruleName The name of the rule
	 * @param policyName The name of the policy to associate with this rule
	 * @param retentionDays The number of days to retain audit logs
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> createRetentionRule(String ruleName, String policyName, int retentionDays) {
		logger.info("Creating retention rule '{}' for policy '{}' with retention period of {} days",
				ruleName, policyName, retentionDays);

		Map<String, Object> params = new HashMap<>();
		params.put(NAME, ruleName);
		params.put(POLICY, policyName);
		params.put(WORKLOAD, ALL_WORKLOADS);
		params.put(RETENTION_DURATION, retentionDays);

		return powershellClient.executeCmdletCommand(
				new CommandRequest(NEW_RETENTION_COMPLIANCE_RULE, params)
		);
	}

	/**
	 * Checks if an audit log retention policy with the specified name exists.
	 *
	 * @param policies The list of policies to check
	 * @param policyName The name of the policy to look for
	 * @return true if the policy exists, false otherwise
	 */
	protected boolean policyExists(JsonNode policies, String policyName) {
		if (policies == null || !policies.isArray()) {
			return false;
		}

		for (JsonNode policy : policies) {
			if (policy.path(NAME).asText("").equals(policyName)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Checks if an audit log retention rule with the specified name exists.
	 *
	 * @param rules The list of rules to check
	 * @param ruleName The name of the rule to look for
	 * @return true if the rule exists, false otherwise
	 */
	protected boolean ruleExists(JsonNode rules, String ruleName) {
		if (rules == null || !rules.isArray()) {
			return false;
		}

		for (JsonNode rule : rules) {
			if (rule.path(NAME).asText("").equals(ruleName)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Creates a success response node.
	 */
	protected JsonNode createSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Creates a partial success response node.
	 */
	protected JsonNode createPartialSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, "partial");
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Creates a failure response node.
	 */
	protected JsonNode createFailureNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}
}