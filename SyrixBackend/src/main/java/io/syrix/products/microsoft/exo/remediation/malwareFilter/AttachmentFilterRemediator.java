package io.syrix.products.microsoft.exo.remediation.malwareFilter;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.datamodel.task.remediation.exchange.AttachmentFileTypeList;
import io.syrix.datamodel.task.remediation.exchange.AttachmentFilterAction;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediates MS.EXO.9.1v2 policy by creating or updating malware filter policies
 * that block emails with risky file type attachments.

 * Implements two policy options:
 * 1. QUARANTINE: Blocks BASE list of high-risk file types with quarantine action
 * 2. REJECT: Blocks combined BASE+EXTENDED file types with reject action
 */
@PolicyRemediator("MS.EXO.9.1v2, MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.5v1")
public class AttachmentFilterRemediator extends MalwareFilterRemediatorBase {
	private static final String RECIPIENT_DOMAIN_IS = "RecipientDomainIs";
	private static final String UNKNOWN_ERROR = "Unknown error";
	public static final String ZAP_ENABLED = "ZapEnabled";
	public static final String FILE_TYPES = "FileTypes";
	public static final String FILE_TYPE_ACTION = "FileTypeAction";
	public static final String ENABLE_FILE_FILTER = "EnableFileFilter";

	private final AttachmentFilterConfig config;

	/**
	 * Constructs a new AttachmentFilterRemediator with the specified PowerShell client
	 * and configuration.
	 *
	 * @param exchangeClient The PowerShell client for Exchange Online operations
	 * @param config The comprehensive configuration for policy and rule
	 */
	public AttachmentFilterRemediator(PowerShellClient exchangeClient, AttachmentFilterConfig config) {
		super(exchangeClient);
		this.config = config;

		// Add default recipient domain condition if no conditions are specified
		ensureRuleHasConditions();
	}

	/**
	 * Convenience constructor that creates a remediator with default configuration
	 * based on the specified action and file type list.
	 *
	 * @param exchangeClient The PowerShell client for Exchange Online operations
	 * @param action The action to take (QUARANTINE or REJECT)
	 * @param fileTypeList The file type list to block
	 */
	public AttachmentFilterRemediator(PowerShellClient exchangeClient,
									  AttachmentFilterAction action,
									  AttachmentFileTypeList fileTypeList) {
		super(exchangeClient);

		// Create appropriate config based on action
		if (action == AttachmentFilterAction.REJECT) {
			this.config = AttachmentFilterConfig.createRejectConfig(fileTypeList);
		} else {
			this.config = AttachmentFilterConfig.createQuarantineConfig(fileTypeList);
		}

		// Add default recipient domain condition if no conditions are specified
		ensureRuleHasConditions();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for attachment filter policy: {}", config.getName());
		logger.info("Will block {} file extensions with {} action",
				config.getFilesTypeList().getCount(),
				config.getAction().getAction());

		// First check if policy exists using base class method
		return checkPolicyExists(config.getName())
				.thenCompose(policyExists -> {
					if (Boolean.TRUE.equals(policyExists)) {
						// Policy exists, check if it's correctly configured
						return getMalwareFilterPolicy(config.getName())
								.thenCompose(policyConfig -> {
									CompletableFuture<JsonNode> policyUpdateFuture =
											CompletableFuture.completedFuture(null);

									if (!isPolicyCorrectlyConfigured(policyConfig)) {
										// Policy exists but needs updating
										logger.info("Attachment filter policy '{}' exists but needs updating",
												config.getName());
										policyUpdateFuture = updateMalwareFilterPolicy(config.getName(),
												buildPolicyParameters());
									} else {
										logger.info("Attachment filter policy '{}' is correctly configured",
												config.getName());
									}

									return policyUpdateFuture.thenCompose(result ->
											// Then check if rule exists
											checkRuleExists()
													.thenCompose(ruleExists -> {
														if (Boolean.TRUE.equals(ruleExists)) {
															// Rule exists, update it
															logger.info("Attachment filter rule exists - updating");
															return updateAttachmentFilterRule();
														} else {
															// Rule doesn't exist, create it
															logger.info("Attachment filter rule doesn't exist - creating");
															return createAttachmentFilterRule();
														}
													})
									).thenApply(result -> IPolicyRemediator.success(
											getPolicyId(),
											"Successfully updated attachment filter policy and rule: " + config.getName()
									).join());
								});
					} else {
						// Policy doesn't exist, create it using base class method
						logger.info("Creating attachment filter policy: {}", config.getName());
						return createMalwareFilterPolicy(config.getName(), buildPolicyParameters())
								.thenCompose(policyResult -> createAttachmentFilterRule())
								.thenApply(result -> IPolicyRemediator.success(
										getPolicyId(),
										"Successfully created attachment filter policy and rule: " + config.getName()
								).join());
					}
				})
				.exceptionally(ex -> {
					logger.error("Exception during attachment filter policy remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	@Override
	protected boolean isPolicyCorrectlyConfigured(JsonNode policyConfig) {
		// Check if the policy is correctly configured for attachment filtering
		try {
			// Check if file filter is enabled
			if (!policyConfig.has(ENABLE_FILE_FILTER) || !policyConfig.get(ENABLE_FILE_FILTER).asBoolean()) {
				logger.info("EnableFileFilter is not enabled");
				return false;
			}

			// Check if file type action matches the configured action
			if (!policyConfig.has(FILE_TYPE_ACTION) ||
					!policyConfig.get(FILE_TYPE_ACTION).asText().equals(config.getAction().getAction())) {
				logger.info("FileTypeAction doesn't match configured action");
				return false;
			}

			// Check if all required file types are included
			if (!policyConfig.has(FILE_TYPES)) {
				logger.info("FileTypes is missing");
				return false;
			}

			// Convert JsonNode array to a list of strings for easier comparison
			List<String> configuredFileTypes = config.getFilesTypeList().getFileTypeList();
			List<String> currentFileTypes = new ArrayList<>();

			JsonNode fileTypesNode = policyConfig.get(FILE_TYPES);
			if (fileTypesNode.isArray()) {
				for (JsonNode fileType : fileTypesNode) {
					currentFileTypes.add(fileType.asText());
				}
			}

			// Check if all required file types are in the current configuration
			for (String requiredType : configuredFileTypes) {
				if (!currentFileTypes.contains(requiredType)) {
					logger.info("Required file type {} is missing", requiredType);
					return false;
				}
			}

			// If ZapEnabled is specified in the config, check if it matches
			if (config.getZapEnabled() != null &&
					(!policyConfig.has(ZAP_ENABLED) ||
							policyConfig.get(ZAP_ENABLED).asBoolean() != config.getZapEnabled())) {
				logger.info("ZapEnabled setting doesn't match");
				return false;
			}

			// Policy is correctly configured
			return true;
		} catch (Exception e) {
			logger.warn("Error checking if policy is correctly configured", e);
			return false;
		}
	}

	@Override
	protected Map<String, Object> buildPolicyParameters() {
		// Build parameters for creating or updating the policy
		Map<String, Object> parameters = new HashMap<>();

		parameters.put(ENABLE_FILE_FILTER, true);
		parameters.put(FILE_TYPE_ACTION, config.getAction().getAction());
		parameters.put(FILE_TYPES, config.getFilesTypeList().getFileTypeList().toArray(new String[0]));

		// Add admin display name if available
		if (config.getAdminDisplayName() != null) {
			parameters.put("AdminDisplayName", config.getAdminDisplayName());
		}

		// Add ZapEnabled if available (Exchange Online specific)
		if (config.getZapEnabled() != null) {
			parameters.put(ZAP_ENABLED, config.getZapEnabled());
		}

		return parameters;
	}

	/**
	 * Ensures that the rule has at least one condition specified.
	 * Exchange requires at least one condition for creating a rule.
	 */
	private void ensureRuleHasConditions() {
		// If no conditions are set, add a default to apply to all recipients
		if ((config.getRecipientDomainIs() == null || config.getRecipientDomainIs().isEmpty()) &&
				(config.getSentTo() == null || config.getSentTo().isEmpty()) &&
				(config.getSentToMemberOf() == null || config.getSentToMemberOf().isEmpty())) {

			// Use "*" to apply to all domains - this is the default behavior we want
			config.setRecipientDomainIs(List.of("*"));
			logger.info("Added default recipient domain condition '*' to apply rule to all domains");
		}
	}

	/**
	 * Checks if the attachment filter rule exists
	 *
	 * @return A CompletableFuture containing a boolean indicating if the rule exists
	 */
	private CompletableFuture<Boolean> checkRuleExists() {
		String ruleName = getRuleName();
		logger.info("Checking if attachment filter rule exists: {}", ruleName);

		Map<String, Object> parameters = Map.of(IDENTITY, ruleName);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Get-MalwareFilterRule", parameters))
				.thenApply(result -> {
					boolean exists = (result != null && !result.has(Constants.ERROR_FIELD));
					logger.info("Rule {} {}", ruleName, exists ? "exists" : "does not exist");
					return exists;
				})
				.exceptionally(ex -> {
					logger.debug("Rule {} does not exist or error checking", ruleName, ex);
					return false;
				});
	}

	/**
	 * Creates a new malware filter rule to apply the policy
	 *
	 * @return A CompletableFuture containing the result of the rule creation
	 */
	private CompletableFuture<JsonNode> createAttachmentFilterRule() {
		String ruleName = getRuleName();
		logger.info("Creating malware filter rule: {}", ruleName);

		// Create minimal parameter set for rule creation to avoid conflicts
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Name", ruleName);
		parameters.put("MalwareFilterPolicy", config.getName());

		// Set the priority to 0 (only valid value for first rule)
		parameters.put("Priority", 0);

		// Add condition - RecipientDomainIs is required
		if (config.getRecipientDomainIs() != null && !config.getRecipientDomainIs().isEmpty()) {
			parameters.put(RECIPIENT_DOMAIN_IS, config.getRecipientDomainIs().toArray(new String[0]));
		} else {
			// If no RecipientDomainIs condition set, use the default "*"
			parameters.put(RECIPIENT_DOMAIN_IS, new String[]{"*"});
		}

		logger.info("Using {} parameters for rule creation", parameters.size());

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("New-MalwareFilterRule", parameters))
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully created attachment filter rule: {}", ruleName);
						return result;
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ?
								result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.error("Failed to create attachment filter rule: {}", error);
						throw new ExoRemediationException("Failed to create attachment filter rule: " + error);
					}
				});
	}

	/**
	 * Updates an existing malware filter rule
	 *
	 * @return A CompletableFuture containing the result of the rule update
	 */
	private CompletableFuture<JsonNode> updateAttachmentFilterRule() {
		String ruleName = getRuleName();
		logger.info("Updating malware filter rule: {}", ruleName);

		Map<String, Object> parameters = buildRuleUpdateParameters(ruleName);

		if (parameters.size() <= 1) {
			logger.info("No changes needed for rule: {}", ruleName);
			return CompletableFuture.completedFuture(null);
		}

		logger.info("Using {} parameters for rule update", parameters.size());
		return executeRuleUpdate(parameters);
	}

	private Map<String, Object> buildRuleUpdateParameters(String ruleName) {
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Identity", ruleName);

		addMalwareFilterPolicyIfNeeded(parameters);
		addRecipientConditions(parameters);

		return parameters;
	}

	private void addMalwareFilterPolicyIfNeeded(Map<String, Object> parameters) {
		if (config.getMalwareFilterPolicyName() != null &&
				!config.getName().equals(config.getMalwareFilterPolicyName())) {
			parameters.put("MalwareFilterPolicy", config.getName());
		}
	}

	private void addRecipientConditions(Map<String, Object> parameters) {
		addRecipientDomainCondition(parameters);
		addSentToCondition(parameters);
		addSentToMemberOfCondition(parameters);
	}

	private void addRecipientDomainCondition(Map<String, Object> parameters) {
		if (config.getRecipientDomainIs() != null && !config.getRecipientDomainIs().isEmpty()) {
			parameters.put(RECIPIENT_DOMAIN_IS, config.getRecipientDomainIs().toArray(new String[0]));
		}
	}

	private void addSentToCondition(Map<String, Object> parameters) {
		if (config.getSentTo() != null && !config.getSentTo().isEmpty()) {
			parameters.put("SentTo", config.getSentTo().toArray(new String[0]));
		}
	}

	private void addSentToMemberOfCondition(Map<String, Object> parameters) {
		if (config.getSentToMemberOf() != null && !config.getSentToMemberOf().isEmpty()) {
			parameters.put("SentToMemberOf", config.getSentToMemberOf().toArray(new String[0]));
		}
	}

	private CompletableFuture<JsonNode> executeRuleUpdate(Map<String, Object> parameters) {
		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Set-MalwareFilterRule", parameters))
				.thenApply(result -> {
					String ruleName = parameters.get("Identity").toString();
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully updated attachment filter rule: {}", ruleName);
						return result;
					}

					String error = result != null && result.has(Constants.ERROR_FIELD) ?
							result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
					logger.error("Failed to update attachment filter rule: {}", error);
					throw new ExoRemediationException("Failed to update attachment filter rule: " + error);
				});
	}

	/**
	 * Gets the rule name for this policy
	 *
	 * @return The rule name
	 */
	private String getRuleName() {
		return config.getName() + " Rule";
	}
}