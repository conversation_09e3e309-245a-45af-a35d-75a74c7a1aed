package io.syrix.products.microsoft.teams;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.teams.model.TeamAppPermissionPolicy;
import io.syrix.products.microsoft.teams.model.TeamsClientConfiguration;
import io.syrix.products.microsoft.teams.model.TeamsMeetingBroadcastPolicy;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.products.microsoft.teams.model.TeamsTenantInfo;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.ClientConfiguration;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingBroadcastPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;
import io.syrix.protocols.client.teams.powershell.command.types.TenantInfo;
import io.syrix.protocols.utils.Retry;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static io.syrix.products.microsoft.teams.TeamsConstants.*;

/**
 * Service for managing and exporting Microsoft Teams configurations.
 * Provides functionality for retrieving Teams settings, policies, and configurations.
 */
public class TeamsConfigurationService extends BaseConfigurationService {
    private static final int MAX_RETRY = 3;

    private static final String SERVICE_VERSION = "1.0";
    private final PowerShellTeamsClient powershellClient;

    private final ObjectMapper jsonMapper;

    public TeamsConfigurationService(MicrosoftGraphClient graphClient, PowerShellTeamsClient powershellClient) {
        this(graphClient, powershellClient, new ObjectMapper(), new MetricsCollector());
    }

    public TeamsConfigurationService(
            MicrosoftGraphClient graphClient,
            PowerShellTeamsClient powershellClient,
            ObjectMapper objectMapper,
            MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
        this.powershellClient = powershellClient;
        this.jsonMapper = new ObjectMapper();
        this.jsonMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);

    }

    @Override
    public ConfigurationResult exportConfiguration() {
        Instant startTime = Instant.now();
        metrics.recordExportStart();
        logger.info("Starting Teams configuration export at {}", startTime);

        try {
            // Create futures for all configuration components
            Map<String, CompletableFuture<?>> futures = new HashMap<>();
            futures.put(CONFIG_KEY_TEAMS_TENANT_INFO, getTeamsTenantInfo()); //done, but need fix with the symbol \ in the date
            futures.put(CONFIG_KEY_MEETING_POLICIES, getMeetingPolicies()); //done
            futures.put(CONFIG_KEY_FEDERATION_CONFIGURATION, getFederationConfig()); //done
            futures.put(CONFIG_KEY_CLIENT_CONFIGURATION, getClientConfig()); //done
            futures.put(CONFIG_KEY_APP_POLICIES, getAppPolicies());
            futures.put(CONFIG_KEY_BROADCAST_POLICIES, getBroadcastPolicies()); //done

            // Wait for all futures to complete with timeout
            ConfigurationResult result = waitForFutures(futures).thenApply(map -> {
                map.put("teams_successful_commands", getSuccessfulCommands());
                map.put("teams_unsuccessful_commands", getUnsuccessfulCommands());
                return buildConfigurationResult(map, SERVICE_VERSION, ConfigurationServiceType.TEAMS);
            }).get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

            // Record success metrics
            Duration duration = Duration.between(startTime, Instant.now());
            metrics.recordExportSuccess(duration);
            logger.info("Teams configuration export completed successfully in {} seconds", duration.toSeconds());
            return result;

        } catch (Exception e) {
            metrics.recordExportFailure();
            logger.error("Teams configuration export failed", e);
            throw new ConfigurationExportException("Teams configuration export failed", e);
        }
    }

    private CompletableFuture<JsonNode> getTeamsTenantInfo() {
        final CsTeamsCommand<TenantInfo> command = CsTeamsCommand.CsTenant.GET();


        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(tenantInfoList -> {
                    try {
                        TenantInfo tenantInfo = tenantInfoList.getFirst();
                        String tenantId = graphClient.getTenantId(); //attention. we don't get this in the answer
                        TeamsTenantInfo teamsTenantInfo = new TeamsTenantInfo(tenantInfo, tenantId);
                        List<TeamsTenantInfo> teamsTenantInfos = List.of(teamsTenantInfo);
                        JsonNode resultNode = jsonMapper.valueToTree(teamsTenantInfos);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                }).exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<JsonNode> getMeetingPolicies() {
        CsTeamsCommand<MeetingPolicy> command = CsTeamsCommand.CsTeamsMeetingPolicy.GET();

        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(meetingPolicies -> {
                    try {
                        List<TeamsMeetingPolicy> teamsMeetingPolicies = meetingPolicies.stream()
                                .map(TeamsMeetingPolicy::new)
                                .toList();
                        JsonNode resultNode = jsonMapper.valueToTree(teamsMeetingPolicies);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                }).exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<JsonNode> getFederationConfig() {
        final CsTeamsCommand<TenantFederationConfiguration> command = CsTeamsCommand.CsTenantFederationConfiguration.GET();

        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(tenantFederationConfigurations -> {
                    try {
                        List<TeamsTenantFederationConfiguration> configurations = tenantFederationConfigurations.stream()
                                .map((TeamsTenantFederationConfiguration::new))
                                .toList();
                        JsonNode resultNode = jsonMapper.valueToTree(configurations);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<JsonNode> getClientConfig() {
        final CsTeamsCommand<ClientConfiguration> command = CsTeamsCommand.CsTeamsClientConfiguration.GET();

        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(clientConfigurations -> {
                    try {
                        List<TeamsClientConfiguration> configurations = clientConfigurations.stream().map(TeamsClientConfiguration::new).toList();
                        JsonNode resultNode = jsonMapper.valueToTree(configurations);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<JsonNode> getAppPolicies() {
        final CsTeamsCommand<AppPermissionPolicy> command = CsTeamsCommand.CsTeamsAppPermissionPolicy.GET();

        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(appPermissionPolicies -> {
                    try {
                        List<TeamAppPermissionPolicy> configurations = appPermissionPolicies.stream().map(TeamAppPermissionPolicy::new).toList();
                        JsonNode resultNode = jsonMapper.valueToTree(configurations);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<JsonNode> getBroadcastPolicies() {
        final CsTeamsCommand<MeetingBroadcastPolicy> command = CsTeamsCommand.CsTeamsMeetingBroadcastPolicy.GET();

        return Retry.executeWithRetry(() -> powershellClient.execute(command), MAX_RETRY)
                .thenCompose(meetingBroadcastPolicies -> {
                    try {
                        List<TeamsMeetingBroadcastPolicy> configurations = meetingBroadcastPolicies.stream().map(TeamsMeetingBroadcastPolicy::new).toList();
                        JsonNode resultNode = jsonMapper.valueToTree(configurations);
                        successfulCommands.add(command.getName());
                        return CompletableFuture.completedFuture(resultNode);
                    } catch (Exception ex) {
                        logger.error("Unable to post-process command: {}", command.getName(), ex);
                        return CompletableFuture.failedFuture(ex);
                    }
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

}