package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static io.syrix.common.constants.Constants.MEMBERS_VALUE;

public class DefenderHelpers {

	public static CompletableFuture<List<JsonNode>> getEOPProtectionPolicyRule(PowerShellClient powershellClient, String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_EOP_PROTECTION_POLICY_RULE,
						Map.of() // No parameters - get all rules
				)
		).thenApply(
				response -> DefenderHelpers.filterRulesByName(response, presetType)
		);
	}


	public static CompletableFuture<List<JsonNode>> getATPProtectionPolicyRule(PowerShellClient powershellClient, String presetType) {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_ATP_PROTECTION_POLICY_RULE,
						Map.of() // No parameters - get all rules
				)
		).thenApply(
				response -> filterRulesByName(response, presetType)
		);
	}


	public static List<JsonNode> filterRulesByName(JsonNode rules, String presetType) {
		if (rules == null || rules.isEmpty()) {
			return List.of();
		}

		List<JsonNode> filteredRules = StreamSupport.stream(rules.spliterator(), false)
				.filter(rule -> {
					String name = rule.path("Name").asText("");
					return name.contains(presetType);
				}).toList();
		return filteredRules;
	}

	/**
	 * Get all mailboxes except discovery mailboxes
	 */
	public static CompletableFuture<List<JsonNode>> getAllMailboxes(PowerShellClient powershellClient, Logger logger) {
		Map<String, Object> params = new HashMap<>();
		params.put(DefenderConstants.RESULT_SIZE_PARAM, Constants.UNLIMITED_VALUE);

		PowerShellClient.CommandRequest command = new PowerShellClient.CommandRequest(DefenderConstants.GET_MAILBOX_CMDLET, params);

		return powershellClient.executeCmdletCommand(command)
				.thenApply(result -> {
					if (result == null || result.has(Constants.ERROR_FIELD)) {
						String error = result != null ? result.get(Constants.ERROR_FIELD).asText() : "No result from PowerShell";
						logger.error("Error getting mailboxes: {}", error);
						return new ArrayList<JsonNode>();
					}

					List<JsonNode> mailboxes = new ArrayList<>();
					if (result.isArray()) {
						for (JsonNode mailbox : result) {
							// Filter out discovery mailboxes
							if (mailbox.has(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD) &&
									!mailbox.get(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText().equals(DefenderConstants.DISCOVERY_MAILBOX_TYPE)) {
								mailboxes.add(mailbox);
							}
						}
					} else if (result.has(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD) &&
							!result.get(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText().equals(DefenderConstants.DISCOVERY_MAILBOX_TYPE)) {
						mailboxes.add(result);
					}

					logger.info("Found {} mailboxes", mailboxes.size());
					return mailboxes;
				});
	}

	/**
	 * Filter out excluded users from the list
	 */
	public static CompletableFuture<List<JsonNode>> filterExcludedUsers(List<JsonNode> allUsers, List<String> excludedUsers) {
		List<JsonNode> filteredUsers = new ArrayList<>();

		for (JsonNode user : allUsers) {
			if (user.has(DefenderConstants.USER_PRINCIPAL_NAME_FIELD)) {
				String upn = user.get(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText();
				if (!excludedUsers.contains(upn)) {
					filteredUsers.add(user);
				}
			} else {
				// If UPN is missing, we still include the user as we can't determine if it should be excluded
				filteredUsers.add(user);
			}
		}
		return CompletableFuture.completedFuture(filteredUsers);
	}

	/**
	 * Apply users to a Protection Policy Rule (ATP or EOP)
	 *
	 * @param powershellClient PowerShell client to execute commands
	 * @param logger           Logger for logging
	 * @param policyId         Policy ID for response messages
	 * @param ruleType         Rule type ("ATP" or "EOP")
	 * @param presetType       Preset type ("Standard" or "Strict")
	 * @param users            List of users to apply to the rule
	 * @return CompletableFuture with JSON response
	 */
	public static CompletableFuture<JsonNode> applyUsersToProtectionPolicyRule(
			PowerShellClient powershellClient,
			Logger logger,
			String policyId,
			RuleType ruleType,
			String presetType,
			List<JsonNode> users,
			ObjectMapper objectMapper) {

		if (users == null || users.isEmpty()) {
			logger.warn("No users to apply to the {} {} preset policy", ruleType.name(), presetType);
			return CompletableFuture.completedFuture(createErrorResponse(policyId,
					"No users to apply to the " + presetType + " preset policy",
					objectMapper));
		}

		// Get appropriate rule based on type
		CompletableFuture<List<JsonNode>> getRulesFuture;
		String cmdlet;

		if (ruleType == RuleType.ATP) {
			getRulesFuture = getATPProtectionPolicyRule(powershellClient, presetType);
			cmdlet = DefenderConstants.SET_ATP_PROTECTION_POLICY_RULE;
		} else if (ruleType == RuleType.EOP) {
			getRulesFuture = getEOPProtectionPolicyRule(powershellClient, presetType);
			cmdlet = DefenderConstants.SET_EOP_PROTECTION_POLICY_RULE;
		} else {
			String error = "Invalid rule type: " + ruleType + ". Must be 'ATP' or 'EOP'.";
			logger.error(error);
			return CompletableFuture.completedFuture(createErrorResponse(policyId, error, objectMapper));
		}

		return getRulesFuture.thenCompose(rules -> {
			if (rules == null || rules.isEmpty()) {
				String error = "No " + ruleType + " Protection Policy Rule found for " + presetType +
						". Ensure the policy is enabled and you have the appropriate license.";
				logger.error(error);
				return CompletableFuture.completedFuture(createErrorResponse(policyId, error, objectMapper));
			}

			JsonNode matchingRule = rules.getFirst();

			// Build the recipient filter
			StringBuilder filterBuilder = new StringBuilder();
			boolean first = true;

			for (JsonNode user : users) {
				if (!first) {
					filterBuilder.append(") -or (");
				} else {
					filterBuilder.append("(");
					first = false;
				}

				String recipientType = user.has(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD) ?
						user.get(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText() : "UserMailbox";
				String objectId = user.has(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD) ?
						user.get(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD).asText() : "";

				filterBuilder.append("RecipientTypeDetails -eq '").append(recipientType)
						.append("' -and -not(Name -like 'SystemMailbox{*') -and -not(Name -like 'CAS_{*')")
						.append(" -and (ExternalDirectoryObjectId -eq '").append(objectId).append("'");
			}

			filterBuilder.append(")");

			// Apply the filter to the rule
			Map<String, Object> setParams = new HashMap<>();
			setParams.put(DefenderConstants.IDENTITY_PROPERTY, matchingRule.get(DefenderConstants.IDENTITY_PROPERTY).asText());

			PowerShellClient.CommandRequest setCommand = new PowerShellClient.CommandRequest(cmdlet, setParams);

			return powershellClient.executeCmdletCommand(setCommand)
					.thenApply(result -> {
						if (result == null || result.has(Constants.ERROR_FIELD)) {
							String error = result != null ?
									result.get(Constants.ERROR_FIELD).asText() :
									"Unknown error applying users to " + presetType + " preset policy";
							logger.error("Failed to apply users to {} {} preset policy: {}", ruleType, presetType, error);
							return createErrorResponse(policyId, error, objectMapper);
						}

						logger.info("Successfully applied users to {} {} preset policy", ruleType, presetType);
						return createSuccessResponse(policyId,
								users.size(),
								ruleType.name(),
								presetType,
								objectMapper,
								"Successfully applied users to " + presetType + " preset policy");
					});
		});
	}


	/**
	 * Filter out members of excluded groups
	 */
	public static CompletableFuture<List<JsonNode>> filterExcludedGroupMembers(List<JsonNode> users,
																			   List<String> excludedGroups,
																			   PowerShellClient powershellClient,
																			   Logger logger) {
		if (excludedGroups.isEmpty()) {
			// No groups to filter, return the users as is
			return CompletableFuture.completedFuture(users);
		}

		// Process each group sequentially
		CompletableFuture<List<JsonNode>> result = CompletableFuture.completedFuture(new ArrayList<>(users));

		for (String groupName : excludedGroups) {
			result = result.thenCompose(currentUsers -> filterGroupMembers(currentUsers, groupName, powershellClient, logger));
		}

		return result;
	}

	/**
	 * Filter out members of a specific group
	 */
	public static CompletableFuture<List<JsonNode>> filterGroupMembers(List<JsonNode> users,
																	   String groupName,
																	   PowerShellClient powershellClient,
																	   Logger logger) {
		// First get the group
		Map<String, Object> params = new HashMap<>();
		params.put(DefenderConstants.IDENTITY_PROPERTY, groupName);

		PowerShellClient.CommandRequest getGroupCommand = new PowerShellClient.CommandRequest(DefenderConstants.GET_UNIFIED_GROUP_CMDLET, params);

		return powershellClient.executeCmdletCommand(getGroupCommand)
				.thenCompose(groupResult -> {
					if (groupResult == null || groupResult.has(Constants.ERROR_FIELD)) {
						logger.warn("Could not retrieve group '{}': {}",
								groupName,
								groupResult != null ? groupResult.get(Constants.ERROR_FIELD).asText() : "No result");
						// If we can't get the group, we keep all users
						return CompletableFuture.completedFuture(users);
					}

					// Get the group name or identity to use for getting members
					String groupIdentity = groupResult.has(DefenderConstants.NAME_FIELD) ?
							groupResult.get(DefenderConstants.NAME_FIELD).asText() : groupName;

					// Now get the group members
					Map<String, Object> memberParams = new HashMap<>();
					memberParams.put(DefenderConstants.IDENTITY_PROPERTY, groupIdentity);
					memberParams.put(DefenderConstants.LINK_TYPE_PARAM, MEMBERS_VALUE);
					memberParams.put(DefenderConstants.RESULT_SIZE_PARAM, Constants.UNLIMITED_VALUE);

					PowerShellClient.CommandRequest getMembersCommand =
							new PowerShellClient.CommandRequest(DefenderConstants.GET_UNIFIED_GROUP_LINKS_CMDLET, memberParams);

					return powershellClient.executeCmdletCommand(getMembersCommand)
							.thenApply(membersResult -> {
								if (membersResult == null || membersResult.has(Constants.ERROR_FIELD)) {
									logger.warn("Could not retrieve members for group '{}': {}",
											groupName,
											membersResult != null ? membersResult.get(Constants.ERROR_FIELD).asText() : "No result");
									// If we can't get the members, we keep all users
									return users;
								}

								// Get all member email addresses
								List<String> memberEmails = new ArrayList<>();
								if (membersResult.isArray()) {
									for (JsonNode member : membersResult) {
										if (member.has(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD)) {
											memberEmails.add(member.get(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD).asText());
										}
									}
								} else if (membersResult.has(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD)) {
									memberEmails.add(membersResult.get(DefenderConstants.PRIMARY_SMTP_ADDRESS_FIELD).asText());
								}

								// Filter out users who are members of the group
								List<JsonNode> filteredUsers = new ArrayList<>();
								for (JsonNode user : users) {
									if (user.has(DefenderConstants.USER_PRINCIPAL_NAME_FIELD) &&
											!memberEmails.contains(user.get(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText())) {
										filteredUsers.add(user);
									}
								}

								logger.info("After filtering members of group '{}': {} users remaining",
										groupName, filteredUsers.size());
								return filteredUsers;
							});
				});
	}

	/**
	 * Check if a policy matches the specified preset type
	 */
	public static boolean isMatchingPolicy(JsonNode policy, String presetType) {
		// First check the RecommendedPolicyType field
		if (policy.has("RecommendedPolicyType") &&
				policy.get("RecommendedPolicyType").asText().equalsIgnoreCase(presetType)) {
			return true;
		}

		// Then check if the name contains the preset type
		if (policy.has(DefenderConstants.NAME_FIELD)) {
			String name = policy.get(DefenderConstants.NAME_FIELD).asText();
			return name.contains(presetType) && name.contains("Preset Security Policy");
		}

		return false;
	}

	/**
	 * Get the AntiPhish policies, filtered by type (Standard or Strict)
	 */
	public static CompletableFuture<JsonNode> getAntiPhishPolicies(PowerShellClient powershellClient, String presetType) {
		// Get all anti-phish policies
		Map<String, Object> params = new HashMap<>();

		return powershellClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								DefenderConstants.GET_ANTI_PHISH_POLICY,
								params
						))
				.thenApply(result -> {
					if (result == null || result.has(Constants.ERROR_FIELD)) {
						return result;
					}

					// Filter by RecommendedPolicyType or Name containing the preset type
					if (result.isArray()) {
						for (JsonNode policy : result) {
							if (isMatchingPolicy(policy, presetType)) {
								// Create a new array with just this one policy
								ObjectMapper mapper = new ObjectMapper();
								return mapper.createArrayNode().add(policy);
							}
						}
					} else if (isMatchingPolicy(result, presetType)) {
						return result;
					}

					// No matching policy found
					ObjectMapper mapper = new ObjectMapper();
					ObjectNode errorNode = mapper.createObjectNode();
					errorNode.put(Constants.ERROR_FIELD, "No AntiPhishPolicy found for " + presetType);
					return errorNode;
				});
	}

	/**
	 * Create a standardized success response with detailed information
	 */
	public static JsonNode createSuccessResponse(String policyId,
												 int userCount,
												 String ruleType,
												 String presetType,
												 ObjectMapper objectMapper,
												 String message) {
		ObjectNode response = objectMapper.createObjectNode();
		response.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
		response.put(Constants.POLICY_ID_FIELD, policyId);
		response.put(Constants.MESSAGE_FIELD, message);

		// Add detailed information
		ObjectNode details = objectMapper.createObjectNode();
		details.put("ruleType", ruleType);
		details.put("presetType", presetType);
		details.put("timestamp", System.currentTimeMillis());
		details.put("userCount", userCount);

		response.set(Constants.DETAILS_FIELD, details);
		return response;
	}

	/**
	 * Create a standardized error response
	 */
	public static JsonNode createErrorResponse(String policyId,
											   String errorMessage,
											   ObjectMapper objectMapper) {
		ObjectNode response = objectMapper.createObjectNode();
		response.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
		response.put(Constants.POLICY_ID_FIELD, policyId);
		response.put(Constants.ERROR_FIELD, errorMessage != null ? errorMessage : DefenderConstants.ERROR_UNKNOWN);
		return response;
	}
}
