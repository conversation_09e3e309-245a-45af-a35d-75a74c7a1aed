package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.List;

/**
 * Represents the configuration of a dynamic security group in Microsoft Entra ID.
 * This model is used for both reading existing groups and creating new dynamic groups.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicGroupConfiguration {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("displayName")
    private String displayName;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("groupTypes")
    private List<String> groupTypes;
    
    @JsonProperty("securityEnabled")
    private Boolean securityEnabled;
    
    @JsonProperty("mailEnabled")
    private Boolean mailEnabled;
    
    @JsonProperty("membershipRule")
    private String membershipRule;
    
    @JsonProperty("membershipRuleProcessingState")
    private String membershipRuleProcessingState;
    
    @JsonProperty("mailNickname")
    private String mailNickname;
    
    @JsonProperty("createdDateTime")
    private Instant createdDateTime;
    
    /**
     * Default constructor
     */
    public DynamicGroupConfiguration() {
    }
    
    /**
     * Constructor for creating a new dynamic guest user group configuration
     * 
     * @param displayName The display name for the group
     * @param description The description for the group
     */
    public DynamicGroupConfiguration(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
        this.groupTypes = List.of("DynamicMembership");
        this.securityEnabled = true;
        this.mailEnabled = false;
        this.membershipRule = "(user.userType -eq \"Guest\")";
        this.membershipRuleProcessingState = "On";
        // Generate mail nickname from display name (lowercase, no spaces)
        String cleanedNickname = displayName.toLowerCase().replaceAll("[^a-z0-9]", "");
        this.mailNickname = cleanedNickname.substring(0, Math.min(cleanedNickname.length(), 64));
    }
    
    /**
     * Checks if this group configuration represents a properly configured dynamic guest user group
     * 
     * @return true if the group is properly configured for guest users
     */
    public boolean isProperlyConfiguredForGuestUsers() {
        return securityEnabled != null && securityEnabled &&
               mailEnabled != null && !mailEnabled &&
               groupTypes != null && groupTypes.contains("DynamicMembership") &&
               isValidGuestUserRule(membershipRule) &&
               "On".equals(membershipRuleProcessingState);
    }
    
    /**
     * Checks if the membership rule is valid for guest users
     * 
     * @param rule The membership rule to validate
     * @return true if the rule correctly identifies guest users
     */
    private boolean isValidGuestUserRule(String rule) {
        if (rule == null) {
            return false;
        }
        
        // Standard guest user rule variations
        return rule.equals("(user.userType -eq \"Guest\")") ||
               rule.equals("(user.userType -eq 'Guest')") ||
               rule.equals("user.userType -eq \"Guest\"") ||
               rule.equals("user.userType -eq 'Guest'");
    }
    
    /**
     * Checks if this group appears to be intended for guest users but is misconfigured
     * 
     * @return true if the group seems to be for guests but has configuration issues
     */
    public boolean isMisconfiguredGuestGroup() {
        if (groupTypes != null && groupTypes.contains("DynamicMembership")) {
            // Check if name or description suggests it's for guests
            boolean suggestsGuests = (displayName != null && displayName.toLowerCase().contains("guest")) ||
                                   (description != null && description.toLowerCase().contains("guest"));
            
            return suggestsGuests && !isProperlyConfiguredForGuestUsers();
        }
        return false;
    }
    
    // Getters and setters
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<String> getGroupTypes() {
        return groupTypes;
    }
    
    public void setGroupTypes(List<String> groupTypes) {
        this.groupTypes = groupTypes;
    }
    
    public Boolean getSecurityEnabled() {
        return securityEnabled;
    }
    
    public void setSecurityEnabled(Boolean securityEnabled) {
        this.securityEnabled = securityEnabled;
    }
    
    public Boolean getMailEnabled() {
        return mailEnabled;
    }
    
    public void setMailEnabled(Boolean mailEnabled) {
        this.mailEnabled = mailEnabled;
    }
    
    public String getMembershipRule() {
        return membershipRule;
    }
    
    public void setMembershipRule(String membershipRule) {
        this.membershipRule = membershipRule;
    }
    
    public String getMembershipRuleProcessingState() {
        return membershipRuleProcessingState;
    }
    
    public void setMembershipRuleProcessingState(String membershipRuleProcessingState) {
        this.membershipRuleProcessingState = membershipRuleProcessingState;
    }
    
    public String getMailNickname() {
        return mailNickname;
    }
    
    public void setMailNickname(String mailNickname) {
        this.mailNickname = mailNickname;
    }
    
    public Instant getCreatedDateTime() {
        return createdDateTime;
    }
    
    public void setCreatedDateTime(Instant createdDateTime) {
        this.createdDateTime = createdDateTime;
    }
    
    @Override
    public String toString() {
        return "DynamicGroupConfiguration{" +
                "id='" + id + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                ", groupTypes=" + groupTypes +
                ", securityEnabled=" + securityEnabled +
                ", mailEnabled=" + mailEnabled +
                ", membershipRule='" + membershipRule + '\'' +
                ", membershipRuleProcessingState='" + membershipRuleProcessingState + '\'' +
                ", mailNickname='" + mailNickname + '\'' +
                ", createdDateTime='" + createdDateTime + '\'' +
                '}';
    }
}