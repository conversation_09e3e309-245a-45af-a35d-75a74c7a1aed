package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.12.1v1 (No IP Allow Lists) in Exchange Online.
 * <p>
 * This class ensures compliance by:
 * - Clearing all IP allow lists (MS.EXO.12.1v1)
 */
@SuppressWarnings("unused")
@PolicyRemediator("MS.EXO.12.1v1")
public class ExchangeIPAllowListRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	private static final String UNKNOWN_ERROR = "Unknown error";

	public ExchangeIPAllowListRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeIPAllowListRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (No IP Allow Lists)", getPolicyId());

		List<ConnectionPolicy> policies = loadConnectionPolicy();
		if (policies.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_CONNECTION_FILTER_POLICIES_FOUND));
		}

		List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
				.filter(policy -> policy.ipAllowList != null && policy.ipAllowList.length > 0)
				.map(policy -> updatePolicy(policy.identity, policy.ipAllowList, new String[0]))
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (No IP Allow Lists)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
					continue;
				}

				String[] parts = change.getParameter().split(":");
				String identity = parts[1].split(",")[0];
				String[] prevValue = change.getPrevValue().toString().split(",");
				String[] newValue = change.getNewValue().toString().split(",");
				// Rolling back: restore original value by swapping newValue and prevValue
				results.add(updatePolicy(identity, newValue, prevValue));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> updatePolicy(String identity, String[] prevValue, String[] newValue) {
		// Ensure prevValue is not null to avoid NPE in logs and parameter change result
		prevValue = prevValue != null ? prevValue : new String[0];

		logger.info("Updating connection filter policy '{}' to clear IP allow list", identity);

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.IDENTITY + ":" + identity + ",params: IPAllowList")
				.prevValue(String.join(",", prevValue))
				.newValue(String.join(",", newValue));

		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Identity", identity);
		parameters.put("IPAllowList", newValue);

		return exchangeClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest("Set-HostedConnectionFilterPolicy", parameters)
				)
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.debug("Connection filter policy '{}' updated successfully", identity);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Connection filter policy '" + identity + "' updated successfully", List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.debug("Failed to update connection filter policy '{}': {}", identity, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to update connection filter policy '" + identity + "': " + error, List.of(paramChange));
					}
				})
				.exceptionally(ex -> {
					logger.debug("Failed to update connection filter policy '{}'", identity, ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update connection filter policy '" + identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "IP allow lists cleared for all " + successCount + " connection filter policies", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to clear IP allow lists for all " + failedCount + " connection filter policies: ", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Cleared " + successCount + " IP allow lists, failed to clear " + failedCount + " connection filter policies",
								allChanges);
					}
				});
	}

}