package io.syrix.products.microsoft.dynamics;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.products.microsoft.base.BaseConfigurationService;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing and exporting Microsoft service configurations.
 * Provides functionality for retrieving environment settings, security roles,
 * access policies, and other organizational configurations.
 */
public class DynamicsConfigurationService extends BaseConfigurationService {

	// Configuration constants
	private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);


	private final PowerShellClient powershellClient;

	public DynamicsConfigurationService(MicrosoftGraphClient graphClient, PowerShellClient powershellClient) {
		this(graphClient, powershellClient, new ObjectMapper(), new MetricsCollector());
	}

	public DynamicsConfigurationService(
			MicrosoftGraphClient graphClient,
			PowerShellClient powershellClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.powershellClient = powershellClient;
	}

	/**
	 * Exports the complete configuration.
	 */
	public ConfigurationResult exportConfiguration() {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting configuration export at {}", startTime);

		try {
			// Create futures for all configuration components with error handling
			Map<String, CompletableFuture<?>> futures = new HashMap<>();
			futures.put("environment_settings", getComponentWithErrorHandling("environment_settings", this::getEnvironmentSettings));
			futures.put("security_roles", getComponentWithErrorHandling("security_roles", this::getSecurityRoles));
			futures.put("field_security_profiles", getComponentWithErrorHandling("field_security_profiles", this::getFieldSecurityProfiles));
			futures.put("access_policies", getComponentWithErrorHandling("access_policies", this::getAccessPolicies));
			futures.put("audit_settings", getComponentWithErrorHandling("audit_settings", this::getAuditSettings));
			futures.put("business_units", getComponentWithErrorHandling("business_units", this::getBusinessUnits));

			// Wait for all futures to complete with timeout
			ConfigurationResult result = waitForFutures(futures)
					.thenApply(this::buildConfigurationResult)
					.get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			// Record success metrics
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordExportSuccess(duration);

			logger.info("Configuration export completed successfully in {} seconds",
					duration.toSeconds());

			return result;

		} catch (Exception e) {
			metrics.recordExportFailure();
			logger.error("Configuration export failed", e);
			throw new ConfigurationExportException("Configuration export failed", e);
		}
	}
	
	/**
	 * Helper method to wrap a component future with error handling.
	 * Ensures that component failures don't propagate and break the entire export.
	 */
	private <T> CompletableFuture<T> getComponentWithErrorHandling(String componentName, Supplier<CompletableFuture<T>> componentFuture) {
		return componentFuture.get()
			.handle((result, ex) -> {
				if (ex != null) {
					logger.error("Failed to retrieve {} component: {}", componentName, ex.getMessage());
					metrics.recordComponentFailure(componentName, ex);
					// Return null or empty result instead of propagating the exception
					return null;
				}
				return result;
			});
	}

	/**
	 * Retrieves environment-wide settings.
	 */
	private CompletableFuture<JsonNode> getEnvironmentSettings() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/organizationSettings")
						.withMethod(HttpMethod.GET)
						.build()), "environment_settings"
		);
	}

	/**
	 * Retrieves security roles with permissions.
	 */
	private CompletableFuture<JsonNode> getSecurityRoles() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/securityRoles")
						.withMethod(HttpMethod.GET)
						.addQueryParam("$expand", "roleprivileges")
						.build()), "security_roles"
		);
	}

	/**
	 * Retrieves field security profiles.
	 */
	private CompletableFuture<JsonNode> getFieldSecurityProfiles() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/fieldSecurityProfiles")
						.withMethod(HttpMethod.GET)
						.build()), "field_security_profiles"
		);
	}

	/**
	 * Retrieves access policies and sharing settings.
	 */
	private CompletableFuture<JsonNode> getAccessPolicies() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/accessPolicies")
						.withMethod(HttpMethod.GET)
						.build()), "access_policies"
		);
	}

	/**
	 * Retrieves audit configuration settings.
	 */
	private CompletableFuture<JsonNode> getAuditSettings() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/settings/audit")
						.withMethod(HttpMethod.GET)
						.build()), "audit_settings"
		);
	}

	/**
	 * Retrieves business unit hierarchy.
	 */
	private CompletableFuture<JsonNode> getBusinessUnits() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/businessunits")
						.withMethod(HttpMethod.GET)
						.addQueryParam("$expand", "parentbusinessunit")
						.build()), "business_units"
		);
	}

//	private <T> CompletableFuture<Map<String, T>> waitForFutures(
//			Map<String, CompletableFuture<?>> futures) {
//
//		return CompletableFuture.allOf(
//				futures.values().toArray(new CompletableFuture[0])
//		).thenApply(v -> {
//			Map<String, T> results = new HashMap<>();
//			futures.forEach((key, future) ->
//					results.put(key, (T) future.join())
//			);
//			return results;
//		});
//	}

//	private <T> CompletableFuture<T> withRetry(Supplier<CompletableFuture<T>> operation) {
//		return CompletableFuture.supplyAsync(() -> {
//			Exception lastException = null;
//
//			for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
//				try {
//					return operation.get().join();
//				} catch (Exception e) {
//					lastException = e;
//					metrics.recordRetryAttempt(e.getMessage());
//					logger.warn("Attempt {} failed, retrying in {} seconds",
//							attempt, RETRY_DELAY.toSeconds(), e);
//
//					if (attempt < MAX_RETRY_ATTEMPTS) {
//						try {
//							Thread.sleep(RETRY_DELAY.toMillis());
//						} catch (InterruptedException ie) {
//							Thread.currentThread().interrupt();
//							throw new CompletionException(ie);
//						}
//					}
//				}
//			}
//
//			throw new CompletionException(lastException);
//		}, executor);
//	}

	private ConfigurationResult buildConfigurationResult(Map<String, ?> results) {
		ObjectNode configData = objectMapper.createObjectNode();

		results.forEach((key, value) -> {
			if (value instanceof JsonNode jsonNode) {
				configData.set(key, jsonNode);
			} else {
				configData.set(key, objectMapper.valueToTree(value));
			}
		});

		return ConfigurationResult.builder()
				.withData(configData)
				.withTimestamp(Instant.now())
				.withMetadata(buildMetadata("1.0"))
//				.withServiceType(ConfigurationServiceType.DYNAMICS)
				.build();
	}
}