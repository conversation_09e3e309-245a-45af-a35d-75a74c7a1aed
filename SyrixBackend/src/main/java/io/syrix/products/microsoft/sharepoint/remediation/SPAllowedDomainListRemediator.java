package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.products.microsoft.base.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.Parameter;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingDomainRestrictionMode;
import io.syrix.protocols.utils.Retry;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingDomainRestrictionMode.ALLOW_LIST;
import static io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingDomainRestrictionMode.BLOCK_LIST;

//Get-PnPTenant | Select-Object SharingDomainRestrictionMode
@PolicyRemediator("MS.SHAREPOINT.1.3v1")
public class SPAllowedDomainListRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
	private static final Logger log = LoggerFactory.getLogger(SPAllowedDomainListRemediator.class);

	private final List<String> allowedDomains;

	public SPAllowedDomainListRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
		super(client, tenant, spConfig);
		this.allowedDomains = spConfig.allowedDomains;
	}

	// constructor for Rollback interface
	public SPAllowedDomainListRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
		super(client, tenant, null);
		this.allowedDomains = null;
	}

	private CompletableFuture<PolicyChangeResult> runCommand(SharingDomainRestrictionMode value, String domains, List<Parameter> parameters) {
		try {
			if (value != SharingDomainRestrictionMode.NONE && domains == null) {
				throw new SyrixRuntimeException("Domains must be specified");
			}


			SPShellCommand<GeneralResult> command = switch (value) {
				case NONE -> SPShellCommand.PnPTenant.SET_RestrictionModeNone(tenant.objectIdentity);
				case ALLOW_LIST -> SPShellCommand.PnPTenant.SET_RestrictionModeAllowList(tenant.objectIdentity, domains);
				case BLOCK_LIST -> SPShellCommand.PnPTenant.SET_RestrictionModeBlockList(tenant.objectIdentity, domains);
			};

			parameters.forEach(command::addParameter);
			return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
					.thenApply(this::checkResult);
		} catch (Exception ex) {
			log.error("Run command for the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.failedFuture(ex);
		}
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		try {
			SharingDomainRestrictionMode curMode = SharingDomainRestrictionMode.fromInt(tenant.sharingDomainRestrictionMode);
			if (curMode != BLOCK_LIST) {
				return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(),
						"SharingDomainRestrictionMode does not require changes. current value :" + curMode.name()));
			}

			SharingDomainRestrictionMode prevMode = SharingDomainRestrictionMode.fromInt(tenant.sharingDomainRestrictionMode);

			String domain = getAllowedDomains();
			SharingDomainRestrictionMode restrictionMode = domain.isEmpty() ? SharingDomainRestrictionMode.NONE : ALLOW_LIST;

			List<Parameter> parameters = List.of(
					Parameter.of("RequireAcceptingAccountMatchInvitedAccount", tenant.requireAcceptingAccountMatchInvitedAccount, true),
					Parameter.of("SharingDomainRestrictionMode", prevMode, restrictionMode),
					Parameter.of("SharingBlockedDomainList", tenant.sharingBlockedDomainList, tenant.sharingBlockedDomainList),
					Parameter.of("SharingAllowedDomainList", tenant.sharingAllowedDomainList, restrictionMode == ALLOW_LIST ? domain : tenant.sharingAllowedDomainList)
			);

			CompletableFuture<PolicyChangeResult> runCommand = domain.isEmpty()
					? runCommand(SharingDomainRestrictionMode.NONE, "", parameters)
					: runCommand(ALLOW_LIST, domain, parameters);

			return runCommand
					.exceptionally(ex -> {
						log.error("Remediate the policy {} finished with exception", getPolicyId(), ex);
						return IPolicyRemediator.failed_(getPolicyId(), "Remediate the policy finished with exception: " + ex.getMessage());
					});
		} catch (Exception ex) {
			log.error("Remediate the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Remediate the policy failed:" + ex.getMessage()));
		}
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			Map<String, ParameterChangeResult> fixParameters = fixResult.getChanges().stream().collect(Collectors.toMap(ParameterChangeResult::getParameter, v -> v));

			ParameterChangeResult restrictionMode = fixParameters.get("SharingDomainRestrictionMode");
			ParameterChangeResult requireAccepting = fixParameters.get("RequireAcceptingAccountMatchInvitedAccount");
			ParameterChangeResult blockedList = fixParameters.get("SharingBlockedDomainList");
			ParameterChangeResult allowedList = fixParameters.get("SharingAllowedDomainList");

			SharingDomainRestrictionMode rollbackRestrictionMode = SharingDomainRestrictionMode.valueOf(restrictionMode.getPrevValue().toString());
			String domains = switch (rollbackRestrictionMode) {
				case NONE -> null;
				case ALLOW_LIST -> (String) allowedList.getPrevValue();
				case BLOCK_LIST -> (String) blockedList.getPrevValue();
			};

			List<Parameter> parameters = List.of(
					Parameter.of("SharingDomainRestrictionMode", restrictionMode.getNewValue(), restrictionMode.getPrevValue()),
					Parameter.of("RequireAcceptingAccountMatchInvitedAccount", requireAccepting.getNewValue(), true),
					Parameter.of("SharingBlockedDomainList", blockedList.getNewValue(), rollbackRestrictionMode == BLOCK_LIST ? blockedList.getPrevValue() : blockedList.getNewValue()),
					Parameter.of("SharingAllowedDomainList", allowedList.getNewValue(), rollbackRestrictionMode == ALLOW_LIST ? allowedList.getPrevValue() : allowedList.getNewValue())
			);

			return runCommand(rollbackRestrictionMode, domains, parameters)
					.exceptionally(ex -> {
						log.error("Rollback the policy {} finished with exception", getPolicyId(), ex);
						return IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy failed");
					});
		} catch (Exception ex) {
			log.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private String getAllowedDomains() {
		if (CollectionUtils.isNotEmpty(allowedDomains)) {
			return String.join(" ", allowedDomains);
		}
		if (StringUtils.isNotEmpty(tenant.sharingAllowedDomainList)) {
			return tenant.sharingAllowedDomainList;
		}

		return "";
	}

}
