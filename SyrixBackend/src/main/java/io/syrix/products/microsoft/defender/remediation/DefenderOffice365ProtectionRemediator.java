package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;


/**
 * Remediator for MS.DEFENDER.1.3v1
 * All users SHALL be added to Defender for Office 365 protection in either the standard or strict preset security policy.
 */
@PolicyRemediator("MS.DEFENDER.1.3v1")
public class DefenderOffice365ProtectionRemediator extends RemediatorBase {
	private final PowerShellClient powershellClient;
	private final PolicyType policyType;

	// Default excluded users (can be overridden by configuration)
	private static final List<String> DEFAULT_EXCLUDED_USERS = new ArrayList<>();

	public DefenderOffice365ProtectionRemediator(PowerShellClient powershellClient, PolicyType policyType) {
		this.powershellClient = powershellClient;
		this.policyType = policyType;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for MS.DEFENDER.1.3v1 - Adding users to {} Defender for Office 365 protection policy", policyType.policyType);

		// Step 1: Get configuration (which could override defaults)
		List<String> excludedUsers = getExcludedUsers();
		List<String> excludedGroups = getExcludedGroups();

		// Step 2: Get all mailboxes
		return DefenderHelpers.getAllMailboxes(powershellClient, logger)
				.thenCompose(allUsers -> {
					if (allUsers == null || allUsers.isEmpty()) {
						logger.warn("No mailboxes found to apply to Defender for Office 365 protection policies");
						return CompletableFuture.completedFuture(new ArrayList<JsonNode>());
					}

					// Step 3: Filter out excluded users
					return filterExcludedUsers(allUsers, excludedUsers);
				})
				.thenCompose(filteredUsers -> {
					if (filteredUsers.isEmpty()) {
						logger.warn("No users left after filtering excluded users");
						return CompletableFuture.completedFuture(new ArrayList<JsonNode>());
					}

					// Step 4: Filter out members of excluded groups
					return DefenderHelpers.filterExcludedGroupMembers(filteredUsers, excludedGroups, powershellClient, logger);
				})
				.thenCompose(finalUsers -> {
					if (finalUsers.isEmpty()) {
						logger.warn("No users found to apply to Defender for Office 365 protection policies after all exclusions");
						return IPolicyRemediator.failed(getPolicyId(), "No users found to apply to policies after exclusions");
					}

					// Apply users to the configured preset policy type
					return DefenderHelpers.applyUsersToProtectionPolicyRule(
							powershellClient,
							logger,
							getPolicyId(),
							RuleType.ATP, // ATP for Defender for Office 365
							policyType.policyType,
							finalUsers,
							new ObjectMapper());
				})
				.exceptionallyCompose(ex -> {
					logger.error("Exception while configuring Defender for Office 365 protection", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage());
				});
	}

	/**
	 * Check if a result indicates successful operation
	 */
	private boolean isSuccessful(JsonNode result) {
		return result != null && !result.has(Constants.ERROR_FIELD);
	}

	/**
	 * Get the list of users to exclude from policy application, combining defaults with any config overrides
	 */
	private List<String> getExcludedUsers() {
		// Get excluded users from configuration if needed
		return new ArrayList<>(DEFAULT_EXCLUDED_USERS);
	}

	/**
	 * Get the list of groups to exclude from policy application
	 */
	private List<String> getExcludedGroups() {
		// Get excluded groups from configuration if needed
		return new ArrayList<>();
	}

	/**
	 * Filter out excluded users from the list
	 */
	private CompletableFuture<List<JsonNode>> filterExcludedUsers(List<JsonNode> allUsers, List<String> excludedUsers) {
		List<JsonNode> filteredUsers = new ArrayList<>();

		for (JsonNode user : allUsers) {
			if (user.has(DefenderConstants.USER_PRINCIPAL_NAME_FIELD)) {
				String upn = user.get(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText();
				if (!excludedUsers.contains(upn)) {
					filteredUsers.add(user);
				}
			} else {
				// If UPN is missing, we still include the user as we can't determine if it should be excluded
				filteredUsers.add(user);
			}
		}
		return CompletableFuture.completedFuture(filteredUsers);
	}
}