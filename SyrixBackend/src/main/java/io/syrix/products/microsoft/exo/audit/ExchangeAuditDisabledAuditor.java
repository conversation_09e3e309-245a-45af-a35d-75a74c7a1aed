package io.syrix.products.microsoft.exo.audit;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ExoRemediationException;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.protocols.client.PowerShellClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Audits the Exchange Online AuditDisabled setting for compliance.
 * 
 * This class checks if the AuditDisabled organization setting is properly configured (set to False)
 * for security compliance without performing any remediation.
 */
public class ExchangeAuditDisabledAuditor {
    private static final Logger logger = LoggerFactory.getLogger(ExchangeAuditDisabledAuditor.class);
    private final ObjectMapper mapper = new ObjectMapper();
    private final PowerShellClient exchangeClient;

    /**
     * Constructs a new ExchangeAuditDisabledAuditor with the specified PowerShell client.
     *
     * @param exchangeClient The Exchange Online PowerShell client
     */
    public ExchangeAuditDisabledAuditor(PowerShellClient exchangeClient) {
        this.exchangeClient = exchangeClient;
    }

    /**
     * Audits the AuditDisabled setting in Exchange Online.
     *
     * @return CompletableFuture containing the audit result
     */
    public CompletableFuture<AuditResult> auditAuditDisabledSetting() {
        logger.info("Starting audit of Exchange Online AuditDisabled setting");

        return getOrganizationConfiguration()
                .thenApply(this::evaluateConfiguration)
                .exceptionally(ex -> {
                    logger.error("Exception during audit", ex);
                    return new AuditResult(
                            false,
                            ex.getMessage() != null ? ex.getMessage() : ExoConstants.UNKNOWN_ERROR,
                            "Unknown",
                            "False"
                    );
                });
    }

    /**
     * Retrieves the current organization configuration.
     *
     * @return CompletableFuture containing the organization configuration
     */
    private CompletableFuture<JsonNode> getOrganizationConfiguration() {
        logger.info("Retrieving organization configuration");

        Map<String, Object> parameters = new HashMap<>(PowerShellClient.DEFAULT_PARAMETERS);
        PowerShellClient.CommandRequest request = new PowerShellClient.CommandRequest(
                ExoConstants.GET_ORGANIZATION_CONFIG, parameters);

        return exchangeClient.executeCmdletCommand(request)
                .thenApply(result -> {
                    if (result != null && !result.isEmpty()) {
                        logger.debug("Successfully retrieved organization configuration");
                        return result;
                    } else {
                        String error = "Failed to retrieve organization configuration: Empty or invalid response";
                        logger.error(error);
                        throw new ExoRemediationException(error);
                    }
                });
    }

    /**
     * Evaluates the organization configuration to determine if AuditDisabled is properly set.
     *
     * @param config The organization configuration
     * @return The audit result
     */
    private AuditResult evaluateConfiguration(JsonNode config) {
        try {
            // Extract organization config from the response
            JsonNode orgConfig = null;

            // Check if the result is directly an array
            if (config.isArray() && config.size() > 0) {
                orgConfig = config.get(0);
            }
            // If the result is just a single object
            else if (config.isObject()) {
                orgConfig = config;
            }

            if (orgConfig == null) {
                logger.error("Failed to extract organization config from response");
                return new AuditResult(
                        false,
                        "Failed to extract organization config from response",
                        "Unknown",
                        "False"
                );
            }

            // Check if AuditDisabled is present and its value
            if (orgConfig.has(ExoConstants.AUDIT_DISABLED)) {
                boolean auditDisabled = orgConfig.get(ExoConstants.AUDIT_DISABLED).asBoolean();
                boolean isCompliant = !auditDisabled; // It's compliant if AuditDisabled is false

                if (isCompliant) {
                    logger.info("Audit check passed: AuditDisabled is properly set to False");
                } else {
                    logger.warn("Audit check failed: AuditDisabled is set to True");
                }

                return new AuditResult(
                        isCompliant,
                        isCompliant ? null : "AuditDisabled is set to True",
                        Boolean.toString(auditDisabled),
                        "False"
                );
            } else {
                logger.error("AuditDisabled setting not found in organization configuration");
                return new AuditResult(
                        false,
                        "AuditDisabled setting not found in organization configuration",
                        "Unknown",
                        "False"
                );
            }
        } catch (Exception e) {
            logger.error("Error evaluating organization configuration", e);
            return new AuditResult(
                    false,
                    "Error evaluating organization configuration: " + e.getMessage(),
                    "Unknown",
                    "False"
            );
        }
    }

    /**
     * Represents the result of an audit operation.
     */
    public static class AuditResult {
        private final boolean isCompliant;
        private final String failureReason;
        private final String currentValue;
        private final String expectedValue;

        public AuditResult(boolean isCompliant, String failureReason, String currentValue, String expectedValue) {
            this.isCompliant = isCompliant;
            this.failureReason = failureReason;
            this.currentValue = currentValue;
            this.expectedValue = expectedValue;
        }

        public boolean isCompliant() {
            return isCompliant;
        }

        public String getFailureReason() {
            return failureReason;
        }

        public String getCurrentValue() {
            return currentValue;
        }

        public String getExpectedValue() {
            return expectedValue;
        }

        @Override
        public String toString() {
            return "AuditResult{" +
                    "isCompliant=" + isCompliant +
                    ", failureReason='" + failureReason + '\'' +
                    ", currentValue='" + currentValue + '\'' +
                    ", expectedValue='" + expectedValue + '\'' +
                    '}';
        }
    }
}