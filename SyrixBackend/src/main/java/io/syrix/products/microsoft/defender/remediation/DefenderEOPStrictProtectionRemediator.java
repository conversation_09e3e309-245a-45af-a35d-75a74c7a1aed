package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellClient.CommandRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.defender.remediation.RuleType.EOP;

/**
 * Remediator for MS.DEFENDER.1.4v1
 * Applies EOP strict protection policy to sensitive accounts.
 */
@PolicyRemediator("MS.DEFENDER.1.4v1")
public class DefenderEOPStrictProtectionRemediator extends RemediatorBase {
	private final PowerShellClient powershellClient;
	private final ObjectMapper objectMapper;

	// Default excluded users (can be overridden by configuration)
	private static final List<String> DEFAULT_EXCLUDED_USERS = new ArrayList<>();

	private final List<String> excludedUsers;
	private final List<String> excludedGroups;
	private final List<String> sensitiveAccounts;

	public DefenderEOPStrictProtectionRemediator(PowerShellClient powershellClient,
												 List<String> excludedUsers,
												 List<String> excludedGroups,
												 List<String> sensitiveAccounts) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
		this.excludedUsers = excludedUsers != null ? excludedUsers : new ArrayList<>();
		this.excludedGroups = excludedGroups != null ? excludedGroups : new ArrayList<>();
		this.sensitiveAccounts = sensitiveAccounts != null ? sensitiveAccounts : new ArrayList<>();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for EOP strict protection policy for sensitive accounts");

		// Get sensitive accounts from constructor list
		return DefenderHelpers.getAllMailboxes(powershellClient, logger)
				.thenCompose(allMailboxes -> {
					List<JsonNode> accounts = allMailboxes.stream()
							.filter(mailbox -> mailbox.has(DefenderConstants.USER_PRINCIPAL_NAME_FIELD)
									&& sensitiveAccounts.contains(mailbox.get(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText()))
							.toList();

					if (accounts.isEmpty()) {
						logger.warn("No sensitive accounts found to apply EOP strict protection policy");
						return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
								"No sensitive accounts found to apply policy", new ObjectMapper()));
					}

					return DefenderHelpers.filterExcludedUsers(accounts, excludedUsers)
							.thenCompose(filteredAccounts -> {
								if (filteredAccounts.isEmpty()) {
									logger.warn("No sensitive accounts left after filtering excluded users");
									return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
											"No sensitive accounts left after filtering excluded users", new ObjectMapper()));
								}

								return DefenderHelpers.filterExcludedGroupMembers(filteredAccounts, excludedGroups, powershellClient, logger)
										.thenCompose(finalAccounts -> {
											if (finalAccounts.isEmpty()) {
												logger.warn("No sensitive accounts found after all exclusions");
												return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
														"No sensitive accounts found after all exclusions", new ObjectMapper()));
											}

											return DefenderHelpers.applyUsersToProtectionPolicyRule(
													powershellClient,
													logger,
													getPolicyId(),
													EOP,
													DefenderConstants.STRICT_PRESET_POLICY,
													finalAccounts,
													objectMapper);
//													applyEOPStrictPolicy(finalAccounts);
										});
							});
				})
				.exceptionally(ex -> {
					logger.error("Exception while configuring EOP strict protection", ex);
					return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), new ObjectMapper());
				});
	}

	/**
	 * Get all sensitive accounts
	 */
	private CompletableFuture<List<JsonNode>> getSensitiveAccounts() {
		// Use DefenderHelpers to get all mailboxes
		return DefenderHelpers.getAllMailboxes(powershellClient, logger)
				.thenApply(allMailboxes -> {
					List<JsonNode> sensitiveAccounts = new ArrayList<>();

					// Filter to include only sensitive accounts
					for (JsonNode account : allMailboxes) {
						if (isSensitiveAccount(account)) {
							sensitiveAccounts.add(account);
						}
					}

					logger.info("Found {} sensitive accounts", sensitiveAccounts.size());
					return sensitiveAccounts;
				});
	}

	/**
	 * Determine if an account is sensitive based on your criteria
	 * This is a placeholder - implement based on your specific logic
	 */
	private boolean isSensitiveAccount(JsonNode account) {
		// This is where you would implement your logic to determine if an account is sensitive
		// For example, you might check for specific properties, group memberships, or custom attributes

		// Example: check if the account has a specific property or attribute
		return account.has("IsSensitive") && account.get("IsSensitive").asBoolean();
	}

	/**
	 * Apply EOP strict protection policy to the specified users
	 */
	private CompletableFuture<JsonNode> applyEOPStrictPolicy(List<JsonNode> users) {
		if (users.isEmpty()) {
			logger.warn("No sensitive accounts to apply EOP strict protection policy");
			return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
					"No sensitive accounts to apply to the policy",
					new ObjectMapper()));
		}

		// Use DefenderHelpers to get the EOP Protection Policy Rule for strict preset
		return DefenderHelpers.getEOPProtectionPolicyRule(powershellClient, DefenderConstants.STRICT_PRESET_POLICY)
				.thenCompose(eopRules -> {
					if (eopRules == null || eopRules.isEmpty()) {
						String error = "No EOP Protection Policy Rule found for Strict preset. " +
								"Ensure the policy is enabled and you have Exchange Online Protection.";
						logger.error(error);
						return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
								error,
								new ObjectMapper()));
					}

					JsonNode matchingRule = eopRules.getFirst();

					// Build the recipient filter
					StringBuilder filterBuilder = new StringBuilder();
					boolean first = true;

					for (JsonNode user : users) {
						if (!first) {
							filterBuilder.append(") -or (");
						} else {
							filterBuilder.append("(");
							first = false;
						}

						String recipientType = user.has(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD) ?
								user.get(DefenderConstants.RECIPIENT_TYPE_DETAILS_FIELD).asText() : "UserMailbox";
						String objectId = user.has(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD) ?
								user.get(DefenderConstants.EXTERNAL_DIRECTORY_OBJECT_ID_FIELD).asText() : "";

						filterBuilder.append("RecipientTypeDetails -eq '").append(recipientType)
								.append("' -and -not(Name -like 'SystemMailbox{*') -and -not(Name -like 'CAS_{*')")
								.append(" -and (ExternalDirectoryObjectId -eq '").append(objectId).append("'");
					}

					filterBuilder.append(")");
					String recipientFilter = filterBuilder.toString();

					// Apply the filter to the rule
					Map<String, Object> setParams = new HashMap<>();
					setParams.put(DefenderConstants.IDENTITY_PROPERTY, matchingRule.get(DefenderConstants.IDENTITY_PROPERTY).asText());

					CommandRequest setCommand = new CommandRequest(DefenderConstants.SET_EOP_PROTECTION_POLICY_RULE, setParams);

					return powershellClient.executeCmdletCommand(setCommand)
							.thenCompose(result -> {
								if (result == null || result.has(Constants.ERROR_FIELD)) {
									String error = result != null ?
											result.get(Constants.ERROR_FIELD).asText() :
											"Unknown error applying EOP strict protection policy to sensitive accounts";
									logger.error("Failed to apply EOP strict protection: {}", error);
									return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
											error, new ObjectMapper()));
								}

								logger.info("Successfully applied EOP strict protection policy to {} sensitive accounts", users.size());

								// Create a detailed success response
								return CompletableFuture.completedFuture(DefenderHelpers.createSuccessResponse(getPolicyId(),
										users.size(),
										EOP.name(),
										DefenderConstants.STRICT_PRESET_POLICY,
										new ObjectMapper(),
										"Successfully applied EOP strict protection policy to sensitive accounts"));
							});
				});
	}
}