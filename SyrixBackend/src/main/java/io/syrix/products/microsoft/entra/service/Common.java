package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import org.slf4j.Logger;

import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.GROUPS_ENDPOINT;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.common.constants.Constants.VALUE_TRUE;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.FILTER_UNIFIED_GROUPS;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.PUBLIC_VISIBILITY;
import static io.syrix.protocols.utils.GraphClientConstants.PARAM_COUNT;
import static io.syrix.protocols.utils.GraphClientConstants.PARAM_FILTER;

public class Common {

	/**
	 * Audits Microsoft 365 Groups for public visibility.
	 * Implements CIS control 1.2.1 (SYX 5.3.1).
	 *
	 * @return CompletableFuture with an array of public group objects
	 */
	public static CompletableFuture<ArrayNode> auditAndExportPublicM365Groups(Logger logger, ObjectMapper objectMapper, MicrosoftGraphClient graphClient) {
		logger.info("Auditing Microsoft 365 Groups for public visibility");
		// Create the request to find all M365 groups (since visibility can't be filtered directly)
		GraphRequest request = GraphRequest.builder()
				.v1()
				.withEndpoint(GROUPS_ENDPOINT)
				.addQueryParam(PARAM_COUNT, VALUE_TRUE)
				.addQueryParam(PARAM_FILTER, FILTER_UNIFIED_GROUPS)
				.build();

		// Execute the request with retry and process the results
		return graphClient.makeGraphRequest(request)
				.thenApply(response -> {
					// Create array for public groups
					ArrayNode publicGroupsArray = objectMapper.createArrayNode();

					// Filter the groups client-side to find those with "Public" visibility
					if (response.has(VALUE_FIELD) && response.get(VALUE_FIELD).isArray()) {
						response.get(VALUE_FIELD).forEach(group -> {
							// Check if visibility property exists and equals "Public"
							if (group.has(EntraIDConstants.VISIBILITY_PROPERTY) &&
									PUBLIC_VISIBILITY.equals(group.get(EntraIDConstants.VISIBILITY_PROPERTY).asText())) {
								// Add the raw group data to the array
								publicGroupsArray.add(group);
							}
						});
					}
					logger.info("Found {} public Microsoft 365 Groups", publicGroupsArray.size());
					return publicGroupsArray;
				})
				.exceptionally(e -> {
					logger.error("Failed to audit M365 Groups privacy: {}", e.getMessage(), e);
					// Return empty array on error
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Creates an empty value node for non-premium cases.
	 */
	public static  JsonNode createEmptyValueNode(ObjectMapper objectMapper) {
		return objectMapper.createObjectNode().put(VALUE_FIELD, "[]");
	}

	public static boolean nodeIsNOTNullWithValue(JsonNode node) {
		return node != null && node.has(VALUE_FIELD);
	}
}
