package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.defender.remediation.RuleType.ATP;

/**
 * Remediator for MS.DEFENDER.1.5v1
 * Applies Defender for Office 365 strict protection policy to sensitive accounts.
 */
@PolicyRemediator("MS.DEFENDER.1.5v1")
public class DefenderOffice365StrictProtectionRemediator extends RemediatorBase {
    private final PowerShellClient powershellClient;
    private final ObjectMapper objectMapper;

    // Lists for excluded and sensitive accounts
    private final List<String> excludedUsers;
    private final List<String> excludedGroups;
    private final List<String> sensitiveAccounts;

    public DefenderOffice365StrictProtectionRemediator(PowerShellClient powershellClient,
                                                       List<String> excludedUsers,
                                                       List<String> excludedGroups,
                                                       List<String> sensitiveAccounts) {
        this.powershellClient = powershellClient;
        this.objectMapper = new ObjectMapper();
        this.excludedUsers = excludedUsers != null ? excludedUsers : new ArrayList<>();
        this.excludedGroups = excludedGroups != null ? excludedGroups : new ArrayList<>();
        this.sensitiveAccounts = sensitiveAccounts != null ? sensitiveAccounts : new ArrayList<>();
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        logger.info("Starting remediation for Defender for Office 365 strict protection policy for sensitive accounts");

        // Get sensitive accounts from constructor list
        return DefenderHelpers.getAllMailboxes(powershellClient, logger)
                .thenCompose(allMailboxes -> {
                    List<JsonNode> accounts = allMailboxes.stream()
                            .filter(mailbox -> mailbox.has(DefenderConstants.USER_PRINCIPAL_NAME_FIELD)
                                    && sensitiveAccounts.contains(mailbox.get(DefenderConstants.USER_PRINCIPAL_NAME_FIELD).asText()))
                            .toList();

                    if (accounts.isEmpty()) {
                        logger.warn("No sensitive accounts found to apply Defender for Office 365 strict protection policy");
                        return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
                                "No sensitive accounts found to apply policy", new ObjectMapper()));
                    }

                    return DefenderHelpers.filterExcludedUsers(accounts, excludedUsers)
                            .thenCompose(filteredAccounts -> {
                                if (filteredAccounts.isEmpty()) {
                                    logger.warn("No sensitive accounts left after filtering excluded users");
                                    return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
                                            "No sensitive accounts left after filtering excluded users", new ObjectMapper()));
                                }

                                return DefenderHelpers.filterExcludedGroupMembers(filteredAccounts, excludedGroups, powershellClient, logger)
                                        .thenCompose(finalAccounts -> {
                                            if (finalAccounts.isEmpty()) {
                                                logger.warn("No sensitive accounts found after all exclusions");
                                                return CompletableFuture.completedFuture(DefenderHelpers.createErrorResponse(getPolicyId(),
                                                        "No sensitive accounts found after all exclusions", new ObjectMapper()));
                                            }

                                            // Use the helper method to apply users to ATP protection policy rule
                                            return DefenderHelpers.applyUsersToProtectionPolicyRule(
                                                    powershellClient,
                                                    logger,
                                                    getPolicyId(),
                                                    ATP, // Using ATP (Defender for Office 365) instead of EOP
                                                    DefenderConstants.STRICT_PRESET_POLICY,
                                                    finalAccounts,
                                                    objectMapper);
                                        });
                            });
                })
                .exceptionally(ex -> {
                    logger.error("Exception while configuring Defender for Office 365 strict protection", ex);
                    return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), new ObjectMapper());
                });
    }
}