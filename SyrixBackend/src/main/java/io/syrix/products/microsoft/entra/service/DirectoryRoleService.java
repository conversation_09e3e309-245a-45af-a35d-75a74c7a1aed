package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.POLICY_ID_FIELD;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.Common.createEmptyValueNode;
import static io.syrix.products.microsoft.entra.service.Common.nodeIsNOTNullWithValue;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_FILTER;

/**
 * Service class to handle directory role operations.
 */
class DirectoryRoleService extends BaseConfigurationService {
	private static final List<String> PRIVILEGED_ROLES = Arrays.asList(
			"Global Administrator",
			"Privileged Role Administrator",
			"User Administrator",
			"SharePoint Administrator",
			"Exchange Administrator",
			"Hybrid Identity Administrator",
			"Application Administrator",
			"Cloud Application Administrator"
	);

	public static final String ROLE_DEFINITION_ID = "roleDefinitionId";
	public static final String ROLE_TEMPLATE_ID = "roleTemplateId";
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private static final String VALUE_KEY = VALUE_FIELD;
	private static final String DISPLAY_NAME = DISPLAY_NAME_FIELD;
	private final Logger logger = LoggerFactory.getLogger(getClass());

	DirectoryRoleService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
		this.objectMapper = objectMapper;
	}

	/**
	 * Retrieves directory roles and their configurations based on premium license status.
	 */
	CompletableFuture<JsonNode> getDirectoryRoles(boolean hasAadPremiumP2) {
		return getFilteredAADRoles()
				.thenCompose(roles -> hasAadPremiumP2 ?
						getFullRoleConfiguration(roles) :
						getBasicRoleConfiguration(roles));
	}

	static String getRoleIDByTemplpateID(JsonNode dirRoles, JsonNode role) {
		String roleId = "";
		for (JsonNode dirRole : dirRoles.get(VALUE_FIELD)) {
			if (dirRole.get(ROLE_TEMPLATE_ID).asText().equals(role.get(ID_FIELD).asText())) {
				roleId = dirRole.get(ID_FIELD).asText();
				break;
			}
		}
		return roleId;
	}

	/**
	 * Retrieves and filters AAD roles to only include privileged roles.
	 */
	private CompletableFuture<JsonNode> getFilteredAADRoles() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/directoryRoleTemplates")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaDirectoryRoleTemplate"
		).thenApply(this::filterPrivilegedRoles).exceptionally(e -> {
			logger.error("Failed to call getFilteredAADRoles {}", e.getMessage());
			return objectMapper.createArrayNode();
		});
	}

	/**
	 * Filters roles to only include privileged roles.
	 */
	private JsonNode filterPrivilegedRoles(JsonNode roles) {
		ArrayNode filteredRoles = objectMapper.createArrayNode();
		roles.get(VALUE_KEY).forEach(role -> {
			if (PRIVILEGED_ROLES.contains(role.get(DISPLAY_NAME).asText())) {
				ObjectNode privilegedRole = (ObjectNode) role;
				privilegedRole.put(ROLE_TEMPLATE_ID, privilegedRole.get(ID_FIELD).asText());
				filteredRoles.add(privilegedRole);
			}
		});
		return objectMapper.createObjectNode().set(VALUE_KEY, filteredRoles);
	}

	/**
	 * Creates basic role configuration without premium features.
	 */
	private CompletableFuture<JsonNode> getBasicRoleConfiguration(JsonNode roles) {
		ArrayNode processedRoles = objectMapper.createArrayNode();
		roles.get(VALUE_KEY).forEach(role -> {
			ObjectNode processedRole = (ObjectNode) role;
			processedRole.putArray("Assignments");
			processedRole.putArray("Rules");
			processedRoles.add(processedRole);
		});
		return CompletableFuture.completedFuture(
				objectMapper.createObjectNode().set(VALUE_KEY, processedRoles));
	}

	/**
	 * Creates full role configuration with premium features.
	 */
	private CompletableFuture<JsonNode> getFullRoleConfiguration(JsonNode roles) {
		return getPIMAssignments().thenCompose(pimAssignments -> {
			CompletableFuture<JsonNode> roleAssignmentsFeature = getPolicyRoleManagementPolicyAssignment();
			return roleAssignmentsFeature.thenCompose(roleAssignments -> {
				List<CompletableFuture<JsonNode>> roleFutures = new ArrayList<>();
				if (nodeIsNOTNullWithValue(roles) && roles.get(VALUE_KEY).isArray()
						&& nodeIsNOTNullWithValue(pimAssignments)
						&& nodeIsNOTNullWithValue(roleAssignments)) {
					roles.get(VALUE_KEY).forEach(role -> {
						JsonNode policyAssignment = getRoleAssignmentsByRoleTemplateId(role.get(ID_FIELD).asText(),
								roleAssignments.get(VALUE_FIELD));
						roleFutures.add(processRoleWithFullConfig(role, pimAssignments.get(VALUE_FIELD),
								policyAssignment.get(0)));
					});
				} else {
					logger.warn("No roles found to process in getFullRoleConfiguration");
					return CompletableFuture.completedFuture(createEmptyValueNode(this.objectMapper));
				}
				return CompletableFuture.allOf(roleFutures.toArray(new CompletableFuture[0]))
						.thenApply(v -> {
							ArrayNode processedRoles = objectMapper.createArrayNode();
							roleFutures.forEach(future -> processedRoles.add(future.join()));
							return objectMapper.createObjectNode().set(VALUE_KEY, processedRoles);
						});
			});
		});
	}

	/**
	 * Filters role assignments by a specific role definition ID.
	 *
	 * @param roleId          The role definition ID to filter by
	 * @param roleAssignments JsonNode containing an array of role assignments
	 * @return JsonNode containing filtered role assignments, or empty array if none match
	 */
	private JsonNode getRoleAssignmentsByRoleTemplateId(String roleTemplateId, JsonNode roleAssignments) {
		ArrayNode filteredAssignments = objectMapper.createArrayNode();

		if (roleAssignments != null && roleAssignments.isArray()) {
			for (JsonNode assignment : roleAssignments) {
				if (assignment.has(ROLE_DEFINITION_ID) && roleTemplateId.equals(assignment.get(ROLE_DEFINITION_ID).asText())) {
					filteredAssignments.add(assignment);
				}
			}
		}

		return filteredAssignments;
	}

	/**
	 * Processes a single role with full configuration.
	 */
	private CompletableFuture<JsonNode> processRoleWithFullConfig(JsonNode role, JsonNode assignments, JsonNode roleAssignments) {
		String roleId = role.get(ID_FIELD).asText();
		String roleTemplateId = role.get(ROLE_TEMPLATE_ID).asText();
		JsonNode pimAssignements = getRoleAssignmentsByRoleTemplateId(roleTemplateId, assignments);
		CompletableFuture<JsonNode> rulesFuture = getRoleRules(roleAssignments.get(POLICY_ID_FIELD).asText());
		return rulesFuture.thenApply(rules -> combineRoleData(role, roleAssignments, pimAssignements, rules));
	}

	/**
	 * Retrieves role members.
	 */
	private CompletableFuture<JsonNode> getPolicyRoleManagementPolicyAssignment() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/policies/roleManagementPolicyAssignments")
						.addQueryParam(PARAM_FILTER, "scopeId eq '/' and scopeType eq 'DirectoryRole'")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaPolicyRoleManagementPolicyAssignment")
				.exceptionally(e -> {
					logger.error("Failed to call getRoleMembers {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves role members.
	 */
	private CompletableFuture<JsonNode> getRoleMembers(String roleId) {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint(String.format("/directoryRoles/%s/members", roleId))
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaDirectoryRoleMember")
				.exceptionally(e -> {
					logger.error("Failed to call getRoleMembers {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves PIM assignments.
	 */
	private CompletableFuture<JsonNode> getPIMAssignments() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/roleManagement/directory/roleAssignmentScheduleInstances")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaRoleManagementDirectoryRoleAssignmentScheduleInstance")
				.exceptionally(e -> {
					logger.error("Failed to call getPIMAssignments {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves role rules.
	 */
	private CompletableFuture<JsonNode> getRoleRules(String roleId) {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint(String.format("/policies/roleManagementPolicies/%s/rules", roleId))
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaPolicyRoleManagementPolicyRule")
				.exceptionally(e -> {
					logger.error("Failed to call getRoleRules {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Combines all role data into a single processed role object.
	 */
	private JsonNode combineRoleData(JsonNode role, JsonNode members,
									 JsonNode pimAssignments, JsonNode rules) {
		ObjectNode processedRole = (ObjectNode) role;
		addAssignments(processedRole, members, pimAssignments);
		addRules(processedRole, rules, role.get(DISPLAY_NAME).asText());
		return processedRole;
	}

	/**
	 * Adds assignments to processed role.
	 */
	private void addAssignments(ObjectNode processedRole, JsonNode members, JsonNode pimAssignments) {
		ArrayNode assignments = processedRole.putArray("Assignments");
		if (members.has(VALUE_KEY)) {
			members.get(VALUE_KEY).forEach(assignments::add);
		}
		if (pimAssignments instanceof ArrayNode array) {
			array.forEach(assignments::add);
		}
	}

	/**
	 * Adds rules to processed role.
	 */
	private void addRules(ObjectNode processedRole, JsonNode policyRules, String displayName) {
		ArrayNode rules = processedRole.putArray("Rules");
		if (policyRules.has(VALUE_KEY)) {
			policyRules.get(VALUE_KEY).forEach(rule -> {
				((ObjectNode) rule).put("RuleSource", displayName);
				((ObjectNode) rule).put("RuleSourceType", "Directory Role");
				rules.add(rule);
			});
		}
	}

	@Override
	public ConfigurationResult exportConfiguration() {
		return null;
	}
}
