package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.12.2v1 (No Safe Lists) in Exchange Online.
 * <p>
 * This class ensures compliance by:
 * - Disabling safe lists (MS.EXO.12.2v1)
 */
@SuppressWarnings("unused")
@PolicyRemediator("MS.EXO.12.2v1")
public class ExchangeEnableSafeListRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

	private static final String UNKNOWN_ERROR = "Unknown error";

	public ExchangeEnableSafeListRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	// Constructor for Rollback interface
	public ExchangeEnableSafeListRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		this(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (No Safe Lists)", getPolicyId());

		List<ConnectionPolicy> policies = loadConnectionPolicy();
		if (policies.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_CONNECTION_FILTER_POLICIES_FOUND));
		}

		List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
				.filter(policy -> policy.enableSafeList)
				.map(policy -> updatePolicy(policy.identity, true, false))
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}
	
	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (No Safe Lists)", getPolicyId());
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
					continue;
				}

				String[] parts = change.getParameter().split(":");
				String identity = parts[1].split(",")[0];
				boolean prevValue = (Boolean) change.getPrevValue();
				boolean newValue = (Boolean) change.getNewValue();

				// Rolling back: restore original value by swapping newValue and prevValue
				results.add(updatePolicy(identity, newValue, prevValue));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> updatePolicy(String identity, Boolean prevValue, Boolean newValue) {
		logger.info("Updating connection filter policy '{}' to {} safe lists", identity, newValue ? "enable" : "disable");

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(ExoConstants.IDENTITY + ":" + identity + ",params: EnableSafeList")
				.prevValue(prevValue)
				.newValue(newValue);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put("Identity", identity);
		parameters.put("EnableSafeList", newValue);

		return exchangeClient.executeCmdletCommand(
					new PowerShellClient.CommandRequest("Set-HostedConnectionFilterPolicy", parameters)
				)
				.thenApply(result -> {
					if (result != null && !result.has(Constants.ERROR_FIELD)) {
						logger.debug("Connection filter policy '{}' updated successfully", identity);
						paramChange.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Connection filter policy '"+identity+"' updated successfully", List.of(paramChange));
					} else {
						String error = result != null && result.has(Constants.ERROR_FIELD) ? result.get(Constants.ERROR_FIELD).asText() : UNKNOWN_ERROR;
						logger.debug("Failed to update connection filter policy '{}': {}", identity, error);
						paramChange.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to update connection filter policy '"+identity+"': " + error, List.of(paramChange));
					}
				})
				.exceptionally(ex ->  {
					logger.debug("Failed to update connection filter policy '{}'", identity, ex);
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update connection filter policy '"+identity+"': " + ex.getMessage(), List.of(paramChange));
				});
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "Enable safe list disabled for all " + successCount + " connection filter policies", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to disable enable safe list for all " + failedCount + " connection filter policies: ", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " disable enable safe list, failed to fix " + failedCount + " connection filter policies",
								allChanges);
					}
				});
	}

}