package io.syrix.products.microsoft.exo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.storage.Storage;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.main.Context;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.products.microsoft.exo.remediation.context.SharingPolicy;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.service.ServiceTypeRemediationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

public class ExchangeRemediationService implements ServiceTypeRemediationService {
	private static final Logger logger = LoggerFactory.getLogger(ExchangeRemediationService.class);
	private final Storage storage;
	private final ObjectMapper mapper;
	private final Context context;

	public ExchangeRemediationService(Context context, Storage storage) {
		this.context = context;
		this.storage = storage;
		this.mapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}

	public CompletableFuture<JsonNode> remediate(RemediationTask task) {
		try {
			JsonNode jsonConfig = loadConfig(task);

			List<PolicyChangeResult> result = new ArrayList<>();
			try (MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context);
				 PowerShellClient client = initClient(graphClient)) {
				List<IPolicyRemediator> iPolicyRemediators = makeRemediators(task, jsonConfig, graphClient, client);
				int ix = 0;
				for (IPolicyRemediator remediator : iPolicyRemediators) {
					PolicyChangeResult changeResult = remediator.remediate_().join();
					changeResult.executeOrder(ix++);
					result.add(changeResult);
				}
			}
			return CompletableFuture.completedFuture(mapper.valueToTree(result));
		} catch (IOException e) {
			return CompletableFuture.failedFuture(e);
		}
	}

	private List<IPolicyRemediator> makeRemediators(RemediationTask task, JsonNode jsonConfig,MicrosoftGraphClient graphClient, PowerShellClient client) throws IOException {
		ExchangeRemediationConfig remediationConfig = task.getExchangeRemediationTask().getRemediationConfig();
		List<String> policyIds = task.getExchangeRemediationTask().getPolicyIds();

		List<SharingPolicy> sharingPolicies = getSharingPolicy(jsonConfig);

		ExchangeRemediationContext remediationContext = new ExchangeRemediationContext();
		remediationContext.setSharingPolicies(sharingPolicies);

		return policyIds.stream()
				.map(policyId -> (IPolicyRemediator) PolicyRemediatorRegistry.getPolicyRemediator(policyId, graphClient, client, jsonConfig, remediationContext, remediationConfig))
				.toList();
	}

	private JsonNode loadConfig(Task task) throws IOException {
		Path configPath = storage.loadConfigPath(task);
		return mapper.readTree(configPath.toFile());
	}

	private PowerShellClient initClient(MicrosoftGraphClient graphClient) {
		String tenantId = graphClient.getTenantId();

		if (tenantId == null) {
			logger.error("Tenant ID not initialized");
			throw new SyrixRuntimeException("Tenant ID not initialized");
		}

		return ClientFactory.initPowerShellClient(context, tenantId, "Exchange");
	}


	private List<SharingPolicy> getSharingPolicy(JsonNode jsonConfig) {
		if (jsonConfig == null) {
			logger.warn("Config node is null, returning empty list of Sharing Policy");
			return Collections.emptyList();
		}

		JsonNode domains = jsonConfig.get(ExoConstants.CONFIG_KEY_SHARING_POLICY);

		if (domains == null) {
			logger.warn("Remote Domains '{}' node not found or not an array", ExoConstants.CONFIG_KEY_SHARING_POLICY);
			return Collections.emptyList();
		}
		CollectionType collectionType = mapper.getTypeFactory().constructCollectionType(List.class, SharingPolicy.class);
		return mapper.convertValue(domains, collectionType);
	}
} 