package io.syrix.products.microsoft.base;

import java.time.Instant;

public class ParameterChangeResult {

	private Instant timeStamp;
	private String correlationId;
	private String parameter;
	private Object prevValue;
	private Object newValue;
	private ParameterChangeStatus status;

	public ParameterChangeResult() {
		this.timeStamp = Instant.now();
	}

	@SuppressWarnings("unused")
	public Instant getTimeStamp() {
		return timeStamp;
	}

	public ParameterChangeResult timeStamp(Instant timeStamp) {
		this.timeStamp = timeStamp;
		return this;
	}

	@SuppressWarnings("unused")
	public String getCorrelationId() {
		return correlationId;
	}

	public ParameterChangeResult correlationId(String correlationId) {
		this.correlationId = correlationId;
		return this;
	}

	public String getParameter() {
		return parameter;
	}

	public ParameterChangeResult parameter(String parameter) {
		this.parameter = parameter;
		return this;
	}

	public Object getPrevValue() {
		return prevValue;
	}

	public ParameterChangeResult prevValue(Object prevValue) {
		this.prevValue = prevValue;
		return this;
	}

	public Object getNewValue() {
		return newValue;
	}

	public ParameterChangeResult newValue(Object newValue) {
		this.newValue = newValue;
		return this;
	}

	public ParameterChangeStatus getStatus() {
		return status;
	}

	public ParameterChangeResult status(ParameterChangeStatus status) {
		this.status = status;
		return this;
	}

	public static ParameterChangeResult failed(String parameter) {
		return new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter(parameter)
				.status(ParameterChangeStatus.FAILED);
	}
}
