package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.entra.model.DynamicGroupConfiguration;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for managing dynamic security groups in Microsoft Entra ID.
 * Provides functionality for creating, retrieving, and validating dynamic groups
 * specifically for guest user management.
 */
public class DynamicGroupService {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicGroupService.class);
    private static final String GROUPS_ENDPOINT = "/groups";
    
    private final MicrosoftGraphClient graphClient;
    private final ObjectMapper objectMapper;
    
    public DynamicGroupService(MicrosoftGraphClient graphClient) {
        this.graphClient = graphClient;
        this.objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
    }
    
    /**
     * Retrieves all dynamic groups from Microsoft Entra ID
     * 
     * @return CompletableFuture containing list of dynamic group configurations
     */
    public CompletableFuture<List<DynamicGroupConfiguration>> getAllDynamicGroups() {
        logger.info("Retrieving all dynamic groups from Microsoft Entra ID");
        
        return graphClient.makeGraphRequest(
                GraphRequest.builder()
                        .v1()
                        .withEndpoint(GROUPS_ENDPOINT)
                        .addQueryParam("$filter", "groupTypes/any(c:c eq 'DynamicMembership')")
                        .build()
        ).thenApply(this::parseGroupsResponse)
         .exceptionally(ex -> {
             logger.error("Failed to retrieve dynamic groups", ex);
             throw new GraphClientException("Failed to retrieve dynamic groups", ex);
         });
    }
    
    /**
     * Retrieves dynamic groups that are properly configured for guest user management
     * 
     * @return CompletableFuture containing list of properly configured guest user groups
     */
    public CompletableFuture<List<DynamicGroupConfiguration>> getProperlyConfiguredGuestGroups() {
        return getAllDynamicGroups()
                .thenApply(groups -> groups.stream()
                        .filter(DynamicGroupConfiguration::isProperlyConfiguredForGuestUsers)
                        .toList());
    }
    
    /**
     * Retrieves dynamic groups that appear to be for guest users but are misconfigured
     * 
     * @return CompletableFuture containing list of misconfigured guest user groups
     */
    public CompletableFuture<List<DynamicGroupConfiguration>> getMisconfiguredGuestGroups() {
        return getAllDynamicGroups()
                .thenApply(groups -> groups.stream()
                        .filter(DynamicGroupConfiguration::isMisconfiguredGuestGroup)
                        .toList());
    }
    
    /**
     * Checks if any properly configured dynamic guest user groups exist
     * 
     * @return CompletableFuture<Boolean> true if at least one properly configured group exists
     */
    public CompletableFuture<Boolean> hasProperlyConfiguredGuestGroups() {
        return getProperlyConfiguredGuestGroups()
                .thenApply(groups -> !groups.isEmpty());
    }
    
    /**
     * Creates a new dynamic security group for guest user management
     * 
     * @param displayName The display name for the new group
     * @param description The description for the new group
     * @return CompletableFuture containing the created group configuration
     */
    public CompletableFuture<DynamicGroupConfiguration> createDynamicGuestGroup(String displayName, String description) {
        logger.info("Creating dynamic guest user group: {}", displayName);
        
        DynamicGroupConfiguration groupConfig = new DynamicGroupConfiguration(displayName, description);
        
        return createGroup(groupConfig);
    }
    
    /**
     * Creates a dynamic group using the Microsoft Graph API
     * 
     * @param groupConfig The group configuration to create
     * @return CompletableFuture containing the created group configuration
     */
    public CompletableFuture<DynamicGroupConfiguration> createGroup(DynamicGroupConfiguration groupConfig) {
        logger.info("createGroup method called with config: {}", groupConfig.getDisplayName());
        
        try {
            // Use ObjectNode like ServicePrincipalCreator instead of full object serialization
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("displayName", groupConfig.getDisplayName());
            requestBody.put("description", groupConfig.getDescription());
            requestBody.put("securityEnabled", groupConfig.getSecurityEnabled());
            requestBody.put("mailEnabled", groupConfig.getMailEnabled());
            requestBody.put("mailNickname", groupConfig.getMailNickname());
            requestBody.put("membershipRule", groupConfig.getMembershipRule());
            requestBody.put("membershipRuleProcessingState", groupConfig.getMembershipRuleProcessingState());
            
            // Add groupTypes array
            ArrayNode groupTypes = objectMapper.createArrayNode();
            groupTypes.add("DynamicMembership");
            requestBody.set("groupTypes", groupTypes);
            
            String requestBodyStr = objectMapper.writeValueAsString(requestBody);
            HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(requestBodyStr);
            
            logger.info("Creating dynamic group with manual payload: {}", requestBodyStr);
            
            return graphClient.makeGraphRequest(GraphRequest.builder()
                    .v1()
                    .withEndpoint(GROUPS_ENDPOINT)
                    .withMethod(HttpMethod.POST)
                    .addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
                    .withBody(body)
                    .build()
            ).thenApply(response -> {
                if (response != null && !response.has(Constants.ERROR_FIELD)) {
                    logger.info("Successfully created dynamic group");
                    try {
                        DynamicGroupConfiguration createdGroup = objectMapper.treeToValue(response, DynamicGroupConfiguration.class);
                        logger.info("Created group with ID: {}", createdGroup.getId());
                        return createdGroup;
                    } catch (Exception e) {
                        logger.error("Failed to parse created group response", e);
                        throw new GraphClientException("Failed to parse created group response", e);
                    }
                } else {
                    String error = response != null && response.has(Constants.ERROR_FIELD) ?
                            response.get(Constants.ERROR_FIELD).asText() :
                            "Unknown error creating dynamic group";
                    throw new GraphClientException(error);
                }
            }).exceptionally(ex -> {
                String errorMessage = String.format("Failed to create dynamic group %s: %s",
                        groupConfig.getDisplayName(), ex.getMessage());
                logger.error(errorMessage);
                throw new GraphClientException(errorMessage, ex);
            });
            
        } catch (Exception e) {
            String errorMessage = String.format("Error preparing request for group %s: %s",
                    groupConfig.getDisplayName(), e.getMessage());
            logger.error(errorMessage);
            throw new GraphClientException(errorMessage, e);
        }
    }
    
    /**
     * Updates an existing dynamic group's configuration
     * 
     * @param groupId The ID of the group to update
     * @param updates The updates to apply
     * @return CompletableFuture containing the update result
     */
    public CompletableFuture<JsonNode> updateGroup(String groupId, ObjectNode updates) {
        try {
            String jsonPayload = objectMapper.writeValueAsString(updates);
            HttpRequest.BodyPublisher requestBody = HttpRequest.BodyPublishers.ofString(jsonPayload);
            
            logger.info("Updating group {} with payload: {}", groupId, jsonPayload);
            
            return graphClient.makeGraphRequest(
                    GraphRequest.builder()
                            .v1()
                            .withMethod(HttpMethod.PATCH)
                            .withEndpoint(GROUPS_ENDPOINT + "/" + groupId)
                            .addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
                            .withBody(requestBody)
                            .build()
            ).thenApply(response -> {
                logger.info("Successfully updated group: {}", groupId);
                return response;
            }).exceptionally(ex -> {
                logger.error("Failed to update group: {}", groupId, ex);
                throw new GraphClientException("Failed to update group: " + ex.getMessage(), ex);
            });
            
        } catch (Exception e) {
            logger.error("Failed to serialize group updates", e);
            throw new GraphClientException("Failed to serialize group updates", e);
        }
    }
    
    /**
     * Validates that a group's membership rule is correct for guest users
     * 
     * @param membershipRule The rule to validate
     * @return true if the rule is valid for guest users
     */
    public boolean isValidGuestUserRule(String membershipRule) {
        if (membershipRule == null || membershipRule.trim().isEmpty()) {
            return false;
        }
        
        String rule = membershipRule.trim();
        return rule.equals("(user.userType -eq \"Guest\")") ||
               rule.equals("(user.userType -eq 'Guest')") ||
               rule.equals("user.userType -eq \"Guest\"") ||
               rule.equals("user.userType -eq 'Guest'");
    }
    
    /**
     * Fixes a misconfigured dynamic group to properly manage guest users
     * 
     * @param groupId The ID of the group to fix
     * @return CompletableFuture containing the update result
     */
    public CompletableFuture<JsonNode> fixGuestGroupConfiguration(String groupId) {
        logger.info("Fixing configuration for group: {}", groupId);
        
        ObjectNode updates = objectMapper.createObjectNode();
        updates.put("membershipRule", "(user.userType -eq \"Guest\")");
        updates.put("membershipRuleProcessingState", "On");
        updates.put("securityEnabled", true);
        updates.put("mailEnabled", false);
        
        return updateGroup(groupId, updates);
    }
    
    /**
     * Parses the Microsoft Graph API response for groups
     * 
     * @param response The JSON response from Microsoft Graph
     * @return List of DynamicGroupConfiguration objects
     */
    private List<DynamicGroupConfiguration> parseGroupsResponse(JsonNode response) {
        try {
            List<DynamicGroupConfiguration> groups = new ArrayList<>();
            
            if (response.has("value") && response.get("value").isArray()) {
                for (JsonNode groupNode : response.get("value")) {
                    DynamicGroupConfiguration group = objectMapper.treeToValue(groupNode, DynamicGroupConfiguration.class);
                    groups.add(group);
                }
            }
            
            logger.info("Parsed {} dynamic groups from response", groups.size());
            return groups;
            
        } catch (Exception e) {
            logger.error("Failed to parse groups response", e);
            throw new GraphClientException("Failed to parse groups response", e);
        }
    }
}