package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.MicrosoftGraphClient;

import java.util.concurrent.CompletableFuture;

/**
 * Implements remediation for MS.AAD.8.1v1: Restrict Guest User Access to Directory Objects.

 * This remediator restricts guest user permissions to limit directory object access by:
 * 1. Setting guestUserRoleId to the restricted Guest User role
 * 2. Disabling the ability for guests to read other users in the directory
 */
@PolicyRemediator("MS.AAD.8.1v1")
public class EntraIDGuestDirectoryAccessRemediator extends BaseGuestAccessRemediator {
	/**
	 * Constructor for Guest Directory Access Remediator.
	 *
	 * @param graphClient Microsoft Graph API client
	 */
	public EntraIDGuestDirectoryAccessRemediator(MicrosoftGraphClient graphClient) {
		super(graphClient);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation to restrict guest user access to directory objects");

		return getCurrentAuthorizationPolicy()
				.thenCompose(this::checkAndUpdatePolicy)
				.exceptionally(ex -> {
					logger.error("Exception during guest directory access remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if the policy needs updating and updates it if needed.
	 *
	 * @param currentPolicy The current authorization policy
	 * @return CompletableFuture with remediation result
	 */
	private CompletableFuture<JsonNode> checkAndUpdatePolicy(JsonNode currentPolicy) {
		boolean guestRoleCorrect = isStringPropertySetCorrectly(currentPolicy,
				"guestUserRoleId",
				GUEST_USER_ROLE_ID);
		boolean readPermissionCorrect = isBooleanPropertySetCorrectly(currentPolicy,
				"defaultUserRolePermissions.allowedToReadOtherUsers", false);

		if (guestRoleCorrect && readPermissionCorrect) {
			logger.info("Guest user access is already properly restricted");
			return CompletableFuture.completedFuture(
					IPolicyRemediator.success(getPolicyId(),
							"Guest user access is already properly restricted").join());
		}

		return updatePolicy();
	}

	/**
	 * Updates the authorization policy to restrict guest access.
	 *
	 * @return CompletableFuture with remediation result
	 */
	private CompletableFuture<JsonNode> updatePolicy() {
		// Create minimal update payload
		ObjectNode updatePayload = objectMapper.createObjectNode();

		// Set guest user role ID
		updatePayload.put("guestUserRoleId", GUEST_USER_ROLE_ID);

		// Configure default user role permissions
		ObjectNode defaultUserRolePermissions = objectMapper.createObjectNode();
		defaultUserRolePermissions.put("allowedToReadOtherUsers", false);
		updatePayload.set("defaultUserRolePermissions", defaultUserRolePermissions);

		return updateAuthorizationPolicy(
				updatePayload,
				"Guest user access to directory objects has been restricted");
	}
}