package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Service to process enterprise applications data.
 */
public class EnterpriseApplicationProcessor {
	private static final Logger logger = LoggerFactory.getLogger(EnterpriseApplicationProcessor.class);
	private final ObjectMapper objectMapper;
	EnterpriseAppEnricher enricher = null;

	public EnterpriseApplicationProcessor(ObjectMapper objectMapper, EnterpriseAppEnricher enricher) {
		this.objectMapper = objectMapper;
		this.enricher = enricher;
	}

	public List<EnterpriseApplication> processApplications(JsonNode applicationsJson) {
		try {
			if (!applicationsJson.isArray()) {
				logger.warn("Expected array of applications, got: {}", applicationsJson.getNodeType());
				return new ArrayList<>();
			}

			List<EnterpriseApplication> applications = new ArrayList<>();

			for (JsonNode appNode : applicationsJson) {
				try {
					EnterpriseApplication app = objectMapper.treeToValue(appNode, EnterpriseApplication.class);
					if (app != null) {
						this.enricher.enrich(app);
						applications.add(app);
					}
				} catch (Exception e) {
					logger.error("Failed to process application: {}", appNode, e);
					// Continue processing other applications
				}
			}

			return applications;

		} catch (Exception e) {
			logger.error("Failed to process enterprise applications", e);
			throw new RuntimeException("Failed to process enterprise applications: " + e.getMessage(), e);
		}
	}
}