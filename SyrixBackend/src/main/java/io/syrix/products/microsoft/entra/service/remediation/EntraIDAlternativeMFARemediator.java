package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.products.microsoft.entra.model.EntraIDAuthenticationStrength;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;

import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.entra.model.EntraIDAuthenticationStrength.MFA;

/**
 * Implements remediation for MS.AAD.3.2v1: If phishing-resistant MFA has not been enforced,
 * an alternative MFA method SHALL be enforced for all users.
 * This remediation creates a Conditional Access Policy that requires standard MFA
 * if phishing-resistant MFA is not already enforced.
 */
@PolicyRemediator("MS.AAD.3.2v1")
public class EntraIDAlternativeMFARemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Alternative MFA Enforcement";
	private EntraIDAuthenticationStrength authenticationstrength = MFA;
	private final boolean useBuiltInMfa;
	/**
	 * Constructor with custom user configuration.
	 *
	 * @param graphClient            Microsoft Graph API client
	 * @param userConfiguration      Configuration specifying which users to include/exclude
	 * @param authenticationStrength The authentication strength to enforce
	 * @param policyState            The state of the policy
	 */
	public EntraIDAlternativeMFARemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState,
			EntraIDAuthenticationStrength authenticationStrength,
			boolean useBuiltInMfa) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);

		this.useBuiltInMfa = useBuiltInMfa;
		this.authenticationstrength = authenticationStrength;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for alternative MFA enforcement. Checking if phishing-resistant MFA is already enforced...");

		// First check if phishing-resistant MFA is already enforced
		return graphClient.isPhishingResistantMfaEnforced()
				.thenCompose(isEnforced -> {
					if (Boolean.TRUE.equals(isEnforced)) {
						logger.info("Phishing-resistant MFA is already enforced. Skipping alternative MFA enforcement.");
						return CompletableFuture.completedFuture(
								IPolicyRemediator.success(getPolicyId(),
										"Phishing-resistant MFA is already enforced. No need for alternative MFA.").join());
					}

					logger.info("Phishing-resistant MFA is not enforced. Proceeding with alternative MFA enforcement.");
					// Call the parent remediate method to check for and create the policy
					return super.remediate();
				})
				.exceptionally(ex -> {
					logger.error("Exception checking phishing-resistant MFA policy", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Grant controls with standard MFA
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "OR");

		// For standard MFA, we must choose EITHER authentication strength OR built-in controls
		// We can't include both properties in the JSON even if one is null/empty
		if (useBuiltInMfa) {
			// Use built-in MFA control
			ArrayNode builtInControls = objectMapper.createArrayNode();
			builtInControls.add("mfa");
			grantControls.set("builtInControls", builtInControls);

			// Don't include authenticationStrength at all
		} else {
			// Use authentication strength
			ObjectNode authStrength = objectMapper.createObjectNode();
			authStrength.put("id", EntraIDAuthenticationStrength.MFA.getId());
			grantControls.set("authenticationStrength", authStrength);

			// Empty array for built-in controls
			grantControls.set("builtInControls", objectMapper.createArrayNode());
		}

		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}

	/**
	 * Verifies the authentication strength policy exists.
	 *
	 * @return CompletableFuture<Boolean> - true if the auth strength policy exists, false otherwise
	 */
	public CompletableFuture<Boolean> verifyAuthStrengthPolicyExists() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AUTH_STRENGTH_ENDPOINT + "/" + authenticationstrength.getId())
						.build()
		).thenApply(response -> {
			if (response != null && response.has("id")) {
				logger.info("Verified {} policy exists", authenticationstrength.getDisplayName());
				return true;
			}
			logger.warn("{} policy not found", authenticationstrength.getDisplayName());
			return false;
		}).exceptionally(ex -> {
			logger.error("Error verifying authentication strength policy", ex);
			return false;
		});
	}
}