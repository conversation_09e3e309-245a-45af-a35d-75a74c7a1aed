package io.syrix.products.microsoft.defender.remediation;

import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.SecurityAlertRemediatorBase;
import io.syrix.protocols.client.PowerShellClient;

import java.util.List;

/**
 * Remediator for MS.DEFENDER.5.1v1 which enables critical security alerts in Microsoft 365 Defender.
 * <p>
 * This class implements the MS.DEFENDER.5.1v1 control to enable required alerts:
 * a. Suspicious email sending patterns detected
 * b. Suspicious Connector Activity
 * c. Suspicious Email Forwarding Activity
 * d. Messages have been delayed
 * e. Tenant restricted from sending unprovisioned email
 * f. Tenant restricted from sending email
 * g. A potentially malicious URL click was detected

 * These alerts help detect potential attacks including phishing and suspicious activities.

 * MITRE ATT&CK TTP Mapping:
 * - T1562: Impair Defenses
 *   - T1562.006: Indicator Blocking
 */
@PolicyRemediator("MS.DEFENDER.5.1v1")
public class DefenderRequiredAlertsRemediator extends SecurityAlertRemediatorBase {

	/**
	 * Constructs a new DefenderRequiredAlertsRemediator with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 * @param tenantId The tenant ID to use for creating the hash suffix
	 */
	public DefenderRequiredAlertsRemediator(PowerShellClient powershellClient, String tenantId, List<String> additionalAlerts) {
		super(powershellClient, tenantId, additionalAlerts);
	}
}