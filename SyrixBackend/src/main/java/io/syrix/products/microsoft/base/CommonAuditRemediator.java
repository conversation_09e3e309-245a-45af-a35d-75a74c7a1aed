package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static io.syrix.products.microsoft.exo.ExoConstants.GET_ORGANIZATION_CONFIG;
import static io.syrix.products.microsoft.exo.ExoConstants.SET_ORGANIZATION_CONFIG;

/**
 * Common base implementation for Microsoft Purview Audit remediation.
 * <p>
 * Provides the implementation for:
 * <ul>
 *     <li>Enabling Microsoft Purview Audit (Standard)</li>
 *     <li>Enabling Microsoft Purview Audit (Premium)</li>
 *     <li>Configuring Audit Log Retention for OMB M-21-31 compliance</li>
 * </ul>
 * <p>
 * This class is meant to be extended by specific remediator implementations
 * like MicrosoftLogAuditRetentionRemediator and DefenderAuditRemediator.
 */
public abstract class CommonAuditRemediator extends RemediatorBase {
	private static final String ADMIN_AUDIT_LOG_CONFIG = "Get-AdminAuditLogConfig";
	private static final String SET_ADMIN_AUDIT_LOG_CONFIG = "Set-AdminAuditLogConfig";
	private static final String CREATE_AUDIT_RETENTION_POLICY = "New-UnifiedAuditLogRetentionPolicy";
	private static final String GET_AUDIT_RETENTION_POLICY = "Get-UnifiedAuditLogRetentionPolicy";

	private static final String UNIFIED_AUDIT_LOG_ENABLED = "UnifiedAuditLogIngestionEnabled";
	private static final String AUDIT_DISABLED = "AuditDisabled";
	private static final String POLICY_NAME = "OMB-12-Month-Retention";
	private static final String RETENTION_DURATION = "TwelveMonths";
	private static final String RECORD_TYPES = "RecordTypes";
	private static final String NAME = "Name";
	public static final String NO_RESPONSE = "No response";
	public static final String STATUS = "status";
	public static final String POLICY_ID = "policyId";
	public static final String MESSAGE = "message";

	protected final PowerShellClient powershellClient;
	protected final ObjectMapper objectMapper = new ObjectMapper();
	protected String cppsEndpoint;

	/**
	 * Constructs a new CommonAuditRemediator with the specified PowerShell client.
	 *
	 * @param powershellClient The PowerShell client
	 */
	public CommonAuditRemediator(PowerShellClient powershellClient) {
		this.powershellClient = powershellClient;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting Microsoft Purview Audit Logging and Retention remediation");

		return powershellClient.getCPPSEndpoint().thenCompose(tmpCppsEndpoint -> {
			this.cppsEndpoint = tmpCppsEndpoint;
			return checkAndEnableStandardAudit()
					.thenCompose(standardEnabled -> {
						if (Boolean.FALSE.equals(standardEnabled)) {
							return CompletableFuture.completedFuture(
									createFailureNode("Failed to enable Standard Audit logging"));
						}

						return checkAndEnablePremiumAudit()
								.thenCompose(premiumEnabled -> {
									if (Boolean.FALSE.equals(premiumEnabled)) {
										return CompletableFuture.completedFuture(
												createPartialSuccessNode("Enabled Standard Audit but failed to enable Premium Audit"));
									}

									return configureAuditRetention()
											.thenApply(retentionConfigured -> {
												if (Boolean.FALSE.equals(retentionConfigured)) {
													return createPartialSuccessNode(
															"Enabled Standard and Premium Audit but failed to configure 12-month retention");
												}

												return createSuccessNode(
														"Successfully enabled Microsoft Purview Audit (Standard and Premium) " +
																"and configured 12-month retention policy");
											});
								});
					})
					.exceptionally(ex -> {
						logger.error("Exception during Microsoft Purview Audit remediation", ex);
						return createFailureNode("Failed to remediate Microsoft Purview Audit: " + ex.getMessage());
					});
		});
	}

	private CompletableFuture<Boolean> checkAndEnableStandardAudit() {
		logger.info("Checking Microsoft Purview Audit (Standard) status");
		return getAdminAuditLogConfig()
				.thenCompose(this::handleAuditLogConfig);
	}

	private CompletableFuture<JsonNode> getAdminAuditLogConfig() {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(ADMIN_AUDIT_LOG_CONFIG, new HashMap<>())
		);
	}

	private CompletableFuture<Boolean> handleAuditLogConfig(JsonNode result) {
		if (!isValidAuditLogConfig(result)) {
			return CompletableFuture.completedFuture(false);
		}

		if (isStandardAuditEnabled(result)) {
			logger.info("Microsoft Purview Audit (Standard) is already enabled");
			return CompletableFuture.completedFuture(true);
		}

		return enableStandardAudit();
	}

	private boolean isValidAuditLogConfig(JsonNode result) {
		if (result == null || result.has(Constants.ERROR_FIELD)) {
			logger.error("Failed to get AdminAuditLogConfig: {}",
					result != null ? result.get(Constants.ERROR_FIELD).asText() : NO_RESPONSE);
			return false;
		}
		return true;
	}

	private boolean isStandardAuditEnabled(JsonNode result) {
		return result.has(UNIFIED_AUDIT_LOG_ENABLED) &&
				result.get(UNIFIED_AUDIT_LOG_ENABLED).asBoolean();
	}

	private CompletableFuture<Boolean> enableStandardAudit() {
		logger.info("Enabling Microsoft Purview Audit (Standard)");
		Map<String, Object> params = Map.of(UNIFIED_AUDIT_LOG_ENABLED, true);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(SET_ADMIN_AUDIT_LOG_CONFIG, params)
		).thenApply(this::handleEnableStandardAuditResult);
	}

	private boolean handleEnableStandardAuditResult(JsonNode enableResult) {
		if (enableResult != null && !enableResult.has(Constants.ERROR_FIELD)) {
			logger.info("Successfully enabled Microsoft Purview Audit (Standard)");
			return true;
		}
		logger.error("Failed to enable Microsoft Purview Audit (Standard): {}",
				enableResult != null ? enableResult.get(Constants.ERROR_FIELD).asText() : NO_RESPONSE);
		return false;
	}

	private CompletableFuture<Boolean> checkAndEnablePremiumAudit() {
		logger.info("Checking Microsoft Purview Audit (Premium) status");
		return getOrganizationConfig()
				.thenCompose(this::enablePremiumAuditIfNeeded);
	}

	private CompletableFuture<JsonNode> getOrganizationConfig() {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(GET_ORGANIZATION_CONFIG, new HashMap<>())
		);
	}

	private CompletableFuture<Boolean> enablePremiumAuditIfNeeded(JsonNode result) {
		if (!isValidOrganizationConfig(result)) {
			return CompletableFuture.completedFuture(false);
		}

		if (!isPremiumAuditDisabled(result)) {
			logger.info("Microsoft Purview Audit (Premium) is already enabled");
			return CompletableFuture.completedFuture(true);
		}

		return enablePremiumAudit();
	}

	private boolean isValidOrganizationConfig(JsonNode result) {
		if (result == null || result.has(Constants.ERROR_FIELD)) {
			logger.error("Failed to get OrganizationConfig: {}",
					result != null ? result.get(Constants.ERROR_FIELD).asText() : NO_RESPONSE);
			return false;
		}
		return true;
	}

	private boolean isPremiumAuditDisabled(JsonNode result) {
		return !result.has(AUDIT_DISABLED) || result.get(AUDIT_DISABLED).asBoolean();
	}

	private CompletableFuture<Boolean> enablePremiumAudit() {
		logger.info("Enabling Microsoft Purview Audit (Premium)");
		Map<String, Object> params = Map.of(AUDIT_DISABLED, false);

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(SET_ORGANIZATION_CONFIG, params)
		).thenApply(this::handleEnableResult);
	}

	private boolean handleEnableResult(JsonNode enableResult) {
		if (enableResult != null && !enableResult.has(Constants.ERROR_FIELD)) {
			logger.info("Successfully enabled Microsoft Purview Audit (Premium)");
			return true;
		}
		logger.error("Failed to enable Microsoft Purview Audit (Premium): {}",
				enableResult != null ? enableResult.get(Constants.ERROR_FIELD).asText() : NO_RESPONSE);
		return false;
	}

	private CompletableFuture<Boolean> configureAuditRetention() {
		logger.info("Checking existing audit log retention policies");
		return getExistingRetentionPolicies()
				.thenCompose(this::handleRetentionPolicies);
	}

	private CompletableFuture<JsonNode> getExistingRetentionPolicies() {
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(GET_AUDIT_RETENTION_POLICY, new HashMap<>(), this.cppsEndpoint)
		);
	}

	private CompletableFuture<Boolean> handleRetentionPolicies(JsonNode result) {
		if (result != null && !result.has(Constants.ERROR_FIELD) && hasPolicyNameMatch(result)) {
			logger.info("12-month audit retention policy already exists");
			return CompletableFuture.completedFuture(true);
		}
		return createRetentionPolicy();
	}

	private boolean hasPolicyNameMatch(JsonNode result) {
		if (result.isArray()) {
			return StreamSupport.stream(result.spliterator(), false)
					.anyMatch(this::isPolicyNameMatch);
		}
		return isPolicyNameMatch(result);
	}

	private boolean isPolicyNameMatch(JsonNode policy) {
		return policy.has(NAME) && POLICY_NAME.equals(policy.get(NAME).asText());
	}

	private CompletableFuture<Boolean> createRetentionPolicy() {
		logger.info("Creating 12-month audit retention policy");
		Map<String, Object> params = createPolicyParams();

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(CREATE_AUDIT_RETENTION_POLICY, params, this.cppsEndpoint)
		).thenApply(this::handlePolicyCreationResult);
	}

	private Map<String, Object> createPolicyParams() {
		Map<String, Object> params = new HashMap<>();
		params.put(NAME, POLICY_NAME);
		params.put("RetentionDuration", RETENTION_DURATION);
		params.put("Priority", 100);  // Required parameter for priority ordering

		// Instead of "All", we need to use specific record types
		// Creating an array of most relevant security and compliance audit types
		String[] recordTypes = new String[] {
				"ExchangeAdmin",
				"ExchangeItem",
				"ExchangeItemGroup",
				"SharePoint",
				"AzureActiveDirectory",
				"AzureActiveDirectoryAccountLogon",
				"ComplianceDLPSharePoint",
				"ComplianceDLPExchange",
				"MicrosoftTeams",
				"ThreatIntelligence",
				"SecurityComplianceAlerts",
				"MailSubmission",
				"MicrosoftFlow",
				"AeD",
				"MicrosoftStream",
				"SecurityComplianceUserChange",
				"AuditSearch",
				"AuditRetentionPolicy",
				"AuditConfig"
		};

		params.put(RECORD_TYPES, recordTypes);
		return params;
	}

	private boolean handlePolicyCreationResult(JsonNode createResult) {
		if (createResult != null && !createResult.has(Constants.ERROR_FIELD)) {
			logger.info("Successfully created 12-month audit retention policy");
			return true;
		}
		logger.error("Failed to create audit retention policy: {}",
				createResult != null ? createResult.get(Constants.ERROR_FIELD).asText() : NO_RESPONSE);
		return false;
	}

	protected JsonNode createSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(STATUS, "success");
		node.put(POLICY_ID, getPolicyId());
		node.put(MESSAGE, message);
		return node;
	}

	protected JsonNode createPartialSuccessNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(STATUS, "partial");
		node.put(POLICY_ID, getPolicyId());
		node.put(MESSAGE, message);
		return node;
	}

	protected JsonNode createFailureNode(String message) {
		ObjectNode node = objectMapper.createObjectNode();
		node.put(STATUS, "failed");
		node.put(POLICY_ID, getPolicyId());
		node.put(MESSAGE, message);
		return node;
	}
}