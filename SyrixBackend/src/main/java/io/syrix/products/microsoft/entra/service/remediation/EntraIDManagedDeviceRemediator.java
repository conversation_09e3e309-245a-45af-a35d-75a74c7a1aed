package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.protocols.client.MicrosoftGraphClient;

/**
 * Implements remediation for MS.AAD.3.7v1: Managed devices SHOULD be required for authentication.

 * This remediation creates a Conditional Access Policy that requires devices to be
 * managed (Intune, Hybrid Azure AD joined, or Azure AD joined) for authentication.
 */
@PolicyRemediator("MS.AAD.3.7v1")
public class EntraIDManagedDeviceRemediator extends BaseConditionalAccessRemediator {
	private static final String POLICY_NAME_DEFAULT = "Managed Device Requirement required for authentication";

	/**
	 * Constructor with custom user configuration.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param userConfiguration Configuration specifying which users to include/exclude
	 * @param policyState The state of the policy
	 */
	public EntraIDManagedDeviceRemediator(
			MicrosoftGraphClient graphClient,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		super(graphClient,
				POLICY_NAME_DEFAULT,
				userConfiguration,
				policyState);
	}

	@Override
	protected ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = createBasePolicyPayload();

		// Get the conditions object and add device platform requirements
		ObjectNode conditions = (ObjectNode) policyPayload.get("conditions");

		// Configure platforms - all platforms
		ObjectNode platforms = objectMapper.createObjectNode();
		ArrayNode includePlatforms = objectMapper.createArrayNode();
		includePlatforms.add("all");
		platforms.set("includePlatforms", includePlatforms);
		platforms.set("excludePlatforms", objectMapper.createArrayNode());
		conditions.set("platforms", platforms);

		// Configure grant controls to require device compliance
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "AND");

		// Require managed device or Hybrid Azure AD join
		ArrayNode builtInControls = objectMapper.createArrayNode();
		builtInControls.add("compliantDevice");
		builtInControls.add("domainJoinedDevice");
		grantControls.set("builtInControls", builtInControls);

		// Empty arrays for other controls
		grantControls.set("customAuthenticationFactors", objectMapper.createArrayNode());
		grantControls.set("termsOfUse", objectMapper.createArrayNode());

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}
}