package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.2.2v1")
public class TeamsAllowTeamsConsumerInboundRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	private final ObjectMapper objectMapper = new ObjectMapper();

	public TeamsAllowTeamsConsumerInboundRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowTeamsConsumerInboundRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> federationConfigurations = getFederationConfigurations();

		if (federationConfigurations.isEmpty()) {
			logger.error("No federation configurations found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No federation configurations found in configuration"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

		for (TeamsTenantFederationConfiguration curTenantConfig : federationConfigurations) {
			if (curTenantConfig.allowTeamsConsumer || curTenantConfig.allowTeamsConsumerInbound) {
				TenantFederationConfiguration curConfig = new TenantFederationConfiguration();
				curConfig.identity = curTenantConfig.identity;
				curConfig.allowTeamsConsumer = curTenantConfig.allowTeamsConsumer;
				curConfig.allowTeamsConsumerInbound = curTenantConfig.allowTeamsConsumerInbound;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = curTenantConfig.identity;
				newConfig.allowTeamsConsumer = false;
				newConfig.allowTeamsConsumerInbound = false;

				results.add(fixConfig_(curConfig, newConfig));
			}
		}

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig_(TenantFederationConfiguration curConfig, TenantFederationConfiguration newConfig) {
		try {
			FederationConfig prevConfig = new FederationConfig(curConfig.allowTeamsConsumer, curConfig.allowTeamsConsumerInbound);
			FederationConfig newFederationConfig = new FederationConfig(newConfig.allowTeamsConsumer, newConfig.allowTeamsConsumerInbound);

			ParameterChangeResult param = new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter("federationConfig: " + curConfig.identity)
					.prevValue(objectMapper.writeValueAsString(prevConfig))
					.newValue(objectMapper.writeValueAsString(newFederationConfig));

			return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(newConfig))
					.thenApply(config -> {
						param.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration fixed: " + newConfig.identity, List.of(param));
					})
					.exceptionally(ex -> {
						logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
						param.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(param));
					});
		} catch (Exception ex) {
			logger.error("Failed to serialize configuration", ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Failed to serialize configuration: " + ex.getMessage()));
		}
	}

	private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					List<PolicyChangeResult> results = futures.stream()
							.map(CompletableFuture::join)
							.toList();

					List<ParameterChangeResult> allChanges = results.stream()
							.map(PolicyChangeResult::getChanges)
							.filter(Objects::nonNull)
							.flatMap(Collection::stream)
							.filter(Objects::nonNull)
							.toList();

					// Count results by type
					long successCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.SUCCESS)
							.count();
					long failedCount = results.stream()
							.filter(r -> r.getResult() == RemediationResult.FAILED)
							.count();

					// Determine overall result status
					if (failedCount == 0) {
						return IPolicyRemediator.success_(getPolicyId(), "All federation configurations fixed successfully", allChanges);
					} else if (successCount == 0) {
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to fix any federation configurations", allChanges);
					} else {
						return IPolicyRemediator.partial_success_(getPolicyId(),
								"Fixed " + successCount + " configurations, failed to fix " + failedCount + " configurations",
								allChanges);
					}
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String[] parts = change.getParameter().split(": ");
				String identity = parts[1];

				TenantFederationConfiguration sourceConfig = new TenantFederationConfiguration();
				TenantFederationConfiguration remediationConfig = new TenantFederationConfiguration();
				sourceConfig.identity = identity;
				remediationConfig.identity = identity;

				FederationConfig prevConfig = objectMapper.readValue(change.getPrevValue().toString(), FederationConfig.class);
				FederationConfig newConfig = objectMapper.readValue(change.getNewValue().toString(), FederationConfig.class);

				sourceConfig.allowTeamsConsumer = prevConfig.allowTeamsConsumer;
				sourceConfig.allowTeamsConsumerInbound = prevConfig.allowTeamsConsumerInbound;
				remediationConfig.allowTeamsConsumer = newConfig.allowTeamsConsumer;
				remediationConfig.allowTeamsConsumerInbound = newConfig.allowTeamsConsumerInbound;

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					ParameterChangeResult param = new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter("federationConfig: " + identity)
							.prevValue(change.getNewValue().toString())
							.newValue(change.getPrevValue().toString())
							.status(ParameterChangeStatus.FAILED);

					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(param))));
					continue;
				}

				results.add(fixConfig_(remediationConfig, sourceConfig));
			}

			return combineResults(results);
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private static class FederationConfig {
		public boolean allowTeamsConsumer;
		public boolean allowTeamsConsumerInbound;

		public FederationConfig(boolean allowTeamsConsumer, boolean allowTeamsConsumerInbound) {
			this.allowTeamsConsumer = allowTeamsConsumer;
			this.allowTeamsConsumerInbound = allowTeamsConsumerInbound;
		}

		@SuppressWarnings("unused") // necessary for jackson
		public FederationConfig() {
		}
	}

}
