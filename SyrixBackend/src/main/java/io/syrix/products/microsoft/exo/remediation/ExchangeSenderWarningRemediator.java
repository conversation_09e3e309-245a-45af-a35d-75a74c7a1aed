package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.common.constants.Constants;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.*;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@PolicyRemediator("MS.EXO.7.1v1")
public class ExchangeSenderWarningRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	private static final String NEW_RULE_NAME = "External Sender Warning";

	public static final String STATE = "State";
	public static final String FIELD_NAME = "Name";
	public static final String KEY_IDENTITY = "Identity";
	private static final String KEY_FROM_SCOPE = "FromScope";
	private static final String KEY_PREPEND_SUBJECT = "PrependSubject";
	private static final String KEY_PRIORITY = "Priority";
	private static final String KEY_COMMENTS = "Comments";
	private static final String KEY_ENABLED = "Enabled";
	// Parameters for creating a new rule
	private static final TransportRule createRule = new TransportRule()
			.name(NEW_RULE_NAME)
			.fromScope("NotInOrganization")
			.prependSubject("[External]")
			.priority(0)
			.comments("This rule prepends the subject of messages from external senders with '[External]'")
			.enabled(true);

	public ExchangeSenderWarningRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext exchangeRemediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
	}

	public ExchangeSenderWarningRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		super(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for {} (External Sender Warning)", getPolicyId());

		List<TransportRule> transportRules = loadTransportRule();
		if (transportRules.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_TRANSPORT_RULE));
		}

		TransportRule externalSenderRule = findValidRule(transportRules);
		if (externalSenderRule == null) {
			TransportRule newRule = genNewRule(transportRules);
			return createRule(newRule);
		} else {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Found valid transport rule:" + externalSenderRule.name));
		}
	}

	private TransportRule genNewRule(List<TransportRule> transportRules) {
		Set<String> names = transportRules.stream()
				.map(rule -> rule.name)
				.collect(Collectors.toSet());

		if (names.contains(createRule.name)) {
			LocalDate today = LocalDate.now();
			createRule.name(NEW_RULE_NAME + " (" + today + ")");
		} else {
			return createRule;
		}

		String name = createRule.name;
		int ix = 1;
		while (names.contains(name)) {
			name = createRule.name + " [" + ix + "]";
			ix++;
		}
		return createRule.name(name);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for {} (External Sender Warning)", getPolicyId());
		ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
		if (changeResult.getStatus() != ParameterChangeStatus.SUCCESS) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy " + getPolicyId() + " skipped"));
		}

		String ruleName = changeResult.getNewValue().toString();
		return deleteRule(ruleName);
	}

	private TransportRule findValidRule(List<TransportRule> transportRules) {
		return transportRules.stream()
				.filter(rule -> "Enabled".equals(rule.state))
				.filter(rule -> "Enforce".equals(rule.mode))
				.filter(rule -> StringUtils.isNotEmpty(rule.prependSubject))
				.filter(rule -> "NotInOrganization".equals(rule.fromScope))
				.findFirst()
				.orElse(null);
	}

	@SuppressWarnings("SameParameterValue")
	private CompletableFuture<PolicyChangeResult> createRule(TransportRule transportRule) {
		logger.info("Creating new transport rule '{}' for external sender warnings", transportRule.name);

		Map<String, Object> newRuleParams = Map.of(
				FIELD_NAME, transportRule.name,
				KEY_FROM_SCOPE, transportRule.fromScope,
				KEY_PREPEND_SUBJECT, transportRule.prependSubject,
				KEY_PRIORITY, transportRule.priority,
				KEY_COMMENTS, transportRule.comments,
				KEY_ENABLED, transportRule.enabled
		);

		ParameterChangeResult changeResult = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("External Sender rule: create")
				.prevValue(null)
				.newValue(transportRule.name);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("New-TransportRule", newRuleParams))
				.thenApply(answer -> {
					if (answer != null && !answer.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully created transport rule '{}' for external sender warnings", transportRule.name);
						changeResult.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Transport rule '" + transportRule.name + "' created", List.of(changeResult));
					} else {
						String error = answer != null && answer.has(Constants.ERROR_FIELD) ?
								answer.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
						logger.error("Failed to create transport rule '{}' for external sender warnings. Err:{}", transportRule.name, error);
						changeResult.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to create transport rule '" + transportRule.name + "' for external sender warnings", List.of(changeResult));
					}
				}).exceptionally(ex -> {
							logger.error("Failed to create transport rule '{}'", transportRule.name, ex);
							changeResult.status(ParameterChangeStatus.FAILED);
							return IPolicyRemediator.failed_(getPolicyId(), "Failed to create transport rule '" + transportRule.name + "' ex:" + ex.getMessage(), List.of(changeResult));
						}
				);
	}

	private CompletableFuture<PolicyChangeResult> deleteRule(String ruleName) {
		logger.info("Deleting transport rule '{}' for external sender warnings", ruleName);

		ParameterChangeResult changeResult = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("External Sender rule: delete")
				.prevValue(ruleName)
				.newValue(null);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Remove-TransportRule", Map.of(KEY_IDENTITY, ruleName)))
				.thenApply(answer -> {
					if (answer != null && !answer.has(Constants.ERROR_FIELD)) {
						logger.info("Successfully deleted transport rule '{}' for external sender warnings", ruleName);
						changeResult.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Transport rule '" + ruleName + "' deleted", List.of(changeResult));
					} else {
						String error = answer != null && answer.has(Constants.ERROR_FIELD) ?
								answer.get(Constants.ERROR_FIELD).asText() : ExoConstants.UNKNOWN_ERROR;
						logger.error("Failed to delete transport rule '{}' for external sender warnings. Err:{}", ruleName, error);
						changeResult.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to delete transport rule '" + ruleName + "' for external sender warnings", List.of(changeResult));
					}
				}).exceptionally(ex -> {
							logger.error("Failed to delete transport rule '{}'", ruleName, ex);
							changeResult.status(ParameterChangeStatus.FAILED);
							return IPolicyRemediator.failed_(getPolicyId(), "Failed to delete transport rule '" + ruleName + "' ex:" + ex.getMessage(), List.of(changeResult));
						}
				);
	}

	private List<TransportRule> loadTransportRule() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Remote Domains");
			return Collections.emptyList();
		}

		JsonNode rules = configNode.get(ExoConstants.CONFIG_KEY_TRANSPORT_RULE);

		if (rules == null || !rules.isArray()) {
			logger.warn("'{}' node not found or not an array", ExoConstants.CONFIG_KEY_TRANSPORT_RULE);
			return Collections.emptyList();
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, TransportRule.class);
		return jsonMapper.convertValue(rules, collectionType);
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	private static class TransportRule {
		public String name;
		public String fromScope;
		public String comments;
		public int priority;
		public boolean enabled;

		public String state;
		public String mode;
		public String prependSubject;

		public TransportRule name(String name) {
			this.name = name;
			return this;
		}

		public TransportRule fromScope(String fromScope) {
			this.fromScope = fromScope;
			return this;
		}

		public TransportRule prependSubject(String prependSubject) {
			this.prependSubject = prependSubject;
			return this;
		}

		public TransportRule comments(String comments) {
			this.comments = comments;
			return this;
		}

		public TransportRule priority(int priority) {
			this.priority = priority;
			return this;
		}

		public TransportRule enabled(boolean enabled) {
			this.enabled = enabled;
			return this;
		}
	}
}