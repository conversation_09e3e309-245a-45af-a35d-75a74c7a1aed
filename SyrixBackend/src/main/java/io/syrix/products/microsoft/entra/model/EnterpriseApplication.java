package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Represents a complete enterprise application with all its details.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnterpriseApplication extends EnterpriseAppBaseModel {
	private static final Logger logger = LoggerFactory.getLogger(EnterpriseApplication.class);

	@JsonProperty("basic")
	private BasicDetails basic;

	@JsonProperty("permissions")
	private List<ApplicationPermission> permissions;

	@JsonProperty("delegatedPermissions")
	private List<DelegatedPermission> delegatedPermissions;

	@JsonProperty("branding")
	private Branding branding;

	@JsonProperty("assignedUsers")
	private List<AssignedPrincipal> assignedUsers;

	@JsonIgnore
	private RiskLevel riskLevel;

	@JsonIgnore
	private String justification;

	// Getters and setters
	public BasicDetails getBasic() {
		return basic;
	}

	public void setBasic(BasicDetails basic) {
		this.basic = basic;
	}

	public List<ApplicationPermission> getPermissions() {
		return permissions;
	}

	public void setPermissions(List<ApplicationPermission> permissions) {
		this.permissions = permissions;
	}

	public List<DelegatedPermission> getDelegatedPermissions() {
		return delegatedPermissions;
	}

	public void setDelegatedPermissions(List<DelegatedPermission> delegatedPermissions) {
		this.delegatedPermissions = delegatedPermissions;
	}

	public Branding getBranding() {
		return branding;
	}

	@JsonProperty("branding")
	public void setBranding(JsonNode node) {
		if (node.isTextual()) {
			// If branding is a direct URL string
			Branding b = new Branding();
			b.setLogoUrl(node.asText());
			this.branding = b;
		} else if (node.isObject()) {
			// If branding is an object
			try {
				this.branding = new ObjectMapper().treeToValue(node, Branding.class);
			} catch (JsonProcessingException e) {
				logger.error("Failed to parse branding object", e);
				this.branding = null;
			}
		}
	}

	public List<AssignedPrincipal> getAssignedUsers() {
		return assignedUsers;
	}

	public void setAssignedUsers(List<AssignedPrincipal> assignedUsers) {
		this.assignedUsers = assignedUsers;
	}

	@JsonIgnore
	public String getRiskLevel() {
		return riskLevel != null ? riskLevel.name() : "";
	}

	@JsonIgnore
	public void setRiskLevel(RiskLevel riskLevel) {
		this.riskLevel = riskLevel;
	}

	@JsonIgnore
	public String getJustification() {
		return justification;
	}

	@JsonIgnore
	public void setJustification(String justification) {
		this.justification = justification;
	}
}
