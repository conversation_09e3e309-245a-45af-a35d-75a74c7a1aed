package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Implements remediation for MS.AAD.1.1v1: Block legacy authentication methods.
 *
 * This remediation creates a Conditional Access Policy that blocks legacy authentication
 * protocols including Exchange ActiveSync and other legacy protocols.
 */
@PolicyRemediator("MS.AAD.1.1v1")
public class EntraIDLegacyAuthRemediator extends RemediatorBase {
	private static final String CA_POLICIES_ENDPOINT = "/identity/conditionalAccess/policies";
	private static final String POLICY_NAME = "Block Legacy Authentication";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDLegacyAuthRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for legacy authentication policy using MS Graph REST API");

		return createLegacyAuthPolicy()
				.exceptionally(ex -> {
					logger.error("Exception while creating legacy authentication policy", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> createLegacyAuthPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withMethod(HttpMethod.POST)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(CA_POLICIES_ENDPOINT)
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Legacy authentication policy created successfully");
					return IPolicyRemediator.success(getPolicyId(),
							"Legacy authentication has been blocked");
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error creating legacy authentication policy";
					logger.error("Failed to create legacy authentication policy: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			}).exceptionally(ex -> {
				logger.error("Failed to create legacy authentication policy", ex);
				return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to create legacy authentication policy", e);
		}
	}

	private ObjectNode createPolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();

		// Basic policy settings
		policyPayload.put("displayName", POLICY_NAME);
		policyPayload.put("state", "enabled");

		// Conditions
		ObjectNode conditions = objectMapper.createObjectNode();

		// Applications condition
		ObjectNode applications = objectMapper.createObjectNode();
		ArrayNode includeApps = objectMapper.createArrayNode();
		includeApps.add("All");
		applications.set("includeApplications", includeApps);
		applications.set("excludeApplications", objectMapper.createArrayNode());
		conditions.set("applications", applications);

		// Users condition
		ObjectNode users = objectMapper.createObjectNode();
		ArrayNode includeUsers = objectMapper.createArrayNode();
		includeUsers.add("All");
		users.set("includeUsers", includeUsers);
		users.set("excludeUsers", objectMapper.createArrayNode());
		conditions.set("users", users);

		// Client apps condition
		ObjectNode clientApps = objectMapper.createObjectNode();
		ArrayNode clientAppTypes = objectMapper.createArrayNode();
		clientAppTypes.add("exchangeActiveSync");
		clientAppTypes.add("other");
		clientApps.set("includeClientAppTypes", clientAppTypes);
		conditions.set("clientApps", clientApps);

		policyPayload.set("conditions", conditions);

		// Grant controls
		ObjectNode grantControls = objectMapper.createObjectNode();
		grantControls.put("operator", "OR");
		ArrayNode builtInControls = objectMapper.createArrayNode();
		builtInControls.add("block");
		grantControls.set("builtInControls", builtInControls);

		policyPayload.set("grantControls", grantControls);

		return policyPayload;
	}

	/**
	 * Checks if a legacy authentication blocking policy exists.
	 */
	public CompletableFuture<Boolean> checkLegacyAuthPolicy() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.v1()
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.withMethod(HttpMethod.GET)
						.build())
				.thenApply(response -> {
					if (response.has("value") && response.get("value").isArray()) {
						for (JsonNode policy : response.get("value")) {
							if (POLICY_NAME.equals(policy.path("displayName").asText()) &&
									"enabled".equals(policy.path("state").asText())) {
								JsonNode clientApps = policy.path("conditions").path("clientApps");
								List<String> appTypes = new ArrayList<>();
								if (clientApps.has("includeClientAppTypes")) {
									clientApps.get("includeClientAppTypes").forEach(
											app -> appTypes.add(app.asText())
									);
								}
								boolean hasRequiredTypes = appTypes.contains("exchangeActiveSync") &&
										appTypes.contains("other");
								logger.info("Legacy authentication policy is {} configured correctly",
										hasRequiredTypes ? "" : "not");
								return hasRequiredTypes;
							}
						}
					}
					logger.warn("No legacy authentication blocking policy found");
					return false;
				})
				.exceptionally(e -> {
					logger.error("Failed to check legacy authentication policy: {}", e.getMessage(), e);
					return false;
				});
	}
}