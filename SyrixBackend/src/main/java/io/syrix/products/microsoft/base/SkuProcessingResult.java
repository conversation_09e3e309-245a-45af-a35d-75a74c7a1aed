package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.JsonNode;

public class SkuProcessingResult {
	private final JsonNode licenseInfo;
	private final JsonNode servicePlans;
	private final boolean hasAadPremiumP2;
	private final boolean hasP5License;

	public SkuProcessingResult(JsonNode licenseInfo,
							   JsonNode servicePlans,
							   boolean hasAadPremiumP2,
							   boolean hasP5License) {
		this.licenseInfo = licenseInfo;
		this.servicePlans = servicePlans;
		this.hasAadPremiumP2 = hasAadPremiumP2;
		this.hasP5License = hasP5License;
	}

	public JsonNode getLicenseInfo() {
		return licenseInfo;
	}

	public boolean hasAadPremiumP2() {
		return hasAadPremiumP2;
	}

	public JsonNode getServicePlans() {
		return servicePlans;
	}

	public boolean hasP5License() {
		return hasP5License;
	}
}