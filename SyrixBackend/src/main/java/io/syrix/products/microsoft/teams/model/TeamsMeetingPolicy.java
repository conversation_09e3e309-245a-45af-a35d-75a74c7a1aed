package io.syrix.products.microsoft.teams.model;

import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.TeamsPowerShellCommandResult;

@SuppressWarnings({"CommentedOutCode"})
public class TeamsMeetingPolicy implements TeamsPowerShellCommandResult {
    public String AIInterpreter;
    public Boolean allowAnnotations;
    public Boolean allowAnonymousUsersToDialOut;
    public Boolean allowAnonymousUsersToJoinMeeting;
    public Boolean allowAnonymousUsersToStartMeeting;
    public Boolean allowAvatarsInGallery;
    public Boolean allowBreakoutRooms;
    public Boolean allowCarbonSummary;
    public String allowCartCaptionsScheduling;
    public Boolean allowChannelMeetingScheduling;
    public Boolean allowCloudRecording;
    public String allowDocumentCollaboration;
    public String allowEngagementReport;
    public Boolean allowExternalNonTrustedMeetingChat;
    public Boolean allowExternalParticipantGiveRequestControl;
    public Boolean allowIPAudio;
    public Boolean allowIPVideo;
    public Boolean allowImmersiveView;
    public Boolean allowLocalRecording;
    public Boolean allowMeetNow;
    public Boolean allowMeetingCoach;
    public Boolean allowMeetingReactions;
    public Boolean allowMeetingRegistration;
    public Boolean allowNDIStreaming;
    public Boolean allowNetworkConfigurationSettingsLookup;
    public Boolean allowOrganizersToOverrideLobbySettings;
    public Boolean allowOutlookAddIn;
    public Boolean allowPSTNUsersToBypassLobby;
    public Boolean allowParticipantGiveRequestControl;
    public Boolean allowPowerPointSharing;
    public Boolean allowPrivateMeetNow;
    public Boolean allowPrivateMeetingScheduling;
    public Boolean allowRecordingStorageOutsideRegion;
    public String allowScreenContentDigitization;
    public Boolean allowSharedNotes;
    public String allowTasksFromTranscript;
    public String allowTrackingInReport;
    public Boolean allowTranscription;
    public Boolean allowWatermarkCustomizationForCameraVideo;
    public Boolean allowWatermarkCustomizationForScreenSharing;
    public Boolean allowWatermarkForCameraVideo;
    public Boolean allowWatermarkForScreenSharing;
    public Boolean allowWhiteboard;
    public String allowedStreamingMediaInput;
//    public String allowedUsersForMeetingContext;
    public String allowedUsersForMeetingDetails;
    public String anonymousUserAuthenticationMethod;
    public String attendeeIdentityMasking;
    public String audibleRecordingNotification;
    public String autoAdmittedUsers;
    public String autoRecording;
    public String automaticallyStartCopilot;
    public String blockedAnonymousJoinClientTypes;
    public String captchaVerificationForMeetingJoin;
    public String channelRecordingDownload;
//    public ConfigMetadata configMetadata;
    public String connectToMeetingControls;
    public String contentSharingInExternalMeetings;
    public String copilot;
    public Boolean copyRestriction;
//    public String dataSource;
    public String description;
    public String designatedPresenterRoleMode;
    public Boolean detectSensitiveContentDuringScreenSharing;
    public String enrollUserOverride;
    public String explicitRecordingConsent;
    public String externalMeetingJoin;
//    public String forceStreamingAttendeeMode;
    public String IPAudioMode;
    public String IPVideoMode;
    public String identity;
    public String infoShownInReportMode;
//    public Key key;
    public String liveCaptionsEnabledType;
    public String liveInterpretationEnabledType;
    public String liveStreamingMode;
    public String lobbyChat;
    public Integer mediaBitRateKb;
    public String meetingChatEnabledType;
    public String meetingInviteLanguages;
    public Integer newMeetingRecordingExpirationDays;
    public String noiseSuppressionForDialInParticipants;
    public String participantNameChange;
    public String preferredMeetingProviderForIslandsMode;
    public String QnAEngagementMode;
    public String realTimeText;
    public String recordingStorageMode;
    public String roomAttributeUserOverride;
    public String roomPeopleNameUserOverride;
    public String screenSharingMode;
    public String smsNotifications;
    public String speakerAttributionMode;
    public String streamingAttendeeMode;
    public String teamsCameraFarEndPTZMode;
    public String usersCanAdmitFromLobby;
    public String videoFiltersMode;
    public String voiceIsolation;
    public String voiceSimulationInInterpreter;
    public String participantSlideControl;
    public String watermarkForAnonymousUsers;
    public Integer watermarkForCameraVideoOpacity;
    public String watermarkForCameraVideoPattern;
    public Integer watermarkForScreenSharingOpacity;
    public String watermarkForScreenSharingPattern;
    public String whoCanRegister;



//    public static class ConfigMetadata {
//        public String authority;
//
//        public ConfigMetadata() {};
//        public ConfigMetadata(MeetingPolicy.ConfigMetadata configMetadata) {
//            if (configMetadata == null) return;
//            this.authority = configMetadata.authority;
//        };
//    }

//    public static class Key {
//        public AuthorityId authorityId;
//        public DefaultXml defaultXml;
//        public SchemaId schemaId;
//        public String scopeClass;
//        public XmlRoot xmlRoot;
//
//        public Key(){} //for serialization from json
//        public Key(MeetingPolicy.Key key) {
//            if (key == null) return;
//
//            this.authorityId = key.authorityId == null ? null : new AuthorityId(key.authorityId);
//            this.defaultXml = key.defaultXml == null ? null : new DefaultXml(key.defaultXml);
//            this.schemaId = key.schemaId == null ? null : new SchemaId(key.schemaId);
//            this.scopeClass = key.scopeClass;
//            this.xmlRoot = key.xmlRoot == null ? null : new XmlRoot(key.xmlRoot);
//        }
//    }
//
//    public static class AuthorityId {
//        @JsonProperty(value = "Class")
//        public String classType;
//        public String instanceId;
//        public XmlRoot xmlRoot;
//
//        public AuthorityId() {} //for serialization from json
//        public AuthorityId(MeetingPolicy.AuthorityId authorityId) {
//            if (authorityId == null) return;
//            this.classType = authorityId.classType;
//            this.instanceId = authorityId.instanceId;
//            this.xmlRoot = authorityId.xmlRoot == null ? null : new XmlRoot(authorityId.xmlRoot);
//        }
//    }
//
//    public static class DefaultXml {
//        public Object configObject;
//        public Data data;
//        public Boolean isModified;
//        public SchemaId schemaId;
//        public String signature;
//
//        public DefaultXml() { } //for serialization from json
//        public DefaultXml(MeetingPolicy.DefaultXml defaultXml) {
//            if (defaultXml == null) return;
//            this.configObject = defaultXml.configObject;
//            this.data = defaultXml.data == null ? null : new Data(defaultXml.data);
//            this.isModified = defaultXml.isModified;
//            this.schemaId = defaultXml.schemaId == null ? null : new SchemaId(defaultXml.schemaId);
//            this.signature = defaultXml.signature;
//        }
//    }

//    public static class Data {
//        public TeamsMeetingPolicyData teamsMeetingPolicy;
//
//        public Data() {}//for serialization from json
//        public Data(MeetingPolicy.Data data) {
//            if (data == null) return;
//            this.teamsMeetingPolicy = data.teamsMeetingPolicy == null ? null : new TeamsMeetingPolicyData(data.teamsMeetingPolicy);
//        }
//    }
//
//    public static class TeamsMeetingPolicyData {
//        @JsonProperty(value = "@xmlns")
//        public String xmlns;
//
//        public TeamsMeetingPolicyData() {}//for serialization from json
//        public TeamsMeetingPolicyData(MeetingPolicy.TeamsMeetingPolicyData teamsMeetingPolicy) {
//            if (teamsMeetingPolicy == null) return;
//            this.xmlns = teamsMeetingPolicy.xmlns;
//        }
//    }
//
//    public static class SchemaId {
//        public XName xName;
//
//        public SchemaId() {} //for serialization from json
//        public SchemaId(MeetingPolicy.SchemaId schemaId) {
//            if (schemaId == null) return;
//            this.xName = schemaId.xName == null ? null : new XName(schemaId.xName);
//        }
//    }
//
//    public static class XName {
//        public String name;
//
//        public XName() {}//for serialization from json
//        public XName(MeetingPolicy.XName xName) {
//            if (xName == null) return;
//            this.name = xName.name;
//        }
//    }
//
//    public static class XmlRoot {
//        public String name;
//
//        public XmlRoot() {} //for serialization from json
//        public XmlRoot(MeetingPolicy.XmlRoot xmlRoot) {
//            if (xmlRoot == null) return;
//            this.name = xmlRoot.name;
//        }
//    }

    public TeamsMeetingPolicy() {} //for serialization from json
    public TeamsMeetingPolicy(MeetingPolicy meetingPolicy) {
        this.AIInterpreter = meetingPolicy.aiInterpreter;
        this.allowAnnotations = meetingPolicy.allowAnnotations;
        this.allowAnonymousUsersToDialOut = meetingPolicy.allowAnonymousUsersToDialOut;
        this.allowAnonymousUsersToJoinMeeting = meetingPolicy.allowAnonymousUsersToJoinMeeting;
        this.allowAnonymousUsersToStartMeeting = meetingPolicy.allowAnonymousUsersToStartMeeting;
        this.allowAvatarsInGallery = meetingPolicy.allowAvatarsInGallery;
        this.allowBreakoutRooms = meetingPolicy.allowBreakoutRooms;
        this.allowCarbonSummary = meetingPolicy.allowCarbonSummary;
        this.allowCartCaptionsScheduling = meetingPolicy.allowCartCaptionsScheduling;
        this.allowChannelMeetingScheduling = meetingPolicy.allowChannelMeetingScheduling;
        this.allowCloudRecording = meetingPolicy.allowCloudRecording;
        this.allowDocumentCollaboration = meetingPolicy.allowDocumentCollaboration;
        this.allowEngagementReport = meetingPolicy.allowEngagementReport;
        this.allowExternalNonTrustedMeetingChat = meetingPolicy.allowExternalNonTrustedMeetingChat;
        this.allowExternalParticipantGiveRequestControl = meetingPolicy.allowExternalParticipantGiveRequestControl;
        this.allowIPAudio = meetingPolicy.allowIPAudio;
        this.allowIPVideo = meetingPolicy.allowIPVideo;
        this.allowImmersiveView = meetingPolicy.allowImmersiveView;
        this.allowLocalRecording = meetingPolicy.allowLocalRecording;
        this.allowMeetNow = meetingPolicy.allowMeetNow;
        this.allowMeetingCoach = meetingPolicy.allowMeetingCoach;
        this.allowMeetingReactions = meetingPolicy.allowMeetingReactions;
        this.allowMeetingRegistration = meetingPolicy.allowMeetingRegistration;
        this.allowNDIStreaming = meetingPolicy.allowNDIStreaming;
        this.allowNetworkConfigurationSettingsLookup = meetingPolicy.allowNetworkConfigurationSettingsLookup;
        this.allowOrganizersToOverrideLobbySettings = meetingPolicy.allowOrganizersToOverrideLobbySettings;
        this.allowOutlookAddIn = meetingPolicy.allowOutlookAddIn;
        this.allowPSTNUsersToBypassLobby = meetingPolicy.allowPSTNUsersToBypassLobby;
        this.allowParticipantGiveRequestControl = meetingPolicy.allowParticipantGiveRequestControl;
        this.allowPowerPointSharing = meetingPolicy.allowPowerPointSharing;
        this.allowPrivateMeetNow = meetingPolicy.allowPrivateMeetNow;
        this.allowPrivateMeetingScheduling = meetingPolicy.allowPrivateMeetingScheduling;
        this.allowRecordingStorageOutsideRegion = meetingPolicy.allowRecordingStorageOutsideRegion;
        this.allowScreenContentDigitization = meetingPolicy.allowScreenContentDigitization;
        this.allowSharedNotes = meetingPolicy.allowSharedNotes;
        this.allowTasksFromTranscript = meetingPolicy.allowTasksFromTranscript;
        this.allowTrackingInReport = meetingPolicy.allowTrackingInReport;
        this.allowTranscription = meetingPolicy.allowTranscription;
        this.allowWatermarkCustomizationForCameraVideo = meetingPolicy.allowWatermarkCustomizationForCameraVideo;
        this.allowWatermarkCustomizationForScreenSharing = meetingPolicy.allowWatermarkCustomizationForScreenSharing;
        this.allowWatermarkForCameraVideo = meetingPolicy.allowWatermarkForCameraVideo;
        this.allowWatermarkForScreenSharing = meetingPolicy.allowWatermarkForScreenSharing;
        this.allowWhiteboard = meetingPolicy.allowWhiteboard;
        this.allowedStreamingMediaInput = meetingPolicy.allowedStreamingMediaInput;
//        this.allowedUsersForMeetingContext = meetingPolicy.allowedUsersForMeetingContext;
        this.allowedUsersForMeetingDetails = meetingPolicy.allowedUsersForMeetingDetails;
        this.anonymousUserAuthenticationMethod = meetingPolicy.anonymousUserAuthenticationMethod;
        this.attendeeIdentityMasking = meetingPolicy.attendeeIdentityMasking;
        this.audibleRecordingNotification = meetingPolicy.audibleRecordingNotification;
        this.autoAdmittedUsers = meetingPolicy.autoAdmittedUsers == null ? null : meetingPolicy.autoAdmittedUsers.asString();
        this.autoRecording = meetingPolicy.autoRecording;
        this.automaticallyStartCopilot = meetingPolicy.automaticallyStartCopilot;
        this.blockedAnonymousJoinClientTypes = meetingPolicy.blockedAnonymousJoinClientTypes;
        this.captchaVerificationForMeetingJoin = meetingPolicy.captchaVerificationForMeetingJoin;
        this.channelRecordingDownload = meetingPolicy.channelRecordingDownload;
//        this.configId = meetingPolicy.configId;
//        this.configMetadata = meetingPolicy.configMetadata == null ? null : new ConfigMetadata(meetingPolicy.configMetadata);
        this.connectToMeetingControls = meetingPolicy.connectToMeetingControls;
        this.contentSharingInExternalMeetings = meetingPolicy.contentSharingInExternalMeetings;
        this.copilot = meetingPolicy.copilot;
        this.copyRestriction = meetingPolicy.copyRestriction;
//        this.dataSource = meetingPolicy.dataSource;
        this.description = meetingPolicy.description;
        this.designatedPresenterRoleMode = meetingPolicy.designatedPresenterRoleMode;
        this.detectSensitiveContentDuringScreenSharing = meetingPolicy.detectSensitiveContentDuringScreenSharing;
        this.enrollUserOverride = meetingPolicy.enrollUserOverride;
        this.explicitRecordingConsent = meetingPolicy.explicitRecordingConsent;
        this.externalMeetingJoin = meetingPolicy.externalMeetingJoin;
//        this.forceStreamingAttendeeMode = meetingPolicy.forceStreamingAttendeeMode;
        this.IPAudioMode = meetingPolicy.ipAudioMode;
        this.IPVideoMode = meetingPolicy.ipVideoMode;
        this.identity = meetingPolicy.identity;
        this.infoShownInReportMode = meetingPolicy.infoShownInReportMode;
//        this.key = meetingPolicy.key == null ? null : new Key(meetingPolicy.key);
        this.liveCaptionsEnabledType = meetingPolicy.liveCaptionsEnabledType;
        this.liveInterpretationEnabledType = meetingPolicy.liveInterpretationEnabledType;
        this.liveStreamingMode = meetingPolicy.liveStreamingMode;
        this.lobbyChat = meetingPolicy.lobbyChat;
        this.mediaBitRateKb = meetingPolicy.mediaBitRateKb;
        this.meetingChatEnabledType = meetingPolicy.meetingChatEnabledType;
        this.meetingInviteLanguages = meetingPolicy.meetingInviteLanguages;
        this.newMeetingRecordingExpirationDays = meetingPolicy.newMeetingRecordingExpirationDays;
        this.noiseSuppressionForDialInParticipants = meetingPolicy.noiseSuppressionForDialInParticipants;
        this.participantNameChange = meetingPolicy.participantNameChange;
        this.preferredMeetingProviderForIslandsMode = meetingPolicy.preferredMeetingProviderForIslandsMode;
        this.QnAEngagementMode = meetingPolicy.qnaEngagementMode;
        this.realTimeText = meetingPolicy.realTimeText;
        this.recordingStorageMode = meetingPolicy.recordingStorageMode;
        this.roomAttributeUserOverride = meetingPolicy.roomAttributeUserOverride;
        this.roomPeopleNameUserOverride = meetingPolicy.roomPeopleNameUserOverride;
        this.screenSharingMode = meetingPolicy.screenSharingMode;
        this.smsNotifications = meetingPolicy.smsNotifications;
        this.speakerAttributionMode = meetingPolicy.speakerAttributionMode;
        this.streamingAttendeeMode = meetingPolicy.streamingAttendeeMode;
        this.teamsCameraFarEndPTZMode = meetingPolicy.teamsCameraFarEndPTZMode;
        this.usersCanAdmitFromLobby = meetingPolicy.usersCanAdmitFromLobby;
        this.videoFiltersMode = meetingPolicy.videoFiltersMode;
        this.voiceIsolation = meetingPolicy.voiceIsolation;
        this.voiceSimulationInInterpreter = meetingPolicy.voiceSimulationInInterpreter;
        this.participantSlideControl = meetingPolicy.participantSlideControl;
        this.watermarkForAnonymousUsers = meetingPolicy.watermarkForAnonymousUsers;
        this.watermarkForCameraVideoOpacity = meetingPolicy.watermarkForCameraVideoOpacity;
        this.watermarkForCameraVideoPattern = meetingPolicy.watermarkForCameraVideoPattern;
        this.watermarkForScreenSharingOpacity = meetingPolicy.watermarkForScreenSharingOpacity;
        this.watermarkForScreenSharingPattern = meetingPolicy.watermarkForScreenSharingPattern;
        this.whoCanRegister = meetingPolicy.whoCanRegister;
    }

}



