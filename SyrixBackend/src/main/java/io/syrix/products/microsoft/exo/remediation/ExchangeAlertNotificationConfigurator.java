package io.syrix.products.microsoft.exo.remediation;

import io.syrix.products.microsoft.base.AlertNotificationConfiguratorBase;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.protocols.client.PowerShellClient;

import java.util.List;

/**
 * Remediator for MS.EXO.16.2v1 which configures notification recipients for critical security alerts
 * in Microsoft Exchange Online.
 * <p>
 * This class configures notification recipients for the following required alerts:
 * a. Suspicious email sending patterns detected
 * b. Suspicious Connector Activity
 * c. Suspicious Email Forwarding Activity
 * d. Messages have been delayed
 * e. Tenant restricted from sending unprovisioned email
 * f. Tenant restricted from sending email
 * g. A potentially malicious URL click was detected

 * The alerts SHOULD be sent to a monitored address or incorporated into a Security
 * Information and Event Management (SIEM).

 * Rationale: Suspicious or malicious events, if not resolved promptly, may have a greater
 * impact to users and the agency. Sending alerts to a monitored email address or SIEM system
 * helps ensure events are acted upon in a timely manner to limit overall impact.

 * MITRE ATT&CK TTP Mapping:
 * - T1562: Impair Defenses
 *   - T1562.006: Indicator Blocking
 */
@PolicyRemediator("MS.EXO.16.2v1")
public class ExchangeAlertNotificationConfigurator extends AlertNotificationConfiguratorBase {

	/**
	 * Constructs a new ExchangeAlertNotificationConfigurator with the specified PowerShell client
	 * and notification recipient.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 * @param notificationRecipient The email address to notify for alerts
	 * @param tenantId The tenant ID to use for creating the hash suffix
	 */
	public ExchangeAlertNotificationConfigurator(PowerShellClient exchangeClient,
												 String notificationRecipient,
												 String tenantId,
												 List<String> additionalAlerts) {
		super(exchangeClient, List.of(notificationRecipient), tenantId, additionalAlerts);
	}

	/**
	 * Constructor that accepts a list of notification recipients.
	 *
	 * @param exchangeClient The Exchange Online PowerShell client
	 * @param notificationRecipients List of email addresses to notify for alerts
	 * @param tenantId The tenant ID to use for creating the hash suffix
	 */
	public ExchangeAlertNotificationConfigurator(PowerShellClient exchangeClient,
												 List<String> notificationRecipients,
												 String tenantId,
												 List<String> additionalAlerts) {
		super(exchangeClient, notificationRecipients, tenantId, additionalAlerts);
	}
}