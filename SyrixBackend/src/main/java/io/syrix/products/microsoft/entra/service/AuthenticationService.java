package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static io.syrix.common.constants.Constants.ID_FIELD;

class AuthenticationService extends BaseConfigurationService {
	private static final String CONFIGURATIONS_FIELD = "authenticationMethodConfigurations";
	private static final String FEATURE_SETTINGS_FIELD = "authentication_method_feature_settings";
	private static final String POLICY_FIELD = "authentication_method_policy";

	private final MicrosoftGraphClient graphClient;

	AuthenticationService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
	}

	private CompletableFuture<JsonNode> getAuthenticationMethods() {
		return withRetry(this::fetchAuthenticationPolicy, "Get-MgBetaPolicyAuthenticationMethodPolicy")
				.thenApply(this::processAuthenticationPolicyResponse);
	}

	private CompletableFuture<JsonNode> getAuthorizationPolicies() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/policies/authorizationPolicy")
						.withMethod(HttpMethod.GET)
						.build()), "authorizationPolicy");
	}

	/**
	 * Fetches authentication policy from Microsoft Graph API.
	 */
	private CompletableFuture<JsonNode> fetchAuthenticationPolicy() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
				.beta()
				.withEndpoint("/policies/authenticationMethodsPolicy")
				.withMethod(HttpMethod.GET)
				.build());
	}

	/**
	 * Extracts authentication method feature settings from the root object.
	 * Filters configurations to only include those with valid IDs.
	 */
	private ArrayNode extractFeatureSettings(JsonNode rootObject) {
		ArrayNode featureSettings = objectMapper.createArrayNode();
		JsonNode configurations = rootObject.path(CONFIGURATIONS_FIELD);

		if (configurations.isArray()) {
			StreamSupport.stream(configurations.spliterator(), false)
					.filter(config -> config.hasNonNull(ID_FIELD))
					.forEach(featureSettings::add);
		}

		return featureSettings;
	}

	/**
	 * Processes the authentication policy response and builds the result structure.
	 */
	private JsonNode processAuthenticationPolicyResponse(JsonNode authenticationMethodPolicyRootObject) {
		ObjectNode result = objectMapper.createObjectNode();

		// Extract and add feature settings
		ArrayNode featureSettings = extractFeatureSettings(authenticationMethodPolicyRootObject);
		result.set(FEATURE_SETTINGS_FIELD, featureSettings);

		// Extract and add policy details
		ObjectNode policyDetails = extractPolicyDetails(authenticationMethodPolicyRootObject);
		result.set(POLICY_FIELD, policyDetails);

		return result;
	}

	/**
	 * Extracts authentication policy details excluding configurations.
	 * Creates a new object with all fields except authenticationMethodConfigurations.
	 */
	private ObjectNode extractPolicyDetails(JsonNode rootObject) {
		ObjectNode policyDetails = objectMapper.createObjectNode();

		rootObject.fields()
				.forEachRemaining(field -> {
					if (!field.getKey().equals(CONFIGURATIONS_FIELD)) {
						policyDetails.set(field.getKey(), field.getValue());
					}
				});

		return policyDetails;
	}

	@Override
	public ConfigurationResult exportConfiguration() {
		logger.info("Exporting authentication methods configuration");

		try {
			// Get the authentication methods and authorization policies
			CompletableFuture<JsonNode> authMethods = getAuthenticationMethods();
			CompletableFuture<JsonNode> authPolicies = getAuthorizationPolicies();

			// Build the result with policies and methods
			Map<String, CompletableFuture<?>> resultData = new HashMap<>();
			resultData.put("authentication_methods", authMethods);
			resultData.put("authorization_policies", authPolicies);


			// Wait for all futures to complete and then build the result
			return waitForFutures(resultData)
					.thenApply(map -> {
						// Add successful and unsuccessful commands
						map.put("successful_commands", getSuccessfulCommands());
						map.put("unsuccessful_commands", getUnsuccessfulCommands());

						// Build and return the result
						return ConfigurationResult.builder()
								.withData(objectMapper.valueToTree(map))
								.withTimestamp(Instant.now())
								.withMetadata(buildMetadata(SERVICE_VERSION))
								.build();
					})
					.join();  // Wait for the final result

		} catch (Exception e) {
			logger.error("Failed to export authentication methods configuration", e);
			throw new ConfigurationExportException("Failed to export authentication methods", e);
		}
	}
}