package io.syrix.products.microsoft.policy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.common.rego.RegoEvaluator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Service for comparing tenant configurations against Rego baseline rules.
 * Provides functionality for evaluating compliance and generating reports.
 */
public class PolicyComparisonService implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(PolicyComparisonService.class);

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final MetricsCollector metrics;
	private final ExecutorService executor;

	private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);

	public PolicyComparisonService(MicrosoftGraphClient graphClient) {
		this(graphClient, new ObjectMapper(), new MetricsCollector());
	}

	public PolicyComparisonService(
			MicrosoftGraphClient graphClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {

		this.graphClient = Objects.requireNonNull(graphClient, "Graph client cannot be null");
		this.objectMapper = Objects.requireNonNull(objectMapper, "Object mapper cannot be null");
		this.metrics = Objects.requireNonNull(metrics, "Metrics collector cannot be null");
		this.executor = Executors.newVirtualThreadPerTaskExecutor();
	}

	/**
	 * Compare tenant configurations against Rego baseline rules and generate report.
	 *
	 * @param regoFiles Map of service name to Rego file paths to evaluate
	 * @return Comparison results for all evaluated services
	 */
	public ComparisonResult compareConfigurations(Map<String, String> regoFiles) {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting policy comparison at {}", startTime);

		try {
			// Create futures for comparing each service's configuration
			Map<String, CompletableFuture<ServiceComparisonResult>> futures = new HashMap<>();

			for (Map.Entry<String, String> entry : regoFiles.entrySet()) {
				String serviceName = entry.getKey();
				String regoPath = entry.getValue();
				futures.put(serviceName, compareServiceConfig(serviceName, regoPath));
			}

			// Wait for all futures to complete with timeout
			ComparisonResult result = waitForFutures(futures)
					.thenApply(this::buildComparisonResult)
					.get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordExportSuccess(duration);
			logger.info("Policy comparison completed in {} seconds", duration.toSeconds());

			return result;

		} catch (Exception e) {
			metrics.recordExportFailure();
			logger.error("Policy comparison failed", e);
			throw new ConfigurationExportException("Policy comparison failed", e);
		}
	}

	/**
	 * Compare configuration for a specific service against its Rego rules.
	 */
	private CompletableFuture<ServiceComparisonResult> compareServiceConfig(
			String serviceName, String regoPath) {

		return CompletableFuture.supplyAsync(() -> {
			try {
				// Load and parse Rego rules
				String regoContent = Files.readString(new File(regoPath).toPath());

				// Get service configuration from tenant
				JsonNode config = getServiceConfiguration(serviceName);

				// Evaluate rules against configuration
				List<RuleEvaluation> evaluations = evaluateRules(regoContent, config);

				return new ServiceComparisonResult(serviceName, evaluations);

			} catch (Exception e) {
				logger.error("Failed to compare {} configuration: {}",
						serviceName, e.getMessage());
				throw new CompletionException(e);
			}
		}, executor);
	}

	/**
	 * Gets configuration for a specific service from tenant.
	 * Retrieves and formats configuration data according to service's Rego rule expectations.
	 */
	private JsonNode getServiceConfiguration(String serviceName) {
		Instant startTime = Instant.now();
		try {
			// Get service-specific configuration using appropriate Graph API calls
			JsonNode config = switch(serviceName.toLowerCase()) {
				case "defender" -> getDefenderConfiguration();
				case "sharepoint" -> getSharePointConfiguration();
				case "teams" -> getTeamsConfiguration();
				case "exo" -> getExchangeConfiguration();
				case "aad" -> getAADConfiguration();
				default -> throw new IllegalArgumentException("Unsupported service: " + serviceName);
			};

			// Track success metrics
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordApiCall("get_" + serviceName + "_config", duration, true);

			return config;

		} catch (Exception e) {
			// Track failure metrics
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordApiCall("get_" + serviceName + "_config", duration, false);

			logger.error("Failed to get {} configuration: {}", serviceName, e.getMessage());
			throw new ConfigurationExportException(
					"Failed to get " + serviceName + " configuration", e);
		}
	}

	/**
	 * Gets Defender configuration including anti-phishing, ATP, and other security settings.
	 */
	private JsonNode getDefenderConfiguration() {
		ObjectNode config = objectMapper.createObjectNode();

		// Get anti-phishing policies
		CompletableFuture<JsonNode> antiPhishPolicies = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/security/threatProtection/targetedProtectionPolicies")
				.withMethod(HttpMethod.GET)
				.build());

		// Get ATP policies
		CompletableFuture<JsonNode> atpPolicies = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/security/threatProtection/advancedThreatProtectionPolicies")
				.withMethod(HttpMethod.GET)
				.build());

		// Get protection alerts
		CompletableFuture<JsonNode> alerts = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/security/alerts")
				.withMethod(HttpMethod.GET)
				.addQueryParam("$filter", "category eq 'ThreatProtection'")
				.build());

		// Wait for all requests
		try {
			config.set("anti_phish_policies", antiPhishPolicies.get());
			config.set("atp_policy_rules", atpPolicies.get());
			config.set("protection_alerts", alerts.get());
			return config;
		} catch (Exception e) {
			throw new CompletionException(e);
		}
	}

	/**
	 * Gets SharePoint/OneDrive configuration including sharing and security settings.
	 */
	private JsonNode getSharePointConfiguration() {
		ObjectNode config = objectMapper.createObjectNode();

		// Get tenant settings
		CompletableFuture<JsonNode> tenantSettings = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/sharepoint/settings")
				.withMethod(HttpMethod.GET)
				.build());

		// Get sharing settings
		CompletableFuture<JsonNode> sharingSettings = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/sharepoint/settings/sharing")
				.withMethod(HttpMethod.GET)
				.build());

		// Get OneDrive settings
		CompletableFuture<JsonNode> oneDriveSettings = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/onedrive/settings")
				.withMethod(HttpMethod.GET)
				.build());

		try {
			config.set("SPO_tenant", tenantSettings.get());
			config.set("sharing_settings", sharingSettings.get());
			config.set("onedrive_settings", oneDriveSettings.get());
			return config;
		} catch (Exception e) {
			throw new CompletionException(e);
		}
	}

	/**
	 * Gets Teams configuration including meeting policies and security settings.
	 */
	private JsonNode getTeamsConfiguration() {
		ObjectNode config = objectMapper.createObjectNode();

		// Get tenant settings
		CompletableFuture<JsonNode> tenantInfo = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/teams/tenantsettings")
				.withMethod(HttpMethod.GET)
				.build());

		// Get meeting policies
		CompletableFuture<JsonNode> meetingPolicies = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/policies/teamsAppSetupPolicies")
				.withMethod(HttpMethod.GET)
				.build());

		// Get federation configuration
		CompletableFuture<JsonNode> fedConfig = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/policies/teamsFederationConfiguration")
				.withMethod(HttpMethod.GET)
				.build());

		try {
			config.set("teams_tenant_info", tenantInfo.get());
			config.set("meeting_policies", meetingPolicies.get());
			config.set("federation_configuration", fedConfig.get());
			return config;
		} catch (Exception e) {
			throw new CompletionException(e);
		}
	}

	/**
	 * Gets Exchange Online configuration including transport rules and security settings.
	 */
	private JsonNode getExchangeConfiguration() {
		ObjectNode config = objectMapper.createObjectNode();

		// Get transport config
		CompletableFuture<JsonNode> transportConfig = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/exchange/transportConfig")
				.withMethod(HttpMethod.GET)
				.build());

		// Get sharing policies
		CompletableFuture<JsonNode> sharingPolicy = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/exchange/sharingPolicy")
				.withMethod(HttpMethod.GET)
				.build());

		// Get connection filter policies
		CompletableFuture<JsonNode> connFilter = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/admin/exchange/connectionFilterPolicy")
				.withMethod(HttpMethod.GET)
				.build());

		try {
			config.set("transport_config", transportConfig.get());
			config.set("sharing_policy", sharingPolicy.get());
			config.set("conn_filter", connFilter.get());
			return config;
		} catch (Exception e) {
			throw new CompletionException(e);
		}
	}

	/**
	 * Gets Azure AD configuration including conditional access and identity settings.
	 */
	private JsonNode getAADConfiguration() {
		ObjectNode config = objectMapper.createObjectNode();

		// Get conditional access policies
		CompletableFuture<JsonNode> caPolicies = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/identity/conditionalAccess/policies")
				.withMethod(HttpMethod.GET)
				.build());

		// Get authentication methods configuration
		CompletableFuture<JsonNode> authMethods = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/policies/authenticationMethodsPolicy")
				.withMethod(HttpMethod.GET)
				.build());

		// Get directory settings
		CompletableFuture<JsonNode> dirSettings = graphClient.makeGraphRequest(GraphRequest.builder()
				.withEndpoint("/settings")
				.withMethod(HttpMethod.GET)
				.build());

		try {
			config.set("conditional_access_policies", caPolicies.get());
			config.set("authentication_method", authMethods.get());
			config.set("directory_settings", dirSettings.get());
			return config;
		} catch (Exception e) {
			throw new CompletionException(e);
		}
	}

	/**
	 * Evaluate Rego rules against configuration using the OPA evaluator.
	 * Handles evaluation failures and tracks metrics.
	 */
	private List<RuleEvaluation> evaluateRules(String regoRules, JsonNode config) {
		Instant startTime = Instant.now();
		List<RuleEvaluation> evaluations = new ArrayList<>();

		try {
			// First validate the Rego rules format
			RegoEvaluator evaluator = new RegoEvaluator(getOPAPath());
			if (!evaluator.validateRules(regoRules)) {
				logger.error("Invalid Rego rules format");
				throw new IllegalArgumentException("Invalid Rego rules format");
			}

			// Evaluate rules against config
			evaluations = evaluator.evaluateRules(regoRules, config);

			// Track evaluation metrics
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordApiCall("rego_evaluation", duration, true);

			// Log evaluation summary
			int compliantCount = 0;
			int nonCompliantCount = 0;
			for (RuleEvaluation eval : evaluations) {
				if (eval.isCompliant()) {
					compliantCount++;
				} else {
					nonCompliantCount++;
					logger.warn("Policy {} not compliant: {}",
							eval.getRuleId(), eval.getDetails());
				}
			}

			logger.info("Completed policy evaluation: {} compliant, {} non-compliant rules",
					compliantCount, nonCompliantCount);

		} catch (Exception e) {
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordApiCall("rego_evaluation", duration, false);

			logger.error("Failed to evaluate Rego rules: {}", e.getMessage());

			// Add error evaluation result
			evaluations.add(new RuleEvaluation(
					"EVALUATION_ERROR",
					false,
					"Failed to evaluate rules: " + e.getMessage()
			));

			// Don't throw - we want to return error results rather than fail completely
		}

		return evaluations;
	}

	/**
	 * Gets the OPA executable path, either from config or default location.
	 */
	private String getOPAPath() {
		// First check environment
		String envPath = System.getenv("OPA_PATH");
		if (envPath != null && !envPath.isEmpty()) {
			return envPath;
		}

		// Then look in default locations
		List<String> defaultPaths = Arrays.asList(
				"./.scubagear/Tools/opa",           // Linux/Mac
				"./.scubagear/Tools/opa_windows_amd64.exe", // Windows
				"opa",                              // System PATH
				"opa_windows_amd64.exe"             // System PATH Windows
		);

		for (String path : defaultPaths) {
			try {
				File opaFile = new File(path);
				if (opaFile.exists() && opaFile.canExecute()) {
					return opaFile.getAbsolutePath();
				}
			} catch (Exception e) {
				logger.debug("Could not find OPA at {}: {}", path, e.getMessage());
			}
		}

		throw new IllegalStateException(
				"Could not find OPA executable. Please set OPA_PATH environment variable."
		);
	}

	/**
	 * Wait for all service comparison futures to complete.
	 */
	private CompletableFuture<Map<String, ServiceComparisonResult>> waitForFutures(
			Map<String, CompletableFuture<ServiceComparisonResult>> futures) {

		return CompletableFuture.allOf(
						futures.values().toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					Map<String, ServiceComparisonResult> results = new HashMap<>();
					futures.forEach((key, future) ->
							results.put(key, future.join()));
					return results;
				});
	}

	/**
	 * Build final comparison result from all service results.
	 */
	private ComparisonResult buildComparisonResult(
			Map<String, ServiceComparisonResult> results) {

		ComparisonResult.Builder builder = ComparisonResult.builder()
				.withTimestamp(Instant.now())
				.withResults(results);

		// Add metadata
		ObjectNode metadata = objectMapper.createObjectNode();
		metadata.put("version", "1.0");
		metadata.put("generated_at", Instant.now().toString());
		metadata.put("environment", graphClient.getEnvironment().toString());
		builder.withMetadata(metadata);

		return builder.build();
	}

	@Override
	public void close() {
		try {
			executor.shutdown();
			if (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
				executor.shutdownNow();
			}
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			executor.shutdownNow();
		}
	}

	/**
	 * Model classes for representing comparison results
	 */

	public static class ComparisonResult {
		private final Map<String, ServiceComparisonResult> results;
		private final JsonNode metadata;
		private final Instant timestamp;

		private ComparisonResult(Builder builder) {
			this.results = builder.results;
			this.metadata = builder.metadata;
			this.timestamp = builder.timestamp;
		}

		public Map<String, ServiceComparisonResult> getResults() {
			return results;
		}

		public JsonNode getMetadata() {
			return metadata;
		}

		public Instant getTimestamp() {
			return timestamp;
		}

		public static Builder builder() {
			return new Builder();
		}

		public static class Builder {
			private Map<String, ServiceComparisonResult> results;
			private JsonNode metadata;
			private Instant timestamp;

			private Builder() {}

			public Builder withResults(Map<String, ServiceComparisonResult> results) {
				this.results = results;
				return this;
			}

			public Builder withMetadata(JsonNode metadata) {
				this.metadata = metadata;
				return this;
			}

			public Builder withTimestamp(Instant timestamp) {
				this.timestamp = timestamp;
				return this;
			}

			public ComparisonResult build() {
				return new ComparisonResult(this);
			}
		}
	}

	public static class ServiceComparisonResult {
		private final String serviceName;
		private final List<RuleEvaluation> evaluations;

		public ServiceComparisonResult(String serviceName, List<RuleEvaluation> evaluations) {
			this.serviceName = serviceName;
			this.evaluations = evaluations;
		}

		public String getServiceName() {
			return serviceName;
		}

		public List<RuleEvaluation> getEvaluations() {
			return evaluations;
		}
	}

	public static class RuleEvaluation {
		private final String ruleId;
		private final boolean compliant;
		private final String details;

		public RuleEvaluation(String ruleId, boolean compliant, String details) {
			this.ruleId = ruleId;
			this.compliant = compliant;
			this.details = details;
		}

		public String getRuleId() {
			return ruleId;
		}

		public boolean isCompliant() {
			return compliant;
		}

		public String getDetails() {
			return details;
		}
	}
}