package io.syrix.products.microsoft.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.ConfigurationService;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.protocols.client.MicrosoftGraphClient;

/**
 * Base class for Microsoft 365 configuration services.
 * Provides common functionality for configuration export and management.
 */
public abstract class BaseConfigurationService extends BaseService implements ConfigurationService {

	protected BaseConfigurationService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
	}
}