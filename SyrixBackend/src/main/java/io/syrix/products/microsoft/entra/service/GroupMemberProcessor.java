package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.products.microsoft.entra.model.PrivilegedUser;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.PaginatedResult;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.Common.createEmptyValueNode;
import static io.syrix.protocols.utils.GraphClientConstants.PARAM_FILTER;

/**
 * Service class to handle group member processing.
 */
class GroupMemberProcessor extends BaseConfigurationService {
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private static final String VALUE_KEY = VALUE_FIELD;
	private static final String DATA_TYPE = "dataType";

	GroupMemberProcessor(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
		this.objectMapper = objectMapper;
	}

	/**
	 * Processes a group, retrieving all members and handling nested groups.
	 */
	CompletableFuture<List<PrivilegedUser>> processGroup(String groupId,
														 String roleName,
														 boolean hasAadPremiumP2) {
		return CompletableFuture.allOf(
						getGroupMembers(groupId),
						getEligibilityData(groupId, hasAadPremiumP2))
				.thenCompose(v -> processAllMembers(
						groupId, roleName, hasAadPremiumP2));
	}

	/**
	 * Retrieves members of a group.
	 */
	private CompletableFuture<JsonNode> getGroupMembers(String groupId) {
		return withRetry(() ->
						getPaginatedResults("/groups/" + groupId + "/members", DEFAULT_BATCH_SIZE, "1.0"),
				"Get-MgBetaGroupMembers"
		).exceptionally(e -> {
			logger.error("Failed to call getGroupMembers {}", e.getMessage());
			return objectMapper.createArrayNode();
		});
	}

	/**
	 * Retrieves PIM eligibility for a group.
	 */
	private CompletableFuture<JsonNode> getPIMGroupEligibility(String groupId) {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().v1()
						.withEndpoint("/identityGovernance/privilegedAccess/group/eligibilityScheduleInstances")
						.withMethod(HttpMethod.GET)
						.addQueryParam(PARAM_FILTER, "groupId eq '" + groupId + "'")
						.build()), "Get-MgBetaIdentityGovernancePrivilegedAccessGroupEligibilityScheduleInstance"
		).exceptionally(e -> {
			logger.error("Failed to call getPIMGroupEligibility {}", e.getMessage());
			return objectMapper.createArrayNode();
		});
	}

	/**
	 * Retrieves member eligibility data based on premium license status.
	 */
	private CompletableFuture<JsonNode> getEligibilityData(String groupId,
														   boolean hasAadPremiumP2) {
		return hasAadPremiumP2 ?
				getPIMGroupEligibility(groupId) :
				CompletableFuture.completedFuture(createEmptyValueNode(this.objectMapper));
	}

	/**
	 * Handles paginated requests to the Graph API.
	 */
	private CompletableFuture<JsonNode> getPaginatedResults(String endpoint, int pageSize, String version) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				List<JsonNode> allResults = new ArrayList<>();
				String nextLink = endpoint + "?$top=" + pageSize;

				while (nextLink != null) {
					PaginatedResult result = graphClient.makePaginatedRequest(GraphRequest.builder()
							.withEndpoint(nextLink)
							.withMethod(HttpMethod.GET)
							.withVersion(version)
							.build());
					allResults.addAll(result.getValues());
					nextLink = result.getNextLink();
				}

				ObjectNode combined = objectMapper.createObjectNode();
				combined.putArray(VALUE_KEY).addAll(allResults);
				return combined;

			} catch (Exception e) {
				throw new CompletionException(e);
			}
		}, executor);
	}

	/**
	 * Processes all members including direct and eligible members.
	 */
	private CompletableFuture<List<PrivilegedUser>> processAllMembers(
			String groupId, String roleName, boolean hasAadPremiumP2) {

		List<CompletableFuture<List<PrivilegedUser>>> futures = new ArrayList<>();

		addDirectMembers(futures, groupId, roleName);

		if (hasAadPremiumP2) {
			addEligibleMembers(futures, groupId, roleName);
		}

		return combineMemberResults(futures);
	}

	/**
	 * Adds direct members to the futures list.
	 */
	private void addDirectMembers(List<CompletableFuture<List<PrivilegedUser>>> futures,
								  String groupId,
								  String roleName) {
		JsonNode members = getGroupMembers(groupId).join();

		if (members.has(VALUE_KEY)) {
			members.get(VALUE_KEY).forEach(member -> {
				if (isUserType(member)) {
					futures.add(processUser(member.get("id").asText(), roleName));
				}
			});
		}
	}

	/**
	 * Processes a single user, creating a PrivilegedUser object with the assigned role.
	 */
	private CompletableFuture<List<PrivilegedUser>> processUser(String userId, String roleName) {
		return withRetry(() -> EntraConfigurationService.processUsersInternal(graphClient, userId, roleName, logger), "Get-MgBetaUser");
	}

	/**
	 * Adds eligible members to the futures list.
	 */
	private void addEligibleMembers(List<CompletableFuture<List<PrivilegedUser>>> futures,
									String groupId,
									String roleName) {
		JsonNode eligible = getPIMGroupEligibility(groupId).join();

		if (eligible.has(VALUE_KEY)) {
			eligible.get(VALUE_KEY).forEach(member -> {
				if (isMemberEligible(member)) {
					futures.add(processUser(member.get("principalId").asText(), roleName));
				}
			});
		}
	}

	/**
	 * Checks if a member node represents a user.
	 */
	private boolean isUserType(JsonNode member) {
		return member.get(DATA_TYPE).asText().endsWith("user");
	}

	/**
	 * Checks if a member is eligible.
	 */
	private boolean isMemberEligible(JsonNode member) {
		return "member".equals(member.get("accessId").asText());
	}

	/**
	 * Combines all member processing results into a single list.
	 */
	private CompletableFuture<List<PrivilegedUser>> combineMemberResults(
			List<CompletableFuture<List<PrivilegedUser>>> futures) {

		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.thenApply(x -> futures.stream()
						.map(CompletableFuture::join)
						.flatMap(List::stream)
						.toList());
	}

	@Override
	public ConfigurationResult exportConfiguration() {
		return null;
	}
}
