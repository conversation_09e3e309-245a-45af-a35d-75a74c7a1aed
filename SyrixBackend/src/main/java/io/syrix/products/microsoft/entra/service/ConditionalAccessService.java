package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static io.syrix.common.constants.Constants.DISABLED_VALUE;
import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.entra.service.EntraConfigurationService.CONDITIONS;

class ConditionalAccessService extends BaseConfigurationService {
	private static final String FIRST_SPECIFIC_USER = "1 specific user";
	private static final String NTH_SPECIFIC_USER = "%d specific users";
	private static final String FIRST_SPECIFIC_ROLE = "1 specific role";
	private static final String NTH_SPECIFIC_ROLE = "%d specific roles";
	private static final String FIRST_SPECIFIC_GRROUP = "1 specific group";
	private static final String NTH_SPECIFIC_GRROUP = "%d specific groups";
	private static final String EXTERNAL_TENANTS = "externalTenants";
	private static final String INCLUDED_APPS = "includeApplications";
	private static final String APPS_FILTER = "applicationFilter";
	private static final String INCLUDED_USER_ACTIONS = "includeUserActions";
	private static final String AUTH_CONTEXTS = "includeAuthenticationContextClassReferences";
	private static final String CONTROL_TITLE = "Ensure that only organizationally managed/approved public groups exist";
	private static final String IS_ENABLED = "isEnabled";
	private static final String DEVICE_FILTER = "deviceFilter";

	private static final Map<String, String> STATES = Map.of(
			"enabled", "On",
			"enabledForReportingButNotEnforced", "Report-only",
			DISABLED_VALUE, "Off"
	);

	// Constants matching PowerShell implementation
	private static final Map<String, String> EXTERNAL_USERS_STRINGS = Map.of(
			"b2bCollaborationGuest", "B2B collaboration guest users",
			"b2bCollaborationMember", "B2B collaboration member users",
			"b2bDirectConnectUser", "B2B direct connect users",
			"internalGuest", "Local guest users",
			"serviceProvider", "Service provider users",
			"otherExternalUser", "Other external users"
	);

	private static final Map<String, String> CONTROL_STRINGS = Map.of(
			"mfa", "multifactor authentication",
			"compliantDevice", "device to be marked compliant",
			"domainJoinedDevice", "Hybrid Azure AD joined device",
			"approvedApplication", "approved client app",
			"compliantApplication", "app protection policy",
			"passwordChange", "password change"
	);

	private static final Map<String, String> CAP_TYPES = Map.of(
			"monitorOnly", "Monitor only",
			"blockDownloads", "Block downloads",
			"mcasConfigured", "Use custom policy"
	);

	private static final Map<String, String> ACTIONS_STRINGS = Map.of(
			"urn:user:registersecurityinfo", "Register security info",
			"urn:user:registerdevice", "Register or join devices"
	);

	private static final Map<String, String> CLIENT_TYPE_MAPS = Map.of(
			"exchangeActiveSync", "Exchange ActiveSync Clients",
			"browser", "Browser",
			"mobileAppsAndDesktopClients", "Mobile apps and desktop clients",
			"other", "Other clients",
			"all", "all"
	);

	private final MicrosoftGraphClient graphClient;

	ConditionalAccessService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
	}

	private CompletableFuture<JsonNode> getConditionalAccessPolicies() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/identity/conditionalAccess/policies")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaIdentityConditionalAccessPolicy");
	}

	// Helper methods for policy formatting
	private String getStateString(String state) {
		return STATES.getOrDefault(state, state);
	}

	/**
	 * Gets list of excluded users from a conditional access policy.
	 */
	private List<String> getExcludedUsers(JsonNode cap) {
		List<String> output = new ArrayList<>();
		JsonNode users = cap.path(CONDITIONS).path("users");

		// Check excludeUsers
		JsonNode excludedUsers = users.path("excludeUsers");
		if (excludedUsers.size() == 1) {
			output.add(FIRST_SPECIFIC_USER);
		} else if (excludedUsers.size() > 1) {
			output.add(String.format(NTH_SPECIFIC_USER, excludedUsers.size()));
		}

		// Check excludeRoles
		JsonNode excludedRoles = users.path("excludeRoles");
		if (excludedRoles.size() == 1) {
			output.add(FIRST_SPECIFIC_ROLE);
		} else if (excludedRoles.size() > 1) {
			output.add(String.format(NTH_SPECIFIC_ROLE, excludedRoles.size()));
		}

		// Check excludeGroups
		JsonNode excludedGroups = users.path("excludeGroups");
		if (excludedGroups.size() == 1) {
			output.add(FIRST_SPECIFIC_GRROUP);
		} else if (excludedGroups.size() > 1) {
			output.add(String.format(NTH_SPECIFIC_GRROUP, excludedGroups.size()));
		}

		// If no exclusions, return "None"
		if (output.isEmpty()) {
			output.add("None");
		}

		return output;
	}

	/**
	 //	 * Gets list of included users from a conditional access policy.
	 //	 */
	private List<String> getIncludedUsers(JsonNode cap) {
		List<String> output = new ArrayList<>();
		JsonNode users = cap.path(CONDITIONS).path("users");

		// Check includeUsers
		JsonNode includedUsers = users.path("includeUsers");
		if (includedUsers.isArray() && includedUsers.size() > 0) {
			if (containsValue(includedUsers, "All")) {
				output.add("All");
			} else if (containsValue(includedUsers, "None")) {
				output.add("None");
			} else if (includedUsers.size() == 1) {
				output.add(FIRST_SPECIFIC_USER);
			} else {
				output.add(String.format(NTH_SPECIFIC_USER, includedUsers.size()));
			}
		}

		// Check includeRoles
		JsonNode includedRoles = users.path("includeRoles");
		if (includedRoles.size() == 1) {
			output.add(FIRST_SPECIFIC_ROLE);
		} else if (includedRoles.size() > 1) {
			output.add(String.format(NTH_SPECIFIC_ROLE, includedRoles.size()));
		}

		// Check includeGroups
		JsonNode includedGroups = users.path("includeGroups");
		if (includedGroups.size() == 1) {
			output.add(FIRST_SPECIFIC_GRROUP);
		} else if (includedGroups.size() > 1) {
			output.add(String.format(NTH_SPECIFIC_GRROUP, includedGroups.size()));
		}

		// Check guest/external users
		JsonNode guestUsers = users.path("includeGuestsOrExternalUsers");
		if (!guestUsers.path(EXTERNAL_TENANTS).path("membershipKind").isMissingNode()) {
			String[] userTypes = guestUsers.path("guestOrExternalUserTypes").asText().split(",");
			for (String type : userTypes) {
				output.add(EXTERNAL_USERS_STRINGS.getOrDefault(type, type));
			}
		}

		return output;
	}
	/**
	 * Generates table data for conditional access policies matching PowerShell implementation.
	 */
	CompletableFuture<JsonNode> generateCapTableData(CompletableFuture<JsonNode> policiesFeature) {
		return policiesFeature.thenApply( policies -> {
				ArrayNode tableData = objectMapper.createArrayNode();
				// Process each conditional access policy
				for (JsonNode cap : policies.path(VALUE_FIELD)) {
					ObjectNode capDetails = objectMapper.createObjectNode();

					// Get base policy info
					capDetails.put("Name", cap.path(DISPLAY_NAME_FIELD).asText());
					capDetails.put("State", getStateString(cap.path("state").asText()));

					// Get users included/excluded
					ArrayNode users = objectMapper.createArrayNode();
					String usersIncluded = String.join(", ", getIncludedUsers(cap));
					String usersExcluded = String.join(", ", getExcludedUsers(cap));
					users.add("Users included: " + usersIncluded);
					users.add("Users excluded: " + usersExcluded);
					capDetails.set("Users", users);

					// Get applications and actions
					ArrayNode apps = objectMapper.createArrayNode();
					getApplications(cap).forEach(apps::add);
					capDetails.set("Apps/Actions", apps);

					// Get conditions
					ArrayNode conditions = objectMapper.createArrayNode();
					getConditions(cap).forEach(conditions::add);
					capDetails.set(CONDITIONS, conditions);

					// Get access controls
					capDetails.put("Block/Grant Access", getAccessControls(cap));

					// Get session controls
					ArrayNode sessionControls = objectMapper.createArrayNode();
					getSessionControls(cap).forEach(sessionControls::add);
					capDetails.set("Session Controls", sessionControls);

					tableData.add(capDetails);
				}
				return (JsonNode) tableData;
			}).exceptionally( e -> {
				logger.error("Failed to generate CAP data", e);
				return objectMapper.createArrayNode();
			});
	}


	/**
	 * Gets access controls (grant/deny) from a conditional access policy.
	 */
	private String getAccessControls(JsonNode cap) {
		JsonNode grantControls = cap.path("grantControls");
		if (grantControls.isMissingNode() || grantControls.path("builtInControls").isMissingNode()) {
			return "None";
		}

		JsonNode controls = grantControls.path("builtInControls");
		if (containsValue(controls, "block")) {
			return "Block access";
		}

		List<String> grantControlsList = new ArrayList<>();

		// Process each control in the array
		if (controls.isArray()) {
			StreamSupport.stream(controls.spliterator(), false)
					.map(JsonNode::asText)
					.forEach(control -> grantControlsList.add(CONTROL_STRINGS.getOrDefault(control, control)));

			// Add authentication strength if present
			JsonNode authStrength = grantControls.path("authenticationStrength");
			if (!authStrength.isMissingNode() &&
					!authStrength.path(DISPLAY_NAME_FIELD).isMissingNode() &&
					controls.toString().contains("authenticationStrength")) {
				grantControlsList.add("authentication strength (" +
						authStrength.path(DISPLAY_NAME_FIELD).asText() +
						")");
			}

			// Add terms of use if present
			JsonNode termsOfUse = grantControls.path("termsOfUse");
			if (!termsOfUse.isMissingNode() &&
					termsOfUse.size() > 0 &&
					controls.toString().contains("termsOfUse")) {
				grantControlsList.add("terms of use");
			}

			String controlsString = String.join(", ", grantControlsList);
			if (grantControlsList.size() > 1) {
				String operator = grantControls.path("operator").asText().toLowerCase();
				int lastComma = controlsString.lastIndexOf(',');
				controlsString = controlsString.substring(0, lastComma + 1) + " " + operator +
						controlsString.substring(lastComma + 1);
			}

			return "Allow access but require " + controlsString;
		}

		return "None";
	}

	/**
	 * Gets session controls from a conditional access policy.
	 */
	private List<String> getSessionControls(JsonNode cap) {
		List<String> output = new ArrayList<>();
		JsonNode sessionControls = cap.path("sessionControls");

		if (sessionControls.isMissingNode() || sessionControls.isEmpty()) {
			output.add("None");
			return output;
		}

		// App enforced restrictions
		if (sessionControls.path("applicationEnforcedRestrictions").path(IS_ENABLED).asBoolean()) {
			output.add("Use app enforced restrictions");
		}

		// Cloud App Security
		JsonNode cloudAppSecurity = sessionControls.path("cloudAppSecurity");
		if (cloudAppSecurity.path(IS_ENABLED).asBoolean()) {
			String mode = CAP_TYPES.getOrDefault(
					cloudAppSecurity.path("cloudAppSecurityType").asText(),
					"Unknown"
			);
			output.add("Use Conditional Access App Control (" + mode + ")");
		}

		// Sign-in frequency
		JsonNode signInFrequency = sessionControls.path("signInFrequency");
		if (signInFrequency.path(IS_ENABLED).asBoolean()) {
			if ("everyTime".equals(signInFrequency.path("frequencyInterval").asText())) {
				output.add("Sign-in frequency (every time)");
			} else {
				output.add(String.format("Sign-in frequency (every %d %s)",
						signInFrequency.path(VALUE_FIELD).asInt(),
						signInFrequency.path("type").asText()));
			}
		}

		// Persistent browser
		JsonNode persistentBrowser = sessionControls.path("persistentBrowser");
		if (persistentBrowser.path(IS_ENABLED).asBoolean()) {
			output.add("Persistent browser session (" +
					persistentBrowser.path("mode").asText() + " persistent)");
		}

		// Continuous access evaluation
		if (DISABLED_VALUE.equals(sessionControls.path("continuousAccessEvaluation").path("mode").asText())) {
			output.add("Customize continuous access evaluation");
		}

		// Resilience defaults
		if (sessionControls.path("disableResilienceDefaults").asBoolean()) {
			output.add("Disable resilience defaults");
		}

		if (output.isEmpty()) {
			output.add("None");
		}

		return output;
	}



	private boolean isPolicyForApps(JsonNode applications) {
		return !applications.path(INCLUDED_APPS).isMissingNode() && !applications.path(INCLUDED_APPS).isEmpty()
				|| !applications.path(APPS_FILTER).isMissingNode();
	}

	private boolean isPolicyForActions(JsonNode applications) {
		return !applications.path(INCLUDED_USER_ACTIONS).isMissingNode() &&
				!applications.path(INCLUDED_USER_ACTIONS).isEmpty();
	}

	private boolean isPolicyForAuthContexts(JsonNode applications) {
		return !applications.path(AUTH_CONTEXTS).isMissingNode()
				&& !applications.path(AUTH_CONTEXTS).isEmpty();
	}

	/**
	 * Gets list of applications and actions from a conditional access policy.
	 */
	private List<String> getApplications(JsonNode cap) {
		List<String> output = new ArrayList<>();
		JsonNode applications = cap.path(CONDITIONS).path("applications");

		if (isPolicyForApps(applications)) {
			output.add("Policy applies to: apps");
			handleIncludedApps(applications, output);
			handleApplicationFilter(applications, output);
			handleExcludedApps(applications, output);
		} else if (isPolicyForActions(applications)) {
			output.add("Policy applies to: actions");
			handleIncludedActions(applications, output);
		} else if (isPolicyForAuthContexts(applications)) {
			handleAuthContexts(applications, output);
		}

		return output;
	}


	private void handleIncludedApps(JsonNode applications, List<String> output) {
		JsonNode includedApps = applications.path(INCLUDED_APPS);
		if (containsValue(includedApps, "All")) {
			output.add("Apps included: All");
		} else if (containsValue(includedApps, "None")) {
			output.add("Apps included: None");
		} else if (includedApps.size() == 1) {
			output.add("Apps included: 1 specific app");
		} else if (includedApps.size() > 1) {
			output.add(String.format("Apps included: %d specific apps", includedApps.size()));
		}
	}

	private void handleApplicationFilter(JsonNode applications, List<String> output) {
		JsonNode appFilter = applications.path(APPS_FILTER);
		if (!appFilter.isMissingNode() && !appFilter.path("mode").isMissingNode()) {
			String mode = appFilter.path("mode").asText();
			if ("include".equals(mode)) {
				output.add("Apps included: custom application filter");
			} else if ("exclude".equals(mode)) {
				output.add("Apps excluded: custom application filter");
			}
		}
	}

	private void handleExcludedApps(JsonNode applications, List<String> output) {
		JsonNode excludedApps = applications.path("excludeApplications");
		if (excludedApps.size() == 1) {
			output.add("Apps excluded: 1 specific app");
		} else if (excludedApps.size() > 1) {
			output.add(String.format("Apps excluded: %d specific apps", excludedApps.size()));
		} else if (!applications.path(APPS_FILTER).isMissingNode() &&
				"exclude".equals(applications.path(APPS_FILTER).path("mode").asText())) {
			output.add("Apps excluded: custom application filter");
		} else {
			output.add("Apps excluded: None");
		}
	}

	private void handleIncludedActions(JsonNode applications, List<String> output) {
		String action = applications.path(INCLUDED_USER_ACTIONS).get(0).asText();
		output.add("User action: " + ACTIONS_STRINGS.getOrDefault(action, action));
	}

	private void handleAuthContexts(JsonNode applications, List<String> output) {
		int contextCount = applications.path(AUTH_CONTEXTS).size();
		output.add(String.format("Policy applies to: %d authentication context%s",
				contextCount, contextCount == 1 ? "" : "s"));
	}

	/**
	 * Gets list of conditions from a conditional access policy.
	 */
	private List<String> getConditions(JsonNode cap) {
		List<String> output = new ArrayList<>();
		JsonNode conditions = cap.path(CONDITIONS);

		addRiskLevelConditions(conditions, output);
		addPlatformConditions(conditions, output);
		addLocationConditions(conditions, output);
		addClientAppConditions(conditions, output);
		addDeviceFilterConditions(conditions, output);

		return output;
	}


	/**
	 * Helper to join node values into comma-separated string.
	 */
	private String joinNodeValues(JsonNode node) {
		return String.join(", ", StreamSupport.stream(node.spliterator(), false)
				.map(JsonNode::asText)
				.toList());
	}

	/**
	 //	 * Adds user and sign-in risk level conditions.
	 //	 */
	private void addRiskLevelConditions(JsonNode conditions, List<String> output) {
		// User risk levels
		JsonNode userRiskLevels = conditions.path("userRiskLevels");
		if (userRiskLevels.size() > 0) {
			output.add("User risk levels: " + joinNodeValues(userRiskLevels));
		}

		// Sign-in risk levels
		JsonNode signInRiskLevels = conditions.path("signInRiskLevels");
		if (signInRiskLevels.size() > 0) {
			output.add("Sign-in risk levels: " + joinNodeValues(signInRiskLevels));
		}
	}

	/**
	 * Adds device platform conditions.
	 */
	private void addPlatformConditions(JsonNode conditions, List<String> output) {
		JsonNode platforms = conditions.path("platforms");
		JsonNode includedPlatforms = platforms.path("includePlatforms");

		if (!includedPlatforms.isMissingNode()) {
			output.add("Device platforms included: " + joinNodeValues(includedPlatforms));

			JsonNode excludedPlatforms = platforms.path("excludePlatforms");
			if (excludedPlatforms.size() == 0) {
				output.add("Device platforms excluded: none");
			} else {
				output.add("Device platforms excluded: " + joinNodeValues(excludedPlatforms));
			}
		}
	}

	/**
	 * Adds location conditions.
	 */
	private void addLocationConditions(JsonNode conditions, List<String> output) {
		JsonNode locations = conditions.path("locations");
		JsonNode includedLocations = locations.path("includeLocations");

		if (!includedLocations.isMissingNode()) {
			addIncludedLocations(includedLocations, output);
			addExcludedLocations(locations.path("excludeLocations"), output);
		}
	}

	private void addIncludedLocations(JsonNode includedLocations, List<String> output) {
		if (containsValue(includedLocations, "All")) {
			output.add("Locations included: all locations");
		} else if (containsValue(includedLocations, "AllTrusted")) {
			output.add("Locations included: all trusted locations");
		} else {
			addSpecificLocationCount(includedLocations.size(), "included", output);
		}
	}

	private void addExcludedLocations(JsonNode excludedLocations, List<String> output) {
		if (containsValue(excludedLocations, "AllTrusted")) {
			output.add("Locations excluded: all trusted locations");
		} else if (excludedLocations.size() == 0) {
			output.add("Locations excluded: none");
		} else {
			addSpecificLocationCount(excludedLocations.size(), "excluded", output);
		}
	}

	private void addSpecificLocationCount(int count, String type, List<String> output) {
		if (count == 1) {
			output.add("Locations " + type + ": 1 specific location");
		} else if (count > 1) {
			output.add(String.format("Locations %s: %d specific locations", type, count));
		}
	}

	/**
	 * Adds client app conditions.
	 */
	private void addClientAppConditions(JsonNode conditions, List<String> output) {
		JsonNode clientAppTypes = conditions.path("clientAppTypes");
		if (clientAppTypes.size() > 0) {
			List<String> apps = StreamSupport.stream(clientAppTypes.spliterator(), false)
					.map(app -> CLIENT_TYPE_MAPS.getOrDefault(app.asText(), app.asText()))
					.toList();
			output.add("Client apps included: " + String.join(", ", apps));
		}
	}

	/**
	 * Adds device filter conditions.
	 */
	private void addDeviceFilterConditions(JsonNode conditions, List<String> output) {
		JsonNode devices = conditions.path("devices");
		JsonNode deviceFilter = devices.path(DEVICE_FILTER);
		JsonNode mode = deviceFilter.path("mode");

		if (!deviceFilter.isMissingNode() && !mode.isMissingNode()) {
			output.add(String.format("Custom device filter in %s mode active", mode.asText()));
		}
	}


	/**
	 * Exports the conditional access policies configuration.
	 * This method retrieves policy data that will be used by EntraConfigurationService.
	 *
	 * @return A ConfigurationResult containing the conditional access policies
	 */
	@Override
	public ConfigurationResult exportConfiguration() {
		logger.info("Exporting conditional access policies configuration");

		try {
			// Get the policies
			CompletableFuture<JsonNode> policies = getConditionalAccessPolicies();

			// Build the result with both policies and table data
			Map<String, CompletableFuture<?>> resultData = new HashMap<>();
			resultData.put("conditional_access_policies", policies);
			resultData.put("cap_table_data", generateCapTableData(policies));

			// Wait for all futures to complete and then build the result
			return waitForFutures(resultData)
					.thenApply(map -> {
						// Add successful and unsuccessful commands
						map.put("successful_commands", getSuccessfulCommands());
						map.put("unsuccessful_commands", getUnsuccessfulCommands());

						// Build and return the result
						return ConfigurationResult.builder()
								.withData(objectMapper.valueToTree(map))
								.withTimestamp(Instant.now())
								.withMetadata(buildMetadata(SERVICE_VERSION))
								.build();
					})
					.join();  // Wait for the final result

		} catch (Exception e) {
			logger.error("Failed to export conditional access policies configuration", e);
			throw new ConfigurationExportException("Failed to export conditional access policies", e);
		}
	}
}