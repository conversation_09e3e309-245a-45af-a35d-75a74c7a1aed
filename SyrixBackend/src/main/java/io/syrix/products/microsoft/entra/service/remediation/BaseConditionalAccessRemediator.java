package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.entra.model.ConditionalAccessUserConfiguration;
import io.syrix.products.microsoft.entra.model.ConditionalPolisyState;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import org.jetbrains.annotations.NotNull;

import java.net.http.HttpRequest;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.products.microsoft.entra.model.ConditionalPolisyState.ENABLED_FOR_REPORTING_BUT_NOT_ENFORCED;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.CA_POLICIES_ENDPOINT;

/**
 * Base class for remediators that create Conditional Access policies.
 * Provides common functionality for policy creation, checking, etc.
 */
public abstract class BaseConditionalAccessRemediator extends RemediatorBase {
	protected static final String AUTH_STRENGTH_ENDPOINT = "/identity/conditionalAccess/authenticationStrengths/policies";
	protected final MicrosoftGraphClient graphClient;
	protected final ObjectMapper objectMapper;
	protected final ConditionalAccessUserConfiguration userConfiguration;
	protected ConditionalPolisyState policyState = ENABLED_FOR_REPORTING_BUT_NOT_ENFORCED;
	protected final String policyName;

	/**
	 * Constructor for BaseConditionalAccessRemediator.
	 *
	 * @param graphClient Microsoft Graph API client
	 * @param policyName Name of the conditional access policy
	 * @param userConfiguration User configuration for the policy
	 * @param policyState State of the policy (enabled, disabled, or audit mode)
	 */
	protected BaseConditionalAccessRemediator(
			MicrosoftGraphClient graphClient,
			String policyName,
			ConditionalAccessUserConfiguration userConfiguration,
			ConditionalPolisyState policyState) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
		this.policyName = policyName;
		this.userConfiguration = userConfiguration;
		this.policyState = policyState;
	}

	/**
	 * Remediation implementation that checks for policy existence and creates it if needed.
	 *
	 * @return CompletableFuture with the remediation result
	 */
	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for {} policy using MS Graph REST API (state: {})",
				policyName, policyState.getState());

		return checkExistingPolicy()
				.thenCompose(exists -> {
					if (Boolean.TRUE.equals(exists)) {
						logger.info("{} policy already exists. Skipping creation.", policyName);
						return IPolicyRemediator.success(getPolicyId(),
										policyName + " policy already exists");
					}

					return createPolicy();
				})
				.exceptionally(ex -> {
					logger.error("Exception while creating {} policy", policyName, ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if a policy with the specified name already exists.
	 *
	 * @return CompletableFuture<Boolean> - true if policy exists, false otherwise
	 */
	protected CompletableFuture<Boolean> checkExistingPolicy() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "displayName eq '" + policyName + "'")
						.build()
		).thenApply(response -> response.has(Constants.VALUE_FIELD)
				&& response.get(Constants.VALUE_FIELD).size() > 0);
	}

	/**
	 * Creates a conditional access policy based on the implementation's policy payload.
	 *
	 * @return CompletableFuture with the API response
	 */
	protected CompletableFuture<JsonNode> createPolicy() {
		ObjectNode policyPayload = createPolicyPayload();
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withMethod(HttpMethod.POST)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint(CA_POLICIES_ENDPOINT)
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("{} policy created successfully with state: {}",
							policyName, policyState.getState());

					String statusMessage = getMessage();

					return IPolicyRemediator.success(getPolicyId(), statusMessage);
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error creating " + policyName + " policy";
					logger.error("Failed to create {} policy: {}", policyName, error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to create " + policyName + " policy", e);
		}
	}

	@NotNull
	private String getMessage() {
		return String.format("%s has been %s for specified users",
				policyName,
				getPolicyStateMessage());
	}

	private String getPolicyStateMessage() {
		if (policyState == ConditionalPolisyState.ENABLED) {
			return "enabled";
		}
		if (policyState == ENABLED_FOR_REPORTING_BUT_NOT_ENFORCED) {
			return "configured in audit mode";
		}
		return "configured but disabled";
	}

	/**
	 * Creates the base structure for a conditional access policy payload.
	 *
	 * @return ObjectNode with the policy base structure
	 */
	protected ObjectNode createBasePolicyPayload() {
		ObjectNode policyPayload = objectMapper.createObjectNode();

		// Basic policy settings
		policyPayload.put("displayName", policyName);
		policyPayload.put("state", policyState.getState());

		// Create conditions object
		ObjectNode conditions = objectMapper.createObjectNode();

		// Client app types - required for conditional access
		ArrayNode clientAppTypes = objectMapper.createArrayNode();
		clientAppTypes.add("all");
		conditions.set("clientAppTypes", clientAppTypes);

		// Applications condition - default to All
		ObjectNode applications = objectMapper.createObjectNode();
		ArrayNode includeApps = objectMapper.createArrayNode();
		includeApps.add("All");
		applications.set("includeApplications", includeApps);
		applications.set("excludeApplications", objectMapper.createArrayNode());
		applications.set("includeUserActions", objectMapper.createArrayNode());
		conditions.set("applications", applications);

		// Users condition - use the provided userConfiguration
		conditions.set("users", userConfiguration.toJsonNode(objectMapper));

		// Empty/null other conditions
		conditions.set("signInRiskLevels", objectMapper.createArrayNode());
		conditions.set("userRiskLevels", objectMapper.createArrayNode());

		policyPayload.set("conditions", conditions);

		return policyPayload;
	}

	/**
	 * Abstract method to be implemented by each specific policy remediation class.
	 * Creates the full policy payload specific to each policy requirement.
	 *
	 * @return ObjectNode with the complete policy payload
	 */
	protected abstract ObjectNode createPolicyPayload();
}
