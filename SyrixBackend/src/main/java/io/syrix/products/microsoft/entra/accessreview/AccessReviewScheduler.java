package io.syrix.products.microsoft.entra.accessreview;

import io.syrix.protocols.client.MicrosoftGraphClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Scheduler for guest user access review automation
 */
@Component
public class AccessReviewScheduler {
	private static final Logger logger = LoggerFactory.getLogger(AccessReviewScheduler.class);

	private final MicrosoftGraphClient graphClient;
	private final AccessReviewDecisionService decisionService;
	private final EntraIDGuestAccessReviewRemediator remediator;

	@Autowired
	public AccessReviewScheduler(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		// Configure trusted domains (optional)
		List<String> trustedDomains = Arrays.asList("partner.com", "trusted.org");
		this.decisionService = new AccessReviewDecisionService(graphClient, trustedDomains, 90);
		this.remediator = new EntraIDGuestAccessReviewRemediator(graphClient);
	}

	/**
	 * Runs daily to ensure monthly guest access reviews are configured and
	 * processes any active reviews
	 */
	@Scheduled(cron = "0 0 1 * * ?") // Run at 1:00 AM every day
	public void ensureGuestAccessReviews() {
		logger.info("Starting scheduled guest access review check");

		try {
			// First ensure the access review is configured
			remediator.remediate()
					.thenCompose(result -> {
						logger.info("Access review configuration result: {}", result);

						// Then process any active access reviews
						return decisionService.processActiveGuestAccessReviews();
					})
					.exceptionally(ex -> {
						logger.error("Error during scheduled access review processing", ex);
						return null;
					})
					.join();
		} catch (Exception e) {
			logger.error("Exception in scheduled access review task", e);
		}
	}
}