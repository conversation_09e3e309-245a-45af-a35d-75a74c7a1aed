package io.syrix.products.microsoft.sharepoint.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Calendar;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SharepointDateSerializer extends JsonSerializer<String> {
    private static final Logger logger = LoggerFactory.getLogger(SharepointDateSerializer.class);
    private static final String REGEX = "^/Date\\((\\d+),(\\d+),(\\d+),(\\d+),(\\d+),(\\d+),(\\d+)\\)/$";
    private static final Pattern PATTERN = Pattern.compile(REGEX);

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String date = parseDate(value);
        gen.writeString(date);
    }

    private String parseDate(String inputValue) {
        Matcher matcher = PATTERN.matcher(inputValue);

        if (matcher.matches()) {
            try {
                int year = Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));
                int day = Integer.parseInt(matcher.group(3));
                int hour = Integer.parseInt(matcher.group(4));
                int minute = Integer.parseInt(matcher.group(5));
                int second = Integer.parseInt(matcher.group(6));
                int millisecond = Integer.parseInt(matcher.group(7));

                Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, day);
                calendar.set(Calendar.HOUR_OF_DAY, hour);
                calendar.set(Calendar.MINUTE, minute);
                calendar.set(Calendar.SECOND, second);
                calendar.set(Calendar.MILLISECOND, millisecond);

                long timestamp = calendar.getTimeInMillis();

                return String.format("\\/Date(%d)\\/", timestamp);
            } catch (NumberFormatException e) {
                logger.error("Can not parse date: {}", inputValue, e);
                return inputValue;
            }
        } else {
            return inputValue;
        }

    }
}