package io.syrix.products.microsoft.teams;

/**
 * Constants for Teams Configuration Service
 */
public class TeamsConstants {
    // Configuration keys
    public static final String CONFIG_KEY_MEETING_POLICIES = "meeting_policies";
    public static final String CONFIG_KEY_FEDERATION_CONFIGURATION = "federation_configuration";
    public static final String CONFIG_KEY_TEAMS_TENANT_INFO = "teams_tenant_info";
    public static final String CONFIG_KEY_CLIENT_CONFIGURATION = "client_configuration";
    public static final String CONFIG_KEY_APP_POLICIES = "app_policies";
    public static final String CONFIG_KEY_BROADCAST_POLICIES = "broadcast_policies";
    public static final String CONFIG_KEY_TENANT_ID = "Tenant_ID";
    
    // Policy identifiers
    public static final String GLOBAL_POLICY_IDENTITY = "Global";
    public static final String IDENTITY_PROPERTY = "Identity";
    
    // Policy property names
    public static final String ALLOW_CLOUD_RECORDING_PROPERTY = "AllowCloudRecording";
    public static final String ALLOW_FEDERATED_USERS_PROPERTY = "AllowFederatedUsers";
    public static final String ALLOW_PUBLIC_USERS_PROPERTY = "AllowPublicUsers";
    public static final String ALLOW_TEAMS_CONSUMER_PROPERTY = "AllowTeamsConsumer";
    public static final String ALLOW_TEAMS_CONSUMER_INBOUND_PROPERTY = "AllowTeamsConsumerInbound";
    public static final String ALLOW_EMAIL_INTO_CHANNEL_PROPERTY = "AllowEmailIntoChannel";
    public static final String AUTO_ADMITTED_USERS_PROPERTY = "AutoAdmittedUsers";
    public static final String AUTO_ADMITTED_USERS_PSTN_PROPERTY = "AllowPSTNUsersToBypassLobby";
    public static final String ANONYMOUS_START_MEETING_PROPERTY = "AllowAnonymousUsersToStartMeeting";
    public static final String BROADCAST_RECORDING_MODE_PROPERTY = "BroadcastRecordingMode";
    public static final String ALLOW_EXTERNAL_CONTROL_PROPERTY = "AllowExternalParticipantGiveRequestControl";
    public static final String ALLOW_PSTN_BYPASS_PROPERTY = "PreventPSTNDialOut";
    
    // App policy properties
    public static final String DEFAULT_CATALOG_APPS_TYPE_PROPERTY = "DefaultCatalogAppsType";
    public static final String GLOBAL_CATALOG_APPS_TYPE_PROPERTY = "GlobalCatalogAppsType";
    public static final String PRIVATE_CATALOG_APPS_TYPE_PROPERTY = "PrivateCatalogAppsType";
    
    // PowerShell commands
    public static final String SET_CSTEAMSMEETINGPOLICY_COMMAND = "Set-CsTeamsMeetingPolicy";
    public static final String SET_CSTENANTSETTINGS_COMMAND = "Set-CsTenantFederationConfiguration";
    public static final String SET_CSTEAMSCLIENTCONFIG_COMMAND = "Set-CsTeamsClientConfiguration";
    public static final String SET_CSTEAMSAPPPERMISSIONPOLICY_COMMAND = "Set-CsTeamsAppPermissionPolicy";
    public static final String SET_CSTEAMSBROADCASTPOLICY_COMMAND = "Set-CsTeamsMeetingBroadcastPolicy";
    
    // Status messages
    public static final String DEFAULT_SUCCESS_MESSAGE = "fixed";
    public static final String CLOUD_RECORDING_SUCCESS_MESSAGE = "Allow cloud recording fixed";
    public static final String FEDERATED_USERS_SUCCESS_MESSAGE = "Federated users access fixed";
    public static final String PUBLIC_USERS_SUCCESS_MESSAGE = "Public users access fixed";
    public static final String TEAMS_CONSUMER_SUCCESS_MESSAGE = "Teams consumer access fixed";
    public static final String TEAMS_CONSUMER_INBOUND_SUCCESS_MESSAGE = "Teams consumer inbound access fixed";
    public static final String EMAIL_INTO_CHANNEL_SUCCESS_MESSAGE = "Email into channel setting fixed";
    public static final String AUTO_ADMITTED_USERS_SUCCESS_MESSAGE = "Auto-admitted users setting fixed";
    public static final String AUTO_ADMITTED_USERS_COMPANY_SUCCESS_MESSAGE = "auto-admit set to company users, PSTN users require lobby approval.";
    public static final String PSTN_USERS_BYPASS_SUCCESS_MESSAGE = "PSTN users bypass setting fixed";
    public static final String ANONYMOUS_START_MEETING_SUCCESS_MESSAGE = "Anonymous users start meeting setting fixed";
    public static final String BROADCAST_RECORDING_MODE_SUCCESS_MESSAGE = "Broadcast recording mode fixed";
    public static final String EXTERNAL_CONTROL_SUCCESS_MESSAGE = "External control setting fixed";
    public static final String PSTN_BYPASS_SUCCESS_MESSAGE = "PSTN bypass setting fixed";
    public static final String CATALOG_APPS_TYPE_SUCCESS_MESSAGE = "Catalog apps type setting fixed";
    
    // Error messages
    public static final String ERROR_UNKNOWN = "Unknown error";
}
