package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.classifiers.ApplicationSecurityRiskClassifier;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.products.microsoft.entra.model.EnterpriseAppEnricher;
import io.syrix.products.microsoft.entra.model.EnterpriseApplication;
import io.syrix.products.microsoft.entra.model.EnterpriseApplicationProcessor;
import io.syrix.products.microsoft.entra.model.EnterpriseApplicationSerializer;
import io.syrix.products.microsoft.entra.model.GraphResourceMapper;
import io.syrix.products.microsoft.entra.model.RiskAssessment;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.reports.service.EnterpriseAppReportGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.USER_PRINCIPAL_NAME;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.protocols.utils.ProtocolConstants.AUTHORIZATION_POLICY;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

class EnterpriseApplicationService extends BaseConfigurationService {
	private static final String RESOURCE_ID = "resourceId";
	private static final String SCOPE = "scope";
	private static final String CONSENT_TYPE = "consentType";
	private static final String PRINCIPAL_TYPE = "principalType";
	private static final String PERMISSION_TYPE = "permissionType";
	private static final String SERVICE_VERSION = "1.0";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;
	private final Logger logger;
	EnterpriseAppEnricher enricher = null;

	EnterpriseApplicationService(MicrosoftGraphClient graphClient, ObjectMapper objectMapper, MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.graphClient = graphClient;
		this.objectMapper = objectMapper;
		this.logger = LoggerFactory.getLogger(getClass());
		this.enricher = new EnterpriseAppEnricher(graphClient);
	}

	/**
	 * Exports the enterprise applications configuration.
	 * This method retrieves detailed information about all enterprise applications
	 * including permissions, branding, and user assignments.
	 *
	 * @return A ConfigurationResult containing the enterprise applications data
	 */
	@Override
	public ConfigurationResult exportConfiguration() {
		logger.info("Exporting enterprise applications configuration");

		try {
			// Get enterprise applications data
			CompletableFuture<JsonNode> enterpriseAppsFuture = getEnterpriseApplications();
//			CompletableFuture<List<EnterpriseApplication>> processedAppsFuture = enterpriseAppsFuture
//					.thenApply(this::processEnterpriseApplications);

			// Build the result data map
			Map<String, CompletableFuture<?>> resultData = new HashMap<>();
			resultData.put("enterprise_applications", enterpriseAppsFuture);

			// Wait for all futures to complete and then build the result
			return waitForFutures(resultData)
					.thenApply(map -> {
						// Process apps for risk assessment if needed
						List<EnterpriseApplication> processedApps = (List<EnterpriseApplication>) map.get("processed_applications");
						if (processedApps != null && !processedApps.isEmpty()) {
							try {
//								ApplicationSecurityRiskClassifier classifier = new ApplicationSecurityRiskClassifier("1234");
//								classifyApps(processedApps, classifier);
								
								// Create serialized version for the result
								ArrayNode serializedApps = objectMapper.createArrayNode();
								processedApps.forEach(app -> {
									try {
										serializedApps.add(EnterpriseApplicationSerializer.serialize(app));
									} catch (Exception e) {
										logger.error("Error serializing app {}: {}", 
												app.getBasic().getDisplayName(), e.getMessage());
									}
								});
								map.put("enterprise_apps_with_risk", serializedApps);
							} catch (Exception e) {
								logger.error("Error processing apps for risk assessment: {}", e.getMessage());
							}
						}

						// Add successful and unsuccessful commands
						map.put("successful_commands", getSuccessfulCommands());
						map.put("unsuccessful_commands", getUnsuccessfulCommands());

						// Build and return the result
						return ConfigurationResult.builder()
								.withData(objectMapper.valueToTree(map))
								.withTimestamp(Instant.now())
								.withMetadata(buildMetadata(SERVICE_VERSION))
								.build();
					})
					.join();  // Wait for the final result

		} catch (Exception e) {
			logger.error("Failed to export enterprise applications configuration", e);
			throw new ConfigurationExportException("Failed to export enterprise applications", e);
		}
	}

	/**
	 * Gets enterprise applications configuration including permissions.
	 */
	private CompletableFuture<JsonNode> getEnterpriseApplications() {
		Map<String, String> params = Map.of(PARAM_SELECT, "id,displayName,appRoles,publisherName,info");
		return withRetry(() -> graphClient.getAzureServicePrincipals(params, null),
				"Get-EnterpriseApplications").thenCompose(this::enrichEnterpriseApps);
	}

	/**
	 * Gets application permissions (app roles) for an enterprise application.
	 */
	private CompletableFuture<JsonNode> getApplicationPermissions(String appId) {
		return withRetry(() ->
						graphClient.getAzureServicePrincipals(null, "/" + appId + "/appRoleAssignments"),
				"Get-ApplicationPermissions").thenApply(this::processPermissions);
	}

	/**
	 * Gets delegated permissions (OAuth2 permissions) for an enterprise application.
	 */
	private CompletableFuture<JsonNode> getDelegatedPermissions(String appId) {
		return withRetry(() -> graphClient.getAzureServicePrincipals(null,
				"/" + appId + "/oauth2PermissionGrants"), "Get-DelegatedPermissions")
				.thenApply(this::processPermissions);
	}

	/**
	 * Processes and formats permissions data.
	 */
	private JsonNode processPermissions(JsonNode permissionsResponse) {
		ArrayNode processedPermissions = objectMapper.createArrayNode();

		if (permissionsResponse.has(VALUE_FIELD)) {
			for (JsonNode permission : permissionsResponse.get(VALUE_FIELD)) {
				ObjectNode processedPermission = objectMapper.createObjectNode();

				// Add common fields
				if (permission.has(RESOURCE_ID)) {
					processedPermission.put(RESOURCE_ID, permission.get(RESOURCE_ID).asText());
				}

				// Handle app roles (application permissions)
				if (permission.has(EntraConfigurationService.APP_ROLE_ID)) {
					processedPermission.put(PERMISSION_TYPE, EntraConfigurationService.APPLICATION);
					processedPermission.put(EntraConfigurationService.APP_ROLE_ID, permission.get(EntraConfigurationService.APP_ROLE_ID).asText());
				}

				// Handle OAuth2 permissions (delegated permissions)
				if (permission.has(SCOPE)) {
					processedPermission.put(PERMISSION_TYPE, "Delegated");
					processedPermission.put(SCOPE, permission.get(SCOPE).asText());
				}

				// Add consent type if available
				if (permission.has(CONSENT_TYPE)) {
					processedPermission.put(CONSENT_TYPE, permission.get(CONSENT_TYPE).asText());
				}

				processedPermissions.add(processedPermission);
			}
		}

		return processedPermissions;
	}


	/**
	 * Gets the resource names for each permission's resourceId.
	 * This helps map permissions to readable resource names.
	 */
	@SuppressWarnings("No usage")
	private CompletableFuture<Map<String, String>> getResourceNames(Set<String> resourceIds) {
		List<CompletableFuture<JsonNode>> resourceFutures = resourceIds.stream()
				.map(resourceId -> withRetry(() ->
						graphClient.getAzureServicePrincipals(Map.of(PARAM_SELECT, DISPLAY_NAME_FIELD),
								"/" + resourceId), "Get-ResourceName")).toList();

		return CompletableFuture.allOf(resourceFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					Map<String, String> resourceNames = new HashMap<>();
					for (int i = 0; i < resourceIds.size(); i++) {
						String resourceId = (String) resourceIds.toArray()[i];
						JsonNode resource = resourceFutures.get(i).join();
						if (resource.has(DISPLAY_NAME_FIELD)) {
							resourceNames.put(resourceId, resource.get(DISPLAY_NAME_FIELD).asText());
						}
					}
					return resourceNames;
				});
	}


	/**
	 * Gets branding information for an enterprise application.
	 */
	@SuppressWarnings("No usage")
	private JsonNode getBranding(JsonNode app) {
		JsonNode info = app.get("info");
		if (info != null && !info.isEmpty()) {
			return info.get("logoUrl");
		}
		return objectMapper.createObjectNode();
	}

	/**
	 * Gets users and groups assigned to an enterprise application.
	 */
	private CompletableFuture<JsonNode> getAssignedUsers(String appId) {
		Map<String, String> params = Map.of(PARAM_SELECT, "principalId,principalType,appRoleId");
		return withRetry(() ->
						graphClient.getAzureServicePrincipals(params, "/" + appId + "/appRoleAssignedTo"),
				"Get-EnterpriseAppUsers").thenCompose(this::enrichAssignments);
	}



	/**
	 * Enriches assignments with principal (user/group) details
	 */
	private CompletableFuture<JsonNode> enrichAssignments(JsonNode assignments) {
		if (!assignments.has(VALUE_FIELD)) {
			return CompletableFuture.completedFuture(objectMapper.createArrayNode());
		}

		List<CompletableFuture<JsonNode>> enrichedAssignments = new ArrayList<>();

		for (JsonNode assignment : assignments.get(VALUE_FIELD)) {
			String principalId = assignment.get("principalId").asText();
			String principalType = assignment.get(PRINCIPAL_TYPE).asText();

			CompletableFuture<JsonNode> enrichedAssignment = CompletableFuture.supplyAsync(() -> {
				ObjectNode enriched = objectMapper.createObjectNode();
				enriched.put(EntraConfigurationService.APP_ROLE_ID, assignment.get(EntraConfigurationService.APP_ROLE_ID).asText());
				enriched.put(PRINCIPAL_TYPE, principalType);

				// Get principal details based on type
				try {
					if ("User".equals(principalType)) {
						CompletableFuture<JsonNode> userDetails = withRetry(() ->
								graphClient.makeGraphRequest(GraphRequest.builder()
										.v1()
										.withEndpoint("/users/" + principalId)
										.addQueryParam(PARAM_SELECT, "displayName,userPrincipalName")
										.build()), "Get-UserDetails");

						JsonNode user = userDetails.join();
						enriched.put(DISPLAY_NAME_FIELD, user.get(DISPLAY_NAME_FIELD).asText());
						enriched.put(USER_PRINCIPAL_NAME, user.get(USER_PRINCIPAL_NAME).asText());

					} else if ("Group".equals(principalType)) {
						CompletableFuture<JsonNode> groupDetails = withRetry(() ->
								graphClient.makeGraphRequest(GraphRequest.builder()
										.v1()
										.withEndpoint("/groups/" + principalId)
										.addQueryParam(PARAM_SELECT, DISPLAY_NAME_FIELD)
										.build()), "Get-GroupDetails");

						JsonNode group = groupDetails.join();
						enriched.put(DISPLAY_NAME_FIELD, group.get(DISPLAY_NAME_FIELD).asText());
					}
				} catch (Exception e) {
					logger.error("Failed to enrich principal details for {}: {}", principalId, e.getMessage());
					enriched.put(DISPLAY_NAME_FIELD, "Error retrieving details");
				}

				return enriched;
			}, executor);

			enrichedAssignments.add(enrichedAssignment);
		}

		return CompletableFuture.allOf(enrichedAssignments.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					ArrayNode result = objectMapper.createArrayNode();
					enrichedAssignments.stream()
							.map(CompletableFuture::join)
							.forEach(result::add);
					return result;
				});
	}

    /**
     * Classifies enterprise applications for security risk assessment.
     * Outputs JSON files with risk assessment data and generates a report.
     */
	private void classifyApps(List<EnterpriseApplication> apps, ApplicationSecurityRiskClassifier classifier) {
		apps.forEach(app -> {
			try {
				Path filePath = Paths.get("AppsJson", app.getBasic().getDisplayName() + ".json");
				JsonNode appJson = EnterpriseApplicationSerializer.serialize(app);
				RiskAssessment assesment = classifier.classifyRisk(appJson);
				app.setRiskLevel(assesment.riskLevel());
				app.setJustification(assesment.justification());
				Files.writeString(filePath, EnterpriseApplicationSerializer.serializeToString(app));
			} catch (Exception excp) {
				logger.error("Error serializing EnterpriseApplication", excp);
			}
		});
		GraphResourceMapper resourceMapper = new GraphResourceMapper(this.graphClient);
		resourceMapper.initialize();
		EnterpriseAppReportGenerator generator = new EnterpriseAppReportGenerator();
		generator.saveReport(apps, "enterprise-apps-report.html");
	}

    /**
     * Processes raw enterprise applications JSON into structured objects.
     */
	private List<EnterpriseApplication> processEnterpriseApplications(JsonNode applicationsJson) {
		EnterpriseApplicationProcessor processor = new EnterpriseApplicationProcessor(objectMapper, this.enricher);
		return processor.processApplications(applicationsJson);
	}

	/**
	 * Enriches enterprise applications with permissions and other details.
	 */
	private CompletableFuture<JsonNode> enrichEnterpriseApps(JsonNode servicePrincipals) {
		if (!servicePrincipals.has(VALUE_FIELD)) {
			return CompletableFuture.completedFuture(objectMapper.createArrayNode());
		}

		List<CompletableFuture<JsonNode>> enrichedAppFutures = new ArrayList<>();

		for (JsonNode app : servicePrincipals.get(VALUE_FIELD)) {
			String appId = app.get("id").asText();

			CompletableFuture<JsonNode> enrichedApp = CompletableFuture.supplyAsync(() -> {
				ObjectNode enrichedAppNode = objectMapper.createObjectNode();
				enrichedAppNode.set("basic", app);

				// Get permissions (both delegated and application)
				CompletableFuture<JsonNode> permissionsFuture = getApplicationPermissions(appId);
				CompletableFuture<JsonNode> delegatedPermissionsFuture = getDelegatedPermissions(appId);

				// Get branding and users as before
				CompletableFuture<JsonNode> brandingFuture = getBranding(appId);
				CompletableFuture<JsonNode> usersFuture = getAssignedUsers(appId);

				try {
					enrichedAppNode.set(EntraConfigurationService.PERMISSIONS, permissionsFuture.join());
					enrichedAppNode.set("delegatedPermissions", delegatedPermissionsFuture.join());
					enrichedAppNode.set("branding", brandingFuture.join());
					enrichedAppNode.set("assignedUsers", usersFuture.join());
					return enrichedAppNode;
				} catch (Exception e) {
					logger.error("Failed to enrich enterprise app {}: {}", appId, e.getMessage());
					return enrichedAppNode;
				}
			}, executor);

			enrichedAppFutures.add(enrichedApp);
		}

		return CompletableFuture.allOf(enrichedAppFutures.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					ArrayNode result = objectMapper.createArrayNode();
					enrichedAppFutures.stream()
							.map(CompletableFuture::join)
							.forEach(result::add);
					return result;
				});
	}

	/**
	 * Gets branding information for an enterprise application.
	 */
	private CompletableFuture<JsonNode> getBranding(String appId) {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder()
						.beta()
						.withEndpoint("/servicePrincipals/" + appId)
						.addQueryParam(PARAM_SELECT, "logoUrl")
						.build()), "Get-EnterpriseAppBranding");
	}

	private CompletableFuture<JsonNode> getAuthorizationPolicies() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint(AUTHORIZATION_POLICY)
						.withMethod(HttpMethod.GET)
						.build()), "authorizationPolicy")
				.exceptionally(e -> {
					logger.error("Failed to call getAuthorizationPolicies {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}
}