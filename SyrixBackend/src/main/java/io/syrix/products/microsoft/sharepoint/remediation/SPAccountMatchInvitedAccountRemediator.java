package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;

import java.util.concurrent.CompletableFuture;

/**
 * This is an outdated policy. It does not require correction. It will be removed soon.
 *
 * @see <a href="/src/main/resources/rego/SharepointConfig.rego">SharepointConfig.rego</a>
 */
@PolicyRemediator("MS.SHAREPOINT.1.4v1")
public class SPAccountMatchInvitedAccountRemediator extends SPRemediatorBase {

	public SPAccountMatchInvitedAccountRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
		super(client, tenant, spConfig);
	}

	// constructor for Rollback interface
	public SPAccountMatchInvitedAccountRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
		this(client, tenant, null);
	}


	@Override
	public CompletableFuture<JsonNode> remediate() {
		return IPolicyRemediator.success(getPolicyId(), "This is an outdated policy. It does not require correction. It will be removed soon.");
	}
}
