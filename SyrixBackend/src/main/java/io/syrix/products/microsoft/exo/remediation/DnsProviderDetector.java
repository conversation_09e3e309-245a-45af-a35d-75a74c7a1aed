package io.syrix.products.microsoft.exo.remediation;

import io.syrix.common.dns.DnsService;
import io.syrix.common.dns.DnsQueryResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Utility class for detecting DNS providers based on nameserver records.
 * This helps determine the appropriate API to use for SPF record remediation.
 */
public class DnsProviderDetector {
	private static final Logger logger = LoggerFactory.getLogger(DnsProviderDetector.class);

	// Provider signature patterns
	private static final Map<String, Pattern> PROVIDER_PATTERNS = new HashMap<>();

	static {
		// Initialize known DNS provider patterns
		PROVIDER_PATTERNS.put("Azure DNS", Pattern.compile(".*azure-dns.*"));
		PROVIDER_PATTERNS.put("Cloudflare", Pattern.compile(".*cloudflare.*"));
		PROVIDER_PATTERNS.put("AWS Route 53", Pattern.compile(".*awsdns.*"));
		PROVIDER_PATTERNS.put("GoDaddy", Pattern.compile(".*godaddy.*|.*domaincontrol.*"));
		PROVIDER_PATTERNS.put("Google Domains", Pattern.compile(".*googledomains.*"));
		PROVIDER_PATTERNS.put("Namecheap", Pattern.compile(".*namecheap.*|.*registrar-servers.*"));
		PROVIDER_PATTERNS.put("DigitalOcean", Pattern.compile(".*digitalocean.*"));
		PROVIDER_PATTERNS.put("OVH", Pattern.compile(".*ovh.*"));
	}

	private final DnsService dnsService;

	/**
	 * Creates a new DnsProviderDetector with the specified DNS service.
	 *
	 * @param dnsService DNS service for lookups
	 */
	public DnsProviderDetector(DnsService dnsService) {
		this.dnsService = dnsService;
	}

	/**
	 * Detects the DNS provider for a given domain by analyzing its nameservers.
	 *
	 * @param domain The domain to analyze
	 * @return A DnsProviderInfo object containing provider information
	 */
	public DnsProviderInfo detectProvider(String domain) {
		logger.info("Detecting DNS provider for domain: {}", domain);

		try {
			// Look up NS records for the domain
			DnsQueryResults nsResults = dnsService.lookupTxtRecords(domain);
			List<String> nameservers = nsResults.records();

			if (nameservers.isEmpty()) {
				logger.warn("No nameservers found for domain: {}", domain);
				return new DnsProviderInfo("Unknown", nameservers);
			}

			// Match nameservers against known patterns
			for (Map.Entry<String, Pattern> provider : PROVIDER_PATTERNS.entrySet()) {
				for (String ns : nameservers) {
					if (provider.getValue().matcher(ns.toLowerCase()).matches()) {
						logger.info("Detected DNS provider for {}: {}", domain, provider.getKey());
						return new DnsProviderInfo(provider.getKey(), nameservers);
					}
				}
			}

			// If no matches, return unknown with the nameservers
			logger.info("Could not identify DNS provider for {}. Nameservers: {}", domain, nameservers);
			return new DnsProviderInfo("Unknown", nameservers);

		} catch (Exception e) {
			logger.error("Error detecting DNS provider for {}: {}", domain, e.getMessage());
			return new DnsProviderInfo("Error", List.of(e.getMessage()));
		}
	}

	/**
	 * Represents information about a domain's DNS provider.
	 */
	public static class DnsProviderInfo {
		private final String providerName;
		private final List<String> nameservers;

		public DnsProviderInfo(String providerName, List<String> nameservers) {
			this.providerName = providerName;
			this.nameservers = nameservers;
		}

		public String getProviderName() {
			return providerName;
		}

		public List<String> getNameservers() {
			return nameservers;
		}

		public boolean isAzureDns() {
			return "Azure DNS".equals(providerName);
		}

		public boolean isCloudflare() {
			return "Cloudflare".equals(providerName);
		}

		public boolean isAwsRoute53() {
			return "AWS Route 53".equals(providerName);
		}

		public boolean isGoDaddy() {
			return "GoDaddy".equals(providerName);
		}

		@Override
		public String toString() {
			return "DnsProviderInfo{" +
					"providerName='" + providerName + '\'' +
					", nameservers=" + nameservers +
					'}';
		}
	}
}