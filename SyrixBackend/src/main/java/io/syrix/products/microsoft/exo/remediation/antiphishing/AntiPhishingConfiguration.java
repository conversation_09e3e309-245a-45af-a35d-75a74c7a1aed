package io.syrix.products.microsoft.exo.remediation.antiphishing;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Configuration class for Anti-Phishing policy settings.
 * Provides a builder pattern for creating flexible anti-phishing configurations.
 */
public class AntiPhishingConfiguration {

	private final String policyName;
	private final List<String> protectedUsers;
	private final List<String> protectedDomains;
	private final String recipientDomain;
	private final boolean enableFirstContactSafetyTips;
	private final boolean enableSimilarUsersSafetyTips;
	private final boolean enableSimilarDomainsSafetyTips;
	private final boolean enableUnusualCharactersSafetyTips;
	private final boolean enableMailboxIntelligence;
	private final String mailboxIntelligenceAction;
	private final int phishThresholdLevel;

	/**
	 * Private constructor used by the builder.
	 */
	private AntiPhishingConfiguration(Builder builder) {
		this.policyName = builder.policyName;
		this.protectedUsers = builder.protectedUsers;
		this.protectedDomains = builder.protectedDomains;
		this.recipientDomain = builder.recipientDomain;
		this.enableFirstContactSafetyTips = builder.enableFirstContactSafetyTips;
		this.enableSimilarUsersSafetyTips = builder.enableSimilarUsersSafetyTips;
		this.enableSimilarDomainsSafetyTips = builder.enableSimilarDomainsSafetyTips;
		this.enableUnusualCharactersSafetyTips = builder.enableUnusualCharactersSafetyTips;
		this.enableMailboxIntelligence = builder.enableMailboxIntelligence;
		this.mailboxIntelligenceAction = builder.mailboxIntelligenceAction;
		this.phishThresholdLevel = builder.phishThresholdLevel;
	}

	/**
	 * Get the list of protected users.
	 * @return List of users protected from impersonation
	 */
	public List<String> getProtectedUsers() {
		return protectedUsers;
	}

	/**
	 * Get the list of protected domains.
	 * @return List of domains protected from impersonation
	 */
	public List<String> getProtectedDomains() {
		return protectedDomains;
	}

	/**
	 * Get the recipient domain to apply the policy to.
	 * @return Recipient domain
	 */
	public String getRecipientDomain() {
		return recipientDomain;
	}

	/**
	 * Check if first contact safety tips are enabled.
	 * @return true if enabled
	 */
	public boolean isEnableFirstContactSafetyTips() {
		return enableFirstContactSafetyTips;
	}

	/**
	 * Check if similar users safety tips are enabled.
	 * @return true if enabled
	 */
	public boolean isEnableSimilarUsersSafetyTips() {
		return enableSimilarUsersSafetyTips;
	}

	/**
	 * Check if similar domains safety tips are enabled.
	 * @return true if enabled
	 */
	public boolean isEnableSimilarDomainsSafetyTips() {
		return enableSimilarDomainsSafetyTips;
	}

	/**
	 * Check if unusual characters safety tips are enabled.
	 * @return true if enabled
	 */
	public boolean isEnableUnusualCharactersSafetyTips() {
		return enableUnusualCharactersSafetyTips;
	}

	/**
	 * Check if mailbox intelligence is enabled.
	 * @return true if enabled
	 */
	public boolean isEnableMailboxIntelligence() {
		return enableMailboxIntelligence;
	}

	/**
	 * Get the action to take for mailbox intelligence detections.
	 * @return Mailbox intelligence action
	 */
	public String getMailboxIntelligenceAction() {
		return mailboxIntelligenceAction;
	}

	/**
	 * Get the phish threshold level.
	 * @return Phish threshold level (1-4)
	 */
	public int getPhishThresholdLevel() {
		return phishThresholdLevel;
	}

	/**
	 * Get the policy name.
	 * @return Name of the anti-phishing policy
	 */
	public String getPolicyName() {
		return policyName;
	}

	/**
	 * Builder class for creating AntiPhishingConfiguration instances.
	 */
	public static class Builder {
		private List<String> protectedUsers = new ArrayList<>();
		private List<String> protectedDomains = new ArrayList<>();
		private String recipientDomain = null;
		private boolean enableFirstContactSafetyTips = true;
		private boolean enableSimilarUsersSafetyTips = true;
		private boolean enableSimilarDomainsSafetyTips = true;
		private boolean enableUnusualCharactersSafetyTips = true;
		private boolean enableMailboxIntelligence = true;
		private String mailboxIntelligenceAction = "Quarantine";
		private String policyName = "Auto-Impersonation-Protection";
		private int phishThresholdLevel = 2; // Aggressive

		/**
		 * Add protected users to the configuration.
		 * @param users Array of users in the format "DisplayName;EmailAddress"
		 * @return Builder instance
		 */
		public Builder withProtectedUsers(String... users) {
			this.protectedUsers.addAll(Arrays.asList(users));
			return this;
		}

		/**
		 * Add protected domains to the configuration.
		 * @param domains Array of domains to protect
		 * @return Builder instance
		 */
		public Builder withProtectedDomains(String... domains) {
			this.protectedDomains.addAll(Arrays.asList(domains));
			return this;
		}

		/**
		 * Set the recipient domain to apply the policy to.
		 * @param domain Domain to apply the policy to
		 * @return Builder instance
		 */
		public Builder withRecipientDomain(String domain) {
			this.recipientDomain = domain;
			return this;
		}

		/**
		 * Configure safety tips.
		 * @param firstContact Enable first contact safety tips
		 * @param similarUsers Enable similar users safety tips
		 * @param similarDomains Enable similar domains safety tips
		 * @param unusualChars Enable unusual characters safety tips
		 * @return Builder instance
		 */
		public Builder withSafetyTips(boolean firstContact, boolean similarUsers,
									  boolean similarDomains, boolean unusualChars) {
			this.enableFirstContactSafetyTips = firstContact;
			this.enableSimilarUsersSafetyTips = similarUsers;
			this.enableSimilarDomainsSafetyTips = similarDomains;
			this.enableUnusualCharactersSafetyTips = unusualChars;
			return this;
		}

		/**
		 * Configure mailbox intelligence.
		 * @param enabled Enable mailbox intelligence
		 * @param action Action to take (NoAction, MoveToJmf, Quarantine, etc.)
		 * @return Builder instance
		 */
		public Builder withMailboxIntelligence(boolean enabled, String action) {
			this.enableMailboxIntelligence = enabled;
			this.mailboxIntelligenceAction = action;
			return this;
		}

		/**
		 * Set the policy name.
		 * @param name Name for the anti-phishing policy
		 * @return Builder instance
		 */
		public Builder withPolicyName(String name) {
			this.policyName = name;
			return this;
		}

		/**
		 * Set the phish threshold level.
		 * @param level Threshold level (1=Standard, 2=Aggressive, 3=More aggressive, 4=Most aggressive)
		 * @return Builder instance
		 */
		public Builder withPhishThresholdLevel(int level) {
			if (level < 1 || level > 4) {
				throw new IllegalArgumentException("Phish threshold level must be between 1 and 4");
			}
			this.phishThresholdLevel = level;
			return this;
		}

		/**
		 * Build the configuration.
		 * @return AntiPhishingConfiguration instance
		 */
		public AntiPhishingConfiguration build() {
			return new AntiPhishingConfiguration(this);
		}
	}

	/**
	 * Create a default configuration for standard protection.
	 * @param organizationDomain The organization's primary domain
	 * @return AntiPhishingConfiguration with standard settings
	 */
	public static AntiPhishingConfiguration createStandardConfig(String organizationDomain) {
		return new Builder()
				.withProtectedDomains(organizationDomain)
				.withRecipientDomain(organizationDomain)
				.withPhishThresholdLevel(1) // Standard
				.build();
	}

	/**
	 * Create an enhanced configuration with high protection.
	 * @param organizationDomain The organization's primary domain
	 * @param executiveUsers Array of executive users to protect (format: "Name;<EMAIL>")
	 * @return AntiPhishingConfiguration with enhanced settings
	 */
	public static AntiPhishingConfiguration createEnhancedConfig(
			String organizationDomain, String... executiveUsers) {
		return new Builder()
				.withProtectedDomains(organizationDomain)
				.withProtectedUsers(executiveUsers)
				.withRecipientDomain(organizationDomain)
				.withPhishThresholdLevel(2) // Aggressive
				.build();
	}

	/**
	 * Create a strict configuration with maximum protection.
	 * @param organizationDomain The organization's primary domain
	 * @param executiveUsers Array of executive users to protect (format: "Name;<EMAIL>")
	 * @return AntiPhishingConfiguration with strict settings
	 */
	public static AntiPhishingConfiguration createStrictConfig(
			String organizationDomain, String... executiveUsers) {
		return new Builder()
				.withProtectedDomains(organizationDomain)
				.withProtectedUsers(executiveUsers)
				.withRecipientDomain(organizationDomain)
				.withPhishThresholdLevel(4) // Most aggressive
				.build();
	}
}