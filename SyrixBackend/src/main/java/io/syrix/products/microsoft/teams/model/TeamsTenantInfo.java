package io.syrix.products.microsoft.teams.model;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.products.microsoft.teams.serialization.CustomDateDeserializer;
import io.syrix.products.microsoft.teams.serialization.CustomDateSerializer;
import io.syrix.protocols.client.teams.powershell.command.types.TenantInfo;
import org.apache.commons.lang3.BooleanUtils;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@SuppressWarnings({"UnusedDeclaration"})
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TeamsTenantInfo {
    public String tenantId; //attention. we don't get this in the answer
    public Object announcementsDisabled;
    public Object companyTags;
    public Object country;
    public Object defaultPoolFqdn;
    public List<String> assignedPlan;
    public String city;
    public List<Object> companyPartnership;
    public String countryAbbreviation;
    @JsonIgnore
    public String createdDateTime;
    public boolean dirSyncEnabled;
    public String displayName;
    public Map<String, String> lastProvisionTimeStamps;
    public Map<String, String> lastPublishTimeStamps;
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    public OffsetDateTime lastSyncTimeStamp;
    public Object nameRecordingDisabled;
    public Object pools;
    @JsonIgnore
    public String objectId;
    public String postalCode;
    public String preferredLanguage;
    public List<String> provisionedPlan;
    @JsonIgnore
    public ServiceDiscovery serviceDiscovery;
    public String serviceInfo;
    public String serviceInstance;
    public List<String> sipDomain;
    public Object serviceNumberCount;
    public String stateOrProvince;
    public String street;
    public Object subscriberNumberCount;
    public SyncInLyncAdInfo syncInLyncAdInfo = new SyncInLyncAdInfo();
    public String teamsUpgradeEffectiveMode;
    public boolean teamsUpgradeNotificationsEnabled;
    public String teamsUpgradeOverridePolicy;
    public String teamsUpgradePolicyIsReadOnly;
    public String tnmAccountId;
    public List<String> verifiedDomains; //TODO Artur only for equals example
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    public OffsetDateTime whenCreated;
    public Object whenChanged;

    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class ServiceDiscovery {
        public Map<String, String> endpoints;
        public Map<String, String> headers;

        public ServiceDiscovery() {} //for serialization from json

        public ServiceDiscovery(TenantInfo.ServiceDiscovery serviceDiscovery) {
            this.endpoints = Map.copyOf(serviceDiscovery.endpoints);
            this.headers = Map.copyOf(serviceDiscovery.headers);
        }
    }

    public static class SyncInLyncAdInfo {
        public Object isSyncDisabledAtTenantCreation;
        public Object isUserSyncDisabled;
        public Object isUserSyncStateChanging;
        public Object stopSyncRevertCompleteTimestamp;
        public Object stopSyncRevertTimestamp;
        public Object stopSyncTimestamp;

        public SyncInLyncAdInfo() {} //for serialization from json

        public SyncInLyncAdInfo(TenantInfo.SyncInLyncAdInfo syncInLyncAdInfo) {
            if (syncInLyncAdInfo == null) {
                return;
            }
            this.isSyncDisabledAtTenantCreation = syncInLyncAdInfo.isSyncDisabledAtTenantCreation;
            this.isUserSyncDisabled = syncInLyncAdInfo.isUserSyncDisabled;
            this.isUserSyncStateChanging = syncInLyncAdInfo.isUserSyncStateChanging;
            this.stopSyncRevertCompleteTimestamp = syncInLyncAdInfo.stopSyncRevertCompleteTimestamp;
            this.stopSyncRevertTimestamp = syncInLyncAdInfo.stopSyncRevertTimestamp;
            this.stopSyncTimestamp = syncInLyncAdInfo.stopSyncTimestamp;
        }
    }

//    @JsonInclude(JsonInclude.Include.ALWAYS)
//    public static class VerifiedDomain {
//        public String name;
//        public String status;
//
//        @JsonValue
//        public String toJsonString() {
//            return "Microsoft.Teams.ConfigAPI.Cmdlets.Generated.Models.TenantVerifiedSipDomain"; //TODO Artur only for equals example
//        }
//
//        public VerifiedDomain() {} //VerifiedDomain
//        public VerifiedDomain(TenantInfo.VerifiedDomain verifiedDomain) {
//            if (verifiedDomain == null) {
//                return;
//            }
//            this.name = verifiedDomain.name;
//            this.status = verifiedDomain.status;
//        }
//    }

    public TeamsTenantInfo() {} //for serialization from json

    public TeamsTenantInfo(TenantInfo tenantInfo, String tenantId) {
        this.tenantId = tenantId;
        this.announcementsDisabled = tenantInfo.announcementsDisabled;
        this.companyTags = tenantInfo.companyTags;
        this.country = tenantInfo.country;
        this.defaultPoolFqdn = tenantInfo.defaultPoolFqdn;
        this.assignedPlan = Stream.ofNullable(tenantInfo.assignedPlans).flatMap(Collection::stream).map(plan ->plan.capability).toList();
        this.city = tenantInfo.city;
        this.companyPartnership = tenantInfo.companyPartnership;
        this.countryAbbreviation = tenantInfo.countryLetterCode;
        this.createdDateTime = tenantInfo.createdDateTime;
        this.dirSyncEnabled = BooleanUtils.isTrue(tenantInfo.dirSyncEnabled);
        this.displayName = tenantInfo.displayName;
        this.lastProvisionTimeStamps = Map.copyOf(tenantInfo.lastProvisionTimeStamps);
        this.lastPublishTimeStamps = Map.copyOf(tenantInfo.lastPublishTimeStamps);
        this.lastSyncTimeStamp = tenantInfo.lastSyncTimeStamp;
        this.nameRecordingDisabled = tenantInfo.nameRecordingDisabled;
        this.pools = tenantInfo.pools;
        this.objectId = tenantInfo.objectId;
        this.postalCode = tenantInfo.postalCode;
        this.preferredLanguage = tenantInfo.preferredLanguage;
        this.provisionedPlan = Stream.ofNullable(tenantInfo.provisionedPlans).flatMap(Collection::stream).map(plan -> plan.service).toList() ;
        this.serviceDiscovery = new ServiceDiscovery(tenantInfo.serviceDiscovery);
        this.serviceInfo = tenantInfo.serviceInfo;
        this.serviceInstance = tenantInfo.serviceInstance;
        this.sipDomain = List.copyOf(tenantInfo.sipDomains);
        this.serviceNumberCount = tenantInfo.serviceNumberCount;
        this.stateOrProvince = tenantInfo.state;
        this.street = tenantInfo.street;
        this.subscriberNumberCount = tenantInfo.subscriberNumberCount;
        this.syncInLyncAdInfo = new SyncInLyncAdInfo(tenantInfo.syncInLyncAdInfo);
        this.teamsUpgradeEffectiveMode = tenantInfo.teamsUpgradeEffectiveMode;
        this.teamsUpgradeNotificationsEnabled = tenantInfo.teamsUpgradeNotificationsEnabled;
        this.teamsUpgradeOverridePolicy = tenantInfo.teamsUpgradeOverridePolicy;
        this.teamsUpgradePolicyIsReadOnly = tenantInfo.teamsUpgradePolicyIsReadOnly;
        this.tnmAccountId = tenantInfo.tnmAccountId;
        this.verifiedDomains = Stream.ofNullable(tenantInfo.verifiedDomains).flatMap(Collection::stream).map(v -> "Microsoft.Teams.ConfigAPI.Cmdlets.Generated.Models.TenantVerifiedSipDomain").toList() ;
        this.whenCreated = tenantInfo.whenCreated;
        this.whenChanged = tenantInfo.whenChanged;
    }
}


