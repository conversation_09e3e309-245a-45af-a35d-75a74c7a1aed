package io.syrix.products.microsoft.exo.remediation.malwareFilter;

import io.syrix.datamodel.task.remediation.exchange.AttachmentFileTypeList;
import io.syrix.datamodel.task.remediation.exchange.AttachmentFilterAction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Configuration class for Malware Filter Policy and Rule settings,
 * based on New-MalwareFilterPolicy and New-MalwareFilterRule cmdlets.
 * Provides a single location to configure all options for both policy and rule.
 */
public class AttachmentFilterConfig {

	// Common properties
	private String name;

	// ======== MalwareFilterPolicy Properties ========
	// Basic policy properties
	private String adminDisplayName;
	private Boolean enableFileFilter;
	private AttachmentFilterAction action; // Enum for Quarantine/Delete actions
	private AttachmentFileTypeList filesTypeList;
	private Boolean zapEnabled;
	private String quarantineTag;

	// Notification properties
	private Boolean customNotifications;
	private String customAlertText;
	private String customExternalBody;
	private String customExternalSubject;
	private String customFromAddress;
	private String customFromName;
	private String customInternalBody;
	private String customInternalSubject;
	private Boolean enableExternalSenderAdminNotifications;
	private Boolean enableExternalSenderNotifications;
	private Boolean enableInternalSenderAdminNotifications;
	private Boolean enableInternalSenderNotifications;
	private String externalSenderAdminAddress;
	private String internalSenderAdminAddress;

	// ======== MalwareFilterRule Properties ========
	private String malwareFilterPolicyName;
	private String comments;
	private Boolean enabled;
	private List<String> exceptIfRecipientDomainIs;
	private List<String> exceptIfSentTo;
	private List<String> exceptIfSentToMemberOf;
	private Integer priority;
	private List<String> recipientDomainIs;
	private List<String> sentTo;
	private List<String> sentToMemberOf;

	/**
	 * Creates a default MalwareFilterConfig with minimal required settings
	 *
	 * @param name          The name for both policy and rule (if rule name not specified)
	 * @param action        The action to take on matches (AttachmentFilterAction.QUARANTINE or AttachmentFilterAction.DELETE)
	 * @param filesTypeList The list of file types to filter
	 */
	public AttachmentFilterConfig(String name, AttachmentFilterAction action, AttachmentFileTypeList filesTypeList) {
		this.name = name;
		this.action = action;
		this.filesTypeList = filesTypeList;

		// Set default values
		this.enableFileFilter = true;
		this.enabled = true;
		this.malwareFilterPolicyName = name;

		this.recipientDomainIs = List.of("*");
	}

	public Map<String, Object> buildPolicyParameters(boolean forUpdate) {
		Map<String, Object> parameters = new HashMap<>();
		addIdentityParameter(parameters, forUpdate);
		addBasicPolicyParameters(parameters);
		addNotificationParameters(parameters);
		return parameters;
	}

	private void addIdentityParameter(Map<String, Object> parameters, boolean forUpdate) {
		String key = forUpdate ? "Identity" : "Name";
		parameters.put(key, name);
	}

	private void addBasicPolicyParameters(Map<String, Object> parameters) {
		addIfNotNull(parameters, "AdminDisplayName", adminDisplayName);
		addIfNotNull(parameters, "EnableFileFilter", enableFileFilter);
		addIfNotNull(parameters, "FileTypeAction", action != null ? action.getAction() : null);
		addIfNotNull(parameters, "FileTypes", filesTypeList != null ? filesTypeList.getFileTypeList().toArray(new String[0]) : null);
		addIfNotNull(parameters, "ZapEnabled", zapEnabled);
		addIfNotNull(parameters, "QuarantineTag", quarantineTag);
	}

	private void addNotificationParameters(Map<String, Object> parameters) {
		addBasicNotifications(parameters);
		addCustomNotifications(parameters);
		addSenderNotifications(parameters);
		addAdminAddresses(parameters);
	}

	private void addBasicNotifications(Map<String, Object> parameters) {
		addIfNotNull(parameters, "CustomNotifications", customNotifications);
		addIfNotNull(parameters, "CustomAlertText", customAlertText);
	}

	private void addCustomNotifications(Map<String, Object> parameters) {
		addIfNotNull(parameters, "CustomExternalBody", customExternalBody);
		addIfNotNull(parameters, "CustomExternalSubject", customExternalSubject);
		addIfNotNull(parameters, "CustomFromAddress", customFromAddress);
		addIfNotNull(parameters, "CustomFromName", customFromName);
		addIfNotNull(parameters, "CustomInternalBody", customInternalBody);
		addIfNotNull(parameters, "CustomInternalSubject", customInternalSubject);
	}

	private void addSenderNotifications(Map<String, Object> parameters) {
		addIfNotNull(parameters, "EnableExternalSenderAdminNotifications", enableExternalSenderAdminNotifications);
		addIfNotNull(parameters, "EnableExternalSenderNotifications", enableExternalSenderNotifications);
		addIfNotNull(parameters, "EnableInternalSenderAdminNotifications", enableInternalSenderAdminNotifications);
		addIfNotNull(parameters, "EnableInternalSenderNotifications", enableInternalSenderNotifications);
	}

	private void addAdminAddresses(Map<String, Object> parameters) {
		addIfNotNull(parameters, "ExternalSenderAdminAddress", externalSenderAdminAddress);
		addIfNotNull(parameters, "InternalSenderAdminAddress", internalSenderAdminAddress);
	}

	public Map<String, Object> buildRuleParameters(String ruleName, boolean forUpdate) {
		Map<String, Object> parameters = new HashMap<>();
		addRuleIdentityParameter(parameters, ruleName, forUpdate);
		addMalwareFilterPolicyParameter(parameters);
		addBasicRuleParameters(parameters);
		addExceptionParameters(parameters);
		addPriorityParameter(parameters, forUpdate);
		addConditionParameters(parameters, forUpdate);
		return parameters;
	}

	private void addRuleIdentityParameter(Map<String, Object> parameters, String ruleName, boolean forUpdate) {
		String key = forUpdate ? "Identity" : "Name";
		parameters.put(key, ruleName);
	}

	private void addMalwareFilterPolicyParameter(Map<String, Object> parameters) {
		String policyName = malwareFilterPolicyName != null ? malwareFilterPolicyName : name;
		parameters.put("MalwareFilterPolicy", policyName);
	}

	private void addBasicRuleParameters(Map<String, Object> parameters) {
		addIfNotNull(parameters, "Comments", comments);
		addIfNotNull(parameters, "Enabled", enabled);
	}

	private void addExceptionParameters(Map<String, Object> parameters) {
		addListParameterIfNotEmpty(parameters, "ExceptIfRecipientDomainIs", exceptIfRecipientDomainIs);
		addListParameterIfNotEmpty(parameters, "ExceptIfSentTo", exceptIfSentTo);
		addListParameterIfNotEmpty(parameters, "ExceptIfSentToMemberOf", exceptIfSentToMemberOf);
	}

	private void addPriorityParameter(Map<String, Object> parameters, boolean forUpdate) {
		if (!forUpdate) {
			parameters.put("Priority", 0);
		} else if (priority != null) {
			parameters.put("Priority", priority);
		}
	}

	private void addConditionParameters(Map<String, Object> parameters, boolean forUpdate) {
		boolean hasCondition = addConditions(parameters);
		if (!hasCondition && !forUpdate) {
			parameters.put("RecipientDomainIs", new String[]{"*"});
		}
	}

	private boolean addConditions(Map<String, Object> parameters) {
		boolean hasCondition = false;
		hasCondition |= addListParameterIfNotEmpty(parameters, "RecipientDomainIs", recipientDomainIs);
		hasCondition |= addListParameterIfNotEmpty(parameters, "SentTo", sentTo);
		hasCondition |= addListParameterIfNotEmpty(parameters, "SentToMemberOf", sentToMemberOf);
		return hasCondition;
	}

	private void addIfNotNull(Map<String, Object> parameters, String key, Object value) {
		if (value != null) {
			parameters.put(key, value);
		}
	}

	private boolean addListParameterIfNotEmpty(Map<String, Object> parameters, String key, List<String> values) {
		if (values != null && !values.isEmpty()) {
			parameters.put(key, values.toArray(new String[0]));
			return true;
		}
		return false;
	}

	// ======== Getters and Setters ========

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAdminDisplayName() {
		return adminDisplayName;
	}

	public void setAdminDisplayName(String adminDisplayName) {
		this.adminDisplayName = adminDisplayName;
	}

	public Boolean getEnableFileFilter() {
		return enableFileFilter;
	}

	public void setEnableFileFilter(Boolean enableFileFilter) {
		this.enableFileFilter = enableFileFilter;
	}

	public AttachmentFilterAction getAction() {
		return action;
	}

	public void setAction(AttachmentFilterAction action) {
		this.action = action;
	}

	public AttachmentFileTypeList getFilesTypeList() {
		return filesTypeList;
	}

	public void setFilesTypeList(AttachmentFileTypeList filesTypeList) {
		this.filesTypeList = filesTypeList;
	}

	public Boolean getZapEnabled() {
		return zapEnabled;
	}

	public void setZapEnabled(Boolean zapEnabled) {
		this.zapEnabled = zapEnabled;
	}

	public String getQuarantineTag() {
		return quarantineTag;
	}

	public void setQuarantineTag(String quarantineTag) {
		this.quarantineTag = quarantineTag;
	}

	public Boolean getCustomNotifications() {
		return customNotifications;
	}

	public void setCustomNotifications(Boolean customNotifications) {
		this.customNotifications = customNotifications;
	}

	public String getCustomAlertText() {
		return customAlertText;
	}

	public void setCustomAlertText(String customAlertText) {
		this.customAlertText = customAlertText;
	}

	public String getCustomExternalBody() {
		return customExternalBody;
	}

	public void setCustomExternalBody(String customExternalBody) {
		this.customExternalBody = customExternalBody;
	}

	public String getCustomExternalSubject() {
		return customExternalSubject;
	}

	public void setCustomExternalSubject(String customExternalSubject) {
		this.customExternalSubject = customExternalSubject;
	}

	public String getCustomFromAddress() {
		return customFromAddress;
	}

	public void setCustomFromAddress(String customFromAddress) {
		this.customFromAddress = customFromAddress;
	}

	public String getCustomFromName() {
		return customFromName;
	}

	public void setCustomFromName(String customFromName) {
		this.customFromName = customFromName;
	}

	public String getCustomInternalBody() {
		return customInternalBody;
	}

	public void setCustomInternalBody(String customInternalBody) {
		this.customInternalBody = customInternalBody;
	}

	public String getCustomInternalSubject() {
		return customInternalSubject;
	}

	public void setCustomInternalSubject(String customInternalSubject) {
		this.customInternalSubject = customInternalSubject;
	}

	public Boolean getEnableExternalSenderAdminNotifications() {
		return enableExternalSenderAdminNotifications;
	}

	public void setEnableExternalSenderAdminNotifications(Boolean enableExternalSenderAdminNotifications) {
		this.enableExternalSenderAdminNotifications = enableExternalSenderAdminNotifications;
	}

	public Boolean getEnableExternalSenderNotifications() {
		return enableExternalSenderNotifications;
	}

	public void setEnableExternalSenderNotifications(Boolean enableExternalSenderNotifications) {
		this.enableExternalSenderNotifications = enableExternalSenderNotifications;
	}

	public Boolean getEnableInternalSenderAdminNotifications() {
		return enableInternalSenderAdminNotifications;
	}

	public void setEnableInternalSenderAdminNotifications(Boolean enableInternalSenderAdminNotifications) {
		this.enableInternalSenderAdminNotifications = enableInternalSenderAdminNotifications;
	}

	public Boolean getEnableInternalSenderNotifications() {
		return enableInternalSenderNotifications;
	}

	public void setEnableInternalSenderNotifications(Boolean enableInternalSenderNotifications) {
		this.enableInternalSenderNotifications = enableInternalSenderNotifications;
	}

	public String getExternalSenderAdminAddress() {
		return externalSenderAdminAddress;
	}

	public void setExternalSenderAdminAddress(String externalSenderAdminAddress) {
		this.externalSenderAdminAddress = externalSenderAdminAddress;
	}

	public String getInternalSenderAdminAddress() {
		return internalSenderAdminAddress;
	}

	public void setInternalSenderAdminAddress(String internalSenderAdminAddress) {
		this.internalSenderAdminAddress = internalSenderAdminAddress;
	}

	public String getMalwareFilterPolicyName() {
		return malwareFilterPolicyName;
	}

	public void setMalwareFilterPolicyName(String malwareFilterPolicyName) {
		this.malwareFilterPolicyName = malwareFilterPolicyName;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public List<String> getExceptIfRecipientDomainIs() {
		return exceptIfRecipientDomainIs;
	}

	public void setExceptIfRecipientDomainIs(List<String> exceptIfRecipientDomainIs) {
		this.exceptIfRecipientDomainIs = exceptIfRecipientDomainIs;
	}

	public List<String> getExceptIfSentTo() {
		return exceptIfSentTo;
	}

	public void setExceptIfSentTo(List<String> exceptIfSentTo) {
		this.exceptIfSentTo = exceptIfSentTo;
	}

	public List<String> getExceptIfSentToMemberOf() {
		return exceptIfSentToMemberOf;
	}

	public void setExceptIfSentToMemberOf(List<String> exceptIfSentToMemberOf) {
		this.exceptIfSentToMemberOf = exceptIfSentToMemberOf;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public List<String> getRecipientDomainIs() {
		return recipientDomainIs;
	}

	public void setRecipientDomainIs(List<String> recipientDomainIs) {
		this.recipientDomainIs = recipientDomainIs;
	}

	public List<String> getSentTo() {
		return sentTo;
	}

	public void setSentTo(List<String> sentTo) {
		this.sentTo = sentTo;
	}

	public List<String> getSentToMemberOf() {
		return sentToMemberOf;
	}

	public void setSentToMemberOf(List<String> sentToMemberOf) {
		this.sentToMemberOf = sentToMemberOf;
	}

	/**
	 * Factory method to create a default configuration for the "Quarantine" policy option
	 *
	 * @param fileTypeList The BASE list of file types to block
	 * @return A pre-configured AttachmentFilterConfig
	 */
	public static AttachmentFilterConfig createQuarantineConfig(AttachmentFileTypeList fileTypeList) {
		AttachmentFilterConfig config = new AttachmentFilterConfig(
				AttachmentFilterAction.QUARANTINE.getPolicyName(),
				AttachmentFilterAction.QUARANTINE,
				fileTypeList);

		config.setAdminDisplayName("MS.EXO.9.1v2 - Quarantine Policy");
		config.setEnabled(true);
		config.setPriority(1);
		return config;
	}

	/**
	 * Factory method to create a config with DELETE_ATTACHMENT_AND_USE_DEFAULT_ALERT action
	 *
	 * @param fileTypeList The file type list to block
	 * @return A pre-configured AttachmentFilterConfig
	 */
	public static AttachmentFilterConfig createDeleteAttachmentWithDefaultAlertConfig(AttachmentFileTypeList fileTypeList) {
		AttachmentFilterConfig config = new AttachmentFilterConfig(
				"Default Alert Malware Policy",
				AttachmentFilterAction.QUARANTINE,
				fileTypeList);

		config.setAdminDisplayName("MS.EXO.9.1v2 - Default Alert Policy");
		config.setEnabled(true);
		config.setPriority(2);
		return config;
	}

	/**
	 * Factory method to create a config with DELETE_ATTACHMENT_AND_USE_CUSTOM_ALERT action
	 *
	 * @param fileTypeList    The file type list to block
	 * @param customAlertText The custom alert text to use
	 * @return A pre-configured AttachmentFilterConfig
	 */
	public static AttachmentFilterConfig createDeleteAttachmentWithCustomAlertConfig(
			AttachmentFileTypeList fileTypeList, String customAlertText) {
		AttachmentFilterConfig config = new AttachmentFilterConfig(
				"Custom Alert Malware Policy",
				AttachmentFilterAction.QUARANTINE,
				fileTypeList);

		config.setAdminDisplayName("MS.EXO.9.1v2 - Custom Alert Policy");
		config.setEnabled(true);
		config.setPriority(3);
		config.setCustomAlertText(customAlertText);
		return config;
	}

	/**
	 * Factory method to create a default configuration for the "Delete" policy option
	 *
	 * @param fileTypeList The EXTENDED list of file types to block
	 * @return A pre-configured AttachmentFilterConfig
	 */
	public static AttachmentFilterConfig createRejectConfig(AttachmentFileTypeList fileTypeList) {
		AttachmentFilterConfig config = new AttachmentFilterConfig(
				AttachmentFilterAction.REJECT.getPolicyName(),
				AttachmentFilterAction.REJECT,
				fileTypeList);

		config.setAdminDisplayName("MS.EXO.9.1v2 - Delete Policy");
		config.setEnabled(true);
		config.setPriority(0);  // Higher priority than Quarantine
		return config;
	}
}