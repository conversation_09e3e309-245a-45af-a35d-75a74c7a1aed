package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import java.net.http.HttpRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.AAD.5.3v1")
public class EntraIDAdminApprovalWorkflowRemediator extends RemediatorBase {
	private final MicrosoftGraphClient graphClient;
	ObjectMapper objectMapper = new ObjectMapper();
	private static final String GROUPS_ENDPOINT = "/groups";
	private List<String> admins;
	private static final String UNIQUE_GROUP_NAME = "AdminConsentReviewers";

	public EntraIDAdminApprovalWorkflowRemediator(MicrosoftGraphClient graphClient,
												  List<String> admins) {
		this.graphClient = graphClient;
		this.admins = admins;
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for admin consent workflow using MS Graph REST API");

		return upsertReviewerGroup()
				.thenCompose(this::retrieveGroupDetailsIfNeeded)
				.thenCompose(this::updateAdminConsentPolicy)
				.exceptionally(ex -> {
					logger.error("Exception while configuring admin consent workflow", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	private CompletableFuture<JsonNode> upsertReviewerGroup() {
		ObjectNode groupPayload = createGroupPayload();
		try {
			String groupPayloadStr = objectMapper.writeValueAsString(groupPayload);
			HttpRequest.BodyPublisher groupBody = HttpRequest.BodyPublishers.ofString(groupPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.beta()
							.withMethod(HttpMethod.PATCH)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.addHeader("Prefer", "create-if-missing")
							.withBody(groupBody)
							.withEndpoint(GROUPS_ENDPOINT + "(uniqueName='" + UNIQUE_GROUP_NAME + "')")
							.build()
			);
		} catch (Exception e) {
			throw new GraphClientException("Failed to upsert reviewer group", e);
		}
	}

	private ObjectNode createGroupPayload() {
		ObjectNode groupPayload = objectMapper.createObjectNode();
		groupPayload.put(Constants.DISPLAY_NAME_FIELD, UNIQUE_GROUP_NAME);
		groupPayload.put("mailEnabled", false);
		groupPayload.put("securityEnabled", true);
		groupPayload.put("mailNickname", UNIQUE_GROUP_NAME);

		ArrayNode ownersArray = objectMapper.createArrayNode();
		ArrayNode membersArray = objectMapper.createArrayNode();
		admins.forEach(adminId -> {
			String userUrl = "https://graph.microsoft.com/beta/users/" + adminId;
			ownersArray.add(userUrl);
			membersArray.add(userUrl);
		});
		groupPayload.set("<EMAIL>", ownersArray);
		groupPayload.set("<EMAIL>", membersArray);

		return groupPayload;
	}

	private CompletableFuture<JsonNode> retrieveGroupDetailsIfNeeded(JsonNode groupResult) {
		if (groupResult == null || !groupResult.has(Constants.ID_FIELD)) {
			logger.info("Group already exists; retrieving group details...");
			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.beta()
							.withMethod(HttpMethod.GET)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withEndpoint(GROUPS_ENDPOINT + "(uniqueName='" + UNIQUE_GROUP_NAME + "')")
							.build()
			);
		} else {
			return CompletableFuture.completedFuture(groupResult);
		}
	}

	private CompletableFuture<JsonNode> updateAdminConsentPolicy(JsonNode finalGroupResult) {
		if (finalGroupResult == null || !finalGroupResult.has(Constants.ID_FIELD)) {
			String error = "Failed to retrieve group id for " + UNIQUE_GROUP_NAME;
			logger.error(error);
			return IPolicyRemediator.failed(getPolicyId(), error);
		}
		String groupId = finalGroupResult.get(Constants.ID_FIELD).asText();
		logger.info("Reviewer group available with id: {}", groupId);

		ObjectNode policyPayload = createPolicyPayload(groupId);
		try {
			String policyPayloadStr = objectMapper.writeValueAsString(policyPayload);
			HttpRequest.BodyPublisher policyBody = HttpRequest.BodyPublishers.ofString(policyPayloadStr);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.beta()
							.withMethod(HttpMethod.PUT)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(policyBody)
							.withEndpoint("/policies/adminConsentRequestPolicy")
							.build()
			).thenCompose(policyResult -> {
				if (policyResult != null && !policyResult.has(Constants.ERROR_FIELD)) {
					logger.info("Admin consent workflow configured successfully");
					return IPolicyRemediator.success(getPolicyId(),
							"Admin consent workflow enabled with reviewer group id: " + groupId);
				} else {
					String error = policyResult != null && policyResult.has(Constants.ERROR_FIELD)
							? policyResult.get(Constants.ERROR_FIELD).asText()
							: "Unknown error updating admin consent policy";
					logger.error("Failed to update admin consent policy: {}", error);
					return IPolicyRemediator.failed(getPolicyId(), error);
				}
			});
		} catch (Exception e) {
			throw new GraphClientException("Failed to update admin consent policy", e);
		}
	}

	private ObjectNode createPolicyPayload(String groupId) {
		ObjectNode policyPayload = objectMapper.createObjectNode();
		policyPayload.put("isEnabled", true);
		policyPayload.put("notifyReviewers", true);
		ArrayNode reviewersArray = objectMapper.createArrayNode();
		ObjectNode reviewerObject = objectMapper.createObjectNode();
		reviewerObject.put("query", "/groups/" + groupId + "/transitiveMembers");
		reviewerObject.put("queryType", "MicrosoftGraph");
		reviewersArray.add(reviewerObject);
		policyPayload.set("reviewers", reviewersArray);
		return policyPayload;
	}
}
