package io.syrix.products.microsoft.entra.accessreview;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;


import static io.syrix.protocols.utils.ProtocolConstants.PARAM_FILTER;

/**
 * Implements MS.AAD.8.5v1 for guest user access review automation
 */
@PolicyRemediator("MS.AAD.8.5v1")
public class EntraIDGuestAccessReviewRemediator extends RemediatorBase {
	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDGuestAccessReviewRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting guest user access review remediation (MS.AAD.8.5v1)");

		// First check if an access review for guest users already exists
		return checkExistingAccessReview()
				.thenCompose(exists -> {
					if (exists) {
						logger.info("Monthly guest user access review already exists");
						return IPolicyRemediator.requirementMet(getPolicyId(),
								"Monthly guest user access review is already configured");
					} else {
						logger.info("Creating new monthly guest user access review");
						return createGuestAccessReview();
					}
				})
				.exceptionally(ex -> {
					logger.error("Error during guest access review remediation", ex);
					return IPolicyRemediator.failed(getPolicyId(),
							"Failed to configure guest access review: " + ex.getMessage()).join();
				});
	}

	/**
	 * Checks if a monthly guest user access review already exists
	 */
	private CompletableFuture<Boolean> checkExistingAccessReview() {
		String filter = String.format("%s eq '%s'",
				AccessReviewConstants.DISPLAY_NAME,
				AccessReviewConstants.MONTHLY_GUEST_REVIEW_NAME);

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(AccessReviewConstants.ACCESS_REVIEWS_ENDPOINT)
						.addQueryParam(PARAM_FILTER, filter)
						.build()
		).thenApply(response -> {
			if (response.has(Constants.VALUE_FIELD) &&
					response.get(Constants.VALUE_FIELD).isArray() &&
					response.get(Constants.VALUE_FIELD).size() > 0) {
				return true;
			}
			return false;
		});
	}

	/**
	 * Creates a new monthly guest user access review
	 */
	private CompletableFuture<JsonNode> createGuestAccessReview() {
		try {
			ObjectNode requestBody = createAccessReviewRequestBody();
			String requestJson = objectMapper.writeValueAsString(requestBody);
			HttpRequest.BodyPublisher bodyPublisher = HttpRequest.BodyPublishers.ofString(requestJson);

			logger.debug("Creating access review with payload: {}", requestJson);

			return graphClient.makeGraphRequest(
					GraphRequest.builder()
							.withEndpoint(AccessReviewConstants.ACCESS_REVIEWS_ENDPOINT)
							.withMethod(HttpMethod.POST)
							.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
							.withBody(bodyPublisher)
							.build()
			).thenApply(response -> {
				logger.info("Successfully created monthly guest user access review");
				return IPolicyRemediator.success(getPolicyId(),
						"Monthly guest user access review has been configured successfully").join();
			});
		} catch (Exception e) {
			logger.error("Failed to create access review request", e);
			return IPolicyRemediator.failed(getPolicyId(),
					"Failed to create access review request: " + e.getMessage());
		}
	}

	/**
	 * Creates the request body for the access review creation
	 */
	private ObjectNode createAccessReviewRequestBody() {
		ObjectNode requestBody = objectMapper.createObjectNode();

		// Basic properties
		requestBody.put(AccessReviewConstants.DISPLAY_NAME, AccessReviewConstants.MONTHLY_GUEST_REVIEW_NAME);
		requestBody.put(AccessReviewConstants.DESCRIPTION, AccessReviewConstants.MONTHLY_GUEST_REVIEW_DESCRIPTION);

		// Scope - Microsoft 365 groups with guest users
		ObjectNode scope = objectMapper.createObjectNode();
		scope.put(AccessReviewConstants.QUERY, "/groups?$filter=groupTypes/any(t:t eq 'unified')");
		scope.put(AccessReviewConstants.QUERY_TYPE, AccessReviewConstants.MICROSOFT_GRAPH);
		requestBody.set(AccessReviewConstants.SCOPE, scope);

		requestBody.putNull(AccessReviewConstants.INSTANCE_ENUMERATION_SCOPE);

		// Settings
		ObjectNode settings = objectMapper.createObjectNode();
		settings.put(AccessReviewConstants.MAIL_NOTIFICATIONS_ENABLED, true);
		settings.put(AccessReviewConstants.REMINDERS_ENABLED, true);
		settings.put(AccessReviewConstants.JUSTIFICATION_REQUIRED, true);
		settings.put(AccessReviewConstants.AUTO_APPLY_DECISIONS_ENABLED, true);
		settings.put(AccessReviewConstants.ACTIVITY_DURATION, AccessReviewConstants.DEFAULT_ACTIVITY_DURATION);

		// Auto-review settings
		ObjectNode autoReview = objectMapper.createObjectNode();
		autoReview.put(AccessReviewConstants.NOT_REVIEWED_RESULT, AccessReviewConstants.DENY);
		settings.set(AccessReviewConstants.AUTO_REVIEW, autoReview);

		// Recurrence pattern - Monthly
		ObjectNode recurrence = objectMapper.createObjectNode();
		ObjectNode pattern = objectMapper.createObjectNode();
		pattern.put(AccessReviewConstants.TYPE, AccessReviewConstants.ABSOLUTE_MONTHLY);
		pattern.put(AccessReviewConstants.INTERVAL, 1);
		pattern.put(AccessReviewConstants.DAY_OF_MONTH, 1);
		recurrence.set(AccessReviewConstants.PATTERN, pattern);

		// Range - No end date
		ObjectNode range = objectMapper.createObjectNode();
		range.put(AccessReviewConstants.RANGE_TYPE, AccessReviewConstants.NO_END);
		range.put(AccessReviewConstants.START_DATE, getCurrentDate());
		recurrence.set(AccessReviewConstants.RANGE, range);
		settings.set(AccessReviewConstants.RECURRENCE, recurrence);

		// Apply actions - Remove access when denied
		ArrayNode applyActions = objectMapper.createArrayNode();
		ObjectNode action = objectMapper.createObjectNode();
		action.put(AccessReviewConstants.OPERATION, AccessReviewConstants.REMOVE);
		applyActions.add(action);
		settings.set(AccessReviewConstants.APPLY_ACTIONS, applyActions);

		requestBody.set(AccessReviewConstants.SETTINGS, settings);

		// Reviewed entity - External users only
		ObjectNode reviewedEntity = objectMapper.createObjectNode();
		reviewedEntity.put(Constants.ID_FIELD, AccessReviewConstants.EXTERNAL_USERS);
		requestBody.set(AccessReviewConstants.REVIEWED_ENTITY, reviewedEntity);

		// Reviewers - Security team
		ArrayNode reviewers = objectMapper.createArrayNode();
		ObjectNode reviewer = objectMapper.createObjectNode();
		reviewer.put(AccessReviewConstants.QUERY, AccessReviewConstants.SECURITY_TEAM_QUERY);
		reviewer.put(AccessReviewConstants.QUERY_TYPE, AccessReviewConstants.MICROSOFT_GRAPH);
		reviewers.add(reviewer);
		requestBody.set(AccessReviewConstants.REVIEWERS, reviewers);

		return requestBody;
	}

	/**
	 * Gets the current date in YYYY-MM-DD format
	 */
	private String getCurrentDate() {
		return LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
	}
}