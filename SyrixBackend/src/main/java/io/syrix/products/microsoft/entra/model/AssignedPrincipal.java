package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Represents an assigned user or group.
 */
public class AssignedPrincipal extends EnterpriseAppBaseModel {
	@JsonProperty("principalType")
	private String principalType;

	@JsonProperty("displayName")
	private String displayName;

	@JsonProperty("userPrincipalName")
	private String userPrincipalName;

	@JsonProperty("appRoleId")
	private String appRoleId;

	@JsonProperty("appRoleDisplayName")
	private String appRoleDisplayName;

	// Getters and setters
	public String getPrincipalType() { return principalType; }
	public void setPrincipalType(String principalType) { this.principalType = principalType; }
	public String getDisplayName() { return displayName; }
	public void setDisplayName(String displayName) { this.displayName = displayName; }
	public String getUserPrincipalName() { return userPrincipalName; }
	public void setUserPrincipalName(String userPrincipalName) { this.userPrincipalName = userPrincipalName; }
	public String getAppRoleId() { return appRoleId; }
	public void setAppRoleId(String appRoleId) { this.appRoleId = appRoleId; }
	public String getAppRoleDisplayName() { return appRoleDisplayName; }
	public void setAppRoleDisplayName(String appRoleDisplayName) { this.appRoleDisplayName = appRoleDisplayName; }
}