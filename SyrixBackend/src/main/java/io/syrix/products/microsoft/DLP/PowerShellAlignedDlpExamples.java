package io.syrix.products.microsoft.DLP;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import io.syrix.main.Configuration;
import io.syrix.products.microsoft.base.LicenseProcessor;
import io.syrix.products.microsoft.base.SkuProcessingResult;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.MSEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Examples demonstrating DLP configuration aligned with PowerShell cmdlets.
 */
public class PowerShellAlignedDlpExamples {
	public static final String SETTING = "Setting";
	public static final String VALUE = "Value";
	private static final Logger logger = LoggerFactory.getLogger(PowerShellAlignedDlpExamples.class);
	private static final ObjectMapper yamlMapper = new YAMLMapper();
	public static final String BLOCK = "Block";
	public static final String SITE_ADMIN = "SiteAdmin";

	/**
	 * Creates a basic SSN protection rule aligned with PowerShell Example 1.
	 *
	 * @return A DLP configuration that matches New-DlpComplianceRule -Name "SocialSecurityRule" -Policy "USFinancialChecks"
	 * -ContentContainsSensitiveInformation @{Name="U.S. Social Security Number (SSN)"} -BlockAccess $True
	 */
	public static DlpConfiguration createSsnProtectionRule() {
		// Create policy configuration
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("USFinancialChecks")
				.exchangeLocation("All")
				.sharePointLocation("All")
				.oneDriveLocation("All")
				.teamsLocation("All")
				.mode(DlpPolicyMode.Enable)
				.build();

		// Create sensitive information type for SSN
		SensitiveInfoTypeConfig ssnInfo = new SensitiveInfoTypeConfig("U.S. Social Security Number (SSN)");

		// Create the rule configuration
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("SocialSecurityRule")
				.sensitiveInfoTypes(List.of(ssnInfo))
				.blockAccess(true)
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	/**
	 * Creates a complex medical information rule aligned with PowerShell Example 2.
	 *
	 * @return A DLP configuration with complex medical information detection rules
	 */
	public static DlpConfiguration createMedicalInformationRule() {
		// Create policy configuration
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("Contoso Medical Checks")
				.exchangeLocation("All")
				.sharePointLocation("All")
				.oneDriveLocation("All")
				.build();

		// Create PII identifier group (using the advanced constructor with grouping)
		SensitiveInfoTypeConfig deaNumber = new SensitiveInfoTypeConfig(
				"Drug Enforcement Agency (DEA) Number",
				1,          // minCount
				-1,         // maxCount (unlimited)
				75,         // minConfidence
				100,        // maxConfidence
				null,       // labelsToMatch
				false,      // isNested
				"PII Identifiers", // groupName
				"Or"        // groupOperator
		);

		// Create Medical Terms group
		SensitiveInfoTypeConfig icd9 = new SensitiveInfoTypeConfig(
				"International Classification of Diseases (ICD-9-CM)",
				1,          // minCount
				-1,         // maxCount (unlimited)
				75,         // minConfidence
				100,        // maxConfidence
				null,       // labelsToMatch
				false,      // isNested
				"Medical Terms", // groupName
				"Or"        // groupOperator
		);

		SensitiveInfoTypeConfig icd10 = new SensitiveInfoTypeConfig(
				"International Classification of Diseases (ICD-10-CM)",
				1,          // minCount
				-1,         // maxCount (unlimited)
				75,         // minConfidence
				100,        // maxConfidence
				null,       // labelsToMatch
				false,      // isNested
				"Medical Terms", // groupName
				"Or"        // groupOperator
		);

		// Create the rule configuration
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("Contoso Medical Information")
				.sensitiveInfoTypes(Arrays.asList(deaNumber, icd9, icd10))
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	/**
	 * Creates a DLP rule with advanced rules from file, aligned with PowerShell Example 3.
	 *
	 * @param advancedRuleJson JSON string containing the advanced rule syntax
	 * @return A DLP configuration with advanced rule syntax
	 */
	public static DlpConfiguration createAdvancedRuleConfiguration(String advancedRuleJson) {
		// Create policy configuration
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("Contoso Policy 1")
				.exchangeLocation("All")
				.build();

		// Create the rule configuration with advanced rule syntax
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("Contoso Rule 1")
				.advancedRule(advancedRuleJson)
				.notifyUser(List.of(SITE_ADMIN))
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	/**
	 * Creates an endpoint DLP configuration that demonstrates comprehensive endpoint protection.
	 *
	 * @return An endpoint DLP configuration
	 */
	public static DlpConfiguration createEndpointDlpConfiguration() {
		// Create policy configuration
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("Endpoint Data Protection")
				.endpointDlpLocation("All")
				.build();

		// Create endpoint DLP restrictions
		List<Map<String, Object>> endpointRestrictions = new ArrayList<>();

		Map<String, Object> printRestriction = new HashMap<>();
		printRestriction.put(SETTING, "Print");
		printRestriction.put(VALUE, BLOCK);

		Map<String, Object> copyPasteRestriction = new HashMap<>();
		copyPasteRestriction.put(SETTING, "CopyPaste");
		copyPasteRestriction.put(VALUE, BLOCK);

		Map<String, Object> screenCaptureRestriction = new HashMap<>();
		screenCaptureRestriction.put(SETTING, "ScreenCapture");
		screenCaptureRestriction.put(VALUE, BLOCK);

		Map<String, Object> removableMediaRestriction = new HashMap<>();
		removableMediaRestriction.put(SETTING, "RemovableMedia");
		removableMediaRestriction.put(VALUE, BLOCK);

		Map<String, Object> networkShareRestriction = new HashMap<>();
		networkShareRestriction.put(SETTING, "NetworkShare");
		networkShareRestriction.put(VALUE, BLOCK);

		endpointRestrictions.add(printRestriction);
		endpointRestrictions.add(copyPasteRestriction);
		endpointRestrictions.add(screenCaptureRestriction);
		endpointRestrictions.add(removableMediaRestriction);
		endpointRestrictions.add(networkShareRestriction);

		// Create sensitive information types
		List<SensitiveInfoTypeConfig> sensitiveInfoTypes = new ArrayList<>();
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("Credit Card Number"));
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("U.S. Social Security Number (SSN)"));
		sensitiveInfoTypes.add(new SensitiveInfoTypeConfig("U.S. Individual Taxpayer Identification Number (ITIN)"));

		// Create notification configuration
		Map<String, String> notifyEndpointUser = new HashMap<>();
		notifyEndpointUser.put("CustomText", "This file contains sensitive information and cannot be shared externally.");
		notifyEndpointUser.put("CustomTitle", "Data Protection Alert");

		// Create the rule configuration
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("Endpoint Protection Rule")
				.sensitiveInfoTypes(sensitiveInfoTypes)
				.endpointDlpRestrictions(endpointRestrictions)
				.notifyUser(List.of(SITE_ADMIN))
				.notifyEndpointUser(notifyEndpointUser)
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	/**
	 * Creates a DLP configuration with various notification options.
	 *
	 * @return A DLP configuration with rich notification settings
	 */
	public static DlpConfiguration createNotificationRichConfiguration() {
		// Create policy configuration
		DlpPolicyConfig policyConfig = new DlpPolicyConfig.Builder("Notification-Rich Policy")
				.exchangeLocation("All")
				.sharePointLocation("All")
				.oneDriveLocation("All")
				.teamsLocation("All")
				.build();

		// Create translations for policy tips
		Map<String, String> translations = new HashMap<>();
		translations.put("en", "This message contains sensitive information");
		translations.put("es", "Este mensaje contiene información sensible");
		translations.put("fr", "Ce message contient des informations sensibles");
		translations.put("de", "Diese Nachricht enthält sensible Informationen");

		// Create rule configuration with rich notification settings
		DlpRuleConfig ruleConfig = new DlpRuleConfig.Builder("Notification-Rich Rule")
				.sensitiveInfoTypes(List.of(new SensitiveInfoTypeConfig("Credit Card Number")))
				.generateAlert(List.of("<EMAIL>", SITE_ADMIN))
				.generateIncidentReport(List.of(SITE_ADMIN))
				.incidentReportContent(List.of("All"))
				.notifyAllowOverride(List.of("FalsePositive", "WithJustification"))
				.notifyEmailCustomSenderDisplayName("Data Protection Officer")
				.notifyEmailCustomSubject("Action Required: Sensitive Information Detection")
				.notifyEmailCustomText("The system has detected sensitive information in a document " +
						"that requires your attention. Please review the content at %%ContentURL%% " +
						"and take appropriate action according to company policy.")
				.notifyPolicyTipCustomText("This item contains sensitive information that requires special handling.")
				.notifyPolicyTipCustomTextTranslations(translations)
				.notifyPolicyTipDisplayOption("Dialog")
				.notifyUser(List.of("Owner", "LastModifier", SITE_ADMIN))
				.notifyUserType("Email,PolicyTip")
				.blockAccess(true)
				.build();

		return new DlpConfiguration(policyConfig, ruleConfig);
	}

	/**
	 * Example showing how to use the DLP configuration with a remediator.
	 */
	public static void main(String[] args) {
		try {
			// Load configuration from YAML file
			if (args.length < 2) {
				logger.error("""
						Specify as arguments:
						args[0] - password for Certificate
						args[1] - path to Certificate""");
				System.exit(1);
			}

			String certPath = args[1];
			String certPass = args[0];

			String configPath = args.length > 2 ? args[2] : "./config.yml";
			Configuration config = yamlMapper.readValue(
					Paths.get(configPath).toFile(),
					Configuration.class
			);

			// Validate required fields
			if (config.credentials() == null ||
					config.credentials().clientId() == null ||
					config.credentials().clientSecret() == null) {
				throw new IllegalArgumentException("Missing required credentials in config file");
			}

			// Create a simple SSN protection policy (Example 1 from PowerShell doc)
			DlpConfiguration ssnConfig = createSsnProtectionRule();
			// Initialize Graph client
			MSEnvironment environment = MSEnvironment.valueOf(config.environment());
			logger.info("Initializing Microsoft Graph client for environment: {}", environment);
			MicrosoftGraphClient graphClient = MicrosoftGraphClient.builder()
					.withClientId(config.credentials().clientId())
					.withClientSecret(config.credentials().clientSecret())
					.withRefreshToken(config.credentials().refreshToken())
					.withEnvironment(environment)
					.withRequestTimeout(Duration.ofMinutes(5))
					.withMaxRetries(3)
					.withAppId(config.credentials().clientId())
					.withCertPath(certPath)
					.withCertPassword(certPass.toCharArray())
					.build();


			String domain = graphClient.getDomain().join();

			PowerShellClient powerShellClient = PowerShellClient.builder()
					.withAppId(config.credentials().clientId())
					.withTenantId(graphClient.getTenantId())
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withEndpointPath("/adminapi/beta")  // Use adminapi endpoint
					.withRequestTimeout(Duration.ofMinutes(5))
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.withDomain(domain)
					.build();

			LicenseProcessor licenseProcessor = new LicenseProcessor(graphClient);
			SkuProcessingResult skus = licenseProcessor.processSkus();
			// Create a remediation service using the configuration
			DlpRemediator remediator = new DlpRemediator(powerShellClient, skus, ssnConfig);

			// Start remediation to ensure configuration is applied
			CompletableFuture<JsonNode> dlp = remediator.remediate();
			dlp.thenApply(
					result -> {
						logger.info("DLP remediation completed: {}", result);
						return null;
					})
					.exceptionally(result -> {
						logger.info("DLP remediation failed: {}",result.getMessage(), result);
						return null;
			});

			dlp.join();
			// For demonstration purposes only
			logger.info("Created PowerShell-aligned DLP Configuration for: {}",
					ssnConfig.getPolicyConfig().getName());
			logger.info("Rule name: {}",ssnConfig.getRuleConfig().getName());
		} catch (Exception e) {
			logger.error("Error during configuration retrieval", e);
			System.exit(1);
		}
	}
}