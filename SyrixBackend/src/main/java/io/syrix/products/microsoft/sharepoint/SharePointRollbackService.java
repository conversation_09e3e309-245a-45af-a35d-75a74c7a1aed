package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.storage.Storage;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.domain.RollbackTask;
import io.syrix.datamodel.task.Task;
import io.syrix.main.Context;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.service.RollbackService;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.CONFIG_KEY_SPO_TENANT;

public class SharePointRollbackService implements RollbackService {
	private static final Logger logger = LoggerFactory.getLogger(SharePointRollbackService.class);
	private final Storage storage;
	private final ObjectMapper mapper;
	private final Context context;

	public SharePointRollbackService(Context context, Storage storage) {
		this.context = context;
		this.storage = storage;
		this.mapper = new ObjectMapper()
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE)
				.registerModule(new JavaTimeModule())
				.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
	}


	@Override
	public CompletableFuture<JsonNode> rollback(RollbackTask task) {
		try {
			JsonNode jsonConfig = loadConfig(task);

			SharePointTenantProperties tenant = readSharepointTenantProperties(jsonConfig);
			List<PolicyChangeResult> remediationResult = loadRemediationResult(task);

			List<PolicyChangeResult> resList = new ArrayList<>();
			try (PowerShellSharepointClient client = initClient()) {
				List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> iPolicyRollback = makeRollbacks(tenant, client, remediationResult);
				int ix = 0;
				for (Pair<IPolicyRemediatorRollback, PolicyChangeResult> pair : iPolicyRollback) {
					PolicyChangeResult res = runRollback(pair);
					res.executeOrder(ix++);
					resList.add(res);
				}
			}
			return CompletableFuture.completedFuture(mapper.valueToTree(resList));
		} catch (IOException e) {
			return CompletableFuture.failedFuture(e);
		}
	}

	private PolicyChangeResult runRollback(Pair<IPolicyRemediatorRollback, PolicyChangeResult> pair) {
		IPolicyRemediatorRollback remediatorRollback = pair.getKey();
		PolicyChangeResult policyChangeResult = pair.getValue();
		try {
			return remediatorRollback.rollback(policyChangeResult).get();
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", policyChangeResult.getPolicyId(), ex);
			return IPolicyRemediator.failed_(policyChangeResult.getPolicyId(), "Rollback the policy failed:" + ex.getMessage());
		}
	}

	private List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> makeRollbacks(SharePointTenantProperties tenant, PowerShellSharepointClient client, List<PolicyChangeResult> remediationResult) throws IOException {

		Map<String, PolicyChangeResult> changeResultMap = remediationResult.stream()
				.filter(res -> res.getResult() == RemediationResult.SUCCESS || res.getResult() == RemediationResult.PARTIAL_SUCCESS)
				.collect(Collectors.toMap(PolicyChangeResult::getPolicyId, res -> res));


		List<Pair<IPolicyRemediatorRollback, PolicyChangeResult>> remediators = new ArrayList<>();

		List<String> policyIds = changeResultMap.keySet().stream().sorted(Comparator.reverseOrder()).toList();

		for (String policyId : policyIds) {
			IPolicyRemediatorRollback rollback = PolicyRemediatorRegistry.getPolicyRemediator(policyId, client, tenant);
			remediators.add(Pair.of(rollback, changeResultMap.get(policyId)));
		}

		return remediators;
	}

	private JsonNode loadConfig(Task task) throws IOException {
		Path configPath = storage.loadConfigPath(task);
		return mapper.readTree(configPath.toFile());
	}

	private List<PolicyChangeResult> loadRemediationResult(Task task) throws IOException {
		Path resPath = storage.loadRemediationResult(task);

		JsonNode node = mapper.readTree(resPath.toFile());
		JsonNode spNode = node.get(ConfigurationServiceType.SHAREPOINT.getResultName());

		CollectionType collectionType = mapper.getTypeFactory().constructCollectionType(List.class, PolicyChangeResult.class);

		return mapper.treeToValue(spNode, collectionType);
	}


	private PowerShellSharepointClient initClient() {
		String domain;
		String site;
		try (MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context)) {
			domain = graphClient.getDomain().join();
			site = graphClient.getSite();
		}

		if (domain == null || site == null) {
			logger.error("Domain or site not init");
			throw new SyrixRuntimeException("Domain or site not init");
		}


		String adminDomain = site.replace(".sharepoint.com", "-admin.sharepoint.com").replace("https://", "");

		return ClientFactory.initSharepointPowerShellClient(context, domain, adminDomain);
	}

	private SharePointTenantProperties readSharepointTenantProperties(JsonNode jsonConfig) throws IOException {
		JsonNode policiesNode = jsonConfig.get(CONFIG_KEY_SPO_TENANT);

		if (policiesNode == null || !policiesNode.isArray()) {
			throw new IOException("Can not found key 'teams_tenant_info'");
		}

		CollectionType collectionType = mapper.getTypeFactory().constructCollectionType(List.class, SharePointTenantProperties.class);
		List<SharePointTenantProperties> tenantProperties = mapper.convertValue(policiesNode, collectionType);

		return tenantProperties.getFirst();
	}

}
