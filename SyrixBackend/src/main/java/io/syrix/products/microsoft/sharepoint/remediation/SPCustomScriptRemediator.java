package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;

import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.SHAREPOINT.4.2v1")
public class SPCustomScriptRemediator extends SPRemediatorBase {

    public SPCustomScriptRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
        super(client, tenant, spConfig);
    }
    // constructor for Rollback interface
    public SPCustomScriptRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        this(client, tenant, null);
    }


    @Override
    public CompletableFuture<JsonNode> remediate() {
        return IPolicyRemediator.success(getPolicyId(), "This is an outdated policy. It does not require correction. It will be removed soon.");
    }
}
