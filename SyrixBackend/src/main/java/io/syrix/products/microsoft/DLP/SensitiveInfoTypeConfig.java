package io.syrix.products.microsoft.DLP;

import java.util.List;

/**
 * Configuration for sensitive information types aligned with PowerShell format.
 * Supports both simple and complex formats for ContentContainsSensitiveInformation.
 */
public class SensitiveInfoTypeConfig {
	private final String name;
	private final Integer minCount;
	private final Integer maxCount;
	private final Integer minConfidence;
	private final Integer maxConfidence;
	private final List<String> labelsToMatch;
	private final Boolean isNested;
	private final String groupName;
	private final String groupOperator;

	/**
	 * Creates a new sensitive information type configuration with just the name.
	 */
	public SensitiveInfoTypeConfig(String name) {
		this(name, null, null, null, null);
	}

	/**
	 * Creates a sensitive information type with count and confidence settings.
	 */
	public SensitiveInfoTypeConfig(String name, Integer minCount, Integer maxCount,
								   Integer minConfidence, Integer maxConfidence) {
		this.name = name;
		this.minCount = minCount;
		this.maxCount = maxCount;
		this.minConfidence = minConfidence;
		this.maxConfidence = maxConfidence;
		this.labelsToMatch = null;
		this.isNested = false;
		this.groupName = null;
		this.groupOperator = null;
	}

	/**
	 * Full constructor for advanced configuration with grouping.
	 */
	public SensitiveInfoTypeConfig(String name, Integer minCount, Integer maxCount,
								   Integer minConfidence, Integer maxConfidence,
								   List<String> labelsToMatch, Boolean isNested,
								   String groupName, String groupOperator) {
		this.name = name;
		this.minCount = minCount;
		this.maxCount = maxCount;
		this.minConfidence = minConfidence;
		this.maxConfidence = maxConfidence;
		this.labelsToMatch = labelsToMatch;
		this.isNested = isNested;
		this.groupName = groupName;
		this.groupOperator = groupOperator;
	}

	public String getName() {
		return name;
	}

	public Integer getMinCount() {
		return minCount;
	}

	public Integer getMaxCount() {
		return maxCount;
	}

	public Integer getMinConfidence() {
		return minConfidence;
	}

	public Integer getMaxConfidence() {
		return maxConfidence;
	}

	public List<String> getLabelsToMatch() {
		return labelsToMatch;
	}

	public Boolean getIsNested() {
		return isNested;
	}

	public String getGroupName() {
		return groupName;
	}

	public String getGroupOperator() {
		return groupOperator;
	}
}
