package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharingLinkType;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.DEFAULT_SHARING_LINK_TYPE;

@PolicyRemediator("MS.SHAREPOINT.2.1v1")
public class SPDefaultSharingLinkRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
	private static final Logger log = LoggerFactory.getLogger(SPDefaultSharingLinkRemediator.class);

	public SPDefaultSharingLinkRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant, SharepointRemediationConfig spConfig) {
		super(client, tenant, spConfig);
	}
	// constructor for Rollback interface
	public SPDefaultSharingLinkRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
		this(client, tenant, null);
	}


	private CompletableFuture<PolicyChangeResult> runCommand(SharingLinkType value, SharingLinkType prevValue) {
		try {

			SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(tenant.objectIdentity, DEFAULT_SHARING_LINK_TYPE, value, prevValue);
			return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
					.thenApply(this::checkResult);
		} catch (Exception ex) {
			log.error("Run command for the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.failedFuture(ex);
		}
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		SharingLinkType prevValue = SharingLinkType.fromInt(tenant.defaultSharingLinkType);
		return runCommand(SharingLinkType.DIRECT, prevValue)
				.exceptionally(ex -> {
					log.error("Remediate the policy {} finished with exception", getPolicyId(), ex);
					return IPolicyRemediator.failed_(getPolicyId(), "Remediate the policy finished with exception:"+ex.getMessage());
				});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
			SharingLinkType sharingLinkType = SharingLinkType.valueOf(changeResult.getPrevValue().toString());
			SharingLinkType prevSharingLinkType = SharingLinkType.valueOf(changeResult.getNewValue().toString());
			return runCommand(sharingLinkType, prevSharingLinkType)
					.exceptionally(ex -> {
						log.error("Rollback the policy {} finished with exception", getPolicyId(), ex);
						return IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy failed:" + ex.getMessage());
					});
		} catch (Exception ex) {
			log.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy failed:"+ ex.getMessage()));
		}
	}
}
