package io.syrix.products.microsoft.sharepoint.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SiteProperties;
import io.syrix.products.microsoft.sharepoint.serialization.SharepointDateSerializer;
import io.syrix.products.microsoft.sharepoint.serialization.SharepointGuidSerializer;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Objects.requireNonNullElse;
// Assembly location: C:\Users\<USER>\OneDrive\Documents\PowerShell\Modules\Microsoft.Online.SharePoint.PowerShell\16.0.25715.12000\Microsoft.Online.SharePoint.PowerShell.dll
//namespace Microsoft.Online.SharePoint.PowerShell

@JsonIgnoreProperties(value = {"ArchiveStatus", "ArchivedBy", "ArchivedTime", "AuthenticationContextLimitedAccess",
        "AuthenticationContextName", "BlockDownloadPolicy", "BlockGuestsAsSiteAdmin",
        "BonusDiskQuota", "CapabilitySandboxedCodeActivation", "CreatedTime", "DefaultShareLinkRole", "DefaultShareLinkScope",
        "EnableAutoExpirationVersionTrim", "ExcludeBlockDownloadPolicySiteOwners", "ExcludeBlockDownloadSharePointGroups",
        "ExcludedBlockDownloadGroupIds", "ExpireVersionsAfterDays", "HidePeoplePreviewingFiles", "HidePeopleWhoHaveListsOpen",
        "InformationBarriersMode", "IsTeamsChannelConnected", "IsTeamsConnected",
        "LoopDefaultSharingLinkRole", "LoopDefaultSharingLinkScope", "MajorVersionLimit", "MediaTranscription",
        "OverrideBlockUserInfoVisibility", "OverrideSharingCapability", "ListsShowHeaderAndNavigation",
        "RequestFilesLinkEnabled", "RequestFilesLinkExpirationInDays", "RestrictContentOrgWideSearch", "RestrictedAccessControl",
        "RestrictedAccessControlGroups", "SharingLockDownCanBeCleared", "SharingLockDownEnabled", "TeamsChannelType",
"ReadOnlyForBlockDownloadPolicy","ReadOnlyForUnmanagedDevices"})
public class SharePointSite {
    public String createdTime; //DateTime
    @JsonSerialize(using = SharepointDateSerializer.class)
    public String lastContentModifiedDate; //DateTime
    public String status;
    public String archiveStatus;
    public String archivedBy;
    public String archivedTime; //DateTime
    public long bonusDiskQuota;
    public int resourceUsageCurrent;
    public int resourceUsageAverage;
    public long storageUsageCurrent;
    public String lockIssue;
    public int websCount;
    public int compatibilityLevel;
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String relatedGroupId; //Guid
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String groupId; //Guid
    public String url;
    @JsonProperty("LocaleId")
    public Integer lcid; //uint
    public String lockState;
    public String owner;
    public long storageQuota;
    public long storageQuotaWarningLevel;
    public int resourceQuota;
    public int resourceQuotaWarningLevel;
    public String template;
    public String title;
    public boolean allowSelfServiceUpgrade;
    public Integer denyAddAndCustomizePages; //DenyAddAndCustomizePagesStatus
    @JsonProperty("PWAEnabled")
    public Integer pwaEnabled; //PWAEnabledStatus
    public Integer sharingCapability; //SharingCapabilities
    public Integer siteDefinedSharingCapability; //SharingCapabilities
    @JsonProperty("SandboxedCodeActivationCapability")
    public Object capabilitySandboxedCodeActivation; //Microsoft.Online.SharePoint.TenantAdministration.SandboxedCodeActivationCapabilities
    public Integer disableCompanyWideSharingLinks; //CompanyWideSharingLinksPolicy
    public Integer disableAppViews; //AppViewsPolicy
    public Integer disableFlows; //FlowsPolicy
    public String authenticationContextName;
    public String storageQuotaType;
    public Integer restrictedToGeo; //RestrictedToRegion
    public boolean showPeoplePickerSuggestionsForGuestUsers;
    public Integer sharingDomainRestrictionMode; //SharingDomainRestrictionModes
    public String sharingAllowedDomainList;
    public String sharingBlockedDomainList;
    public Integer conditionalAccessPolicy; //SPOConditionalAccessPolicyType
    public boolean allowDownloadingNonWebViewableFiles;
    public Integer limitedAccessFileType; //SPOLimitedAccessFileType = SPOLimitedAccessFileType.WebPreviewableFiles;
    public boolean allowEditing;
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String sensitivityLabel;// = string.Empty;
    public Boolean disableSharingForNonOwnersStatus;
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String hubSiteId; //Guid
    public boolean isHubSite;
    public boolean commentsOnSitePagesDisabled;
    public boolean socialBarOnSitePagesDisabled;
    public Integer defaultSharingLinkType; //SharingLinkType
    public Integer defaultLinkPermission;//SharingPermissionType
    public boolean defaultLinkToExistingAccess;
    public int anonymousLinkExpirationInDays;
    public boolean overrideTenantAnonymousLinkExpirationPolicy;
    public int externalUserExpirationInDays;
    public boolean overrideTenantExternalUserExpirationPolicy;
    public boolean sharingLockDownEnabled;
    public boolean sharingLockDownCanBeCleared;
    public List<String> informationSegment;//Guid[]
    public String informationBarriersMode;
    public Integer blockDownloadLinksFileType; //BlockDownloadLinksFileTypes
    public Integer overrideBlockUserInfoVisibility;//SiteUserInfoVisibilityPolicyValue
    public boolean isTeamsConnected;
    public boolean isTeamsChannelConnected;
    public Integer teamsChannelType;//TeamsChannelTypeValue
    public Integer mediaTranscription; //MediaTranscriptionPolicyType
    public List<String> excludedBlockDownloadGroupIds;//Guid[]
    public boolean excludeBlockDownloadPolicySiteOwners;
    public boolean readOnlyForBlockDownloadPolicy;
    public List<String> excludeBlockDownloadSharePointGroups;
    public boolean blockDownloadPolicy;
    public Integer loopDefaultSharingLinkScope;//SharingScope
    public Integer loopDefaultSharingLinkRole;//SharingRole
    public boolean requestFilesLinkEnabled;
    public int requestFilesLinkExpirationInDays;
    public boolean overrideSharingCapability;
    public Integer defaultShareLinkScope;//SharingScope
    public Integer defaultShareLinkRole;//SharingRole
    public Integer blockGuestsAsSiteAdmin;//SharingState
    public boolean readOnlyForUnmanagedDevices;
    public boolean restrictedAccessControl;
    public boolean restrictContentOrgWideSearch;
    public boolean authenticationContextLimitedAccess;
    public List<String> restrictedAccessControlGroups; //Guid[]
    public Boolean restrictedContentDiscoveryforCopilotAndAgents;
    public boolean listsShowHeaderAndNavigation;
    public Boolean enableAutoExpirationVersionTrim;
    public Integer expireVersionsAfterDays;
    public Integer majorVersionLimit;
    public boolean hidePeoplePreviewingFiles;
    public boolean hidePeopleWhoHaveListsOpen;

    private final Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        additionalProperties.put(name, value);
    }


    public SharePointSite(SiteProperties siteProperties) {
        this.lcid = siteProperties.lcid;
        this.status = siteProperties.status;
        this.lastContentModifiedDate = siteProperties.lastContentModifiedDate;
        this.lockIssue = siteProperties.lockIssue;
        this.lockState = siteProperties.lockState;
        this.owner = siteProperties.owner;
        this.storageUsageCurrent = siteProperties.storageUsage;
        this.storageQuota = siteProperties.storageMaximumLevel;
        this.storageQuotaWarningLevel = siteProperties.storageWarningLevel;
        this.url = siteProperties.url;
        this.resourceUsageCurrent = siteProperties.currentResourceUsage;
        this.resourceUsageAverage = siteProperties.averageResourceUsage;
        this.resourceQuota = siteProperties.userCodeMaximumLevel;
        this.resourceQuotaWarningLevel = siteProperties.userCodeWarningLevel;
        this.template = siteProperties.template;
        this.title = siteProperties.title;
        this.websCount = siteProperties.websCount;
        this.allowSelfServiceUpgrade = siteProperties.allowSelfServiceUpgrade;
        this.compatibilityLevel = siteProperties.compatibilityLevel;
        this.denyAddAndCustomizePages = siteProperties.denyAddAndCustomizePages;
        this.hubSiteId = siteProperties.hubSiteId;
        this.isHubSite = siteProperties.isHubSite;
        this.pwaEnabled = siteProperties.pwaEnabled;
        this.sharingCapability = siteProperties.sharingCapability;
        this.capabilitySandboxedCodeActivation = siteProperties.sandboxedCodeActivationCapability;
        this.storageQuotaType = siteProperties.storageQuotaType;
        this.createdTime = requireNonNullElse(siteProperties.createdTime, "0"); //DateTime.MinValue
        this.restrictedToGeo = requireNonNullElse(siteProperties.restrictedToRegion, 0); //RestrictedToRegion.Unknown;
        this.disableCompanyWideSharingLinks = requireNonNullElse(siteProperties.disableCompanyWideSharingLinks, 0); //CompanyWideSharingLinksPolicy.Unknown
        this.showPeoplePickerSuggestionsForGuestUsers = requireNonNullElse(siteProperties.showPeoplePickerSuggestionsForGuestUsers, false);
        this.sharingDomainRestrictionMode = requireNonNullElse(siteProperties.sharingDomainRestrictionMode, 0); //SharingDomainRestrictionModes.None;
        this.sharingAllowedDomainList = requireNonNullElse(siteProperties.sharingAllowedDomainList, ""); //string.Empty;
        this.sharingBlockedDomainList = requireNonNullElse(siteProperties.sharingBlockedDomainList, "");  //string.Empty;
        this.siteDefinedSharingCapability = requireNonNullElse(siteProperties.siteDefinedSharingCapability, 0); //SharingCapabilities.Disabled
        this.disableAppViews = requireNonNullElse(siteProperties.disableAppViews, 0); //AppViewsPolicy.Unknown;
        this.disableFlows = requireNonNullElse(siteProperties.disableFlows, 0);//FlowsPolicy.Unknown;
        this.authenticationContextName = siteProperties.authenticationContextName;
        this.conditionalAccessPolicy = requireNonNullElse(siteProperties.conditionalAccessPolicy, 0); //SPOConditionalAccessPolicyType.AllowFullAccess;
        this.allowDownloadingNonWebViewableFiles = requireNonNullElse(siteProperties.allowDownloadingNonWebViewableFiles, true);
        this.limitedAccessFileType = requireNonNullElse(siteProperties.limitedAccessFileType, 0);  //SPOLimitedAccessFileType.WebPreviewableFiles;
        this.allowEditing = requireNonNullElse(siteProperties.allowEditing, true);
        this.sensitivityLabel = siteProperties.sensitivityLabel2;
        this.disableSharingForNonOwnersStatus = siteProperties.disableSharingForNonOwnersStatus;
        this.commentsOnSitePagesDisabled = requireNonNullElse(siteProperties.commentsOnSitePagesDisabled, false);
        this.socialBarOnSitePagesDisabled = requireNonNullElse(siteProperties.socialBarOnSitePagesDisabled, false);
        this.relatedGroupId = requireNonNullElse(siteProperties.relatedGroupId, "");//Guid.Empty;
        this.groupId = requireNonNullElse(siteProperties.groupId, ""); //Guid.Empty
        this.defaultSharingLinkType = requireNonNullElse(siteProperties.defaultSharingLinkType, 0); //SharingLinkType.None;
        this.defaultLinkPermission = requireNonNullElse(siteProperties.defaultLinkPermission, 0); //SharingPermissionType.None;
        this.defaultLinkToExistingAccess = requireNonNullElse(siteProperties.defaultLinkToExistingAccess, false);
        this.anonymousLinkExpirationInDays = requireNonNullElse(siteProperties.anonymousLinkExpirationInDays, 0);
        this.overrideTenantAnonymousLinkExpirationPolicy = requireNonNullElse(siteProperties.overrideTenantAnonymousLinkExpirationPolicy, false);
        this.externalUserExpirationInDays = requireNonNullElse(siteProperties.externalUserExpirationInDays, 0);
        this.overrideTenantExternalUserExpirationPolicy = requireNonNullElse(siteProperties.overrideTenantExternalUserExpirationPolicy, false);
        this.sharingLockDownEnabled = requireNonNullElse(siteProperties.sharingLockDownEnabled, false);
        this.sharingLockDownCanBeCleared = requireNonNullElse(siteProperties.sharingLockDownCanBeCleared, false);
        this.informationSegment = siteProperties.ibSegments; //requireNonNullElse(siteProperties.ibSegments, Collections.emptyList());
        this.informationBarriersMode = requireNonNullElse(siteProperties.ibMode, ""); //string.Empty;
        this.blockDownloadLinksFileType = requireNonNullElse(siteProperties.blockDownloadLinksFileType, 0); //BlockDownloadLinksFileTypes.WebPreviewableFiles;
        this.overrideBlockUserInfoVisibility = requireNonNullElse(siteProperties.overrideBlockUserInfoVisibility, 0); //SiteUserInfoVisibilityPolicyValue.OrganizationDefault;
        this.isTeamsConnected = requireNonNullElse(siteProperties.isTeamsConnected, false);
        this.isTeamsChannelConnected = requireNonNullElse(siteProperties.isTeamsChannelConnected, false);
        this.teamsChannelType = requireNonNullElse(siteProperties.teamsChannelType, 0);//TeamsChannelTypeValue.None;
        this.mediaTranscription = requireNonNullElse(siteProperties.mediaTranscription, 0); //MediaTranscriptionPolicyType.Enabled;
        this.blockDownloadPolicy = requireNonNullElse(siteProperties.blockDownloadPolicy, false);
        this.excludedBlockDownloadGroupIds = requireNonNullElse(siteProperties.excludedBlockDownloadGroupIds, Collections.emptyList()); //new Guid[0];
        this.loopDefaultSharingLinkScope = requireNonNullElse(siteProperties.loopDefaultSharingLinkScope, 0); //SharingScope.Uninitialized;
        this.loopDefaultSharingLinkRole = requireNonNullElse(siteProperties.loopDefaultSharingLinkRole, 0);
//        try
//        {
//            SharingRole sharingRole;
//            SharingUtility.ConvertRoleToSharingRole(siteProperties.loopDefaultSharingLinkRole, out sharingRole);
//            this.LoopDefaultSharingLinkRole = sharingRole;
//        }
//        catch
//        {
//            this.LoopDefaultSharingLinkRole = SharingRole.None;
//        }
        this.requestFilesLinkEnabled = requireNonNullElse(siteProperties.requestFilesLinkEnabled, false);
        this.requestFilesLinkExpirationInDays = requireNonNullElse(siteProperties.requestFilesLinkExpirationInDays, 0);
        this.overrideSharingCapability = requireNonNullElse(siteProperties.overrideSharingCapability, false);
        this.defaultShareLinkScope = requireNonNullElse(siteProperties.defaultShareLinkScope, 0); //SharingScope.Uninitialized;
        this.defaultShareLinkRole = requireNonNullElse(siteProperties.defaultShareLinkRole, 0);
//        try
//        {
//            SharingRole sharingRole;
//            SharingUtility.ConvertRoleToSharingRole(siteProperties.defaultShareLinkRole, out sharingRole);
//            this.DefaultShareLinkRole = sharingRole;
//        }
//        catch
//        {
//            this.DefaultShareLinkRole = SharingRole.None;
//        }

        this.readOnlyForUnmanagedDevices = requireNonNullElse(siteProperties.readOnlyForUnmanagedDevices, false);
        this.restrictContentOrgWideSearch = requireNonNullElse(siteProperties.restrictContentOrgWideSearch, false);
        this.restrictedAccessControl = requireNonNullElse(siteProperties.restrictedAccessControl, false);
        this.restrictedAccessControlGroups = requireNonNullElse(siteProperties.restrictedAccessControlGroups, Collections.emptyList()); //new Guid[0];
        this.restrictedContentDiscoveryforCopilotAndAgents = siteProperties.restrictedContentDiscoveryforCopilotAndAgents;
        this.excludeBlockDownloadPolicySiteOwners = requireNonNullElse(siteProperties.excludeBlockDownloadPolicySiteOwners, false);
        this.authenticationContextLimitedAccess = requireNonNullElse(siteProperties.authenticationContextLimitedAccess, false);
        this.readOnlyForBlockDownloadPolicy = requireNonNullElse(siteProperties.readOnlyForBlockDownloadPolicy, false);
        this.blockGuestsAsSiteAdmin = requireNonNullElse(siteProperties.blockGuestsAsSiteAdmin, 0); //SharingState.Unspecified;
        this.listsShowHeaderAndNavigation = requireNonNullElse(siteProperties.listsShowHeaderAndNavigation, false);
        this.excludeBlockDownloadSharePointGroups = requireNonNullElse(siteProperties.excludeBlockDownloadSharePointGroups, Collections.emptyList()); //new string[0];
        this.archiveStatus = requireNonNullElse(siteProperties.archiveStatus, ""); //string.Empty;
        this.archivedBy = requireNonNullElse(siteProperties.archivedBy, ""); //string.Empty;
        this.archivedTime = requireNonNullElse(siteProperties.archivedTime, "0"); //DateTime.MinValue;
        this.bonusDiskQuota = requireNonNullElse(siteProperties.bonusDiskQuota, 0L);
        this.enableAutoExpirationVersionTrim = siteProperties.enableAutoExpirationVersionTrim;

        if (BooleanUtils.isNotTrue(siteProperties.inheritVersionPolicyFromTenant) && BooleanUtils.isNotTrue(siteProperties.enableAutoExpirationVersionTrim)) {
            this.expireVersionsAfterDays = siteProperties.expireVersionsAfterDays;
        }


        if (BooleanUtils.isNotTrue(siteProperties.inheritVersionPolicyFromTenant) && BooleanUtils.isNotTrue(siteProperties.enableAutoExpirationVersionTrim)) {
            this.majorVersionLimit = siteProperties.majorVersionLimit;
        }

        this.hidePeoplePreviewingFiles = requireNonNullElse(siteProperties.hidePeoplePreviewingFiles, false);
        this.hidePeopleWhoHaveListsOpen = requireNonNullElse(siteProperties.hidePeopleWhoHaveListsOpen, false);

        this.additionalProperties.putAll(siteProperties.getAdditionalProperties());
    }


}
