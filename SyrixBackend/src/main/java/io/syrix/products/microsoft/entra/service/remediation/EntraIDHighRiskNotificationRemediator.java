package io.syrix.products.microsoft.entra.service.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;

import java.net.http.HttpRequest;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.products.microsoft.entra.service.EntraIDConstants.CA_POLICIES_ENDPOINT;

/**
 * Remediator for MS.AAD.2.2v1 that configures high-risk user notifications
 * using Microsoft Graph API and Conditional Access policies.
 */
@PolicyRemediator("MS.AAD.2.2v1")
public class EntraIDHighRiskNotificationRemediator extends RemediatorBase {
	private static final String POLICY_DISPLAY_NAME = "Alert on High-Risk Users";

	private final MicrosoftGraphClient graphClient;
	private final ObjectMapper objectMapper;

	public EntraIDHighRiskNotificationRemediator(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for high-risk users notification policy using Microsoft Graph API");

		return checkExistingPolicy()
				.thenCompose(exists -> {
					if (Boolean.TRUE.equals(exists)) {
						logger.info("High-risk user policy already exists. Skipping creation.");
						return CompletableFuture.completedFuture(
								IPolicyRemediator.success(getPolicyId(),
										"High-risk user policy already exists").join());
					}

					return createConditionalAccessPolicy();
				})
				.exceptionally(ex -> {
					logger.error("Exception while configuring high-risk users notification", ex);
					return IPolicyRemediator.failed(getPolicyId(), ex.getMessage()).join();
				});
	}

	/**
	 * Checks if a conditional access policy for high-risk users already exists.
	 *
	 * @return CompletableFuture<Boolean> - true if policy exists, false otherwise
	 */
	private CompletableFuture<Boolean> checkExistingPolicy() {
		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.addQueryParam(FILTER_PARAM, "displayName eq '" + POLICY_DISPLAY_NAME + "'")
						.build()
		).thenApply(response ->
			response.has(Constants.VALUE_FIELD) && response.get(Constants.VALUE_FIELD).size() > 0
		);
	}

	/**
	 * Creates a conditional access policy for high-risk users.
	 *
	 * @return CompletableFuture<JsonNode> - result of the policy creation
	 */
	private CompletableFuture<JsonNode> createConditionalAccessPolicy() {
		logger.info("Creating conditional access policy for high-risk users");

		ObjectNode policyBody = createPolicyRequestBody();

		String requestBodyStr;
		try {
			requestBodyStr = objectMapper.writeValueAsString(policyBody);
		} catch (Exception e) {
			logger.error("Failed to serialize request body", e);
			return CompletableFuture.failedFuture(
					new GraphClientException("Failed to serialize request body", e));
		}

		return graphClient.makeGraphRequest(
				GraphRequest.builder()
						.withMethod(HttpMethod.POST)
						.withEndpoint(CA_POLICIES_ENDPOINT)
						.addHeader(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
						.withBody(HttpRequest.BodyPublishers.ofString(requestBodyStr))
						.build()
		).thenCompose(response -> {
			if (response.has(Constants.ID_FIELD)) {
				logger.info("Successfully created conditional access policy with ID: {}",
						response.get(Constants.ID_FIELD).asText());
				return IPolicyRemediator.success(getPolicyId(),
						"High-risk users conditional access policy has been configured to alert administrators");
			} else {
				logger.error("Failed to create conditional access policy: {}", response);
				return IPolicyRemediator.failed(getPolicyId(),
						"Failed to create conditional access policy: " + response.toString());
			}
		});
	}

	/**
	 * Creates the request body for the conditional access policy.
	 *
	 * @return ObjectNode - JSON object with policy configuration
	 */
	private ObjectNode createPolicyRequestBody() {
		ObjectNode policy = objectMapper.createObjectNode();

		// Policy basics
		policy.put("displayName", POLICY_DISPLAY_NAME);
		policy.put("state", "enabled");

		// Conditions
		ObjectNode conditions = policy.putObject("conditions");

		// User risk levels
		conditions.set("userRiskLevels", objectMapper.valueToTree(Arrays.asList("high")));

		// Applications
		ObjectNode applications = conditions.putObject("applications");
		applications.set("includeApplications", objectMapper.valueToTree(Arrays.asList("All")));

		// Users
		ObjectNode users = conditions.putObject("users");
		users.set("includeUsers", objectMapper.valueToTree(Arrays.asList("All")));

		// Grant controls
		ObjectNode grantControls = policy.putObject("grantControls");
		grantControls.put("operator", "OR");
		grantControls.set("builtInControls", objectMapper.valueToTree(Arrays.asList("block")));

		// Session controls for alert notification
		ObjectNode sessionControls = policy.putObject("sessionControls");
		ObjectNode cloudAppSecurity = sessionControls.putObject("cloudAppSecurity");
		cloudAppSecurity.put("isEnabled", true);
		cloudAppSecurity.put("cloudAppSecurityType", "monitorOnly");

		return policy;
	}
}