package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.DEFENDER.2.3v1
 * Enables domain impersonation protection for important partner domains in both Standard and Strict preset policies.
 */
@PolicyRemediator("MS.DEFENDER.2.3v1")
public class DefenderPartnerDomainImpersonationProtectionRemediator extends AbstractDomainImpersonationProtectionRemediator {
	private final List<String> partnerDomains;

	public DefenderPartnerDomainImpersonationProtectionRemediator(PowerShellClient powershellClient, List<String> partnerDomains) {
		super(powershellClient);
		this.partnerDomains = partnerDomains != null ? partnerDomains : new ArrayList<>();
	}

	@Override
	protected CompletableFuture<JsonNode> validateBeforeRemediation() {
		if (partnerDomains.isEmpty()) {
			logger.warn("No partner domains provided for impersonation protection");
			return CompletableFuture.completedFuture(
					DefenderHelpers.createErrorResponse(getPolicyId(),
							"No partner domains provided for impersonation protection",
							objectMapper));
		}
		return null;
	}

	@Override
	protected int getDomainsCount() {
		return partnerDomains.size();
	}

	@Override
	protected String getRemediationType() {
		return "PartnerDomainImpersonation";
	}

	@Override
	protected boolean isDomainImpersonationAlreadyEnabled(JsonNode policySettings) {
		if (policySettings == null) {
			return false;
		}

		// Handle array result
		JsonNode policy = policySettings;
		if (policySettings.isArray() && policySettings.size() > 0) {
			policy = policySettings.get(0);
		}

		// Check if targeted domains protection is enabled
		boolean targetedDomainsProtectionEnabled = policy.path(ENABLE_TARGETED_DOMAINS_PROTECTION).asBoolean(false);

		// Check if organization domains protection remains enabled (should maintain this setting)
		boolean orgDomainsProtectionEnabled = policy.path(ENABLE_ORG_DOMAINS_PROTECTION).asBoolean(false);

		// Check impersonation state - should be "Automatic" or "Manual" but not "Off"
		String impersonationState = policy.path(IMPERSONATION_PROTECTION_STATE).asText("");
		boolean impersonationEnabled = !"Off".equalsIgnoreCase(impersonationState);

		// Check if all partner domains are included in the targeted domains list
		boolean allPartnerDomainsIncluded = false;
		if (policy.has(TARGETED_DOMAINS_TO_PROTECT) && policy.get(TARGETED_DOMAINS_TO_PROTECT).isArray()) {
			JsonNode configuredDomains = policy.get(TARGETED_DOMAINS_TO_PROTECT);
			List<String> configuredDomainsList = new ArrayList<>();

			for (JsonNode domain : configuredDomains) {
				configuredDomainsList.add(domain.asText().toLowerCase());
			}

			// Check if all partner domains are included in the configured domains
			allPartnerDomainsIncluded = true;
			for (String partnerDomain : partnerDomains) {
				if (!configuredDomainsList.contains(partnerDomain.toLowerCase())) {
					allPartnerDomainsIncluded = false;
					break;
				}
			}
		}

		// All settings need to be enabled for partner domain impersonation protection
		return targetedDomainsProtectionEnabled && orgDomainsProtectionEnabled &&
				impersonationEnabled && allPartnerDomainsIncluded;
	}

	@Override
	protected CompletableFuture<JsonNode> configureDomainImpersonationProtection(String policyName) {
		logger.info("Configuring partner domain impersonation protection for policy: {}", policyName);

		Map<String, Object> params = new HashMap<>();
		params.put(Constants.IDENTITY_FIELD, policyName);

		// Keep organization domains protection enabled
		params.put(ENABLE_ORG_DOMAINS_PROTECTION, true);

		// Enable targeted domains protection
		params.put(ENABLE_TARGETED_DOMAINS_PROTECTION, true);

		// Set the list of partner domains to protect
		params.put(TARGETED_DOMAINS_TO_PROTECT, partnerDomains);

		// Set ImpersonationProtectionState to "Automatic"
		params.put(IMPERSONATION_PROTECTION_STATE, "Automatic");

		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.SET_ANTIPHISHPOLICY_COMMAND,
						params
				));
	}
}