package io.syrix.products.microsoft.entra.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.client.MicrosoftGraphClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnterpriseAppEnricher {
	private final MicrosoftGraphClient graphClient;

	private final Map<String, JsonNode> resourceCache = new HashMap<>();
	private static final Logger logger = LoggerFactory.getLogger(EnterpriseAppEnricher.class);

	public EnterpriseAppEnricher(MicrosoftGraphClient graphClient) {
		this.graphClient = graphClient;
	}

	public void enrich(EnterpriseApplication application) {
		enrichPermissionDisplayNames(application);

		if (CollectionUtils.isNotEmpty(application.getPermissions())) {
			enrichApplicationPermissions(application.getPermissions());
		}

		if (CollectionUtils.isNotEmpty(application.getDelegatedPermissions())) {
			enrichDelegatedPermissions(application.getDelegatedPermissions());
		}
	}

	private JsonNode fetchResource(String resourceId) {
		return graphClient.getAzureServicePrincipals(null, "/"+resourceId).join();
	}

	private void enrichPermissionDisplayNames(EnterpriseApplication app) {
		try {
			if (app.getBasic() == null) {
				logger.warn("Application has no basic details");
				return;
			}

			// Create lookup maps for app roles
			Map<String, AppRole> appRoleMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(app.getBasic().getAppRoles())) {
				for (AppRole role : app.getBasic().getAppRoles()) {
					if (role.getId() != null) {
						appRoleMap.put(role.getId(), role);
					}
				}
			}

			// Enrich application permissions
			enrichAppPermissions(app, appRoleMap);

			// Enrich assigned users' app roles
			enrichAssignedUsers(app, appRoleMap);

		} catch (Exception e) {
			logger.error("Failed to enrich permission display names", e);
		}
	}

	private void enrichAppPermissions(EnterpriseApplication app, Map<String, AppRole> appRoleMap) {
		if (CollectionUtils.isNotEmpty(app.getPermissions())) {
			for (ApplicationPermission permission : app.getPermissions()) {
				try {
					if (permission.getAppRoleId() != null) {
						AppRole role = appRoleMap.get(permission.getAppRoleId());
						if (role != null && role.getDisplayName() != null) {
							permission.setAppRoleDisplayName(role.getDisplayName());
						}
					}
				} catch (Exception e) {
					logger.error("Failed to enrich application permission", e);
				}
			}
		}
	}

	private void enrichAssignedUsers(EnterpriseApplication app, Map<String, AppRole> appRoleMap) {
		if (CollectionUtils.isNotEmpty(app.getAssignedUsers())) {
			for (AssignedPrincipal principal : app.getAssignedUsers()) {
				try {
					if (principal.getAppRoleId() != null) {
						AppRole role = appRoleMap.get(principal.getAppRoleId());
						if (role != null && role.getDisplayName() != null) {
							principal.setAppRoleDisplayName(role.getDisplayName());
						}
					}
				} catch (Exception e) {
					logger.error("Failed to enrich assigned user", e);
				}
			}
		}
	}

	private JsonNode getResource(String resourceId) {
		return resourceCache.computeIfAbsent(resourceId, id -> {
			JsonNode fetched = fetchResource(id);
			if (fetched == null) {
				throw new IllegalStateException("Failed to fetch resource for id: " + id);
			}
			return fetched;
		});
	}

	private void enrichApplicationPermissions(List<ApplicationPermission> permissionsList) {
		for (ApplicationPermission permission : permissionsList) {
			JsonNode resource = getResource(permission.getResourceId());
			if (resource == null) continue;
			// Set resource display name
			permission.setResourceDisplayName(resource.path(Constants.DISPLAY_NAME_FIELD).asText());

			// Find matching app role
			JsonNode appRoles = resource.path("appRoles");
			if (appRoles.isArray()) {
				for (JsonNode role : appRoles) {
					if (role.path(Constants.ID_FIELD).asText().equals(permission.getAppRoleId())) {
						permission.setAppRoleDisplayName(role.path(Constants.DISPLAY_NAME_FIELD).asText());
						permission.setAppRoleDescription(role.path(Constants.DESCRIPTION_FIELD).asText());
						permission.setAppRoleValue(role.path(Constants.VALUE_FIELD).asText());
						break;
					}
				}
			}
		}
	}

	private void enrichDelegatedPermissions(List<DelegatedPermission> permissionsList) {
		for (DelegatedPermission permission : permissionsList) {
			JsonNode resource = getResource(permission.getResourceId());
			if (resource == null) continue;
			// Set resource display name
			permission.setResourceDisplayName(resource.path(Constants.DISPLAY_NAME_FIELD).asText());
		}
	}
}