package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.products.microsoft.exo.remediation.malware.MalwareAttachmentFilterPolicyRemediator;
import io.syrix.products.microsoft.exo.remediation.malware.MalwareAttachmentFilterRuleRemediator;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for Exchange malware attachment filter settings.
 * Combines policy and rule remediation operations to ensure both components
 * are properly configured.
 */
@PolicyRemediator("MS.EXO.9.1v2")
public class ExchangeMalwareAttachmentFilterRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	private final MalwareAttachmentFilterPolicyRemediator policyRemediator;
	private final MalwareAttachmentFilterRuleRemediator ruleRemediator;

	public ExchangeMalwareAttachmentFilterRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient, ObjectNode configNode, ExchangeRemediationContext remediationContext, ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
		policyRemediator = new MalwareAttachmentFilterPolicyRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig, getPolicyId());
		ruleRemediator = new MalwareAttachmentFilterRuleRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig, getPolicyId());
	}

	public ExchangeMalwareAttachmentFilterRemediator(MicrosoftGraphClient graphClient, PowerShellClient exchangeClient) {
		super(graphClient, exchangeClient, null, null, null);
		policyRemediator = new MalwareAttachmentFilterPolicyRemediator(graphClient, exchangeClient, getPolicyId());
		ruleRemediator = new MalwareAttachmentFilterRuleRemediator(graphClient, exchangeClient, getPolicyId());
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting malware attachment filter remediation for both policy and rule components");

		return policyRemediator.remediate_()
			.thenCompose(policyResult -> {
				logger.info("Policy remediation completed with status: {}", policyResult.getResult());

				if (policyResult.getResult() == RemediationResult.SUCCESS || 
					policyResult.getResult() == RemediationResult.REQUIREMENT_MET) {
					return ruleRemediator.remediate_().thenApply(ruleResult -> {
						logger.info("Rule remediation completed with status: {}", ruleResult.getResult());
						return combineResults(policyResult, ruleResult);
					});
				} else {
					return CompletableFuture.completedFuture(policyResult);
				}
			});
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting malware attachment filter rollback for both policy and rule components");

		if (fixResult == null || fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
			logger.warn("No changes to rollback - empty fix result");
			return CompletableFuture.completedFuture(IPolicyRemediator.unknown_(getPolicyId(), "No changes to rollback"));
		}

		return ruleRemediator.rollback(fixResult)
			.thenCompose(ruleRollbackResult -> {
				logger.info("Rule rollback completed with status: {}", ruleRollbackResult.getResult());
				
				// Затем выполняем откат политики
				return policyRemediator.rollback(fixResult)
					.thenApply(policyRollbackResult -> {
						logger.info("Policy rollback completed with status: {}", policyRollbackResult.getResult());
						
						// Объединяем результаты откатов
						return combineResults(policyRollbackResult, ruleRollbackResult);
					});
			});
	}

	/**
	 * Combines the results from policy and rule remediation operations.
	 * 
	 * @param policyResult The result from policy remediation
	 * @param ruleResult The result from rule remediation
	 * @return A combined result representing both operations
	 */
	private PolicyChangeResult combineResults(PolicyChangeResult policyResult, PolicyChangeResult ruleResult) {
		RemediationResult combinedResult = determineCombinedResult(policyResult.getResult(), ruleResult.getResult());

		String description = "Policy: " + policyResult.getDesc() + "; " +
							 "Rule: " + ruleResult.getDesc();
		
		// Объединяем списки изменений
		List<ParameterChangeResult> combinedChanges = new ArrayList<>();
		
		if (policyResult.getChanges() != null) {
			combinedChanges.addAll(policyResult.getChanges());
		}
		
		if (ruleResult.getChanges() != null) {
			combinedChanges.addAll(ruleResult.getChanges());
		}
		
		return new PolicyChangeResult()
			.policyId(getPolicyId())
			.result(combinedResult)
			.desc(description)
			.changes(combinedChanges);
	}

	/**
	 * Determines the overall result based on individual policy and rule results.
	 * 
	 * @param policyResult Result of policy remediation
	 * @param ruleResult Result of rule remediation
	 * @return The combined result status
	 */
	private RemediationResult determineCombinedResult(RemediationResult policyResult, RemediationResult ruleResult) {
		if ((policyResult == RemediationResult.FAILED && (ruleResult == RemediationResult.SUCCESS || ruleResult == RemediationResult.REQUIREMENT_MET)) ||
			((policyResult == RemediationResult.SUCCESS || policyResult == RemediationResult.REQUIREMENT_MET) && ruleResult == RemediationResult.FAILED)) {
			return RemediationResult.PARTIAL_SUCCESS;
		}

		if (policyResult == RemediationResult.FAILED && ruleResult == RemediationResult.FAILED) {
			return RemediationResult.FAILED;
		}

		if (policyResult == RemediationResult.SUCCESS && ruleResult == RemediationResult.SUCCESS) {
			return RemediationResult.SUCCESS;
		}

		if (policyResult == RemediationResult.REQUIREMENT_MET && ruleResult == RemediationResult.REQUIREMENT_MET) {
			return RemediationResult.REQUIREMENT_MET;
		}

		if ((policyResult == RemediationResult.SUCCESS && ruleResult == RemediationResult.REQUIREMENT_MET) ||
			(policyResult == RemediationResult.REQUIREMENT_MET && ruleResult == RemediationResult.SUCCESS)) {
			return RemediationResult.SUCCESS;
		}

		if ((policyResult == RemediationResult.SUCCESS || policyResult == RemediationResult.REQUIREMENT_MET) &&
			ruleResult == RemediationResult.UNKNOWN) {
			return RemediationResult.PARTIAL_SUCCESS;
		}
		
		if (policyResult == RemediationResult.UNKNOWN &&
			(ruleResult == RemediationResult.SUCCESS || ruleResult == RemediationResult.REQUIREMENT_MET)) {
			return RemediationResult.PARTIAL_SUCCESS;
		}

		return RemediationResult.UNKNOWN;
	}
}