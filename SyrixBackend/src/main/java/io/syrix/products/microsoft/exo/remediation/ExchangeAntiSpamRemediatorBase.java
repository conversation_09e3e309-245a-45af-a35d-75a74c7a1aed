package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static io.syrix.products.microsoft.exo.ExoConstants.IDENTITY;

/**
 * Abstract remediator for Exchange Online anti-spam policies according to the CISA M365 Secure Configuration Baseline.
 * Derived classes implement specific security controls:
 * - MS.EXO.14.1v2: Enable spam filtering
 * - MS.EXO.14.2v1: Move spam and high confidence spam to junk or quarantine
 * - MS.EXO.14.3v1: Prevent allowed domains from being added to inbound anti-spam policies
 */
public abstract class ExchangeAntiSpamRemediatorBase extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
	public static final String ENABLED = "Enabled";
	public static final String ERROR = "Error";

	public static final String SPAM_ACTION = "SpamAction";
	public static final String QUARANTINE = "Quarantine";
	public static final String MOVE_TO_JMF = "MoveToJmf";
	public static final String JUNK = "Junk";


	public static final String HIGH_CONFIDENCE_SPAM_ACTION = "HighConfidenceSpamAction";
	public static final String ALLOWED_SENDER_DOMAINS = "AllowedSenderDomains";

	// Configure anti-spam policy settings according to security baseline
	protected abstract Map<String, Object> getAntispamPolicy();

	/**
	 * Constructs a new ExchangeAntiSpamRemediator.
	 *
	 * @param graphClient        Microsoft Graph API client
	 * @param exchangeClient     PowerShell client for Exchange Online
	 * @param configNode         Configuration node
	 * @param remediationContext Exchange remediation context
	 * @param remediationConfig  Remediation configuration
	 */
	public ExchangeAntiSpamRemediatorBase(MicrosoftGraphClient graphClient,
										  PowerShellClient exchangeClient,
										  ObjectNode configNode,
										  ExchangeRemediationContext remediationContext,
										  ExchangeRemediationConfig remediationConfig) {
		super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
	}

	public ExchangeAntiSpamRemediatorBase(MicrosoftGraphClient graphClient,
										  PowerShellClient exchangeClient) {
		super(graphClient, exchangeClient, null, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		logger.info("Starting remediation for anti-spam policies");
		List<ContentFilterPolicy> contentFilterPolicies = loadContentFilterPolicies();
		if (contentFilterPolicies.isEmpty()) {
			logger.warn("No content filter policies found in configuration");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_CONTENT_FILTER_POLICY));
		}
		Optional<ContentFilterPolicy> defaultPolicy = contentFilterPolicies.stream()
				.filter(policy -> "Default".equalsIgnoreCase(policy.identity))
				.findFirst();
		if (defaultPolicy.isEmpty()) {
			logger.warn("Default content filter policy not found");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Default content filter policy not found"));
		}
		if (isValidPolicy(defaultPolicy.get())) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId(), "Default content filter policy is requirement met"));
		}
		return configureAntiSpamPolicy(defaultPolicy.get());
	}

	/**
	 * Implements rollback functionality for anti-spam policy remediation.
	 * This method reverts the changes made by the remediation process,
	 * restoring the original anti-spam policy configuration.
	 *
	 * @param fixResult The result of the fix operation that needs to be rolled back
	 * @return A CompletableFuture containing the rollback operation result
	 */
	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		logger.info("Starting rollback for anti-spam policy remediation (MS.EXO.14.1v2, MS.EXO.14.2v1, MS.EXO.14.3v1)");
		
		// Check if the remediation was successful and has changes to rollback
		if (fixResult == null || fixResult.getResult() != RemediationResult.SUCCESS || fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
			logger.warn("No successful remediation to roll back or no changes were made");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No successful remediation to roll back"));
		}
		
		// Find the parameter change for HostedContentFilterPolicy
		ParameterChangeResult policyChange = fixResult.getChanges().getFirst();

		// Get the previous policy values to restore
		@SuppressWarnings("unchecked")
		Map<String, Object> prevParameters = (Map<String, Object>) policyChange.getPrevValue();
		if (prevParameters == null || prevParameters.isEmpty()) {
			logger.warn("No previous policy values found to restore");
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No previous policy values found to restore"));
		}
		
		logger.info("Rolling back to previous anti-spam policy configuration");
		
		// Create change result for rollback operation
		ParameterChangeResult rollbackChangeResult = new ParameterChangeResult()
				.parameter("RollbackHostedContentFilterPolicy")
				.prevValue(policyChange.getNewValue())
				.newValue(prevParameters);
				
		// Execute PowerShell command to restore previous settings
		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-HostedContentFilterPolicy", prevParameters))
				.thenApply(result -> {
					logger.info("Successfully rolled back anti-spam policy to previous settings");
					rollbackChangeResult.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back anti-spam policy to previous settings", List.of(rollbackChangeResult));
				})
				.exceptionally(ex -> {
					logger.error("Exception during anti-spam policy rollback", ex);
					rollbackChangeResult.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to roll back anti-spam policy: " + ex.getMessage(), List.of(rollbackChangeResult));
				});
	}

	protected List<ContentFilterPolicy> loadContentFilterPolicies() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Content Filter Policies");
			return new ArrayList<>();
		}

		JsonNode policies = configNode.get(ExoConstants.CONFIG_KEY_CONTENT_FILTER_POLICY);

		if (policies == null || !policies.isArray()) {
			logger.warn("Content filter policy '{}' node not found or not an array", ExoConstants.CONFIG_KEY_CONTENT_FILTER_POLICY);
			return new ArrayList<>();
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, ContentFilterPolicy.class);
		return jsonMapper.convertValue(policies, collectionType);
	}


	private boolean isValidPolicy(ContentFilterPolicy policy) {
		Set<String> validAction = Set.of(QUARANTINE, MOVE_TO_JMF, JUNK);
		boolean isSpamActionValid = policy.spamAction != null && validAction.contains(policy.spamAction);
		boolean isHighConfidenceSpamAction = policy.highConfidenceSpamAction != null && validAction.contains(policy.highConfidenceSpamAction);

		if (!isSpamActionValid) {
			logger.warn("Spam not set to be quarantined or moved to junk.");
		}

		if (!isHighConfidenceSpamAction) {
			logger.warn("High confidence spam not set to be quarantined or moved to junk.");
		}

		if (CollectionUtils.isNotEmpty(policy.allowedSenderDomains)) {
			logger.warn("Allowed sender domains are configured (must be empty).");
		}

		return isSpamActionValid && isHighConfidenceSpamAction && CollectionUtils.isEmpty(policy.allowedSenderDomains);
	}

	private CompletableFuture<PolicyChangeResult> configureAntiSpamPolicy(ContentFilterPolicy defaultPolicy) {
		logger.info("Configuring anti-spam policies to meet security requirements");

		Map<String, Object> parameters = new HashMap<>(getAntispamPolicy());
		ParameterChangeResult changeResult = new ParameterChangeResult()
				.parameter("UpdateHostedContentFilterPolicy")
				.prevValue(toParameters(defaultPolicy))
				.newValue(parameters);

		return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("Set-HostedContentFilterPolicy", parameters))
				.thenApply(result -> {
					logger.info("Successfully updated anti-spam policy settings, verifying changes");
					changeResult.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Successfully updated anti-spam policy settings", List.of(changeResult));
				})
				.exceptionally(ex -> {
					logger.error("Exception during anti-spam policy configuration", ex);
					changeResult.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to configure anti-spam policies: " + ex.getMessage(), List.of(changeResult));
				});
	}

	private Map<String, Object> toParameters(ContentFilterPolicy defaultPolicy) {
		return Map.of(
				IDENTITY, defaultPolicy.identity,
				SPAM_ACTION, defaultPolicy.spamAction,
				HIGH_CONFIDENCE_SPAM_ACTION, defaultPolicy.highConfidenceSpamAction,
				ALLOWED_SENDER_DOMAINS, defaultPolicy.allowedSenderDomains
		);
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	protected static class ContentFilterPolicy {
		public String identity;
		public String spamAction;
		public String highConfidenceSpamAction;
		public List<String> allowedSenderDomains;
	}
}

