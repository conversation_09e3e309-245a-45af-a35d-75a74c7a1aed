package io.syrix.products.microsoft.entra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.LicenseProcessor;
import io.syrix.products.microsoft.base.SkuProcessingResult;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.DelegatedMicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.metrics.MetricsSnapshot;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.PaginatedResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.products.microsoft.entra.model.PrivilegedUser;
import org.apache.commons.lang3.StringUtils;

import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import java.util.stream.Collectors;

import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.VALUE_FIELD;

/**
 * Service for managing and exporting Microsoft Entra ID configurations.
 * Provides comprehensive functionality for retrieving security settings,
 * policies, and user privileges with proper error handling and metrics collection.
 */
public class EntraConfigurationService extends BaseConfigurationService {
	static final String CONDITIONS = "conditions";
	static final String APP_ROLE_ID = "appRoleId";
	static final String RISKY_APPLICATIONS = "risky_applications";
	static final String RISKY_THIRD_PARTY_SERVICE_PRINCIPALS = "risky_third_party_service_principals";
	static final String APPLICATION = "Application";
	static final String PERMISSIONS = "permissions";

	protected static final String FILTER_UNIFIED_GROUPS = "groupTypes/any(c:c eq 'Unified')";
	protected static final String PUBLIC_VISIBILITY = "Public";
	private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);
	public static final String UNSUCCESSFUL_COMMANDS = "unsuccessful_commands";
	public static final String SUCCESSFUL_COMMANDS = "successful_commands";

	private final DirectoryRoleService directoryRoleService;
	private final GroupMemberProcessor groupProcessor;
	private final DelegatedMicrosoftGraphClient delegatedGraphClient;

	/**
	 * Creates a new configuration service with default settings.
	 */
	public EntraConfigurationService(MicrosoftGraphClient graphClient) {
		this(graphClient, new ObjectMapper(), new MetricsCollector());
	}

	/**
	 * Creates a new configuration service with custom dependencies.
	 */
	public EntraConfigurationService(
			MicrosoftGraphClient graphClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics) {
		super(graphClient, objectMapper, metrics);
		this.directoryRoleService = createDirectoryRoleService();
		this.groupProcessor = createGroupMemberProcessor();
		this.delegatedGraphClient = DelegatedMicrosoftGraphClient.fromApplicationClient(graphClient);
	}
	
	/**
	 * Creates a new DirectoryRoleService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new DirectoryRoleService instance
	 */
	protected DirectoryRoleService createDirectoryRoleService() {
		return new DirectoryRoleService(graphClient, objectMapper, metrics);
	}
	
	/**
	 * Creates a new GroupMemberProcessor instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new GroupMemberProcessor instance
	 */
	protected GroupMemberProcessor createGroupMemberProcessor() {
		return new GroupMemberProcessor(graphClient, objectMapper, metrics);
	}
	
	/**
	 * Creates a new ConditionalAccessService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new ConditionalAccessService instance
	 */
	protected ConditionalAccessService createConditionalAccessService() {
		return new ConditionalAccessService(this.graphClient, this.objectMapper, this.metrics);
	}
	
	/**
	 * Creates a new AuthenticationService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new AuthenticationService instance
	 */
	protected AuthenticationService createAuthenticationService() {
		return new AuthenticationService(this.graphClient, this.objectMapper, this.metrics);
	}
	
	/**
	 * Creates a new RiskyPermissionsService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new RiskyPermissionsService instance
	 */
	protected RiskyPermissionsService createRiskyPermissionsService() {
		return new RiskyPermissionsService(this.graphClient, this.objectMapper, this.metrics);
	}
	
	/**
	 * Creates a new EnterpriseApplicationService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new EnterpriseApplicationService instance
	 */
	protected EnterpriseApplicationService createEnterpriseApplicationService() {
		return new EnterpriseApplicationService(this.graphClient, this.objectMapper, this.metrics);
	}
	
	/**
	 * Creates a new LicenseProcessor instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new LicenseProcessor instance
	 */
	protected LicenseProcessor createLicenseProcessor() {
		return new LicenseProcessor(this.graphClient);
	}
	
	/**
	 * Creates a new DynamicGroupComplianceService instance.
	 * This method can be overridden in tests to provide a mock implementation.
	 * 
	 * @return A new DynamicGroupComplianceService instance
	 */
	protected DynamicGroupComplianceService createDynamicGroupComplianceService() {
		return new DynamicGroupComplianceService(this.graphClient, this.objectMapper, this.metrics);
	}

	/**
	 * Exports the complete Entra ID configuration with metrics collection and error handling.
	 */
	public ConfigurationResult exportConfiguration() {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting configuration export at {}", startTime);

		try {
			Map<String, CompletableFuture<?>> futures = new HashMap<>();

			LicenseProcessor licenseProcessor = createLicenseProcessor();
			SkuProcessingResult processedSkus = licenseProcessor.processSkus();

			futures.put("total_user_count", getTotalUserCount());
			futures.put("directory_settings", getDirectorySettings());

			boolean hasP2 = processedSkus.hasAadPremiumP2();
			CompletableFuture<JsonNode> privilegeDirectoryRoles = this.directoryRoleService.getDirectoryRoles(hasP2);

			futures.put("privileged_users", getPrivilegedUsers(privilegeDirectoryRoles, hasP2));
			futures.put("domain_settings", getDomains());
			futures.put("privileged_roles", privilegeDirectoryRoles);
			futures.put("public_groups", auditAndExportPublicM365Groups());
			futures.put("entraAdminCenterAccess", getEntraAdminCenterAccess());

			// Create and use all the services
			ConfigurationResult capConfig;
			try (ConditionalAccessService capService = createConditionalAccessService()) {
				capConfig = capService.exportConfiguration();
			}

			// Create and use the AuthenticationService
			ConfigurationResult authConfig;
			try (AuthenticationService authService = createAuthenticationService()) {
				authConfig = authService.exportConfiguration();
			}

			// Create and use the RiskyPermissionsService
			ConfigurationResult riskyPermissionsConfig;
			try (RiskyPermissionsService riskyPermissionsService = createRiskyPermissionsService()) {
				riskyPermissionsConfig = riskyPermissionsService.exportConfiguration();
			}

			// Create and use the EnterpriseApplicationService
			ConfigurationResult enterpriseAppConfig;
			try (EnterpriseApplicationService enterpriseAppService = createEnterpriseApplicationService()) {
				enterpriseAppConfig = enterpriseAppService.exportConfiguration();
			}

			// Create and use the DynamicGroupComplianceService
			ConfigurationResult dynamicGroupConfig;
			try (DynamicGroupComplianceService dynamicGroupService = createDynamicGroupComplianceService()) {
				dynamicGroupConfig = dynamicGroupService.exportConfiguration();
			}

			// Wait for all futures to complete with timeout
			ConfigurationResult result = waitForFutures(futures)
					.thenApply(map -> {
						map.put("service_plans", hasP2);
						map.put("license_information", hasP2);

						// Add data from ConditionalAccessService
						map.put("conditional_access_policies", capConfig.getData().get("conditional_access_policies"));
						map.put("cap_table_data", capConfig.getData().get("cap_table_data"));

						// Add data from AuthenticationService
						map.put("authentication_method", authConfig.getData().get("authentication_methods"));
						map.put("authorization_policies", authConfig.getData().get("authorization_policies"));

						// Add risky permissions data
						if (riskyPermissionsConfig.getData().has(RISKY_APPLICATIONS)) {
							map.put(RISKY_APPLICATIONS, riskyPermissionsConfig.getData().get(RISKY_APPLICATIONS));
						}
						if (riskyPermissionsConfig.getData().has(RISKY_THIRD_PARTY_SERVICE_PRINCIPALS)) {
							map.put(RISKY_THIRD_PARTY_SERVICE_PRINCIPALS,
									riskyPermissionsConfig.getData().get(RISKY_THIRD_PARTY_SERVICE_PRINCIPALS));
						}

						// Add enterprise applications data
						map.put("enterprise_applications", enterpriseAppConfig.getData().get("enterprise_applications"));

						// Add dynamic group compliance data
						map.put("dynamic_group_compliance", dynamicGroupConfig.getData().get("dynamic_group_compliance"));
						map.put("all_dynamic_groups", dynamicGroupConfig.getData().get("all_dynamic_groups"));

						// Merge successful commands from all services with our own
						ArrayNode mergedSuccessful = objectMapper.createArrayNode();
						mergeCommands(mergedSuccessful, capConfig.getData().get(SUCCESSFUL_COMMANDS));
						mergeCommands(mergedSuccessful, authConfig.getData().get(SUCCESSFUL_COMMANDS));
						mergeCommands(mergedSuccessful, riskyPermissionsConfig.getData().get(SUCCESSFUL_COMMANDS));
						mergeCommands(mergedSuccessful, enterpriseAppConfig.getData().get(SUCCESSFUL_COMMANDS));
						mergeCommands(mergedSuccessful, dynamicGroupConfig.getData().get(SUCCESSFUL_COMMANDS));
						getSuccessfulCommands().forEach(mergedSuccessful::add);
						map.put("aad_successful_commands", mergedSuccessful);

						// Merge unsuccessful commands from all services with our own
						ArrayNode mergedUnsuccessful = objectMapper.createArrayNode();
						mergeCommands(mergedUnsuccessful, capConfig.getData().get(UNSUCCESSFUL_COMMANDS));
						mergeCommands(mergedUnsuccessful, authConfig.getData().get(UNSUCCESSFUL_COMMANDS));
						mergeCommands(mergedUnsuccessful, riskyPermissionsConfig.getData().get(UNSUCCESSFUL_COMMANDS));
						mergeCommands(mergedUnsuccessful, enterpriseAppConfig.getData().get(UNSUCCESSFUL_COMMANDS));
						mergeCommands(mergedUnsuccessful, dynamicGroupConfig.getData().get(UNSUCCESSFUL_COMMANDS));
						getUnsuccessfulCommands().forEach(mergedUnsuccessful::add);
						map.put("aad_unsuccessful_commands", mergedUnsuccessful);

						return buildConfigurationResult(map);
					}).get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			// Record success metrics
			Duration duration = Duration.between(startTime, Instant.now());
			metrics.recordExportSuccess(duration);

			try {
				Files.createDirectory(Path.of("AppsJson"));
			} catch (FileAlreadyExistsException alreadyExistsException) {
				logger.debug("AppsJson directory is already exists");
			} catch (Exception excp) {
				logger.error("Error creating AppsJson directory", excp);
			}

			logger.info("Configuration export completed successfully in {} seconds", duration.toSeconds());
			return result;

		} catch (Exception e) {
			metrics.recordExportFailure();
			logger.error("Configuration export failed", e);
			throw new ConfigurationExportException("Configuration export failed", e);
		}
	}

	// Helper method to merge commands from different services
	private void mergeCommands(ArrayNode targetArray, JsonNode commands) {
		if (commands != null) {
			commands.forEach(targetArray::add);
		}
	}

	/**
	 * Audits Microsoft 365 Groups for public visibility.
	 * Implements CIS control 1.2.1 (MS.AAD.8.5v1).
	 *
	 * @return CompletableFuture with an array of public group objects
	 */
	private CompletableFuture<ArrayNode> auditAndExportPublicM365Groups() {
		logger.info("Auditing Microsoft 365 Groups for public visibility");
		return withRetry(() -> Common.auditAndExportPublicM365Groups(logger, objectMapper, graphClient), "Get-MgGroup")
				.exceptionally(e -> {
					logger.error("Failed to audit M365 Groups privacy: {}", e.getMessage(), e);
					// Return empty array on error
					return objectMapper.createArrayNode();
				});
	}


	private CompletableFuture<String> getTotalUserCount() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/users/$count")
						.withMethod(HttpMethod.GET)
						.build()).thenApply(JsonNode::asText), "Get-MgBetaUserCount")
				.exceptionally(e -> {
					logger.error("Failed to call getTotalUserCount {}", e.getMessage());
					return "";
				});
	}


	/**
	 * Retrieves directory settings.
	 */
	private CompletableFuture<JsonNode> getDirectorySettings() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/settings")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaDirectorySetting")
				.exceptionally(e -> {
					logger.error("Failed to call getDirectorySettings {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves domain settings.
	 */
	private CompletableFuture<JsonNode> getDomains() {
		return withRetry(() ->
				graphClient.makeGraphRequest(GraphRequest.builder().beta()
						.withEndpoint("/domains")
						.withMethod(HttpMethod.GET)
						.build()), "Get-MgBetaDomain")
				.exceptionally(e -> {
					logger.error("Failed to call getDomains {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves all privileged users with their roles.
	 *
	 * @param privilegeDirectoryRoles  CompletableFuture<JsonNode> containing directory roles
	 * @param hasAadPremiumP2 Whether tenant has AAD Premium P2 license
	 * @return CompletableFuture<Map < String, PrivilegedUser>> Map of user IDs to their privileges
	 */
	public CompletableFuture<Map<String, PrivilegedUser>> getPrivilegedUsers(
			CompletableFuture<JsonNode> privilegeDirectoryRoles,
			boolean hasAadPremiumP2) {

		return privilegeDirectoryRoles.thenCompose(roles -> {
			List<CompletableFuture<List<PrivilegedUser>>> userFutures = new ArrayList<>();
			CompletableFuture<JsonNode> directoryRoles = getDirectoryRoles();
			return directoryRoles.thenCompose(dirRoles -> {
				for (JsonNode role : roles.get(VALUE_FIELD)) {
					String roleId = DirectoryRoleService.getRoleIDByTemplpateID(dirRoles, role);
					String roleName = role.get(DISPLAY_NAME_FIELD).asText();

					if (!StringUtils.isEmpty(roleId)) {
						CompletableFuture<List<PrivilegedUser>> usersFuture =
								getRoleMembers(roleId)
										.thenCompose(members -> processRoleMembers(
												members,
												roleName,
												hasAadPremiumP2
										));

						userFutures.add(usersFuture);
					}
				}
				return CompletableFuture.allOf(userFutures.toArray(new CompletableFuture[0]))
						.thenApply(v -> userFutures.stream()
								.map(CompletableFuture::join)
								.flatMap(List::stream)
								.collect(Collectors.toMap(
										PrivilegedUser::getId,
										user -> user,
										(existing, replacement) -> {
											// Merge roles if user exists
											replacement.getRoles().forEach(existing::addRole);
											return existing;
										}
								)));
			});
		});
	}

	private CompletableFuture<JsonNode> getDirectoryRoles() {
		return graphClient.makeGraphRequest(GraphRequest.builder()
						.withEndpoint("/directoryRoles")
						.beta()
						.build())
				.exceptionally(ex -> {
					logger.error("Failed to call getDirectoryRoles {}", ex.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Processes members of a role, handling both direct user assignments and group memberships.
	 */
	private CompletableFuture<List<PrivilegedUser>> processRoleMembers(
			JsonNode members,
			String roleName,
			boolean hasAadPremiumP2) {

		List<CompletableFuture<List<PrivilegedUser>>> futures = new ArrayList<>();

		for (JsonNode member : members.get(VALUE_FIELD)) {
			String type = member.get(EntraIDConstants.ODATA_TYPE).asText();
			String id = member.get(ID_FIELD).asText();

			if (type.endsWith("user")) {
				futures.add(processUser(id, roleName));
			} else if (type.endsWith("group")) {
				futures.add(groupProcessor.processGroup(id, roleName, hasAadPremiumP2));
			}
		}

		return CompletableFuture.allOf(
						futures.toArray(new CompletableFuture[0]))
				.thenApply(v -> futures.stream().map(CompletableFuture::join).flatMap(List::stream).toList()
				);
	}

	static CompletableFuture<List<PrivilegedUser>> processUsersInternal(MicrosoftGraphClient graphClient,
																		String userId,
																		String roleName,
																		Logger logger) {
		GraphRequest request = GraphRequest.builder()
				.beta()
				.withEndpoint("/users/" + userId)
				.withMethod(HttpMethod.GET)
				.build();
		return graphClient.makeGraphRequest(request)
				.thenApply(user -> {
					JsonNode isOnPremEnabled = user.get("onPremisesImmutableId");
					PrivilegedUser privilegedUser = new PrivilegedUser(
							userId,
							user.get(DISPLAY_NAME_FIELD).asText(),
							isOnPremEnabled != null ? isOnPremEnabled.asText() : null
					);
					privilegedUser.addRole(roleName);
					return Collections.singletonList(privilegedUser);
				}).exceptionally(e -> {
					logger.error("Failed to call processUser {}", e.getMessage());
					return Collections.emptyList();
				});
	}

	/**
	 * Processes a single user, creating a PrivilegedUser object with the assigned role.
	 */
	private CompletableFuture<List<PrivilegedUser>> processUser(String userId, String roleName) {
		return withRetry(() -> processUsersInternal(graphClient, userId, roleName, logger), "Get-MgBetaUser");
	}

	/**
	 * Retrieves members of a specific directory role, handling pagination and group expansion.
	 * This method fetches both direct members and members inherited through group assignments.
	 *
	 * @param roleId The unique identifier of the directory role
	 * @return A CompletableFuture containing the JSON representation of role members
	 * @throws ConfigurationExportException if the retrieval fails
	 *                                      <p>
	 *                                      // In the getPrivilegedUsers method:
	 *                                      CompletableFuture<List<PrivilegedUser>> usersFuture =
	 *                                      getRoleMembers(roleId)
	 *                                      .thenCompose(members -> processRoleMembers(members, roleName));
	 */
	private CompletableFuture<JsonNode> getRoleMembers(String roleId) {
		Instant startTime = Instant.now();
		String endpoint = String.format("/directoryRoles/%s/members", roleId);

		return withRetry(() ->
				CompletableFuture.supplyAsync(() -> {
					try {
						List<JsonNode> allMembers = new ArrayList<>();
						String nextLink = endpoint;

						// Continue fetching pages until there are no more
						while (nextLink != null) {
							PaginatedResult result = graphClient.makePaginatedRequest(GraphRequest.builder().v1()
									.withEndpoint(nextLink)
									.withMethod(HttpMethod.GET)
									.build());
							allMembers.addAll(result.getValues());
							nextLink = result.getNextLink();

							metrics.recordApiCall(endpoint,
									Duration.between(startTime, Instant.now()),
									true);
						}

						// Here's where we make the type handling explicit
						// Create a combined result as JsonNode
						JsonNode combined = objectMapper.createObjectNode()
								.set(VALUE_FIELD, objectMapper.valueToTree(allMembers));

						logger.debug("Retrieved {} members for role {}",
								allMembers.size(),
								roleId);

						return combined;

					} catch (Exception e) {
						metrics.recordApiCall(endpoint,
								Duration.between(startTime, Instant.now()),
								false);

						logger.error("Failed to retrieve members for role {}: {}",
								roleId,
								e.getMessage());

						throw new CompletionException(new ConfigurationExportException(
								String.format("Failed to retrieve members for role %s", roleId),
								e));
					}
				}, executor), "Get-MgBetaDirectoryRoleMember"
		).exceptionally(throwable -> {
			logger.error("Failed to retrieve role members after retries: {}",
					throwable.getMessage());

			throw new CompletionException(new ConfigurationExportException(
					"Failed to retrieve role members after retries",
					throwable));
		});
	}

	/**
	 * Builds the final configuration result from all collected data.
	 */
	private ConfigurationResult buildConfigurationResult(Map<String, ?> results) {
		ObjectNode configData = objectMapper.createObjectNode();

		results.forEach((key, value) -> {
			if (value instanceof JsonNode jsonNode) {
				JsonNode valueNode = jsonNode.get(VALUE_FIELD);
				configData.set(key, Objects.requireNonNullElse(valueNode, jsonNode));
			} else if (value instanceof Map) {
				configData.set(key, objectMapper.valueToTree(value));
			} else {
				configData.set(key, value == null ? NullNode.getInstance() : objectMapper.valueToTree(value));
			}
		});

		return ConfigurationResult.builder()
				.withData(configData)
				.withTimestamp(Instant.now())
				.withMetadata(buildMetadata("1.0"))
				.build();
	}

	/**
	 * Retrieves Entra admin center access configuration.
	 * Implements the configuration retrieval for CIS Microsoft 365 Foundations Benchmark 5.1.2.4.
	 * 
	 * Note: Microsoft Graph API does not support programmatic access to UX settings.
	 * Returns a placeholder indicating manual configuration is required.
	 */
	private CompletableFuture<JsonNode> getEntraAdminCenterAccess() {
		logger.warn("Entra admin center access settings cannot be retrieved via API - returning placeholder");
		
		// Create a placeholder response indicating API limitation
		ObjectNode placeholderResponse = objectMapper.createObjectNode();
		placeholderResponse.put("apiSupported", false);
		placeholderResponse.put("message", "Microsoft Graph API does not support UX settings access");
		placeholderResponse.put("manualConfigurationRequired", true);
		placeholderResponse.put("configurationLocation", "Entra admin center > Settings > User settings");
		
		return CompletableFuture.completedFuture(placeholderResponse);
	}

	/**
	 * Gets the current metrics snapshot.
	 */
	public MetricsSnapshot getMetrics() {
		return metrics.getSnapshot();
	}
}