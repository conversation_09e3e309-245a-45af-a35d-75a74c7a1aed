package io.syrix.products.microsoft.teams.model;

import io.syrix.protocols.client.teams.powershell.command.types.MeetingBroadcastPolicy;

@SuppressWarnings({"CommentedOutCode", "unused"})
public class TeamsMeetingBroadcastPolicy {
    public Boolean allowBroadcastScheduling;
    public Boolean allowBroadcastTranscription;
    public String broadcastAttendeeVisibilityMode;
    public String broadcastRecordingMode;
    public String configId;
    public ConfigMetadata configMetadata;
    public String dataSource;
    public String description;
    public String identity;
    public Key key;

    public static class ConfigMetadata {
        public String authority;

        public ConfigMetadata() {}
        public ConfigMetadata(MeetingBroadcastPolicy.ConfigMetadata configMetadata) {
            if (configMetadata == null) return;
            this.authority = configMetadata.authority;
        }
    }

    public static class Key {
        public String authorityId;
        public String defaultXml;
        public String schemaId;
        public String scopeClass;
        public String xmlRoot;

        public Key(){} //for serialization from json
        public Key(MeetingBroadcastPolicy.Key key) {
            if (key == null) return;

            this.authorityId = key.authorityId == null ? null : String.format("Class=%s;InstanceId=%s;XmlRoot=", key.authorityId.classType, key.authorityId.instanceId);
            this.defaultXml = key.defaultXml == null ? null : String.format("SchemaId=;Data=;ConfigObject=;Signature=%s;IsModified=%s",key.defaultXml.signature, key.defaultXml.isModified ? "True" : "False");
            this.schemaId = key.schemaId == null ? null : "XName="; //TODO Artur recheck
            this.scopeClass = key.scopeClass;
            this.xmlRoot = key.xmlRoot == null ? null : String.format("name=%s", key.xmlRoot.name);

//            this.authorityId = key.authorityId == null ? null : new AuthorityId(key.authorityId);
//            this.defaultXml = key.defaultXml == null ? null : new DefaultXml(key.defaultXml);
//            this.schemaId = key.schemaId == null ? null : new SchemaId(key.schemaId);
//            this.scopeClass = key.scopeClass;
//            this.xmlRoot = key.xmlRoot == null ? null : new XmlRoot(key.xmlRoot);
        }
    }

//    public static class AuthorityId {
//        @JsonProperty(value = "Class")
//        public String classType;
//        public String instanceId;
//        public XmlRoot xmlRoot;
//
//        public AuthorityId() {} //for serialization from json
//        public AuthorityId(MeetingBroadcastPolicy.AuthorityId authorityId) {
//            if (authorityId == null) return;
//            this.classType = authorityId.classType;
//            this.instanceId = authorityId.instanceId;
//            this.xmlRoot = authorityId.xmlRoot == null ? null : new XmlRoot(authorityId.xmlRoot);
//        }
//    }
//
//    public static class DefaultXml {
//        public Object configObject;
//        public Data data;
//        public Boolean isModified;
//        public SchemaId schemaId;
//        public String signature;
//
//        public DefaultXml() { } //for serialization from json
//        public DefaultXml(MeetingBroadcastPolicy.DefaultXml defaultXml) {
//            if (defaultXml == null) return;
//
//            this.configObject = defaultXml.configObject;
//            this.data = defaultXml.data == null ? null : new Data(defaultXml.data);
//            this.isModified = defaultXml.isModified;
//            this.schemaId = defaultXml.schemaId == null ? null : new SchemaId(defaultXml.schemaId);
//            this.signature = defaultXml.signature;
//        }
//
//    }
//
//    public static class Data {
//        public TeamsMeetingBroadcastPolicyData teamsMeetingBroadcastPolicy;
//
//        public Data() {}//for serialization from json
//        public Data(MeetingBroadcastPolicy.Data data) {
//            if (data == null) return;
//            this.teamsMeetingBroadcastPolicy = data.teamsMeetingBroadcastPolicy == null ? null : new TeamsMeetingBroadcastPolicyData(data.teamsMeetingBroadcastPolicy);
//        }
//    }
//
//    public static class TeamsMeetingBroadcastPolicyData {
//        @JsonProperty(value = "@xmlns")
//        public String xmlns;
//
//        public TeamsMeetingBroadcastPolicyData() {}//for serialization from json
//        public TeamsMeetingBroadcastPolicyData(MeetingBroadcastPolicy.TeamsMeetingBroadcastPolicyData data) {
//            if (data == null) return;
//
//            this.xmlns = data.xmlns;
//        }
//    }
//
//    public static class SchemaId {
//        public XName xName;
//
//        public SchemaId() {} //for serialization from json
//        public SchemaId(MeetingBroadcastPolicy.SchemaId schemaId) {
//            if (schemaId == null) return;
//
//            this.xName = schemaId.xName == null ? null : new XName(schemaId.xName);
//        }
//    }
//
//    public static class XName {
//        public String name;
//
//        public XName() {}//for serialization from json
//        public XName(MeetingBroadcastPolicy.XName xName) {
//            if (xName == null) return;
//            this.name = xName.name;
//        }
//    }
//
//    public static class XmlRoot {
//        public String name;
//
//        public XmlRoot() {} //for serialization from json
//        public XmlRoot(MeetingBroadcastPolicy.XmlRoot xmlRoot) {
//            if (xmlRoot == null) return;
//            this.name = xmlRoot.name;
//        }
//    }

    public TeamsMeetingBroadcastPolicy() {}
    public TeamsMeetingBroadcastPolicy(MeetingBroadcastPolicy meetingBroadcastPolicy) {
        this.allowBroadcastScheduling = meetingBroadcastPolicy.allowBroadcastScheduling;
        this.allowBroadcastTranscription = meetingBroadcastPolicy.allowBroadcastTranscription;
        this.broadcastAttendeeVisibilityMode = meetingBroadcastPolicy.broadcastAttendeeVisibilityMode;
        this.broadcastRecordingMode = meetingBroadcastPolicy.broadcastRecordingMode.asString();
        this.configId = meetingBroadcastPolicy.configId;
        this.configMetadata = meetingBroadcastPolicy.configMetadata == null ? null : new ConfigMetadata(meetingBroadcastPolicy.configMetadata);
        this.dataSource = meetingBroadcastPolicy.dataSource;
        this.description = meetingBroadcastPolicy.description;
        this.identity = meetingBroadcastPolicy.identity;
        this.key = meetingBroadcastPolicy.key == null ? null : new Key(meetingBroadcastPolicy.key);
    }
}

