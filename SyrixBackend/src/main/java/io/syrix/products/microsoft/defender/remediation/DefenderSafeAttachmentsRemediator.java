package io.syrix.products.microsoft.defender.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediatorBase;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.DEFENDER.3.1v1
 * Enables Safe Attachments for SharePoint, OneDrive, and Microsoft Teams in Microsoft Defender for Office 365.
 */
@PolicyRemediator("MS.DEFENDER.3.1v1")
public class DefenderSafeAttachmentsRemediator extends RemediatorBase {
	private final PowerShellClient powershellClient;
	private final ObjectMapper objectMapper;

	// Parameter names for ATP Policy for Office 365
	private static final String ENABLE_ATP_FOR_SPO_TEAMS_ODB = "EnableATPForSPOTeamsODB";


	public DefenderSafeAttachmentsRemediator(PowerShellClient powershellClient) {
		this.powershellClient = powershellClient;
		this.objectMapper = new ObjectMapper();
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting remediation for MS.DEFENDER.3.1v1 - Safe Attachments for SharePoint, OneDrive, and Teams");

		// First check if Safe Attachments is already enabled
		return checkSafeAttachmentsConfiguration()
				.thenCompose(configResult -> {
					if (configResult == null || configResult.has(Constants.ERROR_FIELD)) {
						String error = configResult != null ?
								configResult.get(Constants.ERROR_FIELD).asText() :
								"Failed to check Safe Attachments configuration";
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Check if already enabled
					boolean isEnabled = isATPEnabled(configResult);
					if (isEnabled) {
						logger.info("Safe Attachments for SharePoint, OneDrive, and Teams is already enabled");
						return CompletableFuture.completedFuture(
								DefenderHelpers.createSuccessResponse(
										getPolicyId(),
										0,
										"SafeAttachments",
										"",
										objectMapper,
										"Safe Attachments for SharePoint, OneDrive, and Teams is already enabled"
								));
					}

					// Get the policy identity
					String policyIdentity = extractPolicyIdentity(configResult);
					if (policyIdentity.isEmpty()) {
						String error = "Failed to get ATP Policy identity";
						logger.error(error);
						return CompletableFuture.completedFuture(
								DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper));
					}

					// Enable Safe Attachments
					return enableSafeAttachments(policyIdentity);
				})
				.exceptionally(ex -> {
					logger.error("Exception during Safe Attachments configuration", ex);
					return DefenderHelpers.createErrorResponse(getPolicyId(), ex.getMessage(), objectMapper);
				});
	}

	/**
	 * Check if Safe Attachments is already enabled for SharePoint, OneDrive, and Teams
	 */
	private CompletableFuture<JsonNode> checkSafeAttachmentsConfiguration() {
		// Get the global ATP Policy for Office 365
		return powershellClient.executeCmdletCommand(
				new PowerShellClient.CommandRequest(
						DefenderConstants.GET_ATP_POLICY_FOR_O365,
						Map.of()
				));
	}

	/**
	 * Check if ATP is enabled for SharePoint, OneDrive, and Teams in the policy
	 */
	private boolean isATPEnabled(JsonNode policyResult) {
		if (policyResult == null) {
			return false;
		}

		// Handle array result
		JsonNode policy = policyResult;
		if (policyResult.isArray() && policyResult.size() > 0) {
			policy = policyResult.get(0);
		}

		// Check if EnableATPForSPOTeamsODB is true
		return policy.path(ENABLE_ATP_FOR_SPO_TEAMS_ODB).asBoolean(false);
	}

	/**
	 * Extract the ATP Policy identity from the result
	 */
	private String extractPolicyIdentity(JsonNode policyResult) {
		if (policyResult == null) {
			return "";
		}

		// Handle array result
		JsonNode policy = policyResult;
		if (policyResult.isArray() && policyResult.size() > 0) {
			policy = policyResult.get(0);
		}

		return policy.path(Constants.IDENTITY_FIELD).asText("");
	}

	/**
	 * Enable Safe Attachments for SharePoint, OneDrive, and Teams
	 */
	private CompletableFuture<JsonNode> enableSafeAttachments(String policyIdentity) {
		logger.info("Enabling Safe Attachments for SharePoint, OneDrive, and Teams");

		Map<String, Object> params = new HashMap<>();
		params.put(Constants.IDENTITY_FIELD, policyIdentity);
		params.put(ENABLE_ATP_FOR_SPO_TEAMS_ODB, true);

		return powershellClient.executeCmdletCommand(
						new PowerShellClient.CommandRequest(
								DefenderConstants.SET_ATPPOLICY_COMMAND,
								params
						))
				.thenApply(result -> {
					if (result == null || result.has(Constants.ERROR_FIELD)) {
						String error = result != null ?
								result.get(Constants.ERROR_FIELD).asText() :
								"Failed to enable Safe Attachments for SharePoint, OneDrive, and Teams";
						logger.error(error);
						return DefenderHelpers.createErrorResponse(getPolicyId(), error, objectMapper);
					}

					logger.info("Successfully enabled Safe Attachments for SharePoint, OneDrive, and Teams");
					return DefenderHelpers.createSuccessResponse(
							getPolicyId(),
							0,
							"SafeAttachments",
							"",
							objectMapper,
							"Successfully enabled Safe Attachments for SharePoint, OneDrive, and Teams"
					);
				});
	}
}