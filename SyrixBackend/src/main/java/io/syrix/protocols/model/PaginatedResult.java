package io.syrix.protocols.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Represents a paginated response from the Microsoft Graph API.
 * This class handles the standard Graph API pagination format where results
 * are returned in pages with a "value" array and an "@odata.nextLink" for
 * additional results.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaginatedResult {

	// The actual data values returned in this page
	private final List<JsonNode> values;

	// The URL to fetch the next page of results, if any
	private final String nextLink;

	// Total count of items across all pages, if provided by the API
	private final Integer totalCount;

	/**
	 * Creates a new paginated result instance.
	 *
	 * @param values The list of values in the current page
	 * @param nextLink The URL for the next page, or null if this is the last page
	 * @param totalCount The total count of items, or null if not provided
	 */
	public PaginatedResult(
			@JsonProperty("value") List<JsonNode> values,
			@JsonProperty("@odata.nextLink") String nextLink,
			@JsonProperty("@odata.count") Integer totalCount) {

		this.values = values != null ? Collections.unmodifiableList(new ArrayList<>(values)) : Collections.emptyList();
		this.nextLink = nextLink;
		this.totalCount = totalCount;
	}

	/**
	 * Gets the list of values in the current page.
	 * Returns an unmodifiable list to preserve immutability.
	 *
	 * @return List of JsonNode values from the current page
	 */
	public List<JsonNode> getValues() {
		return values;
	}

	/**
	 * Gets the URL for the next page of results.
	 *
	 * @return The next page URL, or null if this is the last page
	 */
	public String getNextLink() {
		return nextLink;
	}

	/**
	 * Checks if there are more pages available.
	 *
	 * @return true if there are more pages to fetch, false otherwise
	 */
	public boolean hasNextPage() {
		return nextLink != null && !nextLink.trim().isEmpty();
	}

	/**
	 * Gets the total count of items across all pages.
	 * Note: This may be null if the API doesn't provide a count.
	 *
	 * @return The total count of items, or null if not available
	 */
	public Integer getTotalCount() {
		return totalCount;
	}

	/**
	 * Gets the number of items in the current page.
	 *
	 * @return The count of items in this page
	 */
	public int getCurrentPageSize() {
		return values.size();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		PaginatedResult that = (PaginatedResult) o;
		return Objects.equals(values, that.values) &&
				Objects.equals(nextLink, that.nextLink) &&
				Objects.equals(totalCount, that.totalCount);
	}

	@Override
	public int hashCode() {
		return Objects.hash(values, nextLink, totalCount);
	}

	@Override
	public String toString() {
		return String.format(
				"PaginatedResult{pageSize=%d, hasNextPage=%s, totalCount=%s}",
				getCurrentPageSize(),
				hasNextPage(),
				totalCount != null ? totalCount : "unknown"
		);
	}

	/**
	 * Creates a builder for constructing PaginatedResult instances.
	 *
	 * @return A new Builder instance
	 */
	public static Builder builder() {
		return new Builder();
	}

	/**
	 * Builder class for creating PaginatedResult instances.
	 * Provides a fluent interface for setting values.
	 */
	public static class Builder {
		private List<JsonNode> values;
		private String nextLink;
		private Integer totalCount;

		public Builder withValues(List<JsonNode> values) {
			this.values = values;
			return this;
		}

		public Builder withNextLink(String nextLink) {
			this.nextLink = nextLink;
			return this;
		}

		public Builder withTotalCount(Integer totalCount) {
			this.totalCount = totalCount;
			return this;
		}

		public PaginatedResult build() {
			return new PaginatedResult(values, nextLink, totalCount);
		}
	}
}