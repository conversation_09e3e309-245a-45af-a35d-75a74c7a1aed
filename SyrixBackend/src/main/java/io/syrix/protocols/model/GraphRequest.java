// src/main/java/com/syrix/graph/model/GraphRequest.java
package io.syrix.protocols.model;

import java.net.http.HttpRequest.BodyPublisher;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Represents a request to the Microsoft Graph API.
 * Immutable class for request configuration.
 */
public class GraphRequest {
	private final String endpoint;
	private final HttpMethod method;
	private final Map<String, String> headers;
	private final BodyPublisher body;
	private final Map<String, String> queryParams;

	private GraphRequest(Builder builder) {
		this.endpoint = Objects.requireNonNull(builder.endpoint, "Endpoint cannot be null");
		this.method = Objects.requireNonNull(builder.method, "HTTP method cannot be null");
		this.headers = new HashMap<>(builder.headers);
		this.body = builder.body;
		this.queryParams = new HashMap<>(builder.queryParams);
	}

	/**
	 * Copy constructor that creates a deep copy of another GraphRequest.
	 * @param other The GraphRequest to copy
	 */
	private GraphRequest(GraphRequest other) {
		Objects.requireNonNull(other, "Source GraphRequest cannot be null");
		this.endpoint = other.endpoint;
		this.method = other.method;
		this.headers = new HashMap<>(other.headers);
		this.body = other.body; // BodyPublisher is typically immutable
		this.queryParams = new HashMap<>();
	}

	/**
	 * Creates and returns a copy of this request.
	 * @return A deep copy of this GraphRequest
	 */
	public GraphRequest copyNoQuery() {
		return new GraphRequest(this);
	}

	public String getEndpoint() {
		return endpoint;
	}

	public HttpMethod getMethod() {
		return method;
	}

	public Map<String, String> getHeaders() {
		return new HashMap<>(headers);
	}

	public BodyPublisher getBody() {
		return body;
	}

	public Map<String, String> getQueryParams() {
		return new HashMap<>(queryParams);
	}

	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {
		private String endpoint;
		private HttpMethod method = HttpMethod.GET;
		private final Map<String, String> headers = new HashMap<>();
		private BodyPublisher body;
		private final Map<String, String> queryParams = new HashMap<>();
		private String version = "v1.0"; // Default to v1.0

		public Builder withEndpoint(String endpoint) {
			this.endpoint = endpoint;
			return this;
		}

		public Builder withMethod(HttpMethod method) {
			this.method = method;
			return this;
		}

		public Builder withVersion(String version) {
			this.version = version;
			return this;
		}

		public Builder beta() {
			this.version = "beta";
			return this;
		}

		public Builder v1() {
			this.version = "v1.0";
			return this;
		}

		public Builder addHeader(String name, String value) {
			headers.put(name, value);
			return this;
		}

		public Builder withBody(BodyPublisher body) {
			this.body = body;
			return this;
		}

		public Builder addQueryParam(String name, String value) {
			queryParams.put(name, value);
			return this;
		}

		public GraphRequest build() {
			if (!endpoint.startsWith("http")) {
				if (endpoint.startsWith("/")) {
					endpoint = String.format("/%s%s", version, endpoint);
				} else {
					endpoint = String.format("/%s/%s", version, endpoint);
				}
			}
			return new GraphRequest(this);
		}

		// Getters for GraphRequest constructor
		String getEndpoint() { return endpoint; }
		HttpMethod getMethod() { return method; }
		Map<String, String> getHeaders() { return headers; }
		BodyPublisher getBody() { return body; }
		Map<String, String> getQueryParams() { return queryParams; }
	}
}

