package io.syrix.protocols.model;

public enum MSEnvironment {

    COMMERCIAL("https://login.microsoftonline.com/%s/oauth2/v2.0/token","https://outlook.office365.com",
            "https://graph.microsoft.com", "https://login.microsoftonline.com/common/oauth2/v2.0/token",
            "https://api.powerplatform.com", "https://ps.compliance.protection.outlook.com/",
            "https://api.interfaces.records.teams.microsoft.com"),
    GCC("https://login.microsoftonline.us/%s/oauth2/v2.0/token", "https://outlook.office365.us",
            "https://graph.microsoft.com/beta","https://login.microsoftonline.us/common/oauth2/v2.0/token",
            "https://api.high.powerapps.us", "https://ps.compliance.protection.outlook.com/",
            "https://api.interfaces.records.teams.microsoft.com"),
    GCC_HIGH("https://login.microsoftonline.us/%s/oauth2/v2.0/token", "https://outlook.office365.us",
            "https://graph.microsoft.us/beta", "https://login.microsoftonline.us/common/oauth2/v2.0/token",
            "https://api.high.powerapps.us", "https://ps.compliance.protection.office365.us/",
            "https://api.interfaces.records.teams.microsoft.com"),
    DOD("https://login.microsoftonline.us/%s/oauth2/v2.0/token", "https://outlook.mil",
            "https://dod-graph.microsoft.us/beta", "https://login.microsoftonline.us/common/oauth2/v2.0/token",
            "https://api.appsplatform.us", "https://l5.ps.compliance.protection.office365.us/",
                "https://api.interfaces.records.teams.microsoft.com");

    private final String authUrl;
    private final String outlookEndpoint;
    private final String graphEndpoint;
    private final String msOnlineEndpoint;
    private final String powerEndpoint;
    private final String scpsEndpoint;
    private final String teamsEndpoint;

    MSEnvironment(final String authUrl,
                  String outlookEndpoint,
                  String graphEndpoint,
                  String msOnlineEndpoint,
                  String powerEndpoint,
                  String scpsEndpoint,
                  String teamsEndpoint) {
        this.authUrl = authUrl;
        this.outlookEndpoint = outlookEndpoint;
        this.graphEndpoint = graphEndpoint;
        this.msOnlineEndpoint = msOnlineEndpoint;
        this.powerEndpoint = powerEndpoint;
        this.scpsEndpoint = scpsEndpoint;
        this.teamsEndpoint = teamsEndpoint;
    }
    public String getAuthUrl() {
        return authUrl;
    }

    public String getOutlookEndpoint() {
        return outlookEndpoint;
    }

    public String getGraphEndpoint() { return graphEndpoint; }

    public String getMSOnlineEndpoint() { return msOnlineEndpoint; }

    public String getMSPowerEndpoint() {return powerEndpoint; }

    public String getSCPsEndpoint() { return scpsEndpoint; }

    public String getTeamsEndpoint() {
        return teamsEndpoint;
    }
}