package io.syrix.protocols.utils.security.storage;

import io.syrix.protocols.client.sharepoint.powershell.cscom.PowerShellDigest;

import java.util.HashMap;
import java.util.Map;

public class DigestStorage {
    private final Map<String, PowerShellDigest> storage = new HashMap<>();

    public PowerShellDigest getToken(String key) {
        return storage.get(key);
    }

    public void putToken(String key, PowerShellDigest token) {
        storage.put(key, token);
    }
}