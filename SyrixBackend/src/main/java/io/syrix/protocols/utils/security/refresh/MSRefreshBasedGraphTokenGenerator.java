package io.syrix.protocols.utils.security.refresh;

import io.syrix.common.constants.Constants;

public class MSRefreshBasedGraphTokenGenerator extends MSRefreshTokenBasedTokenGenerator {


    public MSRefreshBasedGraphTokenGenerator(Params params) {

        super(params);
    }

    @Override
    protected String getScopeUrl() {
        return Constants.MS_GRAPH_SCOPE_URL;
    }

    @Override
    protected String getAuthUrl() {
        return environment.getMSOnlineEndpoint();
    }
}
