package io.syrix.protocols.utils.security;

import io.syrix.protocols.model.MSEnvironment;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

public abstract  class MSTokenGenerator implements ITokenGenerator {

    protected String accessToken;
    protected long tokenExpiresAt;


    protected abstract String getScopeUrl();
    protected abstract String aquierAccessToken();

    public CompletableFuture<String> getAccessToken() {
        return CompletableFuture.supplyAsync(() -> {
            if (accessToken != null && System.currentTimeMillis() < tokenExpiresAt) {
                return accessToken;
            }
            return aquierAccessToken();
        });
    }

    public static Params getParams() {
        return new Params();
    }

    public static class Params {

        public String identifier;
        public MSEnvironment environment;

        public String clientId;
        public String clientSecret;
        public String refreshToken;

        public String appId;
        public String certPath;
        public char[] certPassword;
        public String adminEndpoint;

        public Params identifier(String identifier) {
            this.identifier = identifier;
            return this;
        }

        public Params environment(MSEnvironment environment) {
            this.environment = environment != null ? environment : MSEnvironment.COMMERCIAL;
            return this;
        }

        public Params clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public Params clientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
            return this;
        }

        public Params refreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
            return this;
        }

        public Params appId(String appId) {
            this.appId = appId;
            return this;
        }

        public Params certPath(String certPath) {
            this.certPath = certPath;
            return this;
        }

        public Params certPassword(char[] certPassword) {
            this.certPassword = certPassword != null ?
                    Arrays.copyOf(certPassword, certPassword.length) : null;
            return this;
        }

        public Params adminEndpoint(String adminEndpoint) {
            this.adminEndpoint = adminEndpoint;
            return this;
        }
    }

}
