package io.syrix.protocols.utils.security.cert;

public class PowerShellCertBasedSPTokenGenerator extends MSCertBasedTokenGenerator {

    final private String adminEndpoint;

    public PowerShellCertBasedSPTokenGenerator(Params params) {
        super(params);
        this.adminEndpoint = params.adminEndpoint;
    }

    @Override
    protected String getScopeUrl() {
       return String.format("https://%s/.default", adminEndpoint);
    }
}
