package io.syrix.protocols.utils;

/**
 * Constants for PowerShell client interactions
 */
public class PowerShellConstants {
    // HTTP Headers
    public static final String HEADER_X_PREFER_SERVER_AFFINITY = "X-PreferServerAffinity";
    public static final String HEADER_X_ORGANIZATION = "X-Organization";
    public static final String HEADER_X_CMDLET_NAME = "X-CmdletName";
    
    // Header Values
    public static final String HEADER_VALUE_TRUE = "true";
    
    // PowerShell Request Fields
    public static final String CMDLET_NAME_FIELD = "CmdletName";
    public static final String PARAMETERS_FIELD = "Parameters";
    public static final String CMDLET_INPUT_FIELD = "CmdletInput";
    
    // Error Fields
    public static final String POWERSHELL_ERROR_FIELD = "PowerShellError";
    public static final String POWERSHELL_ERROR_MESSAGE = "PowerShellErrorMessage";
    public static final String POWERSHELL_ERROR_DETAILS = "PowerShellErrorDetails";
    
    // Common Error Messages
    public static final String ERROR_AUTHENTICATION_FAILED = "Authentication failed";
    public static final String ERROR_INVALID_CMDLET = "Invalid PowerShell cmdlet";
    public static final String ERROR_EXECUTION_FAILED = "PowerShell execution failed";
}