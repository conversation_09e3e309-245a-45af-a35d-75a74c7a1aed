package io.syrix.protocols.utils.security.refresh;

import io.syrix.common.constants.Constants;

public class MSRefreshTokenBasedManagementTokenGenerator extends MSRefreshTokenBasedTokenGenerator{

    private final String tenantId;

    public MSRefreshTokenBasedManagementTokenGenerator(Params params) {
        super(params);
        this.tenantId = params.identifier;
    }

    @Override
    protected String getScopeUrl() {
        return Constants.MS_MANAGMENT_SCOPE_URL;
    }

    @Override
    protected String getAuthUrl() {
        return String.format(environment.getAuthUrl(), tenantId);
    }
}