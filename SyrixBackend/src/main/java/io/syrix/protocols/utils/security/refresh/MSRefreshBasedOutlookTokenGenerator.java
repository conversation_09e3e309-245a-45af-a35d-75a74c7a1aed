package io.syrix.protocols.utils.security.refresh;

import io.syrix.common.constants.Constants;

public class MSRefreshBasedOutlookTokenGenerator extends MSRefreshTokenBasedTokenGenerator{

    public MSRefreshBasedOutlookTokenGenerator(Params params) {
        super(params);
    }

    @Override
    protected String getScopeUrl() {
        return Constants.MS_OUTLOOK_SCOPE_URL;
    }

    @Override
    protected String getAuthUrl() {
        return environment.getMSOnlineEndpoint();
    }
}
