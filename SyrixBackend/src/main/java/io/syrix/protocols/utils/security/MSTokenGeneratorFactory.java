package io.syrix.protocols.utils.security;

import io.syrix.protocols.utils.security.cert.*;
import io.syrix.protocols.utils.security.refresh.MSRefreshBasedGraphTokenGenerator;
import io.syrix.protocols.utils.security.refresh.MSRefreshBasedOutlookTokenGenerator;
import io.syrix.protocols.utils.security.refresh.MSRefreshTokenBasedManagementTokenGenerator;

public class MSTokenGeneratorFactory {

    private static volatile MSTokenGeneratorFactory instance;

    private  MSTokenGeneratorFactory() {
        // Protect against reflection
        if (instance != null) {
            throw new IllegalStateException("Singleton already initialized");
        }
    }

    public static MSTokenGeneratorFactory getInstance() {
        // First check (no locking)
        if (instance == null) {
            synchronized (MSTokenGeneratorFactory.class) {
                // Second check (with locking)
                if (instance == null) {
                    instance = new MSTokenGeneratorFactory();
                }
            }
        }
        return instance;
    }

     public ITokenGenerator getPowerShellSPTokenGenerator(MSTokenGenerator.Params builder)  {
        return new PowerShellCertBasedSPTokenGenerator(builder);
    }

    public ITokenGenerator getPowerShellTokenGenerator(MSTokenGenerator.Params builder) {
        return new PowerShellCertBasedTokenGenerator(builder);
    }

    public ITokenGenerator getPowerShellTeamsTokenGenerator(MSTokenGenerator.Params builder) {
        return new PowerShellCertBasedTeamsTokenGenerator(builder);
    }

    public ITokenGenerator getMGraphTokenGenerator(final boolean certBased, MSTokenGenerator.Params builder) {
        if (certBased) {
            return new MSCertBasedGraphTokenGenerator(builder);
        } else {
            return  new MSRefreshBasedGraphTokenGenerator(builder);
        }
    }

    public ITokenGenerator getMSOutlookTokenGenerator(boolean certBased, MSTokenGenerator.Params builder) {
        if (certBased) {
            return new MSCertBasedOutlookTokenGenerator(builder);
        } else {
            return  new MSRefreshBasedOutlookTokenGenerator(builder);
        }
    }

    public ITokenGenerator MSManagmentTokenGenerator(boolean certBased, MSTokenGenerator.Params builder) {

        if (certBased) {
            return new MSCertBasedManagmentTokenGenerator(builder);
        } else {
            return new MSRefreshTokenBasedManagementTokenGenerator(builder);
        }
    }

}
