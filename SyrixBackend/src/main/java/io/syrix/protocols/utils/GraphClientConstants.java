package io.syrix.protocols.utils;

/**
 * Constants for Microsoft Graph client interactions
 */
public class GraphClientConstants {
    // Graph API Endpoints
    public static final String SERVICE_PRINCIPALS_ENDPOINT = "/servicePrincipals";
    public static final String GRAPH_SUBSCRIPTIONS_ENDPOINT = "https://management.azure.com/subscriptions?api-version=2020-01-01";
    public static final String GRAPH_SERVICE_PRINCIPALS_BASE_URL = "https://graph.microsoft.com/v1.0/servicePrincipals/";
    
    // Role IDs and Names
    public static final String DEFAULT_GLOBAL_ADMIN_GROUP = "62e90394-69f5-4237-9190-012177145e10";
    public static final String GLOBAL_ADMIN_ROLE_NAME = "Global Administrator";
    public static final String PHISHING_RESISTANT_MFA_POLICY_NAME = "Phishing-Resistant MFA Policy";
    
    // Common Error Messages
    public static final String ERROR_CANNOT_FIND_DEFAULT_DOMAIN = "Cannot find default domain";
    public static final String ERROR_MULTIPLE_DEFAULT_DOMAINS = "More than one default domain found, cannot select default domain";
    public static final String ERROR_CANNOT_GET_DEFAULT_DOMAIN = "Cannot get default domain";
    public static final String ERROR_ADMIN_ROLE_NOT_FOUND = "Failed to find Global Administrator role: {}. Using default";
    public static final String ERROR_FETCH_SUBSCRIPTIONS = "Failed to fetch subscriptions. HTTP Status: {}";
    public static final String ERROR_RESPONSE_BODY = "Response Body: {}";

    public static final String HEADER_CONSISTENCY_LEVEL = "ConsistencyLevel";
    public static final String CONSISTENCY_LEVEL_EVENTUAL = "eventual";
    public static final String PARAM_COUNT = "$count";
    public static final String PARAM_FILTER = "$filter";

    // Log Messages
    public static final String LOG_ASSIGNED_ROLE = "Assigned role to service principals: {}";
}