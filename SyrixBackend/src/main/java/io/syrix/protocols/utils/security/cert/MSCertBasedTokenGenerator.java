package io.syrix.protocols.utils.security.cert;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.util.Base64URL;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.exception.sharepoint.powershell.PowerShellAuthenticationException;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.utils.security.MSTokenGenerator;

import java.io.FileInputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.security.*;
import java.security.cert.CertificateEncodingException;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public abstract class MSCertBasedTokenGenerator extends MSTokenGenerator {

    private static final String payload = "client_id=%s" +
            "&client_assertion_type=urn:ietf:params:oauth:client-assertion-type:jwt-bearer" +
            "&client_assertion=%s" +
            "&grant_type=client_credentials" +
            "&scope=%s";
    private static final List<String> roles = List.of("Exchange.ManageAsApp");

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final String appId;
    private final String identifier;
    private final String certificatePath;
    private final char[] certificatePassword;
    protected final MSEnvironment environment;



    public MSCertBasedTokenGenerator(Params params) {
        this.appId = params.appId;
        this.environment = params.environment;
        this.identifier = params.identifier;
        this.certificatePath = params.certPath;
        this.certificatePassword = params.certPassword;
    }


    // aquier a new access token
    @Override
    protected String aquierAccessToken() {
        try (HttpClient httpClient = HttpClient.newBuilder()
                .connectTimeout(Constants.connectTimeout)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .build()) {

            String authUrl = String.format(environment.getAuthUrl(), identifier);
            String clientAssertion = generateClientAssertion( authUrl);

            // Build token request body for Exchange Online
            String requestBody = String.format(payload,
                    appId,
                    clientAssertion,
                    getScopeUrl()
            );

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(authUrl))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            HttpResponse<String> response = httpClient.send(
                    request,
                    HttpResponse.BodyHandlers.ofString()
            );

            if (response.statusCode() != 200) {
                throw new PowerShellAuthenticationException("Authentication failed with status code: " + response.statusCode());
            }

            JsonNode tokenResponse = objectMapper.readTree(response.body());
            accessToken = tokenResponse.get("access_token").asText();

            // Calculate token expiration time (with 5 minute buffer)
            tokenExpiresAt = System.currentTimeMillis() +
                    (tokenResponse.get("expires_in").asLong() * 1000) -
                    Duration.ofMinutes(5).toMillis();

            return accessToken;
        } catch (Exception e) {
            throw new PowerShellAuthenticationException("Failed to authenticate: " + e.getMessage(), e);
        }
    }


    private String generateClientAssertion(final String audience) throws Exception {
        // Load certificate and private key from keystore
        KeyStore keystore = KeyStore.getInstance("PKCS12");
        try (FileInputStream fis = new FileInputStream(certificatePath)) {
            keystore.load(fis, certificatePassword);
        }

        // Get the private key and certificate
        String alias = keystore.aliases().nextElement();
        PrivateKey privateKey = (PrivateKey) keystore.getKey(alias, certificatePassword);
        X509Certificate certificate = (X509Certificate) keystore.getCertificate(alias);

        // Create JWT header with certificate thumbprint
        byte[] thumbprint = calculateThumbprint(certificate);
        JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                .x509CertSHA256Thumbprint(Base64URL.encode(thumbprint)) // Use SHA-256 thumbprint instead
                .build();

// Build JWT claims
        JWTClaimsSet claims = new JWTClaimsSet.Builder()
                .issuer(appId)
                .subject(appId)
                .audience(audience)
                .jwtID(UUID.randomUUID().toString())
                .issueTime(Date.from(Instant.now()))
                .claim("tid", identifier)
                .claim("roles", roles)
                .expirationTime(Date.from(Instant.now().plusSeconds(300))) // 5 minute expiration
                .build();

        // Sign the JWT
        SignedJWT signedJWT = new SignedJWT(header, claims);
        JWSSigner signer = new RSASSASigner(privateKey);
        signedJWT.sign(signer);

        return signedJWT.serialize();
    }

    private static byte[] calculateThumbprint(X509Certificate certificate)
            throws CertificateEncodingException, NoSuchAlgorithmException {
        byte[] encodedCert = certificate.getEncoded();
        java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
        return md.digest(encodedCert);  // Return raw bytes, not Base64 encoded
    }
}
