package io.syrix.protocols.utils.security.cert;

public class PowerShellCertBasedTeamsTokenGenerator extends MSCertBasedTokenGenerator {

    final private static String scopeUrl = "48ac35b8-9aa8-4d74-927d-1f4a14a0b239/.default";

    public PowerShellCertBasedTeamsTokenGenerator(Params params) {

        super(params);
    }

    @Override
    protected String getScopeUrl() {
        return scopeUrl;
    }
}