package io.syrix.protocols.utils;

/**
 * Constants for protocols, HTTP headers, and common API related values
 */
public class ProtocolConstants {
    // HTTP Headers
    public static final String HEADER_AUTHORIZATION = "Authorization";
    public static final String HEADER_CONSISTENCY_LEVEL = "ConsistencyLevel";
    public static final String HEADER_BEARER_PREFIX = "Bearer ";
    public static final String HEADER_RETRY_AFTER = "Retry-After";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_ACCESS_TOKEN = "Access-Token";
    
    // Content Types
    public static final String CONTENT_TYPE_JSON = "application/json";
    
    // Graph API Common Fields
    public static final String FIELD_ODATA_ID = "@odata.id";
    public static final String FIELD_ODATA_NEXT_LINK = "@odata.nextLink";
    public static final String FIELD_SUBSCRIPTION_ID = "subscriptionId";
    public static final String FIELD_TENANT_ID = "tid";
    public static final String FIELD_DISPLAY_NAME = "displayName";
    
    // Graph API Common Query Parameters
    public static final String PARAM_FILTER = "$filter";
    public static final String PARAM_SELECT = "$select";
    public static final String PARAM_EXPAND = "$expand";
    public static final String TOP_PARAM = "$top";
    // Common Error Messages
    public static final String ERROR_RATE_LIMIT = "Rate limit hit. Waiting {} seconds before retry";
    
    // Graph API Versions
    public static final String GRAPH_API_V1 = "v1.0";
    public static final String GRAPH_API_BETA = "beta";
    
    // Consistency Level Values
    public static final String CONSISTENCY_LEVEL_EVENTUAL = "eventual";
    
    // Authentication
    public static final String GRAPH_DEFAULT_SCOPE = "https://graph.microsoft.com/.default";
    public static final String AUTHORIZATION_POLICY = "/policies/authorizationPolicy";
}