package io.syrix.protocols.utils.security.refresh;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.constants.Constants;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.utils.security.MSTokenGenerator;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

public abstract class MSRefreshTokenBasedTokenGenerator extends MSTokenGenerator {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final String clientId;
    private final String clientSecret;
    protected final MSEnvironment environment;

    private String refreshToken;
    private String accessToken;
    private long tokenExpiresAt;

    public MSRefreshTokenBasedTokenGenerator (Params params) {
        this.clientId = params.clientId;
        this.clientSecret = params.clientSecret;
        this.refreshToken = params.refreshToken;
        this.environment = params.environment;
    }

    protected abstract String getAuthUrl();

    @Override
    protected String aquierAccessToken() {
        // Return cached token if still valid
        if (accessToken != null && System.currentTimeMillis() < tokenExpiresAt) {
            return accessToken;
        }

        try (HttpClient httpClient = HttpClient.newBuilder()
                .connectTimeout(Constants.connectTimeout)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .build()){

            // Build the request body depending on whether we have a refresh token
            String requestBody = String.format("client_id=%s&client_secret=%s&grant_type=refresh_token&refresh_token=%s&scope=%s",
                    clientId, clientSecret, refreshToken, getScopeUrl());


            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(getAuthUrl()))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            HttpResponse<String> response = httpClient.send(
                    request, HttpResponse.BodyHandlers.ofString()
            );

            if (response.statusCode() != 200) {
                throw new GraphClientException("Authentication failed with status code: " + response.statusCode());
            }

            JsonNode tokenResponse = objectMapper.readTree(response.body());
            accessToken = tokenResponse.get("access_token").asText();

            // Update refresh token if provided
            if (tokenResponse.has("refresh_token")) {
                refreshToken = tokenResponse.get("refresh_token").asText();
            }

            // Cache expiration time with a 5-minute safety buffer
            tokenExpiresAt = System.currentTimeMillis()
                    + (tokenResponse.get("expires_in").asLong() * 1000)
                    - Duration.ofMinutes(5).toMillis();

            return accessToken;
        } catch (Exception e) {
            throw new GraphClientException("Failed to authenticate: " + e.getMessage(), e);
        }
    }

}
