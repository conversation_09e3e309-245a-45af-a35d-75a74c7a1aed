package io.syrix.protocols.utils;

import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.BadRequestException;
import io.syrix.protocols.exception.HttpException;
import io.syrix.protocols.exception.ResourceNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Supplier;

public class Retry {
	private static final Logger logger = LoggerFactory.getLogger(Retry.class);
	private static final Duration RETRY_DELAY = Duration.ofSeconds(2);
	private static final Duration INITIAL_DELAY = Duration.ofSeconds(1);
	private static final Duration MAX_DELAY = Duration.ofSeconds(32);
	private static final Random random = new Random();
	/**
	 * Executes an operation with retry logic and exponential backoff.
	 */
	@FunctionalInterface
	public interface CheckedSupplier<T> {
		T get() throws Exception;
	}

    public static <T> CompletableFuture<T> executeWithRetry(Supplier<CompletableFuture<T>> operation, int maxRetries) {
        return executeWithRetry(operation, maxRetries, Executors.newVirtualThreadPerTaskExecutor());
    }

    public static <T> CompletableFuture<T> executeWithRetry(Supplier<CompletableFuture<T>> operation, int maxRetries, Executor executor) {
        return operation.get().exceptionallyCompose(ex -> {
            Throwable originThrowable = getOriginException(ex);

            if (!shouldRetry(originThrowable)) {
                return CompletableFuture.failedFuture(ex);
            }

            logger.trace("The attempt ended with an error. Attempts remaining: {}", maxRetries - 1, ex);

            if (maxRetries <= 0) {
                return CompletableFuture.failedFuture(ex);
            }

            return CompletableFuture.supplyAsync(operation, executor)
                    .thenCompose(future -> executeWithRetry(operation, maxRetries - 1, executor));
        });
    }

    private static Throwable getOriginException(Throwable throwable) {
        if (throwable instanceof CompletionException && throwable.getCause() != null) {
            return getOriginException(throwable.getCause());
        }
        return throwable;
    }

    private static boolean shouldRetry(Throwable ex) {
        return !(ex instanceof CompletionException
                 || ex instanceof ResourceNotFoundException
                 || ex instanceof BadRequestException
                 || ex instanceof AccessForbiddenException);
    }

    public static <T> T executeWithRetry(CheckedSupplier<T> operation, int maxRetries) {
        Exception lastException = null;
        int attempts = 0;

		while (attempts++ < maxRetries) {
			try {
				return operation.get();
			} catch (ResourceNotFoundException | BadRequestException | AccessForbiddenException e) {
				throw e; // Don't retry these errors
			} catch (Exception e) {
				lastException = e;
				if (attempts < maxRetries) {
					Duration delay = calculateBackoffDelay(attempts);
					logger.warn("Attempt {} failed, retrying in {} ms", attempts, delay.toMillis(), e);
					try {
						Thread.sleep(delay.toMillis());
					} catch (InterruptedException ex) {
						Thread.currentThread().interrupt();
						throw new RuntimeException(ex);
					}
					continue;
				}
				throw new HttpException("Request failed after " + maxRetries + " attempts", e);
			}
		}


		return null;
	}

	/**
	 * Calculates exponential backoff delay with jitter.
	 */
	private static Duration calculateBackoffDelay(int attempt) {
		// Calculate exponential delay: 2^n * initial delay
		long delayMillis = (long) (Math.pow(2, attempt - 1) * INITIAL_DELAY.toMillis());

		// Add jitter: randomly adjust by +/- 25%
		double jitter = 0.25;
		long jitterRange = (long) (delayMillis * jitter);
		long jitterAmount = random.nextLong(-jitterRange, jitterRange + 1);
		delayMillis += jitterAmount;

		// Cap at max delay
		return Duration.ofMillis(Math.min(delayMillis, MAX_DELAY.toMillis()));
	}
}
