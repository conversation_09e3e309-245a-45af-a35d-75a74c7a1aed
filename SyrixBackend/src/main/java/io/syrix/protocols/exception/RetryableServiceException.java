package io.syrix.protocols.exception;

public class RetryableServiceException extends GraphClientException {
	private final boolean retryable;

	public RetryableServiceException(String message, boolean retryable) {
		super(message);
		this.retryable = retryable;
	}

	public RetryableServiceException(String message, Throwable cause, boolean retryable) {
		super(message, cause);
		this.retryable = retryable;
	}

	public boolean isRetryable() {
		return retryable;
	}
}