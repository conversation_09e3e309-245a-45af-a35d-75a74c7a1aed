package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

/**
 @see <a href="https://learn.microsoft.com/en-us/powershell/module/sharepoint-online/set-spotenant?view=sharepoint-ps#-defaultsharinglinktype">SharingLinkType</a>
 */
public enum SharingLinkType implements MsEnum{
    NONE(0), //Respect the organization default sharing link type
    DIRECT(1), //Sets the default sharing link for this site to the Specific people link
    INTERNAL(2), //Sets the default sharing link for this site to the organization link or company shareable link
    ANONYMOUS_ACCESS(3); //Sets the default sharing link for this site to an Anonymous Access or Anyone link

    private final int intVal;

    SharingLinkType(int intVal) {
        this.intVal = intVal;
    }

    @Override
    public int asInt() {
        return intVal;
    }

    public static SharingLinkType fromInt(int intVal) {
        for (SharingLinkType value : values()) {
            if (value.asInt() == intVal) {
                return value;
            }
        }
        throw new IllegalArgumentException("Can not convert int "+intVal+" to SharingLinkType");
    }

}
