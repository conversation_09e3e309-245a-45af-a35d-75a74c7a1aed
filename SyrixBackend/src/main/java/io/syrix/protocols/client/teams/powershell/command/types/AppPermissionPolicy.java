package io.syrix.protocols.client.teams.powershell.command.types;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.protocols.client.teams.powershell.command.serialization.CatalogAppsTypeDeserializer;
import io.syrix.protocols.client.teams.powershell.command.serialization.CatalogAppsTypeSerializer;

import java.util.List;

@SuppressWarnings("unused")
public class AppPermissionPolicy implements TeamsPowerShellCommandResult {
    public String configId;
    public ConfigMetadata configMetadata;
    public String dataSource;
    public List<String> defaultCatalogApps;
    @JsonSerialize(using = CatalogAppsTypeSerializer.class)
    @JsonDeserialize(using = CatalogAppsTypeDeserializer.class)
    public CatalogAppsType defaultCatalogAppsType;
    public String description;
    public List<String> globalCatalogApps;
    @JsonSerialize(using = CatalogAppsTypeSerializer.class)
    @JsonDeserialize(using = CatalogAppsTypeDeserializer.class)
    public CatalogAppsType globalCatalogAppsType;
    public String identity;
    public Key key;
    public List<String> privateCatalogApps;
    @JsonSerialize(using = CatalogAppsTypeSerializer.class)
    @JsonDeserialize(using = CatalogAppsTypeDeserializer.class)
    public CatalogAppsType privateCatalogAppsType;

    public static class ConfigMetadata {
        public String authority;
    }

    public static class Key {
        public AuthorityId authorityId;
        public DefaultXml defaultXml;
        public SchemaId schemaId;
        public String scopeClass;
        public XmlRoot xmlRoot;
    }

    public static class AuthorityId {
        @JsonProperty(value = "Class")
        public String classType;
        public String instanceId;
        public XmlRoot xmlRoot;
    }

    public static class DefaultXml {
        public Object configObject;
        public Data data;
        public Boolean isModified;
        public SchemaId schemaId;
        public String signature;
    }

    public static class Data {
        public TeamsAppPermissionPolicyData teamsAppPermissionPolicy;
    }

    public static class TeamsAppPermissionPolicyData {
        @JsonProperty(value = "@xmlns")
        public String xmlns;
        public List<String> defaultCatalogApps;
        public List<String> globalCatalogApps;
        public List<String> privateCatalogApps;
    }

    public static class SchemaId {
        public XName xName;
    }

    public static class XName {
        public String name;
    }

    public static class XmlRoot {
        public String name;
    }

}

