package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

@JacksonXmlRootElement(localName = "Request")
public class Request {
    @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
    private final String xmlns = "http://schemas.microsoft.com/sharepoint/clientquery/2009";
    @JacksonXmlProperty(isAttribute = true)
    private boolean AddExpandoFieldTypeSuffix;
    @JacksonXmlProperty(isAttribute = true)
    private String SchemaVersion;
    @JacksonXmlProperty(isAttribute = true)
    private String LibraryVersion;
    @JacksonXmlProperty(isAttribute = true)
    private String ApplicationName;

    private Actions Actions;
    private ObjectPaths ObjectPaths;


    public void setAddExpandoFieldTypeSuffix(boolean addExpandoFieldTypeSuffix) {
        AddExpandoFieldTypeSuffix = addExpandoFieldTypeSuffix;
    }

    public void setSchemaVersion(String schemaVersion) {
        SchemaVersion = schemaVersion;
    }

    public void setLibraryVersion(String libraryVersion) {
        LibraryVersion = libraryVersion;
    }

    public void setApplicationName(String applicationName) {
        ApplicationName = applicationName;
    }

    public Actions getActions() {
        return Actions;
    }

    public void setActions(Actions actions) {
        Actions = actions;
    }

    public ObjectPaths getObjectPaths() {
        return ObjectPaths;
    }

    public void setObjectPaths(ObjectPaths objectPaths) {
        ObjectPaths = objectPaths;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private boolean AddExpandoFieldTypeSuffix;
        private String SchemaVersion;
        private String LibraryVersion;
        private String ApplicationName;
        private Actions Actions;
        private ObjectPaths ObjectPaths;

        public Builder() {}

        public Builder addExpandoFieldTypeSuffix(boolean addExpandoFieldTypeSuffix) {
            this.AddExpandoFieldTypeSuffix = addExpandoFieldTypeSuffix;
            return this;
        }

        public Builder schemaVersion(String schemaVersion) {
            this.SchemaVersion = schemaVersion;
            return this;
        }

        public Builder libraryVersion(String libraryVersion) {
            this.LibraryVersion = libraryVersion;
            return this;
        }

        public Builder applicationName(String applicationName) {
            this.ApplicationName = applicationName;
            return this;
        }

        public Builder actions(Actions actions) {
            this.Actions = actions;
            return this;
        }

        public Builder objectPaths(ObjectPaths objectPaths) {
            this.ObjectPaths = objectPaths;
            return this;
        }

        public Request build() {
            Request request = new Request();
            request.setAddExpandoFieldTypeSuffix(this.AddExpandoFieldTypeSuffix);
            request.setSchemaVersion(this.SchemaVersion);
            request.setLibraryVersion(this.LibraryVersion);
            request.setApplicationName(this.ApplicationName);
            request.setActions(this.Actions);
            request.setObjectPaths(this.ObjectPaths);
            return request;
        }
    }

}