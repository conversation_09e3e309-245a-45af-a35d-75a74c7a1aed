package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharePointShellCommandDataResult;

import java.util.List;

public class ShellCommandResult <T extends SharePointShellCommandDataResult> {
	private final List<T> data;
	private final SharePointShellCommand<T> command;
	private ShellCommandResult(List<T> data, SharePointShellCommand<T> command) {
		this.data = data;
		this.command = command;
	}

	public static <T extends SharePointShellCommandDataResult> ShellCommandResult<T> of(List<T> data, SharePointShellCommand<T> command) {
		return new ShellCommandResult<>(data, command);
	}

	public List<T> getData() {
		return data;
	}

	public SharePointShellCommand<T> getCommand() {
		return command;
	}
}
