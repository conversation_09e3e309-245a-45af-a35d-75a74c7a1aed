package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class InnerQuery {
    @JacksonXmlProperty(isAttribute = true)
    private boolean SelectAllProperties;
    private Properties Properties;

    public Properties getProperties() {
        return Properties;
    }

    public void setProperties(Properties properties) {
        Properties = properties;
    }

    public InnerQuery(boolean selectAllProperties, Property property) {
        this.SelectAllProperties = selectAllProperties;
        this.Properties = new Properties(property);
    }



}