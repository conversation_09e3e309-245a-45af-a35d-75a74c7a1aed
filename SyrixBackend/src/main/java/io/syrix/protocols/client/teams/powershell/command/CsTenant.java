package io.syrix.protocols.client.teams.powershell.command;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;

import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.client.teams.powershell.command.types.TenantInfo;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public class CsTenant implements TeamsPowerShellCommand<TenantInfo> {
    private static final String commandName = "Get-CsTenant";
    private static final String cmdletName = "Get-CsTenantObou_Get";

    private final ObjectMapper objectMapper;

    public CsTenant() {

        //todo Artur need to add serializator for OffsetDateTeme
        objectMapper = JsonMapper.builder()
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .build();

    }

    @Override
    public HttpMethod getMethod() {
        return HttpMethod.GET;
    }

    @Override
    public String getName() {
        return commandName;
    }

    @Override
    public String getBody() {
        return "";
    }

    @Override
    public List<Header> getHeaders() {
        return List.of(Header.of("X-MS-CmdletName", cmdletName));
    }

    @Override
    public String getEndPoint() {
        return "/Teams.Tenant/tenants?defaultpropertyset=Extended";
    }

    @Override
    public List<TenantInfo> parseResponse(String body) throws Exception {
        return List.of(objectMapper.readValue(body, TenantInfo.class));
    }
}
