package io.syrix.protocols.client.teams.powershell.command.types;

import io.syrix.common.utils.ValidationUtils;

/**
 * @see <a href="https://learn.microsoft.com/en-us/powershell/module/teams/set-csteamsmeetingpolicy?view=teams-ps#-autoadmittedusers">AutoAdmittedUsers</a>
 */

public enum AutoAdmittedUsers {
    EVERYONE("Everyone"), //if you'd like to admit anonymous users by default.
    EVERYONE_IN_COMPANY("EveryoneInCompany"), //if you would like meetings to place every external user in the lobby but allow all users in the company to join the meeting immediately.
    EVERYONE_IN_SAME_AND_FEDERATED_COMPANY("EveryoneInSameAndFederatedCompany"), // if you would like meetings to allow federated users to join like your company's users, but place all other external users in a lobby.
    EVERYONE_IN_COMPANY_EXCLUDING_GUESTS("EveryoneInCompanyExcludingGuests"), //if you would like meetings to place every external and guest users in the lobby but allow all other users in the company to join the meeting immediately.
    ORGANIZER_ONLY("OrganizerOnly"), //if you would like that only meeting organizers can bypass the lobby.
    INVITED_USERS("InvitedUsers"); //if you would like that only meeting organizers and invited users can bypass the lobby.

    private final String value;

    AutoAdmittedUsers(String value) {
        this.value = value;
    }

    public String asString() {
        return value;
    }

    public static AutoAdmittedUsers fromString(String value) {
        ValidationUtils.Strings.requireNonBlank(value, "value can not be blank");
        for (AutoAdmittedUsers val : AutoAdmittedUsers.values()) {
            if (val.asString().equals(value)) {
                return val;
            }
        }
        throw new IllegalArgumentException("Can not find enum:"+value);
    }

}
