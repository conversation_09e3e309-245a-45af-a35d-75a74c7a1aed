package io.syrix.protocols.client;

import io.syrix.main.Configuration;
import io.syrix.main.Context;
import io.syrix.protocols.model.MSEnvironment;

import java.time.Duration;

public class ClientFactory {
	public static MicrosoftGraphClient initGraphClient(Context context) {
		Configuration config = context.getConfig();
		String certPath = context.getCertPath();
		String certPass = context.getCertPass();
		MSEnvironment environment = context.getEnvironment();

		return MicrosoftGraphClient.builder()
				.withClientId(config.credentials().clientId())
				.withClientSecret(config.credentials().clientSecret())
				.withRefreshToken(config.credentials().refreshToken())
				.withEnvironment(environment)
				.withRequestTimeout(Duration.ofMinutes(5))
				.withMaxRetries(3)
				.withAppId(config.credentials().clientId())
				.withCertPath(certPath)
				.withCertPassword(certPass.toCharArray())
				.build();
	}

	public static PowerShellSharepointClient initSharepointPowerShellClient(Context context, String domain, String adminDomain) {
		Configuration config = context.getConfig();
		String certPath = context.getCertPath();
		String certPass = context.getCertPass();
		MSEnvironment environment = context.getEnvironment();

		return PowerShellSharepointClient.builder()
				.withAppId(config.credentials().clientId())
				.withDomain(domain)
				.withAdminDomain(adminDomain)
				.withCertificatePath(certPath)
				.withCertificatePassword(certPass.toCharArray())
				.withEnvironment(environment)
				.withConnectTimeout(Duration.ofSeconds(30))
				.withMaxRetries(3)
				.build();
	}

	public static PowerShellClient initPowerShellClient(Context context, String tenantId, String domain) {
		Configuration config = context.getConfig();
		String certPath = context.getCertPath();
		String certPass = context.getCertPass();
		MSEnvironment environment = context.getEnvironment();

		return PowerShellClient.builder()
				.withAppId(config.credentials().clientId())
				.withTenantId(tenantId)
				.withCertificatePath(certPath)
				.withCertificatePassword(certPass.toCharArray())
				.withEnvironment(environment)
				.withEndpointPath("/adminapi/beta")  // Use adminapi endpoint
				.withRequestTimeout(Duration.ofMinutes(5))
				.withConnectTimeout(Duration.ofSeconds(30))
				.withMaxRetries(3)
				.withDomain(domain)
				.build();
	}

	public static PowerShellTeamsClient initPowerShellTeamsClient(Context context, String tenantId) {
		Configuration config = context.getConfig();
		String certPath = context.getCertPath();
		String certPass = context.getCertPass();
		MSEnvironment environment = context.getEnvironment();


		return PowerShellTeamsClient.builder()
				.withAppId(config.credentials().clientId())
				.withTenantId(tenantId)
				.withCertificatePath(certPath)
				.withCertificatePassword(certPass.toCharArray())
				.withEnvironment(environment)
				.withConnectTimeout(Duration.ofSeconds(30))
				.withMaxRetries(3)
				.build();
	}
}
