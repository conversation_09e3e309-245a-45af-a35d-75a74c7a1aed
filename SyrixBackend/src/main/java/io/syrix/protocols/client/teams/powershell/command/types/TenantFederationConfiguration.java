package io.syrix.protocols.client.teams.powershell.command.types;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

@SuppressWarnings("unused")
public class TenantFederationConfiguration implements TeamsPowerShellCommandResult {
    public Boolean allowFederatedUsers;
    public Boolean allowPublicUsers;
    public Boolean allowTeamsConsumer;
    public Boolean allowTeamsConsumerInbound;
    public Boolean allowTeamsSms;
    public Map<String, Object> allowedDomains;
    public List<Object> allowedTrialTenantDomains;
    public Boolean blockAllSubdomains;
    public List<Object> blockedDomains;
    public String configId;
    public ConfigMetadata configMetadata;
    public Boolean customizeFederation;
    public String dataSource;
    public String externalAccessWithTrialTenants;
    public String domainBlockingForMDOAdminsInTeams;
    public String identity;
    public Key key;
    public Boolean restrictTeamsConsumerToExternalUserProfiles;
    public Boolean sharedSipAddressSpace;
    public Boolean treatDiscoveredPartnersAsUnverified;

    public static class ConfigMetadata {
        public String authority;
    }

    public static class Key {
        public AuthorityId authorityId;
        public DefaultXml defaultXml;
        public SchemaId schemaId;
        public String scopeClass;
        public XmlRoot xmlRoot;
    }

    public static class AuthorityId {
        @JsonProperty(value = "Class")
        public String classType;
        public String instanceId;
        public XmlRoot xmlRoot;
    }

    public static class DefaultXml {
        public Object configObject;
        public Data data;
        public Boolean isModified;
        public SchemaId schemaId;
        public String signature;
    }

    public static class Data {
        public TenantFederationSettingsData tenantFederationSettings;
    }


    public static class TenantFederationSettingsData {
        @JsonProperty(value = "@xmlns")
        public String xmlns;
        public Map<String, Object> allowedDomains;
        public List<String> allowedTrialTenantDomains;
        public List<String> blockedDomains;
    }

    public static class SchemaId {
        public XName xName;
    }

    public static class XName {
        public String name;
    }

    public static class XmlRoot {
        public String name;
    }

}

