package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import io.syrix.protocols.client.teams.powershell.command.types.CatalogAppsType;

import java.io.IOException;

public class CatalogAppsTypeSerializer extends JsonSerializer<CatalogAppsType> {

    @Override
    public void serialize(CatalogAppsType value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(value.asString());
    }
}
