package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.*;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.model.HttpMethod;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class SPShellCommand<T extends SharePointShellCommandDataResult> implements SharePointShellCommand<T> {
	private static final ObjectMapper objectMapper;

	private String cmdName;
	private String tag;
	private String body;
	private HttpMethod method;
	private List<Header> heads;

	private ParseFunction<String, T> parseResponse;

	private List<Parameter> parameters;

	static {
		objectMapper = JsonMapper.builder()
				.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
				.build();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
	}

	public SPShellCommand() {

	}

	@Override
	public String getName() {
		return cmdName;
	}

	@Override
	public String getTag() {
		return tag;
	}

	@Override
	public String getBody() {
		return body;
	}

	@Override
	public HttpMethod getMethod() {
		return method;
	}

	@Override
	public List<Header> getHeaders() {
		return heads;
	}

	@Override
	public List<T> parseResponse(String body) throws Exception {
		return parseResponse.parse(body);
	}

	@Override
	public List<Parameter> getParameters() {
		return parameters;
	}

	public SPShellCommand<T> addParameter(Parameter parameter) {
		if (parameters == null) {
			parameters = new ArrayList<>();
		}
		parameters.add(parameter);
		return this;
	}


	private interface ParseFunction<String, T> {
		List<T> parse(String body) throws Exception;
	}

	//------------------------------------- Builder
	private static <T extends SharePointShellCommandDataResult> Builder<T> builder(Class<T> tClass) {
		return new Builder<>();
	}

	private static class Builder<T extends SharePointShellCommandDataResult> {
		private String cmdName;
		private String tag;
		private String body;
		private HttpMethod method;
		private List<Header> heads;
		private ParseFunction<String, T> parseResponse;
		private List<Parameter> parameters;

		public Builder() {
		}

		public Builder<T> cmdName(String cmdName) {
			this.cmdName = cmdName;
			return this;
		}

		public Builder<T> tag(String tag) {
			this.tag = tag;
			return this;
		}

		public Builder<T> body(String body) {
			this.body = body;
			return this;
		}

		public Builder<T> method(HttpMethod method) {
			this.method = method;
			return this;
		}

		public Builder<T> heads(List<Header> heads) {
			this.heads = heads;
			return this;
		}

		public Builder<T> parseResponse(ParseFunction<String, T> parseResponse) {
			this.parseResponse = parseResponse;
			return this;
		}

		public Builder<T> addParameter(Parameter parameter) {
			if (parameters == null) {
				parameters = new ArrayList<>();
			}
			parameters.add(parameter);
			return this;
		}

		public SPShellCommand<T> build() {
			SPShellCommand<T> command = new SPShellCommand<>();
			command.cmdName = this.cmdName;
			command.tag = this.tag;
			command.body = this.body;
			command.heads = this.heads;
			command.method = this.method;
			command.parseResponse = this.parseResponse;
			command.parameters = parameters;
			return command;
		}
	}

	private static JsonNode selectObject(JsonNode jsonNode) {
		if (jsonNode.isArray()) {
			ArrayNode arrayNode = (ArrayNode) jsonNode;
			for (JsonNode element : arrayNode) {
				if (element.isObject() && element.has("_ObjectType_")) {
					return element;
				}
			}
		}
		throw new RuntimeException("Can not select site");
	}


	//-------------------------------------------- PnP-Tenant
	public static class PnPTenant {
		public static SPShellCommand<TenantProperties> GET_ALL() {
			return SPShellCommand.builder(TenantProperties.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:1.12:Get-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:1.12:Get-PnPTenant"),
							Header.of("Content-Type", "text/xml")
					))
					.body("""
							<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0"
							         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
							    <Actions>
							        <ObjectPath Id="17" ObjectPathId="16"/>
							        <Query Id="18" ObjectPathId="16">
							            <Query SelectAllProperties="true">
							                <Properties/>
							            </Query>
							        </Query>
							    </Actions>
							    <ObjectPaths>
							        <Constructor Id="16" TypeId="{268004ae-ef6b-4e9b-8425-127220d84719}"/>
							    </ObjectPaths>
							</Request>
							""")
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						jsonNode = selectObject(jsonNode);
						return List.of(objectMapper.treeToValue(jsonNode, TenantProperties.class));
					})
					.build();
		}

		public static SPShellCommand<AllowToBeDeleted> GET_ALL_ALLOW_TO_BE_DELETED() {
			return SPShellCommand.builder(AllowToBeDeleted.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:1.12:Get-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:1.12:Get-PnPTenant"),
							Header.of("Content-Type", "text/xml"),
							Header.of("X-RequestForceAuthentication", "true")
					))
					.body("""
							<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
							    <Actions>
							        <StaticMethod TypeId="{9efa17eb-0d34-4f69-a085-5cc3f802439e}" Name="GetAllowFilesWithKeepLabelToBeDeletedSPO" Id="5"/>
							        <StaticMethod TypeId="{9efa17eb-0d34-4f69-a085-5cc3f802439e}" Name="GetAllowFilesWithKeepLabelToBeDeletedODB" Id="6"/>
							    </Actions>
							    <ObjectPaths/>
							</Request>
							""")
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);

						AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();

						for (int i = 0; i < jsonNode.size() - 1; i++) {
							if (jsonNode.get(i).isInt()) {
								int value = jsonNode.get(i).asInt();
								if (value == 5) {
									allowToBeDeleted.allowToBeDeletedSPO = jsonNode.get(i + 1).asBoolean();
								} else if (value == 6) {
									allowToBeDeleted.allowToBeDeletedODB = jsonNode.get(i + 1).asBoolean();
								}
							}
						}
						return List.of(allowToBeDeleted);
					})
					.build();
		}

		private static final String setBody = """
				<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0"
				         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
				    <Actions>
				        <SetProperty Id="1" ObjectPathId="2" Name="{0}">
				            <Parameter Type="{3}">{1}</Parameter>
				        </SetProperty>
				    </Actions>
				    <ObjectPaths>
				        <Identity Id="2" Name="{2}"/>
				    </ObjectPaths>
				</Request>
				""";

		private static final CollectionType generalResultType = objectMapper.getTypeFactory().constructCollectionType(List.class, GeneralResult.class);

		private static SPShellCommand<GeneralResult> SET(String objectIdentity, String propName, String type, String value, Parameter parameter) {
			return SPShellCommand.builder(GeneralResult.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:2.12:Set-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:2.12:Set-PnPTenant"),
							Header.of("Content-Type", "text/xml")
					))
					.body(MessageFormat.format(setBody, propName, value, objectIdentity.replace("\n", "&#xA;"), type))
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						return objectMapper.treeToValue(jsonNode, generalResultType);
					})
					.addParameter(parameter)
					.build();
		}

		public static SPShellCommand<GeneralResult> SET(String objectIdentity, String propName, MsEnum msEnum, MsEnum prevEnum) {
			return SET(objectIdentity, propName, "Enum", Integer.toString(msEnum.asInt()), Parameter.of(propName, prevEnum, msEnum));
		}

		public static SPShellCommand<GeneralResult> SET(String objectIdentity, String propName, Integer value, Integer prevValue) {
			return SET(objectIdentity, propName, "Int32", Integer.toString(value), Parameter.of(propName, prevValue, value));
		}

		public static SPShellCommand<GeneralResult> SET(String objectIdentity, String propName, Boolean value, Boolean prevValue) {
			return SET(objectIdentity, propName, "Boolean", Boolean.toString(value),  Parameter.of(propName, prevValue, value));
		}

		private static final String setBodyRestrictionModeNone = """
				<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName=".NET Library"
				         xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
				    <Actions>
				        <SetProperty Id="1" ObjectPathId="2" Name="RequireAcceptingAccountMatchInvitedAccount">
				            <Parameter Type="Boolean">true</Parameter>
				        </SetProperty>
				        <SetProperty Id="3" ObjectPathId="4" Name="SharingDomainRestrictionMode">
				            <Parameter Type="Enum">0</Parameter>
				        </SetProperty>
				    </Actions>
				    <ObjectPaths>
				        <Identity Id="5" Name="{0}"/>
				    </ObjectPaths>
				</Request>
				""";

		public static SPShellCommand<GeneralResult> SET_RestrictionModeNone(String objectIdentity) {
			return SPShellCommand.builder(GeneralResult.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:2.12:Set-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:2.12:Set-PnPTenant"),
							Header.of("Content-Type", "text/xml")
					))
					.body(MessageFormat.format(setBodyRestrictionModeNone, objectIdentity.replace("\n", "&#xA;")))
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						return objectMapper.treeToValue(jsonNode, generalResultType);
					})
					.build();
		}

		private static final String getSetBodyRestrictionModeAllowList = """
				<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName=".NET Library"
				         xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
				    <Actions>
				        <SetProperty Id="449" ObjectPathId="445" Name="RequireAcceptingAccountMatchInvitedAccount">
				            <Parameter Type="Boolean">true</Parameter>
				        </SetProperty>
				        <SetProperty Id="450" ObjectPathId="445" Name="SharingAllowedDomainList">
				            <Parameter Type="String">{0}</Parameter>
				        </SetProperty>
				        <SetProperty Id="451" ObjectPathId="445" Name="SharingDomainRestrictionMode">
				            <Parameter Type="Enum">1</Parameter>
				        </SetProperty>
				    </Actions>
				    <ObjectPaths>
				        <Identity Id="445" Name="{1}"/>
				    </ObjectPaths>
				</Request>
				""";

		public static SPShellCommand<GeneralResult> SET_RestrictionModeAllowList(String objectIdentity, String allowDomainList) {
			return SPShellCommand.builder(GeneralResult.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:2.12:Set-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:2.12:Set-PnPTenant"),
							Header.of("Content-Type", "text/xml")
					))
					.body(MessageFormat.format(getSetBodyRestrictionModeAllowList, allowDomainList, objectIdentity.replace("\n", "&#xA;")))
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						return objectMapper.treeToValue(jsonNode, generalResultType);
					})
					.build();
		}

		private static final String getSetBodyRestrictionModeBlockList = """
				<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName=".NET Library"
				         xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
				    <Actions>
				        <SetProperty Id="456" ObjectPathId="452" Name="RequireAcceptingAccountMatchInvitedAccount">
				            <Parameter Type="Boolean">true</Parameter>
				        </SetProperty>
				        <SetProperty Id="457" ObjectPathId="452" Name="SharingBlockedDomainList">
				            <Parameter Type="String">{0}</Parameter>
				        </SetProperty>
				        <SetProperty Id="458" ObjectPathId="452" Name="SharingDomainRestrictionMode">
				            <Parameter Type="Enum">2</Parameter>
				        </SetProperty>
				    </Actions>
				    <ObjectPaths>
				        <Identity Id="452" Name="{1}"/>
				    </ObjectPaths>
				</Request>
				""";

		public static SPShellCommand<GeneralResult> SET_RestrictionModeBlockList(String objectIdentity, String blockDomainList) {
			return SPShellCommand.builder(GeneralResult.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenant")
					.tag("PnPPS:2.12:Set-PnPTenant")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:2.12:Set-PnPTenant"),
							Header.of("Content-Type", "text/xml")
					))
					.body(MessageFormat.format(getSetBodyRestrictionModeBlockList, blockDomainList, objectIdentity.replace("\n", "&#xA;")))
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						return objectMapper.treeToValue(jsonNode, generalResultType);
					})
					.build();
		}

		//-------------------------------------------- PnP-Tenant
	}

	public static class PnPTenantSite {

		public static SPShellCommand<SiteProperties> GET(String domain) {
			return SPShellCommand.builder(SiteProperties.class)
					.method(HttpMethod.POST)
					.cmdName("Get-PnPTenantSite")
					.tag("PnPPS:1.12:Get-PnPTenantSite")
					.heads(List.of(
							Header.of("X-ClientService-ClientTag", "PnPPS:1.12:Get-PnPTenantSite"),
							Header.of("Content-Type", "text/xml"),
							Header.of("X-RequestForceAuthentication", "true")
					))
					.body("""
							<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0"
							         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
							    <Actions>
							        <ObjectPath Id="8" ObjectPathId="7"/>
							        <ObjectPath Id="10" ObjectPathId="9"/>
							        <Query Id="11" ObjectPathId="9">
							            <Query SelectAllProperties="true">
							                <Properties/>
							            </Query>
							        </Query>
							    </Actions>
							    <ObjectPaths>
							        <Constructor Id="7"
							                     TypeId="{268004ae-ef6b-4e9b-8425-127220d84719}"/>
							        <Method Id="9" ParentId="7" Name="GetSitePropertiesByUrl">
							            <Parameters>
							                <Parameter Type="String">%s</Parameter>
							                <Parameter Type="Boolean">false</Parameter>
							            </Parameters>
							        </Method>
							    </ObjectPaths>
							</Request>
							""".replace("%s", domain))
					.parseResponse(body -> {
						JsonNode jsonNode = objectMapper.readTree(body);
						jsonNode = selectObject(jsonNode);
						return List.of(objectMapper.treeToValue(jsonNode, SiteProperties.class));
					})
					.build();
		}
	}

}
