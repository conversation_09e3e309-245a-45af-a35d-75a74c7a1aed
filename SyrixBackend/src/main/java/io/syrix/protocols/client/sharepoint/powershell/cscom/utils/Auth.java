package io.syrix.protocols.client.sharepoint.powershell.cscom.utils;

import org.apache.commons.lang3.tuple.Pair;

public class Auth {
    private final Pair<String, String> pair;

    private Auth(String token, String digest) {
        pair = Pair.of(token, digest);
    }

    public static Auth of(String token, String digest) {
        return new Auth(token, digest);
    }

    public String token() {
        return pair.getLeft();
    }

    public String digest() {
        return pair.getRight();
    }
}
