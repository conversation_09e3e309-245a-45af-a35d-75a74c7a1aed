package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;

import java.util.List;
//Get-SPOSite
public class TapsGetSitePropertiesCommand implements PowerShellCommand {
    private static final String TAG = "TAPS (16.0.24810.0)";

    @Override
    public String getTag() {
        return TAG;
    }

    public String getBody() {
        return """
                <Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName="SharePoint Online PowerShell (16.0.24810.0)" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
                    <Actions>
                        <ObjectPath Id="8" ObjectPathId="7"/>
                        <ObjectPath Id="10" ObjectPathId="9"/>
                        <Query Id="11" ObjectPathId="9">
                            <Query SelectAllProperties="true">
                                <Properties>
                                    <Property Name="NextStartIndexFromSharePoint" ScalarProperty="true"/>
                                </Properties>
                            </Query>
                            <ChildItemQuery SelectAllProperties="true">
                                <Properties/>
                            </ChildItemQuery>
                        </Query>
                    </Actions>
                    <ObjectPaths>
                        <Constructor Id="7" TypeId="{268004ae-ef6b-4e9b-8425-127220d84719}"/>
                        <Method Id="9" ParentId="7" Name="GetSitePropertiesFromSharePoint">
                            <Parameters>
                                <Parameter Type="Null"/>
                                <Parameter Type="Boolean">false</Parameter>
                            </Parameters>
                        </Method>
                    </ObjectPaths>
                </Request>
                """;

    }

    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-ClientService-ClientTag", TAG),
                Header.of("Content-Type", "text/xml"),
                Header.of("X-RequestForceAuthentication", "true")
        );
    }
}
