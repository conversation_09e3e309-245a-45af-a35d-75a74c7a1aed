package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import io.syrix.protocols.client.teams.powershell.command.types.CatalogAppsType;

import java.io.IOException;

public class CatalogAppsTypeDeserializer extends JsonDeserializer<CatalogAppsType> {
    @Override
    public CatalogAppsType deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText();
        return CatalogAppsType.fromString(value);
    }
}
