package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.products.microsoft.sharepoint.serialization.SharepointGuidSerializer;
import io.syrix.products.microsoft.sharepoint.serialization.SharepointDateSerializer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("unused")
public class SiteProperties implements SharePointShellCommandDataResult {
    @JsonProperty("_ObjectType_")
    public String objectType;
    @JsonProperty("_ObjectIdentity_")
    public String objectIdentity;
    public Boolean allowDownloadingNonWebViewableFiles;
    public Boolean allowWebPropertyBagUpdateWhenDenyAddAndCustomizePagesIsEnabled;
    public Boolean allowEditing;
    public Boolean allowFileArchive;
    public Boolean allowSelfServiceUpgrade;
    public Integer anonymousLinkExpirationInDays;
    public Boolean applyToExistingDocumentLibraries;
    public Boolean applyToNewDocumentLibraries;
    public String archivedBy;
    public String archivedTime;
    public String archiveStatus;
    public String authContextStrength;
    public Boolean authenticationContextLimitedAccess;
    public String authenticationContextName;
    public Integer averageResourceUsage;
    public Integer blockDownloadLinksFileType;
    public String blockDownloadMicrosoft365GroupIds;
    public Boolean blockDownloadPolicy;
    public String blockDownloadPolicyFileTypeIds;
    public Integer blockGuestsAsSiteAdmin;
    public Long bonusDiskQuota;
    public Boolean clearGroupId;
    public Boolean clearRestrictedAccessControl;
    public Boolean commentsOnSitePagesDisabled;
    public Integer compatibilityLevel;
    public Integer conditionalAccessPolicy;
    public String createdTime;
    public Integer currentResourceUsage;
    public Integer defaultLinkPermission;
    public Boolean defaultLinkToExistingAccess;
    public Boolean defaultLinkToExistingAccessReset;
    public Integer defaultShareLinkRole;
    public Integer defaultShareLinkScope;
    public Integer defaultSharingLinkType;
    public Integer denyAddAndCustomizePages;
    public String description;
    public Integer disableAppViews;
    public Integer disableCompanyWideSharingLinks;
    public Integer disableFlows;
    public Boolean disableSharingForNonOwnersStatus;
    public Boolean enableAutoExpirationVersionTrim;
    public Boolean excludeBlockDownloadPolicySiteOwners;
    public List<String> excludeBlockDownloadSharePointGroups;
    public List<String> excludedBlockDownloadGroupIds;
    public Integer expireVersionsAfterDays;
    public Integer externalUserExpirationInDays;
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String groupId;
    public String groupOwnerLoginName;
    public Boolean hasHolds;
    public Boolean hidePeoplePreviewingFiles;
    public Boolean hidePeopleWhoHaveListsOpen;
    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String hubSiteId;
    public String ibMode;
    public List<String> ibSegments;
    public String ibSegmentsToAdd;
    public String ibSegmentsToRemove;
    public Boolean inheritVersionPolicyFromTenant;
    public Boolean isGroupOwnerSiteAdmin;
    public Boolean isHubSite;
    public Boolean isTeamsChannelConnected;
    public Boolean isTeamsConnected;
    public Object informationSegment;
    @JsonSerialize(using = SharepointDateSerializer.class)
    public String lastContentModifiedDate;
    public Integer lcid;
    public Integer limitedAccessFileType;
    public Boolean listsShowHeaderAndNavigation;
    public String lockIssue;
    public Integer lockReason;
    public String lockState;
    public Integer loopDefaultSharingLinkRole;
    public Integer loopDefaultSharingLinkScope;
    public Integer majorVersionLimit;
    public Integer majorWithMinorVersionsLimit;
    public Integer mediaTranscription;
    public Integer overrideBlockUserInfoVisibility;
    public Boolean overrideSharingCapability;
    public Boolean overrideTenantAnonymousLinkExpirationPolicy;
    public Boolean overrideTenantExternalUserExpirationPolicy;
    public String owner;
    public String ownerEmail;
    public String ownerLoginName;
    public String ownerName;
    public Integer pwaEnabled;
    public Object protectionLevelName;
    public Boolean readOnlyAccessPolicy;
    public Boolean readOnlyForBlockDownloadPolicy;
    public Boolean readOnlyForUnmanagedDevices;
//    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String relatedGroupId;
    public Boolean requestFilesLinkEnabled;
    public Integer requestFilesLinkExpirationInDays;
    public Boolean restrictContentOrgWideSearch;
    public Boolean restrictedAccessControl;
    public List<String> restrictedAccessControlGroups;
    public String restrictedAccessControlGroupsToAdd;
    public String restrictedAccessControlGroupsToRemove;
    public Boolean restrictedContentDiscoveryforCopilotAndAgents;
    public Integer restrictedToRegion;
    public Integer sandboxedCodeActivationCapability;
//    @JsonSerialize(using = SharepointGuidSerializer.class)
    public String sensitivityLabel;
    public String sensitivityLabel2;
    public Boolean setOwnerWithoutUpdatingSecondaryAdmin;
    public String sharingAllowedDomainList;
    public String sharingBlockedDomainList;
    public Integer sharingCapability;
    public Integer sharingDomainRestrictionMode;
    public Boolean sharingLockDownCanBeCleared;
    public Boolean sharingLockDownEnabled;
    public Boolean showPeoplePickerSuggestionsForGuestUsers;
    public Integer siteDefinedSharingCapability;
    public String siteId;
    public Boolean socialBarOnSitePagesDisabled;
    public String status;
    public Integer storageMaximumLevel;
    public String storageQuotaType;
    public Integer storageUsage;
    public Integer storageWarningLevel;
    public Integer teamsChannelType;
    public String template;
    public Integer timeZoneId;
    public String title;
    public String titleTranslations;
    public String url;
    public Integer userCodeMaximumLevel;
    public Integer userCodeWarningLevel;
    public Integer versionCount;
    public Integer versionSize;
    public Integer websCount;

    private final Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        additionalProperties.put(name, value);
    }
}

