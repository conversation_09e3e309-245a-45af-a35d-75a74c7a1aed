package io.syrix.protocols.client.teams.powershell.command.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.protocols.client.teams.powershell.command.serialization.AutoAdmittedUsersDeserializer;
import io.syrix.protocols.client.teams.powershell.command.serialization.AutoAdmittedUsersSerializer;

@SuppressWarnings("unused")
public class MeetingPolicy implements TeamsPowerShellCommandResult {
    public String aiInterpreter;
    public Boolean allowAnnotations;
    public Boolean allowAnonymousUsersToDialOut;
    public Boolean allowAnonymousUsersToJoinMeeting;
    public Boolean allowAnonymousUsersToStartMeeting;
    public Boolean allowAvatarsInGallery;
    public Boolean allowBreakoutRooms;
    public Boolean allowCarbonSummary;
    public String allowCartCaptionsScheduling;
    public Boolean allowChannelMeetingScheduling;
    public Boolean allowCloudRecording;
    public String allowDocumentCollaboration;
    public String allowEngagementReport;
    public Boolean allowExternalNonTrustedMeetingChat;
    public Boolean allowExternalParticipantGiveRequestControl;
    public Boolean allowIPAudio;
    public Boolean allowIPVideo;
    public Boolean allowImmersiveView;
    public Boolean allowLocalRecording;
    public Boolean allowMeetNow;
    public Boolean allowMeetingCoach;
    public Boolean allowMeetingReactions;
    public Boolean allowMeetingRegistration;
    public Boolean allowNDIStreaming;
    public Boolean allowNetworkConfigurationSettingsLookup;
    public Boolean allowOrganizersToOverrideLobbySettings;
    public Boolean allowOutlookAddIn;
    public Boolean allowPSTNUsersToBypassLobby;
    public Boolean allowParticipantGiveRequestControl;
    public Boolean allowPowerPointSharing;
    public Boolean allowPrivateMeetNow;
    public Boolean allowPrivateMeetingScheduling;
    public Boolean allowRecordingStorageOutsideRegion;
    public String allowScreenContentDigitization;
    public Boolean allowSharedNotes;
    public String allowTasksFromTranscript;
    public String allowTrackingInReport;
    public Boolean allowTranscription;
    public Boolean allowWatermarkCustomizationForCameraVideo;
    public Boolean allowWatermarkCustomizationForScreenSharing;
    public Boolean allowWatermarkForCameraVideo;
    public Boolean allowWatermarkForScreenSharing;
    public Boolean allowWhiteboard;
    public String allowedStreamingMediaInput;
    public String allowedUsersForMeetingContext;
    public String allowedUsersForMeetingDetails;
    public String anonymousUserAuthenticationMethod;
    public String attendeeIdentityMasking;
    public String audibleRecordingNotification;
    @JsonSerialize(using = AutoAdmittedUsersSerializer.class)
    @JsonDeserialize(using = AutoAdmittedUsersDeserializer.class)
    public AutoAdmittedUsers autoAdmittedUsers;
    public String autoRecording;
    public String automaticallyStartCopilot;
    public String blockedAnonymousJoinClientTypes;
    public String captchaVerificationForMeetingJoin;
    public String channelRecordingDownload;
    public String configId;
    public ConfigMetadata configMetadata;
    public String connectToMeetingControls;
    public String contentSharingInExternalMeetings;
    public String copilot;
    public Boolean copyRestriction;
    public String dataSource;
    public String description;
    public String designatedPresenterRoleMode;
    public Boolean detectSensitiveContentDuringScreenSharing;
    public String enrollUserOverride;
    public String explicitRecordingConsent;
    public String externalMeetingJoin;
    public String forceStreamingAttendeeMode;
    public String ipAudioMode;
    public String ipVideoMode;
    public String identity;
    public String infoShownInReportMode;
    public Key key;
    public String liveCaptionsEnabledType;
    public String liveInterpretationEnabledType;
    public String liveStreamingMode;
    public String lobbyChat;
    public Integer mediaBitRateKb;
    public String meetingChatEnabledType;
    public String meetingInviteLanguages;
    public Integer newMeetingRecordingExpirationDays;
    public String noiseSuppressionForDialInParticipants;
    public String participantNameChange;
    public String preferredMeetingProviderForIslandsMode;
    public String qnaEngagementMode;
    public String realTimeText;
    public String recordingStorageMode;
    public String roomAttributeUserOverride;
    public String roomPeopleNameUserOverride;
    public String screenSharingMode;
    public String smsNotifications;
    public String speakerAttributionMode;
    public String streamingAttendeeMode;
    public String teamsCameraFarEndPTZMode;
    public String usersCanAdmitFromLobby;
    public String videoFiltersMode;
    public String voiceIsolation;
    public String voiceSimulationInInterpreter;
    public String participantSlideControl;
    public String watermarkForAnonymousUsers;
    public Integer watermarkForCameraVideoOpacity;
    public String watermarkForCameraVideoPattern;
    public Integer watermarkForScreenSharingOpacity;
    public String watermarkForScreenSharingPattern;
    public String whoCanRegister;

    public static class ConfigMetadata {
        public String authority;
    }

    public static class Key {
        public AuthorityId authorityId;
        public DefaultXml defaultXml;
        public SchemaId schemaId;
        public String scopeClass;
        public XmlRoot xmlRoot;
    }

    public static class AuthorityId {
        @JsonProperty(value = "Class")
        public String classType;
        public String instanceId;
        public XmlRoot xmlRoot;
    }

    public static class DefaultXml {
        public Object configObject;
        public Data data;
        public Boolean isModified;
        public SchemaId schemaId;
        public String signature;
    }

    public static class Data {
        public TeamsMeetingPolicyData teamsMeetingPolicy;
    }

    public static class TeamsMeetingPolicyData {
        @JsonProperty(value = "@xmlns")
        public String xmlns;
    }

    public static class SchemaId {
        public XName xName;
    }

    public static class XName {
        public String name;
    }

    public static class XmlRoot {
        public String name;
    }
}



