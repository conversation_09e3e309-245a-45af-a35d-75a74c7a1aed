package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

public enum AnonymousLinkType implements MsEnum {
    NONE(0),
    VIEW(1),
    EDIT(2);

    private final int intVal;

    AnonymousLinkType(int intVal) {
        this.intVal = intVal;
    }

    @Override
    public int asInt() {
        return intVal;
    }

    public static AnonymousLinkType fromInt(int intVal) {
        for (AnonymousLinkType value : values()) {
            if (value.asInt() == intVal) {
                return value;
            }
        }
        throw new IllegalArgumentException("Can not convert int "+intVal+" to AnonymousLinkType");
    }
}
