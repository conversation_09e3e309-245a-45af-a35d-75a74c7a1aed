package io.syrix.protocols.client.graph.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class OrganizationDomains extends GraphResponse {

    @JsonProperty("@odata.context")
    private String odataContext;

    @JsonProperty("value")
    private List<Value> value;

    // Вложенный класс для элемента value
    public static class Value {

        @JsonProperty("verifiedDomains")
        private List<VerifiedDomain> verifiedDomains;

        public List<VerifiedDomain> getVerifiedDomains() {
            return verifiedDomains;
        }

        public void setVerifiedDomains(List<VerifiedDomain> verifiedDomains) {
            this.verifiedDomains = verifiedDomains;
        }

        @Override
        public String toString() {
            return "Value{" +
                   "verifiedDomains=" + verifiedDomains +
                   '}';
        }
    }

    // Вложенный класс для verifiedDomains
    public static class VerifiedDomain {

        @JsonProperty("capabilities")
        private String capabilities;

        @JsonProperty("isDefault")
        private boolean isDefault;

        @JsonProperty("isInitial")
        private boolean isInitial;

        @JsonProperty("name")
        private String name;

        @JsonProperty("type")
        private String type;

        public String getCapabilities() {
            return capabilities;
        }

        public void setCapabilities(String capabilities) {
            this.capabilities = capabilities;
        }

        public boolean isDefault() {
            return isDefault;
        }

        public void setDefault(boolean isDefault) {
            this.isDefault = isDefault;
        }

        public boolean isInitial() {
            return isInitial;
        }

        public void setInitial(boolean isInitial) {
            this.isInitial = isInitial;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return "VerifiedDomain{" +
                   "capabilities='" + capabilities + '\'' +
                   ", isDefault=" + isDefault +
                   ", isInitial=" + isInitial +
                   ", name='" + name + '\'' +
                   ", type='" + type + '\'' +
                   '}';
        }
    }

    public String getOdataContext() {
        return odataContext;
    }

    public void setOdataContext(String odataContext) {
        this.odataContext = odataContext;
    }

    public List<Value> getValue() {
        return value;
    }

    public void setValue(List<Value> value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "OrganizationDomains{" +
               "odataContext='" + odataContext + '\'' +
               ", value=" + value +
               '}';
    }
}
