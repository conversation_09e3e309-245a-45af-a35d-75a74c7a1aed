package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.common.utils.ValidationUtils;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.MsEnum;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;

import java.text.MessageFormat;
import java.util.List;

/*
POST https://{site}-admin.sharepoint.com/_vti_bin/client.svc/ProcessQuery HTTP/1.1
Host: {site}-admin.sharepoint.com
X-ClientService-ClientTag: PnPPS:2.12:Set-PnPTenant
Authorization: Bearer Token
User-Agent: NONISV|SharePointPnP|PnPPS/******** (Microsoft Windows NT 10.0.26100.0)
Connection: Keep-Alive
Accept-Encoding: gzip, deflate
Content-Type: text/xml
Content-Length: 508

<Request AddExpandoFieldTypeSuffix="true" SchemaVersion="********" LibraryVersion="********"
         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
    <Actions>
        <SetProperty Id="20" ObjectPathId="16" Name="SharingCapability">
            <Parameter Type="Enum">1</Parameter>
        </SetProperty>
    </Actions>
    <ObjectPaths>
        <Identity Id="16" Name="{_ObjectIdentity_}"/>
    </ObjectPaths>
</Request>
*/

public class SetPnpTenantCommand implements PowerShellCommand {
    private static final String TAG = "PnPPS:2.12:Set-PnPTenant";
    private final Builder builder;

    private SetPnpTenantCommand(Builder builder) {
        this.builder = builder;
    }

    @Override
    public String getTag() {
        return TAG;
    }

    @Override
    public String getBody() {
        String body = """
                <Request AddExpandoFieldTypeSuffix="true" SchemaVersion="********" LibraryVersion="********"
                         ApplicationName=".NET Library" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
                    <Actions>
                        <SetProperty Id="1" ObjectPathId="2" Name="{0}">
                            <Parameter Type="{3}">{1}</Parameter>
                        </SetProperty>
                    </Actions>
                    <ObjectPaths>
                        <Identity Id="2" Name="{2}"/>
                    </ObjectPaths>
                </Request>
                """;
        return MessageFormat.format(body, builder.propName, builder.value, builder.objectIdentity, builder.type);
    }

    @Override
    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-ClientService-ClientTag", TAG),
                Header.of("Content-Type", "text/xml")
        );
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String objectIdentity;
        private String propName;
        private String type;
        private String value;

        public Builder objectIdentity(String objectIdentity) {
            this.objectIdentity = objectIdentity.replace("\n", "&#xA;"); //need to decode html using the hexadecimal value
            return this;
        }

        public Builder propName(String propName) {
            this.propName = propName;
            return this;
        }

        public Builder value(MsEnum msEnum) {
            this.type = "Enum";
            this.value = Integer.toString(msEnum.asInt());
            return this;
        }

        public Builder value(Integer value) {
            this.type = "Int32";
            this.value = Integer.toString(value);
            return this;
        }

        public Builder value(Boolean value) {
            this.type = "Boolean";
            this.value = Boolean.toString(value);
            return this;
        }


        public SetPnpTenantCommand build() {
            ValidationUtils.Strings.requireNonBlank(objectIdentity, "objectIdentity required");
            ValidationUtils.Strings.requireNonBlank(propName, "propName required");
            ValidationUtils.Strings.requireNonBlank(value, "value required!");

            return new SetPnpTenantCommand(this);
        }
    }
}
