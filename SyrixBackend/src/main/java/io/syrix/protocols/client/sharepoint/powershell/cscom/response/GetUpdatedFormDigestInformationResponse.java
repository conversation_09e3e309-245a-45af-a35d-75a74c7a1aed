package io.syrix.protocols.client.sharepoint.powershell.cscom.response;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class GetUpdatedFormDigestInformationResponse {
    public static class Envelope {
        public Body body;
    }

    public static class Body {
        @JacksonXmlProperty(localName = "GetUpdatedFormDigestInformationResponse")
        public Response response;
    }

    public static class Response {
        @JacksonXmlProperty(localName = "GetUpdatedFormDigestInformationResult")
        public Result result;
    }

    public static class Result {
        public String digestValue;
        public Long timeoutSeconds;
        public String webFullUrl;
        public String libraryVersion;
        public String supportedSchemaVersions;
    }

}
