package io.syrix.protocols.client.graph.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Site extends GraphResponse{

    @JsonProperty("@odata.context")
    private String odataContext;

    @JsonProperty("createdDateTime")
    private String createdDateTime;

    @JsonProperty("description")
    private String description;

    @JsonProperty("id")
    private String id;

    @JsonProperty("lastModifiedDateTime")
    private String lastModifiedDateTime;

    @JsonProperty("name")
    private String name;

    @JsonProperty("webUrl")
    private String webUrl;

    @JsonProperty("displayName")
    private String displayName;

    @JsonProperty("root")
    private Object root;

    @JsonProperty("siteCollection")
    private SiteCollection siteCollection;

    // Вложенный класс для siteCollection
    public static class SiteCollection {
        @JsonProperty("hostname")
        private String hostname;

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        @Override
        public String toString() {
            return "SiteCollection{" +
                   "hostname='" + hostname + '\'' +
                   '}';
        }
    }

    // Геттеры и сеттеры

    public String getOdataContext() {
        return odataContext;
    }

    public void setOdataContext(String odataContext) {
        this.odataContext = odataContext;
    }

    public String getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(String createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(String lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Object getRoot() {
        return root;
    }

    public void setRoot(Object root) {
        this.root = root;
    }

    public SiteCollection getSiteCollection() {
        return siteCollection;
    }

    public void setSiteCollection(SiteCollection siteCollection) {
        this.siteCollection = siteCollection;
    }

    @Override
    public String toString() {
        return "Site{" +
               "odataContext='" + odataContext + '\'' +
               ", createdDateTime='" + createdDateTime + '\'' +
               ", description='" + description + '\'' +
               ", id='" + id + '\'' +
               ", lastModifiedDateTime='" + lastModifiedDateTime + '\'' +
               ", name='" + name + '\'' +
               ", webUrl='" + webUrl + '\'' +
               ", displayName='" + displayName + '\'' +
               ", root=" + root +
               ", siteCollection=" + siteCollection +
               '}';
    }

}
