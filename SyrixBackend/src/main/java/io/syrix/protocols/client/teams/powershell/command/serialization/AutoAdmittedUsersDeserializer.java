package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import io.syrix.protocols.client.teams.powershell.command.types.AutoAdmittedUsers;

import java.io.IOException;

public class AutoAdmittedUsersDeserializer extends JsonDeserializer<AutoAdmittedUsers> {
    @Override
    public AutoAdmittedUsers deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        String value = p.getText();
        return AutoAdmittedUsers.fromString(value);
    }
}
