package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

public class Actions {
    private ObjectPath ObjectPath;
    private Query Query;

    public ObjectPath getObjectPath() {
        return ObjectPath;
    }

    public void setObjectPath(ObjectPath objectPath) {
        ObjectPath = objectPath;
    }

    public Query getQuery() {
        return Query;
    }

    public void setQuery(Query query) {
        Query = query;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private ObjectPath ObjectPath;
        private Query Query;

        public Builder objectPath(ObjectPath objectPath) {
            ObjectPath = objectPath;
            return this;
        }

        public Builder query(Query query) {
            Query = query;
            return this;
        }

        public Actions build() {
            Actions actions = new Actions();
            actions.ObjectPath = this.ObjectPath;
            actions.Query = this.Query;
            return actions;
        }
    }
}