package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class Constructor {
    @JacksonXmlProperty(isAttribute = true)
    private String Id;
    @JacksonXmlProperty(isAttribute = true)
    private String TypeId;

    public Constructor(String id, String typeId) {
        this.Id = id;
        this.TypeId = typeId;
    }

    public static Constructor new_(String id, String typeId) {
        return new Constructor(id, typeId);
    }
}
