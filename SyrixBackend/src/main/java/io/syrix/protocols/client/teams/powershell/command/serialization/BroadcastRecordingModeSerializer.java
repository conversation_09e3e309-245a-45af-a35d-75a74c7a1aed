package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import io.syrix.protocols.client.teams.powershell.command.types.BroadcastRecordingMode;

import java.io.IOException;

public class BroadcastRecordingModeSerializer extends JsonSerializer<BroadcastRecordingMode> {

    @Override
    public void serialize(BroadcastRecordingMode value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(value.asString());
    }
}
