package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class Property {
    @JacksonXmlProperty(isAttribute = true)
    private String Name;
    @JacksonXmlProperty(isAttribute = true)
    private boolean ScalarProperty;

    public Property(String name, boolean scalarProperty) {
        this.Name = name;
        this.ScalarProperty = scalarProperty;
    }

    public static Property new_(String name, boolean scalarProperty) {
        return new Property(name, scalarProperty);
    }

}
