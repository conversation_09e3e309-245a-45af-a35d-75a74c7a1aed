package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;

import java.util.List;
//Get-PnPTenantSite
public class PnpCoreCommand implements PowerShellCommand {
    private static final String TAG = "PnPCore:1.11:";

    @Override
    public String getTag() {
        return TAG;
    }

    //don't work. need more investigate
    public String getBody() {
        return  "<Request AddExpandoFieldTypeSuffix=\"true\" SchemaVersion=\"15.0.0.0\" LibraryVersion=\"16.0.0.0\" ApplicationName=\".NET Library\" xmlns=\"http://schemas.microsoft.com/sharepoint/clientquery/2009\"><Actions><ObjectPath Id=\"2\" ObjectPathId=\"1\" /><ObjectPath Id=\"4\" ObjectPathId=\"3\" /><Query Id=\"5\" ObjectPathId=\"3\"><Query SelectAllProperties=\"true\"><Properties /></Query><ChildItemQuery SelectAllProperties=\"true\"><Properties /></ChildItemQuery></Query></Actions><ObjectPaths><Constructor Id=\"1\" TypeId=\"{268004ae-ef6b-4e9b-8425-127220d84719}\" /><Method Id=\"3\" ParentId=\"1\" Name=\"GetSitePropertiesFromSharePointByFilters\"><Parameters><Parameter TypeId=\"{b92aeee2-c92c-4b67-abcc-024e471bc140}\"><Property Name=\"Filter\" Type=\"Null\" /><Property Name=\"GroupIdDefined\" Type=\"Int32\">0</Property><Property Name=\"IncludeDetail\" Type=\"Boolean\">false</Property><Property Name=\"IncludePersonalSite\" Type=\"Enum\">0</Property><Property Name=\"StartIndex\" Type=\"Null\" /><Property Name=\"Template\" Type=\"Null\" /></Parameter></Parameters></Method></ObjectPaths></Request>";
    }

    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-ClientService-ClientTag", TAG),
                Header.of("Content-Type", "text/xml"),
                Header.of("X-RequestForceAuthentication", "true"),
                Header.of("User-Agent", "NONISV|SharePointPnP|PnPCore/1.11.2.0 (Microsoft Windows NT 10.0.26120.0)")
        );
    }
}
