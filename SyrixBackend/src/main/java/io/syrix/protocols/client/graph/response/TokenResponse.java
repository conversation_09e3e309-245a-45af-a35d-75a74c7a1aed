package io.syrix.protocols.client.graph.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TokenResponse extends GraphResponse {
    @JsonProperty("token_type")
    private String tokenType;
    @JsonProperty("expires_in")
    private long expires_in;
    @JsonProperty("ext_expires_in")
    private long extExpiresIn;
    @JsonProperty("access_token")
    private String accessToken;

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public long getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(long expires_in) {
        this.expires_in = expires_in;
    }

    public long getExtExpiresIn() {
        return extExpiresIn;
    }

    public void setExtExpiresIn(long extExpiresIn) {
        this.extExpiresIn = extExpiresIn;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
