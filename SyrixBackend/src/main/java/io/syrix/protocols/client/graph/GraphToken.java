package io.syrix.protocols.client.graph;

public class GraphToken {
    private final String tokenType;
    private final long tokenExpiresAt;
    private final String accessToken;


    // Private constructor to enforce usage of the Builder
    private GraphToken(Builder builder) {
        this.tokenType = builder.tokenType;
        this.tokenExpiresAt = builder.tokenExpiresAt;
        this.accessToken = builder.accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public long getTokenExpiresAt() {
        return tokenExpiresAt;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public static Builder builder() {
        return new Builder();
    }

    // Builder class
    public static class Builder {
        private String tokenType;
        private long tokenExpiresAt;
        private String accessToken;

        public Builder() {
            // Default constructor for Builder
        }

        public Builder tokenType(String tokenType) {
            this.tokenType = tokenType;
            return this;
        }

        public Builder tokenExpiresAt(long tokenExpiresAt) {
            this.tokenExpiresAt = tokenExpiresAt;
            return this;
        }

        public Builder accessToken(String accessToken) {
            this.accessToken = accessToken;
            return this;
        }

        public GraphToken build() {
            return new GraphToken(this);
        }
    }
}
