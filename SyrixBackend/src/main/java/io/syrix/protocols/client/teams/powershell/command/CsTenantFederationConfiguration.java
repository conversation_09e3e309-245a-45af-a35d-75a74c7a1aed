package io.syrix.protocols.client.teams.powershell.command;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public class CsTenantFederationConfiguration implements TeamsPowerShellCommand<TenantFederationConfiguration>{
    private static final String commandName = "Get-CsTenantFederationConfiguration";
    private static final String cmdletName = "Get-CsConfiguration_Get";

    private final ObjectMapper objectMapper;
    private final CollectionType collectionType;

    public CsTenantFederationConfiguration() {
        this.objectMapper = JsonMapper.builder()
                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .build();

        collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, TenantFederationConfiguration.class);
    }

    @Override
    public HttpMethod getMethod() {
        return HttpMethod.GET;
    }

    @Override
    public String getName() {
        return commandName;
    }

    @Override
    public String getBody() {
        return "";
    }

    @Override
    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-MS-CmdletName", cmdletName)
        );
    }

    @Override
    public String getEndPoint() {
        return "/Skype.Policy/configurations/TenantFederationSettings";
    }

    @Override
    public List<TenantFederationConfiguration> parseResponse(String body) throws Exception {
        return objectMapper.readValue(body, collectionType);
    }


}
