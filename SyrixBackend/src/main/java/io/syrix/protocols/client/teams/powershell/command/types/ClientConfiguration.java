package io.syrix.protocols.client.teams.powershell.command.types;

import com.fasterxml.jackson.annotation.JsonProperty;

@SuppressWarnings({"unused"})
public class ClientConfiguration implements TeamsPowerShellCommandResult {
    public Boolean allowEmailIntoChannel;
    public String restrictedSenderList;
    public Boolean allowDropBox;
    public Boolean allowBox;
    public Boolean allowGoogleDrive;
    public Boolean allowShareFile;
    public Boolean allowEgnyte;
    public Boolean allowOrganizationTab;
    public Boolean allowSkypeBusinessInterop;
    public String contentPin;
    public Boolean allowResourceAccountSendMessage;
    public String resourceAccountContentAccess;
    public Boolean allowGuestUser;
    public Boolean allowScopedPeopleSearchandAccess;
    public Boolean allowRoleBasedChatPermissions;
    public Boolean extendedWorkInfoInPeopleSearch;
    public String dataSource;
    public Key key;
    public String identity;
    public ConfigMetadata configMetadata;
    public String configId;

    public static class ConfigMetadata {
        public String authority;
    }

    public static class Key {
        public String scopeClass;
        public SchemaId schemaId;
        public AuthorityId authorityId;
        public DefaultXml defaultXml;
        public XmlRoot xmlRoot;
    }

    public static class SchemaId {
        public XName xName;
    }

    public static class XName {
        public String name;
    }

    public static class AuthorityId {
        @JsonProperty(value = "Class")
        public String classType;
        public String instanceId;
        public XmlRoot xmlRoot;
    }

    public static class DefaultXml {
        public SchemaId schemaId;
        public Data data;
        public String configObject;
        public String signature;
        public Boolean isModified;
    }

    public static class Data {
        public TeamsClientConfigurationData teamsClientConfiguration;
    }

    public static class TeamsClientConfigurationData {
        @JsonProperty(value = "@xmlns")
        public String xmlns;
    }

    public static class XmlRoot {
        public String name;
    }

}
