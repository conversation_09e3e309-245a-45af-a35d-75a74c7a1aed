package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharePointShellCommandDataResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public interface SharePointShellCommand<T extends SharePointShellCommandDataResult> {
    String getName();
    String getTag();
    String getBody();
    HttpMethod getMethod();
    List<Header> getHeaders();
    List<T> parseResponse(String body) throws Exception;
    List<Parameter> getParameters();
}
