package io.syrix.protocols.client.sharepoint.powershell.cscom.utils;

import org.apache.commons.lang3.tuple.Pair;

public class Header {
    private final Pair<String, String> header;

    private Header(String name, String value) {
        header = Pair.of(name, value);
    }

    public static Header of(String name, String value) {
        return new Header(name, value);
    }

    public String name() {
        return header.getLeft();
    }

    public String value() {
        return header.getRight();
    }
}
