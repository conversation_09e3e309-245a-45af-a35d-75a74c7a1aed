package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import io.syrix.protocols.client.teams.powershell.command.types.AutoAdmittedUsers;

import java.io.IOException;

public class AutoAdmittedUsersSerializer extends JsonSerializer<AutoAdmittedUsers> {

    @Override
    public void serialize(AutoAdmittedUsers value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(value.asString());
    }
}
