package io.syrix.protocols.client.teams.powershell.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.client.teams.powershell.command.types.TeamsPowerShellCommandResult;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public interface TeamsPowerShellCommand<T extends TeamsPowerShellCommandResult> {
    HttpMethod getMethod();
    String getName();
    String getBody();
    List<Header> getHeaders();
    String getEndPoint();
    List<T> parseResponse(String body) throws Exception;
}
