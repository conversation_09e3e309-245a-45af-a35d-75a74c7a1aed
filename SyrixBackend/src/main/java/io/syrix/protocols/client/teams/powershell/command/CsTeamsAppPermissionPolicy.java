package io.syrix.protocols.client.teams.powershell.command;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public class CsTeamsAppPermissionPolicy implements TeamsPowerShellCommand<AppPermissionPolicy> {
    private static final String commandName = "Get-CsTeamsAppPermissionPolicy";
    private static final String cmdletName = "Get-CsTeamsAppPermissionPolicy";

    private final ObjectMapper objectMapper;
    private final CollectionType collectionType;

    public CsTeamsAppPermissionPolicy() {
        this.objectMapper = JsonMapper.builder()
                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .build();

        collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, AppPermissionPolicy.class);

    }

    @Override
    public HttpMethod getMethod() {
        return HttpMethod.GET;
    }

    @Override
    public String getName() {
        return commandName;
    }

    @Override
    public String getBody() {
        return "";
    }

    @Override
    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-MS-CmdletName", cmdletName),
                Header.of("MPACmdlet", "true")
        );
    }

    @Override
    public String getEndPoint() {
        return "/Skype.Policy/configurations/TeamsAppPermissionPolicy";
    }

    @Override
    public List<AppPermissionPolicy> parseResponse(String body) throws Exception {
        return objectMapper.readValue(body, collectionType);
    }
}
