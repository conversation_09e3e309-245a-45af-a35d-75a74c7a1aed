package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class Query {
    @JacksonXmlProperty(isAttribute = true)
    private String Id;
    @JacksonXmlProperty(isAttribute = true)
    private String ObjectPathId;
    private InnerQuery Query;
    
    public InnerQuery getQuery() {
        return Query;
    }

    public void setQuery(InnerQuery query) {
        Query = query;
    }

    public Query(String id, String objectPathId, InnerQuery query) {
        this.Id = id;
        this.ObjectPathId = objectPathId;
        this.Query = query;
    }

    // Getters и Setters
    // ...
}
