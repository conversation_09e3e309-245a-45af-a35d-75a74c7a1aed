package io.syrix.protocols.client.teams.powershell.command;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;
import io.syrix.protocols.client.teams.powershell.command.types.*;
import io.syrix.protocols.model.HttpMethod;

import java.util.List;

public class CsTeamsCommand<T extends TeamsPowerShellCommandResult> implements TeamsPowerShellCommand<T> {
    private static final ObjectMapper objectMapper;
    private final CollectionType collectionType;

    private String cmdName;
    private HttpMethod method;
    private String body;
    private List<Header> heads;
    private String endPoint;
    private final Class<T> tClass;


    static {
        objectMapper = JsonMapper.builder()
                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .build();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public CsTeamsCommand(Class<T> tClass) {
        this.tClass = tClass;
        this.collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, tClass);
    }

    @Override
    public HttpMethod getMethod() {
        return method;
    }

    @Override
    public String getName() {
        return cmdName;
    }

    @Override
    public String getBody() {
        return body;
    }

    @Override
    public List<Header> getHeaders() {
        return heads;
    }

    @Override
    public String getEndPoint() {
        return endPoint;
    }

    @Override
    public List<T> parseResponse(String body) throws Exception {
        JsonNode jsonNode = objectMapper.readTree(body);
        if (jsonNode.isArray()) {
            return objectMapper.readValue(body, collectionType);
        } else {
            return List.of(objectMapper.readValue(body, tClass));
        }
    }

    //------------------------------------- Builder
    public static <T extends TeamsPowerShellCommandResult> Builder<T> builder(Class<T> tClass) {
        return new Builder<>(tClass);
    }

    public static class Builder<T extends TeamsPowerShellCommandResult> {
        private final Class<T> tClass;
        private String cmdName;
        private HttpMethod method;
        private String body;
        private List<Header> heads;
        private String endPoint;

        public Builder(Class<T> tClass) {
            this.tClass = tClass;
        }

        public Builder<T> cmdName(String cmdName) {
            this.cmdName = cmdName;
            return this;
        }

        public Builder<T> method(HttpMethod method) {
            this.method = method;
            return this;
        }

        public Builder<T> body(String body) {
            this.body = body;
            return this;
        }

        public Builder<T> headers(List<Header> heads) {
            this.heads = heads;
            return this;
        }

        public Builder<T> endPoint(String endPoint) {
            this.endPoint = endPoint;
            return this;
        }

        public CsTeamsCommand<T> build() {
            CsTeamsCommand<T> command = new CsTeamsCommand<>(tClass);
            command.cmdName = this.cmdName;
            command.method = this.method;
            command.body = this.body;
            command.heads = this.heads;
            command.endPoint = this.endPoint;
            return command;
        }
    }

    //-------------------------------------------- CsTeamsMeetingPolicy
    public static class CsTeamsMeetingPolicy {
        public static CsTeamsCommand<MeetingPolicy> GET() {
            return CsTeamsCommand.builder(MeetingPolicy.class)
                    .cmdName("Get-CsTeamsMeetingPolicy")
                    .endPoint("/Skype.Policy/configurations/TeamsMeetingPolicy")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers(List.of(
                            Header.of("X-MS-CmdletName", "Get-CsTeamsMeetingPolicy"),
                            Header.of("MPACmdlet", "true")
                    ))
                    .build();
        }

        public static CsTeamsCommand<MeetingPolicy> SET(MeetingPolicy policy) {
            try {
                return CsTeamsCommand.builder(MeetingPolicy.class)
                        .cmdName("Set-CsTeamsMeetingPolicy")
                        .endPoint("/Skype.Policy/configurations/TeamsMeetingPolicy/configuration/Global")
                        .method(HttpMethod.PATCH)
                        .body(objectMapper.writeValueAsString(policy))
                        .headers(List.of(
                                Header.of("X-MS-CmdletName", "Set-CsTeamsMeetingPolicy"),
                                Header.of("MPACmdlet", "true"),
                                Header.of("Content-Type", "application/json; charset=utf-8")
                        ))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

//-------------------------------------------- CsTeamsAppPermissionPolicy

    public static class CsTeamsAppPermissionPolicy {
        public static CsTeamsCommand<AppPermissionPolicy> GET() {
            return CsTeamsCommand.builder(AppPermissionPolicy.class)
                    .cmdName("Get-CsTeamsAppPermissionPolicy")
                    .endPoint("/Skype.Policy/configurations/TeamsAppPermissionPolicy")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers(List.of(
                            Header.of("X-MS-CmdletName", "Get-CsTeamsAppPermissionPolicy"),
                            Header.of("MPACmdlet", "true")
                    ))
                    .build();
        }
        public static CsTeamsCommand<AppPermissionPolicy> SET(AppPermissionPolicy policy) {
            try {
                return CsTeamsCommand.builder(AppPermissionPolicy.class)
                        .cmdName("Set-CsTeamsAppPermissionPolicy")
                        .endPoint("/Skype.Policy/configurations/TeamsAppPermissionPolicy/configuration/Global")
                        .method(HttpMethod.PATCH)
                        .headers(List.of(
                                Header.of("X-MS-CmdletName", "Set-CsTeamsAppPermissionPolicy"),
                                Header.of("MPACmdlet", "true"),
                                Header.of("Content-Type", "application/json; charset=utf-8")
                        ))
                        .body(objectMapper.writeValueAsString(policy))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

    }

//-------------------------------------------- CsTeamsClientConfiguration

    public static class CsTeamsClientConfiguration {
        public static CsTeamsCommand<ClientConfiguration> GET() {
            return CsTeamsCommand.builder(ClientConfiguration.class)
                    .cmdName("Get-CsTeamsClientConfiguration")
                    .endPoint("/Skype.Policy/configurations/TeamsClientConfiguration")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers(List.of(
                            Header.of("X-MS-CmdletName", "Get-CsTeamsClientConfiguration"),
                            Header.of("MPACmdlet", "true")
                    ))
                    .build();
        }

        public static CsTeamsCommand<ClientConfiguration> SET(ClientConfiguration clientConfiguration) {
            try {
                return CsTeamsCommand.builder(ClientConfiguration.class)
                        .cmdName("Set-CsTeamsClientConfiguration")
                        .endPoint("/Skype.Policy/configurations/TeamsClientConfiguration/configuration/Global")
                        .method(HttpMethod.PUT)
                        .headers(List.of(
                                Header.of("X-MS-CmdletName", "Set-CsConfiguration_Update"),
                                Header.of("MPACmdlet", "true"),
                                Header.of("Content-Type", "application/json; charset=utf-8")
                        ))
                        .body(objectMapper.writeValueAsString(clientConfiguration))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

//-------------------------------------------- CsTeamsMeetingBroadcastPolicy

    public static class CsTeamsMeetingBroadcastPolicy {
        public static CsTeamsCommand<MeetingBroadcastPolicy> GET() {
            return CsTeamsCommand.builder(MeetingBroadcastPolicy.class)
                    .cmdName("Get-CsTeamsMeetingBroadcastPolicy")
                    .endPoint("/Skype.Policy/configurations/TeamsMeetingBroadcastPolicy")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers(List.of(
                            Header.of("X-MS-CmdletName", "Get-CsConfiguration_Get")
                    ))
                    .build();
        }

        public static CsTeamsCommand<MeetingBroadcastPolicy> SET(MeetingBroadcastPolicy broadcastPolicy) {
            try {
                return CsTeamsCommand.builder(MeetingBroadcastPolicy.class)
                        .cmdName("Set-CsTeamsMeetingBroadcastPolicy")
                        .endPoint("/Skype.Policy/configurations/TeamsMeetingBroadcastPolicy/configuration/Global")
                        .method(HttpMethod.PUT)
                        .headers(List.of(
                                Header.of("X-MS-CmdletName", "Set-CsConfiguration_Get"),
                                Header.of("MPACmdlet", "true"),
                                Header.of("Content-Type", "application/json")
                        ))
                        .body(objectMapper.writeValueAsString(broadcastPolicy))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    //--------------------------------------------  CsTenant

    public static class CsTenant {
        public static CsTeamsCommand<TenantInfo> GET() {
//        objectMapper = JsonMapper.builder()
//                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
//                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
//                .build();
            return CsTeamsCommand.builder(TenantInfo.class)
                    .cmdName("Get-CsTenant")
                    .endPoint("/Teams.Tenant/tenants?defaultpropertyset=Extended")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers(List.of(Header.of("X-MS-CmdletName", "Get-CsTenantObou_Get")))
                    .build();
        }
    }

 //-------------------------------------------- CsTenantFederationConfiguration

    public static class CsTenantFederationConfiguration {
        public static CsTeamsCommand<TenantFederationConfiguration> GET() {
            return CsTeamsCommand.builder(TenantFederationConfiguration.class)
                    .cmdName("Get-CsTenantFederationConfiguration")
                    .endPoint("/Skype.Policy/configurations/TenantFederationSettings")
                    .method(HttpMethod.GET)
                    .body("")
                    .headers( List.of(
                            Header.of("X-MS-CmdletName", "Get-CsConfiguration_Get")
                    ))
                    .build();
        }

        public static CsTeamsCommand<TenantFederationConfiguration> SET(TenantFederationConfiguration configuration) {
            try {
                return CsTeamsCommand.builder(TenantFederationConfiguration.class)
                        .cmdName("Set-CsTenantFederationConfiguration")
                        .endPoint("/Skype.Policy/configurations/TenantFederationSettings/configuration/Global")
                        .method(HttpMethod.PUT)
                        .headers(List.of(
                                Header.of("X-MS-CmdletName", "Set-CsConfiguration_Update"),
                                Header.of("Content-Type", "application/json")
                        ))
                        .body(objectMapper.writeValueAsString(configuration))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }


}
