package io.syrix.protocols.client.teams.powershell.command.types;

import io.syrix.common.utils.ValidationUtils;

/**
 * @see <a href="https://learn.microsoft.com/en-us/powershell/module/teams/set-csteamsapppermissionpolicy?view=teams-ps#-defaultcatalogappstype">DefaultCatalogAppsType</a>
 */
public enum CatalogAppsType {
    ALLOWED_APP_LIST("AllowedAppList"),
    BLOCKED_APP_LIST("BlockedAppList");

    private final String value;

    CatalogAppsType(String value) {
        this.value = value;
    }

    public String asString() {
        return value;
    }

    public static CatalogAppsType fromString(String value) {
        ValidationUtils.Strings.requireNonBlank(value, "value can not be blank");
        for (CatalogAppsType val : CatalogAppsType.values()) {
            if (val.asString().equals(value)) {
                return val;
            }
        }
        throw new IllegalArgumentException("Can not find enum:"+value);
    }
}
