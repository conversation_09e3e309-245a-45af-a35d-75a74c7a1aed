package io.syrix.protocols.client.sharepoint.powershell.cscom;

public class PowerShellDigest {
    public String digestValue;
    public Long expiresAt;
    public String webFullUrl;
    public String libraryVersion;
    public String supportedSchemaVersions;


    public String getDigestValue() {
        return digestValue;
    }

    public Long getExpiresAt() {
        return expiresAt;
    }

    public String getWebFullUrl() {
        return webFullUrl;
    }

    public String getLibraryVersion() {
        return libraryVersion;
    }

    public String getSupportedSchemaVersions() {
        return supportedSchemaVersions;
    }

    // Private constructor to enforce usage of the Builder
    private PowerShellDigest(Builder builder) {
        this.digestValue = builder.digestValue;
        this.expiresAt = builder.expiresAt;
        this.webFullUrl = builder.webFullUrl;
        this.libraryVersion = builder.libraryVersion;
        this.supportedSchemaVersions = builder.supportedSchemaVersions;
    }

    public static Builder builder() {
        return new Builder();
    }
    // Builder class
    public static class Builder {
        private String digestValue;
        private Long expiresAt;
        private String webFullUrl;
        private String libraryVersion;
        private String supportedSchemaVersions;

        public Builder() {
            // Default constructor for Builder
        }

        public Builder digestValue(String digestValue) {
            this.digestValue = digestValue;
            return this;
        }

        public Builder expiresAt(Long expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }

        public Builder webFullUrl(String webFullUrl) {
            this.webFullUrl = webFullUrl;
            return this;
        }

        public Builder libraryVersion(String libraryVersion) {
            this.libraryVersion = libraryVersion;
            return this;
        }

        public Builder supportedSchemaVersions(String supportedSchemaVersions) {
            this.supportedSchemaVersions = supportedSchemaVersions;
            return this;
        }

        public PowerShellDigest build() {
            return new PowerShellDigest(this);
        }
    }
}
