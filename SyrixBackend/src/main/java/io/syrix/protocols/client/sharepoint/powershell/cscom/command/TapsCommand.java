package io.syrix.protocols.client.sharepoint.powershell.cscom.command;

import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Header;

import java.util.List;
//Get-SPOTenant
public class TapsCommand implements PowerShellCommand {
    private static final String TAG = "TAPS (16.0.24810.0)";

    @Override
    public String getTag() {
        return TAG;
    }

    public String getBody() {
        return """
                <Request AddExpandoFieldTypeSuffix="true" SchemaVersion="15.0.0.0" LibraryVersion="16.0.0.0" ApplicationName="SharePoint Online PowerShell (16.0.24810.0)" xmlns="http://schemas.microsoft.com/sharepoint/clientquery/2009">
                    <Actions>
                        <ObjectPath Id="4" ObjectPathId="3"/>
                        <Query Id="5" ObjectPathId="3">
                            <Query SelectAllProperties="true">
                                <Properties/>
                            </Query>
                        </Query>
                    </Actions>
                    <ObjectPaths>
                        <Constructor Id="3" TypeId="{268004ae-ef6b-4e9b-8425-127220d84719}"/>
                    </ObjectPaths>
                </Request>
                """;

    }

    public List<Header> getHeaders() {
        return List.of(
                Header.of("X-ClientService-ClientTag", TAG),
                Header.of("Content-Type", "text/xml"),
                Header.of("X-RequestForceAuthentication", "true")
        );
    }
}
