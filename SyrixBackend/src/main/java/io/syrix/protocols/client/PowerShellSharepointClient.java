package io.syrix.protocols.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SharePointShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.ShellCommandResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SharePointShellCommandDataResult;
import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.BadRequestException;
import io.syrix.protocols.exception.ResourceNotFoundException;
import io.syrix.protocols.utils.security.ITokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGeneratorFactory;
import io.syrix.protocols.utils.security.storage.DigestStorage;
import io.syrix.protocols.client.sharepoint.powershell.cscom.PowerShellDigest;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.PowerShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.response.GetUpdatedFormDigestInformationResponse;
import io.syrix.protocols.client.sharepoint.powershell.cscom.utils.Auth;
import io.syrix.protocols.exception.sharepoint.powershell.PowerShellAuthenticationException;
import io.syrix.protocols.exception.sharepoint.powershell.PowerShellClientException;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.utils.Retry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Stream;

import static java.net.HttpURLConnection.*;

public class PowerShellSharepointClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(PowerShellSharepointClient.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient httpClient;
    private final MSEnvironment environment;
    private final String appId;
    private final String domain;
    private final String adminDomain;
    private final ITokenGenerator tokenGenerator;
    private final Executor executor;

    private final int maxRetries;

    private final DigestStorage digestStorage;


    private PowerShellSharepointClient(Builder builder) {
        this.appId = Objects.requireNonNull(builder.appId, "App ID cannot be null");
        this.environment = Objects.requireNonNull(builder.environment, "Environment cannot be null");
        this.maxRetries = builder.maxRetries;
        this.domain = Objects.requireNonNull(builder.domain, "Domain cannot be null");
        this.adminDomain = Objects.requireNonNull(builder.adminDomain, "Admin domain cannot be null");

        this.digestStorage = new DigestStorage();
        this.executor = builder.executor;

        tokenGenerator = MSTokenGeneratorFactory.getInstance().getPowerShellSPTokenGenerator(
                MSTokenGenerator.getParams()
                        .appId(appId)
                        .identifier(domain)
                        .environment(environment)
                        .adminEndpoint(adminDomain)
                        .certPath(Objects.requireNonNull(builder.certificatePath, "certificatePath cannot be null"))
                        .certPassword(Objects.requireNonNull(builder.certificatePassword, "certificatePassword cannot be null")));

        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(builder.connectTimeout)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .build();
    }

    public static Builder builder() {
        return new Builder();
    }

    public <T extends SharePointShellCommandDataResult> CompletableFuture<ShellCommandResult<T>> execute_(SharePointShellCommand<T> command) {
        return execute(command).thenApply(res -> ShellCommandResult.of(res, command));
    }

    public <T extends SharePointShellCommandDataResult> CompletableFuture<List<T>> execute(SharePointShellCommand<T> command) {
        return Retry.executeWithRetry(() ->
                                tokenGenerator.getAccessToken()
                                        .thenCompose(token -> getDigest(token, command.getTag()))
                                        .thenCompose(auth -> executeCommand(auth, command)),
                        maxRetries,
                        executor
                ).thenApplyAsync(response -> handleResponse(response, command), executor);
    }


    public CompletableFuture<JsonNode> executeExchangeCommand(PowerShellCommand command) {
        return Retry.executeWithRetry(() -> authenticate(command).thenCompose(auth -> executeCommand(auth, command)), maxRetries);
    }

    private CompletableFuture<Auth> authenticate(PowerShellCommand command) {
        return tokenGenerator.getAccessToken().thenCompose(token -> getDigest(token, command));
    }

    //TODO Artur need move to tokenGenerator
    private CompletableFuture<Auth> getDigest(String token, String cmdTag) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PowerShellDigest digest = digestStorage.getToken(adminDomain);

                if (digest != null && System.currentTimeMillis() < digest.getExpiresAt()) {
                    return Auth.of(token, digest.getDigestValue());
                }

                String endpoint = String.format("https://%s/_vti_bin/sites.asmx", adminDomain);

                HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                        .uri(URI.create(endpoint))
                        .header("X-ClientService-ClientTag", cmdTag)
                        .header("Authorization", "Bearer " + token)
                        .header("Content-Type", "text/xml")
                        .header("X-RequestForceAuthentication", "true")
                        .header("SOAPAction", "http://schemas.microsoft.com/sharepoint/soap/GetUpdatedFormDigestInformation");

                String body = """
                        <?xml version="1.0" encoding="utf-8"?>
                        <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001
                        /XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                          <soap:Body>
                            <GetUpdatedFormDigestInformation xmlns="http://schemas.microsoft.com/sharepoint/soap/" />
                          </soap:Body>
                        </soap:Envelope>
                        """;
                requestBuilder.POST(HttpRequest.BodyPublishers.ofString(body));

                HttpResponse<String> response = httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() != 200) {
                    throw new PowerShellClientException("Failed to get DigestValue");
                }

                try {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
                    GetUpdatedFormDigestInformationResponse.Envelope envelope = xmlMapper.readValue(response.body(), GetUpdatedFormDigestInformationResponse.Envelope.class);

                    digest = PowerShellDigest.builder()
                            .digestValue(envelope.body.response.result.digestValue)
                            .webFullUrl(envelope.body.response.result.webFullUrl)
                            .libraryVersion(envelope.body.response.result.libraryVersion)
                            .supportedSchemaVersions(envelope.body.response.result.supportedSchemaVersions)
                            .expiresAt(
                                    System.currentTimeMillis() +
                                    (envelope.body.response.result.timeoutSeconds * 1000) -
                                    Duration.ofMinutes(5).toMillis()
                            ).build();

                    digestStorage.putToken(adminDomain, digest);

                    return Auth.of(token, digest.getDigestValue());
                } catch (Exception e) {
                    throw new PowerShellClientException("Failed to parse response: " + e.getMessage(), e);
                }
            } catch (Exception ex) {
                throw new PowerShellAuthenticationException("Failed to get DigestValue", ex);
            }
        });
    }


    private CompletableFuture<Auth> getDigest(String token, PowerShellCommand command) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PowerShellDigest digest = digestStorage.getToken(adminDomain);

                if (digest != null && System.currentTimeMillis() < digest.getExpiresAt()) {
                    return Auth.of(token, digest.getDigestValue());
                }

                String endpoint = String.format("https://%s/_vti_bin/sites.asmx", adminDomain);

                HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                        .uri(URI.create(endpoint))
                        .header("X-ClientService-ClientTag", command.getTag())
                        .header("Authorization", "Bearer " + token)
                        .header("Content-Type", "text/xml")
                        .header("X-RequestForceAuthentication", "true")
                        .header("SOAPAction", "http://schemas.microsoft.com/sharepoint/soap/GetUpdatedFormDigestInformation");

                String body = """
                        <?xml version="1.0" encoding="utf-8"?>
                        <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001
                        /XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                          <soap:Body>
                            <GetUpdatedFormDigestInformation xmlns="http://schemas.microsoft.com/sharepoint/soap/" />
                          </soap:Body>
                        </soap:Envelope>
                        """;
                requestBuilder.POST(HttpRequest.BodyPublishers.ofString(body));

                HttpResponse<String> response = httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() != 200) {
                    throw new PowerShellClientException("Failed to get DigestValue");
                }

                try {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
                    GetUpdatedFormDigestInformationResponse.Envelope envelope = xmlMapper.readValue(response.body(), GetUpdatedFormDigestInformationResponse.Envelope.class);

                    digest = PowerShellDigest.builder()
                            .digestValue(envelope.body.response.result.digestValue)
                            .webFullUrl(envelope.body.response.result.webFullUrl)
                            .libraryVersion(envelope.body.response.result.libraryVersion)
                            .supportedSchemaVersions(envelope.body.response.result.supportedSchemaVersions)
                            .expiresAt(
                                    System.currentTimeMillis() +
                                    (envelope.body.response.result.timeoutSeconds * 1000) -
                                    Duration.ofMinutes(5).toMillis()
                            ).build();

                    digestStorage.putToken(adminDomain, digest);

                    return Auth.of(token, digest.getDigestValue());
                } catch (Exception e) {
                    throw new PowerShellClientException("Failed to parse response: " + e.getMessage(), e);
                }
            } catch (Exception ex) {
                throw new PowerShellAuthenticationException("Failed to get DigestValue", ex);
            }
        });
    }

    private <T extends SharePointShellCommandDataResult> CompletableFuture<HttpResponse<String>> executeCommand(Auth auth, SharePointShellCommand<T> command) {
        try {

            String endpoint = String.format("https://%s/_vti_bin/client.svc/ProcessQuery", adminDomain);

            String[] headersAsArr = command.getHeaders().stream().flatMap(header -> Stream.of(header.name(), header.value())).toArray(String[]::new);

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(endpoint))
                    .header("Authorization", "Bearer " + auth.token())
                    .header("X-RequestDigest", auth.digest())
                    .headers(headersAsArr)
                    .method(command.getMethod().name(), HttpRequest.BodyPublishers.ofString(command.getBody()))
                    .build();

            return httpClient.sendAsync(
                            request,
                            HttpResponse.BodyHandlers.ofString());

        } catch (Exception e) {
            logger.error("Execute command error", e);
            return CompletableFuture.failedFuture(
                    new PowerShellClientException("Failed to execute command", e)
            );
        }
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleResponse(HttpResponse<String> response, SharePointShellCommand<T> command) {
        if (logger.isTraceEnabled()) {
            logger.trace("[Request] commandName: {} commandBody: {}", command.getName(), command.getBody());
            logger.trace("[Response] statusCode:{} body:{}", response.statusCode(), response.body());
        }
        try {
            return switch (response.statusCode()) {
                case HTTP_OK -> handleOkResponse(response, command);
                case HTTP_NO_CONTENT -> handleNoContent();
                case HTTP_FORBIDDEN -> handleForbidden(response);
                case HTTP_BAD_REQUEST -> handleBadRequest(response);
                case HTTP_NOT_FOUND -> handleNotFound(response);
                default -> handleDefault(response, command);
            };
        } catch (Exception ex) {
            logger.error("command: {} body {}", command.getName(), command.getBody(), ex);
            logger.error("command: {} response {}", command.getName(), response.body(), ex);
            throw ex;
        }
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleOkResponse(HttpResponse<String> response, SharePointShellCommand<T> command) {
        try {
            return command.parseResponse(response.body());
        } catch (Exception ex) {
            throw new PowerShellTeamsClient.TeamsClientException("Failed to parse response: " + response.body(), ex);
        }
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleNoContent() {
        return Collections.emptyList();
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleForbidden(HttpResponse<String> response) {
        throw new AccessForbiddenException("Unauthorized access", response.uri().toString());
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleBadRequest(HttpResponse<String> response) {
        throw new BadRequestException(response.body(), response.uri().toString());
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleNotFound(HttpResponse<String> response) {
        throw new ResourceNotFoundException(response.body(), response.uri().toString());
    }

    private <T extends SharePointShellCommandDataResult> List<T> handleDefault(HttpResponse<String> response, SharePointShellCommand<T> command) {
        throw new PowerShellTeamsClient.TeamsClientException(
                String.format("Command '%s' with body '%s' failed with status code: %d, Response: %s",
                        command.getName(),
                        command.getBody(),
                        response.statusCode(),
                        response.body()));
    }

    private CompletableFuture<JsonNode> executeCommand(Auth auth, PowerShellCommand command) {
        try {
            String endpoint = String.format("https://%s/_vti_bin/client.svc/ProcessQuery", adminDomain);

            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(endpoint))
                    .header("X-RequestDigest", auth.digest())
                    .header("Authorization", "Bearer " + auth.token());

            command.getHeaders().forEach(header -> requestBuilder.header(header.name(), header.value()));

            String body = command.getBody();

            logger.debug("Command:{}", body);

            requestBuilder.POST(HttpRequest.BodyPublishers.ofString(body));

            return httpClient.sendAsync(
                    requestBuilder.build(),
                    HttpResponse.BodyHandlers.ofString()
            ).thenApply(response -> {
                if (response.statusCode() != 200) {
                    throw new PowerShellClientException("Failed to execute command: " + response.body());
                }

                try {
                    return objectMapper.readTree(response.body());
                } catch (Exception e) {
                    throw new PowerShellClientException("Failed to parse response", e);
                }
            });
        } catch (Exception e) {
            return CompletableFuture.failedFuture(
                    new PowerShellClientException("Failed to execute command", e)
            );
        }
    }


    public static class Builder {
        private String appId;
        private String certificatePath;
        private char[] certificatePassword;
        private MSEnvironment environment = MSEnvironment.COMMERCIAL;
        private Duration connectTimeout = Duration.ofSeconds(30);
        private int maxRetries = 3;
        private String domain;
        private String adminDomain;
        private Executor executor = Executors.newVirtualThreadPerTaskExecutor();

        public Builder withAppId(String appId) {
            this.appId = appId;
            return this;
        }

        public Builder withCertificatePath(String certificatePath) {
            this.certificatePath = certificatePath;
            return this;
        }

        public Builder withCertificatePassword(char[] certificatePassword) {
            this.certificatePassword = certificatePassword;
            return this;
        }

        public Builder withEnvironment(MSEnvironment environment) {
            this.environment = environment;
            return this;
        }

        public Builder withConnectTimeout(Duration timeout) {
            this.connectTimeout = timeout;
            return this;
        }

        public Builder withMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public Builder withDomain(String domain) {
            this.domain = domain;
            return this;
        }

        public Builder withAdminDomain(String adminDomain) {
            this.adminDomain = adminDomain;
            return this;
        }

        public Builder executor(Executor executor) {
            this.executor = executor;
            return this;
        }

        public PowerShellSharepointClient build() {
            return new PowerShellSharepointClient(this);
        }
    }

    @Override
    public void close() {
        // Cleanup if needed
    }

    public MSEnvironment getEnvironment() {
        return environment;
    }

}