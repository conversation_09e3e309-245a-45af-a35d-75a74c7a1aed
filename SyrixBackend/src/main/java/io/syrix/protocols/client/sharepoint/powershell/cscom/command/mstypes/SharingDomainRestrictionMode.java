package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

/**
 @see <a href="https://learn.microsoft.com/en-us/powershell/module/sharepoint-online/set-spotenant?view=sharepoint-ps#-sharingdomainrestrictionmode">SharingDomainRestrictionMode</a>
 */
public enum SharingDomainRestrictionMode implements MsEnum{
    NONE(0), //There are no restrictions on external domains. Users can share with any external users.
    ALLOW_LIST(1), // Users will be able to share with external collaborators coming only from that email domain.
    BLOCK_LIST(2); //Users will be able to share with all external collaborators apart from the ones on the BlockedDomainList.

    private final int intVal;

    SharingDomainRestrictionMode(int intVal) {
        this.intVal = intVal;
    }

    @Override
    public int asInt() {
        return intVal;
    }

    public static SharingDomainRestrictionMode fromInt(int intVal) {
        for (SharingDomainRestrictionMode value : values()) {
            if (value.asInt() == intVal) {
                return value;
            }
        }
        throw new IllegalArgumentException("Can not convert int "+intVal+" to SharingDomainRestrictionMode");
    }

}
