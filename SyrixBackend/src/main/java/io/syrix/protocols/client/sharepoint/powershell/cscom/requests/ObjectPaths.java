package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

public class ObjectPaths {
    private Constructor Constructor;

    public Constructor getConstructor() {
        return Constructor;
    }

    public void setConstructor(Constructor constructor) {
        Constructor = constructor;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Constructor Constructor;

        public Builder constructor(Constructor constructor) {
            Constructor = constructor;
            return this;
        }

        public ObjectPaths build() {
            ObjectPaths res = new ObjectPaths();
            res.setConstructor(Constructor);
            return res;
        }
    }
}
