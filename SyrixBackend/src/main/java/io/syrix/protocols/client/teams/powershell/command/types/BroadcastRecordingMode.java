package io.syrix.protocols.client.teams.powershell.command.types;

import io.syrix.common.utils.ValidationUtils;

/**
 * @see <a href="https://learn.microsoft.com/en-us/powershell/module/teams/set-csteamsmeetingbroadcastpolicy?view=teams-ps#-broadcastrecordingmode">BroadcastRecordingMode</a>
 */

public enum BroadcastRecordingMode {
    ALWAYS_ENABLED("AlwaysEnabled"),
    ALWAYS_DISABLED("AlwaysDisabled"),
    USER_OVERRIDE("UserOverride");

    private final String value;

    BroadcastRecordingMode(String value) {
        this.value = value;
    }

    public String asString() {
        return value;
    }

    public static BroadcastRecordingMode fromString(String value) {
        ValidationUtils.Strings.requireNonBlank(value, "value can not be blank");
        for (BroadcastRecordingMode val : BroadcastRecordingMode.values()) {
            if (val.asString().equals(value)) {
                return val;
            }
        }
        throw new IllegalArgumentException("Can not find enum:"+value);
    }
}
