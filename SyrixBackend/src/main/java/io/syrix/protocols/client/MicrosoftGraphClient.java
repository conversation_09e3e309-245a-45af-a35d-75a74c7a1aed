package io.syrix.protocols.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.NoPremiumLicenseException;
import io.syrix.products.microsoft.entra.service.EntraIDConstants;
import io.syrix.protocols.client.graph.response.GraphResponse;
import io.syrix.protocols.client.graph.response.OrganizationDomains;
import io.syrix.protocols.client.graph.response.Site;
import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.BadRequestException;
import io.syrix.protocols.exception.ResourceNotFoundException;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.protocols.model.HttpMethod;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.model.PaginatedResult;
import io.syrix.protocols.exception.GraphClientException;
import io.syrix.protocols.utils.security.ITokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGeneratorFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.function.Function;

import io.syrix.protocols.utils.ProtocolConstants;
import io.syrix.protocols.utils.Retry;

import static com.azure.storage.common.implementation.Constants.HeaderConstants.CONTENT_TYPE;
import static io.syrix.common.constants.Constants.APPLICATION_JSON;
import static io.syrix.common.constants.Constants.GROUPS_ENDPOINT;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.NEXT_LINK_FIELD;
import static io.syrix.common.constants.Constants.RETRY_AFTER;
import static io.syrix.common.constants.Constants.STATE;
import static io.syrix.common.constants.Constants.VALUE_FIELD;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_FILTER;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

/**
 * Core client for Microsoft Graph API interactions.
 * This class provides the foundational infrastructure for all Microsoft 365/Azure service communications.
 */
public class MicrosoftGraphClient implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(MicrosoftGraphClient.class);
	private static final int MAX_CONCURRENT_REQUESTS = 50; // HTTP/2 default max concurrent streams
	private static final int THROTTELING_HTTP_CODE = 429;
	private static final String TENNAT_ID = "tid";
	public static final String DEFUALT_GLOBAL_ADMIN_GROUP = "62e90394-69f5-4237-9190-012177145e10";
	private static final String CHECK_AND_ASSIGN_FAILURE = "Failed to checkAndAssignServicePrincipalToAdminGroup";
	private static final String SERVICE_PRINCIPAL = "/servicePrincipals";
	private static final String FAILED_TO_FIND_SERVICE_PRINCIPALS = "Failed to find servicePrincipals : {}";
	private static final String FAILED_TO_FIND_SHAREPOINT_SITE = "Failed to find sharepoint site : {}";
	private static final String PHISHING_RESISTANT_POLICY_NAME = "Phishing-Resistant MFA Policy";

	// Core dependencies
	private final HttpClient httpClient;
	private final ObjectMapper objectMapper;
	private final MSEnvironment environment;

	// Configuration settings
	private final Duration requestTimeout;
	private final Duration connectTimeout;
	private final int maxRetries;
	private final String tenantId;
	private final String clientId;
	// OAuth token cache
	private final Semaphore requestSemaphore;
	private final ExecutorService executor;
	private final ITokenGenerator graphTokenGenerator;
	private final ITokenGenerator outlookTokenGenerator;
	private final ITokenGenerator managedTokenGenerator;



	// Environment-specific Graph API endpoints

	private MicrosoftGraphClient(Builder builder) {
		this.environment = Objects.requireNonNull(builder.environment, "Environment cannot be null");
		this.objectMapper = new ObjectMapper();
		this.requestTimeout = builder.requestTimeout;
		this.connectTimeout = builder.connectTimeout;
		this.maxRetries = builder.maxRetries;
		this.clientId = Objects.requireNonNull(builder.clientId, "Client ID cannot be null");
		this.executor = Executors.newVirtualThreadPerTaskExecutor();

		// Initialize request semaphore to limit concurrent streams
		this.requestSemaphore = new Semaphore(MAX_CONCURRENT_REQUESTS);

		// Configure HTTP client with proper settings
		this.httpClient = HttpClient.newBuilder()
				.connectTimeout(connectTimeout)
				.executor(executor)
				.version(HttpClient.Version.HTTP_1_1)
				.followRedirects(HttpClient.Redirect.NORMAL)
				// Add these HTTP/2 specific settings:
//				.proxy(HttpClient.Builder.NO_PROXY)  // Avoid proxy issues
//				.priority(1) // Set high priority for requests
				.build();
		tenantId = getTenantIdformMS365(builder);
		this.graphTokenGenerator =
				MSTokenGeneratorFactory.getInstance().getMGraphTokenGenerator(true,
						MSTokenGenerator.getParams()
								.clientId(builder.clientId)
								.clientSecret(builder.clientSecret)
								.refreshToken(builder.refreshToken)
								.environment(environment)
								.appId(builder.appId)
								.certPath(builder.certPath)
								.certPassword(builder.certPassword)
								.identifier(tenantId)
				);

		this.outlookTokenGenerator = MSTokenGeneratorFactory.getInstance().getMSOutlookTokenGenerator(true,
				MSTokenGenerator.getParams()
						.clientId(builder.clientId)
						.clientSecret(builder.clientSecret)
						.refreshToken(builder.refreshToken)
						.environment(environment)
						.appId(builder.appId)
						.certPath(builder.certPath)
						.certPassword(builder.certPassword)
		);


		this.managedTokenGenerator = MSTokenGeneratorFactory.getInstance().MSManagmentTokenGenerator(true,
				MSTokenGenerator.getParams()
						.clientId(builder.clientId)
						.clientSecret(builder.clientSecret)
						.refreshToken(builder.refreshToken)
						.environment(environment)
						.identifier(tenantId)
						.appId(builder.appId)
						.certPath(builder.certPath)
						.certPassword(builder.certPassword)
		);
	}

	private String getTenantIdformMS365(Builder builder) {
		String accessToken = MSTokenGeneratorFactory.getInstance()
				.getMGraphTokenGenerator(false, MSTokenGenerator.getParams()
						.clientId(builder.clientId)
						.clientSecret(builder.clientSecret)
						.refreshToken(builder.refreshToken)
						.environment(environment)).getAccessToken().join();
		return extractTenantId(accessToken);
	}

	public CompletableFuture<String> getDomain() {
		CompletableFuture<OrganizationDomains> organizationDomainsFuture = makeGraphRequest(
				GraphRequest.builder()
						.v1()
						.withEndpoint("/organization?$select=verifiedDomains")
						.build(),
				OrganizationDomains.class
		);

		return organizationDomainsFuture.thenApply(result -> {
			try {
				List<String> domains = result.getValue().stream().flatMap(
								value -> value.getVerifiedDomains().stream())
						.filter(OrganizationDomains.VerifiedDomain::isDefault)
						.map(OrganizationDomains.VerifiedDomain::getName)
						.toList();
				if (domains.isEmpty()) {
					throw new GraphClientException("Can not find default domain");
				}
				if (domains.size() > 1) {
					throw new GraphClientException("More that one default domains, Can not select default domain");
				}
				return domains.getFirst();
			} catch (Exception ex) {
				throw new GraphClientException("Can not get default domain", ex);
			}
		});
	}

	public void assignRoleToServicePrincipals(String roleTemplate,
											  String servicePrincipals) {
		try {
			// Create JSON object correctly
			ObjectNode jsonBody = objectMapper.createObjectNode();
			jsonBody.put("@odata.id",
					"https://graph.microsoft.com/v1.0/servicePrincipals/" + servicePrincipals);

			// Convert to string and create body publisher
			String jsonString = objectMapper.writeValueAsString(jsonBody);
			HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(jsonString);

			CompletableFuture<JsonNode> servicePrincipleFuture = makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withMethod(HttpMethod.POST)
							.addHeader(CONTENT_TYPE, APPLICATION_JSON)
							.withBody(body)
							.withEndpoint("/directoryRoles/roleTemplateId=" + roleTemplate + "/members/$ref")
							.build());

			servicePrincipleFuture.join();
			logger.info("Assigned role to service principals: {}", servicePrincipals);
		} catch (Exception e) {
			throw new GraphClientException("Failed to assignRoleToServicePrincipals", e);
		}
	}

	public boolean checkAndAssignServicePrincipalToAdminGroup(String servicePrincipal) {
		String globaAdminRole = getGlobalAdminGroup();
		try {
			return makeGraphRequest(
					GraphRequest.builder()
							.v1()
							.withEndpoint("/directoryRoles/" + globaAdminRole + "/members")
							.addQueryParam(PARAM_FILTER, String.format("id eq '%s'", servicePrincipal))
							.build())
					.exceptionally(e -> {
								if (e.getCause().getCause() instanceof ResourceNotFoundException) {
									logger.debug("Failed to find service principal in the Global Administrator role. Adding");
									assignRoleToServicePrincipals(globaAdminRole, servicePrincipal);
									return BooleanNode.TRUE;//Addded
								}
								throw new GraphClientException(CHECK_AND_ASSIGN_FAILURE, e);
							}
					).get().asBoolean();
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt(); // Restore the interrupted status
			throw new GraphClientException(CHECK_AND_ASSIGN_FAILURE, e);
		} catch (Exception e) {
			throw new GraphClientException(CHECK_AND_ASSIGN_FAILURE, e);
		}
	}

	public String getGlobalAdminGroup() {
		CompletableFuture<JsonNode> globalAdminRole = makeGraphRequest(
				GraphRequest.builder()
						.v1()
						.withEndpoint("/directoryRoles")
						.addQueryParam(PARAM_FILTER, "displayName eq 'Global Administrator'")
						.addQueryParam(PARAM_SELECT, ID_FIELD)
						.build());

		try {
			JsonNode roleNode = globalAdminRole.get();
			return roleNode.get(VALUE_FIELD).get(0).get(ID_FIELD).asText();
		} catch (Exception e) {
			logger.error("Failed to find Global Administrator role : {}. Using default", e.getMessage());
			return DEFUALT_GLOBAL_ADMIN_GROUP;
		}
	}

	public static String getServicePrincipalID(JsonNode servicePrincipal) {
		try {
			return servicePrincipal.get(VALUE_FIELD).get(0).get(ID_FIELD).asText();
		} catch (Exception e) {
			throw new GraphClientException(FAILED_TO_FIND_SERVICE_PRINCIPALS, e);
		}
	}

	public static String getServicePrincipalID(CompletableFuture<JsonNode> servicePrincipal) {
		try {
			return servicePrincipal.get().get(VALUE_FIELD).get(0).get(ID_FIELD).asText();
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt(); // Restore the interrupted status
			throw new GraphClientException(FAILED_TO_FIND_SERVICE_PRINCIPALS, e);
		} catch (Exception e) {
			throw new GraphClientException(FAILED_TO_FIND_SERVICE_PRINCIPALS, e);
		}
	}

	public CompletableFuture<JsonNode> getAzureServicePrincipals(Map<String, String> queryParams, String additionalPath) {
		StringBuilder urlPath = new StringBuilder(SERVICE_PRINCIPAL);
		if(StringUtils.isNotEmpty(additionalPath)) {
			urlPath.append(additionalPath);
		}
		GraphRequest.Builder builder = GraphRequest.builder().beta().withEndpoint(urlPath.toString());
		if (queryParams != null && !queryParams.isEmpty()) {
			queryParams.forEach(builder::addQueryParam);
		}
		CompletableFuture<JsonNode> servicePrincipleFuture = makeGraphRequest(builder.build());

		try {
			return servicePrincipleFuture;
		} catch (Exception e) {
			throw new GraphClientException(FAILED_TO_FIND_SERVICE_PRINCIPALS, e);
		}
	}


//	public String getAppServicePrinciple() {
//		CompletableFuture<JsonNode> servicePrincipleFuture = makeGraphRequest(
//				GraphRequest.builder()
//						.v1()
//						.withEndpoint("/servicePrincipals")
//						.addQueryParam("$filter", "appId eq 'e3fc1b90-ca49-42f2-ae5c-d51fe9ae6133'")
//						.addQueryParam("$select", "id")
//						.build());
//
//		try {
//			JsonNode servicePrinciple = servicePrincipleFuture.get();
//			return servicePrinciple.get("value").get(0).get("id").asText();
//		} catch (InterruptedException e) {
//			Thread.currentThread().interrupt(); // Restore the interrupted status
//			throw new GraphClientException("Failed to find servicePrincipals : {}", e);
//		} catch (Exception e) {
//			throw new GraphClientException("Failed to find servicePrincipals : {}", e);
//		}
//	}

	public String getSite() {
		CompletableFuture<Site> siteFuture = makeGraphRequest(
				GraphRequest.builder().v1().withEndpoint("/sites/root").build(), Site.class);

		try {
			Site site = siteFuture.get();
			return site.getWebUrl();
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt(); // Restore the interrupted status
			throw new GraphClientException(FAILED_TO_FIND_SHAREPOINT_SITE, e);
		} catch (Exception e) {
			throw new GraphClientException(FAILED_TO_FIND_SHAREPOINT_SITE, e);
		}
	}

	private String extractTenantId(final String accessToken) {
		// Split the token into header, payload, signature
		String[] parts = accessToken.split("\\.");
		// Decode the payload
		String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]));

		// Parse the JSON using Jackson
		ObjectMapper objectMapperTmp = new ObjectMapper();
		JsonNode payloadNode = null;
		try {
			payloadNode = objectMapperTmp.readTree(payloadJson);
			if (payloadNode.has(TENNAT_ID)) {
				return payloadNode.get(TENNAT_ID).asText();
			} else {
				return null; // or throw an exception if tid is required
			}
		} catch (JsonProcessingException e) {
			return null; // or throw an exception if tid is required
		}
	}


	public List<String> fetchSubscriptions() {
		String accessToken = managedTokenGenerator.getAccessToken().join();
		String subscriptionsEndpoint = "https://management.azure.com/subscriptions?api-version=2020-01-01";

		try {
			HttpResponse<String> response = sendAzureRequest(subscriptionsEndpoint, accessToken);
			return processResponse(response);
		} catch (IOException | InterruptedException e) {
			logger.error("Failed to fetch subscriptions: {}", e.getMessage(), e);
			Thread.currentThread().interrupt();
			return Collections.emptyList();
		}
	}

	private HttpResponse<String> sendAzureRequest(String url, String accessToken) throws IOException, InterruptedException {
		HttpRequest request = HttpRequest.newBuilder()
				.uri(URI.create(url))
				.header(Constants.AUTHORIZATION_HEADER, Constants.BEARER_PREFIX + accessToken)
				.header(Constants.CONTENT_TYPE_HEADER, Constants.APPLICATION_JSON)
				.GET()
				.build();
		return httpClient.send(request, HttpResponse.BodyHandlers.ofString());
	}

	private List<String> processResponse(HttpResponse<String> response) {
		if (response.statusCode() == 200) {
			JsonObject responseJson = JsonParser.parseString(response.body()).getAsJsonObject();
			JsonArray subscriptions = responseJson.getAsJsonArray(Constants.VALUE_FIELD);

			List<String> subscriptionIds = new ArrayList<>();
			logger.debug("Azure Subscription IDs:");
			for (int i = 0; i < subscriptions.size(); i++) {
				JsonObject subscription = subscriptions.get(i).getAsJsonObject();
				String subscriptionId = subscription.get("subscriptionId").getAsString();
				logger.debug(subscriptionId);
				subscriptionIds.add(subscriptionId);
			}
			return subscriptionIds;
		} else {
			logger.debug("Failed to fetch subscriptions. HTTP Status: {}", response.statusCode());
			logger.debug("Response Body: {}", response.body());
			return Collections.emptyList();
		}
	}

//	public void get365DkimConfig() {
//
//		String tmp_accessToken = managedTokenGenerator.getAccessToken().join();
//
//		HttpRequest request = HttpRequest.newBuilder()
//				.uri(URI.create("https://outlook.office365.com/adminapi/beta/" + this.tenantId))
//				.header("Authorization", "Bearer " + tmp_accessToken)
//				.header("Accept", "application/json")
//				.header("X-CmdletName", "Get-DkimSigningConfig")
//				.header("X-ClientApplication", "ExoManagementModule")
//				.header("Prefer", "odata.maxpagesize=1000")
//				.header("Accept-Language", "en-US")
//				.header("X-ResponseFormat", "clixml")
//				.header("Accept-Charset","UTF-8")
//				.header("X-SerializationLevel","Partial")
//				.header("X-ClientModuleVersion","3.7.0")
//				.header("User-Agent","Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.2200")
//				.header("X-AnchorMailbox" , "UPN:<EMAIL>")
//				.POST(HttpRequest.BodyPublishers.ofString("{\"CmdletInput\":{\"CmdletName\":\"Get-DkimSigningConfig\",\"Parameters\":{}}}"))
//				.build();
//
//		try {
//			HttpResponse<String> response = this.httpClient.send(request, HttpResponse.BodyHandlers.ofString());
//			if (response.statusCode() != 200) {
//				throw new RuntimeException("Failed to retrieve subscriptions using REST API. Status code " +
//						response.statusCode() + " body: " + response.body() );
//			}
//		} catch (IOException e) {
//			throw new RuntimeException(e);
//		} catch (InterruptedException e) {
//			throw new RuntimeException(e);
//		}
//
//	}

//	private List<String> getSubscriptions() {
//		String subscriptionsUrl = "https://management.azure.com/subscriptions?api-version=2021-04-01";
//		List<String> subscriptionIds = new ArrayList<>();
//
//		HttpRequest request = HttpRequest.newBuilder()
//				.uri(URI.create(subscriptionsUrl))
//				.header("Authorization", "Bearer " + graphTokenGenerator.getAccessToken().join())
//				.header("Accept", "application/json")
//				.GET()
//				.build();
//
//		HttpResponse<String> response = null;
//		try {
//			response = this.httpClient.send(request, HttpResponse.BodyHandlers.ofString());
//		} catch (IOException e) {
//			throw new RuntimeException(e);
//		} catch (InterruptedException e) {
//			throw new RuntimeException(e);
//		}
//		if (response.statusCode() != 200) {
//			throw new RuntimeException("Failed to retrieve subscriptions using REST API. Status code " +
//					response.statusCode() + " body: " + response.body() );
//		}
//
//		ObjectMapper objectMapper = new ObjectMapper();
//		JsonNode rootNode = null;
//		try {
//			rootNode = objectMapper.readTree(response.body());
//			JsonNode valueNode = rootNode.get("value");
//			if(valueNode != null && valueNode.isArray()) {
//				for (JsonNode subscriptionNode : valueNode) {
//					JsonNode subscriptionIdNode = subscriptionNode.get("subscriptionId");
//					if (subscriptionIdNode != null) {
//						subscriptionIds.add(subscriptionIdNode.asText());
//					}
//				}
//			}
//		} catch (JsonProcessingException e) {
//			throw new RuntimeException(e);
//		}
//		return subscriptionIds;
//	}

	/**
	 * Makes an authenticated request to the Graph API with retry and timeout handling.
	 *
	 * @param response The GraphRequest object containing request details
	 * @return CompletableFuture containing the JSON response
	 */
	private void handleGraphResponse(HttpResponse<String> response) {
		try {
			JsonNode errorResponse = parseErrorResponse(response.body());
			String url = response.uri().toString();

			switch (response.statusCode()) {
				case HttpURLConnection.HTTP_OK,
					 HttpURLConnection.HTTP_CREATED -> { break; }
				case HttpURLConnection.HTTP_BAD_REQUEST -> handleBadRequest(errorResponse, url);
				case HttpURLConnection.HTTP_FORBIDDEN -> handleForbidden(errorResponse, url);
				case HttpURLConnection.HTTP_NOT_FOUND -> handleNotFound(errorResponse, url);
				case HttpURLConnection.HTTP_NO_CONTENT -> handleNoContent(errorResponse);
				default -> handleDefaultError(response.statusCode(), errorResponse);
			}
		} catch (IOException e) {
			throw new GraphClientException("Failed to parse error response", e);
		}
	}

	private JsonNode parseErrorResponse(String responseBody) throws IOException {
		return objectMapper.readTree(responseBody);
	}

	private void handleBadRequest(JsonNode errorResponse, String url) {
		String errorMessage = extractErrorMessage(errorResponse);
		if (errorMessage.contains("Resource not found for the segment")) {
			throw new ResourceNotFoundException(errorMessage, url);
		}
		throw new BadRequestException(errorMessage, url);
	}

	private void handleForbidden(JsonNode errorResponse, String url) {
		String errorMessage = extractErrorMessage(errorResponse);
		if("Your tenant is not licensed for this feature.".equals(errorMessage)) {
			throw new NoPremiumLicenseException(errorMessage, url);
		}
		throw new AccessForbiddenException(errorMessage, url);
	}

	private void handleNotFound(JsonNode errorResponse, String url) {
		String errorMessage = extractErrorMessage(errorResponse);
		throw new ResourceNotFoundException(errorMessage, url);
	}

	private void handleNoContent(JsonNode errorResponse) {
		if (errorResponse.isEmpty()) {
			return;
		}
		throw new GraphClientException("Unexpected content in NO_CONTENT response");
	}

	private void handleDefaultError(int statusCode, JsonNode errorResponse) {
		String errorMessage = extractErrorMessage(errorResponse);
		throw new GraphClientException(String.format(
				"Request failed with status code: %d - %s",
				statusCode,
				errorMessage
		));
	}

	private String extractErrorMessage(JsonNode errorResponse) {
		if (errorResponse.has(Constants.ERROR_FIELD) &&
				errorResponse.get(Constants.ERROR_FIELD).has(Constants.MESSAGE_FIELD)) {
			return errorResponse.get(Constants.ERROR_FIELD)
					.get(Constants.MESSAGE_FIELD)
					.asText();
		}
		return errorResponse.toString();
	}

	public String getTenantId() {
		return tenantId;
	}

	/**
	 * Makes a paginated request to the Graph API and combines all results.
	 */
	public CompletableFuture<JsonNode> makeGraphRequest(GraphRequest request) {
		return graphTokenGenerator.getAccessToken()
				.thenCompose(token -> CompletableFuture.supplyAsync(
						() -> fetchAllPages(request, token),
						executor
				));
	}

	/**
	 * Retrieves service plans and licenses.
	 */
	public CompletableFuture<JsonNode> getSubscribedSkus() {
		return makeGraphRequest(GraphRequest.builder()
						.v1()
						.withEndpoint("/subscribedSkus")
						.withMethod(HttpMethod.GET)
						.build())
				.exceptionally(e -> {
					logger.error("Failed to call getSubscribedSkus {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}


	/**
	 * Fetches and combines all pages of results from the Graph API.
	 */
	private JsonNode fetchAllPages(GraphRequest request, String token) {
		try {
			String originalUrl = environment.getGraphEndpoint() + request.getEndpoint();
			JsonNode firstPage = fetchSinglePage(originalUrl, request, token);

			if (!isPageableResponse(firstPage)) {
				return firstPage;
			}

			return aggregatePages(request, token, firstPage);
		} catch (Exception e) {
			throw new GraphClientException("Failed to fetch all pages : " + e.getMessage(), e);
		}
	}

	/**
	 * Checks if response contains pageable results.
	 */
	private boolean isPageableResponse(JsonNode response) {
		return response.has(VALUE_FIELD) && response.get(VALUE_FIELD).isArray();
	}

	/**
	 * Aggregates results from all pages into a single response.
	 */
	private JsonNode aggregatePages(GraphRequest request, String token, JsonNode firstPage) {
		ObjectNode combinedResults = objectMapper.createObjectNode();
		ArrayNode valueArray = combinedResults.putArray(VALUE_FIELD);

		// Add first page results
		valueArray.addAll((ArrayNode) firstPage.get(VALUE_FIELD));
		copyNonValueFields(firstPage, combinedResults);

		// Fetch and add subsequent pages
		String nextPageUrl = getNextPageUrl(firstPage);
		while (nextPageUrl != null) {
			JsonNode pageResults = fetchSinglePage(nextPageUrl, request.copyNoQuery(), token);
			valueArray.addAll((ArrayNode) pageResults.get(VALUE_FIELD));
			nextPageUrl = getNextPageUrl(pageResults);
		}

		return combinedResults;
	}

	/**
	 * Gets the URL for the next page if it exists.
	 */
	private String getNextPageUrl(JsonNode response) {
		return response.has(NEXT_LINK_FIELD) ?
				response.get(NEXT_LINK_FIELD).asText() :
				null;
	}

	/**
	 * Fetches a single page of results from the Graph API with retry handling.
	 */
	// In fetchSinglePage method
	private JsonNode fetchSinglePage(String url, GraphRequest request, String token) {
		return Retry.executeWithRetry(() -> {
			try {
				requestSemaphore.acquire();
				HttpRequest httpRequest = buildHttpRequest(url, request, token);
				logger.debug("Sending request to {}", url);

				try {
					HttpResponse<String> response = httpClient.send(
							httpRequest,
							HttpResponse.BodyHandlers.ofString()
					);

					if (response.statusCode() == THROTTELING_HTTP_CODE) {
						handleRateLimit(response);
						throw new GraphClientException("Rate limit hit, retrying");
					}

					handleGraphResponse(response);
					return objectMapper.readTree(response.body());

				} catch (IOException e) {
					// Special handling for GOAWAY errors
					if (e.getMessage() != null && e.getMessage().contains("GOAWAY")) {
						logger.warn("Received HTTP/2 GOAWAY, will retry with new connection");
						// Force a small delay before retry
						Thread.sleep(1000);
						throw new GraphClientException("HTTP/2 GOAWAY received");
					}
					throw e;
				}
			} finally {
				requestSemaphore.release();
			}
		}, maxRetries);
	}

	/**
	 * Copies non-value fields from source to target node.
	 */
	private void copyNonValueFields(JsonNode source, ObjectNode target) {
		source.fields().forEachRemaining(entry -> {
			if (!entry.getKey().equals(VALUE_FIELD) && !entry.getKey().equals(NEXT_LINK_FIELD)) {
				target.set(entry.getKey(), entry.getValue());
			}
		});
	}

	public <T extends GraphResponse> CompletableFuture<T> makeGraphRequest(GraphRequest request, Class<T> tClass) {
		return makeGraphRequest(request).thenApply(answer -> {
			try {
				return new ObjectMapper().readValue(answer.toString(), tClass);
			} catch (Exception ex) {
				throw new GraphClientException("Failed to parse response", ex);
			}
		});
	}

	/**
	 * Makes a paginated request to the Graph API with stream management.
	 */
	public PaginatedResult makePaginatedRequest(GraphRequest request) {
		try {
			// First authenticate and get the token synchronously
			String token = graphTokenGenerator.getAccessToken().join();

			String url = request.getEndpoint().startsWith("http")
					? request.getEndpoint()
					: environment.getGraphEndpoint() + request.getEndpoint();

			return Retry.executeWithRetry(() -> {
				try {
					// Acquire semaphore before making request
					requestSemaphore.acquire();

					HttpRequest httpRequest = buildHttpRequest(url, request, token);
					HttpResponse<String> response = httpClient.send(
							httpRequest,
							HttpResponse.BodyHandlers.ofString()
					);

					if (response.statusCode() == THROTTELING_HTTP_CODE) {
						handleRateLimit(response);
						throw new GraphClientException("Rate limit hit, retrying");
					}

					handleGraphResponse(response);
					return buildPaginatedResult(response.body());
				} finally {
					requestSemaphore.release();
				}
			}, maxRetries);

		} catch (Exception e) {
			throw new GraphClientException("Failed to make paginated request", e);
		}
	}

	/**
	 * Handles rate limit response by extracting retry-after header.
	 */
	private void handleRateLimit(HttpResponse<String> response) throws InterruptedException {
		long retryAfter = Long.parseLong(
				response.headers()
						.firstValue("Retry-After")
						.orElse("5")
		);
		logger.warn("Rate limit hit. Waiting {} seconds before retry", retryAfter);
		Thread.sleep(retryAfter * 1000);
	}

	/**
	 * Makes multiple requests in parallel and processes their results.
	 *
	 * @param requests        List of GraphRequest objects
	 * @param resultProcessor Function to process individual results
	 * @return CompletableFuture of the processed results
	 */
	public <T> CompletableFuture<List<T>> makeBatchRequest(
			List<GraphRequest> requests,
			Function<JsonNode, T> resultProcessor) {

		List<CompletableFuture<T>> futures = requests.stream()
				.map(request -> makeGraphRequest(request)
						.thenApply(resultProcessor))
				.toList();

		return CompletableFuture.allOf(
				futures.toArray(new CompletableFuture[0])
		).thenApply(v ->
				futures.stream()
						.map(CompletableFuture::join)
						.toList()
		);
	}

	/**
	 * Builds an HTTP request with headers, query parameters and appropriate method.
	 *
	 * @param baseUrl Base URL for the request
	 * @param request Graph request containing parameters and configuration
	 * @param token   Authentication token
	 * @return Built HttpRequest object
	 */
	private HttpRequest buildHttpRequest(String baseUrl, GraphRequest request, String token) {
		// Build the complete URL with query parameters
		URI uri = buildRequestUri(baseUrl, request.getQueryParams());

		// Configure the request builder with common headers
		HttpRequest.Builder builder = HttpRequest.newBuilder()
				.uri(uri)
				.header(ProtocolConstants.HEADER_AUTHORIZATION, ProtocolConstants.HEADER_BEARER_PREFIX + token)
				.header(ProtocolConstants.HEADER_CONSISTENCY_LEVEL, ProtocolConstants.CONSISTENCY_LEVEL_EVENTUAL)
				.timeout(requestTimeout);

		// Add any custom headers from the request
		request.getHeaders().forEach(builder::header);

		// Apply the appropriate HTTP method
		return switch (request.getMethod()) {
			case GET -> builder.GET().build();
			case POST -> builder.POST(request.getBody()).build();
			case PATCH -> builder.method("PATCH", request.getBody()).build();
			case PUT -> builder.PUT(request.getBody()).build();
			case DELETE -> builder.DELETE().build();
		};
	}

	/**
	 * Checks if phishing-resistant MFA is already enforced via a conditional access policy.
	 *
	 * @return CompletableFuture<Boolean> - true if enforced, false otherwise
	 */
	public CompletableFuture<Boolean> isPhishingResistantMfaEnforced() {
		return makeGraphRequest(
				GraphRequest.builder()
						.withEndpoint(EntraIDConstants.CA_POLICIES_ENDPOINT)
						.addQueryParam(PARAM_FILTER, "displayName eq '" + PHISHING_RESISTANT_POLICY_NAME + "'")
						.build()
		).thenApply(response -> {
			if (response.has(VALUE_FIELD) && response.get(VALUE_FIELD).isArray() && response.get(VALUE_FIELD).size() > 0) {
				// Check if the policy is enabled
				JsonNode policy = response.get(VALUE_FIELD).get(0);
				String state = policy.path(STATE).asText();

				// If the policy is enabled or in audit mode, consider phishing-resistant MFA enforced
				if (EntraIDConstants.STATE_ENABLED.equals(state)) {
					logger.info("Found enabled phishing-resistant MFA policy");
					return true;
				}

				logger.info("Found phishing-resistant MFA policy in state: {}", state);
				return false;
			}

			logger.info("No phishing-resistant MFA policy found");
			return false;
		});
	}


	/**
	 * Updates a Microsoft 365 Group.
	 *
	 * @param groupId ID of the group to update
	 * @param groupData JSON object containing the group data to update
	 * @return CompletableFuture that resolves to the API response
	 */
	public CompletableFuture<JsonNode> updateGroup(String groupId, ObjectNode groupData) {
		try {
			String jsonBody = groupData.toString();
			HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(jsonBody);

			GraphRequest request = GraphRequest.builder()
					.v1()
					.withEndpoint(GROUPS_ENDPOINT+"/" + groupId)
					.withMethod(HttpMethod.PATCH)
					.withBody(body)
					.addHeader(CONTENT_TYPE, APPLICATION_JSON)
					.build();

			return makeGraphRequest(request);
		} catch (Exception e) {
			return CompletableFuture.failedFuture(
					new RuntimeException("Failed to update group: " + e.getMessage(), e));
		}
	}

	/**
	 * Builds a URI with query parameters.
	 *
	 * @param baseUrl     Base URL for the request
	 * @param queryParams Map of query parameters
	 * @return URI with query parameters
	 */
	private URI buildRequestUri(String baseUrl, Map<String, String> queryParams) {
		if (queryParams == null || queryParams.isEmpty()) {
			return URI.create(baseUrl);
		}

		try {
			URI baseUri = URI.create(baseUrl);
			StringBuilder queryBuilder = new StringBuilder();

			// Handle existing query parameters
			String existingQuery = baseUri.getQuery();
			if (existingQuery != null && !existingQuery.isEmpty()) {
				queryBuilder.append(existingQuery);
				if (!existingQuery.endsWith("&")) {
					queryBuilder.append("&");
				}
			}

			// Process query parameters
			boolean first = queryBuilder.length() == 0;
			for (Map.Entry<String, String> entry : queryParams.entrySet()) {
				if (!first) {
					queryBuilder.append("&");
				}

				String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8);
				String value = entry.getValue(); // Don't encode the value
				queryBuilder.append(key).append("=").append(value);
				first = false;
			}

			return new URI(
					baseUri.getScheme(),
					baseUri.getAuthority(),
					baseUri.getPath(),
					queryBuilder.toString(),
					baseUri.getFragment()
			);
		} catch (URISyntaxException e) {
			throw new GraphClientException("Failed to build request URI", e);
		}
	}

	private void handleRateLimit(HttpResponse<String> response, int attempt) throws InterruptedException {
		long retryAfter = Long.parseLong(
				response.headers()
						.firstValue(RETRY_AFTER)
						.orElse("5")
		);
		logger.warn("Rate limit hit on attempt {}. Waiting {} seconds.", attempt, retryAfter);
		Thread.sleep(retryAfter * 1000);
	}

	@SuppressWarnings("java:S1144")
	private void handleRetry(int attempt, Exception cause) {
		long delay = (long) Math.pow(2, attempt) * 1000;
		logger.warn("Request failed on attempt {}. Retrying in {} ms. Cause: {}",
				attempt, delay, cause.getMessage());
		try {
			Thread.sleep(delay);
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
	}

	private PaginatedResult buildPaginatedResult(String responseBody) throws Exception {
		JsonNode responseJson = objectMapper.readTree(responseBody);

		List<JsonNode> values = new ArrayList<>();
		if (responseJson.has(VALUE_FIELD) && responseJson.get(VALUE_FIELD).isArray()) {
			responseJson.get(VALUE_FIELD).forEach(values::add);
		}

		String nextLink = null;
		if (responseJson.has(NEXT_LINK_FIELD)) {
			nextLink = responseJson.get(NEXT_LINK_FIELD).asText();
		}

		Integer totalCount = null;
		if (responseJson.has(Constants.COUNT_FIELD)) {
			totalCount = responseJson.get(Constants.COUNT_FIELD).asInt();
		}

		return new PaginatedResult(values, nextLink, totalCount);
	}

	@SuppressWarnings("java:S1144")
	private PaginatedResult handleRateLimitedPaginatedRequest(
			GraphRequest request,
			HttpResponse<String> response) throws Exception {

		handleRateLimit(response, 1);
		return makePaginatedRequest(request);
	}

	@Override
	public void close() {
		// Cleanup if needed
	}

	public static Builder builder() {
		return new Builder();
	}

	public MSEnvironment getEnvironment() {
		return environment;
	}

	public String getClientId() {
		return clientId;
	}

	/**
	 * Builder class for creating MicrosoftGraphClient instances.
	 */
	public static class Builder {
		private String clientId;
		private String clientSecret;
		private String refreshToken;
		private MSEnvironment environment = MSEnvironment.COMMERCIAL;
		private Duration requestTimeout = Duration.ofMinutes(5);
		private Duration connectTimeout = Duration.ofSeconds(30);
		private int maxRetries = 3;
		private String appId;
		private String certPath;
		private char[] certPassword;

		public Builder withClientId(String clientId) {
			this.clientId = clientId;
			return this;
		}

		public Builder withClientSecret(String clientSecret) {
			this.clientSecret = clientSecret;
			return this;
		}

		public Builder withRefreshToken(String refreshToken) {
			this.refreshToken = refreshToken;
			return this;
		}

		public Builder withEnvironment(MSEnvironment environment) {
			this.environment = environment;
			return this;
		}

		public Builder withRequestTimeout(Duration timeout) {
			this.requestTimeout = timeout;
			return this;
		}

		public Builder withConnectTimeout(Duration timeout) {
			this.connectTimeout = timeout;
			return this;
		}

		public Builder withMaxRetries(int maxRetries) {
			this.maxRetries = maxRetries;
			return this;
		}

		public Builder withCertPath(String certPath) {
			this.certPath = certPath;
			return this;
		}

		public Builder withCertPassword(char[] certPassword) {
			this.certPassword = certPassword;
			return this;
		}

		public Builder withAppId(String appId) {
			this.appId = appId;
			return this;
		}

		public MicrosoftGraphClient build() {
			return new MicrosoftGraphClient(this);
		}
	}
}
