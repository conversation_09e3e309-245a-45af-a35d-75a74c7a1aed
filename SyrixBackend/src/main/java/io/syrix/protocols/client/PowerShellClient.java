package io.syrix.protocols.client;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.HttpException;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.utils.Retry;
import io.syrix.protocols.utils.security.ITokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGeneratorFactory;

import static io.syrix.common.constants.Constants.*;
import static java.net.HttpURLConnection.HTTP_FORBIDDEN;
import static java.net.HttpURLConnection.HTTP_MOVED_TEMP;
import static java.net.HttpURLConnection.HTTP_OK;

// TODO We can take the correlation Id from the Headers like request-id=[2611ba02-a305-5e6c-2354-1e1f858c23a8] and service time like date=[Sat, 03 May 2025 10:58:01 GMT]
public class PowerShellClient implements AutoCloseable {
	public static final Map<String, Object> DEFAULT_PARAMETERS = Map.of("ErrorAction", "Stop");
	private static final ObjectMapper objectMapper = new ObjectMapper();
	private static final String SYSTEM_X_ANCHOR_MAILBOX = "UPN:SystemMailbox{bb558c35-97f1-4cb9-8ff7-d53741dc928c}@";
	private static final String X_ANCHOR_MAILBOX = "X-AnchorMailbox";

	private final HttpClient httpClient;
	private final MSEnvironment environment;
	private final String baseEndpoint;  // Base endpoint URL
	private final String endpointPath;  // Caller-provided path
	private final String appId;
	private final Duration connectTimeout;
	private final String tenantId;

	private final int maxRetries;
	private final ITokenGenerator tokenGenerator;
	private final String domain;

	private PowerShellClient(Builder builder) {
		this.appId = Objects.requireNonNull(builder.appId, "App ID cannot be null");
		this.environment = Objects.requireNonNull(builder.environment, "Environment cannot be null");
		this.endpointPath = Objects.requireNonNull(builder.endpointPath, "Endpoint path cannot be null");
		this.connectTimeout = builder.connectTimeout;
		this.maxRetries = builder.maxRetries;
		this.tenantId = builder.tenantId;
		this.baseEndpoint = environment.getOutlookEndpoint();
		this.domain = builder.domain;

		tokenGenerator = MSTokenGeneratorFactory.getInstance().getPowerShellTokenGenerator(
				MSTokenGenerator.getParams()
						.appId(appId)
						.identifier(tenantId)
						.environment(environment)
						.certPath(Objects.requireNonNull(builder.certificatePath, "certificatePath cannot be null"))
						.certPassword(Objects.requireNonNull(builder.certificatePassword, "certificatePassword cannot be null")));

		this.httpClient = HttpClient.newBuilder()
				.connectTimeout(connectTimeout)
				.followRedirects(HttpClient.Redirect.NORMAL)
				.build();
	}

	public CompletableFuture<JsonNode> executeCmdletCommand(CommandRequest command) {
		return Retry.executeWithRetry(() ->
						tokenGenerator.getAccessToken().thenCompose(token -> fetchAllPages(command, token)),
				maxRetries);
	}

	public CompletableFuture<String> getCPPSEndpoint() {
		try (HttpClient tmpHttpClient = HttpClient.newBuilder()
				.connectTimeout(connectTimeout)
				.followRedirects(HttpClient.Redirect.NEVER)
				.build()) {
			return Retry.executeWithRetry(() -> tmpHttpClient.sendAsync(
					HttpRequest.newBuilder()
							.uri(URI.create(environment.getSCPsEndpoint() + endpointPath + "/" + tenantId))
							.header(AUTHORIZATION_HEADER, BEARER_PREFIX + tokenGenerator.getAccessToken().join())
							.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
							.header("X-PreferServerAffinity", "true")
							.header(X_ANCHOR_MAILBOX, SYSTEM_X_ANCHOR_MAILBOX + domain)
							.GET()
							.build(),
					HttpResponse.BodyHandlers.ofString()
			).thenApply(response -> {
				String retUrl = environment.getSCPsEndpoint();
				if (response.statusCode() == HTTP_MOVED_TEMP) {
					if (response.statusCode() == HTTP_MOVED_TEMP) {
						retUrl = response.headers().firstValue("Location").map(this::buildCPPSEndpoint)
								.orElse(environment.getSCPsEndpoint());
					}
				} else if (response.statusCode() != HTTP_OK) {
					throw new HttpException(
							String.format("Failed to get domain details for %s, status code: %d, Response: %s",
									domain,
									response.statusCode(),
									response.body())
					);
				}
				return retUrl;
			}), this.maxRetries);
		}
	}

	private String buildCPPSEndpoint(String value) {
		try {
			URL redIrectUrl = new URI(value).toURL();
			URL defaultPSUrl = new URI(environment.getSCPsEndpoint()).toURL();
			String serverRedirect = redIrectUrl.getHost().split("\\.")[0];
			return String.format("%s://%s.%s", redIrectUrl.getProtocol(), serverRedirect, defaultPSUrl.getHost());
		} catch (URISyntaxException | MalformedURLException e) {
			throw new HttpException("Failed to build CPPS endpoint", e);
		}
	}

	private CompletableFuture<JsonNode> fetchAllPages(CommandRequest command, String token) {
		return fetchPage(command, token, null)
				.thenCompose(firstPage -> {
					List<JsonNode> allResults = new ArrayList<>();
					if (firstPage.has(VALUE_FIELD)) {
						firstPage.get(VALUE_FIELD).forEach(allResults::add);
					}

					if (firstPage.has(NEXT_LINK_FIELD)) {
						return fetchRemainingPages(allResults, firstPage.get(NEXT_LINK_FIELD).asText(), command, token);
					}

					return CompletableFuture.completedFuture(objectMapper.createArrayNode().addAll(allResults));
				});
	}

	private CompletableFuture<JsonNode> fetchRemainingPages(List<JsonNode> accumulator,
															String nextLink,
															CommandRequest command,
															String token) {
		return fetchPage(command, token, nextLink)
				.thenCompose(page -> {
					if (page.has(VALUE_FIELD)) {
						page.get(VALUE_FIELD).forEach(accumulator::add);
					}

					if (page.has(NEXT_LINK_FIELD)) {
						return fetchRemainingPages(accumulator, page.get(NEXT_LINK_FIELD).asText(), command, token);
					}

					return CompletableFuture.completedFuture(objectMapper.createArrayNode().addAll(accumulator));
				});
	}

	private CompletableFuture<JsonNode> fetchPage(CommandRequest command, String token, String nextLink) {
		try {
			String endpoint = nextLink != null ? nextLink : buildInitialEndpoint(command.scpsUrl);

			HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
					.uri(URI.create(endpoint))
					.header("Authorization", "Bearer " + token)
					.header("Content-Type", "application/json")
					.header("X-PreferServerAffinity", "true")
					.header("X-Organization", this.tenantId)
					.header("X-CmdletName", command.getCmdletName());

			String jsonBody = objectMapper.writeValueAsString(command.toRequestBody());
			requestBuilder.POST(HttpRequest.BodyPublishers.ofString(jsonBody));

			return httpClient.sendAsync(requestBuilder.build(), HttpResponse.BodyHandlers.ofString())
					.thenApply(response -> {
						if (response.statusCode() != HTTP_OK) {
							if (response.statusCode() == HTTP_FORBIDDEN) {
								throw new AccessForbiddenException("Unauthorized access", response.body());
							}
							throw new ExchangeClientException(
									String.format("Command '%s' failed with status code: %d, Response: %s",
											command.getCmdletName(),
											response.statusCode(),
											response.body())
							);
						}

						try {
							return objectMapper.readTree(response.body());
						} catch (Exception e) {
							throw new ExchangeClientException("Failed to parse response: " + e.getMessage(), e);
						}
					});
		} catch (Exception e) {
			return CompletableFuture.failedFuture(
					new ExchangeClientException("Failed to execute command: " + e.getMessage(), e)
			);
		}
	}

	private String buildInitialEndpoint(String scpsUrl) {
		String endpoint = scpsUrl != null && !scpsUrl.isEmpty() ? scpsUrl + endpointPath : baseEndpoint + endpointPath;
		if (endpointPath.contains("adminapi")) {
			endpoint += "/" + this.tenantId + "/InvokeCommand";
		}
		return endpoint;
	}

	public static Builder builder() {
		return new Builder();
	}

	public static class CommandRequest {
		private final String cmdletName;
		private final Map<String, Object> parameters;
		String scpsUrl = null;

		public CommandRequest(String cmdletName, Map<String, Object> parameters) {
			this.cmdletName = cmdletName;
			this.parameters = parameters != null ? parameters : new HashMap<>();
		}

		public CommandRequest(String cmdletName, Map<String, Object> parameters, String scpsUrl) {
			this(cmdletName, parameters);
			this.scpsUrl = scpsUrl;
		}

		public String getCmdletName() {
			return cmdletName;
		}

		public Map<String, Object> toRequestBody() {
			Map<String, Object> cmdletInput = new HashMap<>();
			cmdletInput.put("CmdletName", cmdletName);
			cmdletInput.put("Parameters", parameters);

			Map<String, Object> requestBody = new HashMap<>();
			requestBody.put("CmdletInput", cmdletInput);
			return requestBody;
		}
	}

	public static class Builder {
		private String appId;
		private String certificatePath;
		private char[] certificatePassword;
		private MSEnvironment environment = MSEnvironment.COMMERCIAL;
		private String endpointPath;  // New field for endpoint path
		private Duration requestTimeout = Duration.ofMinutes(5);
		private Duration connectTimeout = Duration.ofSeconds(30);
		private int maxRetries = 3;
		private String tenantId;
		private String domain;

		public Builder withEndpointPath(String endpointPath) {
			this.endpointPath = endpointPath;
			return this;
		}

		public Builder withAppId(String appId) {
			this.appId = appId;
			return this;
		}

		public Builder withCertificatePath(String certificatePath) {
			this.certificatePath = certificatePath;
			return this;
		}

		public Builder withCertificatePassword(char[] certificatePassword) {
			this.certificatePassword = certificatePassword;
			return this;
		}

		public Builder withEnvironment(MSEnvironment environment) {
			this.environment = environment;
			return this;
		}

		public Builder withRequestTimeout(Duration timeout) {
			this.requestTimeout = timeout;
			return this;
		}

		public Builder withConnectTimeout(Duration timeout) {
			this.connectTimeout = timeout;
			return this;
		}

		public Builder withMaxRetries(int maxRetries) {
			this.maxRetries = maxRetries;
			return this;
		}

		public PowerShellClient build() {
			return new PowerShellClient(this);
		}

		public Builder withTenantId(String tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder withDomain(String domain) {
			this.domain = domain;
			return this;
		}
	}

	@Override
	public void close() {
		// Cleanup if needed
	}

	public MSEnvironment getEnvironment() {
		return environment;
	}

	public String getAppId() {
		return appId;
	}

	public static class ExchangeClientException extends SyrixRuntimeException {
		public ExchangeClientException(String message) {
			super(message);
		}

		public ExchangeClientException(String message, Throwable cause) {
			super(message, cause);
		}
	}
}