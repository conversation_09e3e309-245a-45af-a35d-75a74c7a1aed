package io.syrix.protocols.client;

import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.utils.ValidationUtils;
import io.syrix.protocols.client.teams.powershell.command.TeamsPowerShellCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TeamsPowerShellCommandResult;
import io.syrix.protocols.exception.AccessForbiddenException;
import io.syrix.protocols.exception.BadRequestException;
import io.syrix.protocols.exception.ResourceNotFoundException;
import io.syrix.protocols.model.MSEnvironment;
import io.syrix.protocols.utils.Retry;
import io.syrix.protocols.utils.security.ITokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGenerator;
import io.syrix.protocols.utils.security.MSTokenGeneratorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Stream;

import static java.net.HttpURLConnection.*;

public class PowerShellTeamsClient implements AutoCloseable {
    private static final Logger log = LoggerFactory.getLogger(PowerShellTeamsClient.class);
    private final HttpClient httpClient;
    private final MSEnvironment environment;
    private final String baseEndpoint;  // Base endpoint URL
    private final String appId;
    private final Executor executor;
    private final int maxRetries;
    private final ITokenGenerator tokenGenerator;

    private PowerShellTeamsClient(Builder builder) {
        this.appId = builder.appId;
        this.environment = builder.environment;
        this.maxRetries = builder.maxRetries;
        this.baseEndpoint = environment.getTeamsEndpoint();
        this.executor = builder.executor;

        tokenGenerator = MSTokenGeneratorFactory.getInstance().getPowerShellTeamsTokenGenerator(
                MSTokenGenerator.getParams()
                        .appId(appId)
                        .identifier(builder.tenantId)
                        .environment(environment)
                        .certPath(Objects.requireNonNull(builder.certificatePath, "certificatePath cannot be null"))
                        .certPassword(Objects.requireNonNull(builder.certificatePassword, "certificatePassword cannot be null")));

        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(builder.connectTimeout)
                .executor(executor)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .build();
    }

    public <T extends TeamsPowerShellCommandResult> CompletableFuture<List<T>> execute(TeamsPowerShellCommand<T> command) {
        return Retry.executeWithRetry(() ->
                tokenGenerator.getAccessToken().thenCompose(token -> executeCommand(command, token)),
                maxRetries,
                executor
        );
    }

    private <T extends TeamsPowerShellCommandResult> CompletableFuture<List<T>> executeCommand(TeamsPowerShellCommand<T> command, String token) {
        String endpoint = baseEndpoint + command.getEndPoint();
        String[] headersAsArr = command.getHeaders().stream().flatMap(header -> Stream.of(header.name(), header.value())).toArray(String[]::new);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(endpoint))
                .header("Authorization", "Bearer " + token)
                .headers(headersAsArr)
                .method(command.getMethod().name(), HttpRequest.BodyPublishers.ofString(command.getBody()))
                .build();
        return httpClient.sendAsync(
                        request,
                        HttpResponse.BodyHandlers.ofString())
                .thenApplyAsync(response -> handleResponse(response, command), executor);
    }


    private <T extends TeamsPowerShellCommandResult> List<T> handleResponse(HttpResponse<String> response, TeamsPowerShellCommand<T> command) {
        if (log.isTraceEnabled()) {
            log.trace("[Request] commandName: {} commandBody: {}", command.getName(), command.getBody());
            log.trace("[Response] statusCode:{} body:{}", response.statusCode(), response.body());
        }
        try {
            return switch (response.statusCode()) {
                case HTTP_OK -> handleOkResponse(response, command);
                case HTTP_NO_CONTENT -> handleNoContent();
                case HTTP_FORBIDDEN -> handleForbidden(response);
                case HTTP_BAD_REQUEST -> handleBadRequest(response);
                case HTTP_NOT_FOUND -> handleNotFound(response);
                default -> handleDefault(response, command);
            };
        } catch (Exception ex) {
            log.error("command: {} body {}", command.getName(), command.getBody(), ex);
            throw ex;
        }
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleOkResponse(HttpResponse<String> response, TeamsPowerShellCommand<T> command) {
        try {
            return command.parseResponse(response.body());
        } catch (Exception ex) {
           throw new TeamsClientException("Failed to parse response: " + response.body(), ex);
        }
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleNoContent() {
        return Collections.emptyList();
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleForbidden(HttpResponse<String> response) {
        throw new AccessForbiddenException("Unauthorized access", response.uri().toString());
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleBadRequest(HttpResponse<String> response) {
        throw new BadRequestException(response.body(), response.uri().toString());
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleNotFound(HttpResponse<String> response) {
        throw new ResourceNotFoundException(response.body(), response.uri().toString());
    }

    private <T extends TeamsPowerShellCommandResult> List<T> handleDefault(HttpResponse<String> response, TeamsPowerShellCommand<T> command) {
        throw new TeamsClientException(
                String.format("Command '%s' with body '%s' failed with status code: %d, Response: %s",
                        command.getName(),
                        command.getBody(),
                        response.statusCode(),
                        response.body()));
    }

    public static Builder builder() {
        return new Builder();
    }

    @SuppressWarnings("unused")
    public static class Builder {
        private String appId;
        private String certificatePath;
        private char[] certificatePassword;
        private MSEnvironment environment = MSEnvironment.COMMERCIAL;
        private Duration connectTimeout = Duration.ofSeconds(30);
        private Executor executor = Executors.newVirtualThreadPerTaskExecutor();
        private int maxRetries = 3;
        private String tenantId;

        public Builder withAppId(String appId) {
            this.appId = appId;
            return this;
        }

        public Builder withCertificatePath(String certificatePath) {
            this.certificatePath = certificatePath;
            return this;
        }

        public Builder withCertificatePassword(char[] certificatePassword) {
            this.certificatePassword = certificatePassword;
            return this;
        }

        public Builder withEnvironment(MSEnvironment environment) {
            this.environment = environment;
            return this;
        }

        public Builder withConnectTimeout(Duration timeout) {
            this.connectTimeout = timeout;
            return this;
        }

        public Builder withMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public Builder withTenantId(String tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public Builder executor(Executor executor) {
            this.executor = executor;
            return this;
        }

        public PowerShellTeamsClient build() {
            ValidationUtils.Strings.requireNonBlank(appId, "App ID cannot be null or empty");
            ValidationUtils.Strings.requireNonBlank(tenantId, "Tenant ID cannot be null or empty");
            ValidationUtils.Objects.requireNonNull(environment, "Environment cannot be null");
            ValidationUtils.Objects.requireNonNull(connectTimeout, "connectTimeout cannot be null");
            ValidationUtils.isCondition(maxRetries < 1, "maxRetries must be greater than 1");

            return new PowerShellTeamsClient(this);
        }

    }

    @Override
    public void close() {
        // Cleanup if needed
    }

    public MSEnvironment getEnvironment() {
        return environment;
    }

    public String getAppId() {
        return appId;
    }

    public static class TeamsClientException extends SyrixRuntimeException {
        public TeamsClientException(String message) {
            super(message);
        }

        public TeamsClientException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}