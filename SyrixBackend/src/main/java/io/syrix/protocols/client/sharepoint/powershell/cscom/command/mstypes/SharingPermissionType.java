package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

public enum SharingPermissionType implements MsEnum {
    NONE(0), // can not set
    VIEW(1),
    EDIT(2);


    private final int intVal;

    SharingPermissionType(int intVal) {
        this.intVal = intVal;
    }

    @Override
    public int asInt() {
        return intVal;
    }

    public static SharingPermissionType fromInt(int intVal) {
        for (SharingPermissionType value : values()) {
            if (value.asInt() == intVal) {
                return value;
            }
        }
        throw new IllegalArgumentException("Can not convert int "+intVal+" to SharingPermissionType");
    }
}
