package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

/**
 * @see <a href="https://learn.microsoft.com/en-us/powershell/module/sharepoint-online/set-spotenant?view=sharepoint-ps#-coresharingcapability">SharingCapabilities</a
>*/
public enum SharingCapability implements MsEnum{
    DISABLE(0), //ONLYPEOPLEINORG  External user sharing (share by email) and guest link sharing are both disabled.
    EXTERNAL_USER_SHARING_ONLY(1), //NEWANDEXISTINGGUESTS -- External user sharing (share by email) is enabled, but guest link sharing is disabled.
    EXTERNAL_USER_AND_GUEST_SHARING(2),  //ANYONE -- External user sharing (share by email) and guest link sharing are both enabled.
    EXISTING_EXTERNAL_USER_SHARING_ONLY(3); //EXISTINGGUESTS  -- РOnly guests already in your organization's directory.

    private final int intVal;

    SharingCapability(int intVal) {
        this.intVal = intVal;
    }

    @Override
    public int asInt() {
        return intVal;
    }

    public static SharingCapability fromInt(int val) {
        for (SharingCapability value : values()) {
            if (value.asInt() == val) {
                return value;
            }
        }
        throw new IllegalArgumentException("Can not convert int "+val+" to SharingCapability");
    }

}
