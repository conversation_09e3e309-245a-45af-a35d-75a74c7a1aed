package io.syrix.protocols.client;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.protocols.model.GraphRequest;

import java.util.concurrent.CompletableFuture;

/**
 * Microsoft Graph client wrapper that uses delegated authentication flow for user-context operations.
 * This client is specifically designed for endpoints that require delegated permissions,
 * such as /admin/entra/uxSetting which cannot be accessed with application-only permissions.
 * 
 * Since the original MicrosoftGraphClient uses application-only permissions (certificate-based),
 * this wrapper creates a new client instance configured for delegated authentication flow.
 */
public class DelegatedMicrosoftGraphClient {
    
    private final MicrosoftGraphClient delegatedClient;
    
    /**
     * Creates a delegated Microsoft Graph client from an existing application-based client.
     * This method creates a new MicrosoftGraphClient instance configured for delegated authentication.
     *
     * @param applicationClient Existing application-based Microsoft Graph client
     * @return New delegated Microsoft Graph client wrapper
     */
    public static DelegatedMicrosoftGraphClient fromApplicationClient(MicrosoftGraphClient applicationClient) {
        return new DelegatedMicrosoftGraphClient(applicationClient);
    }
    
    /**
     * Private constructor that creates a new MicrosoftGraphClient configured for delegated authentication.
     * Note: This is a simplified approach that reuses the existing application client for delegated calls.
     * In a production environment, you would need to properly configure delegated authentication
     * with refresh tokens obtained through user consent flow.
     */
    private DelegatedMicrosoftGraphClient(MicrosoftGraphClient baseClient) {
        // For now, we'll use the base client directly
        // In production, this would need proper delegated token configuration
        this.delegatedClient = baseClient;
        
        // TODO: Implement proper delegated authentication with refresh token
        // This would require:
        // 1. User consent flow to obtain refresh token
        // 2. Creating new token generator with delegated permissions
        // 3. New client instance with delegated token generator
    }
    
    /**
     * Makes a Graph API request using delegated authentication.
     * Currently delegates to the base client - this should be updated to use
     * proper delegated authentication in production.
     *
     * @param request The Graph API request to execute
     * @return CompletableFuture containing the response
     */
    public CompletableFuture<JsonNode> makeGraphRequest(GraphRequest request) {
        // TODO: Use delegated authentication token instead of application token
        // For now, delegate to the base client
        throw new UnsupportedOperationException("Delegated authentication is not yet implemented for DelegatedMicrosoftGraphClient.");
    }
}