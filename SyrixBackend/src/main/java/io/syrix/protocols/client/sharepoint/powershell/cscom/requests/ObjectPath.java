package io.syrix.protocols.client.sharepoint.powershell.cscom.requests;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class ObjectPath {
    @JacksonXmlProperty(isAttribute = true)
    private String id;
    @JacksonXmlProperty(isAttribute = true)
    private String objectPathId;

    public ObjectPath(String id, String objectPathId) {
        this.id = id;
        this.objectPathId = objectPathId;
    }

    public static ObjectPath new_(String id, String objectPathId) {
        return new ObjectPath(id, objectPathId);
    }

}
