package io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class TenantProperties implements SharePointShellCommandDataResult {
    @JsonProperty("_ObjectType_")
    public String objectType;
    @JsonProperty("_ObjectIdentity_")
    public String objectIdentity;

    public Integer sharingCapability;
    public Integer odbSharingCapability;
    public Integer sharingDomainRestrictionMode;
    public Integer defaultSharingLinkType;
    public Integer defaultLinkPermission;
    public Integer requireAnonymousLinksExpireInDays;
    public Integer fileAnonymousLinkType;
    public Integer folderAnonymousLinkType;
    public Boolean emailAttestationRequired;
    public Integer emailAttestationReAuthDays;
    public String sharingAllowedDomainList;
    public String sharingBlockedDomainList;
    public Boolean requireAcceptingAccountMatchInvitedAccount;

    private final Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        additionalProperties.put(name, value);
    }
}
