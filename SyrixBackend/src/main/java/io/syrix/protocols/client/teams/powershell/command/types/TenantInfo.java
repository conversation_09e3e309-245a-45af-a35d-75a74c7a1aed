package io.syrix.protocols.client.teams.powershell.command.types;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.protocols.client.teams.powershell.command.serialization.OffsetDateTimeDeserializer;
import io.syrix.protocols.client.teams.powershell.command.serialization.OffsetDateTimeSerializer;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

@SuppressWarnings({"UnusedDeclaration"})
public class TenantInfo implements TeamsPowerShellCommandResult {
    public Object announcementsDisabled;
    public Object companyTags;
    public Object country;
    public Object defaultPoolFqdn;
    public List<AssignedPlan> assignedPlans;
    public String city;
    public List<Object> companyPartnership;
    public String countryLetterCode;
    public String createdDateTime;
    public Boolean dirSyncEnabled;
    public Object dataProviderErrors;
    public String displayName;
    public Map<String, String> lastProvisionTimeStamps; //Map<String, OffsetDateTime>
    public Map<String, String> lastPublishTimeStamps; //Map<String, OffsetDateTime>

    @JsonDeserialize(using = OffsetDateTimeDeserializer.class)
    @JsonSerialize(using = OffsetDateTimeSerializer.class)
    public OffsetDateTime lastSyncTimeStamp;
    public Object nameRecordingDisabled;
    public Object pools;
    public String objectId;
    public String postalCode;
    public String preferredLanguage;
    public List<ProvisionedPlan> provisionedPlans;
    public ServiceDiscovery serviceDiscovery;
    public String serviceInfo;
    public String serviceInstance;
    public List<String> sipDomains;
    public Object serviceNumberCount;
    public String state;
    public String street;
    public Object subscriberNumberCount;
    public SyncInLyncAdInfo syncInLyncAdInfo = new SyncInLyncAdInfo();
    public String teamsUpgradeEffectiveMode;
    public Boolean teamsUpgradeNotificationsEnabled;
    public String teamsUpgradeOverridePolicy;
    public String teamsUpgradePolicyIsReadOnly;
    public String tnmAccountId;
    public List<VerifiedDomain> verifiedDomains;
    @JsonDeserialize(using = OffsetDateTimeDeserializer.class)
    @JsonSerialize(using = OffsetDateTimeSerializer.class)
    public OffsetDateTime whenCreated;
    public Object whenChanged;

    public static class AssignedPlan {
        public String assignedTimestamp;
        public String capability;
        public String capabilityStatus;
        public Boolean isInGracePeriod;
        public String servicePlanId;
        public String subscribedPlanId;
    }

    public static class ProvisionedPlan {
        public String capabilityStatus;
        public String provisioningStatus;
        public String service;
    }

    public static class ServiceDiscovery {
        public Map<String, String> endpoints;
        public Map<String, String> headers;
    }

    public static class SyncInLyncAdInfo {
        public Object isSyncDisabledAtTenantCreation;
        public Object isUserSyncDisabled;
        public Object isUserSyncStateChanging;
        public Object stopSyncRevertCompleteTimestamp;
        public Object stopSyncRevertTimestamp;
        public Object stopSyncTimestamp;
    }

    public static class VerifiedDomain {
        public String name;
        public String status;
    }
}


