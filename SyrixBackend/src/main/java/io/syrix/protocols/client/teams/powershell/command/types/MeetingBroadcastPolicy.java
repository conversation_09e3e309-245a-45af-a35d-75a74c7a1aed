package io.syrix.protocols.client.teams.powershell.command.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.syrix.protocols.client.teams.powershell.command.serialization.BroadcastRecordingModeDeserializer;
import io.syrix.protocols.client.teams.powershell.command.serialization.BroadcastRecordingModeSerializer;

@SuppressWarnings("unused")
public class MeetingBroadcastPolicy implements TeamsPowerShellCommandResult {
    public Boolean allowBroadcastScheduling;
    public Boolean allowBroadcastTranscription;
    public String broadcastAttendeeVisibilityMode;
    @JsonSerialize(using = BroadcastRecordingModeSerializer.class)
    @JsonDeserialize(using = BroadcastRecordingModeDeserializer.class)
    public BroadcastRecordingMode broadcastRecordingMode;
    public String configId;
    public ConfigMetadata configMetadata;
    public String dataSource;
    public String description;
    public String identity;
    public Key key;

    public static class ConfigMetadata {
        public String authority;
    }

    public static class Key {
        public AuthorityId authorityId;
        public DefaultXml defaultXml;
        public SchemaId schemaId;
        public String scopeClass;
        public XmlRoot xmlRoot;
    }

    public static class AuthorityId {
        @JsonProperty(value = "Class")
        public String classType;
        public String instanceId;
        public XmlRoot xmlRoot;
    }

    public static class DefaultXml {
        public Object configObject;
        public Data data;
        public Boolean isModified;
        public SchemaId schemaId;
        public String signature;
    }

    public static class Data {
        public TeamsMeetingBroadcastPolicyData teamsMeetingBroadcastPolicy;
    }

    public static class TeamsMeetingBroadcastPolicyData {
        @JsonProperty(value = "@xmlns")
        public String xmlns;
    }

    public static class SchemaId {
        public XName xName;
    }

    public static class XName {
        public String name;
    }

    public static class XmlRoot {
        public String name;
    }
}

