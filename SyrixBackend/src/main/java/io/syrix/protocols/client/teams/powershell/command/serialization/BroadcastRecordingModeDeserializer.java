package io.syrix.protocols.client.teams.powershell.command.serialization;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import io.syrix.protocols.client.teams.powershell.command.types.BroadcastRecordingMode;

import java.io.IOException;

public class BroadcastRecordingModeDeserializer extends JsonDeserializer<BroadcastRecordingMode> {
    @Override
    public BroadcastRecordingMode deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText();
        return BroadcastRecordingMode.fromString(value);
    }
}
