package io.syrix.reports;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.exceptions.ReportGenerationException;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.reports.OPA.OpaClient;
import io.syrix.reports.model.ReportConfig;
import io.syrix.reports.model.ReportSummary;
import io.syrix.reports.service.MarkdownProcessor;
import io.syrix.reports.service.ReportService;
import io.syrix.reports.service.ReportTemplateService;
import io.syrix.utils.baseline.BaselineGroup;
import io.syrix.utils.baseline.SecureBaselineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static io.syrix.datamodel.report.ConfigurationServiceType.ALL;


public class Report {
	private static final Logger logger = LoggerFactory.getLogger(Report.class);

	private Report() {
	}

	public static JsonNode generate(Path outputDir,
									Path configFile,
									Collection<ConfigurationServiceType> serviceTypes,
									ObjectMapper objectMapper, boolean separateReports) {
		ObjectNode evaluationDataAll = objectMapper.createObjectNode();
		for (ConfigurationServiceType serviceType : serviceTypes) {
			try {
				// Evaluate configuration data
				JsonNode resultNode = evaluateConfigData(configFile, serviceType, objectMapper);
				if(separateReports) {
					ObjectNode evaluationDataSeparate = objectMapper.createObjectNode();
					evaluationDataSeparate.set(serviceType.getResultName(), resultNode);
					Path evalFile = writeEvalData(outputDir, evaluationDataSeparate);
					createReport(outputDir, configFile, evalFile, serviceType);
					logger.info("Evaluation result saved to {}", evalFile);
				}
				evaluationDataAll.set(serviceType.getResultName(), resultNode);
			} catch (Exception e) {
				throw new ReportGenerationException("Failed to parse evaluation result for " + serviceType.name(), e);
			}
		}
		Path evalFile = writeEvalData(outputDir, evaluationDataAll);
		createReport(outputDir, configFile, evalFile, ALL);
		return evaluationDataAll;
	}

	private static Path writeEvalData(Path outputDir, JsonNode evaluationData) {
		String fileName = "evaluation.json";
		Path evalFile = outputDir.resolve(fileName);
		try {
			Files.writeString(evalFile, evaluationData.toString());
			logger.info("Evaluation data saved to {}", evalFile);
			return evalFile;
		} catch (IOException e) {
			throw new ReportGenerationException("Failed to save evaluation data", e);
		}
	}

	private static JsonNode evaluateConfigData(Path reportFile, ConfigurationServiceType serviceType, ObjectMapper objectMapper) {
		if (Files.exists(reportFile)) {
			logger.info("Configuration report saved to {}", reportFile);
		} else {
			logger.error("Configuration report not found at {}", reportFile);
		}

		try (OpaClient opaClient = new OpaClient()) {
			String result = opaClient.evaluate(reportFile, serviceType.getResultName(), "");
			return objectMapper.readTree(result);
		} catch (IOException e) {
			throw new ReportGenerationException("Failed to evaluate data", e);
		}
	}

	private static ReportSummary createReport(Path outputDir,
											  Path settingsExports,
											  Path evalFile,
											  ConfigurationServiceType serviceType) {
		MarkdownProcessor markdownProcessor = new MarkdownProcessor();
		ReportTemplateService templateService = new ReportTemplateService();
		SecureBaselineService baselineService = new SecureBaselineService();

		// Set up paths
		List<String> serviceTypes = null;
		Path tempOutputDir = null;
		if(serviceType == ALL) {
			tempOutputDir = outputDir;
			serviceTypes = Arrays.stream(ConfigurationServiceType.values()).filter(t -> t != ALL)
					.map(ConfigurationServiceType::getResultName).toList();
		} else {
			serviceTypes = List.of(serviceType.getResultName());
			tempOutputDir = outputDir.resolve("IndividualReports");
			try {
				Files.createDirectories(tempOutputDir);
			} catch (IOException e) {
				throw new ReportGenerationException("Failed to create report directory", e);
			}
		}

		Map<String, List<BaselineGroup>> secureBaselines = baselineService.importSecureBaseline(serviceTypes);

		// Import secure baselines
		// Initialize report service
		ReportService reportService = new ReportService(
				tempOutputDir,
				markdownProcessor,
				templateService
		);

		// Create report config
		ReportConfig reportConfig = ReportConfig.builder()
				.baselineName(serviceType.getResultName())
				.fullName(serviceType.getFullName())
				.outProviderFileName(settingsExports.getFileName().toString())  // Use same filename as config JSON
				.configPath(settingsExports)
				.outRegoFileName(evalFile.getFileName().toString())
				.darkMode(false)
				.reportsFolderName(outputDir.toString())
				.secureBaselines(secureBaselines)
				.build();

		// Generate report
		try {
			ReportSummary summary = reportService.generateReport(reportConfig);
			logger.info("Report generated successfully with {} passes, {} failures",
					summary.getPasses(), summary.getFailures());
			// Log report location
			logger.info("HTML report saved to {}", tempOutputDir.resolve(summary.getReportHTMLPath()));
			logger.info("JSON report saved to {}", tempOutputDir.resolve(summary.getReportJSONPath()));
			return summary;
		} catch (Exception e) {
			throw new ReportGenerationException("Report generation failed", e);
		}
	}
}
