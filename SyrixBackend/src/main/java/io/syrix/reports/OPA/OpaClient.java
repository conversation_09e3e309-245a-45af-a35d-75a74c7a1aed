package io.syrix.reports.OPA;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.utils.FileUtils;
import org.json.HTTP;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.Duration;

import static io.syrix.common.constants.Constants.APPLICATION_JSON;
import static io.syrix.common.constants.Constants.CONTENT_TYPE_HEADER;
import static java.net.HttpURLConnection.HTTP_OK;

public class OpaClient implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(OpaClient.class);
	private static final String DEFAULT_OPA_URL = "http://localhost:8181";
	private static final ObjectMapper objectMapper = new ObjectMapper();
	private final HttpClient httpClient;
	private final String baseUrl;

	public OpaClient() {
		this(DEFAULT_OPA_URL);
	}

	public OpaClient(String baseUrl) {
		this.baseUrl = baseUrl;
		this.httpClient = HttpClient.newBuilder()
				.version(HttpClient.Version.HTTP_1_1)
				.connectTimeout(Duration.ofSeconds(10))
				.build();
	}

	/**
	 * Evaluates data against an OPA policy.
	 *
	 * @param input The input data to evaluate
	 * @param packageName The package name containing the rule
	 * @param data Additional data to include in evaluation
	 * @return The evaluation result as JsonNode
	 */
	public String evaluate(Path input, String packageName, String data) throws IOException {
		// Construct evaluation request body
		ObjectNode requestBody = objectMapper.createObjectNode();
		JsonNode  inputJson = FileUtils.readJsonFile(input);
		requestBody.set("input", inputJson);

		if (data != null && !data.isBlank()) {
			requestBody.set("data", objectMapper.readTree(data));
		}

		String evaluateUrl = String.format("%s/v1/data/%s/tests", baseUrl, packageName);

		HttpRequest request = HttpRequest.newBuilder()
				.uri(URI.create(evaluateUrl))
				.header(CONTENT_TYPE_HEADER, APPLICATION_JSON)
				.POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
				.build();

		try {
			HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
			if (response.statusCode() != HTTP_OK) {
				throw new IOException(String.format("OPA evaluation failed with status code %d: %s",
						response.statusCode(), response.body()));
			}
			return response.body();
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new IOException("OPA evaluation interrupted", e);
		}
	}

	/**
	 * Checks OPA server health
	 */
	public boolean isHealthy() {
		try {
			HttpRequest request = HttpRequest.newBuilder().uri(URI.create(baseUrl + "/health")).GET().build();
			HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
			return response.statusCode() == HTTP_OK;
		} catch (Exception e) {
			logger.warn("Health check failed", e);
			return false;
		}
	}

	@Override
	public void close() {
		// HttpClient doesn't need explicit cleanup
	}
}