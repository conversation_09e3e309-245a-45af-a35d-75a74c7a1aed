package io.syrix.reports;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ReportGenerationException;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.reports.OPA.OpaClient;
import io.syrix.reports.model.opa.EvaluationReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;

/**
 * Generates OPA evaluation reports for Microsoft 365 security configurations.
 * This class focuses on policy evaluation without generating HTML/JSON report files.
 */
public class OpaEvaluationReport {
    private static final Logger logger = LoggerFactory.getLogger(OpaEvaluationReport.class);
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private OpaEvaluationReport() {
        // Utility class
    }
    
    /**
     * Generates an evaluation report by evaluating configuration data against OPA policies.
     *
     * @param configFile   Path to the configuration file containing Microsoft 365 settings
     * @param serviceTypes Collection of service types to evaluate
     * @return EvaluationReport containing policy evaluation results
     * @throws ReportGenerationException if evaluation fails
     */
    public static EvaluationReport generate(Path configFile, Collection<ConfigurationServiceType> serviceTypes) {
        logger.info("Starting OPA evaluation for {} service types", serviceTypes.size());
        
        if (!Files.exists(configFile)) {
            throw new ReportGenerationException("Configuration file not found at: " + configFile);
        }
        
        try {
            // Create aggregated evaluation result
            JsonNode aggregatedResult = evaluateAllServices(configFile, serviceTypes);
            
            // Convert to EvaluationReport model
            EvaluationReport evaluationReport = objectMapper.convertValue(aggregatedResult, EvaluationReport.class);
            
            logger.info("OPA evaluation completed successfully for {} services", serviceTypes.size());
            return evaluationReport;
            
        } catch (Exception e) {
            throw new ReportGenerationException("Failed to generate OPA evaluation report", e);
        }
    }
    
    /**
     * Evaluates configuration data for all specified service types.
     */
    private static JsonNode evaluateAllServices(Path configFile, Collection<ConfigurationServiceType> serviceTypes) {
        var evaluationDataAll = objectMapper.createObjectNode();
        
        for (ConfigurationServiceType serviceType : serviceTypes) {
            try {
                logger.debug("Evaluating service type: {}", serviceType.name());
                
                JsonNode resultNode = evaluateConfigurationData(configFile, serviceType);
                evaluationDataAll.set(serviceType.getResultName(), resultNode);
                
                logger.debug("Successfully evaluated service type: {}", serviceType.name());
                
            } catch (Exception e) {
                logger.error("Failed to evaluate service type: {}", serviceType.name(), e);
                throw new ReportGenerationException(
                    "Failed to evaluate configuration for service: " + serviceType.name(), e);
            }
        }
        
        return evaluationDataAll;
    }
    
    /**
     * Evaluates configuration data for a specific service type using OPA.
     */
    private static JsonNode evaluateConfigurationData(Path configFile, ConfigurationServiceType serviceType) {
        logger.debug("Evaluating configuration data for service: {}", serviceType.getResultName());
        
        try (OpaClient opaClient = new OpaClient()) {
            // Check OPA server health before evaluation
            if (!opaClient.isHealthy()) {
                throw new ReportGenerationException("OPA server is not healthy or not accessible");
            }
            
            String evaluationResult = opaClient.evaluate(configFile, serviceType.getResultName(), "");
            JsonNode resultNode = objectMapper.readTree(evaluationResult);
            
            logger.debug("Successfully evaluated configuration for service: {}", serviceType.getResultName());
            return resultNode;
            
        } catch (IOException e) {
            throw new ReportGenerationException("Failed to evaluate configuration data for service: " + serviceType.getResultName(), e);
        }
    }
}
