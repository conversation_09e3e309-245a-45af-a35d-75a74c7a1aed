package io.syrix.reports.service;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Helper;
import com.github.jknack.handlebars.Template;
import com.github.jknack.handlebars.io.ClassPathTemplateLoader;
import com.github.jknack.handlebars.io.TemplateLoader;
import io.syrix.common.exceptions.TemplateProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for processing HTML templates using the Handlebars templating engine.
 */
public class ReportTemplateService {
	private static final Logger logger = LoggerFactory.getLogger(ReportTemplateService.class);

	private final Handlebars handlebars;
	private final Map<String, Template> templateCache;

	public ReportTemplateService() {
		// Initialize template loader to look for templates in /templates directory
		TemplateLoader loader = new ClassPathTemplateLoader();
		loader.setPrefix("/templates");
		loader.setSuffix(".hbs");

		this.handlebars = new Handlebars(loader);
		this.templateCache = new HashMap<>();

		// Register custom helpers
		registerHelpers();
	}

	/**
	 * Process a template with the provided data.
	 */
	public String processTemplate(String templateName, Map<String, Object> data) {
		try {
			Template template = getTemplate(templateName);
			return template.apply(data);
		} catch (IOException e) {
			throw new TemplateProcessingException(
					"Failed to process template: " + templateName, e);
		}
	}

	/**
	 * Gets a template from cache or loads it if not cached.
	 */
	private Template getTemplate(String templateName) throws IOException {
		Template template = templateCache.get(templateName);
		if (template == null) {
			template = handlebars.compile(templateName);
			templateCache.put(templateName, template);
		}
		return template;
	}

	/**
	 * Register custom Handlebars helpers.
	 */
	private void registerHelpers() {
		// Helper for conditional CSS classes
		handlebars.registerHelper("cssClass", (Helper<String>) (result, options) -> {
			switch (result) {
				case "Pass":
					return "pass";
				case "Fail":
					return "fail";
				case "Warning":
					return "warning";
				case "Error":
					return "error";
				case "N/A":
					return "manual";
				case "Omitted":
					return "omitted";
				default:
					return "";
			}
		});

		// Helper for dark mode
		handlebars.registerHelper("darkModeClass", (Helper<Boolean>) (darkMode, options) ->
				darkMode ? "dark-mode" : "");

		// Helper for conditional content
		handlebars.registerHelper("ifEquals", (Helper<Object>) (obj1, options) -> {
			Object obj2 = options.param(0);
			if (obj1 == null && obj2 == null) {
				return options.fn();
			}
			if (obj1 != null && obj1.equals(obj2)) {
				return options.fn();
			}
			return options.inverse();
		});

		// Helper for array iteration with index
		handlebars.registerHelper("forEach", (Helper<Object[]>) (array, options) -> {
			StringBuilder buffer = new StringBuilder();
			for (int i = 0; i < array.length; i++) {
				Map<String, Object> context = new HashMap<>();
				context.put("item", array[i]);
				context.put("index", i);
				buffer.append(options.fn(context));
			}
			return buffer.toString();
		});
	}
}