package io.syrix.reports.service;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Template;
import com.github.jknack.handlebars.helper.StringHelpers;
import io.syrix.products.microsoft.entra.model.reccomendations.Recommendation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecommendationReportService {
	private static final Logger logger = LoggerFactory.getLogger(RecommendationReportService.class);
	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm:ss");
	private static final String TEMPLATE_PATH = "/templates/recommendation-report.hbs";

	private final Handlebars handlebars;
	private final Template template;

	public RecommendationReportService() {
		this.handlebars = new Handlebars();
		registerHelpers();
		this.template = initializeTemplate();
	}

	private void registerHelpers() {
		// Register built-in string helpers
		StringHelpers.register(handlebars);

		// Add custom helper for date formatting
		handlebars.registerHelper("formatDate", (context, options) -> {
			if (context == null) return "";
			if (context instanceof TemporalAccessor temporal) {
				return DATE_FORMATTER.format(temporal);
			}
			return context.toString(); // Fallback if not a date/time type
		});

		// Add helper for status color
		handlebars.registerHelper("statusColor", (context, options) -> {
			if (context == null) return "gray";
			String status = context.toString();
			return switch (status) {
				case "ACTIVE" -> "yellow";
				case "COMPLETED_BY_SYSTEM", "COMPLETED_BY_USER" -> "green";
				case "POSTPONED" -> "orange";
				case "DISMISSED" -> "gray";
				default -> "gray";
			};
		});

		// Add helper for priority color
		handlebars.registerHelper("priorityColor", (context, options) -> {
			if (context == null) return "gray";
			return switch (context.toString()) {
				case "HIGH" -> "red";
				case "MEDIUM" -> "orange";
				case "LOW" -> "green";
				default -> "gray";
			};
		});

		// Equality helper
		handlebars.registerHelper("eq", (context, options) -> {
			if (context == null) return false;
			return context.toString().equals(options.param(0));
		});
	}

	private Template initializeTemplate() {
		try (InputStream is = getClass().getResourceAsStream(TEMPLATE_PATH)) {
			if (is == null) {
				throw new IOException("Template file not found: " + TEMPLATE_PATH);
			}
			String templateContent = new String(is.readAllBytes(), StandardCharsets.UTF_8);
			return handlebars.compileInline(templateContent);
		} catch (IOException e) {
			logger.error("Failed to initialize Handlebars template", e);
			throw new RuntimeException("Failed to initialize report template", e);
		}
	}

	public void generateReport(List<Recommendation> recommendations, Path outputPath) {
		try {
			Map<String, Object> context = new HashMap<>();
			context.put("recommendations", recommendations);
			context.put("generatedDate", java.time.LocalDateTime.now());
			context.put("totalCount", recommendations.size());
			context.put("activeCount", countByStatus(recommendations, "ACTIVE"));
			context.put("completedCount",
					countByStatus(recommendations, "COMPLETED_BY_SYSTEM") +
							countByStatus(recommendations, "COMPLETED_BY_USER"));
			context.put("postponedCount", countByStatus(recommendations, "POSTPONED"));

			String report = template.apply(context);
			Files.writeString(outputPath, report);
			logger.info("Report generated successfully at: {}", outputPath);

		} catch (IOException e) {
			logger.error("Failed to generate recommendations report", e);
			throw new RuntimeException("Failed to generate report", e);
		}
	}

	private long countByStatus(List<Recommendation> recommendations, String status) {
		return recommendations.stream()
				.filter(r -> r.getStatus() != null && r.getStatus().toString().equals(status))
				.count();
	}
}