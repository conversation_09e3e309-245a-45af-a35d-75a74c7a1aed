package io.syrix.reports.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.exceptions.ReportGenerationException;
import io.syrix.common.utils.FileUtils;
import io.syrix.reports.model.LicenseInfo;
import io.syrix.reports.model.ReportConfig;
import io.syrix.reports.model.ReportFragment;
import io.syrix.reports.model.ReportJson;
import io.syrix.reports.model.ReportSummary;
import io.syrix.reports.model.TestResult;
import io.syrix.utils.baseline.BaselineGroup;
import io.syrix.utils.baseline.Control;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static io.syrix.datamodel.report.ConfigurationServiceType.ALL;


/**
 * Service for generating security baseline reports.
 * Handles the creation of HTML and JSON reports for security baseline assessments.
 */
public class ReportService {
    private static final Logger logger = LoggerFactory.getLogger(ReportService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SCUBA_GITHUB_URL = "https://github.com/cisagov/ScubaGear";

    private final Path individualReportPath;
    private final MarkdownProcessor markdownProcessor;
    private final ReportTemplateService templateService;
    private final Set<String> processedFiles = new HashSet<>();
    private final Map<String, String> productNameMap;

    public ReportService(Path individualReportPath,
                         MarkdownProcessor markdownProcessor,
                         ReportTemplateService templateService) {
        this.individualReportPath = individualReportPath;
        this.markdownProcessor = markdownProcessor;
        this.templateService = templateService;
        this.productNameMap = initializeProductNameMap();
        objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * Generates a new security baseline report.
     */
    public ReportSummary generateReport(ReportConfig config) {
        logger.info("Starting report generation for baseline: {}", config.baselineName());

        try {
            // Load settings and test results
            JsonNode settingsExport = loadSettingsExport(config);
            JsonNode testResults = loadTestResults(config);

            // Initialize report data structures
            List<String> fragments = new ArrayList<>();
            ReportSummary summary = initializeReportSummary(settingsExport, config);
            ReportJson reportJson = new ReportJson(summary);

            // Generate report sections for each baseline group
            List<BaselineGroup> groups;
            if(config.baselineName().equalsIgnoreCase(ALL.name())) {
                groups = config.secureBaselines().values().stream()
                        .flatMap(Collection::stream)
                        .toList();
            } else {
                groups = config.secureBaselines().get(config.baselineName());
            }
            Map<String, JsonNode> policyMap = createPolicyMap(testResults);
            for (BaselineGroup group : groups) {
                processBaselineGroup(policyMap, group, testResults, settingsExport, fragments, reportJson, summary);
            }

            // Generate final HTML report
            generateHtmlReport(config, fragments, settingsExport);

            // Save JSON report
            saveJsonReport(reportJson, config);

            return summary;

        } catch (Exception e) {
            throw new ReportGenerationException("Failed to generate report", e);
        }
    }

    private void processBaselineGroup(Map<String, JsonNode> policyMap,
                                      BaselineGroup group,
                                      JsonNode testResults,
                                      JsonNode settingsExport,
                                      List<String> fragments,
                                      ReportJson reportJson,
                                      ReportSummary summary) {

        List<ReportFragment> groupFragments = new ArrayList<>();
        // Process each control in the group
        for (Control control : group.getControls()) {
            JsonNode test = policyMap.get(control.id());
            if (test != null) {
                processControl(control, test, settingsExport, groupFragments, summary);
            } else {
                handleMissingTest(control, groupFragments, summary);
            }
        }

        // Add group fragments to report
        String groupHtml = generateGroupHtml(group, groupFragments, settingsExport);
        fragments.add(groupHtml);

        // Add to JSON report
        reportJson.addGroupResults(group, groupFragments);
    }

    /**
     * Loads and validates the settings export file.
     */
    private JsonNode loadSettingsExport(ReportConfig config) {
        Path settingsPath = config.configPath();
        if (!processedFiles.add(settingsPath.toString())) {
            logger.debug("Settings already processed for {}", settingsPath);
            return objectMapper.createObjectNode();
        }

        JsonNode settings = FileUtils.readJsonFile(settingsPath);
        validateSettings(settings);
        return settings;
    }

    /**
     * Loads and parses the test results file.
     */
    private JsonNode loadTestResults(ReportConfig config) {
        Path resultsPath = Paths.get(config.reportsFolderName(), config.outRegoFileName());
        return FileUtils.readJsonFile(resultsPath);
    }

    /**
     * Initialize report summary with metadata from settings.
     */
    private ReportSummary initializeReportSummary(JsonNode settingsExport, ReportConfig config) {
        return ReportSummary.builder()
                .warnings(0)
                .failures(0)
                .passes(0)
                .omits(0)
                .manual(0)
                .errors(0)
                .jsonReportFilePath(config.reportsFolderName())
                .htmlReportFilePath(config.reportsFolderName())
                .date(parseTimestamp(settingsExport.path("timestamp_zulu").asText()))
                .build();
    }

    private void processControl(Control control,
                                JsonNode test,
                                JsonNode settings,
                                List<ReportFragment> fragments,
                                ReportSummary summary) {

        // Check for policy omission
        if (isControlOmitted(control.id(), settings)) {
            handleOmittedControl(control, settings, fragments, summary);
            return;
        }

        // Process test results
        TestResult result = evaluateTestResult(test, settings, control.baselineName());

        // Update summary statistics
        updateSummaryStats(result, summary);

        // Create fragment
        ReportFragment fragment = new ReportFragment.Builder()
                .controlId(control.id())
                .requirement(control.value())
                .result(result.status())
                .criticality(test.get("Criticality").asText())
                .details(result.details())
                .build();

        fragments.add(fragment);
    }

    private boolean isControlOmitted(String controlId, JsonNode settings) {
        JsonNode config = settings.path("scuba_config");
        return getOmissionState(config, controlId);
    }

    private boolean getOmissionState(JsonNode config, String controlId) {
        if (!config.has("OmitPolicy") || !config.get("OmitPolicy").has(controlId)) {
            return false;
        }

        JsonNode omitPolicy = config.get("OmitPolicy").get(controlId);

        if (!omitPolicy.has("Expiration")) {
            return true;
        }

        String expirationStr = omitPolicy.get("Expiration").asText();
        if (expirationStr.isEmpty()) {
            return true;
        }

        try {
            Instant expiration = Instant.parse(expirationStr);
            return expiration.isAfter(Instant.now());
        } catch (Exception e) {
            logger.warn("Invalid expiration date format for control {}: {}", controlId, expirationStr);
            return false;
        }
    }

    private void generateHtmlReport(ReportConfig config, List<String> fragments, JsonNode settings) {
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("title", config.fullName() + " Baseline Report");
//		templateData.put("baselineUrl", getBaselineUrl(config, settings));
        templateData.put("fragments", fragments);
        templateData.put("darkMode", config.darkMode());
        addAadSpecificContent(templateData, settings);
        String htmlReport = templateService.processTemplate("report", templateData);
        Path reportPath = individualReportPath.resolve(config.baselineName() + "Report.html");
        FileUtils.writeTextFile(htmlReport, reportPath);
    }

    private void addAadSpecificContent(Map<String, Object> templateData,
                                       JsonNode settings) {
        // Add AAD-specific warning and licensing info
        templateData.put("aadWarning", true);
        templateData.put("licenseInfo", processLicenseInfo(settings));
    }

    private List<LicenseInfo> processLicenseInfo(JsonNode settings) {
        List<LicenseInfo> licenses = new ArrayList<>();
        JsonNode licenseData = settings.path("license_information").path("licenses");

        for (JsonNode license : licenseData) {
            licenses.add(new LicenseInfo(
                    getMappedProductName(license.get("skuId").asText()),
                    license.get("skuPartNumber").asText(),
                    license.get("ConsumedUnits").asInt(),
                    license.path("PrepaidUnits").path("Enabled").asInt()
            ));
        }

        return licenses;
    }

    private String saveJsonReport(ReportJson reportJson, ReportConfig config) {
        try {
            Path jsonPath = individualReportPath.resolve(config.baselineName() + "Report.json");
            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(reportJson)
                    .replace("\\u003c", "<")
                    .replace("\\u003e", ">")
                    .replace("\\u0027", "'");

            FileUtils.writeTextFile(jsonContent, jsonPath);
            logger.debug("Saved JSON report to {}", jsonPath);
            return jsonPath.toString();
        } catch (Exception e) {
            throw new ReportGenerationException("Failed to save JSON report", e);
        }
    }

    private void validateSettings(JsonNode settings) {
        if (!settings.has("tenant_details")) {
            throw new ReportGenerationException("Settings export missing tenant details");
        }
        if (!settings.has("timestamp_zulu")) {
            throw new ReportGenerationException("Settings export missing timestamp");
        }
        if (!settings.has("Syrix_version")) {
            throw new ReportGenerationException("Settings export missing module version");
        }
    }

    private Instant parseTimestamp(String timestamp) {
        try {
            return Instant.parse(timestamp);
        } catch (Exception e) {
            logger.warn("Failed to parse timestamp: {}", timestamp);
            return Instant.now();
        }
    }

    /**
     * Creates a Map with PolicyId as key and the corresponding JsonNode as value
     *
     * @param rootNode The root JsonNode of the JSON structure
     * @return A Map with PolicyId to JsonNode mapping
     */
    public static Map<String, JsonNode> createPolicyMap(JsonNode rootNode) {
        Map<String, JsonNode> policyMap = new HashMap<>();

        // Iterate through all root-level fields to find the one with "result" array
        // (handling different possible root keys beyond just "aad")
        Iterator<String> fieldNames = rootNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            JsonNode node = rootNode.get(fieldName);

            // Check if this node has a "result" array
            if (node.has("result") && node.get("result").isArray()) {
                JsonNode resultArray = node.get("result");

                // Process each policy result in the array
                for (JsonNode policyNode : resultArray) {
                    if (policyNode.has("PolicyId")) {
                        String policyId = policyNode.get("PolicyId").asText();
                        policyMap.put(policyId, policyNode);
                    }
                }
            }
        }

        return policyMap;
    }

    static JsonNode findNodeByPolicyId(JsonNode rootNode, String policyId) {
        // Iterate through all top-level keys (like "aad", "exchange", etc.)
        Iterator<String> fieldNames = rootNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            JsonNode nextNode = rootNode.get(fieldName);

            // Check if the current node has a "result" array
            if (nextNode != null && nextNode.has("result") && nextNode.get("result").isArray()) {
                JsonNode resultArray = nextNode.get("result");

                // Iterate through the elements of the "result" array
                for (JsonNode resultNode : resultArray) {
                    // Check if the current result node has a "PolicyId" field and if it matches
                    if (resultNode.has("PolicyId") && resultNode.get("PolicyId").asText().equals(policyId)) {
                        return resultNode;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Finds matching test results for a control ID.
     */
    private JsonNode findTestResults(JsonNode testResults, String controlId) {
        if (testResults.isArray()) {
            for (JsonNode test : testResults) {
                if (test.has("PolicyId") && test.get("PolicyId").asText().equals(controlId)) {
                    return test;
                }
            }
        }
        logger.warn("No test results found for control ID: {}", controlId);
        return null;
    }

    /**
     * Handles case where test results are missing for a control.
     */
    private void handleMissingTest(Control control, List<ReportFragment> fragments, ReportSummary summary) {
        summary.setErrors(summary.getErrors() + 1);
        fragments.add(ReportFragment.builder()
                .controlId(control.id())
                .requirement(control.value())
                .result("Error - Test results missing")
                .criticality("-")
                .details(String.format("Report issue on <a href=\"%s/issues\" target=\"_blank\">GitHub</a>", SCUBA_GITHUB_URL))
                .build());
        logger.warn("No test results found for Control Id {}", control.id());
    }

    /**
     * Generates HTML for a baseline group.
     */
    private String generateGroupHtml(BaselineGroup group, List<ReportFragment> fragments, JsonNode settings) {
        Map<String, Object> templateData = new HashMap<>();

        // Build group metadata
        String baselineName = fragments.getFirst().getControlId().split("\\.")[1];
        String number = baselineName.toUpperCase() + '-' + group.getGroupNumber();
        String groupAnchor = markdownProcessor.createMarkdownAnchor(group.getGroupNumber(), group.getGroupName());

        String moduleVersion = settings.path("module_version").asText();
        String referenceUrl = String.format("%s/blob/v%s/PowerShell/ScubaGear/baselines/%s.md%s",
                SCUBA_GITHUB_URL,
                moduleVersion,
                baselineName.toLowerCase(),
                groupAnchor);

        // Populate template data
        templateData.put("groupNumber", number);
        templateData.put("groupName", group.getGroupName());
        templateData.put("groupReferenceUrl", referenceUrl);
        templateData.put("controls", fragments);

        // Process template
        String html = templateService.processTemplate("control-group", templateData);

        // Add ID to table tag if missing
        if (!html.contains("class='policy-data' id=")) {
            html = html.replaceFirst("<table(?![^>]+id)*>",
                    String.format("<table class='policy-data' id='%s'>", number));
        }

        return html;
    }

    private TestResult evaluateTestResult(JsonNode test, JsonNode settings, String baselineName) {
        // Check for missing commands
        List<String> missingCommands = findMissingCommands(test, settings, baselineName);

        if (!missingCommands.isEmpty()) {
            return new TestResult("Missing",
                    "This test depends on the following command(s) which did not execute successfully: " +
                            String.join(", ", missingCommands) + ". See terminal output for more details.");
        }

        // Evaluate test result
        boolean requirementMet = test.path("RequirementMet").asBoolean();
        String criticality = test.path("Criticality").asText();
        String reportDetails = test.path("ReportDetails").asText();

        if (requirementMet) {
            return new TestResult("Pass", reportDetails);
        } else if ("Should".equals(criticality)) {
            return new TestResult("Warning", reportDetails);
        } else if (criticality.endsWith("3rd Party") || criticality.endsWith("Not-Implemented")) {
            return new TestResult("N/A", reportDetails);
        } else {
            return new TestResult("Fail", reportDetails);
        }
    }

    private List<String> findMissingCommands(JsonNode test, JsonNode settings, String baselineName) {
        List<String> missing = new ArrayList<>();
        if (test.has("Commandlet") && test.get("Commandlet").isArray()) {
            JsonNode successfulCommands = settings.path(baselineName + "_successful_commands");
            for (JsonNode cmd : test.get("Commandlet")) {
                String cmdStr = cmd.asText();
                if (!commandExists(successfulCommands, cmdStr)) {
                    missing.add(cmdStr);
                }
            }
        }
        return missing;
    }

    private boolean commandExists(JsonNode commands, String command) {
        if (commands.isArray()) {
            for (JsonNode cmd : commands) {
                if (cmd.asText().equals(command)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Updates summary statistics based on test result.
     */
    private void updateSummaryStats(TestResult result, ReportSummary summary) {
        switch (result.status()) {
            case "Pass":
                summary.setPasses(summary.getPasses() + 1);
                break;
            case "Fail":
                summary.setFailures(summary.getFailures() + 1);
                break;
            case "Warning":
                summary.setWarnings(summary.getWarnings() + 1);
                break;
            case "Error":
                summary.setErrors(summary.getErrors() + 1);
                break;
            case "N/A":
                summary.setManual(summary.getManual() + 1);
                break;
            default:
                logger.warn("Unknown test result status: {}", result.status());
        }
    }

    /**
     * Gets mapped product name from SKU ID.
     */
    private String getMappedProductName(String skuId) {
        return productNameMap.getOrDefault(skuId, "Unknown SKU Name");
    }

    /**
     * Handles omitted controls in report generation.
     */
    private void handleOmittedControl(Control control, JsonNode settings, List<ReportFragment> fragments, ReportSummary summary) {
        summary.setOmits(summary.getOmits() + 1);

        String rationale = getOmitRationale(settings, control.id());
        if (rationale.isEmpty()) {
            logger.warn("Config file indicates omitting {}, but no rationale provided.", control.id());
            rationale = "Rationale not provided.";
        }

        fragments.add(ReportFragment.builder()
                .controlId(control.id())
                .requirement(control.value())
                .result("Omitted")
                .criticality("N/A")
                .details("Test omitted by user. \"" + rationale + "\"")
                .build());
    }

    private String getOmitRationale(JsonNode settings, String controlId) {
        return settings.path("scuba_config")
                .path("OmitPolicy")
                .path(controlId)
                .path("Rationale")
                .asText("");
    }

    private Map<String, String> initializeProductNameMap() {
        Map<String, String> map = new HashMap<>();
        try {
            // Load CSV from resources using ClassLoader
            InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream("MicrosoftLicenseToProductNameMappings.csv");

            if (inputStream == null) {
                logger.error("Could not find MicrosoftLicenseToProductNameMappings.csv in resources");
                return map;
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                // Skip header
                reader.readLine();

                // Process remaining lines
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(",");
                    if (parts.length >= 2) {
                        map.put(parts[0], parts[1]); // GUID to Product Display Name
                    }
                }
            }
        } catch (IOException e) {
            logger.warn("Failed to load product name mappings from resources", e);
        }
        return map;
    }
}