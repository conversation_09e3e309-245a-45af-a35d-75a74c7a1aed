package io.syrix.reports.service;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Template;
import io.syrix.common.exceptions.TemplateProcessingException;
import io.syrix.products.microsoft.entra.model.EnterpriseApplication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EnterpriseAppReportGenerator {
	private static final Logger logger = LoggerFactory.getLogger(EnterpriseAppReportGenerator.class);
	private static final String TEMPLATE_PATH = "templates/enterprise-app-report.hbs";
	private final Handlebars handlebars;
	private final Template reportTemplate;

	public EnterpriseAppReportGenerator() {
		this.handlebars = new Handlebars();
		registerHelpers();
		this.reportTemplate = loadTemplate();
	}

	private void registerHelpers() {
		// Date formatting helper
		handlebars.registerHelper("formatDate", (context, options) -> {
			if (context instanceof Instant instant) {
				return DateTimeFormatter
						.ofPattern("yyyy-MM-dd HH:mm:ss z")
						.withZone(ZoneId.systemDefault())
						.format(instant);
			}
			return "";
		});

		// Risk level color class helper
		handlebars.registerHelper("getRiskColorClass", (context, options) -> {
			if (context == null) return "bg-white";
			return switch(context.toString()) {
				case "CRITICAL", "HIGH" -> "bg-red-50";
				case "MEDIUM" -> "bg-yellow-50";
				case "LOW" -> "bg-green-50";
				default -> "bg-white";
			};
		});

		// Risk badge color class helper
		handlebars.registerHelper("getRiskBadgeClass", (context, options) -> {
			if (context == null) return "bg-gray-100 text-gray-800";
			return switch(context.toString()) {
				case "CRITICAL", "HIGH" -> "bg-red-100 text-red-800";
				case "MEDIUM" -> "bg-yellow-100 text-yellow-800";
				case "LOW" -> "bg-green-100 text-green-800";
				default -> "bg-gray-100 text-gray-800";
			};
		});

		// Equality helper
		handlebars.registerHelper("eq", (context, options) -> {
			if (context == null) return false;
			return context.toString().equals(options.param(0));
		});

		handlebars.registerHelper("getRiskLevel", (context, options) -> {
			if (context instanceof Map map) {
				return map.getOrDefault("riskLevel", "UNKNOWN");
			}
			return "UNKNOWN";
		});

// Justification helper
		handlebars.registerHelper("getJustification", (context, options) -> {
			if (context instanceof Map map) {
				return map.getOrDefault("justification", "");
			}
			return "";
		});
	}

	private Template loadTemplate() {
		try {
			String templateContent = readTemplateFromResources();
			return handlebars.compileInline(templateContent);
		} catch (IOException e) {
			String errorMsg = "Failed to load and compile template";
			logger.error(errorMsg, e);
			throw new TemplateProcessingException(errorMsg, e);
		}
	}

	private String readTemplateFromResources() throws IOException {
		try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(TEMPLATE_PATH)) {
			if (inputStream == null) {
				throw new IOException("Template file not found: " + TEMPLATE_PATH);
			}

			try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
				return reader.lines().collect(Collectors.joining("\n"));
			}
		}
	}

	private Map<String, Object> buildContext(List<EnterpriseApplication> applications) {
		Map<String, Object> context = new HashMap<>();

		// Process applications
		context.put("applications", applications.stream().map(app -> {
			Map<String, Object> appMap = new HashMap<>();
			appMap.put("basic", app.getBasic());
			appMap.put("permissions", app.getPermissions());
			appMap.put("delegatedPermissions", app.getDelegatedPermissions());
			appMap.put("assignedUsers", app.getAssignedUsers());
			// Explicitly map riskLevel and justification
			appMap.put("riskLevel", app.getRiskLevel() != null ? app.getRiskLevel() : "UNKNOWN");
			appMap.put("justification", app.getJustification());
			// Add additional metadata
			appMap.put("permissionCount",
					(app.getPermissions() != null ? app.getPermissions().size() : 0) +
							(app.getDelegatedPermissions() != null ? app.getDelegatedPermissions().size() : 0)
			);
			return appMap;
		}).toList());

		// Add report metadata
		context.put("generatedAt", Instant.now());
		context.put("totalApps", applications.size());

		// Add statistics
		addStatistics(context, applications);

		return context;
	}

	private void addStatistics(Map<String, Object> context, List<EnterpriseApplication> applications) {
		// High-risk permissions stats
		long appsWithHighRiskPerms = applications.stream()
				.filter(app -> app.getPermissions() != null && !app.getPermissions().isEmpty())
				.count();
		context.put("appsWithHighRiskPerms", appsWithHighRiskPerms);

		// User consent stats
		long appsWithUserConsent = applications.stream()
				.filter(app -> app.getDelegatedPermissions() != null)
				.filter(app -> app.getDelegatedPermissions().stream()
						.anyMatch(p -> "Principal".equals(p.getConsentType())))
				.count();
		context.put("appsWithUserConsent", appsWithUserConsent);

		// Risk level distribution
		Map<String, Long> riskDistribution = applications.stream()
				.collect(Collectors.groupingBy(
						EnterpriseApplication::getRiskLevel,
						Collectors.counting()
				));
		context.put("riskDistribution", riskDistribution);

		// Apps without justification
		long appsWithoutJustification = applications.stream()
				.filter(app -> app.getJustification() == null || app.getJustification().trim().isEmpty())
				.count();
		context.put("appsWithoutJustification", appsWithoutJustification);
	}

	public String generateReport(List<EnterpriseApplication> applications) {
		try {
			Map<String, Object> context = buildContext(applications);
			return reportTemplate.apply(context);
		} catch (IOException e) {
			String errorMsg = "Failed to generate enterprise applications report";
			logger.error(errorMsg, e);
			throw new TemplateProcessingException(errorMsg, e);
		}
	}

	public void saveReport(List<EnterpriseApplication> applications, String outputPath) {
		try {
			String report = generateReport(applications);
			Files.writeString(
					Path.of(outputPath),
					report,
					StandardCharsets.UTF_8
			);
			logger.info("Enterprise applications report saved to {}", outputPath);
		} catch (IOException e) {
			String errorMsg = "Failed to save enterprise applications report";
			logger.error(errorMsg, e);
			throw new TemplateProcessingException(errorMsg, e);
		}
	}
}