package io.syrix.reports.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.utils.baseline.BaselineGroup;

import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

public class ReportJson {
	private final ReportMetadata metadata;
	private final List<BaselineGroupResult> results;
	private ReportSummary summary;

	public ReportMetadata getMetadata() {
		return metadata;
	}

	public List<BaselineGroupResult> getResults() {
		return results;
	}

	public ReportSummary getSummary() {
		return summary;
	}

	public void setSummary(ReportSummary summary) {
		this.summary = summary;
	}

	public ReportJson(ReportSummary summary) {
		this.metadata = new ReportMetadata();
		this.results = new ArrayList<>();
		this.summary = summary;
	}

	public void addGroupResults(BaselineGroup group, List<ReportFragment> fragments) {
		BaselineGroupResult groupResult = new BaselineGroupResult();
		groupResult.setGroupName(group.getGroupName());
		groupResult.setGroupNumber(group.getGroupNumber());
		groupResult.setGroupReferenceUrl(buildGroupReferenceUrl(group));
		groupResult.setControls(new ArrayList<>(fragments));
		results.add(groupResult);
	}

	public void updateSummary(ReportSummary newSummary) {
		this.summary = newSummary;
	}

	public void setMetadataFromSettings(JsonNode settings) {
		metadata.setTenantDisplayName(settings.path("tenant_details").path("DisplayName").asText());
		metadata.setReportDate(parseReportDate(settings.path("date").asText()));
		metadata.setBaselineVersion(settings.path("baseline_version").asText());
		metadata.setModuleVersion(settings.path("module_version").asText());
	}

	private String buildGroupReferenceUrl(BaselineGroup group) {
		return String.format("https://github.com/cisagov/ScubaGear/blob/main/baselines/%s.md#%s-%s",
				group.getBaselineName().toLowerCase(),
				group.getGroupNumber(),
				group.getGroupName().toLowerCase().replace(' ', '-'));
	}

	private Instant parseReportDate(String dateStr) {
		try {
			return Instant.parse(dateStr);
		} catch (DateTimeParseException e) {
			return Instant.now();
		}
	}


	public static class ReportMetadata {
		private String tenantDisplayName;
		private Instant reportDate;
		private String baselineVersion;
		private String moduleVersion;

		public String getTenantDisplayName() {
			return tenantDisplayName;
		}

		public void setTenantDisplayName(String tenantDisplayName) {
			this.tenantDisplayName = tenantDisplayName;
		}

		public Instant getReportDate() {
			return reportDate;
		}

		public void setReportDate(Instant reportDate) {
			this.reportDate = reportDate;
		}

		public String getBaselineVersion() {
			return baselineVersion;
		}

		public void setBaselineVersion(String baselineVersion) {
			this.baselineVersion = baselineVersion;
		}

		public String getModuleVersion() {
			return moduleVersion;
		}

		public void setModuleVersion(String moduleVersion) {
			this.moduleVersion = moduleVersion;
		}
	}


	public static class BaselineGroupResult {
		private String groupName;
		private String groupNumber;
		private String groupReferenceUrl;
		private List<ReportFragment> controls;

		public void addControl(ReportFragment control) {
			if (controls == null) {
				controls = new ArrayList<>();
			}
			controls.add(control);
		}

		public String getGroupName() {
			return groupName;
		}

		public void setGroupName(String groupName) {
			this.groupName = groupName;
		}

		public String getGroupNumber() {
			return groupNumber;
		}

		public void setGroupNumber(String groupNumber) {
			this.groupNumber = groupNumber;
		}

		public String getGroupReferenceUrl() {
			return groupReferenceUrl;
		}

		public void setGroupReferenceUrl(String groupReferenceUrl) {
			this.groupReferenceUrl = groupReferenceUrl;
		}

		public List<ReportFragment> getControls() {
			return controls;
		}

		public void setControls(List<ReportFragment> controls) {
			this.controls = controls;
		}
	}

	public static class ReportJsonBuilder {
		private ReportSummary summary;
		private final List<BaselineGroupResult> results = new ArrayList<>();
		private final ReportMetadata metadata = new ReportMetadata();

		public ReportJsonBuilder withSummary(ReportSummary summary) {
			this.summary = summary;
			return this;
		}

		public ReportJsonBuilder withMetadata(JsonNode settings) {
			metadata.setTenantDisplayName(settings.path("tenant_details").path("DisplayName").asText());
			metadata.setReportDate(parseReportDate(settings.path("date").asText()));
			metadata.setBaselineVersion(settings.path("baseline_version").asText());
			metadata.setModuleVersion(settings.path("module_version").asText());
			return this;
		}

		public ReportJsonBuilder addGroupResult(BaselineGroup group, List<ReportFragment> fragments) {
			BaselineGroupResult groupResult = new BaselineGroupResult();
			groupResult.setGroupName(group.getGroupName());
			groupResult.setGroupNumber(group.getGroupNumber());
			groupResult.setGroupReferenceUrl(buildGroupReferenceUrl(group));
			groupResult.setControls(new ArrayList<>(fragments));
			results.add(groupResult);
			return this;
		}

		private String buildGroupReferenceUrl(BaselineGroup group) {
			return String.format("https://github.com/cisagov/ScubaGear/blob/main/baselines/%s.md#%s-%s",
					group.getBaselineName().toLowerCase(),
					group.getGroupNumber(),
					group.getGroupName().toLowerCase().replace(' ', '-'));
		}

		private Instant parseReportDate(String dateStr) {
			try {
				return Instant.parse(dateStr);
			} catch (DateTimeParseException e) {
				return Instant.now();
			}
		}

		public ReportJson build() {
			if (summary == null) {
				throw new IllegalStateException("Report summary is required");
			}
			ReportJson report = new ReportJson(summary);
			report.results.addAll(results);
			report.metadata.setTenantDisplayName(metadata.getTenantDisplayName());
			report.metadata.setReportDate(metadata.getReportDate());
			report.metadata.setBaselineVersion(metadata.getBaselineVersion());
			report.metadata.setModuleVersion(metadata.getModuleVersion());
			return report;
		}
	}

	public static ReportJsonBuilder builder() {
		return new ReportJsonBuilder();
	}
}