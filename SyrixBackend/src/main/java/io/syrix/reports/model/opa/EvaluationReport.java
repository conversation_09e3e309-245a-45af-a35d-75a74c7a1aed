package io.syrix.reports.model.opa;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.datamodel.report.ConfigurationServiceType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Root class for deserializing the OPA (Open Policy Agent) report.
 * Contains the results of the security policy evaluation for various Microsoft 365 products.
 */
public class EvaluationReport {

	private Map<ConfigurationServiceType, List<PolicyResult>> serviceResults = new HashMap<>();

	@JsonAnySetter
	public void setServiceResult(String key, JsonNode value) {
		ConfigurationServiceType serviceType = ConfigurationServiceType.fromResultName(key);
		ObjectMapper mapper = new ObjectMapper();

		JsonNode resultNode = value.get("result");
		if (resultNode != null && resultNode.isArray()) {
			List<PolicyResult> results = mapper.convertValue(
					resultNode,
					mapper.getTypeFactory().constructCollectionType(List.class, PolicyResult.class)
			);
			serviceResults.put(serviceType, results);
		}
	}

	public Map<ConfigurationServiceType, List<PolicyResult>> getServiceResults() {
		return serviceResults;
	}

}
