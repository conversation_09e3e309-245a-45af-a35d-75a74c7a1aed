package io.syrix.reports.model;

import io.syrix.common.utils.ValidationUtils;
import io.syrix.utils.baseline.BaselineGroup;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;

public record ReportConfig(
        // Main configuration fields
        String baselineName,
        String fullName,
        String outProviderFileName,
        Path configPath,
        String outRegoFileName,
        String outReportName,
        String outPath,
        String outFolderName,
        String reportsFolderName,

        // Output control settings
        boolean darkMode,
        boolean quiet,

        // File output settings
        boolean keepIndividualJSON,
        String outJsonFileName,
        String outCsvFileName,
        String outActionPlanFileName,

        // Baseline configurations
        Map<String, List<BaselineGroup>> secureBaselines,

        // Optional UUID settings
        int numberOfUUIDCharactersToTruncate

) {

    public static ReportConfigBuilder builder() {
        return new ReportConfigBuilder();
    }

    // Custom builder that includes validation
    public static class ReportConfigBuilder {
        private String baselineName;
        private String fullName;
        private String outProviderFileName;
        private Path configPath;
        private String outRegoFileName;
        private String outReportName;
        private String outPath;
        private String outFolderName;
        private String reportsFolderName;

        // Output control settings
        private boolean darkMode;
        private boolean quiet;

        // File output settings
        private boolean keepIndividualJSON;
        private String outJsonFileName;
        private String outCsvFileName;
        private String outActionPlanFileName;

        // Baseline configurations
        private Map<String, List<BaselineGroup>> secureBaselines;

        // Optional UUID settings
        private int numberOfUUIDCharactersToTruncate;

        public ReportConfigBuilder baselineName(String baselineName) {
            this.baselineName = baselineName;
            return this;
        }

        public ReportConfigBuilder fullName(String fullName) {
            this.fullName = fullName;
            return this;
        }

        public ReportConfigBuilder outProviderFileName(String outProviderFileName) {
            this.outProviderFileName = outProviderFileName;
            return this;
        }

        public ReportConfigBuilder configPath(Path configPath) {
            this.configPath = configPath;
            return this;
        }

        public ReportConfigBuilder outRegoFileName(String outRegoFileName) {
            this.outRegoFileName = outRegoFileName;
            return this;
        }

        public ReportConfigBuilder outReportName(String outReportName) {
            this.outReportName = outReportName;
            return this;
        }

        public ReportConfigBuilder outPath(String outPath) {
            this.outPath = outPath;
            return this;
        }

        public ReportConfigBuilder outFolderName(String outFolderName) {
            this.outFolderName = outFolderName;
            return this;
        }

        public ReportConfigBuilder reportsFolderName(String reportsFolderName) {
            this.reportsFolderName = reportsFolderName;
            return this;
        }

        public ReportConfigBuilder darkMode(boolean darkMode) {
            this.darkMode = darkMode;
            return this;
        }

        public ReportConfigBuilder quiet(boolean quiet) {
            this.quiet = quiet;
            return this;
        }

        public ReportConfigBuilder keepIndividualJSON(boolean keepIndividualJSON) {
            this.keepIndividualJSON = keepIndividualJSON;
            return this;
        }

        public ReportConfigBuilder outJsonFileName(String outJsonFileName) {
            this.outJsonFileName = outJsonFileName;
            return this;
        }

        public ReportConfigBuilder outCsvFileName(String outCsvFileName) {
            this.outCsvFileName = outCsvFileName;
            return this;
        }

        public ReportConfigBuilder outActionPlanFileName(String outActionPlanFileName) {
            this.outActionPlanFileName = outActionPlanFileName;
            return this;
        }

        public ReportConfigBuilder secureBaselines(Map<String, List<BaselineGroup>> secureBaselines) {
            this.secureBaselines = secureBaselines;
            return this;
        }

        public ReportConfigBuilder numberOfUUIDCharactersToTruncate(int numberOfUUIDCharactersToTruncate) {
            this.numberOfUUIDCharactersToTruncate = numberOfUUIDCharactersToTruncate;
            return this;
        }

        // Validate required fields
        private void validate() {
            ValidationUtils.Strings.requireNonBlank(baselineName, "Baseline name is required");
            ValidationUtils.Strings.requireNonBlank(fullName, "Full name is required");
            ValidationUtils.Strings.requireNonBlank(outProviderFileName, "Output provider file name is required");
            ValidationUtils.Strings.requireNonBlank(outRegoFileName, "Output rego file name is required");
            ValidationUtils.Maps.requireNonEmpty(secureBaselines, "Secure baselines configuration is required");

            if (outCsvFileName != null && outCsvFileName.equals(outActionPlanFileName)) {
                throw new IllegalArgumentException("OutCsvFileName and OutActionPlanFileName cannot be equal");
            }
        }

        public ReportConfig build() {
            validate();
            return new ReportConfig(
                    this.baselineName,
                    this.fullName,
                    this.outProviderFileName,
                    this.configPath,
                    this.outRegoFileName,
                    this.outReportName,
                    this.outPath,
                    this.outFolderName,
                    this.reportsFolderName,
                    this.darkMode,
                    this.quiet,
                    this.keepIndividualJSON,
                    this.outJsonFileName,
                    this.outCsvFileName,
                    this.outActionPlanFileName,
                    this.secureBaselines,
                    this.numberOfUUIDCharactersToTruncate
            );
        }

        // Factory method for creating builder with default values
        public static ReportConfig buildByDefault() {
            return ReportConfig.builder()
                    .darkMode(false)
                    .quiet(false)
                    .keepIndividualJSON(false)
                    .numberOfUUIDCharactersToTruncate(18)
                    .outPath(".")
                    .outFolderName("M365BaselineConformance")
                    .outProviderFileName("ProviderSettingsExport")
                    .outRegoFileName("TestResults")
                    .outReportName("BaselineReports")
                    .outJsonFileName("ScubaResults")
                    .outCsvFileName("ScubaResults")
                    .outActionPlanFileName("ActionPlan")
                    .build();
        }


    }

//    // Copy constructor for creating modified instances
//    public ReportConfig copy() {
//        return ReportConfig.builder()
//                .baselineName(this.baselineName)
//                .fullName(this.fullName)
//                .outProviderFileName(this.outProviderFileName)
//                .outRegoFileName(this.outRegoFileName)
//                .outReportName(this.outReportName)
//                .outPath(this.outPath)
//                .outFolderName(this.outFolderName)
//                .darkMode(this.darkMode)
//                .quiet(this.quiet)
//                .keepIndividualJSON(this.keepIndividualJSON)
//                .outJsonFileName(this.outJsonFileName)
//                .outCsvFileName(this.outCsvFileName)
//                .outActionPlanFileName(this.outActionPlanFileName)
//                .secureBaselines(this.secureBaselines)
//                .numberOfUUIDCharactersToTruncate(this.numberOfUUIDCharactersToTruncate)
//                .reportsFolderName(this.reportsFolderName)
//                .build();
//    }
}