package io.syrix.reports.model;

import com.fasterxml.jackson.databind.JsonNode;

import java.time.Instant;

public class ReportSummary {
    private int warnings;
    private int failures;
    private int passes;
    private int omits;
    private int manual;
    private int errors;
    private Instant date;
    private String reportJSONPath;
    private String reportHTMLPath;
    private JsonNode reportData;

    public String getReportJSONPath() {
        return reportJSONPath;
    }
    public void setReportJSONPath(String reportJSONPath) {
        this.reportJSONPath = reportJSONPath;
    }
    public String getReportHTMLPath() {
        return reportHTMLPath;
    }
    public void setReportHTMLPath(String reportHTMLPath) {
        this.reportHTMLPath = reportHTMLPath;
    }
    public int getWarnings() {
        return warnings;
    }

    public void setWarnings(int warnings) {
        this.warnings = warnings;
    }

    public int getFailures() {
        return failures;
    }

    public void setFailures(int failures) {
        this.failures = failures;
    }

    public int getPasses() {
        return passes;
    }

    public void setPasses(int passes) {
        this.passes = passes;
    }

    public int getOmits() {
        return omits;
    }

    public void setOmits(int omits) {
        this.omits = omits;
    }

    public int getManual() {
        return manual;
    }

    public void setManual(int manual) {
        this.manual = manual;
    }

    public int getErrors() {
        return errors;
    }

    public void setErrors(int errors) {
        this.errors = errors;
    }

    public Instant getDate() {
        return date;
    }

    public void setDate(Instant date) {
        this.date = date;
    }

    public JsonNode getReportData() {
        return reportData;
    }
    public void setReportData(JsonNode reportData) {
        this.reportData = reportData;
    }

    public static ReportSummaryBuilder builder() {
        return new ReportSummaryBuilder();
    }

    public static class ReportSummaryBuilder {
        private int warnings;
        private int failures;
        private int passes;
        private int omits;
        private int manual;
        private int errors;
        private Instant date;
        private String jsonReportFilePath;
        private String htmlReportFilePath;
        private JsonNode reportJsonData;

        public ReportSummaryBuilder warnings(int warnings) {
            this.warnings = warnings;
            return this;
        }

        public ReportSummaryBuilder failures(int failures) {
            this.failures = failures;
            return this;
        }

        public ReportSummaryBuilder passes(int passes) {
            this.passes = passes;
            return this;
        }

        public ReportSummaryBuilder omits(int omits) {
            this.omits = omits;
            return this;
        }

        public ReportSummaryBuilder manual(int manual) {
            this.manual = manual;
            return this;
        }

        public ReportSummaryBuilder errors(int errors) {
            this.errors = errors;
            return this;
        }

        public ReportSummaryBuilder date(Instant date) {
            this.date = date;
            return this;
        }

        public ReportSummaryBuilder jsonReportData(JsonNode jsonReportData) {
            this.reportJsonData = jsonReportData;
            return this;
        }

        public ReportSummaryBuilder htmlReportFilePath(String htmlReportFilePath) {
            this.htmlReportFilePath = htmlReportFilePath;
            return this;
        }

        public ReportSummaryBuilder jsonReportFilePath(String jsonReportFilePath) {
            this.jsonReportFilePath = jsonReportFilePath;
            return this;
        }

        public ReportSummary build() {
            ReportSummary summary = new ReportSummary();
            summary.setWarnings(this.warnings);
            summary.setFailures(this.failures);
            summary.setPasses(this.passes);
            summary.setOmits(this.omits);
            summary.setManual(this.manual);
            summary.setErrors(this.errors);
            summary.setDate(this.date);
            summary.setReportJSONPath(this.jsonReportFilePath);
            summary.setReportHTMLPath(this.htmlReportFilePath);
            summary.setReportData(this.reportJsonData);
            return summary;
        }
    }
}
