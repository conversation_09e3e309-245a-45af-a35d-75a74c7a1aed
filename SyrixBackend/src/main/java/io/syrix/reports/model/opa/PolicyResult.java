package io.syrix.reports.model.opa;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * Result of checking a single security policy.
 * Contains detailed information about compliance with policy requirements.
 */
public class PolicyResult {
    
    @JsonProperty("PolicyId")
    private String policyId;
    
    @JsonProperty("RequirementMet")
    private boolean requirementMet;
    
    @JsonProperty("Criticality")
    private String criticality;
    
    @JsonProperty("ReportDetails")
    private String reportDetails;
    
    @JsonProperty("ActualValue")
    private Object actualValue;
    
    @JsonProperty("Commandlet")
    private List<String> commandlets;
    
    public String getPolicyId() {
        return policyId;
    }
    
    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }
    
    public boolean isRequirementMet() {
        return requirementMet;
    }
    
    public void setRequirementMet(boolean requirementMet) {
        this.requirementMet = requirementMet;
    }
    
    public String getCriticality() {
        return criticality;
    }
    
    public void setCriticality(String criticality) {
        this.criticality = criticality;
    }
    
    public String getReportDetails() {
        return reportDetails;
    }
    
    public void setReportDetails(String reportDetails) {
        this.reportDetails = reportDetails;
    }
    
    public Object getActualValue() {
        return actualValue;
    }
    
    public void setActualValue(Object actualValue) {
        this.actualValue = actualValue;
    }
    
    public List<String> getCommandlets() {
        return commandlets;
    }
    
    public void setCommandlets(List<String> commandlets) {
        this.commandlets = commandlets;
    }
}
