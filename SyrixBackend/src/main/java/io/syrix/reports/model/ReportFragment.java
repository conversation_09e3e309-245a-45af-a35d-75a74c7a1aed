package io.syrix.reports.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

public class ReportFragment {
	@JsonProperty("Control ID")
	private final String controlId;
	private final String requirement;
	private final String result;
	private final String criticality;
	private final String details;

	private ReportFragment(Builder builder) {
		this.controlId = builder.controlId;
		this.requirement = builder.requirement;
		this.result = builder.result;
		this.criticality = builder.criticality;
		this.details = builder.details;
	}

	public String getControlId() {
		return controlId;
	}

	public String getRequirement() {
		return requirement;
	}

	public String getResult() {
		return result;
	}

	public String getCriticality() {
		return criticality;
	}

	public String getDetails() {
		return details;
	}

	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {
		private String controlId;
		private String requirement;
		private String result;
		private String criticality;
		private String details;

		public Builder controlId(String controlId) {
			this.controlId = controlId;
			return this;
		}

		public Builder requirement(String requirement) {
			this.requirement = requirement;
			return this;
		}

		public Builder result(String result) {
			this.result = result;
			return this;
		}

		public Builder criticality(String criticality) {
			this.criticality = criticality;
			return this;
		}

		public Builder details(String details) {
			this.details = details;
			return this;
		}

		public ReportFragment build() {
			validateFields();
			return new ReportFragment(this);
		}

		private void validateFields() {
			List<String> missingFields = new ArrayList<>();
			if (controlId == null) missingFields.add("controlId");
			if (requirement == null) missingFields.add("requirement");
			if (result == null) missingFields.add("result");
			if (criticality == null) missingFields.add("criticality");
			if (details == null) missingFields.add("details");

			if (!missingFields.isEmpty()) {
				throw new IllegalStateException("Required fields missing: " +
						String.join(", ", missingFields));
			}
		}
	}
}