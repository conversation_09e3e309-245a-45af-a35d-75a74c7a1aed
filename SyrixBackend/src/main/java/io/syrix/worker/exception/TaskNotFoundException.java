package io.syrix.worker.exception;


/**
 * Exception thrown when a task is not found in the database.
 * Used to indicate that a task lookup operation failed.
 */
public class TaskNotFoundException extends SyrixWorkerException {
    
    /**
     * Creates a new TaskNotFoundException with the specified message.
     * 
     * @param message the detail message
     */
    public TaskNotFoundException(String message) {
        super(message);
    }
}
