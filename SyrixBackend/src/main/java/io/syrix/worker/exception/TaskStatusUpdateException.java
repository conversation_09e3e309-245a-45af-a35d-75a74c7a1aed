package io.syrix.worker.exception;

/**
 * Exception thrown when task status update operation fails.
 * Used to indicate failures in task lifecycle management.
 */
public class TaskStatusUpdateException extends SyrixWorkerException {

    /**
     * Creates a new TaskStatusUpdateException with the specified message and cause.
     * 
     * @param message the detail message
     * @param cause the cause of the exception
     */
    public TaskStatusUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}
