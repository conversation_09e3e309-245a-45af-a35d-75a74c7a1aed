package io.syrix.worker.exception;

import io.syrix.datamodel.task.TaskStatus;

/**
 * Exception thrown when attempting an invalid task status transition.
 * Used to indicate violations of task lifecycle rules.
 */
public class InvalidTaskTransitionException extends SyrixWorkerException {
    private final TaskStatus currentStatus;
    private final TaskStatus targetStatus;
    
    /**
     * Creates a new InvalidTaskTransitionException with the specified transition details.
     * 
     * @param currentStatus the current task status
     * @param targetStatus the attempted target status
     */
    public InvalidTaskTransitionException(TaskStatus currentStatus, TaskStatus targetStatus) {
        super(String.format("Invalid task status transition: %s -> %s", currentStatus, targetStatus));
        this.currentStatus = currentStatus;
        this.targetStatus = targetStatus;
    }
    
    /**
     * Gets the current task status.
     * 
     * @return the current status
     */
    public TaskStatus getCurrentStatus() {
        return currentStatus;
    }
    
    /**
     * Gets the attempted target status.
     * 
     * @return the target status
     */
    public TaskStatus getTargetStatus() {
        return targetStatus;
    }
}
