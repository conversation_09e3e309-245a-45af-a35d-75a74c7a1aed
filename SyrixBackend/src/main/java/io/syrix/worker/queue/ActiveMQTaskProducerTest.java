package io.syrix.worker.queue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.dao.CustomerDao;
import io.syrix.datamodel.Customer;
import io.syrix.datamodel.task.TaskType;
import io.syrix.messaging.TaskMessage;
import io.syrix.dao.impl.mongodb.MongoDaoFactory;
import io.syrix.dao.mongodb.SimpleMongoDatabaseProvider;
import io.syrix.dao.TaskDao;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.datamodel.task.TaskStatus;
import io.syrix.datamodel.report.ConfigurationServiceType;

import java.util.*;

import org.apache.activemq.artemis.api.core.TransportConfiguration;
import org.apache.activemq.artemis.api.core.client.*;
import org.apache.activemq.artemis.api.core.QueueConfiguration;
import org.apache.activemq.artemis.api.core.RoutingType;
import org.apache.activemq.artemis.core.remoting.impl.netty.NettyConnectorFactory;
import org.apache.activemq.artemis.core.remoting.impl.netty.TransportConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple producer to send messages to ActiveMQ Artemis queue.
 * Can be run as a main class to test message queue functionality.
 */
public class ActiveMQTaskProducerTest {
    private static final Logger logger = LoggerFactory.getLogger(ActiveMQTaskProducerTest.class);
    
    private static final String BROKER_HOST = "localhost";
    private static final int BROKER_PORT = 61616;
    private static final String BROKER_USERNAME = "admin";
    private static final String BROKER_PASSWORD = "admin123";
    private static final String QUEUE_NAME = "syrix-tasks";
    
    private final ObjectMapper objectMapper;
    
    public ActiveMQTaskProducerTest() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    
    public static void main(String[] args) {
        System.out.println("=== Starting ActiveMQ Task Producer Test ===");
        
        ActiveMQTaskProducerTest producer = new ActiveMQTaskProducerTest();
        try {
            producer.runTests();
            logger.info("=== All tests completed successfully ===");
        } catch (Exception e) {
            logger.error("=== Error running producer tests: {} ===", e.getMessage());
        }
    }

    public void runTests() throws Exception {
        System.out.println("[INFO] Starting runTests() method");
        
        ServerLocator serverLocator = null;
        ClientSessionFactory sessionFactory = null;
        ClientSession session = null;
        ClientProducer producer = null;
        
        try {
            System.out.println("[INFO] Setting up ActiveMQ Artemis producer for testing");
            logger.info("Setting up ActiveMQ Artemis producer for testing");
            
            // Create transport configuration
            Map<String, Object> connectionParams = new HashMap<>();
            connectionParams.put(TransportConstants.HOST_PROP_NAME, BROKER_HOST);
            connectionParams.put(TransportConstants.PORT_PROP_NAME, BROKER_PORT);
            
            System.out.println("[INFO] Creating connection to " + BROKER_HOST + ":" + BROKER_PORT);
            
            TransportConfiguration transportConfiguration = new TransportConfiguration(
                NettyConnectorFactory.class.getName(), 
                connectionParams
            );
            
            // Create server locator and session factory
            System.out.println("[INFO] Creating server locator");
            serverLocator = ActiveMQClient.createServerLocatorWithoutHA(transportConfiguration);
            
            System.out.println("[INFO] Creating session factory");
            sessionFactory = serverLocator.createSessionFactory();
            
            System.out.println("[INFO] Creating session with credentials: " + BROKER_USERNAME);
            session = sessionFactory.createSession(BROKER_USERNAME, BROKER_PASSWORD, false, true, true, false, 0);
            
            // Create queue if it doesn't exist
            try {
                System.out.println("[INFO] Creating/checking queue: " + QUEUE_NAME);
                QueueConfiguration queueConfig = QueueConfiguration.of(QUEUE_NAME)
                    .setAddress(QUEUE_NAME)
                    .setRoutingType(RoutingType.ANYCAST)
                    .setDurable(true);
                session.createQueue(queueConfig);
                System.out.println("[DEBUG] Queue '" + QUEUE_NAME + "' created or already exists");
                logger.debug("Queue '{}' created or already exists", QUEUE_NAME);
            } catch (Exception e) {
                System.out.println("[DEBUG] Queue might already exist: " + e.getMessage());
                logger.debug("Queue might already exist: {}", e.getMessage());
            }
            
            // Create producer
            System.out.println("[INFO] Creating producer");
            producer = session.createProducer(QUEUE_NAME);
            
            System.out.println("[INFO] ActiveMQ Artemis producer setup completed");
            logger.info("ActiveMQ Artemis producer setup completed");
            
            // Run tests
            System.out.println("[INFO] Running tests...");
            testSendSingleTaskMessage(session, producer);
//            testSendMultipleTaskMessages(session, producer);
//            testSendVulnerabilityAnalysisTask(session, producer);
//            testSendConfigurationRemediationTask(session, producer);
//            testSendRetrieveAndRemediateTask(session, producer);
            
            System.out.println("[INFO] All tests completed successfully");
            logger.info("All tests completed successfully");
            
        } catch (Exception e) {
            logger.error("[ERROR] Exception in runTests: ", e);
            throw e;
        } finally {
            // Cleanup resources
            logger.info("Cleaning up ActiveMQ Artemis producer resources");
            
            if (producer != null) {
                try {
                    producer.close();
                    logger.debug("Producer closed");
                } catch (Exception e) {
                    logger.warn("Error closing producer", e);
                }
            }
            if (session != null) {
                try {
                    session.close();
                    logger.debug("Session closed");
                } catch (Exception e) {
                    logger.warn("Error closing session", e);
                }
            }
            if (sessionFactory != null) {
                try {
                    sessionFactory.close();
                    logger.debug("Session factory closed");
                } catch (Exception e) {
                    logger.warn("Error closing session factory", e);
                }
            }
            if (serverLocator != null) {
                try {
                    serverLocator.close();
                    logger.debug("Server locator closed");
                } catch (Exception e) {
                    logger.warn("Error closing server locator", e);
                }
            }
            logger.info("ActiveMQ Artemis producer cleanup completed");
        }
    }

    /**
     * Test sending a single task message to the queue
     * Creates a RetrieveTask in database first, then uses it for TaskMessage
     */
    public void testSendSingleTaskMessage(ClientSession session, ClientProducer producer) throws Exception {
        logger.info("Starting testSendSingleTaskMessage");
        
        // Create database connection and DAOs with correct MongoDB credentials
        SimpleMongoDatabaseProvider dbProvider = new SimpleMongoDatabaseProvider(
            "******************************************************************", 
            "Syrix"
        );
        MongoDaoFactory daoFactory = new MongoDaoFactory(dbProvider);
        CustomerDao customerDao = daoFactory.getCustomerDao();
        TaskDao taskDao = daoFactory.getTaskDao();
        
        try {
            // Find client "Syrix Development"
            Optional<Customer> clientOpt = customerDao.findByName("Syrix Development");
            if (clientOpt.isEmpty()) {
                throw new RuntimeException("client 'Syrix Development' not found in database");
            }
            
            Customer customer = clientOpt.get();
            logger.info("Found client: {} with ID: {}", customer.getName(), customer.getId());
            
            // Create RetrieveTask with full serviceTypeList
            RetrieveTask retrieveTask = new RetrieveTask();
            retrieveTask.setCustomerId(customer.getId());
            retrieveTask.setStatus(TaskStatus.CREATED);
            
            // Set all available service types
            List<ConfigurationServiceType> serviceTypes = Arrays.asList(
                ConfigurationServiceType.ENTRA,
                ConfigurationServiceType.TEAMS,
                ConfigurationServiceType.SHAREPOINT,
                ConfigurationServiceType.DEFENDER,
                ConfigurationServiceType.POWER_PLATFORM,
                ConfigurationServiceType.EXCHANGE_ONLINE
            );
            retrieveTask.setServiceTypeList(serviceTypes);
            
            // Save RetrieveTask to database
            RetrieveTask savedTask = taskDao.saveTaskSubclass(retrieveTask);
            logger.info("Saved RetrieveTask with ID: {}", savedTask.getId());
            
            // Create TaskMessage using saved task data
            TaskMessage taskMessage = createTaskMessage(savedTask.getId(), savedTask.getCustomerId(), TaskType.RETRIEVE_CONFIGURATION);
            sendTaskMessage(session, producer, taskMessage);

            logger.info("Successfully sent single task message: {}", taskMessage.getTaskId());
            
        } catch (Exception e) {
            logger.error("[ERROR] Failed to execute testSendSingleTaskMessage: {}", e.getMessage(), e);
            throw e;
        } finally {
            // Clean up database connection
            try {
                daoFactory.close();
            } catch (Exception e) {
                logger.error("[ERROR] Failed to close database connection: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Create a task message with specified parameters
     */
    private TaskMessage createTaskMessage(UUID taskId, UUID clientId, TaskType taskType) {
        logger.debug("Creating TaskMessage: {}, {}, {}", taskId, taskType, clientId);
        TaskMessage taskMessage = new TaskMessage();
        taskMessage.setTaskId(taskId);
        taskMessage.setTaskType(taskType);
        taskMessage.setCustomerId(clientId);
        
        return taskMessage;
    }

    /**
     * Send task message to the queue
     */
    private void sendTaskMessage(ClientSession session, ClientProducer producer, TaskMessage taskMessage) throws Exception {
        try {
            logger.info("Sending task message: {}", taskMessage.getTaskId());
            String messageJson = objectMapper.writeValueAsString(taskMessage);
            logger.debug("Message JSON: {}", messageJson);


            logger.debug("Creating TextMessage");
            ClientMessage message = session.createMessage(true);
            message.getBodyBuffer().writeString(messageJson);

            logger.debug("Sending message to producer");
            producer.send(message);

            logger.info("Sent message to queue: TaskId={}, Type={}, client={}",
                taskMessage.getTaskId(), taskMessage.getTaskType(), taskMessage.getCustomerId());
        } catch (Exception e) {
            logger.error("[ERROR] Failed to send message: {}", e.getMessage(), e);
            throw e;
        }
    }
}
