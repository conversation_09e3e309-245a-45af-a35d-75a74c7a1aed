package io.syrix.worker.queue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.messaging.TaskMessage;
import io.syrix.worker.orchestrator.TaskOrchestrator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.runtime.Startup;
import org.apache.activemq.artemis.api.core.ActiveMQException;
import org.apache.activemq.artemis.api.core.ActiveMQExceptionType;
import org.apache.activemq.artemis.api.core.TransportConfiguration;
import org.apache.activemq.artemis.api.core.client.*;
import org.apache.activemq.artemis.api.core.QueueConfiguration;
import org.apache.activemq.artemis.api.core.RoutingType;
import org.apache.activemq.artemis.core.remoting.impl.netty.NettyConnectorFactory;
import org.apache.activemq.artemis.core.remoting.impl.netty.TransportConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * ActiveMQ Artemis message consumer for processing Syrix security tasks.
 * Connects directly to localhost ActiveMQ Artemis broker.
 */
@ApplicationScoped
@Startup
public class ActiveMQTaskConsumer {
    private static final Logger logger = LoggerFactory.getLogger(ActiveMQTaskConsumer.class);
    
    private static final String BROKER_HOST = "localhost";
    private static final int BROKER_PORT = 61616;
    private static final String BROKER_USERNAME = "admin"; //TODO Artur need add spesial user
    private static final String BROKER_PASSWORD = "admin123"; 
    private static final String QUEUE_NAME = "syrix-tasks";
    private static final int MAX_CONCURRENT_TASKS = 5;
    private static final long RECEIVE_TIMEOUT = 5000L; // 5 seconds
    
    @Inject
    TaskOrchestrator taskOrchestrator;
    
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService = Executors.newFixedThreadPool(MAX_CONCURRENT_TASKS);
    
    public ActiveMQTaskConsumer() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    
    private ServerLocator serverLocator;
    private ClientSessionFactory sessionFactory;
    private ClientSession session;
    private ClientConsumer consumer;
    private volatile boolean running = false;

    /**
     * Initialize ActiveMQ Artemis connection and start consuming messages
     * This runs after CommandLineArgsProcessor due to priority ordering
     */
    void onStartup(@Observes @jakarta.annotation.Priority(200) StartupEvent event) {
        initialize();
    }
    
    /**
     * Initialize ActiveMQ Artemis connection and start consuming messages
     */
    public void initialize() {
        // Check system property set by command line processor
        boolean activeMQEnabled = Boolean.parseBoolean(System.getProperty("syrix.messaging.activemq.enabled", "true"));
        
        if (!activeMQEnabled) {
            logger.info("ActiveMQ integration is disabled via command line flag");
            return;
        }
        
        try {
            logger.info("Initializing ActiveMQ Artemis consumer for queue: {}", QUEUE_NAME);
            
            // Create transport configuration for Netty connector
            Map<String, Object> connectionParams = new HashMap<>();
            connectionParams.put(TransportConstants.HOST_PROP_NAME, BROKER_HOST);
            connectionParams.put(TransportConstants.PORT_PROP_NAME, BROKER_PORT);
            
            TransportConfiguration transportConfiguration = new TransportConfiguration(
                NettyConnectorFactory.class.getName(), 
                connectionParams
            );
            
            // Create server locator with transport configuration
            serverLocator = ActiveMQClient.createServerLocatorWithoutHA(transportConfiguration);
            serverLocator.setReconnectAttempts(-1); // Infinite reconnect attempts
            serverLocator.setRetryInterval(1000L); // 1 second retry interval
            
            // Create session factory and session with authentication
            sessionFactory = serverLocator.createSessionFactory();
            session = sessionFactory.createSession(BROKER_USERNAME, BROKER_PASSWORD, false, true, true, false, 0);
            
            // Create queue if it doesn't exist
            try {
                QueueConfiguration queueConfig = QueueConfiguration.of(QUEUE_NAME)
                    .setAddress(QUEUE_NAME)
                    .setRoutingType(RoutingType.ANYCAST)
                    .setDurable(true);
                session.createQueue(queueConfig);
                logger.debug("Queue '{}' created or already exists", QUEUE_NAME);
            } catch (ActiveMQException e) {
                if (e.getType() != ActiveMQExceptionType.QUEUE_EXISTS) {
                    throw e;
                }
            }
            
            // Create consumer
            consumer = session.createConsumer(QUEUE_NAME);
            
            // Start session
            session.start();
            
            // Start consuming messages
            running = true;
            startMessageConsumption();
            
            logger.info("ActiveMQ Artemis consumer started successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize ActiveMQ Artemis consumer", e);
            cleanup();
            throw new RuntimeException("Failed to initialize ActiveMQ consumer", e);
        }
    }

    /**
     * Start consuming messages in background thread
     */
    private void startMessageConsumption() {
        CompletableFuture.runAsync(() -> {
            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    ClientMessage message = consumer.receive(RECEIVE_TIMEOUT);
                    if (message != null) {
                        // Process message asynchronously
                        executorService.submit(() -> processMessage(message));
                    }
                } catch (ActiveMQException e) {
                    if (running) {
                        logger.error("Error receiving message from queue", e);
                    }
                }
            }
            logger.info("Message consumption loop stopped");
        }, executorService);
    }

    /**
     * Process individual message from the queue
     */
    private void processMessage(ClientMessage message) {
        try {
            String messageBody = message.getBodyBuffer().readString();
            logger.info("Received message: {}", messageBody);
            
            // Parse JSON message to TaskMessage object
            TaskMessage taskMessage = parseTaskMessage(messageBody);
            
            if (taskMessage == null) {
                logger.warn("Failed to parse message, acknowledging and skipping: {}", messageBody);
                message.acknowledge();
                return;
            }
            
            // Process task through orchestrator
            taskOrchestrator.processTask(taskMessage)
                .thenAccept(result -> {
                    logger.info("Task completed successfully: {} - {}", taskMessage.getTaskId(), result.getMessage());
                    try {
                        message.acknowledge();
                    } catch (ActiveMQException e) {
                        logger.error("Failed to acknowledge message", e);
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("Task processing failed: {}", taskMessage.getTaskId(), throwable);
                    try {
                        // In case of failure, we still acknowledge to avoid reprocessing
                        // In production, you might want to implement dead letter queue logic
                        message.acknowledge();
                    } catch (ActiveMQException e) {
                        logger.error("Failed to acknowledge failed message", e);
                    }
                    return null;
                });
                
        } catch (Exception e) {
            logger.error("Error processing message", e);
            try {
                message.acknowledge();
            } catch (ActiveMQException aqe) {
                logger.error("Failed to acknowledge message after processing error", aqe);
            }
        }
    }

    /**
     * Parse JSON string to TaskMessage object
     */
    private TaskMessage parseTaskMessage(String messageBody) {
        try {
            return objectMapper.readValue(messageBody, TaskMessage.class);
        } catch (Exception e) {
            logger.error("Failed to parse task message: {}", messageBody, e);
            return null;
        }
    }

    /**
     * Shutdown consumer and cleanup resources
     */
    @PreDestroy
    public void shutdown() {
        // Check system property set by command line processor
        boolean activeMQEnabled = Boolean.parseBoolean(System.getProperty("syrix.messaging.activemq.enabled", "true"));
        
        if (!activeMQEnabled) {
            logger.debug("ActiveMQ was disabled, no shutdown needed");
            return;
        }
        
        logger.info("Shutting down ActiveMQ Artemis consumer...");
        running = false;
        
        cleanup();
        
        // Shutdown executor service
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        logger.info("ActiveMQ Artemis consumer shutdown completed");
    }

    /**
     * Clean up ActiveMQ resources
     */
    private void cleanup() {
        try {
            if (consumer != null) {
                consumer.close();
            }
        } catch (Exception e) {
            logger.warn("Error closing consumer", e);
        }
        
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception e) {
            logger.warn("Error closing session", e);
        }
        
        try {
            if (sessionFactory != null) {
                sessionFactory.close();
            }
        } catch (Exception e) {
            logger.warn("Error closing session factory", e);
        }
        
        try {
            if (serverLocator != null) {
                serverLocator.close();
            }
        } catch (Exception e) {
            logger.warn("Error closing server locator", e);
        }
    }
    
    /**
     * Check if ActiveMQ integration is enabled
     * @return true if ActiveMQ is enabled and initialized
     */
    public boolean isEnabled() {
        return Boolean.parseBoolean(System.getProperty("syrix.messaging.activemq.enabled", "true"));
    }
    
    /**
     * Check if ActiveMQ consumer is running
     * @return true if ActiveMQ is enabled and running
     */
    public boolean isRunning() {
        return isEnabled() && running;
    }
}
