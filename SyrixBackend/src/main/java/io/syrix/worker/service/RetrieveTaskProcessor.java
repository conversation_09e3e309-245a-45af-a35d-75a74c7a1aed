package io.syrix.worker.service;

import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.main.Context;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.reports.model.opa.EvaluationReport;
import io.syrix.messaging.TaskMessage;
import io.syrix.worker.exception.TaskNotFoundException;
import io.syrix.worker.messaging.model.TaskResult;
import io.syrix.dao.TaskDao;
import io.syrix.dao.CustomerReportDao;
import io.syrix.datamodel.report.CustomerReport;
import io.syrix.datamodel.report.PolicyResult;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * Service responsible for processing retrieve configuration tasks.
 * Handles the conversion of TaskMessage to RetrieveTask and coordinates
 * configuration retrieval and report generation.
 */
@ApplicationScoped
public class RetrieveTaskProcessor {

	private final ConfigurationRetrievalService configRetrievalService;
	private final OpaReportService opaReportService;
	private final Context context;
	private final TaskDao taskDao;
	private final CustomerReportDao customerReportDao;
	private final TaskStatusService taskStatusService;

	@Inject
	public RetrieveTaskProcessor(Context context,
								 ConfigurationRetrievalService configRetrievalService,
								 OpaReportService opaReportService,
								 TaskDao taskDao,
								 CustomerReportDao customerReportDao,
								 TaskStatusService taskStatusService) {
		this.context = context;
		this.configRetrievalService = configRetrievalService;
		this.opaReportService = opaReportService;
		this.taskDao = taskDao;
		this.customerReportDao = customerReportDao;
		this.taskStatusService = taskStatusService;
	}

	/**
	 * Processes a retrieve configuration task.
	 * Converts TaskMessage to RetrieveTask and generates evaluation report.
	 *
	 * @param message the task message containing task details
	 * @return CompletionStage with task result
	 */
	public CompletionStage<TaskResult> process(TaskMessage message) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				// Update status to IN_PROGRESS when processing starts
				taskStatusService.updateToInProgress(message);

				RetrieveTask retrieveTask = convertToRetrieveTask(message);

				configRetrievalService.configRetrieve(context, retrieveTask);
				EvaluationReport report = opaReportService.generate(retrieveTask);

				if (report != null) {
					CustomerReport customerReport = convertToClientReport(retrieveTask, report);
					customerReportDao.save(customerReport);

					// Update status to COMPLETED on success
					taskStatusService.updateToCompleted(message);
					return TaskResult.success(message.getTaskId().toString(), "Configuration retrieved and analyzed successfully");
				} else {
					// Update status to FAILED if report generation fails
					taskStatusService.updateToFailed(message, "Report generation returned null");
					return TaskResult.failure(message.getTaskId().toString(), "Failed to generate evaluation report", "Report generation returned null");
				}
			} catch (Exception e) {
				// Update status to FAILED on any exception
				taskStatusService.updateToFailed(message, e.getMessage());
				return TaskResult.failure(message.getTaskId().toString(), "Error processing retrieve task: " + e.getMessage(), e.toString());
			}
		});
	}

	/**
	 * Converts TaskMessage to RetrieveTask domain object.
	 * Retrieves the task from database using TaskMessage.id, TaskMessage.customerId and CREATED status.
	 *
	 * @param message the task message to convert
	 * @return RetrieveTask domain object from database
	 * @throws TaskNotFoundException if task is not found in database or data access error occurs
	 */
	private RetrieveTask convertToRetrieveTask(TaskMessage message) throws TaskNotFoundException {
		UUID taskId = message.getTaskId();
		UUID customerId = message.getCustomerId();

		return taskDao.findRetrieveTaskByIdAndCustomer(taskId, customerId)
				.orElseThrow(() ->
						new TaskNotFoundException("Task not found with taskId: " + taskId + " and customerId: " + customerId));
	}

	/**
	 * Converts EvaluationReport to customerReport for database storage.
	 *
	 * @param retrieveTask     the retrieve task containing customer information
	 * @param evaluationReport the evaluation report to convert
	 * @return customerReport ready for database storage
	 */
	private CustomerReport convertToClientReport(RetrieveTask retrieveTask, EvaluationReport evaluationReport) {
		CustomerReport customerReport = new CustomerReport();
		customerReport.setCustomerId(retrieveTask.getCustomerId());
		customerReport.setCreatedAt(LocalDateTime.now());

		Map<ConfigurationServiceType, List<PolicyResult>> convertedResults = evaluationReport.getServiceResults()
				.entrySet()
				.stream()
				.collect(Collectors.toMap(
						Map.Entry::getKey,
						entry -> entry.getValue().stream()
								.map(this::convertPolicyResult)
								.collect(Collectors.toList())
				));

		customerReport.setResults(convertedResults);
		return customerReport;
	}

	/**
	 * Converts OPA PolicyResult to datamodel PolicyResult.
	 *
	 * @param opaPolicyResult the OPA policy result to convert
	 * @return converted datamodel PolicyResult
	 */
	private PolicyResult convertPolicyResult(io.syrix.reports.model.opa.PolicyResult opaPolicyResult) {
		PolicyResult policyResult = new PolicyResult();
		policyResult.setPolicyId(opaPolicyResult.getPolicyId());
		policyResult.setRequirementMet(opaPolicyResult.isRequirementMet());
		policyResult.setCriticality(opaPolicyResult.getCriticality());
		policyResult.setReportDetails(opaPolicyResult.getReportDetails());
		return policyResult;
	}
}
