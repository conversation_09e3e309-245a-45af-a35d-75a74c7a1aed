package io.syrix.worker.service;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.common.dns.DnsService;
import io.syrix.common.exceptions.SyrixException;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.model.TenantDetails;
import io.syrix.common.storage.Storage;
import io.syrix.common.tmp.TempFolder;
import io.syrix.common.utils.FileUtils;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.datamodel.task.Task;
import io.syrix.main.Configuration;
import io.syrix.main.Context;
import io.syrix.products.microsoft.ConfigurationService;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.common.MSUtils;
import io.syrix.products.microsoft.defender.DefenderConfigurationService;
import io.syrix.products.microsoft.entra.service.EntraConfigurationService;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import io.syrix.products.microsoft.sharepoint.SharePointConfigurationService;
import io.syrix.products.microsoft.teams.TeamsConfigurationService;
import io.syrix.products.microsoft.forms.FormsConfigurationService;
import io.syrix.protocols.client.ClientFactory;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.PowerShellTeamsClient;
import jakarta.enterprise.context.ApplicationScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

@ApplicationScoped
public class ConfigurationRetrievalService {
	private static final Logger logger = LoggerFactory.getLogger(ConfigurationRetrievalService.class);
	private final ObjectMapper jsonMapper;
	private final Storage storage;

	public ConfigurationRetrievalService(Storage storage) {
		this.jsonMapper = new ObjectMapper()
				.registerModule(new JavaTimeModule())
				.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
		this.storage = storage;
	}

	public void configRetrieve(Context context, RetrieveTask task) {
		try {
			Configuration config = context.getConfig();

			// Initialize Graph client
			MicrosoftGraphClient graphClient = ClientFactory.initGraphClient(context);
			String tenant = graphClient.getTenantId();
			String domain = graphClient.getDomain().join();

			TokenCredential credential = new ClientSecretCredentialBuilder()
					.clientId(config.credentials().clientId())
					.clientSecret(config.credentials().clientSecret())
					.tenantId(tenant)
					.build();

			PowerShellClient powerShellClient = ClientFactory.initPowerShellClient(context, tenant, domain);


			String site = graphClient.getSite();
			String adminDomain = site.replace(".sharepoint.com", "-admin.sharepoint.com").replace("https://","");
			String tenantId = graphClient.getTenantId();

			PowerShellSharepointClient powerShellSharepointClient = ClientFactory.initSharepointPowerShellClient(context, domain, adminDomain);
			PowerShellTeamsClient powerShellTeamsClient = ClientFactory.initPowerShellTeamsClient(context, tenantId);

			checkOrAddToAdminGroup(graphClient, config);

			DnsService dnsService = new DnsService.Builder()
					.withAzureCredential(credential)
					.withDnsServers("8.8.4.4", "1.0.0.1")
					.withObjectMapper(jsonMapper)
					.build();

			TenantDetails tenantDetails = MSUtils.getTenantDetails(graphClient);
			ObjectMapper spJsonMapper = new ObjectMapper();
			spJsonMapper = spJsonMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);

			MetricsCollector metrics = new MetricsCollector();
			// Initialize all configuration services
			Map<ConfigurationServiceType, ConfigurationService> services = new HashMap<>();

			for (ConfigurationServiceType serviceType : task.getServiceTypeList()) {
				ConfigurationService configService = switch (serviceType) {
					case ENTRA -> new EntraConfigurationService(graphClient, jsonMapper, metrics);
					case TEAMS -> new TeamsConfigurationService(graphClient, powerShellTeamsClient, jsonMapper, metrics);
					case SHAREPOINT -> new SharePointConfigurationService(graphClient, powerShellSharepointClient, spJsonMapper, metrics);
					case DEFENDER -> new DefenderConfigurationService(graphClient, powerShellClient, jsonMapper, metrics);
					case POWER_PLATFORM -> null;
					case EXCHANGE_ONLINE -> new ExchangeOnlineConfigurationService(graphClient, powerShellClient, jsonMapper, metrics, dnsService);
					case FORMS -> new FormsConfigurationService(graphClient, jsonMapper, metrics);
//					case DYNAMICS -> null;
					case ALL -> null;
				};

				if (configService == null) {
					logger.warn("Has no found service for type: {}", serviceType.name());
				} else {
					services.put(serviceType, configService);
				}
			}

			// Create output directory if it doesn't exist
			Path outputDir = Paths.get(config.outputPath());
			Files.createDirectories(outputDir);

			// Map to store results
			Map<String, ConfigurationResult> results = new HashMap<>();

			// Start configuration export for each service
			Instant startTime = Instant.now();
			logger.info("Starting configuration export for all services");

			List<CompletableFuture<Void>> futures = services.entrySet().stream()
					.map(entry -> CompletableFuture
							.supplyAsync(() -> entry.getValue().exportConfiguration())
							.thenAccept(result -> results.put(entry.getKey().getResultName(), result))
							.exceptionally(e -> {
								logger.error("Error exporting {} ID configuration", entry.getKey().getResultName(), e);
								return null;
							})).toList();


			// Wait for all exports to complete
			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

			// Calculate total duration
			Duration totalDuration = Duration.between(startTime, Instant.now());
			logger.info("All configuration exports completed in {} seconds", totalDuration.getSeconds());

			saveConfiguration(task, results, tenantDetails, jsonMapper);

			// Save metrics
			saveMetrics(task, metrics);
		} catch (Exception e) {
			logger.error("Error during configuration retrieval", e);
			System.exit(1);
		}
	}

	private void checkOrAddToAdminGroup(MicrosoftGraphClient graphClient, Configuration config) {
		Map<String, String> queryParams = Map.of(FILTER_PARAM,
				String.format("appId eq '%s'", config.credentials().clientId()),
				PARAM_SELECT, ID_FIELD);
		CompletableFuture<JsonNode> servicePrincipal = graphClient.getAzureServicePrincipals(queryParams, null);
		graphClient.checkAndAssignServicePrincipalToAdminGroup(MicrosoftGraphClient.getServicePrincipalID(servicePrincipal));
	}

	private void saveConfiguration(Task task, Map<String, ConfigurationResult> results, TenantDetails tenantDetails, ObjectMapper jsonMapper) {
		ObjectNode finalJson = jsonMapper.createObjectNode();

		// Add metadata fields
		finalJson.put("baseline_version", "1.4.0");
		finalJson.put("Syrix_version", "0.1");

		// Add timestamps
		Instant now = Instant.now();
		DateTimeFormatter dateFormatter = DateTimeFormatter
				.ofPattern("MM/dd/yyyy HH:mm:ss 'UTC'")
				.withZone(ZoneOffset.UTC);
		finalJson.put("date", dateFormatter.format(now));

		// Format UTC/Zulu timestamp
		DateTimeFormatter utcFormatter = DateTimeFormatter
				.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
				.withZone(ZoneOffset.UTC);
		finalJson.put("timestamp_zulu", utcFormatter.format(now));

		// Add UUID
		String uuid = UUID.randomUUID().toString();
		finalJson.put("report_uuid", uuid);

		// Add tenant details
		ArrayNode tmpTenantDetails = jsonMapper.createArrayNode();
		ObjectNode tenantInfo = jsonMapper.createObjectNode();
		tenantInfo.put("TenantId", tenantDetails.tenantId());
		tenantInfo.put("DisplayName", tenantDetails.displayName());
		tenantInfo.put("DomainName", tenantDetails.domainName());

		ObjectNode aadTenantInfo = jsonMapper.createObjectNode();
		aadTenantInfo.put("DisplayName", tenantDetails.displayName());
		aadTenantInfo.put("TenantId", tenantDetails.tenantId());
		aadTenantInfo.put("DomainName", tenantDetails.domainName());
		aadTenantInfo.setAll(tenantDetails.additionalData());

		tenantInfo.set("AADTenantInfo", aadTenantInfo);
		tmpTenantDetails.add(tenantInfo);
		finalJson.set("tenant_details", tmpTenantDetails);


		// Add empty scuba config
		finalJson.set("Syrix_config", jsonMapper.createObjectNode());

		try (TempFolder instance = TempFolder.newInstance()) {
			String filename = String.format("config-%s.json", uuid);
			Path filePath = instance.getFolder().resolve(filename);
			// Add all service configurations
			try {
				for (Map.Entry<String, ConfigurationResult> entry : results.entrySet()) {
					if (entry.getValue() != null) {
						jsonMapper.writerWithDefaultPrettyPrinter().writeValue(filePath.toFile(), entry.getValue().getData());
					}
					logger.info("Saved {} configuration to {}", entry.getKey(), filename);
				}
			} catch (Exception excp) {
				throw new SyrixException("Failed to write configuration JSON", excp);
			}

			// Add all service configurations to the root of the JSON
			for (Map.Entry<String, ConfigurationResult> entry : results.entrySet()) {
				if (entry.getValue() != null) {
					JsonNode dataNode = entry.getValue().getData();
					if (dataNode != null) {
						dataNode.fieldNames().forEachRemaining(fieldName ->
								finalJson.set(fieldName, dataNode.get(fieldName))
						);
						logger.info("Added {} configuration data to finalJson", entry.getKey());
					}
				}
			}
			FileUtils.writeJsonFile(finalJson, filePath);
			logger.info("Saved all configurations to {}", filename);

			storage.saveConfig(task, filePath);
		}
	}

	private void saveMetrics(RetrieveTask task, MetricsCollector metrics) {
		try (TempFolder tmpFolder = TempFolder.newInstance()) {
			Path metricsPath = tmpFolder.getFolder().resolve("metrics.json");
			jsonMapper.writerWithDefaultPrettyPrinter().writeValue(metricsPath.toFile(), metrics.getSnapshot());
			storage.saveMetrics(task, metricsPath);
			logger.info("Saved metrics for task {}", task.getId());
		} catch (IOException ex) {
			throw new SyrixRuntimeException("SaveMetrics", ex);
		}
	}
}
