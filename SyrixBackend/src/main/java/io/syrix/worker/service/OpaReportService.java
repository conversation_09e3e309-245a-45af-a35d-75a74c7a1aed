package io.syrix.worker.service;

import io.syrix.common.storage.Storage;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.reports.OpaEvaluationReport;
import io.syrix.reports.model.opa.EvaluationReport;
import jakarta.enterprise.context.ApplicationScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;

@ApplicationScoped
public class OpaReportService {
	private static final Logger logger = LoggerFactory.getLogger(OpaReportService.class);
	private final Storage storage;

	public OpaReportService(Storage storage) {
		this.storage = storage;
	}

	/**
	 * Generates OPA evaluation report for the given task.
	 * 
	 * @param task The retrieve task containing service types and configuration
	 * @return EvaluationReport containing policy evaluation results
	 */
	public EvaluationReport generate(RetrieveTask task) {
		logger.info("Start generate OPA evaluation report");
		
		Path configPath = storage.loadConfigPath(task);
		
		EvaluationReport evaluationReport = OpaEvaluationReport.generate(
			configPath, 
			task.getServiceTypeList()
		);
		
		logger.info("Finished generate OPA evaluation report");
		return evaluationReport;
	}

	/*
		public void generate(RetrieveTask task) {
		logger.info("Start generate Opa report");
		Path confPath = storage.loadConfigPath(task);
		try (TempFolder tmpFolder = TempFolder.newInstance()) {
			Report.generate(tmpFolder.getFolder(), confPath, task.getServiceTypeList(), jsonMapper, false);
			storage.saveReport(task, tmpFolder.getFolder());
		}
		logger.info("Finished generate Opa report");
	}
	 */

}
