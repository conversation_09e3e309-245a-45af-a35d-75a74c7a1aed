package io.syrix.worker.service;

import io.syrix.dao.TaskDao;
import io.syrix.datamodel.task.Task;
import io.syrix.datamodel.task.TaskStatus;
import io.syrix.messaging.TaskMessage;
import io.syrix.worker.exception.InvalidTaskTransitionException;
import io.syrix.worker.exception.TaskNotFoundException;
import io.syrix.worker.exception.TaskStatusUpdateException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.EnumSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Service for managing task status updates in the database.
 * Provides centralized task status management with validation and logging.
 * Enforces valid status transitions to maintain task lifecycle integrity.
 */
@ApplicationScoped
public class TaskStatusService {
    
    private static final Logger log = LoggerFactory.getLogger(TaskStatusService.class);
    
    private final TaskDao taskDao;
    
    // Valid status transitions mapping
    private static final Map<TaskStatus, Set<TaskStatus>> ALLOWED_TRANSITIONS = Map.of(
        TaskStatus.CREATED, EnumSet.of(TaskStatus.QUEUED),
        TaskStatus.QUEUED, EnumSet.of(TaskStatus.RECEIVED),
        TaskStatus.RECEIVED, EnumSet.of(TaskStatus.IN_PROGRESS, TaskStatus.FAILED),
        TaskStatus.IN_PROGRESS, EnumSet.of(TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.ABORTED),
        TaskStatus.COMPLETED, EnumSet.noneOf(TaskStatus.class), // Terminal state
        TaskStatus.FAILED, EnumSet.noneOf(TaskStatus.class),    // Terminal state
        TaskStatus.ABORTED, EnumSet.noneOf(TaskStatus.class)    // Terminal state
    );
    
    @Inject
    public TaskStatusService(TaskDao taskDao) {
        this.taskDao = taskDao;
    }
    
    /**
     * Update task status to QUEUED when task is added to queue.
     * Valid transition: CREATED -> QUEUED
     * 
     * @param message The task message containing task ID and client ID
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToQueued(TaskMessage message) throws TaskStatusUpdateException {
        updateTaskStatus(message, TaskStatus.QUEUED, "Task queued for processing");
    }
    
    /**
     * Update task status to RECEIVED when task is received by worker.
     * Valid transition: QUEUED -> RECEIVED
     * 
     * @param message The task message containing task ID and client ID
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToReceived(TaskMessage message) throws TaskStatusUpdateException {
        updateTaskStatus(message, TaskStatus.RECEIVED, "Task received by worker");
    }
    
    /**
     * Update task status to IN_PROGRESS when task processing starts.
     * Valid transition: RECEIVED -> IN_PROGRESS
     * 
     * @param message The task message containing task ID and client ID
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToInProgress(TaskMessage message) throws TaskStatusUpdateException {
        updateTaskStatus(message, TaskStatus.IN_PROGRESS, "Task processing started");
    }
    
    /**
     * Update task status to COMPLETED when task processing finishes successfully.
     * Valid transition: IN_PROGRESS -> COMPLETED
     * 
     * @param message The task message containing task ID and client ID
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToCompleted(TaskMessage message) throws TaskStatusUpdateException {
        updateTaskStatus(message, TaskStatus.COMPLETED, "Task completed successfully");
    }
    
    /**
     * Update task status to FAILED when task processing fails.
     * Valid transitions: CREATED -> FAILED, RECEIVED -> FAILED, IN_PROGRESS -> FAILED
     * Note: Cannot transition from QUEUED to FAILED (tasks in queue should be processed)
     * 
     * @param message The task message containing task ID and client ID
     * @param errorMessage Optional error message to log
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToFailed(TaskMessage message, String errorMessage) throws TaskStatusUpdateException {
        String logMessage = errorMessage != null ? "Task failed: " + errorMessage : "Task failed";
        updateTaskStatus(message, TaskStatus.FAILED, logMessage);
    }
    
    /**
     * Update task status to ABORTED when task is cancelled.
     * Valid transition: IN_PROGRESS -> ABORTED
     * 
     * @param message The task message containing task ID and client ID
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    public void updateToAborted(TaskMessage message) throws TaskStatusUpdateException {
        updateTaskStatus(message, TaskStatus.ABORTED, "Task aborted");
    }
    
    /**
     * Update task status by task ID and client ID directly with transition validation.
     * 
     * @param taskId The task identifier
     * @param clientId The client identifier  
     * @param newStatus The new status to set
     * @throws TaskNotFoundException if task is not found
     * @throws InvalidTaskTransitionException if status transition is invalid
     * @throws TaskStatusUpdateException if database operation fails
     */
    public void updateTaskStatus(UUID taskId, UUID clientId, TaskStatus newStatus) throws TaskStatusUpdateException {
        try {
            // Find task by both taskId and clientId for security
            Task task = taskDao.findByIdAndCustomerId(taskId, clientId)
                    .orElseThrow(() -> new TaskNotFoundException("Task not found with taskId: " + taskId + " and clientId: " + clientId));
            
            TaskStatus currentStatus = task.getStatus();
            // Validate status transition
            if (!isValidTransition(currentStatus, newStatus)) {
                log.warn("Invalid status transition for task {}: {} -> {}. Transition not allowed.", taskId, currentStatus, newStatus);
                throw new InvalidTaskTransitionException(currentStatus, newStatus);
            }
            
            task.setStatus(newStatus);
            taskDao.save(task);
            
            log.info("Task {} status updated: {} -> {}", taskId, currentStatus, newStatus);
        } catch (TaskNotFoundException | InvalidTaskTransitionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            log.error("Database error while updating task {} status to {}: {}", taskId, newStatus, e.getMessage(), e);
            throw new TaskStatusUpdateException("Failed to update task status to " + newStatus, e);
        }
    }
    
    /**
     * Validates if a status transition is allowed according to the task lifecycle rules.
     * 
     * @param currentStatus The current task status
     * @param newStatus The desired new status
     * @return true if transition is valid, false otherwise
     */
    private boolean isValidTransition(TaskStatus currentStatus, TaskStatus newStatus) {
        // Allow staying in the same status (idempotent operations)
        if (currentStatus == newStatus) {
            return true;
        }
        
        // Special case for FAILED status: can transition from any status except QUEUED
        if (newStatus == TaskStatus.FAILED) {
            return currentStatus != TaskStatus.QUEUED;
        }
        
        // Check if transition is in allowed transitions map
        Set<TaskStatus> allowedTransitions = ALLOWED_TRANSITIONS.get(currentStatus);
        return allowedTransitions != null && allowedTransitions.contains(newStatus);
    }
    
    /**
     * Internal method to update task status with consistent validation and logging.
     * 
     * @param message The task message containing task ID and client ID
     * @param newStatus The new status to set
     * @param logMessage Message to log on successful update
     * @throws TaskStatusUpdateException if the status update fails
     * @throws InvalidTaskTransitionException if the status transition is invalid
     * @throws TaskNotFoundException if the task is not found
     */
    private void updateTaskStatus(TaskMessage message, TaskStatus newStatus, String logMessage) throws TaskStatusUpdateException {
        try {
            UUID taskId = message.getTaskId();
            UUID clientId = message.getCustomerId();
            
            updateTaskStatus(taskId, clientId, newStatus);
            log.info("{} for task {}", logMessage, taskId);
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid UUID format in task message: taskId={}, clientId={}", message.getTaskId(), message.getCustomerId(), e);
            throw new TaskStatusUpdateException("Invalid UUID format in task message", e);
        } catch (TaskNotFoundException | InvalidTaskTransitionException | TaskStatusUpdateException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while updating task {} status to {}: {}", message.getTaskId(), newStatus, e.getMessage(), e);
            throw new TaskStatusUpdateException("Unexpected error while updating task status", e);
        }
    }
}
