package io.syrix.worker.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import io.syrix.dao.CustomerReportDao;
import io.syrix.dao.DaoFactory;
import io.syrix.dao.TaskDao;
import io.syrix.dao.impl.mongodb.MongoDaoFactory;
import io.syrix.dao.mongodb.MongoDatabaseProvider;
import io.syrix.dao.mongodb.SimpleMongoDatabaseProvider;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

/**
 * CDI Producer for DAO beans.
 * Provides MongoDB-based DAO implementations for dependency injection.
 */
@ApplicationScoped
public class DaoProducer {
    private static final Logger logger = LoggerFactory.getLogger(DaoProducer.class);

    private DaoFactory daoFactory;
    private MongoDatabaseProvider databaseProvider;
    private MongoClient mongoClient;

    private final MongoConfig mongoConfig;

    /**
     * Constructor to inject MongoConfig.
     *
     * @param mongoConfig MongoConfig instance
     */
    @Inject
    public DaoProducer(MongoConfig mongoConfig) {
        this.mongoConfig = mongoConfig;
    }

    @Produces
    @ApplicationScoped
    public MongoClient mongoClient() {
        if (mongoClient == null) {

            logger.info("Initializing MongoDB client, connecting to {}:{}/{}", mongoConfig.host(), mongoConfig.port(), mongoConfig.database());

            // Create codec registry for POJO mapping
            CodecRegistry pojoCodecRegistry = fromRegistries(
                    MongoClientSettings.getDefaultCodecRegistry(),
                    fromProviders(PojoCodecProvider.builder().automatic(true).build())
            );

            // Build client settings
            MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                    .applyToClusterSettings(builder ->
                            builder.hosts(List.of(new ServerAddress(mongoConfig.host(), mongoConfig.port()))))
                    .codecRegistry(pojoCodecRegistry);

            // Add credentials if provided

            if (StringUtils.isNotEmpty(mongoConfig.username()) && StringUtils.isNotEmpty(mongoConfig.password())) {
                logger.info("Using authentication for MongoDB with username: {}", mongoConfig.username());
                MongoCredential credential = MongoCredential.createCredential(
                        mongoConfig.username(), mongoConfig.authenticationDatabase(), mongoConfig.password().toCharArray());
                settingsBuilder.credential(credential);
            } else {
                logger.info("No authentication credentials provided for MongoDB, connecting without authentication");
            }

            MongoClientSettings settings = settingsBuilder.build();

            // Create the client
            mongoClient = MongoClients.create(settings);
        }
        return mongoClient;
    }



    /**
     * Produces MongoDatabaseProvider bean for CDI injection.
     * Creates a SimpleMongoDatabaseProvider using configuration from application.properties/yml.
     *
     * @return MongoDatabaseProvider instance
     */
    @Produces
    @ApplicationScoped
    public MongoDatabaseProvider produceMongoDatabaseProvider() {
        if (databaseProvider == null) {
            logger.info("Using database: {}", mongoConfig.database());
            databaseProvider = new SimpleMongoDatabaseProvider(mongoClient(), mongoConfig.database());
        }
        return databaseProvider;
    }

    /**
     * Produces DaoFactory bean for CDI injection.
     * Creates a MongoDB-based DaoFactory using the MongoDatabaseProvider.
     *
     * @return DaoFactory instance
     */
    @Produces
    @ApplicationScoped
    public DaoFactory produceDaoFactory() {
        if (daoFactory == null) {
            logger.info("Creating MongoDB DaoFactory");
            MongoDatabaseProvider provider = produceMongoDatabaseProvider();
            daoFactory = new MongoDaoFactory(provider);
        }
        return daoFactory;
    }

    /**
     * Produces TaskDao bean for CDI injection.
     * Uses the DaoFactory to get the TaskDao implementation.
     *
     * @return TaskDao instance
     */
    @Produces
    @ApplicationScoped
    public TaskDao produceTaskDao() {
        DaoFactory factory = produceDaoFactory();
        TaskDao taskDao = factory.getTaskDao();
        logger.info("TaskDao bean created: {}", taskDao.getClass().getSimpleName());
        return taskDao;
    }


    @Produces
    @ApplicationScoped
    public CustomerReportDao produceClientReportDao() {
        DaoFactory factory = produceDaoFactory();
        CustomerReportDao customerReportDao = factory.getCustomerReportDao();
        logger.info("ClientReportDao bean created: {}", customerReportDao.getClass().getSimpleName());
        return customerReportDao;
    }
}
