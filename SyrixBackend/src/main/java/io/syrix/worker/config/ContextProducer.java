package io.syrix.worker.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.runtime.Quarkus;
import io.syrix.exception.StartupWorkerConfigurationException;
import io.syrix.main.Configuration;
import io.syrix.main.Context;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * CDI Producer for Context bean
 * Handles initialization of Context from config.yaml file
 * All configuration including cert path and password must be in the YAML file
 */
@ApplicationScoped
public class ContextProducer {
    private static final Logger logger = LoggerFactory.getLogger(ContextProducer.class);
    private static final ObjectMapper yamlMapper = new YAMLMapper();

    private final CommandLineArgsProcessor argsProcessor;
    
    private Context context;

    @Inject
    public ContextProducer(CommandLineArgsProcessor argsProcessor) {
        this.argsProcessor = argsProcessor;
    }

    /**
     * Initialize context on application startup
     * Application will be terminated if initialization fails
     * Priority 200 ensures this runs after CommandLineArgsProcessor (priority 100)
     */
    void onStart(@Observes @jakarta.annotation.Priority(200) StartupEvent event) {
        logger.info("Initializing Syrix Context from config.yaml...");
        try {
            this.context = createContext();
            logger.info("Syrix Context initialized successfully");
        } catch (StartupWorkerConfigurationException e) {
            logger.error("Configuration error during startup: {}", e.getMessage());
            logger.error("Application will be terminated due to configuration issues");
            terminateApplication(e);
        } catch (Exception e) {
            logger.error("Critical error during context initialization", e);
            logger.error("Application will be terminated due to critical startup failure");
            terminateApplication(new StartupWorkerConfigurationException("Context initialization failed: " + e.getMessage()));
        }
    }
    
    /**
     * Terminates the application when critical configuration errors occur
     */
    private void terminateApplication(StartupWorkerConfigurationException e) {
        logger.error("=== CRITICAL STARTUP ERROR ===");
        logger.error("Application cannot start due to configuration problems:");
        logger.error("Error: {}", e.getMessage());
        logger.error("Please fix the configuration and restart the application.");
        logger.error("=== APPLICATION TERMINATING ===");
        
        // Use Quarkus.asyncExit() for proper shutdown in async context
        new Thread(() -> {
            try {
                Thread.sleep(100); // Brief delay to ensure logs are written
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
            Quarkus.asyncExit(1);
        }).start();
    }
    
    /**
     * Produces Context bean for CDI injection
     */
    @Produces
    @ApplicationScoped
    public Context produceContext() {
        if (context == null) {
            // If context is null, the app is shutting down due to configuration error
            logger.error("Context producer called but context is null - application startup failed");
            throw new IllegalStateException("Context not initialized. Application startup failed due to configuration issues.");
        }
        return context;
    }
    
    /**
     * Creates Context instance from configuration
     * @throws StartupWorkerConfigurationException if any configuration error occurs
     */
    private Context createContext() {
        try {
            // Determine configuration file path
            String configPath = determineConfigPath();
            
            logger.info("Loading configuration from: {}", configPath);
            
            // Validate configuration file exists
            validateConfigFile(configPath);
            
            // Load configuration from YAML file
            Configuration configuration = loadConfiguration(configPath);
            
            String certPath = argsProcessor.getCertPath();
            String certPassword = argsProcessor.getCertPass();
            
            // Validate certificate file exists
            validateCertFile(certPath);
            
            logger.info("Using certificate: {}", certPath);
            
            return new Context(configuration, certPath, certPassword);
            
        } catch (JsonParseException e) {
            throw new StartupWorkerConfigurationException("Configuration file contains invalid YAML syntax: " + e.getMessage());
        } catch (JsonMappingException e) {
            throw new StartupWorkerConfigurationException("Configuration file structure is invalid: " + e.getMessage());
        } catch (IOException e) {
            throw new StartupWorkerConfigurationException("Failed to read configuration file: " + e.getMessage());
        } catch (StartupWorkerConfigurationException e) {
            throw e; // Re-throw configuration exceptions
        } catch (Exception e) {
            throw new StartupWorkerConfigurationException("Unexpected error during configuration loading: " + e.getMessage());
        }
    }
    
    /**
     * Determines configuration file path from command line arguments
     * Configuration file path is required and must be provided via command line
     */
    private String determineConfigPath() {
        // Check command line arguments
        String cmdLineConfigPath = argsProcessor.getConfigPath();
        if (cmdLineConfigPath == null || cmdLineConfigPath.trim().isEmpty()) {
            throw new StartupWorkerConfigurationException(
                "Configuration file path is required. Please provide it as command line argument: java -jar app.jar [certPass] [certPath] [configPath]"
            );
        }
        
        logger.info("Using config path from command line: {}", cmdLineConfigPath);
        return cmdLineConfigPath;
    }
    
    /**
     * Validates that configuration file exists and is readable
     * @throws StartupWorkerConfigurationException if validation fails
     */
    private void validateConfigFile(String configPath) {
        if (configPath == null || configPath.trim().isEmpty()) {
            throw new StartupWorkerConfigurationException("Configuration file path is required.");
        }
        
        Path configFile = Paths.get(configPath);
        if (!Files.exists(configFile)) {
            throw new StartupWorkerConfigurationException("Configuration file not found: " + configPath);
        }
        
        if (!Files.isReadable(configFile)) {
            throw new StartupWorkerConfigurationException("Configuration file is not readable: " + configPath);
        }
        
        if (!configPath.toLowerCase().endsWith(".yml") && !configPath.toLowerCase().endsWith(".yaml")) {
            logger.warn("Configuration file does not have .yml or .yaml extension: {}", configPath);
        }
    }
    
    /**
     * Validates that certificate file exists and is readable
     * @throws StartupWorkerConfigurationException if validation fails
     */
    private void validateCertFile(String certPath) {
        if (certPath == null || certPath.trim().isEmpty()) {
            throw new StartupWorkerConfigurationException("Certificate path is required in configuration file.");
        }
        
        Path certFile = Paths.get(certPath);
        if (!Files.exists(certFile)) {
            throw new StartupWorkerConfigurationException("Certificate file not found: " + certPath);
        }
        
        if (!Files.isReadable(certFile)) {
            throw new StartupWorkerConfigurationException("Certificate file is not readable: " + certPath);
        }
        
        // Validate certificate file extension
        String fileName = certFile.getFileName().toString().toLowerCase();
        if (!fileName.endsWith(".p12") && !fileName.endsWith(".pfx")) {
            logger.warn("Certificate file does not have expected extension (.p12 or .pfx): {}", certPath);
        }
    }
    
    /**
     * Loads configuration from YAML file
     * @throws IOException if file reading fails
     * @throws JsonParseException if YAML parsing fails
     * @throws JsonMappingException if object mapping fails
     */
    private Configuration loadConfiguration(String configPath) throws IOException {
        Path configFile = Paths.get(configPath);
        logger.debug("Reading configuration file: {}", configFile.toAbsolutePath());

        return yamlMapper.readValue(
                Paths.get(configPath).toFile(),
                Configuration.class
        );
    }
}
