package io.syrix.worker.config;

import io.smallrye.config.ConfigMapping;
import io.smallrye.config.WithDefault;

/**
 * Configuration mapping for MongoDB settings.
 * Maps properties from application.yml under syrix.mongodb prefix.
 */
@ConfigMapping(prefix = "syrix.mongodb")
public interface MongoConfig {

    /**
     * MongoDB host address.
     * Default: localhost
     *
     * @return MongoDB host
     */
    @WithDefault("localhost")
    String host();

    /**
     * MongoDB port number.
     * Default: 27017
     *
     * @return MongoDB port
     */
    @WithDefault("27017")
    int port();

    /**
     * MongoDB database name.
     * Default: Syrix
     *
     * @return Database name
     */
    @WithDefault("Syrix")
    String database();

    /**
     * MongoDB username for authentication.
     * Default: admin
     *
     * @return Username
     */
    @WithDefault("admin")
    String username();

    /**
     * MongoDB password for authentication.
     * Default: password123
     *
     * @return Password
     */
    @WithDefault("password123")
    String password();

    /**
     * Authentication database name.
     * Default: admin
     *
     * @return Authentication database name
     */
    @WithDefault("admin")
    String authenticationDatabase();
}
