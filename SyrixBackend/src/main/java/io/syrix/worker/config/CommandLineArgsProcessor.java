package io.syrix.worker.config;

import io.quarkus.runtime.StartupEvent;
import io.syrix.exception.StartupWorkerConfigurationException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Processes command line arguments for Syrix application.
 * <p>
 * Command line format:
 * java -jar syrix-backend.jar <certPassword> <certPath> <configPath> [--disable-activemq]
 * <p>
 * Where:
 * - certPassword: Password for the certificate file
 * - certPath: Path to certificate file (.p12 or .pfx format)
 * - configPath: Path to YAML configuration file (.yml or .yaml format)
 * - --disable-activemq: Optional flag to disable ActiveMQ integration
 */
@ApplicationScoped
public class CommandLineArgsProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(CommandLineArgsProcessor.class);

    private String certPath;
    private String certPass;
    private String configPath;
    private boolean activeMQEnabled = true; // Default to enabled
    private boolean initialized = false;
    
    // Static holder for command line arguments - set during main method execution
    private static String[] commandLineArgs;
    
    /**
     * Store command line arguments statically for later processing
     * This method is called from SyrixWorker.main() before Quarkus startup
     */
    public static void setCommandLineArgs(String[] args) {
        commandLineArgs = args;
    }
    
    /**
     * Initializes command line arguments on application startup.
     * This method runs before ContextProducer.onStart() due to observer priority.
     */
    void onStartup(@Observes @jakarta.annotation.Priority(10) StartupEvent event) {
        logger.info("Initializing command line arguments processor...");
        
        if (commandLineArgs != null && commandLineArgs.length > 0) {
            initialize(commandLineArgs);
        } else {
            logger.error("No command line arguments available during startup initialization");
            throw new StartupWorkerConfigurationException(
                "Command line arguments not available. Please ensure application is started with required arguments."
            );
        }
    }
    
    /**
     * Initialize from command line arguments
     * Expects: java -jar syrix-backend.jar <certPassword> <certPath> <configPath> [--disable-activemq]
     */
    public void initialize(String[] args) {
        if (initialized) {
            return;
        }

        logger.info("Processing command line arguments...");

        if (args.length < 3) {
            logger.error("Insufficient command line arguments provided. Received {} arguments, expected at least 3.", args.length);
            logger.error("""
                    
                    Usage: java -jar syrix-backend.jar <certPassword> <certPath> <configPath> [--disable-activemq]
                    
                    Required arguments:
                      <certPassword>  - Password for the certificate file
                      <certPath>      - Path to certificate file (.p12 or .pfx)
                      <configPath>    - Path to configuration file (.yml or .yaml)
                      
                    Optional arguments:
                      --disable-activemq  - Disable ActiveMQ task queue integration
                    
                    Examples:
                      java -jar syrix-backend.jar myPassword /path/to/cert.p12 /path/to/config.yml
                      java -jar syrix-backend.jar myPassword /path/to/cert.p12 /path/to/config.yml --disable-activemq
                    """);
            throw new StartupWorkerConfigurationException(
                String.format("Invalid number of arguments provided: expected at least 3, got %d. Please provide certificate password, certificate path, and configuration file path.", args.length)
            );
        }

        // Process required arguments
        certPass = args[0];
        certPath = args[1];
        configPath = args[2];
        
        // Process optional flags
        for (int i = 3; i < args.length; i++) {
            String arg = args[i];
            switch (arg.toLowerCase()) {
                case "--disable-activemq":
                    activeMQEnabled = false;
                    logger.info("ActiveMQ integration disabled via command line flag");
                    break;
                default:
                    logger.warn("Unknown command line argument: {}", arg);
            }
        }
        
        // Set system property for Quarkus config to read
        System.setProperty("syrix.messaging.activemq.enabled", String.valueOf(activeMQEnabled));
        
        logProcessedArguments();
        initialized = true;
    }
    
    /**
     * Log processed arguments
     */
    private void logProcessedArguments() {
        logger.info("Command line arguments processed successfully:");
        logger.info("  - Certificate password: {}", certPass != null ? "***provided***" : "not provided");
        logger.info("  - Certificate path: {}", certPath);
        logger.info("  - Config path: {}", configPath);
        logger.info("  - ActiveMQ enabled: {}", activeMQEnabled);
    }
    
    // Getters
    public String getConfigPath() {
        return configPath;
    }
    
    public String getCertPath() {
        return certPath;
    }
    
    public String getCertPass() {
        return certPass;
    }
    
    public boolean isInitialized() {
        return initialized;
    }
    
    public boolean isActiveMQEnabled() {
        return activeMQEnabled;
    }
}
