package io.syrix.worker.messaging.model;

import java.time.Instant;

/**
 * Represents the result of a processed task
 */
public class TaskResult {
    private String taskId;
    private boolean success;
    private String message;
    private Instant completedAt;
    private String errorDetails;

    public TaskResult() {
        this.completedAt = Instant.now();
    }

    private TaskResult(String taskId, boolean success, String message, String errorDetails) {
        this.taskId = taskId;
        this.success = success;
        this.message = message;
        this.errorDetails = errorDetails;
        this.completedAt = Instant.now();
    }

    /**
     * Create a successful task result
     */
    public static TaskResult success(String taskId, String message) {
        return new TaskResult(taskId, true, message, null);
    }

    /**
     * Create a failed task result
     */
    public static TaskResult failure(String taskId, String message, String errorDetails) {
        return new TaskResult(taskId, false, message, errorDetails);
    }

    // Getters and setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Instant getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Instant completedAt) {
        this.completedAt = completedAt;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    @Override
    public String toString() {
        return "TaskResult{" +
                "taskId='" + taskId + '\'' +
                ", success=" + success +
                ", message='" + message + '\'' +
                ", completedAt=" + completedAt +
                ", errorDetails='" + errorDetails + '\'' +
                '}';
    }
}
