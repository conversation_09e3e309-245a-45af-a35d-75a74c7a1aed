package io.syrix.worker.orchestrator;

import io.syrix.messaging.TaskMessage;
import io.syrix.worker.messaging.model.TaskResult;
import io.syrix.worker.service.RetrieveTaskProcessor;
import io.syrix.worker.service.TaskStatusService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.util.concurrent.CompletionStage;

@ApplicationScoped
public class TaskOrchestrator {
	private final RetrieveTaskProcessor retrieveTaskProcessor;
	private final TaskStatusService taskStatusService;

	@Inject
	public TaskOrchestrator(RetrieveTaskProcessor retrieveTaskProcessor, 
							TaskStatusService taskStatusService) {
		this.retrieveTaskProcessor = retrieveTaskProcessor;
		this.taskStatusService = taskStatusService;
	}

	/**
	 * Process incoming task message and update task status to RECEIVED.
	 * 
	 * @param message The task message to process
	 * @return CompletionStage with task processing result
	 */
	public CompletionStage<TaskResult> processTask(TaskMessage message) {
		// Update task status to RECEIVED when task is received by worker
		taskStatusService.updateToReceived(message);

		return switch (message.getTaskType()) {
			case RETRIEVE_CONFIGURATION -> retrieveTaskProcessor.process(message);
//			case REMEDIATE_CONFIGURATION -> processRemediationTask(message);
//			case ROLLBACK_CHANGES -> processRollbackTask(message);
//			case ANALYZE_VULNERABILITIES -> processAnalysisTask(message);
//			case RETRIEVE_AND_REMEDIATE_CONFIGURATION -> processCombinedTask(message);
			default -> throw new RuntimeException("Unknown task type: " + message.getTaskType());
		};
	}


}
