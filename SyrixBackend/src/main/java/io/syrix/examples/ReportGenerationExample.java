package io.syrix.examples;

import io.syrix.reports.model.ReportConfig;
import io.syrix.reports.model.ReportSummary;
import io.syrix.reports.service.MarkdownProcessor;
import io.syrix.reports.service.ReportService;
import io.syrix.reports.service.ReportTemplateService;
import io.syrix.utils.baseline.BaselineGroup;
import io.syrix.utils.baseline.SecureBaselineService;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class ReportGenerationExample {
	public static void main(String[] args) {
		try {
			// Setup paths
			Path basePath = Paths.get(".");
			Path reportOutputPath = basePath.resolve("configurations");
			Path individualReportPath = reportOutputPath.resolve("IndividualReports");
			Path baselinePath = basePath.resolve("baselines");

			// Create required services
			MarkdownProcessor markdownProcessor = new MarkdownProcessor();
			ReportTemplateService templateService = new ReportTemplateService();
			SecureBaselineService baselineService = new SecureBaselineService();

			// Initialize report service
			ReportService reportService = new ReportService(
					individualReportPath,
					markdownProcessor,
					templateService
			);

			// Parse baseline markdown files using SecureBaselineService
			List<String> productNames = Arrays.asList("exo");
			Map<String, List<BaselineGroup>> secureBaselines =
					baselineService.importSecureBaseline(productNames);

			// Create report configuration
			ReportConfig config = ReportConfig.builder()
					.baselineName("EXO")
					.fullName("Exchange Online")
					.outProviderFileName("exo-config")
					.outRegoFileName("TestResults")
					.darkMode(true)
					.secureBaselines(secureBaselines)
					.build();

			// Ensure output directories exist
			Files.createDirectories(reportOutputPath);
			Files.createDirectories(individualReportPath);

			// Generate report
			ReportSummary summary = reportService.generateReport(config);

			// Print summary
			System.out.println("\nReport Generation Summary:");
			System.out.println("-------------------------");
			System.out.println("Passes: " + summary.getPasses());
			System.out.println("Failures: " + summary.getFailures());
			System.out.println("Warnings: " + summary.getWarnings());
			System.out.println("Manual Checks: " + summary.getManual());
			System.out.println("Errors: " + summary.getErrors());
			System.out.println("Omitted: " + summary.getOmits());
			System.out.println("Generated at: " + summary.getDate());

			System.out.println("\nReport files generated at: " + reportOutputPath.toAbsolutePath());

		} catch (Exception e) {
			System.err.println("Failed to generate report: " + e.getMessage());
			e.printStackTrace();
		}
	}
}