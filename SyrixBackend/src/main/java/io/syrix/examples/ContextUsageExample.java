package io.syrix.examples;

import io.syrix.main.Context;
import io.syrix.main.Configuration;
import io.syrix.protocols.model.MSEnvironment;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Example service demonstrating Context injection and usage
 * Shows how to use Context in any CDI managed component
 */
@ApplicationScoped
public class ContextUsageExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ContextUsageExample.class);
    
    @Inject
    Context context; // Context automatically injected and initialized
    
    /**
     * Example method showing how to use injected Context
     */
    public void demonstrateContextUsage() {
        logger.info("=== Context Usage Example ===");
        
        // Access configuration
        Configuration config = context.getConfig();
        logger.info("Client ID: {}", config.credentials().clientId());
        logger.info("Environment: {}", config.environment());
        logger.info("Output Path: {}", config.outputPath());
        logger.info("Storage Path: {}", config.storagePath());
        
        // Access certificate information
        logger.info("Certificate Path: {}", context.getCertPath());
        logger.info("Certificate Password: {}", context.getCertPass() != null ? "***" : "not set");
        
        // Access derived properties
        MSEnvironment environment = context.getEnvironment();
        logger.info("MS Environment: {}", environment);
        
        logger.info("=== End Context Usage Example ===");
    }
    
    /**
     * Example of conditional logic based on environment
     */
    public void environmentSpecificLogic() {
        MSEnvironment env = context.getEnvironment();
        
        switch (env) {
            case COMMERCIAL:
                logger.info("Running in Commercial Microsoft 365 environment");
                // Commercial-specific logic
                break;
            case GCC:
                logger.info("Running in Government Community Cloud environment");
                // GCC-specific logic
                break;
            case DOD:
                logger.info("Running in Department of Defense environment");
                // DoD-specific logic
                break;
            default:
                logger.warn("Unknown environment: {}", env);
        }
    }
}
