package io.syrix.examples;

import io.syrix.common.utils.FileUtils;
import io.syrix.reports.model.ReportConfig;
import io.syrix.reports.model.ReportSummary;
import io.syrix.reports.service.MarkdownProcessor;
import io.syrix.reports.service.ReportService;
import io.syrix.reports.service.ReportTemplateService;
import io.syrix.utils.baseline.BaselineGroup;
import io.syrix.utils.baseline.Control;

import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReportServiceExample {
	public static void main(String[] args) {
		// Initialize dependencies
		Path baseOutputPath = Path.of("./");
		Path reportOutputPath = baseOutputPath.resolve("reports");
		Path individualReportPath = reportOutputPath.resolve("configurations");


		MarkdownProcessor markdownProcessor = new MarkdownProcessor();
		ReportTemplateService templateService = new ReportTemplateService();

		// Create directories if they don't exist
		FileUtils.createDirectory(reportOutputPath);
		FileUtils.createDirectory(individualReportPath);

		// Initialize report service
		ReportService reportService = new ReportService(
				individualReportPath,
				markdownProcessor,
				templateService
		);

		try {
			// Create baseline groups with controls
			List<Control> controls = Arrays.asList(
					Control.builder()
							.id("MS.AAD.1.1v1")
							.value("Legacy Authentication should be blocked.")
							.baselineName("AAD")
							.build(),
					Control.builder()
							.id("MS.AAD.1.2v1")
							.value("Multi-factor authentication should be enabled.")
							.baselineName("AAD")
							.build()
			);

			BaselineGroup group = BaselineGroup.builder()
					.groupNumber("1")
					.groupName("Authentication")
					.baselineName("AAD")
					.controls(controls)
					.build();

			Map<String, List<BaselineGroup>> secureBaselines = new HashMap<>();
			secureBaselines.put("EXO", Collections.singletonList(group));

			// Configure report generation
			ReportConfig config = ReportConfig.builder()
					.baselineName("EXO")
					.fullName("Exchange Online")
					.outProviderFileName("provider-settings")
					.outRegoFileName("test-results")
					.darkMode(false)
					.secureBaselines(secureBaselines)
					.build();

			// Generate report
			ReportSummary summary = reportService.generateReport(config);

			// Process results
			System.out.println("Report Generation Complete");
			System.out.println("-------------------------");
			System.out.println("Passes: " + summary.getPasses());
			System.out.println("Failures: " + summary.getFailures());
			System.out.println("Warnings: " + summary.getWarnings());
			System.out.println("Manual Checks: " + summary.getManual());
			System.out.println("Errors: " + summary.getErrors());
			System.out.println("Omitted: " + summary.getOmits());
			System.out.println("Report Date: " + summary.getDate());

			// Check output files
			Path reportFile = reportOutputPath.resolve(config.baselineName() + "Report.html");
			Path jsonFile = reportOutputPath.resolve(config.baselineName() + "Report.json");

			if (FileUtils.fileExists(reportFile)) {
				System.out.println("\nHTML Report generated: " + reportFile);
			}
			if (FileUtils.fileExists(jsonFile)) {
				System.out.println("JSON Report generated: " + jsonFile);
			}

		} catch (Exception e) {
			System.err.println("Error generating report: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public static void generateMultipleReports() {
		// Example of generating reports for multiple baselines
		ReportService reportService = setupReportService();

		List<String> baselines = Arrays.asList("AAD", "EXO", "Defender", "SharePoint");

		for (String baseline : baselines) {
			try {
				ReportConfig config = buildConfigForBaseline(baseline);
				ReportSummary summary = reportService.generateReport(config);
				printSummary(baseline, summary);
			} catch (Exception e) {
				System.err.println("Error generating report for " + baseline + ": " + e.getMessage());
			}
		}
	}

	private static ReportService setupReportService() {
		MarkdownProcessor markdownProcessor = new MarkdownProcessor();
		ReportTemplateService templateService = new ReportTemplateService();

		Path baseOutputPath = Path.of("/path/to/output");
		Path reportOutputPath = baseOutputPath.resolve("reports");
		Path individualReportPath = reportOutputPath.resolve("individual");

		FileUtils.createDirectory(reportOutputPath);
		FileUtils.createDirectory(individualReportPath);

		return new ReportService(
				individualReportPath,
				markdownProcessor,
				templateService
		);
	}

	private static ReportConfig buildConfigForBaseline(String baseline) {
		String fullName = getFullName(baseline);
		Map<String, List<BaselineGroup>> secureBaselines = loadBaselineGroups(baseline);

		return ReportConfig.builder()
				.baselineName(baseline)
				.fullName(fullName)
				.outProviderFileName("provider-settings")
				.outRegoFileName("test-results")
				.darkMode(false)
				.secureBaselines(secureBaselines)
				.build();
	}

	private static String getFullName(String baseline) {
		Map<String, String> baselineNames = Map.of(
				"AAD", "Azure Active Directory",
				"EXO", "Exchange Online",
				"Defender", "Microsoft 365 Defender",
				"SharePoint", "SharePoint Online"
		);
		return baselineNames.getOrDefault(baseline, baseline);
	}

	private static Map<String, List<BaselineGroup>> loadBaselineGroups(String baseline) {
		// This would typically load from files or a database
		List<Control> controls = Arrays.asList(
				Control.builder()
						.id("MS." + baseline + ".1.1v1")
						.value("Example control 1")
						.baselineName(baseline)
						.build(),
				Control.builder()
						.id("MS." + baseline + ".1.2v1")
						.value("Example control 2")
						.baselineName(baseline)
						.build()
		);

		BaselineGroup group = BaselineGroup.builder()
				.groupNumber("1")
				.groupName("Security")
				.baselineName(baseline)
				.controls(controls)
				.build();

		return Collections.singletonMap(baseline, Collections.singletonList(group));
	}

	private static void printSummary(String baseline, ReportSummary summary) {
		System.out.printf("\nSummary for %s:\n", baseline);
		System.out.printf("Passes: %d\n", summary.getPasses());
		System.out.printf("Failures: %d\n", summary.getFailures());
		System.out.printf("Warnings: %d\n", summary.getWarnings());
	}
}