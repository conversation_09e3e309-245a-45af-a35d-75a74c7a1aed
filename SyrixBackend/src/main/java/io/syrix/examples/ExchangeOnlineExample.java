package io.syrix.examples;

import io.syrix.protocols.client.PowerShellClient;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import io.syrix.protocols.model.MSEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static io.syrix.products.microsoft.exo.ExoConstants.GET_ORGANIZATION_CONFIG;

public class ExchangeOnlineExample {
	private static final Logger logger = LoggerFactory.getLogger(ExchangeOnlineExample.class);
	private static final String OUTPUT_DIR = "exchange_output";

	private static void writeToFile(String filename, String content) {
		File outputDir = new File(OUTPUT_DIR);
		if (!outputDir.exists()) {
			outputDir.mkdirs();
		}

		try (FileWriter writer = new FileWriter(new File(outputDir, filename))) {
			writer.write(content);
			logger.info("Successfully wrote output to file: {}", filename);
		} catch (IOException e) {
			logger.error("Failed to write to file: {}", filename, e);
		}
	}

	public static void main(String[] args) {
		// Configuration parameters - replace with your values
		String appId = "e3fc1b90-ca49-42f2-ae5c-d51fe9ae6133";  // Your Azure AD App Registration ID
		String tenantId = "472fa13e-da60-4df4-a2fd-c5b288497431"; // Your Azure AD Tenant ID
		String certPath = Path.of(args[1]).toString();
		String certPassword = args[0];         // Certificate password

		// Create the Exchange Online client using adminapi endpoint
		try (PowerShellClient client = PowerShellClient.builder()
				.withAppId(appId)
				.withTenantId(tenantId)
				.withCertificatePath(certPath)
				.withCertificatePassword(certPassword.toCharArray())
				.withEnvironment(MSEnvironment.COMMERCIAL)
				.withEndpointPath("/adminapi/beta")  // Use adminapi endpoint
				.withRequestTimeout(Duration.ofMinutes(5))
				.withConnectTimeout(Duration.ofSeconds(30))
				.withMaxRetries(3)
				.build()) {

			// Example 1: Get DKIM signing config
			PowerShellClient.CommandRequest dkimRequest = new PowerShellClient.CommandRequest("Get-DkimSigningConfig", null);
			CompletableFuture<Void> getDkimSigningConfig = client.executeCmdletCommand(dkimRequest)
					.thenAccept(response -> {
						String filename = String.format("dkim-config-%s.json", 
							Instant.now().toEpochMilli());
						String content = "DkimSigningConfig:\n" + response.toPrettyString();
						writeToFile(filename, content);
					});

			// Example 2: Get mailboxes with parameters
			Map<String, Object> mailboxParams = new HashMap<>();
			mailboxParams.put("ResultSize", "unlimited");
			PowerShellClient.CommandRequest mailboxRequest = new PowerShellClient.CommandRequest("Get-Mailbox", mailboxParams);
			CompletableFuture<Void> getMailboxes = client.executeCmdletCommand(mailboxRequest)
					.thenAccept(response -> {
						String filename = String.format("mailboxes-%s.json", 
							Instant.now().toEpochMilli());
						String content = "Mailboxes:\n" + response.toPrettyString();
						writeToFile(filename, content);
					});

			// Example 3: Get transport rules
			PowerShellClient.CommandRequest transportRequest = new PowerShellClient.CommandRequest("Get-TransportRule", null);
			CompletableFuture<Void> getTransportRules = client.executeCmdletCommand(transportRequest)
					.thenAccept(response -> {
						String filename = String.format("transport-rules-%s.json",
							Instant.now().toEpochMilli());
						String content = "Transport Rules:\n" + response.toPrettyString();
						writeToFile(filename, content);
					});

			// Example 4: Get organization config
			PowerShellClient.CommandRequest orgConfigRequest = new PowerShellClient.CommandRequest(GET_ORGANIZATION_CONFIG, null);
			CompletableFuture<Void> getOrgConfig = client.executeCmdletCommand(orgConfigRequest)
					.thenAccept(response -> {
						String filename = String.format("org-config-%s.json",
							Instant.now().toEpochMilli());
						String content = "Organization Configuration:\n" + response.toPrettyString();
						writeToFile(filename, content);
					});

			// Example 5: Get remote domain
			PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest("Get-RemoteDomain", null);
			CompletableFuture<Void> getRemoteDomain = client.executeCmdletCommand(remoteDomainRequest)
					.thenAccept(response -> {
						String filename = String.format("remote-domain-%s.json",
							Instant.now().toEpochMilli());
						String content = "Remote Domain Configuration:\n" + response.toPrettyString();
						writeToFile(filename, content);
					});

			// Wait for all operations to complete and handle errors
			CompletableFuture.allOf(
				getDkimSigningConfig,
				getMailboxes,
				getTransportRules,
				getOrgConfig,
				getRemoteDomain
			).exceptionally(throwable -> {
				logger.error("One or more operations failed", throwable);
				return null;
			}).join();

			logger.info("All export operations completed successfully");

			// Wait for all commands to complete
			CompletableFuture.allOf(getDkimSigningConfig, getMailboxes, getTransportRules, getOrgConfig)
					.exceptionally(error -> {
						System.err.println("Error executing Exchange commands:");
						error.printStackTrace();
						return null;
					})
					.join();

		} catch (Exception e) {
			System.err.println("Error in Exchange Online connection:");
			e.printStackTrace();
		}
	}
}