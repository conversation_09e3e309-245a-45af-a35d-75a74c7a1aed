package io.syrix.examples;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.exo.audit.ExchangeAuditDisabledAuditor;
import io.syrix.products.microsoft.exo.remediation.ExchangeAuditDisabledRemediator;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.model.MSEnvironment;

import java.time.Duration;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

/**
 * Example demonstrating how to use ExchangeAuditDisabledAuditor and ExchangeAuditDisabledRemediator.
 *
 * This class shows how to:
 * 1. Create a PowerShell client for Exchange Online
 * 2. Audit the AuditDisabled setting
 * 3. Remediate the AuditDisabled setting if needed
 */
public class ExchangeAuditDisabledExample {

    public static void main(String[] args) {
        // This example assumes you've set up proper authentication
        // Replace these values with your actual Azure AD application and certificate details
        String appId = "your-app-id";
        String tenantId = "your-tenant-id";
        String certPath = "/path/to/your/certificate.pfx";
        char[] certPassword = "your-cert-password".toCharArray();
        String domain = "yourdomain.com";

        // Create a PowerShell client for Exchange Online
        PowerShellClient exchangeClient = PowerShellClient.builder()
                .withAppId(appId)
                .withTenantId(tenantId)
                .withCertificatePath(certPath)
                .withCertificatePassword(certPassword)
                .withDomain(domain)
                .withEnvironment(MSEnvironment.COMMERCIAL)
                .withEndpointPath("/adminapi/beta")
                .withConnectTimeout(Duration.ofSeconds(30))
                .withMaxRetries(3)
                .build();

        try {
            // Create the auditor
            ExchangeAuditDisabledAuditor auditor = new ExchangeAuditDisabledAuditor(exchangeClient);

            // Audit the AuditDisabled setting
            CompletableFuture<ExchangeAuditDisabledAuditor.AuditResult> auditFuture = auditor.auditAuditDisabledSetting();
            ExchangeAuditDisabledAuditor.AuditResult auditResult = auditFuture.join();

            // Display the audit result
            System.out.println("\nAudit Result:");
            System.out.println("Is Compliant: " + auditResult.isCompliant());
            System.out.println("Current AuditDisabled Value: " + auditResult.getCurrentValue());
            System.out.println("Expected AuditDisabled Value: " + auditResult.getExpectedValue());

            if (!auditResult.isCompliant()) {
                System.out.println("Failure Reason: " + auditResult.getFailureReason());

                // Ask if remediation should be performed
                Scanner scanner = new Scanner(System.in);
                System.out.print("\nDo you want to remediate this issue? (Y/N): ");
                String choice = scanner.nextLine();

                if (choice.equalsIgnoreCase("Y")) {
                    // Create the remediator
                    ExchangeAuditDisabledRemediator remediator = new ExchangeAuditDisabledRemediator(exchangeClient);

                    // Perform remediation
                    CompletableFuture<JsonNode> remediationFuture = remediator.remediate();
                    JsonNode remediationResult = remediationFuture.join();

                    // Display the remediation result
                    System.out.println("\nRemediation Result:");
                    System.out.println("Status: " + remediationResult.get("status").asText());
                    System.out.println("Message: " + remediationResult.get("message").asText());
                } else {
                    System.out.println("Remediation skipped by user.");
                }
            }
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Close the PowerShell client
            exchangeClient.close();
        }
    }
}