package io.syrix.main;

import io.syrix.protocols.model.MSEnvironment;

/**
 * Context class for Syrix application
 * Contains configuration, certificate path and password
 * Managed by CDI - can be injected into other components
 */
public class Context {
	private final Configuration config;
	private final String certPath;
	private final String certPass;

	/**
	 * Creates new Context instance
	 * @param config Configuration object loaded from YAML
	 * @param certPath Path to certificate file
	 * @param certPass Certificate password
	 */
	public Context(Configuration config, String certPath, String certPass) {
		this.config = config;
		this.certPath = certPath;
		this.certPass = certPass;
	}

	public Configuration getConfig() {
		return config;
	}

	public String getCertPath() {
		return certPath;
	}

	public String getCertPass() {
		return certPass;
	}

	public MSEnvironment getEnvironment() {
		return MSEnvironment.valueOf(config.environment());
	}
}
