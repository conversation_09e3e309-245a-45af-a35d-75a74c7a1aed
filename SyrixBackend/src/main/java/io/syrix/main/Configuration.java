package io.syrix.main;

/**
 * Configuration class for YAML mapping
 */
public record Configuration(Credentials credentials, String environment, String outputPath, String metricsPath, String storagePath) {
	public Configuration {
		environment = environment == null ? "COMMERCIAL" : environment;
		outputPath = outputPath == null ? "outputs/configurations" : outputPath;
		metricsPath = metricsPath == null ? "outputs/metrics" : metricsPath;
		storagePath = storagePath == null ? "outputs/localStorage" : storagePath;
	}
}