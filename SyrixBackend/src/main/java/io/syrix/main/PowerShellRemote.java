package io.syrix.main;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import org.json.JSONObject;

import static io.syrix.products.microsoft.exo.ExoConstants.GET_ORGANIZATION_CONFIG;

public class PowerShellRemote {


    private static final String API_ENDPOINT = "https://outlook.office365.com/adminapi/beta/{}/InvokeCommand";
    private static final String ANCHOR_MAILBOX = "UPN:<EMAIL>";
    private static final String CLIENT_APPLICATION = "ExoManagementModule";
    private static final String CLIENT_MODULE_VERSION = "3.7.0";
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.2200";
    private static final String CONNECTION_ID = "a5facccd-1610-46ff-8c88-e03e96d4195c"; //constant per session

    public static String executeCommand(String cmdletName) throws IOException {
        URL url = new URL(API_ENDPOINT);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Prefer", "odata.maxpagesize=1000");
        connection.setRequestProperty("X-AnchorMailbox", ANCHOR_MAILBOX);
        connection.setRequestProperty("X-ResponseFormat", "clixml");
        connection.setRequestProperty("X-CmdletName", cmdletName);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("connection-id", CONNECTION_ID);
        connection.setRequestProperty("client-request-id", UUID.randomUUID().toString()); //Generate unique IDs per request
        connection.setRequestProperty("X-ClientApplication", CLIENT_APPLICATION);
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Accept-Language", "en-US");
        connection.setRequestProperty("Accept-Charset", "UTF-8");
        connection.setRequestProperty("WarningAction", "");
        connection.setRequestProperty("Accept-Encoding", "gzip");
        connection.setRequestProperty("X-SerializationLevel", "Partial");
        connection.setRequestProperty("X-ClientModuleVersion", CLIENT_MODULE_VERSION);
        connection.setRequestProperty("User-Agent", USER_AGENT);
        connection.setRequestProperty("Host", "outlook.office365.com");
        connection.setDoOutput(true);
        connection.setRequestProperty("Expect","100-continue");

        String requestBody = createRequestBody(cmdletName);

        byte[] postData = requestBody.getBytes(StandardCharsets.UTF_8);
        connection.setRequestProperty("Content-Length", Integer.toString(postData.length));

        try (DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream())) {
            outputStream.write(postData);
        }

        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
        }

        if(responseCode != 200)
            throw new IOException("Request failed with code: " + responseCode + "\n Response body: "+ response.toString());

        return response.toString();
    }

    private static String createRequestBody(String cmdletName) {
        JSONObject cmdletInput = new JSONObject();
        cmdletInput.put("CmdletName", cmdletName);
        JSONObject parameters = new JSONObject();
        parameters.put("ErrorAction", "Stop");
        cmdletInput.put("Parameters",parameters);
        JSONObject requestBody = new JSONObject();
        requestBody.put("CmdletInput", cmdletInput);

        return requestBody.toString();
    }


    public static void main(String[] args) {
        try {
            String organizationConfig = executeCommand(GET_ORGANIZATION_CONFIG);
            System.out.println("Get-OrganizationConfig response: \n" + organizationConfig);

            String remoteDomain = executeCommand("Get-RemoteDomain");
            System.out.println("Get-RemoteDomain response: \n" + remoteDomain);

            String acceptedDomain = executeCommand("Get-AcceptedDomain");
            System.out.println("Get-AcceptedDomain response: \n" + acceptedDomain);

            String dkimSigningConfig = executeCommand("Get-DkimSigningConfig");
            System.out.println("Get-DkimSigningConfig response: \n" + dkimSigningConfig);

            String transportConfig = executeCommand("Get-TransportConfig");
            System.out.println("Get-TransportConfig response: \n" + transportConfig);

            String sharingPolicy = executeCommand("Get-SharingPolicy");
            System.out.println("Get-SharingPolicy response: \n" + sharingPolicy);

            String transportRule = executeCommand("Get-TransportRule");
            System.out.println("Get-TransportRule response: \n" + transportRule);

            String hostedConnectionFilterPolicy = executeCommand("Get-HostedConnectionFilterPolicy");
            System.out.println("Get-HostedConnectionFilterPolicy response: \n" + hostedConnectionFilterPolicy);

            String organizationConfig2 = executeCommand(GET_ORGANIZATION_CONFIG);
            System.out.println("Get-OrganizationConfig second response: \n" + organizationConfig2);

        } catch (IOException e) {
            System.err.println("Error during execution: " + e.getMessage());
            e.printStackTrace();
        }
    }
}