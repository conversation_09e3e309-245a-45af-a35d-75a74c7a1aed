package io.syrix.main;

import io.syrix.common.logging.LoggingInitializer;
import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.quarkus.runtime.Quarkus;
import io.syrix.common.dns.DnsService;
import io.syrix.common.exceptions.SyrixException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.common.model.TenantDetails;
import io.syrix.common.utils.FileUtils;
import io.syrix.products.microsoft.ConfigurationService;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.common.MSUtils;
import io.syrix.products.microsoft.defender.DefenderConfigurationService;
import io.syrix.products.microsoft.entra.service.EntraConfigurationService;
import io.syrix.products.microsoft.entra.service.EntraRecommendationService;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import io.syrix.products.microsoft.sharepoint.SharePointConfigurationService;
import io.syrix.products.microsoft.teams.TeamsConfigurationService;
import io.syrix.products.microsoft.forms.FormsConfigurationService;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.PowerShellTeamsClient;


import io.syrix.protocols.model.MSEnvironment;
import io.syrix.reports.Report;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.time.Duration;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.products.microsoft.defender.DefenderConstants.FILTER_PARAM;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

public class ConfigurationRetrieval {
	private static final Logger logger = LoggerFactory.getLogger(ConfigurationRetrieval.class);
	private static final ObjectMapper jsonMapper = new ObjectMapper();
	private static final ObjectMapper yamlMapper = new YAMLMapper();

	static {
		// Initialize logging system BEFORE any other operations
		LoggingInitializer.initializeLogging();
	}

	public static void main(String[] args) {
		// CRITICAL: Set these properties BEFORE any initialization
//		System.setProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager");
//		System.setProperty("slf4j.provider", "org.slf4j.impl.JBossSlf4jServiceProvider");
//		System.setProperty("logback.configurationFile", "");
		
		Quarkus.run(args);
		try {
			// Load configuration from YAML file
			if (args.length < 2) {
				logger.error("""
                        Specify as arguments:
                        args[0] - password for Certificate
                        args[1] - path to Certificate
                        args[2] - path to yml config file""");
				System.exit(1);
			}

			String certPath = args[1];
			String certPass = args[0];

			String configPath = args.length > 2 ? args[2] : "./config.yml";
			Configuration config = yamlMapper.readValue(
					Paths.get(configPath).toFile(),
					Configuration.class
			);

			// Validate required fields
			if (config.credentials() == null ||
					config.credentials().clientId() == null ||
					config.credentials().clientSecret() == null) {
				throw new IllegalArgumentException("Missing required credentials in config file");
			}

			MSEnvironment environment = MSEnvironment.valueOf(config.environment());

			jsonMapper.registerModule(new JavaTimeModule());
			// Create metrics collector for all services
			MetricsCollector metrics = new MetricsCollector();

			// Initialize Graph client
			logger.info("Initializing Microsoft Graph client for environment: {}", environment);
			MicrosoftGraphClient graphClient = MicrosoftGraphClient.builder()
					.withClientId(config.credentials().clientId())
					.withClientSecret(config.credentials().clientSecret())
					.withRefreshToken(config.credentials().refreshToken())
					.withEnvironment(environment)
					.withRequestTimeout(Duration.ofMinutes(5))
					.withMaxRetries(3)
					.withAppId(config.credentials().clientId())
					.withCertPath(certPath)
					.withCertPassword(certPass.toCharArray())
					.build();


			TokenCredential credential = new ClientSecretCredentialBuilder()
					.clientId(config.credentials().clientId())
					.clientSecret(config.credentials().clientSecret())
					.tenantId(graphClient.getTenantId())
					.build();

			String domain = graphClient.getDomain().join();
			PowerShellClient powerShellClient = PowerShellClient.builder()
					.withAppId(config.credentials().clientId())
					.withTenantId(graphClient.getTenantId())
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withEndpointPath("/adminapi/beta")  // Use adminapi endpoint
					.withRequestTimeout(Duration.ofMinutes(5))
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.withDomain(domain)
					.build();

			Map<String, String> queryParams = Map.of(FILTER_PARAM,
					String.format("appId eq '%s'",config.credentials().clientId()),
					PARAM_SELECT, ID_FIELD);
			CompletableFuture<JsonNode> servicePrincipal = graphClient.getAzureServicePrincipals(queryParams, null);
			graphClient.checkAndAssignServicePrincipalToAdminGroup(MicrosoftGraphClient.getServicePrincipalID(servicePrincipal));

			String site = graphClient.getSite();
			String adminDomain = site.replace(".sharepoint.com", "-admin.sharepoint.com").replace("https://","");

			PowerShellSharepointClient powerShellSharepointClient = PowerShellSharepointClient.builder()
					.withAppId(config.credentials().clientId())
					.withDomain(domain)
					.withAdminDomain(adminDomain)
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.build();

			PowerShellTeamsClient powerShellTeamsClient = PowerShellTeamsClient.builder()
					.withAppId(config.credentials().clientId())
					.withTenantId(graphClient.getTenantId())
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.build();

			DnsService dnsService = new DnsService.Builder()
					.withAzureCredential(credential)
					.withDnsServers("8.8.4.4", "1.0.0.1")
					.withObjectMapper(jsonMapper)
					.build();


			TenantDetails tenantDetails = MSUtils.getTenantDetails(graphClient);
			ObjectMapper spJsonMapper = new ObjectMapper();
			spJsonMapper = spJsonMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);

			EntraRecommendationService recommendationService = new EntraRecommendationService(graphClient);
//			List<Recommendation> recommendations = recommendationService.listRecommendations();
//			RecommendationReportService recommendationReportService = new RecommendationReportService();
//			recommendationReportService.generateReport(recommendations, Paths.get("outputs/recommendations.html"));
			// Initialize all configuration services
			Map<ConfigurationServiceType, ConfigurationService> services = new HashMap<>();
//			services.put(ConfigurationServiceType.ENTRA, new EntraConfigurationService(graphClient, jsonMapper, metrics));
			services.put(ConfigurationServiceType.TEAMS, new TeamsConfigurationService(graphClient, powerShellTeamsClient, jsonMapper, metrics));
//			services.put(ConfigurationServiceType.SHAREPOINT, new SharePointConfigurationService(graphClient, powerShellSharepointClient, spJsonMapper, metrics));
//			services.put(ConfigurationServiceType.DEFENDER, new DefenderConfigurationService(graphClient, powerShellClient, jsonMapper, metrics));
//			services.put(ConfigurationServiceType.EXCHANGE_ONLINE, new ExchangeOnlineConfigurationService(graphClient, powerShellClient, jsonMapper, metrics, dnsService));
//			services.put(ConfigurationServiceType.FORMS, new FormsConfigurationService(graphClient, jsonMapper, metrics));
//			services.put(ConfigurationServiceType.POWER_PLATFORM, new PowerPlatformConfigurationService(graphClient, powerPlatformClient, jsonMapper, metrics));

			// Create output directory if it doesn't exist
			Path outputDir = Paths.get(config.outputPath());
			Files.createDirectories(outputDir);

			// Map to store results
			Map<String, ConfigurationResult> results = new HashMap<>();

			// Start configuration export for each service
			Instant startTime = Instant.now();
			logger.info("Starting configuration export for all services");

			List<CompletableFuture<Void>> futures = services.entrySet().stream()
					.map(entry -> CompletableFuture
							.supplyAsync(() -> entry.getValue().exportConfiguration())
							.thenAccept(result -> results.put(entry.getKey().getResultName(), result))
							.exceptionally(e -> {
								logger.error("Error exporting {} ID configuration", entry.getKey().getResultName(), e);
								return null;
							})).toList();


			// Wait for all exports to complete
			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

			// Calculate total duration
			Duration totalDuration = Duration.between(startTime, Instant.now());
			logger.info("All configuration exports completed in {} seconds", totalDuration.getSeconds());


			Path reportFile = writeConfigJson(results, tenantDetails, jsonMapper, outputDir);
			Report.generate(outputDir, reportFile, services.keySet(), jsonMapper, false);

			// Save metrics
			Path metricsDir = Paths.get(config.metricsPath());
			Files.createDirectories(metricsDir);
			Path metricsPath = metricsDir.resolve("metrics.json");
			jsonMapper.writerWithDefaultPrettyPrinter().writeValue(metricsPath.toFile(), metrics.getSnapshot());
			logger.info("Saved metrics to {}", metricsPath);
			Quarkus.asyncExit(0);
		} catch (Exception e) {
			logger.error("Error during configuration retrieval", e);
			Quarkus.asyncExit(1);
		}

	}


	private static Path writeConfigJson(Map<String, ConfigurationResult> results,
										  TenantDetails tenantDetails, ObjectMapper jsonMapper,
										  Path outputDir) {
		ObjectNode finalJson = jsonMapper.createObjectNode();

		// Add metadata fields
		finalJson.put("baseline_version", "1.4.0");
		finalJson.put("Syrix_version", "0.1");

		// Add timestamps
		Instant now = Instant.now();
		DateTimeFormatter dateFormatter = DateTimeFormatter
				.ofPattern("MM/dd/yyyy HH:mm:ss 'UTC'")
				.withZone(ZoneOffset.UTC);
		finalJson.put("date", dateFormatter.format(now));

		// Format UTC/Zulu timestamp
		DateTimeFormatter utcFormatter = DateTimeFormatter
				.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
				.withZone(ZoneOffset.UTC);
		finalJson.put("timestamp_zulu", utcFormatter.format(now));

		// Add UUID
		String uuid = UUID.randomUUID().toString();
		finalJson.put("report_uuid", uuid);

		// Add tenant details
		ArrayNode tmpTenantDetails = ConfigurationRetrieval.jsonMapper.createArrayNode();
		ObjectNode tenantInfo = ConfigurationRetrieval.jsonMapper.createObjectNode();
		tenantInfo.put("TenantId", tenantDetails.tenantId());
		tenantInfo.put("DisplayName", tenantDetails.displayName());
		tenantInfo.put("DomainName", tenantDetails.domainName());

		ObjectNode aadTenantInfo = ConfigurationRetrieval.jsonMapper.createObjectNode();
		aadTenantInfo.put("DisplayName", tenantDetails.displayName());
		aadTenantInfo.put("TenantId", tenantDetails.tenantId());
		aadTenantInfo.put("DomainName", tenantDetails.domainName());
		aadTenantInfo.setAll(tenantDetails.additionalData());

		tenantInfo.set("AADTenantInfo", aadTenantInfo);
		tmpTenantDetails.add(tenantInfo);
		finalJson.set("tenant_details", tmpTenantDetails);


		// Add empty scuba config
		finalJson.set("Syrix_config", ConfigurationRetrieval.jsonMapper.createObjectNode());

		String filename = String.format("config-%s.json", uuid);
		Path filePath = outputDir.resolve(filename);
		// Add all service configurations
		try {
			for (Map.Entry<String, ConfigurationResult> entry : results.entrySet()) {
				if (entry.getValue() != null) {
					jsonMapper.writerWithDefaultPrettyPrinter()
							.writeValue(filePath.toFile(), entry.getValue().getData());
				}
				logger.info("Saved {} configuration to {}", entry.getKey(), filename);
			}
		} catch (Exception excp) {
			throw new SyrixException("Failed to write configuration JSON", excp);
		}

		// Add all service configurations to the root of the JSON
		for (Map.Entry<String, ConfigurationResult> entry : results.entrySet()) {
			if (entry.getValue() != null) {
				JsonNode dataNode = entry.getValue().getData();
				if (dataNode != null) {
					dataNode.fieldNames().forEachRemaining(fieldName ->
							finalJson.set(fieldName, dataNode.get(fieldName))
					);
					logger.info("Added {} configuration data to finalJson", entry.getKey());
				}
			}
		}
		FileUtils.writeJsonFile(finalJson, filePath);
		logger.info("Saved all configurations to {}", filename);
		return filePath;
	}
}