package io.syrix.main;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import io.quarkus.runtime.Quarkus;
import io.syrix.common.storage.Storage;
import io.syrix.common.storage.StorageFactory;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.datamodel.task.remediation.exchange.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationTask;
import io.syrix.datamodel.task.remediation.sharepoint.SharingCapabilityEntity;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationTask;
import io.syrix.domain.*;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.datamodel.task.remediation.teams.TeamsBroadcastRecordingStrategy;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationStrategy;
import io.syrix.worker.service.OpaReportService;
import io.syrix.worker.service.ConfigurationRetrievalService;
import io.syrix.service.RemediationService;
import io.syrix.service.RemediatiorRollbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

public class RemediationMain {
	private static final Logger logger = LoggerFactory.getLogger(RemediationMain.class);
	private static final ObjectMapper yamlMapper = new YAMLMapper();

	private static final List<ConfigurationServiceType> allServices = List.of(
//			ConfigurationServiceType.ENTRA,
//			ConfigurationServiceType.TEAMS,
			ConfigurationServiceType.SHAREPOINT
//			ConfigurationServiceType.DEFENDER,
//			ConfigurationServiceType.EXCHANGE_ONLINE
//			ConfigurationServiceType.POWER_PLATFORM
	);

	private static final List<String> allSpPolicy = List.of(
			"MS.SHAREPOINT.1.1v1",
			"MS.SHAREPOINT.1.2v1",
			"MS.SHAREPOINT.1.3v1",
			"MS.SHAREPOINT.2.1v1",
			"MS.SHAREPOINT.2.2v1",
			"MS.SHAREPOINT.3.1v1",
			"MS.SHAREPOINT.3.2v1",
			"MS.SHAREPOINT.3.3v1"
	);

	private static final List<String> allTeamsPolicy = List.of(
			"MS.TEAMS.1.1v1",
			"MS.TEAMS.1.2v1",
			"MS.TEAMS.1.3v1",
			"MS.TEAMS.1.4v1",
			"MS.TEAMS.1.5v1",
			"MS.TEAMS.1.6v1",
			"MS.TEAMS.1.7v1",
			"MS.TEAMS.2.1v1",
			"MS.TEAMS.2.2v1",
			"MS.TEAMS.2.3v1",
			"MS.TEAMS.3.1v1",
			"MS.TEAMS.4.1v1",
			"MS.TEAMS.5.1v1",
			"MS.TEAMS.5.2v1",
			"MS.TEAMS.5.3v1"
	);

	private static final List<String> allAllExoPolicy = List.of(
			"MS.EXO.1.1v1", //checked
//			"MS.EXO.2.2v2" //not checked. hard logic :)
//			"MS.EXO.3.1v1", //not implemented - Requires integration with DNS services
//			"MS.EXO.4.1v1", //not implemented - Requires integration with DNS services
//			"MS.EXO.4.2v1", //not implemented - Requires integration with DNS services
//			"MS.EXO.4.3v1", //not implemented - Requires integration with DNS services
//			"MS.EXO.4.4v1", //not implemented - Requires integration with DNS services
			"MS.EXO.5.1v1", //checked
			"MS.EXO.6.1v1", //checked
			"MS.EXO.6.2v1", //checked
			"MS.EXO.7.1v1", //checked
//			"MS.EXO.8.1v2", //not checked. - implemented as an example because it is related to specific customer details
			"MS.EXO.9.1v2", //checked MS.EXO.9.2v1, MS.EXO.9.3v2, MS.EXO.9.5v1
//			"MS.EXO.9.2v1", //not checked. including in MS.EXO.9.2v1?
//			"MS.EXO.9.3v2", //not checked.including in MS.EXO.9.2v1?
//			"MS.EXO.9.5v1", //not checked. including in MS.EXO.9.2v1?
			"MS.EXO.11.1v1", //checked
//			"MS.EXO.11.2v1", //checked including in MS.EXO.11.1v1
//			"MS.EXO.11.3v1", //checked including in MS.EXO.11.1v1
			"MS.EXO.12.1v1", //checked
			"MS.EXO.12.2v1", //checked
			"MS.EXO.13.1v1", //checked
			"MS.EXO.14.1v2", //checked
//			"MS.EXO.14.2v1", //checked including MS.EXO.14.1v2
//			"MS.EXO.14.3v1",//checked including MS.EXO.14.1v2
			"MS.EXO.15.1v1", //checked
//			"MS.EXO.15.2v1",//checked including MS.EXO.15.1v1
//			"MS.EXO.15.3v1",//checked including MS.EXO.15.1v1
//			"MS.EXO.16.1v1" //not checked
//			"MS.EXO.16.2v1",//not checked
//			"MS.EXO.17.1v1",//not checked
//			"MS.EXO.17.2v1",//not checked
//			"MS.EXO.17.3v1",//not checked
			"MS.EXO.18.1v1", //checked
			"MS.EXO.19.1v1"  //checked  - Not yet fully implemented, at this stage need to be done manually
	);

	static {
		System.setProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager");
		System.setProperty("logging.level.root", "INFO");
		System.setProperty("logging.level.io.syrix", "DEBUG");
		System.setProperty("logging.pattern.console", "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n");
		try {
			Class.forName("org.jboss.logmanager.LogManager");
		} catch (ClassNotFoundException e) {
			System.err.println("JBoss LogManager not found on classpath");
		}
	}

	public static void main(String[] args) throws IOException {
		Quarkus.run(args);
		logger.info("Starting RemediationMain application...");
		try {
			Context context = initContext(args);
			Storage storage = StorageFactory.getStorage(context);

			// configRetrieve
			RetrieveTask retrieveTask = new RetrieveTask();
			retrieveTask.setId(UUID.randomUUID());
			retrieveTask.setServiceTypeList(allServices);
			ConfigurationRetrievalService retrievalService = new ConfigurationRetrievalService(storage);
			retrievalService.configRetrieve(context, retrieveTask);

//		Opa report
//			OpaReportService opaReportService = new OpaReportService(storage);
//			opaReportService.generate(retrieveTask);
//
//			//Remeditation
//			RemediationTask remediationTask = genRemediationTask(retrieveTask);
//			RemediationService remediationService = new RemediationService(storage);
//			remediationService.remediate(context, remediationTask);

// Retrieve after Remediation
//		RetrieveTask afterRemediation = new RetrieveTask();
//		afterRemediation.setId("ArturDebug-AfterRemediation");
//		afterRemediation.setServiceTypeList(allServices);
//		retrievalService.configRetrieve(context, afterRemediation);

//		Opa report
//		opaReportService.generate(afterRemediation);

			//Rollback
//			RollbackTask rollbackTask = new RollbackTask();
//			rollbackTask.setRetrieveTaskId(retrieveTask.getId().toString());
//			rollbackTask.setServiceTypeList(allServices);
//			RemediatiorRollbackService rollbackService = new RemediatiorRollbackService(storage);
//			rollbackService.rollback(context, rollbackTask);

// Retrieve after rollback
//		RetrieveTask afterRollback = new RetrieveTask();
//		afterRollback.setId("ArturDebug-AfterRollback");
//		afterRollback.setServiceTypeList(allServices);
//		retrievalService.configRetrieve(context, afterRollback);

//		Opa report
//		opaReportService.generate(afterRollback);
			Quarkus.asyncExit(0);
		} catch (Exception ex) {
			Quarkus.asyncExit(1);
		}

	}

	private static RemediationTask genRemediationTask(RetrieveTask retrieveTask) {
		RemediationTask remediationTask = new RemediationTask();
		remediationTask.setRetrieveTaskId(retrieveTask.getId().toString());
		remediationTask.setServiceType(allServices);

		remediationTask.setSpRemediationTask(genSpRemediationTask());
		remediationTask.setTeamsRemediationTask(genTeamsRemediationTask());
		remediationTask.setExchangeRemediationTask(genExchangeRemediationTask());

		return remediationTask;
	}

	private static SharepointRemediationTask genSpRemediationTask() {
		SharepointRemediationTask spRemediationTask = new SharepointRemediationTask();

		SharepointRemediationConfig remediationConfig = new SharepointRemediationConfig();
		remediationConfig.sharingCapability = SharingCapabilityEntity.DISABLE;
		remediationConfig.odbSharingCapability = SharingCapabilityEntity.DISABLE;
		spRemediationTask.setRemediationConfig(remediationConfig);
		spRemediationTask.setPolicyIds(allSpPolicy);

		return spRemediationTask;
	}

	private static TeamsRemediationTask genTeamsRemediationTask() {
		TeamsRemediationTask teamsRemediationTask = new TeamsRemediationTask();
		TeamsRemediationConfig teamsRemediationConfig = new TeamsRemediationConfig();
		teamsRemediationTask.setPolicyIds(allTeamsPolicy);
		teamsRemediationTask.setRemediationConfig(teamsRemediationConfig);

		teamsRemediationConfig.setAutoAdmittedUsersStrategy(TeamsRemediationStrategy.EVERYONE_IN_COMPANY_EXCLUDING_GUESTS);
		teamsRemediationConfig.setBroadcastRecordingModeStrategy(TeamsBroadcastRecordingStrategy.USER_OVERRIDE);

		return teamsRemediationTask;
	}

	private static ExchangeRemediationTask genExchangeRemediationTask() {
		ExchangeRemediationTask exchangeRemediationTask = new ExchangeRemediationTask();
		exchangeRemediationTask.setPolicyIds(allAllExoPolicy);
		ExchangeRemediationConfig config = new ExchangeRemediationConfig();

		exchangeRemediationTask.setRemediationConfig(config);

		AttachmentFilterConfig attachmentConfig = new AttachmentFilterConfig();
		config.setAttachmentFilterConfig(attachmentConfig);

		attachmentConfig.setPolicyName("Attachment Filter Policy(Artur3)");
		attachmentConfig.setAdminDisplayName("Attachment Filter Policy(Artur3)");
//		attachmentConfig.setRuleName("Attachment Filter Policy(Artur Debug)");
		attachmentConfig.setFilesTypeList(AttachmentFileTypeList.BASE);
		attachmentConfig.setAction(AttachmentFilterAction.REJECT);
		attachmentConfig.setZapEnabled(false);

		attachmentConfig.setRecipientDomainIs(List.of("aegorov.org", "syrixDev.onmicrosoft.com"));
		attachmentConfig.setSentTo(List.of("<EMAIL>"));
//		attachmentConfig.setSentToMemberOf(List.of("<EMAIL>"));

		//
		AntiPhishingRemediationConfig antiPhishingConfig = new AntiPhishingRemediationConfig();
		config.setAntiPhishingConfig(antiPhishingConfig);
		antiPhishingConfig.setPolicyName("AntiPhishing Policy(Artur1)");
		antiPhishingConfig.setRecipientDomain(List.of("aegorov.org"));

		return exchangeRemediationTask;
	}

	private static Context initContext(String[] args) throws IOException {
		if (args.length < 2) {
			logger.error("""
					Specify as arguments:
					args[0] - password for Certificate
					args[1] - path to Certificate""");
			System.exit(1);
		}

		String certPath = args[1];
		String certPass = args[0];

		String configPath = args.length > 2 ? args[2] : "./config.yml";
		Configuration config = yamlMapper.readValue(
				Paths.get(configPath).toFile(),
				Configuration.class
		);

		// Validate required fields
		if (config.credentials() == null ||
			config.credentials().clientId() == null ||
			config.credentials().clientSecret() == null) {
			throw new IllegalArgumentException("Missing required credentials in config file");
		}

		return new Context(config, certPath, certPass);
	}


}
