package io.syrix.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.storage.Storage;
import io.syrix.common.tmp.TempFolder;
import io.syrix.common.utils.FileUtils;
import io.syrix.datamodel.task.remediation.RemediationTask;
import io.syrix.main.Context;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.exo.ExchangeRemediationService;
import io.syrix.products.microsoft.forms.remediation.FormsPhishingProtectionRemediator;
import io.syrix.products.microsoft.sharepoint.SharePointRemediationService;
import io.syrix.products.microsoft.teams.TeamsRemediationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class RemediationService {
	private static final Logger logger = LoggerFactory.getLogger(RemediationService.class);
	private final Storage storage;
	private final ObjectMapper jsonMapper;


	public RemediationService(Storage storage) {
		this.storage = storage;
		this.jsonMapper = new ObjectMapper();
	}

	public void remediate(Context context, RemediationTask remediationTask) {
		logger.info("Start remediate task:{}", remediationTask.getId());
		Map<ConfigurationServiceType, CompletableFuture<JsonNode>> services = new HashMap<>();
		for (ConfigurationServiceType serviceType : remediationTask.getServiceType()) {
			ServiceTypeRemediationService service = switch (serviceType) {
				case ENTRA -> null;
				case TEAMS -> new TeamsRemediationService(context, storage);
				case SHAREPOINT -> new SharePointRemediationService(context, storage);
				case DEFENDER -> null;
				case POWER_PLATFORM -> null;
				case EXCHANGE_ONLINE -> new ExchangeRemediationService(context, storage);
//				case DYNAMICS -> null;
				case FORMS -> null;
				case ALL -> null;
			};
			if (service != null) {
				services.put(serviceType, service.remediate(remediationTask));
			}
		}

		//wait and save result
		CompletableFuture.allOf(services.values().toArray(new CompletableFuture[0])).join();
		writeRemediateResult(services, remediationTask);
		logger.info("Finished remediate task:{}", remediationTask.getId());
	}

	private void writeRemediateResult(Map<ConfigurationServiceType, CompletableFuture<JsonNode>> result, RemediationTask remediationTask) {
		ObjectNode resultJson = jsonMapper.createObjectNode();

		for (Map.Entry<ConfigurationServiceType, CompletableFuture<JsonNode>> entry : result.entrySet()) {
			if (entry.getValue().isCompletedExceptionally()) {
				resultJson.put(entry.getKey().getResultName(), "Failed");
			} else {
				resultJson.putIfAbsent(entry.getKey().getResultName(), entry.getValue().join());
			}
		}

		try (TempFolder tempFolder = TempFolder.newInstance()) {
			Path filePath = tempFolder.getFolder().resolve("remediateResult.json");
			FileUtils.writeJsonFile(resultJson, filePath);
			storage.saveRemediationResult(remediationTask, filePath);
		}
	}

}
