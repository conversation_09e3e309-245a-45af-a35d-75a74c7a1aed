package io.syrix.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.storage.Storage;
import io.syrix.common.tmp.TempFolder;
import io.syrix.common.utils.FileUtils;
import io.syrix.domain.RollbackTask;
import io.syrix.main.Context;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.exo.ExchangeRollbackService;
import io.syrix.products.microsoft.sharepoint.SharePointRollbackService;
import io.syrix.products.microsoft.teams.TeamsRollbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class RemediatiorRollbackService {
	private static final Logger logger = LoggerFactory.getLogger(RemediatiorRollbackService.class);
	private final Storage storage;
	private final ObjectMapper jsonMapper;

	public RemediatiorRollbackService(Storage storage) {
		this.storage = storage;
		jsonMapper = new ObjectMapper();
	}

	public void rollback(Context context, RollbackTask task) {
		logger.info("Start rollback task {} for remediation task {}", task.getId(), task.getRetrieveTaskId());
		Map<ConfigurationServiceType, CompletableFuture<JsonNode>> services = new HashMap<>();

		List<ConfigurationServiceType> serviceTypes = calcServiceTypes(task);

		for (ConfigurationServiceType serviceType : serviceTypes) {
			RollbackService service = switch (serviceType) {
				case ENTRA -> null;
				case TEAMS -> new TeamsRollbackService(context, storage);
				case SHAREPOINT -> new SharePointRollbackService(context, storage);
				case DEFENDER -> null;
				case POWER_PLATFORM -> null;
				case EXCHANGE_ONLINE -> new ExchangeRollbackService(context, storage);
//				case DYNAMICS -> null;
				case FORMS -> null;
				case ALL -> null;
			};
			if (service != null) {
				services.put(serviceType, service.rollback(task));
			}
		}

		//wait and save result
		CompletableFuture.allOf(services.values().toArray(new CompletableFuture[0])).join();
		writeRemediateResult(services, task);
		logger.info("Finished rollback task {} for remediation task {}", task.getId(), task.getRetrieveTaskId());
	}

	private List<ConfigurationServiceType> calcServiceTypes(RollbackTask task) {
		return task.getServiceTypeList();
//		return List.of(ConfigurationServiceType.SHAREPOINT);
	}

	private void writeRemediateResult(Map<ConfigurationServiceType, CompletableFuture<JsonNode>> result, RollbackTask task) {
		ObjectNode resultJson = jsonMapper.createObjectNode();

		for (Map.Entry<ConfigurationServiceType, CompletableFuture<JsonNode>> entry : result.entrySet()) {
			if (entry.getValue().isCompletedExceptionally()) {
				resultJson.put(entry.getKey().getResultName(), "Failed");
			} else {
				resultJson.putIfAbsent(entry.getKey().getResultName(), entry.getValue().join());
			}
		}

		try (TempFolder tempFolder = TempFolder.newInstance()) {
			Path filePath = tempFolder.getFolder().resolve("remediateResult.json");
			FileUtils.writeJsonFile(resultJson, filePath);
			storage.saveRollbackResult(task, filePath);
		}
	}
}
