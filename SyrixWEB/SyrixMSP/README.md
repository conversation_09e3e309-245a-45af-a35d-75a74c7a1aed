# Syrix MSP Portal

This is the Syrix MSP (Managed Service Provider) portal application built with React, TypeScript, and Redux.

## Recent Changes

### UI Enhancements
- Updated header to use Syrix logos and icons from the assets folder
- Created a notification panel with round borders
- Replaced hardcoded notification data with mockup data to be replaced by API later

## Project Structure

- `/src/assets`: Contains all the image assets including logos and icons
- `/src/components`: React components organized by feature or type
- `/src/redux`: Redux state management files
- `/src/types`: TypeScript type definitions
- `/src/context`: Context providers like ThemeContext
- `/src/hooks`: Custom React hooks
- `/src/pages`: Top-level page components

## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in development mode.
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### `yarn test`

Launches the test runner in the interactive watch mode.

### `yarn build`

Builds the app for production to the `build` folder.

## Getting Started

1. Clone the repository
2. Install dependencies: `yarn install` or `npm install`
3. Start the development server: `yarn start` or `npm start`
