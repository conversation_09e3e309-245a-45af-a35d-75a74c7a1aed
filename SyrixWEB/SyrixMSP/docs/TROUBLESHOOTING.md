# TypeScript-React-Redux Troubleshooting Guide

This guide provides solutions for common issues developers may encounter while working with TypeScript, React, and Redux in the Syrix MSP application.

## Table of Contents

1. [TypeScript Issues](#typescript-issues)
2. [React Component Issues](#react-component-issues)
3. [Redux Related Issues](#redux-related-issues)
4. [Styling Issues](#styling-issues)
5. [Performance Problems](#performance-problems)
6. [Build and Compilation Errors](#build-and-compilation-errors)
7. [Common API Integration Issues](#common-api-integration-issues)
8. [Additional TypeScript Tips](#additional-typescript-tips)

## TypeScript Issues

### Type 'X' is not assignable to type 'Y'

**Problem:**
```tsx
// Error: Type '{ name: string; onClick: () => void; }' is not assignable to type 'ButtonProps'
<Button name="Submit" onClick={handleSubmit} />
```

**Solution:**
Check the component's prop interface. In this case, ButtonProps likely has `text` instead of `name`:

```tsx
interface ButtonProps {
  text: string; // Not 'name'
  onClick: () => void;
}

// Fix:
<Button text="Submit" onClick={handleSubmit} />
```

### Object is possibly 'undefined' or 'null'

**Problem:**
```tsx
// Error: Object is possibly 'undefined'
const userEmail = user.email; 
```

**Solution:**
Use optional chaining, nullish coalescing, or type guards:

```tsx
// Optional chaining
const userEmail = user?.email;

// Nullish coalescing with default value
const userEmail = user?.email ?? 'No email';

// Type guard
if (user) {
  const userEmail = user.email;
}
```

### Cannot find module or its corresponding type declarations

**Problem:**
```tsx
// Error: Cannot find module 'some-module' or its corresponding type declarations
import { Something } from 'some-module';
```

**Solution:**
1. Install type definitions: `npm install --save-dev @types/some-module`
2. If types don't exist, create a declaration file:

```typescript
// src/types/some-module.d.ts
declare module 'some-module' {
  export function Something(): void;
  // Define other exports
}
```

### No overload matches this call

**Problem:**
```tsx
// Error: No overload matches this call
const [value, setValue] = useState();
```

**Solution:**
Provide type argument to the hook:

```tsx
const [value, setValue] = useState<string>('');
```

### Type 'string' is not assignable to type 'number'

**Problem:**
```tsx
// Error: Type 'string' is not assignable to type 'number'
const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setCount(e.target.value);
};
```

**Solution:**
Convert string to number:

```tsx
const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setCount(Number(e.target.value));
};
```

## React Component Issues

### React Hook "useX" cannot be called at the top level

**Problem:**
```tsx
// Error: React Hook "useEffect" cannot be called at the top level
useEffect(() => {
  fetchData();
}, []);
```

**Solution:**
Ensure hooks are called within a functional component or custom hook:

```tsx
const MyComponent = () => {
  useEffect(() => {
    fetchData();
  }, []);
  
  return <div>Content</div>;
};
```

### Cannot update a component while rendering a different component

**Problem:**
Updating state of a parent component directly in a child component render.

**Solution:**
Move state updates to event handlers or useEffect:

```tsx
// Instead of this:
const ChildComponent = () => {
  setParentState(newValue); // This causes the error
  return <div>Child</div>;
};

// Do this:
const ChildComponent = () => {
  useEffect(() => {
    setParentState(newValue);
  }, []);
  
  return <div>Child</div>;
};
```

### Too many re-renders

**Problem:**
```tsx
const Counter = () => {
  const [count, setCount] = useState(0);
  
  // This causes infinite re-renders
  setCount(count + 1);
  
  return <div>{count}</div>;
};
```

**Solution:**
Move state updates to event handlers or useEffect with appropriate dependencies:

```tsx
const Counter = () => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    // Add condition to prevent infinite loop
    if (count < 5) {
      setCount(count + 1);
    }
  }, [count]);
  
  return <div>{count}</div>;
};
```

### Missing dependency warning in useEffect

**Problem:**
```tsx
// Warning: React Hook useEffect has a missing dependency: 'data'
useEffect(() => {
  console.log(data);
}, []);
```

**Solution:**
Add all dependencies or refactor to avoid the dependency:

```tsx
// Add dependency
useEffect(() => {
  console.log(data);
}, [data]);

// OR use ref to avoid dependency
const dataRef = useRef(data);
dataRef.current = data;

useEffect(() => {
  console.log(dataRef.current);
}, []);
```

## Redux Related Issues

### Action is dispatched but state doesn't update

**Problem:**
Redux actions are dispatched but the state doesn't change.

**Solution:**
1. Check reducer for immutability issues:

```typescript
// Incorrect - mutating state
const reducer = (state = initialState, action) => {
  switch (action.type) {
    case 'UPDATE_USER':
      state.user = action.payload; // Mutation!
      return state;
    default:
      return state;
  }
};

// Correct - creating new state object
const reducer = (state = initialState, action) => {
  switch (action.type) {
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload
      };
    default:
      return state;
  }
};
```

2. Verify action is correctly dispatched using Redux DevTools
3. Check that your component is connected to the store properly

### Type error when dispatching thunk actions

**Problem:**
```tsx
// Error when trying to dispatch a thunk action
dispatch(fetchUsers());
```

**Solution:**
Make sure your store is configured with the thunk middleware and proper typing:

```typescript
// store.ts
import { createStore, applyMiddleware, AnyAction } from 'redux';
import thunk, { ThunkAction, ThunkDispatch } from 'redux-thunk';
import rootReducer from './reducers';

export type RootState = ReturnType<typeof rootReducer>;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  AnyAction
>;
export type AppDispatch = ThunkDispatch<RootState, unknown, AnyAction>;

const store = createStore(rootReducer, applyMiddleware(thunk));
export default store;

// In your component
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../store';

// Use typed dispatch
const dispatch = useDispatch<AppDispatch>();
dispatch(fetchUsers());
```

### Cannot read property 'X' of undefined in mapStateToProps

**Problem:**
```tsx
// Error: Cannot read property 'username' of undefined
const mapStateToProps = (state) => ({
  username: state.auth.user.username
});
```

**Solution:**
Use optional chaining and provide default values:

```tsx
const mapStateToProps = (state) => ({
  username: state.auth?.user?.username || 'Guest'
});
```

### Redux connected component not updating on state changes

**Problem:**
Component doesn't re-render when Redux state changes.

**Solution:**
1. Check if you're correctly connecting component with `connect` or `useSelector`
2. Verify selector isn't returning the same reference:

```typescript
// Problem - always returns same reference
const useUserData = () => {
  return useSelector(state => {
    return {
      user: state.auth.user
    };
  });
};

// Solution - use memoized selectors
import { createSelector } from 'reselect';

const selectUser = state => state.auth.user;

const selectUserData = createSelector(
  [selectUser],
  (user) => ({
    user
  })
);

const useUserData = () => {
  return useSelector(selectUserData);
};
```

## Styling Issues

### Styled-components and TypeScript errors

**Problem:**
```tsx
// Error: Property 'theme' does not exist on type...
const Button = styled.button`
  color: ${props => props.theme.colors.primary};
`;
```

**Solution:**
Properly type your styled components:

```tsx
import { DefaultTheme } from 'styled-components';

interface ButtonProps {
  variant?: 'primary' | 'secondary';
  theme: DefaultTheme;
}

const Button = styled.button<ButtonProps>`
  color: ${props => props.theme.colors.primary};
  background: ${props => props.variant === 'primary' ? props.theme.colors.primary : 'transparent'};
`;
```

### CSS-in-JS and dynamic props type issues

**Problem:**
```tsx
// Error: Property 'isActive' does not exist on type...
const Tab = styled.div`
  background-color: ${props => props.isActive ? 'blue' : 'gray'};
`;
```

**Solution:**
Provide proper type for props:

```tsx
interface TabProps {
  isActive?: boolean;
}

const Tab = styled.div<TabProps>`
  background-color: ${props => props.isActive ? 'blue' : 'gray'};
`;
```

## Performance Problems

### Component re-renders too often

**Problem:**
Components re-render unnecessarily, causing performance issues.

**Solution:**
1. Use React.memo for functional components:

```typescript
const MyComponent = React.memo(({ value }) => {
  return <div>{value}</div>;
});
```

2. Use useMemo and useCallback for computed values and event handlers:

```typescript
const MyComponent = ({ data, onItemClick }) => {
  // Memoize expensive calculations
  const processedData = useMemo(() => {
    return data.map(item => processItem(item));
  }, [data]);
  
  // Memoize callback functions
  const handleClick = useCallback((id) => {
    onItemClick(id);
  }, [onItemClick]);
  
  return (
    <div>
      {processedData.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onClick={() => handleClick(item.id)} 
        />
      ))}
    </div>
  );
};
```

### Large lists causing performance issues

**Problem:**
Rendering large lists causes significant performance degradation.

**Solution:**
Implement virtualization with `react-window` or `react-virtualized`:

```tsx
import { FixedSizeList } from 'react-window';

const MyList = ({ items }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      {items[index].name}
    </div>
  );

  return (
    <FixedSizeList
      height={500}
      width="100%"
      itemCount={items.length}
      itemSize={35}
    >
      {Row}
    </FixedSizeList>
  );
};
```

## Build and Compilation Errors

### Module not found error

**Problem:**
```
Module not found: Error: Can't resolve 'module-name'
```

**Solution:**
1. Install the missing package: `npm install module-name`
2. Check import path for typos
3. Verify the module exists in node_modules
4. Check tsconfig.json for proper path configurations

### TypeScript paths not resolving

**Problem:**
```
Cannot find module '@components/Button' or its corresponding type declarations
```

**Solution:**
Configure path aliases in tsconfig.json and ensure webpack is configured to use them:

```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/utils/*"]
    }
  }
}
```

### Property does not exist on window object

**Problem:**
```typescript
// Error: Property 'myGlobalVariable' does not exist on type 'Window & typeof globalThis'
window.myGlobalVariable = 'value';
```

**Solution:**
Extend the Window interface definition:

```typescript
// In a .d.ts file or at the top of your file
declare global {
  interface Window {
    myGlobalVariable: string;
  }
}

// Now this works
window.myGlobalVariable = 'value';
```

### Cannot find name 'process'

**Problem:**
```typescript
// Error: Cannot find name 'process'
const apiUrl = process.env.REACT_APP_API_URL;
```

**Solution:**
Add the node types or define the process variable:

```typescript
// Option 1: Install and reference Node types
// npm install --save-dev @types/node

// Option 2: Declare the variable
declare const process: {
  env: {
    REACT_APP_API_URL: string;
    NODE_ENV: 'development' | 'production' | 'test';
    [key: string]: string | undefined;
  }
};
```

## Common API Integration Issues

### Handling API error responses

**Problem:**
Uncaught API errors causing the application to crash or display incorrect states.

**Solution:**
Implement proper error handling with TypeScript:

```typescript
interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}

const fetchData = async (): Promise<User[] | null> => {
  try {
    const response = await fetch('/api/users');
    
    if (!response.ok) {
      const errorData: ApiError = await response.json();
      // Log error for debugging
      console.error('API Error:', errorData);
      
      // Handle specific error status codes
      if (response.status === 401) {
        // Handle unauthorized - perhaps redirect to login
      }
      
      return null;
    }
    
    const data: User[] = await response.json();
    return data;
  } catch (error) {
    // Handle network errors or JSON parsing errors
    console.error('Network or parsing error:', error);
    return null;
  }
};
```

### Type-safe API requests with Axios

**Problem:**
Missing type safety for API requests and responses.

**Solution:**
Create type-safe API service with Axios:

```typescript
import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// Define response type
export interface ApiResponse<T> {
  data: T;
  message: string;
  status: number;
}

class ApiService {
  private api: AxiosInstance;
  
  constructor(baseURL: string) {
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // Add interceptors for authentication, error handling, etc.
    this.setupInterceptors();
  }
  
  private setupInterceptors() {
    this.api.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => this.handleError(error)
    );
  }
  
  private handleError(error: AxiosError) {
    // Handle different error types and status codes
    if (error.response) {
      // Server responded with non-2xx status
      const status = error.response.status;
      
      if (status === 401) {
        // Handle unauthorized
      } else if (status === 404) {
        // Handle not found
      }
    } else if (error.request) {
      // Request made but no response received
      console.error('No response received:', error.request);
    } else {
      // Error setting up the request
      console.error('Request setup error:', error.message);
    }
    
    return Promise.reject(error);
  }
  
  // Type-safe GET request
  public async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    const response = await this.api.get<ApiResponse<T>>(endpoint);
    return response.data;
  }
  
  // Type-safe POST request
  public async post<T, D = any>(endpoint: string, data: D): Promise<ApiResponse<T>> {
    const response = await this.api.post<ApiResponse<T>>(endpoint, data);
    return response.data;
  }
}

// Usage
const api = new ApiService('https://api.example.com');

// Type-safe API calls
interface User {
  id: number;
  name: string;
  email: string;
}

const getUsers = async () => {
  const response = await api.get<User[]>('/users');
  return response.data; // Properly typed as User[]
};
```

### Handling file uploads with TypeScript

**Problem:**
Type safety issues when implementing file uploads.

**Solution:**
Type-safe file upload implementation:

```typescript
interface UploadResponse {
  fileUrl: string;
  fileName: string;
  fileSize: number;
}

const uploadFile = async (file: File): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header, browser will set it with boundary
    });
    
    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }
    
    const result: UploadResponse = await response.json();
    return result;
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
};

// React component implementation
const FileUploader: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadResponse | null>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) return;
    
    setUploading(true);
    try {
      const result = await uploadFile(file);
      setUploadedFile(result);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input type="file" onChange={handleFileChange} />
      <button type="submit" disabled={!file || uploading}>
        {uploading ? 'Uploading...' : 'Upload'}
      </button>
      
      {uploadedFile && (
        <div>
          <p>File uploaded successfully!</p>
          <p>URL: {uploadedFile.fileUrl}</p>
          <p>Size: {uploadedFile.fileSize} bytes</p>
        </div>
      )}
    </form>
  );
};
```

## Additional TypeScript Tips

### Use Type Guards for Runtime Type Checking

```typescript
// Type guard for checking if an object is a User
function isUser(obj: any): obj is User {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.email === 'string'
  );
}

// Usage
const processData = (data: unknown) => {
  if (isUser(data)) {
    // TypeScript knows data is User here
    console.log(data.name);
  } else {
    // Handle case when data is not a User
    console.error('Invalid user data:', data);
  }
};
```

### Use Discriminated Unions for State Management

```typescript
// Define possible states
type RequestState<T> =
  | { status: 'idle' }
  | { status: 'loading' }
  | { status: 'success'; data: T }
  | { status: 'error'; error: string };

// React component using discriminated union
const UserProfile: React.FC = () => {
  const [state, setState] = useState<RequestState<User>>({ status: 'idle' });
  
  useEffect(() => {
    const fetchUser = async () => {
      setState({ status: 'loading' });
      
      try {
        const response = await fetch('/api/user');
        if (!response.ok) throw new Error('Failed to fetch user');
        
        const user: User = await response.json();
        setState({ status: 'success', data: user });
      } catch (error) {
        setState({ 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    };
    
    fetchUser();
  }, []);
  
  // Type-safe rendering based on state
  switch (state.status) {
    case 'idle':
      return <div>Ready to load user data.</div>;
      
    case 'loading':
      return <div>Loading...</div>;
      
    case 'success':
      return (
        <div>
          <h1>{state.data.name}</h1>
          <p>{state.data.email}</p>
        </div>
      );
      
    case 'error':
      return <div>Error: {state.error}</div>;
  }
};
```

### Use Utility Types

Utilize TypeScript's built-in utility types to make your code more flexible:

```typescript
// Pick specific properties from a type
type UserBasicInfo = Pick<User, 'id' | 'name'>;

// Omit specific properties
type UserWithoutCredentials = Omit<User, 'password' | 'token'>;

// Make all properties optional
type PartialUser = Partial<User>;

// Make all properties required
type RequiredUser = Required<User>;

// Extract return type from a function
function fetchUsers() {
  // implementation
  return [] as User[];
}
type FetchResult = ReturnType<typeof fetchUsers>; // User[]

// Parameter types from a function
function updateUser(id: string, data: Partial<User>) {
  // implementation
}
type UpdateUserParams = Parameters<typeof updateUser>; // [string, Partial<User>]
type UpdateData = UpdateUserParams[1]; // Partial<User>
```

### Use Mapped Types for Advanced Type Transformations

```typescript
// Create read-only version of a type
type Readonly<T> = {
  readonly [P in keyof T]: T[P];
};

// Create a type with all properties nullable
type Nullable<T> = {
  [P in keyof T]: T[P] | null;
};

// Create a type with all properties optional
type Optional<T> = {
  [P in keyof T]?: T[P];
};

// Create a record type with specific keys and value type
type UserRoles = Record<'admin' | 'editor' | 'viewer', string[]>;

// Usage
const permissions: UserRoles = {
  admin: ['read', 'write', 'delete'],
  editor: ['read', 'write'],
  viewer: ['read']
};
```

### Use Branded Types for Type Safety

```typescript
// Create branded types for primitive values
type UserId = string & { readonly _brand: unique symbol };
type AuthToken = string & { readonly _brand: unique symbol };

// Create brand constructor functions
function createUserId(id: string): UserId {
  return id as UserId;
}

function createAuthToken(token: string): AuthToken {
  return token as AuthToken;
}

// Usage
function getUserById(id: UserId) {
  // Implementation
}

// This works
const validId = createUserId('123');
getUserById(validId);

// This fails
// Type error: Argument of type 'string' is not assignable to parameter of type 'UserId'
getUserById('123');
```

### Conditional Types for Advanced Type Logic

```typescript
// Create conditional types
type IsArray<T> = T extends Array<any> ? true : false;
type ElementType<T> = T extends Array<infer E> ? E : never;

// Type that extracts success type from a Promise
type Unwrap<T> = T extends Promise<infer U> ? U : T;

// Extract props type from a component
type ComponentProps<T> = T extends React.ComponentType<infer P> ? P : never;

// Usage
type StringArray = IsArray<string[]>; // true
type StringArrayElement = ElementType<string[]>; // string
type ResolvedPromise = Unwrap<Promise<number>>; // number
type ButtonComponentProps = ComponentProps<typeof Button>; // The props interface of Button
```

## Redux Toolkit Tips

### Use Redux Toolkit for Type Safety

Redux Toolkit provides excellent TypeScript integration:

```typescript
import { createSlice, PayloadAction, configureStore } from '@reduxjs/toolkit';

// Define the state interface
interface UserState {
  users: User[];
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  users: [],
  loading: false,
  error: null
};

// Create a slice with TypeScript
const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    fetchUsersStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchUsersSuccess(state, action: PayloadAction<User[]>) {
      state.users = action.payload;
      state.loading = false;
    },
    fetchUsersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    addUser(state, action: PayloadAction<User>) {
      state.users.push(action.payload);
    },
    updateUser(state, action: PayloadAction<{ id: string; updates: Partial<User> }>) {
      const { id, updates } = action.payload;
      const userIndex = state.users.findIndex(user => user.id === id);
      if (userIndex !== -1) {
        state.users[userIndex] = { ...state.users[userIndex], ...updates };
      }
    },
    deleteUser(state, action: PayloadAction<string>) {
      state.users = state.users.filter(user => user.id !== action.payload);
    }
  }
});

// Export actions and reducer
export const { 
  fetchUsersStart, 
  fetchUsersSuccess, 
  fetchUsersFailure,
  addUser,
  updateUser,
  deleteUser
} = userSlice.actions;

// Configure store with type inference
const store = configureStore({
  reducer: {
    users: userSlice.reducer
  }
});

// Infer RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Create typed hooks
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Usage in components
const UserList = () => {
  const dispatch = useAppDispatch();
  const { users, loading, error } = useAppSelector(state => state.users);
  
  // Type-safe dispatch
  const handleAddUser = (newUser: User) => {
    dispatch(addUser(newUser));
  };
  
  // Rest of component
};
```
