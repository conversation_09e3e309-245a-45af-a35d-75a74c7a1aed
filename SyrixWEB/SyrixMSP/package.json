{
  "name": "syrixmsp",
  "version": "0.1.0",
  "private": true,
  "license": "PROPRIETARY",
  "dependencies": {
    "axios": "^1.9.0",
    "react-redux": "^8.1.3",
    "react-router-dom": "^6.20.1",
    "redux": "^4.2.1",
    "redux-devtools-extension": "^2.13.9",
    "redux-thunk": "^2.4.2",
    "reselect": "^5.0.1",
    "@testing-library/jest-dom": "^5.17.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^13.5.0",
    "@types/jest": "^27.5.2",
    "@types/node": "^16.18.82",
    "@types/react-dom": "^18.3.7",
    "@types/react": "^18.3.23",
    "chart.js": "^4.4.1",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-icons": "^5.0.1",
    "react-scripts": "5.0.1",
<<<<<<< snyk-upgrade-2d409ac1647ddc14d589c1607c9134d1
    "recharts": "^2.12.1",
    "styled-components": "^6.1.18",
=======
    "recharts": "^2.15.3",
    "styled-components": "^6.1.8",
>>>>>>> main
    "typescript": "^4.9.5",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "fix-deps": "npm install"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "@types/styled-components": "^5.1.34"
  }
}