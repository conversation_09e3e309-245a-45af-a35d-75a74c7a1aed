import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// No need to import ThunkDispatch from redux-thunk
import {
  LOGIN_REQUEST,
  LOGIN_SUCCESS,
  LOGIN_FAILURE,
  LOGOUT,
  REGISTER_REQUEST,
  REGISTER_SUCCESS,
  REGISTER_FAILURE,
  CLEAR_AUTH_ERROR
} from '../redux/actions/types';
import {
  selectUser,
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
  selectUserRole,
  selectUserPermissions,
  selectIsAdmin,
  selectIsManager
} from '../redux/selectors/authSelectors';
import { User, UserRole } from '../types';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  role: UserRole | null;
  permissions: string[];
  isAdmin: boolean;
  isManager: boolean;
  login: (email: string, password: string) => Promise<{ user: User, token: string }>;
  logout: () => void;
  register: (name: string, email: string, password: string) => Promise<{ user: User, token: string }>;
  clearError: () => void;
  hasPermission: (permission: string) => boolean;
}

export const useAuth = (): UseAuthReturn => {
  const dispatch = useDispatch();
  
  // Selectors
  const user = useSelector(selectUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const role = useSelector(selectUserRole);
  const permissions = useSelector(selectUserPermissions);
  const isAdmin = useSelector(selectIsAdmin);
  const isManager = useSelector(selectIsManager);
  
  // Actions
  const login = useCallback(
    async (email: string, password: string) => {
      dispatch({ type: LOGIN_REQUEST });
      
      try {
        // Simulated API call - replace with actual API call
        const response = await new Promise<{ user: User, token: string }>((resolve) => {
          setTimeout(() => {
            resolve({
              user: {
                id: '1',
                name: 'John Doe',
                email: email,
                role: 'admin',
                companyId: '1',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
              token: 'simulated-jwt-token'
            });
          }, 800);
        });
        
        // Store token in localStorage
        localStorage.setItem('token', response.token);
        
        dispatch({
          type: LOGIN_SUCCESS,
          payload: response
        });
        
        return response;
      } catch (error) {
        dispatch({
          type: LOGIN_FAILURE,
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        });
        
        throw error;
      }
    },
    [dispatch]
  );
  
  const logout = useCallback(() => {
    // Remove token from localStorage
    localStorage.removeItem('token');
    
    dispatch({ type: LOGOUT });
  }, [dispatch]);
  
  const register = useCallback(
    async (name: string, email: string, password: string) => {
      dispatch({ type: REGISTER_REQUEST });
      
      try {
        // Simulated API call - replace with actual API call
        const response = await new Promise<{ user: User, token: string }>((resolve) => {
          setTimeout(() => {
            resolve({
              user: {
                id: '1',
                name: name,
                email: email,
                role: 'admin',
                companyId: '1',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
              token: 'simulated-jwt-token'
            });
          }, 800);
        });
        
        // Store token in localStorage
        localStorage.setItem('token', response.token);
        
        dispatch({
          type: REGISTER_SUCCESS,
          payload: response
        });
        
        return response;
      } catch (error) {
        dispatch({
          type: REGISTER_FAILURE,
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        });
        
        throw error;
      }
    },
    [dispatch]
  );
  
  const clearError = useCallback(() => {
    dispatch({ type: CLEAR_AUTH_ERROR });
  }, [dispatch]);
  
  const hasPermission = useCallback(
    (permission: string) => {
      return permissions.includes(permission);
    },
    [permissions]
  );
  
  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    role,
    permissions,
    isAdmin,
    isManager,
    login,
    logout,
    register,
    clearError,
    hasPermission
  };
};
