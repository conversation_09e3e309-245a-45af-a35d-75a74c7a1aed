import { useState, useCallback, ChangeEvent, FormEvent } from 'react';

type ValidationErrors<T> = Partial<Record<keyof T, string>>;

type ValidationRules<T> = {
  [K in keyof T]?: {
    required?: boolean;
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: T[K], values: T) => string | undefined;
    errorMessage?: string;
  };
};

interface UseFormReturn<T> {
  values: T;
  errors: ValidationErrors<T>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleBlur: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  setFieldValue: (field: keyof T, value: any) => void;
  setFieldTouched: (field: keyof T, isTouched?: boolean) => void;
  setFieldError: (field: keyof T, error: string | undefined) => void;
  resetForm: () => void;
  validateForm: () => boolean;
}

export const useForm = <T extends Record<string, any>>(
  initialValues: T,
  validationRules: ValidationRules<T> = {},
  onSubmit?: (values: T) => void | Promise<void>
): UseFormReturn<T> => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<ValidationErrors<T>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(true);

  const validateField = useCallback(
    (name: keyof T, value: any): string | undefined => {
      const rules = validationRules[name];
      
      if (!rules) return undefined;
      
      // Required check
      if (rules.required && (value === undefined || value === null || value === '')) {
        return rules.errorMessage || `${String(name)} is required`;
      }
      
      // Pattern check
      if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
        return rules.errorMessage || `${String(name)} is invalid`;
      }
      
      // Min length check
      if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
        return rules.errorMessage || `${String(name)} must be at least ${rules.minLength} characters`;
      }
      
      // Max length check
      if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
        return rules.errorMessage || `${String(name)} must be at most ${rules.maxLength} characters`;
      }
      
      // Min value check
      if (rules.min !== undefined && typeof value === 'number' && value < rules.min) {
        return rules.errorMessage || `${String(name)} must be at least ${rules.min}`;
      }
      
      // Max value check
      if (rules.max !== undefined && typeof value === 'number' && value > rules.max) {
        return rules.errorMessage || `${String(name)} must be at most ${rules.max}`;
      }
      
      // Custom validation
      if (rules.custom) {
        return rules.custom(value, values);
      }
      
      return undefined;
    },
    [validationRules, values]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors<T> = {};
    let formIsValid = true;
    
    // Validate each field
    Object.keys(values).forEach((key) => {
      const name = key as keyof T;
      const error = validateField(name, values[name]);
      
      if (error) {
        newErrors[name] = error;
        formIsValid = false;
      }
    });
    
    setErrors(newErrors);
    setIsValid(formIsValid);
    
    return formIsValid;
  }, [values, validateField]);

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value, type } = e.target;
      const key = name as keyof T;
      
      // Handle different input types
      let parsedValue: any = value;
      
      if (type === 'checkbox') {
        parsedValue = (e.target as HTMLInputElement).checked;
      } else if (type === 'number') {
        parsedValue = value === '' ? '' : Number(value);
      }
      
      setValues((prevValues) => ({
        ...prevValues,
        [key]: parsedValue,
      }));
      
      // Validate on change
      const error = validateField(key, parsedValue);
      setErrors((prevErrors) => ({
        ...prevErrors,
        [key]: error,
      }));
      
      // Mark as touched
      if (!touched[key]) {
        setTouched((prevTouched) => ({
          ...prevTouched,
          [key]: true,
        }));
      }
    },
    [touched, validateField]
  );

  const handleBlur = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name } = e.target;
      const key = name as keyof T;
      
      // Mark as touched
      setTouched((prevTouched) => ({
        ...prevTouched,
        [key]: true,
      }));
      
      // Validate on blur
      const error = validateField(key, values[key]);
      setErrors((prevErrors) => ({
        ...prevErrors,
        [key]: error,
      }));
    },
    [values, validateField]
  );

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      
      // Validate all fields before submission
      const formIsValid = validateForm();
      
      // Mark all fields as touched
      const allTouched = Object.keys(values).reduce(
        (acc, key) => ({
          ...acc,
          [key]: true,
        }),
        {} as Record<keyof T, boolean>
      );
      
      setTouched(allTouched);
      
      if (formIsValid && onSubmit) {
        setIsSubmitting(true);
        
        try {
          await onSubmit(values);
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      }
    },
    [values, validateForm, onSubmit]
  );

  const setFieldValue = useCallback((field: keyof T, value: any) => {
    setValues((prevValues) => ({
      ...prevValues,
      [field]: value,
    }));
    
    // Validate the field
    const error = validateField(field, value);
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: error,
    }));
  }, [validateField]);

  const setFieldTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouched((prevTouched) => ({
      ...prevTouched,
      [field]: isTouched,
    }));
  }, []);

  const setFieldError = useCallback((field: keyof T, error: string | undefined) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: error,
    }));
  }, []);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(true);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldTouched,
    setFieldError,
    resetForm,
    validateForm,
  };
};
