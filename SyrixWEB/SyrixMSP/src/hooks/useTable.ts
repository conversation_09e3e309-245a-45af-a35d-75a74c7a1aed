import { useState, useEffect, useCallback } from 'react';
import { PaginatedResponse } from '../types';

interface TableState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  sorting: {
    field: keyof T | null;
    order: 'asc' | 'desc';
  };
  filters: Record<string, any>;
}

interface UseTableOptions<T> {
  defaultPage?: number;
  defaultLimit?: number;
  defaultSorting?: {
    field: keyof T | null;
    order: 'asc' | 'desc';
  };
  defaultFilters?: Record<string, any>;
  autoFetch?: boolean;
}

interface UseTableReturn<T> {
  state: TableState<T>;
  fetchData: () => Promise<void>;
  handlePageChange: (page: number) => void;
  handleLimitChange: (limit: number) => void;
  handleSortChange: (field: keyof T, order?: 'asc' | 'desc') => void;
  handleFilterChange: (filterName: string, value: any) => void;
  resetFilters: () => void;
  refreshData: () => Promise<void>;
}

interface FetchDataParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

type FetchDataFunction<T> = (params: FetchDataParams) => Promise<PaginatedResponse<T>>;

export const useTable = <T>(
  fetchFunction: FetchDataFunction<T>,
  options: UseTableOptions<T> = {}
): UseTableReturn<T> => {
  const {
    defaultPage = 1,
    defaultLimit = 10,
    defaultSorting = { field: null, order: 'asc' },
    defaultFilters = {},
    autoFetch = true
  } = options;

  const [state, setState] = useState<TableState<T>>({
    data: [],
    loading: false,
    error: null,
    pagination: {
      page: defaultPage,
      limit: defaultLimit,
      total: 0,
      totalPages: 0
    },
    sorting: defaultSorting,
    filters: defaultFilters
  });

  const buildParams = useCallback(() => {
    const params: FetchDataParams = {
      page: state.pagination.page,
      limit: state.pagination.limit,
      ...state.filters
    };

    if (state.sorting.field) {
      params.sortBy = String(state.sorting.field);
      params.sortOrder = state.sorting.order;
    }

    return params;
  }, [state.pagination.page, state.pagination.limit, state.sorting, state.filters]);

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const params = buildParams();
      const response = await fetchFunction(params);

      setState(prev => ({
        ...prev,
        data: response.data,
        loading: false,
        pagination: {
          ...prev.pagination,
          total: response.pagination.total,
          totalPages: response.pagination.pages
        }
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred while fetching data'
      }));
    }
  }, [fetchFunction, buildParams]);

  // Initial data fetching
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [fetchData, autoFetch]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        page
      }
    }));
  }, []);

  const handleLimitChange = useCallback((limit: number) => {
    setState(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        limit,
        page: 1 // Reset to first page when changing limit
      }
    }));
  }, []);

  // Sorting handler
  const handleSortChange = useCallback((field: keyof T, order?: 'asc' | 'desc') => {
    setState(prev => {
      // Toggle order if clicking the same field, or use provided order
      const newOrder = order || (prev.sorting.field === field && prev.sorting.order === 'asc') ? 'desc' : 'asc';
      
      return {
        ...prev,
        sorting: {
          field,
          order: newOrder
        },
        pagination: {
          ...prev.pagination,
          page: 1 // Reset to first page when changing sorting
        }
      };
    });
  }, []);

  // Filter handlers
  const handleFilterChange = useCallback((filterName: string, value: any) => {
    setState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [filterName]: value
      },
      pagination: {
        ...prev.pagination,
        page: 1 // Reset to first page when changing filters
      }
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: defaultFilters,
      pagination: {
        ...prev.pagination,
        page: 1 // Reset to first page when resetting filters
      }
    }));
  }, [defaultFilters]);

  // Refresh data - useful for manual refreshes
  const refreshData = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  // Fetch data when pagination, sorting, or filters change
  useEffect(() => {
    fetchData();
  }, [state.pagination.page, state.pagination.limit, state.sorting, state.filters, fetchData]);

  return {
    state,
    fetchData,
    handlePageChange,
    handleLimitChange,
    handleSortChange,
    handleFilterChange,
    resetFilters,
    refreshData
  };
};
