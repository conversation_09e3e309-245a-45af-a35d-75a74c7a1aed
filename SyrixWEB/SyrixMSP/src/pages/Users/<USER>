import React, { useState } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useTable } from '../../hooks/useTable';
import userService from '../../services/userService';
import { User, UserRole } from '../../types';

// Styled components
const UsersContainer = styled.div`
  padding: 1.5rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 24px;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const AddButton = styled.button`
  background: ${props => props.theme.gradients.success};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    opacity: 0.9;
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FilterLabel = styled.label`
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  font-weight: 500;
`;

const FilterSelect = styled.select`
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  min-width: 150px;
`;

const FilterInput = styled.input`
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid ${props => props.theme.colors.border};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  min-width: 200px;
`;

const ResetFiltersButton = styled.button`
  background: transparent;
  color: ${props => props.theme.colors.text};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: auto;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const TableContainer = styled.div`
  background-color: ${props => props.theme.colors.cardBackground};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  background-color: rgba(0, 0, 0, 0.1);
`;

const TableHeadCell = styled.th<{ sortable?: boolean }>`
  text-align: left;
  padding: 1rem;
  color: ${props => props.theme.colors.text};
  font-weight: 600;
  font-size: 14px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: ${props => (props.sortable ? 'pointer' : 'default')};
  white-space: nowrap;
  
  &:hover {
    background-color: ${props => (props.sortable ? 'rgba(0, 0, 0, 0.1)' : 'transparent')};
  }
`;

const SortIcon = styled.span<{ order: 'asc' | 'desc' }>`
  display: inline-block;
  margin-left: 0.5rem;
  transform: ${props => (props.order === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)')};
  transition: transform 0.2s;
  
  &::after {
    content: '▲';
    font-size: 10px;
  }
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.03);
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.08);
    cursor: pointer;
  }
`;

const TableCell = styled.td`
  padding: 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const Badge = styled.span<{ variant: 'admin' | 'manager' | 'user' | 'readonly' }>`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  
  ${props => {
    switch (props.variant) {
      case 'admin':
        return `background-color: rgba(220, 53, 69, 0.2); color: #dc3545;`;
      case 'manager':
        return `background-color: rgba(255, 193, 7, 0.2); color: #ffc107;`;
      case 'user':
        return `background-color: rgba(13, 110, 253, 0.2); color: #0d6efd;`;
      case 'readonly':
        return `background-color: rgba(108, 117, 125, 0.2); color: #6c757d;`;
      default:
        return `background-color: rgba(108, 117, 125, 0.2); color: #6c757d;`;
    }
  }}
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
`;

const PaginationInfo = styled.div`
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const PaginationControls = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const PaginationButton = styled.button<{ active?: boolean; disabled?: boolean }>`
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  background-color: ${props => (props.active ? 'rgba(59, 130, 246, 0.2)' : 'transparent')};
  color: ${props => (props.active ? '#3B82F6' : props.theme.colors.text)};
  border: 1px solid ${props => (props.active ? '#3B82F6' : props.theme.colors.border)};
  cursor: ${props => (props.disabled ? 'not-allowed' : 'pointer')};
  opacity: ${props => (props.disabled ? 0.5 : 1)};
  
  &:hover:not(:disabled) {
    background-color: ${props => (props.active ? 'rgba(59, 130, 246, 0.3)' : 'rgba(255, 255, 255, 0.1)')};
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${props => props.theme.colors.text};
`;

const EmptyStateIcon = styled.div`
  font-size: 48px;
  margin-bottom: 1rem;
  opacity: 0.5;
`;

const EmptyStateTitle = styled.h3`
  font-size: 18px;
  margin: 0 0 0.5rem;
`;

const EmptyStateDescription = styled.p`
  font-size: 14px;
  opacity: 0.7;
  margin: 0 0 1.5rem;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: ${props => props.theme.colors.text};
  
  &::after {
    content: '';
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: ${props => props.theme.colors.text};
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const ErrorMessage = styled.div`
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: 14px;
`;

// Format date utility
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

// Main component
const UsersList: React.FC = () => {
  const navigate = useNavigate();
  
  // Setup table with useTable hook
  const {
    state: { data: users, loading, error, pagination, sorting, filters },
    handlePageChange,
    handleLimitChange,
    handleSortChange,
    handleFilterChange,
    resetFilters,
    refreshData
  } = useTable<User>(userService.getUsers.bind(userService), {
    defaultSorting: { field: 'createdAt', order: 'desc' },
    defaultFilters: { status: 'active' }
  });
  
  // Handle row click
  const handleRowClick = (userId: string) => {
    navigate(`/users/${userId}`);
  };
  
  // Handle add user button click
  const handleAddUser = () => {
    navigate('/users/new');
  };
  
  // Render pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    const { page, totalPages } = pagination;
    
    // Previous button
    buttons.push(
      <PaginationButton
        key="prev"
        disabled={page === 1}
        onClick={() => handlePageChange(page - 1)}
      >
        Previous
      </PaginationButton>
    );
    
    // Page numbers
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <PaginationButton
          key={i}
          active={i === page}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </PaginationButton>
      );
    }
    
    // Next button
    buttons.push(
      <PaginationButton
        key="next"
        disabled={page === totalPages}
        onClick={() => handlePageChange(page + 1)}
      >
        Next
      </PaginationButton>
    );
    
    return buttons;
  };
  
  return (
    <UsersContainer>
      <Header>
        <Title>Users</Title>
        <AddButton onClick={handleAddUser}>
          <span>+</span> Add User
        </AddButton>
      </Header>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      <FiltersContainer>
        <FilterGroup>
          <FilterLabel htmlFor="search">Search</FilterLabel>
          <FilterInput
            id="search"
            type="text"
            placeholder="Search by name or email"
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </FilterGroup>
        
        <FilterGroup>
          <FilterLabel htmlFor="role">Role</FilterLabel>
          <FilterSelect
            id="role"
            value={filters.role || ''}
            onChange={(e) => handleFilterChange('role', e.target.value)}
          >
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="manager">Manager</option>
            <option value="user">User</option>
            <option value="readonly">Read Only</option>
          </FilterSelect>
        </FilterGroup>
        
        <FilterGroup>
          <FilterLabel htmlFor="status">Status</FilterLabel>
          <FilterSelect
            id="status"
            value={filters.status || ''}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </FilterSelect>
        </FilterGroup>
        
        <ResetFiltersButton onClick={resetFilters}>
          Reset Filters
        </ResetFiltersButton>
      </FiltersContainer>
      
      <TableContainer>
        <Table>
          <TableHead>
            <tr>
              <TableHeadCell>Name</TableHeadCell>
              <TableHeadCell>Email</TableHeadCell>
              <TableHeadCell
                sortable
                onClick={() => handleSortChange('role')}
              >
                Role
                {sorting.field === 'role' && (
                  <SortIcon order={sorting.order} />
                )}
              </TableHeadCell>
              <TableHeadCell
                sortable
                onClick={() => handleSortChange('createdAt')}
              >
                Created At
                {sorting.field === 'createdAt' && (
                  <SortIcon order={sorting.order} />
                )}
              </TableHeadCell>
              <TableHeadCell
                sortable
                onClick={() => handleSortChange('lastLoginAt')}
              >
                Last Login
                {sorting.field === 'lastLoginAt' && (
                  <SortIcon order={sorting.order} />
                )}
              </TableHeadCell>
            </tr>
          </TableHead>
          
          <TableBody>
            {loading ? (
              <tr>
                <TableCell colSpan={5}>
                  <LoadingSpinner />
                </TableCell>
              </tr>
            ) : users.length === 0 ? (
              <tr>
                <TableCell colSpan={5}>
                  <EmptyState>
                    <EmptyStateIcon>👥</EmptyStateIcon>
                    <EmptyStateTitle>No users found</EmptyStateTitle>
                    <EmptyStateDescription>
                      {Object.keys(filters).length > 0
                        ? 'Try adjusting your filters or search criteria.'
                        : 'Start by adding a new user to your organization.'}
                    </EmptyStateDescription>
                  </EmptyState>
                </TableCell>
              </tr>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} onClick={() => handleRowClick(user.id)}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={user.role as 'admin' | 'manager' | 'user' | 'readonly'}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>
                    {user.lastLoginAt ? formatDate(user.lastLoginAt) : '-'}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        {!loading && users.length > 0 && (
          <Pagination>
            <PaginationInfo>
              Showing {(pagination.page - 1) * pagination.limit + 1}-
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} users
            </PaginationInfo>
            
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
              <FilterSelect
                value={pagination.limit}
                onChange={(e) => handleLimitChange(Number(e.target.value))}
                style={{ minWidth: '80px' }}
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </FilterSelect>
              
              <PaginationControls>
                {renderPaginationButtons()}
              </PaginationControls>
            </div>
          </Pagination>
        )}
      </TableContainer>
    </UsersContainer>
  );
};

export default UsersList;