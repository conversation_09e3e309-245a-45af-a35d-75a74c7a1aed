import React, { useEffect } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useForm } from '../../hooks/useForm';

interface LoginFormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

const initialValues: LoginFormValues = {
  email: '',
  password: '',
  rememberMe: false
};

const validationRules = {
  email: {
    required: true,
    pattern: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    errorMessage: 'Please enter a valid email address'
  },
  password: {
    required: true,
    minLength: 6,
    errorMessage: 'Password must be at least 6 characters'
  }
};

const LoginContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: ${props => props.theme.gradients.background};
`;

const LoginCard = styled.div`
  background-color: ${props => props.theme.colors.cardBackground};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 24px;
    color: ${props => props.theme.colors.text};
    margin-bottom: 8px;
  }
  
  p {
    font-size: 16px;
    color: ${props => props.theme.colors.text};
    opacity: 0.8;
  }
`;

const LoginForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.theme.colors.text};
`;

const Input = styled.input<{ hasError?: boolean }>`
  padding: 12px;
  border-radius: 4px;
  border: 1px solid ${props => props.hasError 
    ? 'rgba(220, 53, 69, 0.8)' 
    : props.theme.colors.border};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  }
`;

const ErrorMessage = styled.div`
  color: rgba(220, 53, 69, 0.9);
  font-size: 12px;
  margin-top: 4px;
`;

const RememberMeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-size: 14px;
    color: ${props => props.theme.colors.text};
    cursor: pointer;
  }
  
  input[type="checkbox"] {
    cursor: pointer;
  }
`;

const SubmitButton = styled.button<{ isLoading?: boolean }>`
  padding: 12px;
  background: ${props => props.theme.gradients.success};
  border-radius: 4px;
  color: white;
  font-weight: 600;
  border: none;
  cursor: ${props => props.isLoading ? 'wait' : 'pointer'};
  opacity: ${props => props.isLoading ? 0.7 : 1};
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 0.9;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ForgotPasswordLink = styled.a`
  font-size: 14px;
  text-align: center;
  color: ${props => props.theme.colors.text};
  opacity: 0.8;
  margin-top: 1rem;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
    opacity: 1;
  }
`;

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  
  const { 
    values, 
    errors, 
    touched,
    isSubmitting,
    isValid,
    handleChange, 
    handleBlur, 
    handleSubmit,
    setFieldValue
  } = useForm<LoginFormValues>(
    initialValues,
    validationRules,
    async (formValues) => {
      try {
        await login(formValues.email, formValues.password);
        navigate('/dashboard');
      } catch (err) {
        // Error is handled by Redux
        console.error('Login failed:', err);
      }
    }
  );
  
  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);
  
  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);
  
  return (
    <LoginContainer>
      <LoginCard>
        <LoginHeader>
          <h1>Welcome Back</h1>
          <p>Sign in to your Syrix MSP account</p>
        </LoginHeader>
        
        <LoginForm onSubmit={handleSubmit}>
          {error && (
            <ErrorMessage>
              {error}
            </ErrorMessage>
          )}
          
          <FormField>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              hasError={touched.email && !!errors.email}
              placeholder="Enter your email"
              autoComplete="email"
            />
            {touched.email && errors.email && (
              <ErrorMessage>{errors.email}</ErrorMessage>
            )}
          </FormField>
          
          <FormField>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={values.password}
              onChange={handleChange}
              onBlur={handleBlur}
              hasError={touched.password && !!errors.password}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
            {touched.password && errors.password && (
              <ErrorMessage>{errors.password}</ErrorMessage>
            )}
          </FormField>
          
          <RememberMeContainer>
            <input
              id="rememberMe"
              name="rememberMe"
              type="checkbox"
              checked={values.rememberMe}
              onChange={(e) => setFieldValue('rememberMe', e.target.checked)}
            />
            <Label htmlFor="rememberMe">Remember me</Label>
          </RememberMeContainer>
          
          <SubmitButton 
            type="submit" 
            disabled={!isValid || isSubmitting} 
            isLoading={isLoading || isSubmitting}
          >
            {isLoading || isSubmitting ? 'Signing in...' : 'Sign In'}
          </SubmitButton>
          
          <ForgotPasswordLink href="/forgot-password">
            Forgot your password?
          </ForgotPasswordLink>
        </LoginForm>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
