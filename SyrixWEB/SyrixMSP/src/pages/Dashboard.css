.dashboard-container {
  padding: 20px;
}

.card.new-accounts {
  background-color: #080525;
  background: linear-gradient(180deg, #0e3b7a 0%, #062d68 100%);
  flex:2;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.dashboard-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-row {
  display: flex;
  gap: 24px;
}

.dashboard-col {
  flex: 1;
  min-width: 0;
}

.card {
  background-color: var(--card-background);
  border-radius: 0.75rem;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.card-link {
  display: flex;
  align-items: center;
  color: var(--text-primary);
  font-size: 14px;
  text-decoration: none;
}

.card-link svg {
  margin-left: 4px;
}

.card-dropdown select {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.card-content {
  padding: 20px;
  height: 100%;
}

.status-grid {
  display: flex;
  gap: 16px;
}

.status-item {
  flex: 1;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.status-item.active {
  border-left: 4px solid #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

.status-item.expiring {
  border-left: 4px solid #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.status-item.expired {
  border-left: 4px solid #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto 12px;
}

.status-item.active .status-dot {
  background-color: #10b981;
}

.status-item.expiring .status-dot {
  background-color: #f59e0b;
}

.status-item.expired .status-dot {
  background-color: #ef4444;
}

.status-value {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.status-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  border-top: 1px solid var(--border-color);
  padding-top: 16px;
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination-actions {
  display: flex;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  cursor: pointer;
}

.view-all-link {
  display: flex;
  align-items: center;
  color: var(--text-primary);
  font-size: 14px;
  text-decoration: none;
}

.view-all-link svg {
  margin-left: 4px;
}

@media (max-width: 1024px) {
  .dashboard-row {
    flex-direction: column;
  }

  .status-grid {
    flex-direction: column;
  }
}