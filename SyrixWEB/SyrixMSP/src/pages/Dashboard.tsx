import React from 'react';
import { ChevronRightIcon, ChevronLeftIcon } from '../components/icons';
import { useTheme } from '../context/ThemeContext';
import styled from 'styled-components';

// Import Dashboard components
import StatusCards from '../components/dashboard/StatusCards';
import NewAccountsChart from '../components/dashboard/NewAccountsChart';
import AccountsTable from '../components/dashboard/AccountsTable';

const DashboardContainer = styled.div`
  padding: 20px;
`;

const DashboardHeader = styled.div`
  margin-bottom: 24px;
`;

const DashboardTitle = styled.h1`
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: ${({ theme }) => theme.colors.text};
`;

const DashboardSubtitle = styled.p`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.secondary};
  margin: 0;
`;

const DashboardContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const DashboardRow = styled.div`
  display: flex;
  gap: 24px;
  
  @media (max-width: 1024px) {
    flex-direction: column;
  }
`;

const DashboardCol = styled.div`
  flex: 1;
  min-width: 0;
`;

const Card = styled.div`
  background-color: ${({ theme }) => theme.name === 'dark' ? '#093370' : theme.colors.cardBackground};
  border-radius: 0.75rem;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.15);
  overflow: hidden;
`;

const NewAccountsCard = styled(Card)`
  background: ${({ theme }) => theme.name === 'dark' 
    ? 'linear-gradient(180deg, #093370 0%, #093370 100%)' 
    : 'linear-gradient(180deg, #f0f9ff 0%, #e0f2fe 100%)'};
  flex: 2;
`;

const CardHeader = styled.div`
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const CardTitle = styled.h2`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: ${({ theme }) => theme.colors.text};
`;

const CardLink = styled.a`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
  text-decoration: none;
  
  svg {
    margin-left: 4px;
  }
`;

const CardContent = styled.div`
  padding: 20px;
  height: 100%;
`;

const StatusGrid = styled.div`
  display: flex;
  gap: 16px;
  
  @media (max-width: 1024px) {
    flex-direction: column;
  }
`;

interface StatusItemProps {
  type: 'active' | 'expiring' | 'expired';
}

const StatusItem = styled.div<StatusItemProps>`
  flex: 1;
  background-color: ${props => {
    const alpha = props.theme.name === 'dark' ? 0.1 : 0.1;
    switch (props.type) {
      case 'active': return `rgba(16, 185, 129, ${alpha})`;
      case 'expiring': return `rgba(245, 158, 11, ${alpha})`;
      case 'expired': return `rgba(239, 68, 68, ${alpha})`;
      default: return 'rgba(0, 0, 0, 0.15)';
    }
  }};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border-left: 4px solid ${props => {
    switch (props.type) {
      case 'active': return '#10b981';
      case 'expiring': return '#f59e0b';
      case 'expired': return '#ef4444';
      default: return 'transparent';
    }
  }};
`;

const StatusDot = styled.div<StatusItemProps>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto 12px;
  background-color: ${props => {
    switch (props.type) {
      case 'active': return '#10b981';
      case 'expiring': return '#f59e0b';
      case 'expired': return '#ef4444';
      default: return 'transparent';
    }
  }};
`;

const StatusValue = styled.h3`
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: ${({ theme }) => theme.colors.text};
`;

const StatusLabel = styled.p`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.secondary};
  margin: 0;
`;

const TablePagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  padding-top: 16px;
`;

const PaginationInfo = styled.div`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.secondary};
`;

const PaginationActions = styled.div`
  display: flex;
  gap: 8px;
`;

const PaginationButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.cardBackground};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text};
`;

const ViewAllLink = styled.a`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.text};
  font-size: 14px;
  text-decoration: none;
  
  svg {
    margin-left: 4px;
  }
`;

const Dashboard: React.FC = () => {
  const { theme } = useTheme();
  
  return (
    <DashboardContainer>
      <DashboardHeader>
        <DashboardTitle>MSP Portal</DashboardTitle>
        <DashboardSubtitle>Showing a list of medium alerts</DashboardSubtitle>
      </DashboardHeader>
      
      <DashboardContent>
        <DashboardRow>
          <DashboardCol>
            <Card>
              <CardHeader>
                <CardTitle>Billing Status</CardTitle>
                <CardLink href="#">
                  To invoices <ChevronRightIcon size={16} color={theme.colors.text} />
                </CardLink>
              </CardHeader>
              <CardContent>
                <StatusCards />
              </CardContent>
            </Card>
          </DashboardCol>
          
          <DashboardCol>
            <Card>
              <CardHeader>
                <CardTitle>Policy Status</CardTitle>
                <CardLink href="#">
                  Status <ChevronRightIcon size={16} color={theme.colors.text} />
                </CardLink>
              </CardHeader>
              <CardContent>
                <StatusGrid>
                  <StatusItem type="active">
                    <StatusDot type="active" />
                    <StatusValue>154</StatusValue>
                    <StatusLabel>Active</StatusLabel>
                  </StatusItem>
                  <StatusItem type="expiring">
                    <StatusDot type="expiring" />
                    <StatusValue>26</StatusValue>
                    <StatusLabel>Expiring</StatusLabel>
                  </StatusItem>
                  <StatusItem type="expired">
                    <StatusDot type="expired" />
                    <StatusValue>4</StatusValue>
                    <StatusLabel>Expired</StatusLabel>
                  </StatusItem>
                </StatusGrid>
              </CardContent>
            </Card>
          </DashboardCol>
        </DashboardRow>
        
        <DashboardRow>
          <DashboardCol>
            <NewAccountsCard>
              <CardContent>
                <NewAccountsChart />
              </CardContent>
            </NewAccountsCard>
          </DashboardCol>
          
          <DashboardCol>
            <Card>
              <CardHeader>
                <CardTitle>Top risks accounts</CardTitle>
                <CardLink href="#">
                  View all <ChevronRightIcon size={16} color={theme.colors.text} />
                </CardLink>
              </CardHeader>
              <CardContent>
                <AccountsTable />
                <TablePagination>
                  <PaginationInfo>Showing 1 of 2</PaginationInfo>
                  <PaginationActions>
                    <PaginationButton>
                      <ChevronLeftIcon size={16} color={theme.colors.text} />
                    </PaginationButton>
                    <PaginationButton>
                      <ChevronRightIcon size={16} color={theme.colors.text} />
                    </PaginationButton>
                  </PaginationActions>
                  <ViewAllLink href="#">
                    View all <ChevronRightIcon size={16} color={theme.colors.text} />
                  </ViewAllLink>
                </TablePagination>
              </CardContent>
            </Card>
          </DashboardCol>
        </DashboardRow>
      </DashboardContent>
    </DashboardContainer>
  );
};

export default Dashboard;