import {
  <PERSON><PERSON>GIN_REQUEST,
  LOGIN_SUCCESS,
  LOGIN_FAILURE,
  LOGOUT,
  REGISTER_REQUEST,
  REGISTER_SUCCESS,
  REGISTER_FAILURE,
  REFRESH_TOKEN,
  AUTH_ERROR,
  C<PERSON><PERSON>_AUTH_ERROR
} from '../actions/types';
import { AuthActionTypes } from '../actions/authActions';
import { AuthState } from '../../types';

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
  token: localStorage.getItem('token')
};

const authReducer = (
  state = initialState,
  action: AuthActionTypes
): AuthState => {
  switch (action.type) {
    case LOGIN_REQUEST:
    case REGISTER_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
    
    case LOGIN_SUCCESS:
    case REGISTER_SUCCESS:
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null
      };
    
    case LOGIN_FAILURE:
    case REGISTER_FAILURE:
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.error
      };
    
    case LOGOUT:
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null
      };
    
    case REFRESH_TOKEN:
      return {
        ...state,
        token: action.payload.token
      };
    
    case AUTH_ERROR:
      return {
        ...state,
        error: action.error
      };
    
    case CLEAR_AUTH_ERROR:
      return {
        ...state,
        error: null
      };
    
    default:
      return state;
  }
};

export default authReducer;
