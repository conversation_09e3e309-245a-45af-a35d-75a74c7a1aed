import {
  FETCH_USERS_REQUEST,
  FETCH_USERS_SUCCESS,
  FETCH_USERS_FAILURE,
  FETCH_USER_REQUEST,
  FETCH_USER_SUCCESS,
  FETCH_USER_FAILURE,
  CREATE_USER_REQUEST,
  CREATE_USER_SUCCESS,
  CREATE_USER_FAILURE,
  UPDATE_USER_REQUEST,
  UPDATE_USER_SUCCESS,
  UPDATE_USER_FAILURE,
  DELETE_USER_REQUEST,
  DELETE_USER_SUCCESS,
  DELETE_USER_FAILURE
} from '../actions/types';
import { User } from '../../types';

interface UsersState {
  users: User[];
  currentUser: User | null;
  loading: boolean;
  error: string | null;
}

const initialState: UsersState = {
  users: [],
  currentUser: null,
  loading: false,
  error: null
};

type UsersAction = 
  | { type: typeof FETCH_USERS_REQUEST }
  | { type: typeof FETCH_USERS_SUCCESS, payload: User[] }
  | { type: typeof FETCH_USERS_FAILURE, error: string }
  | { type: typeof FETCH_USER_REQUEST }
  | { type: typeof FETCH_USER_SUCCESS, payload: User }
  | { type: typeof FETCH_USER_FAILURE, error: string }
  | { type: typeof CREATE_USER_REQUEST }
  | { type: typeof CREATE_USER_SUCCESS, payload: User }
  | { type: typeof CREATE_USER_FAILURE, error: string }
  | { type: typeof UPDATE_USER_REQUEST }
  | { type: typeof UPDATE_USER_SUCCESS, payload: User }
  | { type: typeof UPDATE_USER_FAILURE, error: string }
  | { type: typeof DELETE_USER_REQUEST }
  | { type: typeof DELETE_USER_SUCCESS, payload: string }
  | { type: typeof DELETE_USER_FAILURE, error: string };

const usersReducer = (
  state = initialState,
  action: UsersAction
): UsersState => {
  switch (action.type) {
    case FETCH_USERS_REQUEST:
    case FETCH_USER_REQUEST:
    case CREATE_USER_REQUEST:
    case UPDATE_USER_REQUEST:
    case DELETE_USER_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
      
    case FETCH_USERS_SUCCESS:
      return {
        ...state,
        users: action.payload,
        loading: false,
        error: null
      };
      
    case FETCH_USER_SUCCESS:
      return {
        ...state,
        currentUser: action.payload,
        loading: false,
        error: null
      };
      
    case CREATE_USER_SUCCESS:
      return {
        ...state,
        users: [...state.users, action.payload],
        loading: false,
        error: null
      };
      
    case UPDATE_USER_SUCCESS:
      return {
        ...state,
        users: state.users.map(user => 
          user.id === action.payload.id ? action.payload : user
        ),
        currentUser: state.currentUser?.id === action.payload.id 
          ? action.payload 
          : state.currentUser,
        loading: false,
        error: null
      };
      
    case DELETE_USER_SUCCESS:
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload),
        currentUser: state.currentUser?.id === action.payload 
          ? null 
          : state.currentUser,
        loading: false,
        error: null
      };
      
    case FETCH_USERS_FAILURE:
    case FETCH_USER_FAILURE:
    case CREATE_USER_FAILURE:
    case UPDATE_USER_FAILURE:
    case DELETE_USER_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.error
      };
      
    default:
      return state;
  }
};

export default usersReducer;
