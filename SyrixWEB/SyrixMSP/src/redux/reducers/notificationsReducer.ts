import {
  FETCH_NOTIFICATIONS_REQUEST,
  FETCH_NOTIFICATIONS_SUCCESS,
  FETCH_NOTIFICATIONS_FAILURE,
  MARK_NOTIFICATION_READ,
  MARK_ALL_NOTIFICATIONS_READ,
  CLEAR_NOTIFICATION,
  ADD_NOTIFICATION
} from '../actions/types';
import { Notification } from '../../types';

interface NotificationsState {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
}

const initialState: NotificationsState = {
  notifications: [],
  loading: false,
  error: null
};

type NotificationsAction = 
  | { type: typeof FETCH_NOTIFICATIONS_REQUEST }
  | { type: typeof FETCH_NOTIFICATIONS_SUCCESS, payload: Notification[] }
  | { type: typeof FETCH_NOTIFICATIONS_FAILURE, error: string }
  | { type: typeof MARK_NOTIFICATION_READ, payload: string }
  | { type: typeof MARK_ALL_NOTIFICATIONS_READ }
  | { type: typeof CLEAR_NOTIFICATION, payload: string }
  | { type: typeof ADD_NOTIFICATION, payload: Notification };

const notificationsReducer = (
  state = initialState,
  action: NotificationsAction
): NotificationsState => {
  switch (action.type) {
    case FETCH_NOTIFICATIONS_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
      
    case FETCH_NOTIFICATIONS_SUCCESS:
      return {
        ...state,
        notifications: action.payload,
        loading: false,
        error: null
      };
      
    case FETCH_NOTIFICATIONS_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.error
      };
      
    case MARK_NOTIFICATION_READ:
      return {
        ...state,
        notifications: state.notifications.map(notification => 
          notification.id === action.payload 
            ? { ...notification, isRead: true }
            : notification
        )
      };
      
    case MARK_ALL_NOTIFICATIONS_READ:
      return {
        ...state,
        notifications: state.notifications.map(notification => ({ 
          ...notification, 
          isRead: true 
        }))
      };
      
    case CLEAR_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(
          notification => notification.id !== action.payload
        )
      };
      
    case ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [action.payload, ...state.notifications]
      };
      
    default:
      return state;
  }
};

export default notificationsReducer;
