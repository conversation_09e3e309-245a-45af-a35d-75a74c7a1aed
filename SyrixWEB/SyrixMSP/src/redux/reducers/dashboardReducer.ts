import {
  FETCH_DASHBOARD_DATA_REQUEST,
  FETCH_DASHBOARD_DATA_SUCCESS,
  FETCH_DASHBOARD_DATA_FAILURE,
  UPDATE_DASHBOARD_FILTER,
  RESET_DASHBOARD_FILTER
} from '../actions/types';
import { DashboardStats, ChartData } from '../../types';

interface DashboardState {
  stats: DashboardStats | null;
  chartData: ChartData | null;
  filters: Record<string, any>;
  loading: boolean;
  error: string | null;
}

const initialState: DashboardState = {
  stats: null,
  chartData: null,
  filters: {
    timeRange: 'month',
    startDate: null,
    endDate: null
  },
  loading: false,
  error: null
};

type DashboardAction = 
  | { type: typeof FETCH_DASHBOARD_DATA_REQUEST }
  | { type: typeof FETCH_DASHBOARD_DATA_SUCCESS, payload: { stats: DashboardStats, chartData: ChartData } }
  | { type: typeof FETCH_DASHBOARD_DATA_FAILURE, error: string }
  | { type: typeof UPDATE_DASHBOARD_FILTER, payload: { key: string, value: any } }
  | { type: typeof RESET_DASHBOARD_FILTER };

const dashboardReducer = (
  state = initialState,
  action: DashboardAction
): DashboardState => {
  switch (action.type) {
    case FETCH_DASHBOARD_DATA_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
      
    case FETCH_DASHBOARD_DATA_SUCCESS:
      return {
        ...state,
        stats: action.payload.stats,
        chartData: action.payload.chartData,
        loading: false,
        error: null
      };
      
    case FETCH_DASHBOARD_DATA_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.error
      };
      
    case UPDATE_DASHBOARD_FILTER:
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value
        }
      };
      
    case RESET_DASHBOARD_FILTER:
      return {
        ...state,
        filters: initialState.filters
      };
      
    default:
      return state;
  }
};

export default dashboardReducer;
