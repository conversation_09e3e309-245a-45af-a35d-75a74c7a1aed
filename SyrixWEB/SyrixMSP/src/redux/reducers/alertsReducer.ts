import {
  FETCH_ALERTS_REQUEST,
  FETCH_ALERTS_SUCCESS,
  FETCH_ALERTS_FAILURE,
  CREATE_ALERT,
  UPDATE_ALERT_STATUS,
  DELETE_ALERT
} from '../actions/types';
import { Alert } from '../../types';

interface AlertsState {
  alerts: Alert[];
  loading: boolean;
  error: string | null;
}

const initialState: AlertsState = {
  alerts: [],
  loading: false,
  error: null
};

type AlertsAction = 
  | { type: typeof FETCH_ALERTS_REQUEST }
  | { type: typeof FETCH_ALERTS_SUCCESS, payload: Alert[] }
  | { type: typeof FETCH_ALERTS_FAILURE, error: string }
  | { type: typeof CREATE_ALERT, payload: Alert }
  | { type: typeof UPDATE_ALERT_STATUS, payload: { id: string, status: string } }
  | { type: typeof DELETE_ALERT, payload: string };

const alertsReducer = (
  state = initialState,
  action: AlertsAction
): AlertsState => {
  switch (action.type) {
    case FETCH_ALERTS_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
      
    case FETCH_ALERTS_SUCCESS:
      return {
        ...state,
        alerts: action.payload,
        loading: false,
        error: null
      };
      
    case FETCH_ALERTS_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.error
      };
      
    case CREATE_ALERT:
      return {
        ...state,
        alerts: [...state.alerts, action.payload]
      };
      
    case UPDATE_ALERT_STATUS:
      return {
        ...state,
        alerts: state.alerts.map(alert => 
          alert.id === action.payload.id 
            ? { ...alert, status: action.payload.status as any }
            : alert
        )
      };
      
    case DELETE_ALERT:
      return {
        ...state,
        alerts: state.alerts.filter(alert => alert.id !== action.payload)
      };
      
    default:
      return state;
  }
};

export default alertsReducer;
