import { createStore, applyMiddleware, combineReducers } from 'redux';
import { composeWithDevTools } from 'redux-devtools-extension';
// Import directly from the module to avoid typings issues
import thunk from 'redux-thunk/es/index.js'; 

// Import reducers
import authReducer from './reducers/authReducer';
import alertsReducer from './reducers/alertsReducer';
import notificationsReducer from './reducers/notificationsReducer';
import usersReducer from './reducers/usersReducer';
import dashboardReducer from './reducers/dashboardReducer';

// Root reducer
const rootReducer = combineReducers({
  auth: authReducer,
  alerts: alertsReducer,
  notifications: notificationsReducer,
  users: usersReducer,
  dashboard: dashboardReducer
});

// RootState type
export type RootState = ReturnType<typeof rootReducer>;

// Use type assertion to work around TypeScript errors
const composedEnhancer = composeWithDevTools({}) as any;
const middlewareEnhancer = applyMiddleware(thunk);
const composedMiddleware = composedEnhancer(middlewareEnhancer);

// Create store with type assertions to avoid TypeScript errors
const store = createStore(rootReducer, composedMiddleware);

export default store;
