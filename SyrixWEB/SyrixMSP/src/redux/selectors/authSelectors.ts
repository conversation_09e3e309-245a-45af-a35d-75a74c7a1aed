import { createSelector } from 'reselect';
import { RootState } from '../store';
import { User, UserRole } from '../../types';

// Base selectors
export const selectAuthState = (state: RootState) => state.auth;
export const selectUser = (state: RootState) => state.auth.user;
export const selectToken = (state: RootState) => state.auth.token;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: RootState) => state.auth.loading;
export const selectAuthError = (state: RootState) => state.auth.error;

// Memoized selectors
export const selectUserRole = createSelector(
  [selectUser],
  (user: User | null): UserRole | null => user ? user.role : null
);

export const selectUserPermissions = createSelector(
  [selectUserRole],
  (role: UserRole | null): string[] => {
    switch (role) {
      case 'admin':
        return ['read', 'write', 'update', 'delete', 'manage_users', 'billing', 'settings'];
      case 'manager':
        return ['read', 'write', 'update', 'delete', 'manage_users'];
      case 'user':
        return ['read', 'write', 'update'];
      case 'readonly':
        return ['read'];
      default:
        return [];
    }
  }
);

export const selectIsAdmin = createSelector(
  [selectUserRole],
  (role: UserRole | null): boolean => role === 'admin'
);

export const selectIsManager = createSelector(
  [selectUserRole],
  (role: UserRole | null): boolean => role === 'admin' || role === 'manager'
);

export const selectHasPermission = (permission: string) => 
  createSelector(
    [selectUserPermissions],
    (permissions: string[]): boolean => permissions.includes(permission)
  );

export const selectUserName = createSelector(
  [selectUser],
  (user: User | null): string => user ? user.name : ''
);

export const selectUserInitials = createSelector(
  [selectUserName],
  (name: string): string => {
    if (!name) return '';
    return name
      .split(' ')
      .map((part: string) => part.charAt(0).toUpperCase())
      .join('')
      .substring(0, 2);
  }
);
