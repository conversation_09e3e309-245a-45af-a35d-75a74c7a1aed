import { Dispatch } from 'redux';
import { Alert } from '../../types';
import alertsService from '../../services/alertsService';
import {
  FETCH_ALERTS_REQUEST,
  FETCH_ALERTS_SUCCESS,
  FETCH_ALERTS_FAILURE,
  UPDATE_ALERT_STATUS
} from './types';

// Action interfaces
export interface FetchAlertsRequestAction {
  type: typeof FETCH_ALERTS_REQUEST;
}

export interface FetchAlertsSuccessAction {
  type: typeof FETCH_ALERTS_SUCCESS;
  payload: Alert[];
}

export interface FetchAlertsFailureAction {
  type: typeof FETCH_ALERTS_FAILURE;
  error: string;
}

export interface UpdateAlertStatusAction {
  type: typeof UPDATE_ALERT_STATUS;
  payload: {
    id: string;
    status: string;
  };
}

export type AlertActionTypes = 
  | FetchAlertsRequestAction
  | FetchAlertsSuccessAction
  | FetchAlertsFailureAction
  | UpdateAlertStatusAction;

// Action creators
export const fetchAlertsRequest = (): FetchAlertsRequestAction => ({
  type: FETCH_ALERTS_REQUEST
});

export const fetchAlertsSuccess = (alerts: Alert[]): FetchAlertsSuccessAction => ({
  type: FETCH_ALERTS_SUCCESS,
  payload: alerts
});

export const fetchAlertsFailure = (error: string): FetchAlertsFailureAction => ({
  type: FETCH_ALERTS_FAILURE,
  error
});

export const updateAlertStatusAction = (id: string, status: string): UpdateAlertStatusAction => ({
  type: UPDATE_ALERT_STATUS,
  payload: { id, status }
});

// Thunk actions
export const fetchAlerts = (status?: string, category?: string) => {
  return async (dispatch: Dispatch<AlertActionTypes>) => {
    dispatch(fetchAlertsRequest());
    
    try {
      const alerts = await alertsService.getAllAlerts(status, category);
      dispatch(fetchAlertsSuccess(alerts));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch alerts';
      dispatch(fetchAlertsFailure(errorMessage));
    }
  };
};

export const updateAlertStatus = (id: string, status: string) => {
  return async (dispatch: Dispatch<AlertActionTypes>) => {
    try {
      await alertsService.updateAlertStatus(id, status);
      dispatch(updateAlertStatusAction(id, status));
    } catch (error) {
      console.error('Failed to update alert status:', error);
      // You might want to dispatch an error action here
    }
  };
};

export const fetchAlertsBySeverity = (severity: string) => {
  return async (dispatch: Dispatch<AlertActionTypes>) => {
    dispatch(fetchAlertsRequest());
    
    try {
      const alerts = await alertsService.getAlertsBySeverity(severity);
      dispatch(fetchAlertsSuccess(alerts));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch alerts by severity';
      dispatch(fetchAlertsFailure(errorMessage));
    }
  };
};

export const fetchAlertsByCategory = (category: string) => {
  return async (dispatch: Dispatch<AlertActionTypes>) => {
    dispatch(fetchAlertsRequest());
    
    try {
      const alerts = await alertsService.getAlertsByCategory(category);
      dispatch(fetchAlertsSuccess(alerts));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch alerts by category';
      dispatch(fetchAlertsFailure(errorMessage));
    }
  };
};
