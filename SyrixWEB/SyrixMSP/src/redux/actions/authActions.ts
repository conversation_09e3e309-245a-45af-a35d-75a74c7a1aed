import { Dispatch } from 'redux';
import { ThunkAction, ThunkDispatch } from 'redux-thunk';
import { RootState } from '../store';
import { 
  LOGIN_REQUEST, 
  LOGIN_SUCCESS, 
  LOGIN_FAILURE, 
  LOGOUT, 
  REGISTER_REQUEST,
  REGISTER_SUCCESS,
  REGISTER_FAILURE,
  REFRESH_TOKEN,
  AUTH_ERROR,
  CLEAR_AUTH_ERROR
} from './types';
import { User } from '../../types';

// Define auth action interfaces
interface LoginRequestAction {
  type: typeof LOGIN_REQUEST;
}

interface LoginSuccessAction {
  type: typeof LOGIN_SUCCESS;
  payload: {
    user: User;
    token: string;
  };
}

interface LoginFailureAction {
  type: typeof LOGIN_FAILURE;
  error: string;
}

interface LogoutAction {
  type: typeof LOGOUT;
}

interface RegisterRequestAction {
  type: typeof REGISTER_REQUEST;
}

interface RegisterSuccessAction {
  type: typeof REGISTER_SUCCESS;
  payload: {
    user: User;
    token: string;
  };
}

interface RegisterFailureAction {
  type: typeof REGISTER_FAILURE;
  error: string;
}

interface RefreshTokenAction {
  type: typeof REFRESH_TOKEN;
  payload: {
    token: string;
  };
}

interface AuthErrorAction {
  type: typeof AUTH_ERROR;
  error: string;
}

interface ClearAuthErrorAction {
  type: typeof CLEAR_AUTH_ERROR;
}

// Export union type of all auth actions
export type AuthActionTypes = 
  | LoginRequestAction
  | LoginSuccessAction
  | LoginFailureAction
  | LogoutAction
  | RegisterRequestAction
  | RegisterSuccessAction
  | RegisterFailureAction
  | RefreshTokenAction
  | AuthErrorAction
  | ClearAuthErrorAction;

// Define AppThunk type
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  null,
  AuthActionTypes
>;
// Auth action creators
export const login = (email: string, password: string): AppThunk<Promise<{ user: User, token: string }>> => {
  return async (dispatch) => {
    try {
      dispatch({ type: LOGIN_REQUEST });
      
      // Simulated API call - replace with actual API call
      const response = await new Promise<{ user: User, token: string }>((resolve) => {
        setTimeout(() => {
          resolve({
            user: {
              id: '1',
              name: 'John Doe',
              email: email,
              role: 'admin',
              companyId: '1',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'simulated-jwt-token'
          });
        }, 800);
      });
      
      // Store token in localStorage
      localStorage.setItem('token', response.token);
      
      dispatch({
        type: LOGIN_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: LOGIN_FAILURE,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      
      throw error;
    }
  };
};

export const logout = (): AppThunk => {
  return (dispatch) => {
    // Remove token from localStorage
    localStorage.removeItem('token');
    
    dispatch({ type: LOGOUT });
  };
};

export const register = (name: string, email: string, password: string): AppThunk<Promise<{ user: User, token: string }>> => {
  return async (dispatch) => {
    try {
      dispatch({ type: REGISTER_REQUEST });
      
      // Simulated API call - replace with actual API call
      const response = await new Promise<{ user: User, token: string }>((resolve) => {
        setTimeout(() => {
          resolve({
            user: {
              id: '1',
              name: name,
              email: email,
              role: 'admin',
              companyId: '1',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'simulated-jwt-token'
          });
        }, 800);
      });
      
      // Store token in localStorage
      localStorage.setItem('token', response.token);
      
      dispatch({
        type: REGISTER_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: REGISTER_FAILURE,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
      
      throw error;
    }
  };
};

export const refreshToken = (token: string): AppThunk => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_TOKEN,
      payload: { token }
    });
  };
};

export const setAuthError = (error: string): AppThunk => {
  return (dispatch) => {
    dispatch({
      type: AUTH_ERROR,
      error
    });
  };
};

export const clearAuthError = (): AppThunk => {
  return (dispatch) => {
    dispatch({ type: CLEAR_AUTH_ERROR });
  };
};
