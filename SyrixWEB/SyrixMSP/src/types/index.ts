/**
 * Core application types for the Syrix MSP portal
 */

/**
 * User types
 */
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export type UserRole = 'admin' | 'manager' | 'user' | 'readonly';

/**
 * Authentication types
 */
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  token: string | null;
}

/**
 * Company types
 */
export interface Company {
  id: string;
  name: string;
  address: Address;
  contactEmail: string;
  contactPhone: string;
  subscription: Subscription;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export interface Subscription {
  id: string;
  plan: 'free' | 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'pending' | 'canceled' | 'expired';
  startDate: string;
  endDate: string;
  trialEndsAt?: string;
  isInTrial: boolean;
}

/**
 * Dashboard types
 */
export interface DashboardStats {
  activeUsers: number;
  totalAccounts: number;
  pendingInvites: number;
  activeAlerts: number;
  billingStatus: 'current' | 'overdue' | 'trial';
  uptimePercent: number;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  fill?: boolean;
}

/**
 * Alert types - Updated to match backend Alert model
 */
export interface Alert {
  id: string;
  title: string;
  description: string;
  severity: AlertSeverity;
  status: AlertStatus;
  category: AlertCategory;
  timestamp: string;
  source: string;
  affectedResource: string;
  affectedResourceType: string;
  assignedTo?: string;
  tags: string[];
}

export type AlertSeverity = 'critical' | 'high' | 'medium' | 'low';
export type AlertStatus = 'open' | 'in_progress' | 'resolved' | 'dismissed';
export type AlertCategory = 'security' | 'compliance' | 'configuration' | 'access' | 'data';

/**
 * Notification types
 */
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  createdAt: string;
  link?: string;
}

export type NotificationType = 'alert' | 'message' | 'update' | 'billing' | 'security';

/**
 * API Response types
 */
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
  timestamp: string;
}

/**
 * Redux Action types
 */
export interface Action<T extends string> {
  type: T;
}

export interface PayloadAction<T extends string, P> extends Action<T> {
  payload: P;
}

export interface ErrorAction<T extends string> extends Action<T> {
  error: string;
}

/**
 * Theme types
 */
export interface ThemeColors {
  text: string;
  background: string;
  sidebar: string;
  sidebarText: string;
  sidebarHover: string;
  border: string;
  cardBackground: string;
  activeItemTextColor: string;
}

export interface ThemeGradients {
  background: string;
  ellipse: string;
  success: string;
  warning: string;
  danger: string;
}

export interface Theme {
  name: 'light' | 'dark';
  colors: ThemeColors;
  gradients: ThemeGradients;
  sidebar: {
    width: string;
    activeItemBackground: string;
    activeItemTextColor: string;
  };
  fonts: {
    body: string;
    heading: string;
  };
  header: {
    height: string;
  };
}

/**
 * Component props types
 */
export interface ButtonProps {
  children: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  noPadding?: boolean;
  bordered?: boolean;
  elevated?: boolean;
  loading?: boolean;
}

export interface TableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  emptyMessage?: string;
  rowKey: keyof T | ((record: T) => string);
  onRowClick?: (record: T) => void;
  pagination?: TablePagination;
  onPaginationChange?: (page: number, pageSize: number) => void;
  sortable?: boolean;
  onSort?: (field: keyof T, order: 'asc' | 'desc') => void;
  sortField?: keyof T;
  sortOrder?: 'asc' | 'desc';
}

export interface TableColumn<T> {
  title: string;
  dataIndex: keyof T;
  key: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
}

export interface TablePagination {
  current: number;
  pageSize: number;
  total: number;
}