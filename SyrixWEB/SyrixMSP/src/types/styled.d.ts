import 'styled-components';

// Completely replace the DefaultTheme interface with an empty interface
// that extends our ThemeType (which will be imported from the context)
declare module 'styled-components' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface DefaultTheme {
    name: string;
    colors: any;
    gradients: any;
    sidebar: any;
    fonts: any;
    header: any;
    card: any;
    button: any;
    [key: string]: any;
  }
}
