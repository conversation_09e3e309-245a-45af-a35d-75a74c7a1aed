import { DefaultTheme } from 'styled-components';

// Extend the DefaultTheme interface from styled-components
declare module 'styled-components' {
  export interface DefaultTheme {
    name: string;
    colors: {
      background: string;
      cardBackground: string;
      primary: string;
      secondary: string;
      text: string;
      textSecondary: string;
      border: string;
      success: string;
      warning: string;
      danger: string;
      info: string;
      paid: string;
      due: string;
      overdue: string;
      active: string;
      expiring: string;
      expired: string;
      sidebar: string;
      sidebarText: string;
      sidebarHover: string;
      chartLine: string;
      chartFill: string;
      subtitleText: string;
      cardBackgroundOpacity: string;
      tableRowBg: string;
      cardTableBorder: string;
      cardText: string;
      chartLegendText: string;
    };
    header: {
      height: string;
      background: string;
      boxShadow: string;
    };
    sidebar: {
      width: string;
      background: string;
      activeItemBackground: string;
      activeItemTextColor: string;
    };
    card: {
      borderRadius: string;
      boxShadow: string;
    };
    button: {
      borderRadius: string;
      primaryBackground: string;
      primaryHover: string;
      secondaryBackground: string;
      secondaryHover: string;
    };
    fonts: {
      body: string;
      heading: string;
    };
    gradients: {
      background: string;
      success: string;
      warning: string;
      danger: string;
      ellipse: string;
    };
  }
}

export const lightTheme: DefaultTheme = {
  name: 'light',
  colors: {
    background: '#E0E8F9',
    cardBackground: '#FFFFFF',
    cardBackgroundOpacity: 'rgba(255, 255, 255, 0.4)',
    primary: '#006FFF',
    secondary: '#f0f2f5',
    text: '#3C3B45',
    textSecondary: '#666666',
    border: '#E0E9F5',
    success: '#2AB666',
    warning: '#E2DC3E',
    danger: '#E86768',
    info: '#2196f3',
    paid: '#2AB666',
    due: '#E2DC3E',
    overdue: '#E86768',
    active: '#2AB666',
    expiring: '#E2DC3E',
    expired: '#E86768',
    sidebar: '#CFDBF5',
    sidebarText: '#3C3B45',
    sidebarHover: 'rgba(255, 255, 255, 0.3)',
    chartLine: '#4CAF50',
    chartFill: 'rgba(76, 175, 80, 0.2)',
    subtitleText: '#E0E9F5',
    tableRowBg: 'rgba(0, 13, 38, 0.1)',
    cardTableBorder: 'rgba(0, 13, 38, 0.2)',
    cardText: '#3C3B45',
    chartLegendText: '#6F8AAB',
  },
  header: {
    height: '60px',
    background: '#E0E8F9',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
  },
  sidebar: {
    width: '260px',
    background: '#FFFFFF',
    activeItemBackground: '#006FFF',
    activeItemTextColor: '#FFFFFF'
  },
  card: {
    borderRadius: '12px',
    boxShadow: '0px 2px 10px 0px rgba(0, 0, 0, 0.15)'
  },
  button: {
    borderRadius: '4px',
    primaryBackground: '#006FFF',
    primaryHover: '#0062e6',
    secondaryBackground: '#f0f2f5',
    secondaryHover: '#e3e3e3'
  },
  fonts: {
    body: 'Poppins, Rubik, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    heading: 'Rubik, Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
  },
  gradients: {
    background: '#BBCBFF',
    success: 'linear-gradient(135deg, #05B8A0 0%, #2AB666 100%)',
    warning: 'linear-gradient(135deg, #F5DE6C 0%, #E2DC3E 100%)',
    danger: 'linear-gradient(135deg, #C93051 0%, #E86768 100%)',
    ellipse: 'rgba(255, 255, 255, 0.6)'
  }
};

export const darkTheme: DefaultTheme = {
  name: 'dark',
  colors: {
    background: '#10569B',
    cardBackground: '#093370',
    cardBackgroundOpacity: 'rgba(0, 0, 0, 0.3)',
    primary: '#006FFF',
    secondary: '#0D234B',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.7)',
    border: '#000D2633',
    success: '#2AB666',
    warning: '#E2DC3E',
    danger: '#E86768',
    info: '#2196f3',
    paid: '#2AB666',
    due: '#E2DC3E',
    overdue: '#E86768',
    active: '#2AB666',
    expiring: '#E2DC3E',
    expired: '#E86768',
    sidebar: '#10569B',
    sidebarText: '#FFFFFF',
    sidebarHover: 'rgba(255, 255, 255, 0.1)',
    chartLine: '#4CAF50',
    chartFill: 'rgba(76, 175, 80, 0.2)',
    subtitleText: 'rgba(255, 255, 255, 0.7)',
    tableRowBg: 'rgba(0, 13, 38, 0.2)',
    cardTableBorder: 'rgba(255, 255, 255, 0.2)',
    cardText: '#FFFFFF',
    chartLegendText: '#6F8AAB',
  },
  header: {
    height: '60px',
    background: 'rgba(0, 0, 0, 0.4)',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
  },
  sidebar: {
    width: '260px',
    background: '#10569B',
    activeItemBackground: 'rgba(18, 41, 89, 0.8)',
    activeItemTextColor: '#FFFFFF'
  },
  card: {
    borderRadius: '12px',
    boxShadow: '0px 2px 10px 0px rgba(0, 0, 0, 0.3)'
  },
  button: {
    borderRadius: '4px',
    primaryBackground: '#006FFF',
    primaryHover: '#0062e6',
    secondaryBackground: 'rgba(255, 255, 255, 0.1)',
    secondaryHover: 'rgba(255, 255, 255, 0.2)'
  },
  fonts: {
    body: 'Poppins, Rubik, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    heading: 'Rubik, Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
  },
  gradients: {
    background: 'linear-gradient(to bottom, #0e4a8e, #063c7c)',
    success: 'linear-gradient(135deg, #05B8A0 0%, #2AB666 100%)',
    warning: 'linear-gradient(135deg, #F5DE6C 0%, #E2DC3E 100%)',
    danger: 'linear-gradient(135deg, #C93051 0%, #E86768 100%)',
    ellipse: 'rgba(14, 67, 123, 0.5)'
  }
};

export type ThemeType = typeof lightTheme;
