import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import RiskAccounts from './dashboard/RiskAccounts';
import alertsService from '../services/alertsService';

const PageContainer = styled.div`
  padding: 30px;
`;

const PageHeader = styled.div`
  margin-bottom: 30px;
`;

const PageTitle = styled.h1`
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: ${props => props.theme.colors.text};
  font-family: ${props => props.theme.fonts.heading};
  letter-spacing: 1%;
  line-height: 1.4;
`;

const PageSubtitle = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.subtitleText};
  margin-top: 5px;
  font-family: ${props => props.theme.fonts.body};
  letter-spacing: 3.33%;
  line-height: 1.42;
`;

interface RiskAccount {
  id: string;
  name: string;
  logo: string;
  email: string;
  risk: number;
  records: string;
  status: 'danger' | 'warning' | 'success';
}

const Dashboard: React.FC = () => {
  const [riskAccounts, setRiskAccounts] = useState<RiskAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAccountRiskData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get account risk data from alerts service
        const accountData = await alertsService.getAccountRiskData();
        setRiskAccounts(accountData);

      } catch (err) {
        console.error('Error fetching account risk data:', err);
        setError('Failed to load account data');

        // Fallback to sample data if API fails
        const fallbackData: RiskAccount[] = [
          {
            id: '1',
            name: 'Microsoft Exchange Online',
            logo: 'https://ui-avatars.com/api/?name=EXO&background=0051a2&color=fff',
            email: '<EMAIL>',
            risk: 94,
            records: '3.13K',
            status: 'danger'
          },
          {
            id: '2',
            name: 'Microsoft Entra ID',
            logo: 'https://ui-avatars.com/api/?name=AAD&background=0051a2&color=fff',
            email: '<EMAIL>',
            risk: 73,
            records: '2.54K',
            status: 'danger'
          }
        ];
        setRiskAccounts(fallbackData);
      } finally {
        setLoading(false);
      }
    };

    fetchAccountRiskData();
  }, []);

  if (loading) {
    return (
      <PageContainer>
        <PageHeader>
          <PageTitle>MSP Portal</PageTitle>
          <PageSubtitle>Loading account data...</PageSubtitle>
        </PageHeader>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>MSP Portal</PageTitle>
        <PageSubtitle>
          {error
            ? 'Showing fallback data due to connection issues'
            : `Showing ${riskAccounts.length} accounts with security alerts`
          }
        </PageSubtitle>
      </PageHeader>

      <RiskAccounts
        title="Accounts"
        accounts={riskAccounts}
        pagination={{ current: 1, total: Math.ceil(riskAccounts.length / 10) }}
      />
    </PageContainer>
  );
};

export default Dashboard;