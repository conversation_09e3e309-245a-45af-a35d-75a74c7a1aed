import React from 'react';

interface SyrixLogoProps {
  theme: 'light' | 'dark';
  type: 'logo' | 'icon';
  height?: number;
}

const SyrixLogo: React.FC<SyrixLogoProps> = ({ theme, type, height = 32 }) => {
  // For simplicity, we'll use a simple SVG logo
  if (type === 'logo') {
    return (
      <svg 
        width={height * 3} 
        height={height} 
        viewBox="0 0 120 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <path 
          d="M20 10L35 20L20 30L5 20L20 10Z" 
          fill={theme === 'light' ? '#FFFFFF' : '#FFFFFF'} 
          stroke={theme === 'light' ? '#FFFFFF' : '#FFFFFF'}
        />
        <text 
          x="45" 
          y="25" 
          fontSize="20" 
          fontWeight="bold" 
          fill={theme === 'light' ? '#FFFFFF' : '#FFFFFF'}
        >
          SYRIX
        </text>
      </svg>
    );
  } else {
    // Icon only
    return (
      <svg 
        width={height} 
        height={height} 
        viewBox="0 0 40 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <path 
          d="M20 10L35 20L20 30L5 20L20 10Z" 
          fill={theme === 'light' ? '#FFFFFF' : '#FFFFFF'} 
          stroke={theme === 'light' ? '#FFFFFF' : '#FFFFFF'}
        />
      </svg>
    );
  }
};

export default SyrixLogo;