import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import styled from 'styled-components';
import { Notification, Alert } from '../types';
import { useTheme } from '../context/ThemeContext';
import { CloseIcon } from './icons';
import alertsService from '../services/alertsService';
import { selectUser } from '../redux/selectors/authSelectors';

interface NotificationsDropdownProps {
  onClose: () => void;
}

// Helper function to convert alerts to notifications
const convertAlertsToNotifications = (alerts: Alert[]): Notification[] => {
  return alerts.slice(0, 5).map(alert => ({
    id: alert.id,
    title: alert.title,
    message: alert.description.length > 100
      ? alert.description.substring(0, 100) + '...'
      : alert.description,
    type: alert.category === 'security' ? 'security' : 'alert',
    isRead: alert.status === 'resolved' || alert.status === 'dismissed',
    createdAt: alert.timestamp,
    link: `/alerts/${alert.id}`
  }));
};

const DropdownContainer = styled.div`
  position: absolute;
  top: 40px;
  right: -120px;
  width: 320px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  overflow: hidden;
  z-index: 9999;
  background-color: #FFFFFF;
  border: 1px solid #E2E8F0;
`;

const DropdownHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #E2E8F0;
  background-color: #FFFFFF;
`;

const DropdownTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  color: #1e293b;
  font-weight: bold;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const NotificationsList = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const NotificationItem = styled.div<{ isRead: boolean }>`
  padding: 12px 16px;
  border-bottom: 1px solid #E2E8F0;
  background-color: ${props => props.isRead ? '#F8FAFC' : '#FFFFFF'};
  opacity: ${props => props.isRead ? 0.8 : 1};
  cursor: pointer;
  
  &:hover {
    background-color: #F1F5F9;
  }
`;

const NotificationHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
`;

const UnreadIndicator = styled.span`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3B82F6;
  margin-right: 8px;
  display: inline-block;
`;

const TypeBadge = styled.span<{ type: string }>`
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
  margin-right: 8px;
  display: inline-block;
  text-transform: uppercase;
  font-weight: 500;
  background-color: ${props => {
    switch (props.type) {
      case 'alert': return '#EF4444';
      case 'security': return '#F59E0B';
      case 'update': return '#3B82F6';
      case 'billing': return '#10B981';
      case 'message': return '#8B5CF6';
      default: return '#6B7280';
    }
  }};
  color: white;
`;

const TimeStamp = styled.span`
  font-size: 11px;
  color: #64748b;
  margin-left: auto;
`;

const NotificationMessage = styled.p`
  margin: 0;
  font-size: 12px;
  color: #1e293b;
`;

const DropdownFooter = styled.div`
  padding: 12px 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.cardBackground};
`;

const ViewAllButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
`;

// Format time as "X minutes/hours/days ago"
const formatTime = (isoString: string): string => {
  const date = new Date(isoString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffMinutes < 60) {
    return `${diffMinutes} min ago`;
  } else if (diffMinutes < (60 * 24)) {
    return `${Math.floor(diffMinutes / 60)} hours ago`;
  } else {
    return `${Math.floor(diffMinutes / (60 * 24))} days ago`;
  }
};

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const user = useSelector(selectUser);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoading(true);

        // Get recent alerts and convert to notifications
        console.log('🔔 NotificationsDropdown: Fetching alerts from session-based API');
        
        const alerts = await alertsService.getAllAlerts();
        const alertNotifications = convertAlertsToNotifications(alerts);

        setNotifications(alertNotifications);

      } catch (error) {
        console.error('Error fetching notifications:', error);

        // Fallback to mock data
        const fallbackNotifications: Notification[] = [
          {
            id: '1',
            title: 'Policy Compliance Alert',
            message: 'Multiple policy compliance failures detected.',
            type: 'security',
            isRead: false,
            createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
            link: '/alerts'
          }
        ];
        setNotifications(fallbackNotifications);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if user is authenticated
    if (user) {
      fetchNotifications();
    }
  }, [user]); // Re-fetch when user changes
  
  return (
    <DropdownContainer style={{ 
      backgroundColor: theme.colors.cardBackground,
      border: `1px solid ${theme.colors.border}`
    }}>
      {/* Header */}
      <DropdownHeader>
        <DropdownTitle>
          Notifications
        </DropdownTitle>
        <CloseButton onClick={onClose}>
          <CloseIcon size={16} color={theme.colors.text} />
        </CloseButton>
      </DropdownHeader>
      
      {/* Notification list */}
      <NotificationsList>
        {loading ? (
          <NotificationItem isRead={false}>
            <NotificationMessage style={{ textAlign: 'center', padding: '20px' }}>
              Loading notifications...
            </NotificationMessage>
          </NotificationItem>
        ) : notifications.length === 0 ? (
          <NotificationItem isRead={false}>
            <NotificationMessage style={{ textAlign: 'center', padding: '20px' }}>
              No notifications available
            </NotificationMessage>
          </NotificationItem>
        ) : (
          notifications.map(notification => (
            <NotificationItem
              key={notification.id}
              isRead={notification.isRead}
            >
              <NotificationHeader>
                {!notification.isRead && <UnreadIndicator />}
                <TypeBadge type={notification.type}>
                  {notification.type}
                </TypeBadge>
                <span style={{ color: theme.colors.text }}>{notification.title}</span>
                <TimeStamp>
                  {formatTime(notification.createdAt)}
                </TimeStamp>
              </NotificationHeader>
              <NotificationMessage>
                {notification.message}
              </NotificationMessage>
            </NotificationItem>
          ))
        )}
      </NotificationsList>
      
      {/* Footer */}
      <DropdownFooter>
        <ViewAllButton>
          View all notifications
        </ViewAllButton>
      </DropdownFooter>
    </DropdownContainer>
  );
};

export default NotificationsDropdown;