import React from 'react';
import styled, { css } from 'styled-components';

type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'text';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const getVariantStyles = (variant: ButtonVariant, theme: any) => {
  switch (variant) {
    case 'primary':
      return css`
        background-color: ${theme.button.primaryBackground};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.button.primaryHover};
        }
      `;
    case 'secondary':
      return css`
        background-color: ${theme.button.secondaryBackground};
        color: ${theme.colors.text};
        border: 1px solid ${theme.colors.border};
        
        &:hover:not(:disabled) {
          background-color: ${theme.button.secondaryHover};
        }
      `;
    case 'success':
      return css`
        background-color: ${theme.colors.success};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.name === 'dark' ? 'rgba(76, 175, 80, 0.8)' : 'rgba(76, 175, 80, 0.8)'};
        }
      `;
    case 'danger':
      return css`
        background-color: ${theme.colors.danger};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.name === 'dark' ? 'rgba(244, 67, 54, 0.8)' : 'rgba(244, 67, 54, 0.8)'};
        }
      `;
    case 'warning':
      return css`
        background-color: ${theme.colors.warning};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.name === 'dark' ? 'rgba(255, 152, 0, 0.8)' : 'rgba(255, 152, 0, 0.8)'};
        }
      `;
    case 'info':
      return css`
        background-color: ${theme.colors.info};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.name === 'dark' ? 'rgba(33, 150, 243, 0.8)' : 'rgba(33, 150, 243, 0.8)'};
        }
      `;
    case 'text':
      return css`
        background-color: transparent;
        color: ${theme.colors.primary};
        
        &:hover:not(:disabled) {
          background-color: ${theme.name === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};
        }
      `;
    default:
      return css`
        background-color: ${theme.button.primaryBackground};
        color: white;
        
        &:hover:not(:disabled) {
          background-color: ${theme.button.primaryHover};
        }
      `;
  }
};

const getSizeStyles = (size: ButtonSize) => {
  switch (size) {
    case 'small':
      return css`
        padding: 6px 12px;
        font-size: 12px;
      `;
    case 'medium':
      return css`
        padding: 8px 16px;
        font-size: 14px;
      `;
    case 'large':
      return css`
        padding: 10px 20px;
        font-size: 16px;
      `;
    default:
      return css`
        padding: 8px 16px;
        font-size: 14px;
      `;
  }
};

const ButtonContainer = styled.button<{
  $variant: ButtonVariant;
  $size: ButtonSize;
  $fullWidth: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: ${props => props.theme.button.borderRadius};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  width: ${props => props.$fullWidth ? '100%' : 'auto'};
  
  ${props => getVariantStyles(props.$variant, props.theme)}
  ${props => getSizeStyles(props.$size)}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
`;

const LeftIconContainer = styled.span`
  display: flex;
  margin-right: 8px;
`;

const RightIconContainer = styled.span`
  display: flex;
  margin-left: 8px;
`;

const Spinner = styled.div`
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 2px solid white;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  ...props
}) => {
  return (
    <ButtonContainer
      $variant={variant}
      $size={size}
      $fullWidth={fullWidth}
      {...props}
    >
      {isLoading && <Spinner />}
      {!isLoading && leftIcon && (
        <LeftIconContainer>{leftIcon}</LeftIconContainer>
      )}
      {children}
      {!isLoading && rightIcon && (
        <RightIconContainer>{rightIcon}</RightIconContainer>
      )}
    </ButtonContainer>
  );
};

export default Button;
