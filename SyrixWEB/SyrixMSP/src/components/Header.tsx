import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme, ThemeType } from '../context/ThemeContext';
import { 
  SearchIcon, 
  SunIcon, 
  MoonIcon, 
  CloseIcon, 
  NotificationIcon, 
  AlertIcon,
  AccountsIcon 
} from './icons';
import NotificationsDropdown from './NotificationsDropdown';

// Import logos from assets
import syrix<PERSON>ogoLight from '../assets/Syrix Logo Dark.svg';
import syrixLogoDark from '../assets/Syrix Logo Dark Theme.svg';
import syrixIconLight from '../assets/Syrix Icon Dark.svg';
import syrixIconDark from '../assets/Syrix Icon Dark Theme.svg';

interface HeaderProps {
  userName: string;
  userRole: string;
}

const HeaderContainer = styled.header<{ theme: ThemeType }>`
  height: ${props => props.theme.header.height};
  background-color: ${props => props.theme.colors.headerBackground};
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 200px minmax(524px, 1fr) auto;
  align-items: center;
  padding: 0 28px;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 5;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  
  img {
    height: 32px;
    object-fit: contain;
  }
`;

const TrialNoticeContainer = styled.div<{ theme: ThemeType }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: ${props => props.theme.gradients.trialNotice};
  border-radius: 50px;
  border: 1px solid #CF8123;
  padding: 8px 12px;
  height: 40px;
  width: 524px;
  margin: 0 auto;
`;

const TrialInfoSection = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const TrialText = styled.span`
  color: white;
  font-size: 12px;
  font-family: 'Poppins';
`;

const TrialButtonsSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const EnableButton = styled.button`
  background-color: #10B981;
  color: white;
  font-size: 12px;
  font-family: 'Poppins';
  padding: 4px 12px;
  border-radius: 2px;
  border: none;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  cursor: pointer;
`;

const DemoButton = styled.button<{ theme: ThemeType }>`
  background: ${props => props.theme.gradients.primaryButton};
  color: white;
  font-size: 12px;
  font-family: 'Poppins';
  padding: 4px 12px;
  border-radius: 2px;
  border: none;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  cursor: pointer;
`;

const CloseTrialButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
`;

const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 30px;
`;

const IconsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  
  svg {
    cursor: pointer;
  }
`;

const ThemeToggleContainer = styled.div<{ theme: ThemeType }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  padding: 2px;
  background-color: ${props => props.theme.colors.themeToggleBackground};
  border-radius: 30px;
`;

const ThemeToggleOption = styled.div<{ isActive: boolean; theme: ThemeType }>`
  padding: ${props => props.isActive ? '0' : '6px'};
  background-color: ${props => props.isActive ? '#FFFFFF' : 'transparent'};
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${props => props.isActive ? 'transparent' : props.theme.colors.text};
  font-size: 12px;
`;

const UserProfile = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  text-align: left;
`;

const UserName = styled.div<{ theme: ThemeType }>`
  font-size: 16px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: ${props => props.theme.colors.text};
  line-height: 1.25;
`;

const UserRole = styled.div<{ theme: ThemeType }>`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-family: 'Poppins', sans-serif;
  color: ${props => props.theme.name === 'dark' ? '#67A9FF' : '#3B82F6'};
  letter-spacing: 0.05em;
`;

const SearchContainer = styled.div<{ theme: ThemeType }>`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid ${props => props.theme.name === 'dark' ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.1)'};
  width: 240px;
  color: ${props => props.theme.colors.text};
`;

const NotificationWrapper = styled.div`
  position: relative;
  cursor: pointer;
  margin-right: 10px;
`;

const NotificationBadge = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 5px;
  background-color: #FF4C4C;
  border-radius: 50%;
`;

// Mock trial notification data
const trialData = {
  daysLeft: 4,
  message: "days left on Free Trial"
};

const Header: React.FC<HeaderProps> = ({ userName, userRole }) => {
  const { theme, toggleTheme } = useTheme();
  const [showTrialNotice, setShowTrialNotice] = useState(true);
  const [showNotifications, setShowNotifications] = useState(false);
  
  const onToggleTheme = () => {
    toggleTheme();
  };

  const toggleNotificationsDropdown = () => {
    setShowNotifications(prev => !prev);
  };

  const isDarkMode = theme.name === 'dark';

  return (
    <HeaderContainer theme={theme}>
      {/* Left section - Logo */}
      <LogoContainer>
        <div style={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '12px',
          padding: '8px 0'
        }}>
          <img 
            src={isDarkMode ? syrixIconDark : syrixIconLight} 
            alt="Syrix Icon" 
            height={24}
          />
          <img 
            src={isDarkMode ? syrixLogoDark : syrixLogoLight} 
            alt="Syrix Logo" 
            height={21}
          />
        </div>
      </LogoContainer>

      {/* Center section - Trial notification */}
      {showTrialNotice && (
        <TrialNoticeContainer theme={theme}>
          <TrialInfoSection>
            <AlertIcon size={16} color="#FFFFFF" />
            <TrialText>
              {trialData.daysLeft} {trialData.message}
            </TrialText>
          </TrialInfoSection>
          
          <TrialButtonsSection>
            <EnableButton>
              🚀 Enable
            </EnableButton>
            
            <DemoButton theme={theme}>
              Book a Demo
            </DemoButton>
          </TrialButtonsSection>
          
          <CloseTrialButton onClick={() => setShowTrialNotice(false)}>
            <CloseIcon size={16} color="#FFFFFF" />
          </CloseTrialButton>
        </TrialNoticeContainer>
      )}

      {/* Right section - Actions */}
      <ActionsContainer>
        {/* Search */}
        <SearchContainer theme={theme} style={{ backgroundColor: theme.colors.searchBackground }}>
          <SearchIcon size={16} color={theme.colors.text} />
          <span style={{ 
            color: theme.colors.text, 
            fontSize: '12px',
            fontFamily: 'Poppins'
          }}>Search</span>
        </SearchContainer>

        {/* Notifications */}
        <IconsContainer>
          <NotificationWrapper onClick={toggleNotificationsDropdown}>
            <NotificationIcon 
              size={20} 
              color={theme.colors.text} 
            />
            <NotificationBadge />
            
            {/* Notification Dropdown */}
            {showNotifications && (
              <NotificationsDropdown onClose={() => setShowNotifications(false)} />
            )}
          </NotificationWrapper>

          <NotificationWrapper>
            <AlertIcon 
              size={20} 
              color={theme.colors.text} 
            />
          </NotificationWrapper>
        </IconsContainer>

        {/* Theme Toggle */}
        <ThemeToggleContainer theme={theme} onClick={onToggleTheme}>
          <ThemeToggleOption isActive={!isDarkMode} theme={theme}>
            <SunIcon size={10} color={!isDarkMode ? '#062448' : theme.colors.text} />
          </ThemeToggleOption>
          <ThemeToggleOption isActive={isDarkMode} theme={theme}>
            <MoonIcon size={10} color={isDarkMode ? '#FFFFFF' : theme.colors.text} />
          </ThemeToggleOption>
        </ThemeToggleContainer>
        
        {/* User Profile */}
        <UserProfile>
          <Avatar>
            {/* Using an avatar placeholder */}
            <img src="https://placehold.co/40x40" alt="User Avatar" />
          </Avatar>
          <UserInfo>
            <UserName theme={theme}>{userName}</UserName>
            <UserRole theme={theme}>
              <AccountsIcon size={14} color={isDarkMode ? '#67A9FF' : '#3B82F6'} />
              {userRole}
            </UserRole>
          </UserInfo>
        </UserProfile>
      </ActionsContainer>
    </HeaderContainer>
  );
};

export default Header;