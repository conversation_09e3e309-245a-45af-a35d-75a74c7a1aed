import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import styled from 'styled-components';
import { Alert } from '../types';
import alertsService from '../services/alertsService';
import { selectUser } from '../redux/selectors/authSelectors';

const PageContainer = styled.div`
  padding: 30px;
`;

const PageHeader = styled.div`
  margin-bottom: 30px;
`;

const PageTitle = styled.h1`
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: ${props => props.theme.colors.text};
  font-family: ${props => props.theme.fonts.heading};
  letter-spacing: 1%;
  line-height: 1.4;
`;

const PageSubtitle = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.subtitleText};
  margin-top: 5px;
  font-family: ${props => props.theme.fonts.body};
  letter-spacing: 3.33%;
  line-height: 1.42;
`;

const AlertsContainer = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  border-radius: 12px;
  border: 1px solid ${props => props.theme.colors.border};
  overflow: hidden;
`;

const AlertsHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const AlertsTitle = styled.h2`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 10px;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 14px;
`;

const AlertsList = styled.div`
  max-height: 600px;
  overflow-y: auto;
`;

const AlertItem = styled.div<{ severity: string }>`
  padding: 16px 20px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  border-left: 4px solid ${props => {
    switch (props.severity) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  }};
  
  &:hover {
    background: ${props => props.theme.colors.hoverBackground || '#F8FAFC'};
  }
`;

const AlertHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const AlertTitle = styled.h3`
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  flex: 1;
`;

const AlertMeta = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;

const SeverityBadge = styled.span<{ severity: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => {
    switch (props.severity) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  }};
  color: white;
`;

const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => {
    switch (props.status) {
      case 'open': return '#EF4444';
      case 'in_progress': return '#F59E0B';
      case 'resolved': return '#10B981';
      case 'dismissed': return '#6B7280';
      default: return '#6B7280';
    }
  }};
  color: white;
`;

const AlertDescription = styled.p`
  margin: 0;
  font-size: 12px;
  color: ${props => props.theme.colors.subtitleText};
  line-height: 1.4;
`;

const AlertFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: ${props => props.theme.colors.subtitleText};
`;

const LoadingMessage = styled.div`
  padding: 40px;
  text-align: center;
  color: ${props => props.theme.colors.subtitleText};
`;

const AlertsPage: React.FC = () => {
  const user = useSelector(selectUser);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('🔧 AlertsPage: Fetching alerts from session-based API');
        
        const alertsData = await alertsService.getAllAlerts();
        setAlerts(alertsData);
        setFilteredAlerts(alertsData);
        
      } catch (err) {
        console.error('Error fetching alerts:', err);
        setError('Failed to load alerts');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if user is authenticated
    if (user) {
      fetchAlerts();
    }
  }, [user]); // Re-fetch when user changes

  useEffect(() => {
    let filtered = alerts;

    if (severityFilter !== 'all') {
      filtered = filtered.filter(alert => alert.severity === severityFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(alert => alert.status === statusFilter);
    }

    setFilteredAlerts(filtered);
  }, [alerts, severityFilter, statusFilter]);

  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch {
      return timestamp;
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <PageHeader>
          <PageTitle>Security Alerts</PageTitle>
          <PageSubtitle>Loading alerts...</PageSubtitle>
        </PageHeader>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Security Alerts</PageTitle>
        <PageSubtitle>
          {error 
            ? 'Error loading alerts - showing fallback data' 
            : `Showing ${filteredAlerts.length} of ${alerts.length} alerts`
          }
        </PageSubtitle>
      </PageHeader>

      <AlertsContainer>
        <AlertsHeader>
          <AlertsTitle>Policy Compliance Alerts</AlertsTitle>
          <FilterContainer>
            <FilterSelect 
              value={severityFilter} 
              onChange={(e) => setSeverityFilter(e.target.value)}
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </FilterSelect>
            
            <FilterSelect 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="dismissed">Dismissed</option>
            </FilterSelect>
          </FilterContainer>
        </AlertsHeader>

        <AlertsList>
          {filteredAlerts.length === 0 ? (
            <LoadingMessage>
              {error ? 'No alerts available' : 'No alerts match the current filters'}
            </LoadingMessage>
          ) : (
            filteredAlerts.map(alert => (
              <AlertItem key={alert.id} severity={alert.severity}>
                <AlertHeader>
                  <AlertTitle>{alert.title}</AlertTitle>
                  <AlertMeta>
                    <SeverityBadge severity={alert.severity}>
                      {alert.severity}
                    </SeverityBadge>
                    <StatusBadge status={alert.status}>
                      {alert.status.replace('_', ' ')}
                    </StatusBadge>
                  </AlertMeta>
                </AlertHeader>
                
                <AlertDescription>
                  {alert.description.length > 200 
                    ? alert.description.substring(0, 200) + '...'
                    : alert.description
                  }
                </AlertDescription>
                
                <AlertFooter>
                  <span>Source: {alert.source}</span>
                  <span>Resource: {alert.affectedResource}</span>
                  <span>{formatTimestamp(alert.timestamp)}</span>
                </AlertFooter>
              </AlertItem>
            ))
          )}
        </AlertsList>
      </AlertsContainer>
    </PageContainer>
  );
};

export default AlertsPage;
