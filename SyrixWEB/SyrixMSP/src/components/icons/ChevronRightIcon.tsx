import React from 'react';
import { IconProps } from './DashboardIcon';

export const ChevronRightIcon: React.FC<IconProps> = ({ size = 20, color = 'currentColor', strokeWidth = 1.5 }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9 18L15 12L9 6" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default ChevronRightIcon;