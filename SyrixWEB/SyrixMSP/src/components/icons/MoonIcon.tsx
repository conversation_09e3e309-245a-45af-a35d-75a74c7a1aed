import React from 'react';
import { IconProps } from './DashboardIcon';

export const MoonIcon: React.FC<IconProps> = ({ size = 20, color = 'currentColor', strokeWidth = 1.5 }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 12.79C20.8427 14.4922 20.2039 16.1144 19.1582 17.4668C18.1126 18.8192 16.7035 19.8458 15.0957 20.4265C13.4879 21.0073 11.7479 21.1181 10.0796 20.7461C8.41127 20.3741 6.88299 19.5345 5.67423 18.3258C4.46546 17.117 3.62594 15.5887 3.25391 13.9204C2.88187 12.2521 2.99271 10.5121 3.57346 8.9043C4.15421 7.29651 5.18083 5.88737 6.53324 4.84175C7.88566 3.79614 9.5078 3.15731 11.21 3C10.2134 4.34827 9.73385 6.00945 9.85856 7.68141C9.98328 9.35338 10.7039 10.9251 11.8894 12.1106C13.0749 13.2961 14.6466 14.0167 16.3186 14.1414C17.9906 14.2662 19.6517 13.7866 21 12.79Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default MoonIcon;