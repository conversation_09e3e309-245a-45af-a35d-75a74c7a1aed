import React from 'react';
import { IconProps } from './DashboardIcon';

export const CloseIcon: React.FC<IconProps> = ({ size = 20, color = 'currentColor', strokeWidth = 1.5 }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18 6L6 18" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6 6L18 18" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default CloseIcon;