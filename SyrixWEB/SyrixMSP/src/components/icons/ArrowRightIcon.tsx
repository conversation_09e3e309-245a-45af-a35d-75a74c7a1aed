import React from 'react';
import { IconProps } from './DashboardIcon';

export const ArrowRightIcon: React.FC<IconProps> = ({ size = 20, color = 'currentColor', strokeWidth = 1.5 }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5 12H19" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12 5L19 12L12 19" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default ArrowRightIcon;