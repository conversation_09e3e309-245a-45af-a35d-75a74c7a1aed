.sidebar {
  width: 240px;
  height: calc(100vh - 60px); /* Account for header height */
  background-color: transparent;
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  border-right: 2px solid #0E437B;
  position: fixed;
  left: 0;
  top: 60px; /* Position below the header */
  z-index: 10;
}

.logo-container {
  padding: 60px 16px 24px; /* Add padding-top: 60px */
}

.logo {
  height: 32px;
}

.sidebar-menu {
  flex: 1;
  padding-top: 16px;
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  padding: 16px 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-menu li:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-menu li.active {
  background-color: rgba(13, 35, 75, 0.6);
}

.sidebar-menu li span {
  margin-left: 16px;
  font-weight: bold;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
}

.sidebar-footer {
  padding: 16px 24px;
  position: absolute;
  bottom: 0px;
  width: 100%;
  display: flex;
  align-items: center;
}

.portal-title {
  font-size: 24px;
  font-weight: 400;
  font-family: 'Poppins', sans-serif;
  color: white;
  margin: 40px 0 ;
  text-align: center;
}

.collapse-button {
  position: absolute;
  right: -12px;
  bottom: 60px; /* Change bottom to 10px */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #4169E1;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
  transform: rotate(180deg);
  z-index: 1000;
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar.collapsed .sidebar-menu li span {
  display: none;
}

.sidebar.collapsed .portal-title {
  display: none;
}

.sidebar.collapsed .collapse-button {
  transform: rotate(0deg);
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 12px;
    align-items: center;
  }

  .sidebar-menu {
    display: flex;
    padding: 0;
  }

  .sidebar-menu ul {
    display: flex;
  }

  .sidebar-menu li {
    padding: 8px 16px;
  }

  .sidebar-footer {
    display: none;
  }

  .sidebar.collapsed {
    width: 100%;
  }
}
