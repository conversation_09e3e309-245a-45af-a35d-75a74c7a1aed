import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { DashboardIcon, AccountsIcon, BillingIcon, SupportIcon, SettingsIcon, ChevronRightIcon } from '../icons';
import { useTheme } from '../../context/ThemeContext';
import styled from 'styled-components';

interface SidebarProps {
  activeItem?: string;
  onMenuSelect?: (item: string) => void;
}

interface SidebarContainerProps {
  collapsed: boolean;
}

const SidebarContainer = styled.div<SidebarContainerProps>`
  width: ${props => props.collapsed ? '80px' : '260px'};
  height: calc(100vh - ${props => props.theme.header.height});
  background-color: ${props => props.theme.colors.sidebar};
  color: ${props => props.theme.colors.sidebarText};
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  border-right: 1px solid rgba(8, 8, 8, 0.3);
  position: fixed;
  left: 0;
  top: ${props => props.theme.header.height};
  z-index: 10;
  
  @media (max-width: 768px) {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 12px;
    align-items: center;
  }
`;

const SidebarMenu = styled.nav`
  flex: 1;
  padding-top: 16px;
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
  }
  
  @media (max-width: 768px) {
    display: flex;
    padding: 0;
    
    ul {
      display: flex;
    }
  }
`;

interface MenuItemProps {
  active: boolean;
  isHovered: boolean;
  isSettings?: boolean;
}

const MenuItem = styled.li<MenuItemProps>`
  padding: 16px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: transparent;
  border-radius: 8px;
  margin: 4px 12px;
  position: relative;
  z-index: 2;
  
  span {
    margin-left: 16px;
    font-weight: 700;
    font-size: 18px;
    font-family: 'Rubik', sans-serif;
    color: ${props => {
      const isActive = props.active || props.isHovered;
      if (props.theme.name === 'light') {
        // Special case for Settings in light theme
        if (props.isSettings) {
          return '#2D82FF'; // Blue for Settings
        }
        return isActive ? '#2D82FF' : '#3C3B45';
      } else {
        return isActive ? '#FFFFFF' : props.theme.colors.sidebarText;
      }
    }};
    transition: color 0.2s ease;
  }
  
  @media (max-width: 768px) {
    padding: 8px 16px;
  }
`;

interface SidebarFooterProps {
  collapsed: boolean;
}

const SidebarFooter = styled.div<SidebarFooterProps>`
  padding: 16px 24px;
  position: absolute;
  bottom: 0px;
  width: 100%;
  display: ${props => props.collapsed ? 'none' : 'flex'};
  align-items: center;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const PortalTitle = styled.div`
  font-size: 24px;
  font-weight: 400;
  font-family: 'Poppins', sans-serif;
  color: ${props => props.theme.colors.sidebarText};
  margin: 40px 0;
  text-align: center;
`;

interface CollapseButtonProps {
  collapsed: boolean;
}

const CollapseButton = styled.button<CollapseButtonProps>`
  position: absolute;
  right: -16px;
  bottom: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #4169E1;
  border: 2px solid #FFFFFF;
  border-radius: 50%;
  cursor: pointer;
  color: white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
  transform: ${props => props.collapsed ? 'rotate(0deg)' : 'rotate(180deg)'};
  z-index: 1000;
`;

const MenuSelector = styled.div`
  position: absolute;
  background-color: ${props => props.theme.name === 'dark' ? '#122959' : '#969696'};
  border-radius: 8px;
  height: 52px;
  left: 12px;
  right: 12px;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
`;

const Sidebar: React.FC<SidebarProps> = ({ activeItem = 'dashboard', onMenuSelect }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [internalActiveItem, setInternalActiveItem] = useState(activeItem);
  const { theme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isDark = theme.name === 'dark';
  const menuRefs = useRef<{ [key: string]: HTMLLIElement | null }>({});
  const selectorRef = useRef<HTMLDivElement>(null);
  
  // Init menu refs
  const menuItems = ['dashboard', 'accounts', 'billing', 'support', 'settings'];
  
  // Update internal state based on current route
  useEffect(() => {
    const currentPath = location.pathname;
    let currentMenuItem = 'dashboard';
    
    if (currentPath === '/') currentMenuItem = 'dashboard';
    else if (currentPath === '/alerts' || currentPath === '/accounts') currentMenuItem = 'accounts';
    else if (currentPath === '/billing') currentMenuItem = 'billing';
    else if (currentPath === '/support') currentMenuItem = 'support';
    else if (currentPath === '/settings') currentMenuItem = 'settings';
    
    setInternalActiveItem(currentMenuItem);
  }, [location.pathname]);
  
  // Update internal state when prop changes
  useEffect(() => {
    if (activeItem !== internalActiveItem) {
      setInternalActiveItem(activeItem);
    }
  }, [activeItem, internalActiveItem]);
  
  useEffect(() => {
    // Set initial selector position
    updateSelectorPosition(internalActiveItem);
    
    // Set special styling for Settings if needed
    if (theme.name === 'light' && menuRefs.current['settings']) {
      const settingsItem = menuRefs.current['settings'];
      const textElements = settingsItem.querySelectorAll('span');
      textElements.forEach(el => {
        el.style.color = '#2D82FF';
      });
    }
  }, [internalActiveItem, collapsed, theme.name]);
  
  const updateSelectorPosition = (item: string) => {
    const currentRef = menuRefs.current[item];
    const selector = selectorRef.current;
    
    if (currentRef && selector) {
      const menuItemTop = currentRef.offsetTop;
      selector.style.transform = `translateY(${menuItemTop}px)`;
      // Force a repaint to ensure smooth transition
      selector.getBoundingClientRect();
      
      // Ensure we're getting the right color for active menu items
      if (item === internalActiveItem || item === hoveredItem) {
        if (theme.name === 'light') {
          // For light theme: blue text for active items
          const textElements = currentRef.querySelectorAll('span');
          textElements.forEach(el => {
            el.style.color = '#2D82FF';
          });
        } else {
          // For dark theme: white text
          const textElements = currentRef.querySelectorAll('span');
          textElements.forEach(el => {
            el.style.color = '#FFFFFF';
          });
        }
      }
    }
  };

  const handleMenuItemClick = (item: string) => {
    // Update internal state first
    setInternalActiveItem(item);
    
    // Navigate to the appropriate route
    switch (item) {
      case 'dashboard':
        navigate('/');
        break;
      case 'accounts':
        navigate('/alerts'); // Accounts menu shows alerts page
        break;
      case 'billing':
        navigate('/billing');
        break;
      case 'support':
        navigate('/support');
        break;
      case 'settings':
        navigate('/settings');
        break;
    }
    
    // Then notify parent if callback exists
    if (onMenuSelect) {
      onMenuSelect(item);
    }
    
    // Update selector position
    updateSelectorPosition(item);
  };
  
  const handleMenuItemHover = (item: string) => {
    // Don't do anything if already hovering over this item
    if (hoveredItem === item) return;
    
    setHoveredItem(item);
    updateSelectorPosition(item);
  };
  
  const handleMenuItemLeave = () => {
    // Only reset hovered item, don't immediately move selector
    setHoveredItem(null);
    
    // Add a small delay before moving the selector back
    // This prevents the jumping when moving between items
    setTimeout(() => {
      // Only move selector if no item is being hovered
      if (!hoveredItem) {
        updateSelectorPosition(internalActiveItem);
      }
    }, 50);
  };

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const getIconColor = (item: string) => {
    const isActiveOrHovered = internalActiveItem === item || hoveredItem === item;
    
    if (theme.name === 'light') {
      // Light theme
      if (isActiveOrHovered) {
        return '#2D82FF'; // Blue for active items
      } else if (item === 'settings' && !isActiveOrHovered) {
        return '#2D82FF'; // Blue for Settings even when not active (per mockup)
      } else {
        return '#FFFFFF'; // White for non-active items
      }
    } else {
      // Dark theme
      return isActiveOrHovered ? '#FFFFFF' : theme.colors.sidebarText;
    }
  };

  const handleSidebarMouseLeave = () => {
    // When mouse leaves the entire sidebar, reset everything
    setHoveredItem(null);
    updateSelectorPosition(internalActiveItem);
  };

  return (
    <SidebarContainer 
      collapsed={collapsed} 
      onMouseLeave={handleSidebarMouseLeave}
    >
      <SidebarMenu>
        <ul>
          <MenuSelector ref={selectorRef} />
          {menuItems.map(item => (
            <MenuItem 
              key={item}
              ref={el => menuRefs.current[item] = el}
              active={internalActiveItem === item} 
              isHovered={hoveredItem === item}
              isSettings={item === 'settings'}
              onClick={() => handleMenuItemClick(item)}
              onMouseEnter={() => handleMenuItemHover(item)}
              onMouseLeave={handleMenuItemLeave}
            >
              <div style={{ width: '24px', display: 'flex', justifyContent: 'center' }}>
                {item === 'dashboard' && <DashboardIcon size={22} color={getIconColor(item)} />}
                {item === 'accounts' && <AccountsIcon size={22} color={getIconColor(item)} />}
                {item === 'billing' && <BillingIcon size={22} color={getIconColor(item)} />}
                {item === 'support' && <SupportIcon size={22} color={getIconColor(item)} />}
                {item === 'settings' && <SettingsIcon size={22} color={getIconColor(item)} />}
              </div>
              {!collapsed && <span>{item.charAt(0).toUpperCase() + item.slice(1)}</span>}
            </MenuItem>
          ))}
        </ul>
      </SidebarMenu>
      
      <SidebarFooter collapsed={collapsed}>
        <PortalTitle>MSP Portal</PortalTitle>
      </SidebarFooter>
      
      <CollapseButton collapsed={collapsed} onClick={toggleSidebar}>
        <ChevronRightIcon size={20} color="#FFFFFF" />
      </CollapseButton>
    </SidebarContainer>
  );
};

export default Sidebar;