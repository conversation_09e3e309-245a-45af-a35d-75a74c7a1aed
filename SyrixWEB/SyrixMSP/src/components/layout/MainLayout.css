.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: linear-gradient(to bottom, #0e4a8e, #063c7c);
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  flex: 1;
  position: relative;
}

.page-content {
  flex: 1;
  padding: 20px;
  padding-left: 260px; /* Add padding to account for sidebar width */
  overflow-y: auto;
  height: calc(100vh - 60px); /* Subtract header height */
  margin-top: 60px; /* Add margin to account for header height */
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .page-content {
    padding-left: 20px;
  }
}
