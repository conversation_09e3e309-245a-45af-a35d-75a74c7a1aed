import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import '../../components/icons';

// Let's create a simpler header component
interface HeaderProps {
  userName?: string;
  userRole?: string;
}

const Header: React.FC<HeaderProps> = ({ userName = "<PERSON>", userRole = "Account Setting" }) => {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <div style={{
      backgroundColor: theme.name === 'light' ? '#FFFFFF' : '#13294B',
      height: '60px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 20px',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 1000,
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
    }}>
      <div style={{ fontWeight: 'bold', fontSize: '20px', color: theme.name === 'light' ? '#333' : 'white' }}>
        SYRIX
      </div>
      
      <div style={{ 
        position: 'absolute', 
        left: '50%', 
        transform: 'translateX(-50%)',
        backgroundColor: '#4CAF50',
        padding: '8px 16px',
        borderRadius: '4px',
        color: 'white'
      }}>
        Some very important information
      </div>
      
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <div style={{
          position: 'relative',
          width: '240px',
          height: '36px',
          backgroundColor: theme.name === 'light' ? '#F5F5F5' : 'rgba(255, 255, 255, 0.1)',
          borderRadius: '20px',
          display: 'flex',
          alignItems: 'center',
          paddingLeft: '36px'
        }}>
          <span style={{ color: theme.name === 'light' ? '#999' : 'rgba(255, 255, 255, 0.6)' }}>Search</span>
        </div>
        
        <div style={{ position: 'relative' }}>
          <span style={{ 
            position: 'absolute', 
            top: 0, 
            right: 0, 
            width: '8px', 
            height: '8px', 
            backgroundColor: 'red', 
            borderRadius: '50%' 
          }}></span>
        </div>
        
        <div onClick={toggleTheme} style={{ display: 'flex', alignItems: 'center', gap: '4px', cursor: 'pointer' }}>
          <span style={{ fontSize: '14px', color: theme.name === 'light' ? '#333' : 'white' }}>
            {theme.name === 'light' ? 'Dark' : 'Light'}
          </span>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div style={{ 
            width: '40px', 
            height: '40px', 
            borderRadius: '50%', 
            backgroundColor: '#ddd',
            overflow: 'hidden'
          }}>
            <img 
              src="https://i.pravatar.cc/100?img=23" 
              alt="User" 
              style={{ width: '100%', height: '100%', objectFit: 'cover' }} 
            />
          </div>
          <div>
            <div style={{ fontWeight: 'bold', fontSize: '14px', color: theme.name === 'light' ? '#333' : 'white' }}>
              {userName}
            </div>
            <div style={{ fontSize: '12px', color: theme.name === 'light' ? '#777' : 'rgba(255, 255, 255, 0.7)' }}>
              {userRole}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
