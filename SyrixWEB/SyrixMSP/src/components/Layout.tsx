import React from 'react';
import styled from 'styled-components';
import Sidebar from './layout/Sidebar'; // Corrected path

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${props => props.theme.gradients.background};
`;

const MainContent = styled.main`
  flex: 1;
  margin-left: ${props => props.theme.sidebar.width};
  background-color: transparent;
  min-height: 100vh;
  position: relative;
  z-index: 1;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  padding: 20px;
`;

// Decorative element resembling an ellipse from the Figma design
const BackgroundEllipse = styled.div`
  position: fixed;
  top: 40%;
  left: 50%;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background-color: ${props => props.theme.gradients.ellipse};
  filter: blur(234px);
  z-index: 0;
  transform: translate(-50%, -50%);
`;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <LayoutContainer>
      <BackgroundEllipse />
      <MainContent>
        <ContentWrapper>
          {children}
        </ContentWrapper>
      </MainContent>
    </LayoutContainer>
  );
};

export default Layout;
