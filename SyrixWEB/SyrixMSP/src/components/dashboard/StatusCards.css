.status-cards {
  display: flex;
  gap: 10px;
}

.status-card {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

.status-card.paid {
  background: linear-gradient(to right, #10b981, #5eead4);
  color: white;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
}

.status-card.due {
  background: linear-gradient(to right, #f59e0b, #fcd34d);
  color: white;
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.2);
}

.status-card.overdue {
  background: linear-gradient(to right, #ef4444, #fb7185);
  color: white;
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
}

.status-card-header {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 16px;
}

.status-card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.status-value-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
}

.status-period {
  font-size: 12px;
  opacity: 0.8;
}

.status-divider {
  width: 1px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.3);
}

@media (max-width: 768px) {
  .status-cards {
    flex-direction: column;
  }
}