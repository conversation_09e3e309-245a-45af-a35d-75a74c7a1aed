.accounts-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  display: flex;
  padding: 12px 0;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  margin-bottom: 8px;
}

.header-cell {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-secondary);
  padding: 0 16px;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: var(--hover-bg);
}

.table-cell {
  padding: 0 16px;
  font-size: 14px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.account-name {
  flex: 2;
}

.email {
  flex: 2;
}

.risk {
  flex: 1;
  justify-content: center;
}

.records {
  flex: 1;
  justify-content: center;
}

.account-logo {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 12px;
  background-color: var(--card-background);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.account-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-placeholder {
  font-weight: 600;
  font-size: 16px;
  color: var(--text-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.risk-value {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.high-risk {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #ef4444;
}

.medium-high-risk {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid #f59e0b;
}

.medium-risk {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border-left: 4px solid #10b981;
}

.low-risk {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3b82f6;
}

@media (max-width: 768px) {
  .accounts-table {
    overflow-x: auto;
  }
  
  .email {
    display: none;
  }
}