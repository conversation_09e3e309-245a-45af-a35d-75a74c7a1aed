import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import './StatusCards.css';
import './StatusCards.dark.css';

const StatusCards: React.FC = () => {
  const { theme } = useTheme();
  
  return (
    <div className={`status-cards ${theme.name}-theme`}>
      <div className="status-card paid">
        <div className="status-card-header">Paid</div>
        <div className="status-card-body">
          <div className="status-value-container">
            <div className="status-value">34</div>
            <div className="status-period">monthly</div>
          </div>
          <div className="status-divider"></div>
          <div className="status-value-container">
            <div className="status-value">23</div>
            <div className="status-period">yearly</div>
          </div>
        </div>
      </div>
      
      <div className="status-card due">
        <div className="status-card-header">Due</div>
        <div className="status-card-body">
          <div className="status-value-container">
            <div className="status-value">34</div>
            <div className="status-period">monthly</div>
          </div>
          <div className="status-divider"></div>
          <div className="status-value-container">
            <div className="status-value">23</div>
            <div className="status-period">yearly</div>
          </div>
        </div>
      </div>
      
      <div className="status-card overdue">
        <div className="status-card-header">Overdue</div>
        <div className="status-card-body">
          <div className="status-value-container">
            <div className="status-value">34</div>
            <div className="status-period">monthly</div>
          </div>
          <div className="status-divider"></div>
          <div className="status-value-container">
            <div className="status-value">23</div>
            <div className="status-period">yearly</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusCards;