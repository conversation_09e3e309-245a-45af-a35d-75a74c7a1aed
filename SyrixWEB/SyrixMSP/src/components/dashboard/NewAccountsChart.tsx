import React, { useState } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Dot } from 'recharts';
import './NewAccountsChart.css';


// Mock data for different time frames
const mockDataTimeframes = {
  'Last Year': [
    { month: 'Jan', accounts: 40 },
    { month: 'Feb', accounts: 55 },
    { month: 'Mar', accounts: 120 },
    { month: 'Apr', accounts: 110 },
    { month: 'May', accounts: 75 },
    { month: 'Jun', accounts: 90 },
    { month: 'Jul', accounts: 120 },
    { month: 'Aug', accounts: 130 },
    { month: 'Sep', accounts: 95 },
    { month: 'Oct', accounts: 130 },
    { month: 'Nov', accounts: 135 },
    { month: 'Dec', accounts: 180 },
  ],
  'Last Month': [
    { month: '1', accounts: 10 },
    { month: '2', accounts: 12 },
    { month: '3', accounts: 15 },
    { month: '4', accounts: 18 },
    { month: '5', accounts: 20 },
    { month: '6', accounts: 22 },
    { month: '7', accounts: 25 },
    { month: '8', accounts: 28 },
    { month: '9', accounts: 30 },
    { month: '10', accounts: 32 },
    { month: '11', accounts: 35 },
    { month: '12', accounts: 33 },
    { month: '13', accounts: 36 },
    { month: '14', accounts: 38 },
    { month: '15', accounts: 40 },
    { month: '16', accounts: 42 },
    { month: '17', accounts: 45 },
    { month: '18', accounts: 43 },
    { month: '19', accounts: 46 },
    { month: '20', accounts: 48 },
    { month: '21', accounts: 50 },
    { month: '22', accounts: 52 },
    { month: '23', accounts: 55 },
    { month: '24', accounts: 53 },
    { month: '25', accounts: 56 },
    { month: '26', accounts: 58 },
    { month: '27', accounts: 60 },
    { month: '28', accounts: 62 },
    { month: '29', accounts: 65 },
    { month: '30', accounts: 63 },

  ],
  'Last Week': [
    { month: 'Mon', accounts: 12 },
    { month: 'Tue', accounts: 15 },
    { month: 'Wed', accounts: 23 },
    { month: 'Thu', accounts: 26 },
    { month: 'Fri', accounts: 30 },
    { month: 'Sat', accounts: 38 },
    { month: 'Sun', accounts: 45 },
  ]
};

interface NewAccountsChartProps {
  data?: typeof mockDataTimeframes['Last Year'];
}

const NewAccountsChart: React.FC<NewAccountsChartProps> = ({ data }) => {
  const [timeframe, setTimeframe] = useState<string>('Last Year');
  const currentData = data || mockDataTimeframes[timeframe as keyof typeof mockDataTimeframes];


  const handleTimeframeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeframe(event.target.value);
  };


  return (
    <div className="chart-container">
      <div className="chart-header">
        <h3 className="chart-title">New accounts</h3>
        <div className="year-selector">
          <select 
            value={timeframe} 
            onChange={handleTimeframeChange} 
            className="timeframe-selector"
          >
            <option value="Last Year">Last Year</option>
            <option value="Last Month">Last Month</option>
            <option value="Last Week">Last Week</option>
          </select>
        </div>
      </div>

      <div className="chart-grid">
        {/* Y-axis values are now rendered directly without the separate labels div */}
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={currentData} margin={{ top: 5, right: 20, left: -25, bottom: -20 }}>
            <defs>
              <linearGradient id="colorAccounts" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse">
                <stop stopColor="#05B8A0" />
                <stop offset="1" stopColor="#2AB666" />
              </linearGradient>
              <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1" gradientUnits="userSpaceOnUse">
                <stop offset="0%" stopColor="#05B8A0" stopOpacity="0.5" />
                <stop offset="95%" stopColor="#05B8A0" stopOpacity="0.4" />
              </linearGradient>
            </defs>
            
            <CartesianGrid strokeDasharray="1 1" stroke="#080525" vertical={true} horizontal={true} />
            
            <XAxis 
              dataKey="month" 
              axisLine={false}
              tickLine={false}
              tick={{ fill: 'rgba(255,255,255,0.6)', fontSize: 12 }}
              stroke="rgba(255,255,255,0.3)"
              dy={10}
              height={50}
            />
            
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fill: 'rgba(255,255,255,0.6)', fontSize: 12 }}
              stroke="rgba(255,255,255,0.3)"
              domain={[0, 200]}
              ticks={[0, 50, 100, 150, 200]}
            />
            
            <Tooltip 
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="custom-tooltip">
                      <p className="indicator-date">{payload[0].payload.month}{timeframe === 'Last Year' ? ' 13' : ''}</p>
                      <p className="indicator-value">Accounts - {payload[0].value}</p>
                    </div>
                  );
                }
                return null;
              }}
              isAnimationActive={false}
            />
            
            <Area 
              type="monotone" 
              dataKey="accounts" 
              stroke="#4eca8e" 
              strokeWidth={4}
              fill="url(#areaGradient)"
              fillOpacity={1}
              activeDot={{ r: 8, fill: "#4eca8e", stroke: "white", strokeWidth: 2 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default NewAccountsChart;