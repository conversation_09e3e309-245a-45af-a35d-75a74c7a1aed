import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useTheme } from '../../context/ThemeContext';
import alertsService from '../../services/alertsService';
import { selectUser } from '../../redux/selectors/authSelectors';
import './AccountsTable.css';
import './AccountsTable.dark.css';

interface Account {
  id: string;
  name: string;
  email: string;
  risk: number;
  records: string;
  logo: string;
}

const AccountsTable: React.FC = () => {
  const { theme } = useTheme();
  const user = useSelector(selectUser);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoading(true);

        // Get account risk data from alerts service
        console.log('📋 AccountsTable: Fetching account risk data from session-based API');
        
        const accountData = await alertsService.getAccountRiskData();

        // Transform to match Account interface
        const transformedAccounts: Account[] = accountData.map(account => ({
          id: account.id,
          name: account.name,
          email: account.email,
          risk: account.risk,
          records: account.records,
          logo: account.logo
        }));

        setAccounts(transformedAccounts);

      } catch (error) {
        console.error('Error fetching accounts:', error);

        // Fallback to sample data
        const fallbackAccounts: Account[] = [
          {
            id: '1',
            name: 'Microsoft Exchange Online',
            email: '<EMAIL>',
            risk: 94,
            records: '3.13K',
            logo: 'https://ui-avatars.com/api/?name=EXO&background=0051a2&color=fff'
          },
          {
            id: '2',
            name: 'Microsoft Entra ID',
            email: '<EMAIL>',
            risk: 73,
            records: '2.54K',
            logo: 'https://ui-avatars.com/api/?name=AAD&background=0051a2&color=fff'
          }
        ];
        setAccounts(fallbackAccounts);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if user is authenticated
    if (user) {
      fetchAccounts();
    }
  }, [user]); // Re-fetch when user changes

  // Function to determine risk color class
  const getRiskColorClass = (risk: number): string => {
    if (risk >= 80) return 'high-risk';
    if (risk >= 60) return 'medium-high-risk';
    if (risk >= 40) return 'medium-risk';
    return 'low-risk';
  };

  if (loading) {
    return (
      <div className={`accounts-table ${theme.name}-theme`}>
        <div className="table-header">
          <div className="header-cell account-name">Account name</div>
          <div className="header-cell email">Email</div>
          <div className="header-cell risk">Risk</div>
          <div className="header-cell records">Records</div>
        </div>
        <div className="table-body">
          <div className="table-row">
            <div className="table-cell" style={{ textAlign: 'center', padding: '20px', gridColumn: '1 / -1' }}>
              Loading accounts...
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`accounts-table ${theme.name}-theme`}>
      <div className="table-header">
        <div className="header-cell account-name">Account name</div>
        <div className="header-cell email">Email</div>
        <div className="header-cell risk">Risk</div>
        <div className="header-cell records">Records</div>
      </div>

      <div className="table-body">
        {accounts.length === 0 ? (
          <div className="table-row">
            <div className="table-cell" style={{ textAlign: 'center', padding: '20px', gridColumn: '1 / -1' }}>
              No accounts found
            </div>
          </div>
        ) : (
          accounts.map((account) => (
            <div className="table-row" key={account.id}>
              <div className="table-cell account-name">
                <div className="account-logo">
                  {/* Fallback to first letter of company name if logo not available */}
                  {account.logo ? (
                    <img src={account.logo} alt={account.name} />
                  ) : (
                    <div className="logo-placeholder">{account.name.charAt(0)}</div>
                  )}
                </div>
                <span>{account.name}</span>
              </div>
              <div className="table-cell email">{account.email}</div>
              <div className="table-cell risk">
                <span className={`risk-value ${getRiskColorClass(account.risk)}`}>
                  {account.risk}%
                </span>
              </div>
              <div className="table-cell records">{account.records}</div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AccountsTable;