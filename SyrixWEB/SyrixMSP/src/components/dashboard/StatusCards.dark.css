/* Dark theme styling for StatusCards */
.dark-theme .status-cards {
  display: flex;
  gap: 10px;
}

.dark-theme .status-card {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  background-color: #093370;
}

.dark-theme .status-card.paid {
  background: linear-gradient(135deg, #05B8A0 0%, #2AB666 100%);
  color: white;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
}

.dark-theme .status-card.due {
  background: linear-gradient(135deg, #F5DE6C 0%, #E2DC3E 100%);
  color: white;
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.2);
}

.dark-theme .status-card.overdue {
  background: linear-gradient(135deg, #C93051 0%, #E86768 100%);
  color: white;
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
}

.dark-theme .status-card-header {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 16px;
}

.dark-theme .status-card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.dark-theme .status-value-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dark-theme .status-value {
  font-size: 24px;
  font-weight: 700;
}

.dark-theme .status-period {
  font-size: 12px;
  opacity: 0.8;
}

.dark-theme .status-divider {
  width: 1px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.3);
}
