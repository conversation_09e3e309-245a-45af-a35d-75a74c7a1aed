import React, { useContext } from 'react';
import styled, { useTheme } from 'styled-components';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ChevronDownIcon } from '../icons';

const CardContainer = styled.div`
  background-color: ${props => props.theme.colors.cardBackgroundOpacity};
  border-radius: ${props => props.theme.card.borderRadius};
  box-shadow: ${props => props.theme.card.boxShadow};
  padding: 20px;
  height: 100%;
  backdrop-filter: blur(5px);
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const CardTitle = styled.h3`
  font-size: 16px;
  font-weight: 700;
  color: ${props => props.theme.colors.cardText};
  font-family: ${props => props.theme.fonts.heading};
  letter-spacing: 1.25%;
  line-height: 1.25;
`;

const FilterDropdown = styled.div`
  background-color: transparent;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  cursor: pointer;
  
  svg {
    margin-left: 8px;
  }
`;

const ChartContainer = styled.div`
  height: 250px;
  width: 100%;
  position: relative;
`;

const ChartLegendContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-family: ${props => props.theme.fonts.heading};
  font-size: 12px;
  color: ${props => props.theme.colors.chartLegendText};
  letter-spacing: 3.33%;
  line-height: 1.42;
`;

const MonthsLegend = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
`;

const ValuesLegend = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 200px;
  position: absolute;
  left: -30px;
  top: 10px;
`;

const CustomTooltipContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 40%;
  left: 45%;
  z-index: 10;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid white;
  }
`;

const TooltipLabel = styled.div`
  font-weight: 600;
  margin-bottom: 3px;
  font-family: ${props => props.theme.fonts.heading};
  font-size: 14px;
`;

const TooltipValue = styled.div`
  color: #4CAF50;
  font-weight: 600;
  font-family: ${props => props.theme.fonts.body};
  font-size: 14px;
`;

interface AccountsChartProps {
  title: string;
  data: Array<{
    name: string;
    value: number;
    [key: string]: any;
  }>;
}

const AccountsChart: React.FC<AccountsChartProps> = ({ title, data }) => {
  const theme = useTheme();
  const gridColor = theme.name === 'dark' ? '#394263' : '#e0e0e0';
  const axisColor = theme.name === 'dark' ? '#6F8AAB' : '#6F8AAB';

  return (
    <CardContainer>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <FilterDropdown>
          Last Year
          <ChevronDownIcon />
        </FilterDropdown>
      </CardHeader>
      <ChartContainer>
        <CustomTooltipContainer>
          <TooltipLabel>June 13</TooltipLabel>
          <TooltipValue>Accounts - 107</TooltipValue>
        </CustomTooltipContainer>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{ top: 10, right: 10, left: 30, bottom: 20 }}
          >
            <defs>
              <linearGradient id="accountsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4CAF50" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#4CAF50" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke={gridColor} opacity={0.4} />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12, fill: axisColor }} 
              axisLine={false} 
              tickLine={false}
              stroke={axisColor}
              dy={10}
              opacity={0.7}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: axisColor }} 
              axisLine={false} 
              tickLine={false} 
              width={30}
              stroke={axisColor}
              domain={[0, 'dataMax + 50']}
              opacity={0.7}
            />
            <Tooltip content={() => null} /> {/* We're using our custom tooltip */}
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke="#4CAF50" 
              fillOpacity={1} 
              fill="url(#accountsGradient)" 
              strokeWidth={2}
              activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </ChartContainer>
      <ChartLegendContainer>
        <MonthsLegend>
          {data.map((item, index) => (
            <span key={index}>{item.name}</span>
          ))}
        </MonthsLegend>
      </ChartLegendContainer>
    </CardContainer>
  );
};

export default AccountsChart;