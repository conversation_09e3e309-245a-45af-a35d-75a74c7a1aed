/* Dark theme variables for Accounts Table */
.dark-theme {
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --border-color: #000D2633;
  --card-background: #093370;
  --hover-bg: rgba(255, 255, 255, 0.05);
  --table-header-bg: rgba(13, 35, 75, 0.6);
}

/* Override risk colors for dark theme */
.dark-theme .high-risk {
  color: #E86768;
  background-color: rgba(232, 103, 104, 0.2);
  border-left: 4px solid #E86768;
}

.dark-theme .medium-high-risk {
  color: #E2DC3E;
  background-color: rgba(226, 220, 62, 0.2);
  border-left: 4px solid #E2DC3E;
}

.dark-theme .medium-risk {
  color: #2AB666;
  background-color: rgba(42, 182, 102, 0.2);
  border-left: 4px solid #2AB666;
}

.dark-theme .low-risk {
  color: #006FFF;
  background-color: rgba(0, 111, 255, 0.2);
  border-left: 4px solid #006FFF;
}

/* Override table header for dark theme */
.dark-theme .table-header {
  background-color: var(--table-header-bg);
}

/* Account logo for dark theme */
.dark-theme .account-logo {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
}

/* Table row hover */
.dark-theme .table-row:hover {
  background-color: rgba(255, 255, 255, 0.05);
}
