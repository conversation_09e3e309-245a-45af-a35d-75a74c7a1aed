import React from 'react';
import styled from 'styled-components';

const CardContainer = styled.div`
  background-color: ${props => props.theme.name === 'dark' ? '#093370' : props.theme.colors.cardBackgroundOpacity};
  border-radius: ${props => props.theme.card.borderRadius};
  box-shadow: ${props => props.theme.card.boxShadow};
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(5px);
`;

const CardHeader = styled.div`
  margin-bottom: 20px;
`;

const CardTitle = styled.h3`
  font-size: 16px;
  font-weight: 700;
  color: ${props => props.theme.colors.cardText};
  font-family: ${props => props.theme.fonts.heading};
  letter-spacing: 1.25%;
  line-height: 1.25;
`;

const CardLink = styled.a`
  color: ${props => props.theme.colors.cardText};
  font-size: 10px;
  font-family: ${props => props.theme.fonts.body};
  text-decoration: none;
  display: flex;
  align-items: center;
  letter-spacing: 4%;
  line-height: 1.7;
  padding: 2px 8px;
  
  &:hover {
    text-decoration: underline;
  }
  
  svg {
    margin-left: 5px;
  }
`;

const CardContent = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 0 24px;
`;

interface StatusItemProps {
  type: 'paid' | 'due' | 'overdue' | 'active' | 'expiring' | 'expired';
}

const StatusItem = styled.div<StatusItemProps>`
  flex: 1;
  min-width: 120px;
  background: ${props => {
    const type = props.type;
    if (type === 'paid' || type === 'active') {
      return props.theme.gradients.success;
    } else if (type === 'due' || type === 'expiring') {
      return props.theme.gradients.warning;
    } else {
      return props.theme.gradients.danger;
    }
  }};
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  border-left: 7px solid ${props => {
    const type = props.type;
    if (type === 'paid' || type === 'active') {
      return props.theme.colors.success;
    } else if (type === 'due' || type === 'expiring') {
      return props.theme.colors.warning;
    } else {
      return props.theme.colors.danger;
    }
  }};
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
`;

const StatusValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 5px;
  font-family: ${props => props.theme.fonts.heading};
`;

const StatusLabel = styled.div`
  font-size: 14px;
  color: white;
  font-weight: 500;
  font-family: ${props => props.theme.fonts.body};
`;

const StatusTimeframe = styled.div`
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 3px;
  font-family: ${props => props.theme.fonts.body};
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 20px;
`;

interface StatusCardProps {
  title: string;
  linkText?: string;
  linkUrl?: string;
  linkIcon?: React.ReactNode;
  items: Array<{
    type: 'paid' | 'due' | 'overdue' | 'active' | 'expiring' | 'expired';
    value: number;
    timeframe?: string;
  }>;
}

const StatusCard: React.FC<StatusCardProps> = ({ title, linkText, linkUrl, linkIcon, items }) => {
  return (
    <CardContainer>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {items.map((item, index) => (
          <StatusItem key={index} type={item.type}>
            <StatusValue>{item.value}</StatusValue>
            <StatusLabel>
              {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
            </StatusLabel>
            {item.timeframe && <StatusTimeframe>{item.timeframe}</StatusTimeframe>}
          </StatusItem>
        ))}
      </CardContent>
      {linkText && linkUrl && (
        <CardFooter>
          <CardLink href={linkUrl}>
            {linkText}
            {linkIcon}
          </CardLink>
        </CardFooter>
      )}
    </CardContainer>
  );
};

export default StatusCard;
