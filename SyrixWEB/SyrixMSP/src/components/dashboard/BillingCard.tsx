import React from 'react';
import './BillingCard.css';

interface BillingData {
  currentPlan: {
    name: string;
    price: number;
    billingCycle: 'monthly' | 'yearly';
    features: string[];
  };
  nextBillingDate: string;
  lastPayment: {
    amount: number;
    date: string;
    status: 'paid' | 'pending' | 'failed';
  };
  usageMetrics?: {
    used: number;
    total: number;
    unit: string;
  };
}

// Mock data that will be replaced with API data later
const mockBillingData: BillingData = {
  currentPlan: {
    name: 'Professional',
    price: 49.99,
    billingCycle: 'monthly',
    features: ['Unlimited Users', '24/7 Support', 'Custom Domain', 'Analytics']
  },
  nextBillingDate: '2024-05-01',
  lastPayment: {
    amount: 49.99,
    date: '2024-04-01',
    status: 'paid'
  },
  usageMetrics: {
    used: 750,
    total: 1000,
    unit: 'GB'
  }
};

interface BillingCardProps {
  data?: BillingData;
}

export const BillingCard: React.FC<BillingCardProps> = ({ data = mockBillingData }) => {
  return (
    <div className="billing-card">
      <div className="billing-card-header">
        <h2>Billing Overview</h2>
      </div>
      
      <div className="billing-card-content">
        <div className="current-plan">
          <h3>{data.currentPlan.name} Plan</h3>
          <p className="price">
            ${data.currentPlan.price}/
            <span className="billing-cycle">{data.currentPlan.billingCycle}</span>
          </p>
          <ul className="features">
            {data.currentPlan.features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
        </div>

        <div className="billing-info">
          <div className="info-row">
            <span>Next billing date</span>
            <span>{new Date(data.nextBillingDate).toLocaleDateString()}</span>
          </div>
          <div className="info-row">
            <span>Last payment</span>
            <span className={`payment-status ${data.lastPayment.status}`}>
              ${data.lastPayment.amount} • {data.lastPayment.status}
            </span>
          </div>
        </div>

        {data.usageMetrics && (
          <div className="usage-metrics">
            <h4>Usage</h4>
            <div className="progress-bar">
              <div 
                className="progress" 
                style={{ width: `${(data.usageMetrics.used / data.usageMetrics.total) * 100}%` }}
              />
            </div>
            <p>
              {data.usageMetrics.used} / {data.usageMetrics.total} {data.usageMetrics.unit}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BillingCard; 