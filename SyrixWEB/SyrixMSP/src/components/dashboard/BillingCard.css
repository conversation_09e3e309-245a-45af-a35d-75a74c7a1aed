.billing-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.billing-card-header {
  margin-bottom: 24px;
}

.billing-card-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.current-plan {
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
  margin-bottom: 24px;
}

.current-plan h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.price {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.billing-cycle {
  font-size: 16px;
  color: #666;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features li {
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.features li:before {
  content: "✓";
  color: #22c55e;
  margin-right: 8px;
}

.billing-info {
  margin-bottom: 24px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  color: #666;
}

.payment-status {
  font-weight: 500;
}

.payment-status.paid {
  color: #22c55e;
}

.payment-status.pending {
  color: #f59e0b;
}

.payment-status.failed {
  color: #ef4444;
}

.usage-metrics h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #3b82f6;
  border-radius: 4px;
  transition: width 0.3s ease;
} 