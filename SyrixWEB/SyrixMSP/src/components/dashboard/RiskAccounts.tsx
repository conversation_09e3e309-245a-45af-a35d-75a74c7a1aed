import React from 'react';
import styled from 'styled-components';

const CardContainer = styled.div`
  background-color: ${props => props.theme.colors.cardBackgroundOpacity};
  border-radius: ${props => props.theme.card.borderRadius};
  box-shadow: ${props => props.theme.card.boxShadow};
  backdrop-filter: blur(5px);
  overflow: hidden;
`;

const CardHeader = styled.div`
  padding: 20px;
  background-color: ${props => props.theme.name === 'dark' ? '#093370' : props.theme.colors.cardBackground};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const CardTitle = styled.h3`
  font-size: 14px;
  font-weight: 600;
  color: ${props => props.theme.colors.cardText};
  font-family: ${props => props.theme.fonts.heading};
  margin: 0;
`;

const CardContent = styled.div`
  width: 100%;
`;

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
`;

const TableHeader = styled.div`
  display: flex;
  padding: 12px 20px;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  & > div {
    flex: 1;
    font-size: 13px;
    font-weight: 600;
    color: ${props => props.theme.colors.textSecondary};
    text-align: left;
    
    &:nth-child(3), &:nth-child(4) {
      flex: 0.5;
    }
  }
`;

const TableRow = styled.div`
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  align-items: center;
  
  &:hover {
    background-color: ${props => props.theme.name === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.03)'};
  }
`;

const TableCell = styled.div`
  flex: 1;
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  
  &:nth-child(3), &:nth-child(4) {
    flex: 0.5;
  }
`;

const CompanyCell = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const CompanyLogo = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 1.8px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const CompanyName = styled.div`
  font-weight: 500;
`;

const RiskText = styled.div<{ risk: number }>`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
`;

const RecordsText = styled.div`
  color: ${props => props.theme.colors.text};
  font-weight: 500;
`;

interface Account {
  id: string;
  name: string;
  logo: string;
  email: string;
  risk: number;
  records: string;
  status?: string;
}

interface RiskAccountsProps {
  title: string;
  accounts: Account[];
  pagination: {
    current: number;
    total: number;
  };
}

const RiskAccounts: React.FC<RiskAccountsProps> = ({ title, accounts }) => {
  return (
    <CardContainer>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <TableContainer>
          <TableHeader>
            <div>Account name</div>
            <div>Email</div>
            <div>Risk</div>
            <div>Records</div>
          </TableHeader>
          {accounts.map(account => (
            <TableRow key={account.id}>
              <TableCell>
                <CompanyCell>
                  <CompanyLogo>
                    <img src={account.logo} alt={account.name} />
                  </CompanyLogo>
                  <CompanyName>{account.name}</CompanyName>
                </CompanyCell>
              </TableCell>
              <TableCell>{account.email}</TableCell>
              <TableCell>
                <RiskText risk={account.risk}>{account.risk}%</RiskText>
              </TableCell>
              <TableCell>
                <RecordsText>{account.records}</RecordsText>
              </TableCell>
            </TableRow>
          ))}
        </TableContainer>
      </CardContent>
    </CardContainer>
  );
};

export default RiskAccounts;