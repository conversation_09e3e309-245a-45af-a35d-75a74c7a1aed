.june-marker {
  position: absolute;
  width:12px;
  height: 12px;
  border-radius: 50%;
  background-color: #4eca8e;
  border: 2px solid white;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.chart-container {
  position: relative;
  height: 100%;
  /*height: 450px;*/
  width: 100%;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
  margin-bottom: 1rem;
}

.chart-title {
  font-weight: 700;
  color: white;
  font-family: 'Rubik', sans-serif;
  margin: 0;
}

.year-selector {
  display: flex;
  align-items: center;
  position: relative;
}

.timeframe-selector {
  appearance: none;
  background-color: transparent;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 130px;
  background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  padding-right: 2rem;
}

.chart-grid {
  display: flex;
  height: 100%;
  position: relative;
}

/* Remove y-axis labels since we're using Recharts built-in labels now */
.y-axis-labels {
  display: none;
}

/* Hide the x-axis labels since we're using Recharts built-in labels now */
.x-axis-labels {
  display: none;
}

.current-month-indicator {
  position: absolute;
  top: 125px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 0.25rem;
  z-index: 10;
  pointer-events: none;
}

.indicator-content {
  text-align: left;
}

.indicator-date {
  font-size: 0.85rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.indicator-value {
  font-size: 0.95rem;
  font-weight: 700;
  color: #374151;
  margin: 0;
}

.custom-tooltip {
  background-color: white;
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  box-shadow: 0 2px 14px rgba(0, 0, 0, 0.25);
  font-size: 12px;
}