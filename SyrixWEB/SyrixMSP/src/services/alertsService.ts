import apiService from './api';
import { Alert, ApiResponse } from '../types';

/**
 * Service for managing alerts data from the backend API
 */
class AlertsService {
  private readonly baseEndpoint = '/api/v1/alerts';

  /**
   * Get all alerts
   * @param status Optional status filter
   * @param category Optional category filter
   * @returns Promise with list of alerts
   */
  async getAllAlerts(status?: string, category?: string): Promise<Alert[]> {
    try {
      const params: Record<string, string> = {};
      if (status) params.status = status;
      if (category) params.category = category;

      const response: ApiResponse<Alert[]> = await apiService.get(
        this.baseEndpoint,
        params
      );
      
      return response.data;
    } catch (error) {
      console.error('Error fetching alerts:', error);
      throw error;
    }
  }

  /**
   * Get a specific alert by ID
   * @param id Alert ID
   * @returns Promise with alert data
   */
  async getAlertById(id: string): Promise<Alert> {
    try {
      const response: ApiResponse<Alert> = await apiService.get(
        `${this.baseEndpoint}/${id}`
      );
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching alert ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update alert status
   * @param id Alert ID
   * @param status New status
   * @returns Promise with updated alert
   */
  async updateAlertStatus(id: string, status: string): Promise<Alert> {
    try {
      const response: ApiResponse<Alert> = await apiService.put(
        `${this.baseEndpoint}/${id}/status`,
        { status }
      );
      
      return response.data;
    } catch (error) {
      console.error(`Error updating alert ${id} status:`, error);
      throw error;
    }
  }

  /**
   * Get alerts by severity level
   * @param severity Severity level
   * @returns Promise with filtered alerts
   */
  async getAlertsBySeverity(severity?: string): Promise<Alert[]> {
    return this.getAllAlerts(undefined, undefined);
  }

  /**
   * Get alerts by category
   * @param category Alert category
   * @returns Promise with filtered alerts
   */
  async getAlertsByCategory(category?: string): Promise<Alert[]> {
    return this.getAllAlerts(undefined, category);
  }

  /**
   * Get alerts by status
   * @param status Alert status
   * @returns Promise with filtered alerts
   */
  async getAlertsByStatus(status?: string): Promise<Alert[]> {
    return this.getAllAlerts(status, undefined);
  }

  /**
   * Get alert statistics
   * @returns Promise with alert statistics
   */
  async getAlertStatistics(): Promise<{
    total: number;
    bySeverity: Record<string, number>;
    byStatus: Record<string, number>;
    byCategory: Record<string, number>;
  }> {
    try {
      const alerts = await this.getAllAlerts();
      
      const stats = {
        total: alerts.length,
        bySeverity: {} as Record<string, number>,
        byStatus: {} as Record<string, number>,
        byCategory: {} as Record<string, number>
      };

      alerts.forEach(alert => {
        // Count by severity
        stats.bySeverity[alert.severity] = (stats.bySeverity[alert.severity] || 0) + 1;
        
        // Count by status
        stats.byStatus[alert.status] = (stats.byStatus[alert.status] || 0) + 1;
        
        // Count by category
        stats.byCategory[alert.category] = (stats.byCategory[alert.category] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error fetching alert statistics:', error);
      throw error;
    }
  }

  /**
   * Transform alerts into account risk data for dashboard
   * This method converts alert data into the format expected by the dashboard components
   */
  async getAccountRiskData(): Promise<Array<{
    id: string;
    name: string;
    logo: string;
    email: string;
    risk: number;
    records: string;
    status: 'danger' | 'warning' | 'success';
  }>> {
    try {
      const alerts = await this.getAllAlerts();
      
      // Group alerts by affected resource (company/service)
      const resourceAlerts = new Map<string, Alert[]>();
      
      alerts.forEach(alert => {
        const resource = alert.affectedResource || alert.source;
        if (!resourceAlerts.has(resource)) {
          resourceAlerts.set(resource, []);
        }
        resourceAlerts.get(resource)!.push(alert);
      });

      // Convert to account risk format
      const accountRiskData = Array.from(resourceAlerts.entries()).map(([resource, resourceAlertList], index) => {
        // Calculate risk score based on alert severity
        const riskScore = this.calculateRiskScore(resourceAlertList);
        
        // Determine status based on risk score
        let status: 'danger' | 'warning' | 'success';
        if (riskScore >= 70) status = 'danger';
        else if (riskScore >= 40) status = 'warning';
        else status = 'success';

        return {
          id: `${index + 1}`,
          name: resource,
          logo: `https://ui-avatars.com/api/?name=${encodeURIComponent(resource)}&background=0051a2&color=fff`,
          email: `info@${resource.toLowerCase().replace(/\s+/g, '')}.com`,
          risk: riskScore,
          records: `${Math.floor(Math.random() * 5000 + 1000).toLocaleString()}`, // Mock record count
          status
        };
      });

      return accountRiskData;
    } catch (error) {
      console.error('Error transforming alerts to account risk data:', error);
      // Return empty array on error to prevent UI crashes
      return [];
    }
  }

  /**
   * Calculate risk score based on alerts
   * @param alerts List of alerts for a resource
   * @returns Risk score (0-100)
   */
  private calculateRiskScore(alerts: Alert[]): number {
    if (alerts.length === 0) return 0;

    const severityWeights = {
      critical: 25,
      high: 15,
      medium: 8,
      low: 3
    };

    const totalWeight = alerts.reduce((sum, alert) => {
      return sum + (severityWeights[alert.severity] || 0);
    }, 0);

    // Normalize to 0-100 scale, with some randomness for demo purposes
    const baseScore = Math.min(100, totalWeight);
    const randomVariation = Math.floor(Math.random() * 20) - 10; // ±10 variation
    
    return Math.max(0, Math.min(100, baseScore + randomVariation));
  }
}

// Create and export singleton instance
const alertsService = new AlertsService();
export default alertsService;
