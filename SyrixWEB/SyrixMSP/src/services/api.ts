import axios, { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  AxiosError, 
  InternalAxiosRequestConfig
} from 'axios';
import { ApiResponse, ApiError, PaginatedResponse } from '../types';

// Define expected error response type
interface ErrorResponse {
  message?: string;
  errors?: Record<string, string[]>;
}

// Types for query parameters
interface QueryParams {
  [key: string]: string | number | boolean | undefined;
}

// Base API class
class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor(baseURL: string = process.env.REACT_APP_API_URL || 'http://localhost:8080') {
    this.baseURL = baseURL;
    
    // Create axios instance
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000 // 30 seconds
    });
    
    // Setup interceptors
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        const customConfig = config as any;
        if (customConfig.requiresAuth !== false) {
          const token = localStorage.getItem('token');
          if (token) {
            config.headers = config.headers || {};
            config.headers['Authorization'] = `Bearer ${token}`;
          }
        }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(this.formatError(error as AxiosError<ErrorResponse>));
      }
    );
    
    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error: AxiosError) => {
        return Promise.reject(this.formatError(error as AxiosError<ErrorResponse>));
      }
    );
  }

  private formatError(error: AxiosError<ErrorResponse>): ApiError {
    // Format the error response to a consistent structure
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || error.message || 'An unexpected error occurred';
    const errors = error.response?.data?.errors;
    
    return {
      status,
      message,
      errors,
      timestamp: new Date().toISOString()
    };
  }

  // Helper method to build query string
  private buildQueryParams(params?: QueryParams): string {
    if (!params) return '';
    
    const query = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
      .join('&');
    
    return query ? `?${query}` : '';
  }

  // Generic GET method
  public async get<T>(
    endpoint: string,
    params?: QueryParams,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const queryParams = this.buildQueryParams(params);
    const response = await this.api.get<ApiResponse<T>>(`${endpoint}${queryParams}`, config);
    return response.data;
  }

  // Generic GET method for paginated responses
  public async getPaginated<T>(
    endpoint: string,
    params?: QueryParams & { page?: number; limit?: number },
    config?: AxiosRequestConfig
  ): Promise<PaginatedResponse<T>> {
    const queryParams = this.buildQueryParams(params);
    const response = await this.api.get<PaginatedResponse<T>>(`${endpoint}${queryParams}`, config);
    return response.data;
  }

  // Generic POST method
  public async post<T, D = any>(
    endpoint: string,
    data: D,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.api.post<ApiResponse<T>>(endpoint, data, config);
    return response.data;
  }

  // Generic PUT method
  public async put<T, D = any>(
    endpoint: string,
    data: D,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.api.put<ApiResponse<T>>(endpoint, data, config);
    return response.data;
  }

  // Generic PATCH method
  public async patch<T, D = any>(
    endpoint: string,
    data: D,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.api.patch<ApiResponse<T>>(endpoint, data, config);
    return response.data;
  }

  // Generic DELETE method
  public async delete<T>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.api.delete<ApiResponse<T>>(endpoint, config);
    return response.data;
  }

  // Upload file(s)
  public async uploadFiles<T>(
    endpoint: string,
    files: File | File[],
    additionalData?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    
    // Add files to form data
    if (Array.isArray(files)) {
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });
    } else {
      formData.append('file', files);
    }
    
    // Add additional data
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      });
    }
    
    // Set content type to multipart/form-data
    const uploadConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        ...(config?.headers || {}),
        'Content-Type': 'multipart/form-data'
      }
    };
    
    const response = await this.api.post<ApiResponse<T>>(endpoint, formData, uploadConfig);
    return response.data;
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;