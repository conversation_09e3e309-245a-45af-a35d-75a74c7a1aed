import apiService from './api';
import { User, ApiResponse, PaginatedResponse } from '../types';

// User filter parameters
interface UserFilters {
  search?: string;
  role?: string;
  status?: 'active' | 'inactive';
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  [key: string]: string | number | boolean | undefined;
}

// Create user parameters
interface CreateUserParams {
  name: string;
  email: string;
  role: string;
  password?: string;
  sendInvite?: boolean;
}

// Update user parameters
interface UpdateUserParams {
  name?: string;
  email?: string;
  role?: string;
  status?: 'active' | 'inactive';
}

// User service class
class UserService {
  private BASE_ENDPOINT = '/users';

  /**
   * Get a list of users with optional filters and pagination
   */
  public async getUsers(filters?: UserFilters): Promise<PaginatedResponse<User>> {
    return apiService.getPaginated<User>(this.BASE_ENDPOINT, filters);
  }

  /**
   * Get a single user by ID
   */
  public async getUserById(userId: string): Promise<ApiResponse<User>> {
    return apiService.get<User>(`${this.BASE_ENDPOINT}/${userId}`);
  }

  /**
   * Create a new user
   */
  public async createUser(userData: CreateUserParams): Promise<ApiResponse<User>> {
    return apiService.post<User, CreateUserParams>(this.BASE_ENDPOINT, userData);
  }

  /**
   * Update an existing user
   */
  public async updateUser(userId: string, userData: UpdateUserParams): Promise<ApiResponse<User>> {
    return apiService.put<User, UpdateUserParams>(`${this.BASE_ENDPOINT}/${userId}`, userData);
  }

  /**
   * Delete a user
   */
  public async deleteUser(userId: string): Promise<ApiResponse<void>> {
    return apiService.delete<void>(`${this.BASE_ENDPOINT}/${userId}`);
  }

  /**
   * Change user password
   */
  public async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    return apiService.post<void>(`${this.BASE_ENDPOINT}/${userId}/change-password`, {
      currentPassword,
      newPassword
    });
  }

  /**
   * Update current user profile
   */
  public async updateProfile(profileData: { name?: string; email?: string }): Promise<ApiResponse<User>> {
    return apiService.put<User>(`${this.BASE_ENDPOINT}/profile`, profileData);
  }

  /**
   * Get current user profile
   */
  public async getProfile(): Promise<ApiResponse<User>> {
    return apiService.get<User>(`${this.BASE_ENDPOINT}/profile`);
  }

  /**
   * Send password reset link to user's email
   */
  public async forgotPassword(email: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>('/auth/forgot-password', { email });
  }

  /**
   * Reset password with token
   */
  public async resetPassword(token: string, newPassword: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>('/auth/reset-password', {
      token,
      newPassword
    });
  }
}

// Create and export a singleton instance
const userService = new UserService();
export default userService;