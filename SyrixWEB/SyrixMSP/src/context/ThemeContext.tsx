import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components';

export interface ThemeType {
  name: string;
  colors: {
    text: string;
    textSecondary: string;
    background: string;
    sidebar: string;
    sidebarText: string;
    sidebarHover: string;
    border: string;
    cardBackground: string;
    activeItemTextColor: string;
    secondaryText?: string;
    headerBackground: string;
    searchBackground: string;
    notificationBackground: string;
    themeToggleBackground: string;
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
    info: string;
    // Add any other colors needed
    [key: string]: string | undefined;
  };
  gradients: {
    background: string;
    ellipse: string;
    success: string;
    warning: string;
    danger: string;
    trialNotice: string;
    primaryButton: string;
    [key: string]: string;
  };
  sidebar: {
    width: string;
    activeItemBackground: string;
    activeItemTextColor: string;
    [key: string]: string;
  };
  fonts: {
    body: string;
    heading: string;
    [key: string]: string;
  };
  header: {
    height: string;
    [key: string]: string;
  };
  card: {
    borderRadius: string;
    boxShadow: string;
    [key: string]: string;
  };
  button: {
    borderRadius: string;
    primary: {
      background: string;
      color: string;
      [key: string]: string;
    };
    secondary: {
      background: string;
      color: string;
      [key: string]: string;
    };
    [key: string]: any;
  };
  [key: string]: any;
}

const lightTheme: ThemeType = {
  name: 'light',
  colors: {
    text: '#FFFFFF',
    textSecondary: '#64748b',
    background: '#BBCBFF',
    sidebar: '#BBCBFF',
    sidebarText: '#FFFFFF',
    sidebarHover: 'rgba(255, 255, 255, 0.3)',
    border: '#E2E8F0',
    cardBackground: '#FFFFFF',
    activeItemTextColor: '#3B82F6',
    secondaryText: '#64748b',
    headerBackground: '#FFFFFF',
    searchBackground: '#F8FAFC',
    notificationBackground: '#F8FAFC',
    themeToggleBackground: 'rgba(0, 0, 0, 0.1)',
    primary: '#3B82F6',
    secondary: '#64748b',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#3b82f6',
  },
  gradients: {
    background: '#BBCBFF',
    ellipse: 'rgba(200, 220, 240, 0.5)',
    success: 'linear-gradient(to right, #05B8A0, #2AB666)',
    warning: 'linear-gradient(to right, #FFC107, #FF9800)',
    danger: 'linear-gradient(to right, #F44336, #D32F2F)',
    trialNotice: 'linear-gradient(to right, rgba(207, 129, 35, 0.3), rgba(240, 160, 63, 0.3))',
    primaryButton: 'linear-gradient(to right, #3B82F6, #2563EB)',
  },
  sidebar: {
    width: '240px',
    activeItemBackground: 'rgba(59, 130, 246, 0.1)',
    activeItemTextColor: '#3B82F6',
  },
  fonts: {
    body: 'Rubik, sans-serif',
    heading: 'Rubik, sans-serif',
  },
  header: {
    height: '64px',
  },
  card: {
    borderRadius: '0.75rem',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.15)'
  },
  button: {
    borderRadius: '4px',
    primary: {
      background: '#3B82F6',
      color: '#FFFFFF'
    },
    secondary: {
      background: '#F1F5F9',
      color: '#1e293b'
    }
  }
};

const darkTheme: ThemeType = {
  name: 'dark',
  colors: {
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.7)',
    background: '#062D68',
    sidebar: '#10569B',
    sidebarText: '#FFFFFF',
    sidebarHover: 'rgba(255, 255, 255, 0.1)',
    border: '#000D2633',
    cardBackground: '#093370',
    activeItemTextColor: '#FFFFFF',
    secondaryText: 'rgba(255, 255, 255, 0.7)',
    headerBackground: 'rgba(0, 0, 0, 0.4)',
    searchBackground: 'rgba(0, 0, 0, 0.3)',
    notificationBackground: '#0D234B',
    themeToggleBackground: 'rgba(0, 0, 0, 0.3)',
    primary: '#006FFF',
    secondary: '#0D234B',
    success: '#2AB666',
    warning: '#E2DC3E',
    danger: '#E86768',
    info: '#2196f3',
  },
  gradients: {
    background: 'linear-gradient(270deg, #10569B, #10569B)',
    ellipse: 'rgba(14, 67, 123, 0.5)',
    success: 'linear-gradient(135deg, #05B8A0 0%, #2AB666 100%)',
    warning: 'linear-gradient(135deg, #F5DE6C 0%, #E2DC3E 100%)',
    danger: 'linear-gradient(135deg, #C93051 0%, #E86768 100%)',
    trialNotice: 'linear-gradient(to right, rgba(207, 129, 35, 0.3), rgba(240, 160, 63, 0.3))',
    primaryButton: 'linear-gradient(to right, #006FFF, #0062e6)',
  },
  sidebar: {
    width: '260px',
    activeItemBackground: 'rgba(13, 35, 75, 0.6)',
    activeItemTextColor: '#FFFFFF',
  },
  fonts: {
    body: 'Rubik, sans-serif',
    heading: 'Poppins, sans-serif',
  },
  header: {
    height: '64px',
  },
  card: {
    borderRadius: '0.75rem',
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.3)'
  },
  button: {
    borderRadius: '4px',
    primary: {
      background: '#006FFF',
      color: '#FFFFFF'
    },
    secondary: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: '#FFFFFF'
    }
  }
};

interface ThemeContextProps {
  theme: ThemeType;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextProps>({
  theme: darkTheme,
  toggleTheme: () => {},
});

export const useTheme = () => useContext(ThemeContext);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Check for saved theme in sessionStorage
  const savedTheme = typeof window !== 'undefined' ? sessionStorage.getItem('syrixTheme') : null;
  const initialTheme = savedTheme === 'light' ? lightTheme : darkTheme; // Default to dark theme
  
  const [theme, setTheme] = useState<ThemeType>(initialTheme);

  // Save the theme in session storage so it persists
  useEffect(() => {
    try {
      sessionStorage.setItem('syrixTheme', theme.name);
    } catch (e) {
      console.error('Failed to save theme to session storage:', e);
    }
  }, [theme.name]);

  const toggleTheme = () => {
    console.log('Toggling theme from:', theme.name);
    setTheme(prevTheme => prevTheme.name === 'dark' ? lightTheme : darkTheme);
  };

  // Use a type assertion to unknown and then to DefaultTheme
  const styledTheme = theme as unknown as DefaultTheme;

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <StyledThemeProvider theme={styledTheme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};
