{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "lib": ["dom", "dom.iterable", "esnext"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true, "noEmit": true, "jsx": "react-jsx", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules", "dist", "build"], "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}