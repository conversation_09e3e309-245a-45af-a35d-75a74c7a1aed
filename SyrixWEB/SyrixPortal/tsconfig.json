{"extends": "./tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "target": "esnext", "module": "esnext", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["./frontend/src/*"]}}, "include": ["frontend/src/**/*"], "exclude": ["node_modules", "build", "target", "frontend/node_modules", "frontend/build"]}