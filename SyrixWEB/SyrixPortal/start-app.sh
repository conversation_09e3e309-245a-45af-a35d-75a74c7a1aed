#!/bin/bash

# Simple script to clean, compile and start the application

echo "=== CLEANING PROJECT ==="
cd /Users/<USER>/Documents/Development/private/syrix/SyrixWEB/SyrixPortal

# Clean Maven build files
./mvnw clean

echo "=== COMPILING PROJECT ==="
# Compile the project
./mvnw compile -q

if [ $? -ne 0 ]; then
    echo "Compilation failed!"
    exit 1
fi

echo "=== STARTING APPLICATION ==="
# Start the Spring Boot application in the background
./mvnw spring-boot:run > backend.log 2>&1 &
BACKEND_PID=$!

echo "Backend started with PID: $BACKEND_PID"
echo "Backend logs are in backend.log"
echo "Wait 10 seconds for backend to start..."
sleep 10

echo "=== STARTING FRONTEND ==="
# Start the frontend
cd frontend
npm start > ../frontend.log 2>&1 &
FRONTEND_PID=$!

echo "Frontend started with PID: $FRONTEND_PID"
echo "Frontend logs are in frontend.log"

echo "=== APPLICATION STARTED ==="
echo "Backend PID: $BACKEND_PID"
echo "Frontend PID: $FRONTEND_PID"
echo "Backend running on http://localhost:8080"
echo "Frontend running on http://localhost:3000"
echo ""
echo "To stop the application:"
echo "kill $BACKEND_PID $FRONTEND_PID"
