package io.syrix.service;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for BaselinePolicyService in Spring context.
 * Verifies that the service loads correctly at startup and provides expected functionality.
 */
@SpringBootTest
@ActiveProfiles("test")
class BaselinePolicyServiceIntegrationTest {

    @Autowired
    private BaselinePolicyService baselinePolicyService;

    @Test
    void testServiceIsInjectedAndInitialized() {
        // Given & When
        assertNotNull(baselinePolicyService, "BaselinePolicyService should be injected");
        assertTrue(baselinePolicyService.isInitialized(), "Service should be initialized");
    }

    @Test
    void testServiceLoadsBaselinePolicies() {
        // Given & When
        Map<String, PolicyBaseline> allPolicies = baselinePolicyService.loadAllPolicies();
        
        // Then
        assertNotNull(allPolicies, "Should return policies map");
        assertTrue(allPolicies.size() > 100, "Should load substantial number of policies");
        
        // Verify we have policies from multiple services
        Set<String> services = baselinePolicyService.getAvailableServices();
        assertTrue(services.size() >= 5, "Should have multiple Microsoft 365 services");
        assertTrue(services.contains("EXO"), "Should contain Exchange Online policies");
        assertTrue(services.contains("AAD"), "Should contain Azure AD policies");
    }

    @Test
    void testServiceProvidesExchangeOnlinePolicies() {
        // Given & When
        List<PolicyBaseline> exoPolicies = baselinePolicyService.getPoliciesByServiceType("EXO");
        
        // Then
        assertNotNull(exoPolicies, "Should return EXO policies");
        assertFalse(exoPolicies.isEmpty(), "Should have Exchange Online policies");
        
        // Verify all policies are EXO policies
        for (PolicyBaseline policy : exoPolicies) {
            assertTrue(policy.getPolicyId().contains(".EXO."), 
                "All policies should be EXO policies: " + policy.getPolicyId());
        }
    }

    @Test
    void testServiceProvidesPolicyLookup() {
        // Given
        Map<String, PolicyBaseline> allPolicies = baselinePolicyService.loadAllPolicies();
        String firstPolicyId = allPolicies.keySet().iterator().next();
        
        // When
        PolicyBaseline policy = baselinePolicyService.findPolicyByNumber(firstPolicyId);
        
        // Then
        assertNotNull(policy, "Should find the policy");
        assertEquals(firstPolicyId, policy.getPolicyId());
        assertNotNull(policy.getTitle());
        assertNotNull(policy.getCriticality());
    }

    @Test
    void testServiceProvidesCriticalityFiltering() {
        // Given & When
        List<PolicyBaseline> shallPolicies = baselinePolicyService.getPoliciesByCriticality(PolicyCriticality.SHALL);
        List<PolicyBaseline> shouldPolicies = baselinePolicyService.getPoliciesByCriticality(PolicyCriticality.SHOULD);
        
        // Then
        assertNotNull(shallPolicies, "Should return SHALL policies");
        assertNotNull(shouldPolicies, "Should return SHOULD policies");
        
        // Should have both SHALL and SHOULD policies from real baselines
        assertFalse(shallPolicies.isEmpty(), "Should have SHALL policies");
        assertFalse(shouldPolicies.isEmpty(), "Should have SHOULD policies");
        
        // Verify criticality filtering works correctly
        for (PolicyBaseline policy : shallPolicies) {
            assertEquals(PolicyCriticality.SHALL, policy.getCriticality());
        }
        for (PolicyBaseline policy : shouldPolicies) {
            assertEquals(PolicyCriticality.SHOULD, policy.getCriticality());
        }
    }

    @Test
    void testServiceProvidesStatistics() {
        // Given & When
        Map<String, Object> stats = baselinePolicyService.getPolicyStatistics();
        
        // Then
        assertNotNull(stats, "Should return statistics");
        assertTrue(stats.containsKey("initialized"), "Should contain initialization status");
        assertTrue(stats.containsKey("totalPolicies"), "Should contain total policies count");
        assertTrue(stats.containsKey("totalServices"), "Should contain total services count");
        
        assertTrue((Boolean) stats.get("initialized"), "Should be initialized");
        assertTrue((Integer) stats.get("totalPolicies") > 100, "Should have substantial number of policies");
        assertTrue((Integer) stats.get("totalServices") >= 5, "Should have multiple services");
    }

    @Test
    void testServiceHandlesPolicyExistence() {
        // Given
        Map<String, PolicyBaseline> allPolicies = baselinePolicyService.loadAllPolicies();
        String existingPolicyId = allPolicies.keySet().iterator().next();
        String nonExistentPolicyId = "MS.NONEXISTENT.1.1v1";
        
        // When & Then
        assertTrue(baselinePolicyService.policyExists(existingPolicyId), 
            "Should return true for existing policy");
        assertFalse(baselinePolicyService.policyExists(nonExistentPolicyId), 
            "Should return false for non-existent policy");
        assertFalse(baselinePolicyService.policyExists(null), 
            "Should return false for null policy ID");
    }

    @Test
    void testServiceHandlesInvalidPolicyLookup() {
        // Given
        String invalidPolicyId = "MS.INVALID.1.1v1";
        
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> baselinePolicyService.findPolicyByNumber(invalidPolicyId),
            "Should throw exception for invalid policy ID");
        
        assertThrows(IllegalArgumentException.class, 
            () -> baselinePolicyService.findPolicyByNumber(null),
            "Should throw exception for null policy ID");
        
        assertThrows(IllegalArgumentException.class, 
            () -> baselinePolicyService.findPolicyByNumber(""),
            "Should throw exception for empty policy ID");
    }
}
