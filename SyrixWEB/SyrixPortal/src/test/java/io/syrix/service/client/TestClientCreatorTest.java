package io.syrix.service.client;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.impl.mongodb.MongoCustomerDao;
import io.syrix.datamodel.Customer;
import io.syrix.service.connect.ConnectionInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for TestCompanyCreator
 */
public class TestClientCreatorTest {

    @Mock
    private ClientService clientService;
    
    @Mock
    private DaoFactory daoFactory;
    
    @Mock
    private MongoCustomerDao companyDao;
    @Mock
    private ConnectionInfoService connectionInfoService;
    
    private TestClientCreator testClientCreator;
    
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(daoFactory.getCustomerDao()).thenReturn(companyDao);
        testClientCreator = new TestClientCreator(clientService, daoFactory, connectionInfoService);
    }
    
    @Test
    public void testInitializeMongoCollection() {
        when(companyDao.count()).thenReturn(5L);
        
        testClientCreator.initializeMongoCollection();
        
        verify(companyDao).count();
    }
    
    @Test
    public void testCreateTestCompany_WhenClientExists() {
        // Arrange
        String tenantId = "syrixdev.onmicrosoft.com";
        Customer existingCustomer = new Customer();
        existingCustomer.setId(UUID.randomUUID());
        existingCustomer.setName("Syrix Development");
        
        when(clientService.getClientByTenantId(tenantId)).thenReturn(Optional.of(existingCustomer));
        
        // Act
        Customer result = testClientCreator.createTestClient();
        
        // Assert
        assertNotNull(result);
        assertEquals(existingCustomer.getId(), result.getId());
        assertEquals(existingCustomer.getName(), result.getName());
        verify(clientService).getClientByTenantId(tenantId);
        verify(clientService, never()).createClient(any(Customer.class));
    }
    
    @Test
    public void testCreateTestCompany_WhenClientDoesNotExist() {
        // Arrange
        String tenantId = "syrixdev.onmicrosoft.com";
        Customer newCustomer = new Customer();
        newCustomer.setId(UUID.randomUUID());
        newCustomer.setName("Syrix Development");
        
        when(clientService.getClientByTenantId(tenantId)).thenReturn(Optional.empty());
        when(clientService.createClient(any(Customer.class))).thenReturn(newCustomer);
        
        // Act
        Customer result = testClientCreator.createTestClient();
        
        // Assert
        assertNotNull(result);
        assertEquals(newCustomer.getId(), result.getId());
        assertEquals(newCustomer.getName(), result.getName());
        verify(clientService).getClientByTenantId(tenantId);
        verify(clientService).createClient(any(Customer.class));
    }
    
    @Test
    public void testCreateSyrixDevClient() {
        // Arrange
        Customer newCustomer = new Customer();
        newCustomer.setId(UUID.randomUUID());
        newCustomer.setName("Syrix Development");
        
        when(clientService.createOrUpdateClient(any(Customer.class))).thenReturn(newCustomer);
        
        // Act
        Customer result = testClientCreator.createSyrixDevClient();
        
        // Assert
        assertNotNull(result);
        assertEquals(newCustomer.getId(), result.getId());
        assertEquals(newCustomer.getName(), result.getName());
        verify(clientService).createOrUpdateClient(any(Customer.class));
    }
}
