package io.syrix.service;

import io.syrix.model.DashboardResponse;
import io.syrix.model.SecurityMetrics;
import io.syrix.model.StatusCardData;
import io.syrix.model.TestStatusItem;
import io.syrix.service.test.TestStatusService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for SecurityService
 * Tests service integration with real Spring context but without external dependencies
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.database=syrix-test",
    "logging.level.io.syrix=DEBUG"
})
@Disabled
class SecurityServiceIntegrationTest {

    @Autowired
    private SecurityService securityService;

    @Autowired
    private TestStatusService testStatusService;

    @Test
    void shouldReturnDashboardData() {
        // When
        DashboardResponse response = securityService.getDashboardData();

        // Then
        assertThat(response).isNotNull();
        assertThat(response.statusCards()).isNotEmpty();
        assertThat(response.testStatusItems()).isNotEmpty();
        assertThat(response.securityMetrics()).isNotEmpty();
        assertThat(response.topRiskUsers()).isNotNull();
        assertThat(response.riskOverTime()).isNotEmpty();
        assertThat(response.notifications()).isNotEmpty();
        assertThat(response.nextScanInfo()).isNotNull();
    }

    @Test
    void shouldReturnConsistentSecurityMetrics() {
        // When
        List<SecurityMetrics> metrics = securityService.getSecurityMetrics();

        // Then
        assertThat(metrics).isNotEmpty();
        assertThat(metrics).hasSize(6); // Expected number of security metrics
        
        // Verify all metrics have required fields
        for (SecurityMetrics metric : metrics) {
            assertThat(metric.name()).isNotBlank();
            assertThat(metric.status()).isIn("safe", "warning", "critical");
            assertThat(metric.current()).isGreaterThanOrEqualTo(0);
            assertThat(metric.total()).isGreaterThan(0);
        }
    }

    @Test
    void shouldReturnValidStatusCards() {
        // When
        List<StatusCardData> cards = securityService.getStatusCards();

        // Then
        assertThat(cards).isNotEmpty();
        assertThat(cards).hasSize(4); // Expected number of status cards
        
        // Verify all cards have required fields
        for (StatusCardData card : cards) {
            assertThat(card.title()).isNotBlank();
            assertThat(card.status()).isIn("safe", "warning", "danger");
            assertThat(card.statusText()).isNotBlank();
            assertThat(card.count()).isGreaterThanOrEqualTo(0);
            assertThat(card.total()).isGreaterThan(0);
        }
    }

    @Test
    void shouldReturnTestStatusItems() {
        // When
        List<TestStatusItem> items = securityService.getTestStatusItems();

        // Then
        assertThat(items).isNotEmpty();
        
        // Verify all items have required fields
        for (TestStatusItem item : items) {
            assertThat(item.label()).isNotBlank();
            assertThat(item.value()).isGreaterThanOrEqualTo(0);
            assertThat(item.color()).matches("^#[0-9A-Fa-f]{6}$"); // Valid hex color
            assertThat(item.iconType()).isNotNull();
        }
    }

    @Test
    void shouldCalculateValidSecurityScore() {
        // When
        double score = securityService.calculateSecurityScore();

        // Then
        assertThat(score).isBetween(0.0, 100.0);
    }

    @Test
    void shouldReturnRiskOverTimeData() {
        // When
        var yearlyRisk = securityService.getRiskOverTime();
        var weeklyRisk = securityService.getRiskOverTimeWeekly();
        var monthlyRisk = securityService.getRiskOverTimeMonthly();

        // Then
        assertThat(yearlyRisk).hasSize(12); // 12 months
        assertThat(weeklyRisk).hasSize(7);  // 7 days
        assertThat(monthlyRisk).hasSize(30); // 30 days

        // Verify all data points have valid values
        yearlyRisk.forEach(risk -> {
            assertThat(risk.month()).isNotBlank();
            assertThat(risk.risks()).isGreaterThanOrEqualTo(0.0);
        });

        weeklyRisk.forEach(risk -> {
            assertThat(risk.month()).isNotBlank(); // Actually contains day name
            assertThat(risk.risks()).isGreaterThanOrEqualTo(0.0);
        });

        monthlyRisk.forEach(risk -> {
            assertThat(risk.month()).isNotBlank(); // Actually contains date
            assertThat(risk.risks()).isGreaterThanOrEqualTo(0.0);
        });
    }

    @Test
    void shouldReturnNotifications() {
        // When
        var notifications = securityService.getNotifications();

        // Then
        assertThat(notifications).isNotEmpty();
        assertThat(notifications).hasSizeGreaterThanOrEqualTo(5);

        // Verify notification structure
        notifications.forEach(notification -> {
            assertThat(notification.id()).isNotBlank();
            assertThat(notification.type()).isNotNull();
            assertThat(notification.service()).isNotNull();
            assertThat(notification.service().name()).isNotBlank();
            assertThat(notification.application()).isNotNull();
            assertThat(notification.application().name()).isNotBlank();
            assertThat(notification.description()).isNotBlank();
        });
    }

    @Test
    void shouldReturnTopRisks() {
        // When
        var risks = securityService.getRisks();

        // Then
        assertThat(risks).isNotEmpty();
        assertThat(risks).hasSize(4); // Expected number of top risks

        // Verify risk structure
        risks.forEach(risk -> {
            assertThat(risk.type()).isNotBlank();
            assertThat(risk.app()).isNotBlank();
            assertThat(risk.risk()).isGreaterThan(0);
            assertThat(risk.records()).isNotBlank();
        });
    }

    @Test
    void shouldReturnTopRiskUsers() {
        // When
        var users = securityService.getTopRiskUsers();

        // Then
        assertThat(users).isNotEmpty();
        assertThat(users).hasSize(3); // Expected number of top risk users

        // Verify user structure
        users.forEach(user -> {
            assertThat(user.owner()).isNotBlank();
            assertThat(user.totalAssets()).isNotBlank();
            assertThat(user.publicAssetsOwned()).isNotBlank();
        });
    }

    @Test
    void shouldReturnFailedFindings() {
        // When
        var findings = securityService.getFailedFindings();

        // Then
        assertThat(findings).isNotNull();
        assertThat(findings.total()).isGreaterThanOrEqualTo(0);
        assertThat(findings.passed()).isGreaterThanOrEqualTo(0);
        assertThat(findings.failed()).isGreaterThanOrEqualTo(0);
        assertThat(findings.alerts()).isNotNull();
        assertThat(findings.alerts().critical()).isGreaterThanOrEqualTo(0);
        assertThat(findings.alerts().medium()).isGreaterThanOrEqualTo(0);
        assertThat(findings.alerts().low()).isGreaterThanOrEqualTo(0);
    }
}
