package io.syrix.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.model.*;
import io.syrix.service.*;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ApiV1Controller.class)
@DisplayName("API v1 Controller Tests")
@Disabled
class ApiV1ControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean private SecurityService securityService;
    @MockBean private AlertsService alertsService;
    @MockBean private UserProfileService userProfileService;
    @MockBean private ScanService scanService;
    @MockBean private AuditLogService auditLogService;
    @MockBean private SystemLogService systemLogService;
    @MockBean private NotificationsService notificationsService;

    @BeforeEach
    void setUp() {
        // Setup common mock responses if needed
    }

    @Nested
    @DisplayName("System Endpoints")
    class SystemEndpoints {

        @Test
        @DisplayName("GET /api/v1/test should return success response")
        void testEndpoint_ShouldReturnSuccess() throws Exception {
            mockMvc.perform(get("/api/v1/test"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.status").value("success"))
                    .andExpect(jsonPath("$.data.method").value("GET"))
                    .andExpect(jsonPath("$.data.message").value("API test endpoint is working"))
                    .andExpect(jsonPath("$.timestamp").exists())
                    .andExpect(jsonPath("$.requestId").exists())
                    .andExpect(jsonPath("$.apiVersion").value("1.0.0"));
        }

        @Test
        @DisplayName("POST /api/v1/test should handle request body")
        void testEndpointPost_ShouldHandleRequestBody() throws Exception {
            String requestBody = "{\"test\": \"data\", \"number\": 42}";
            
            mockMvc.perform(post("/api/v1/test")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.status").value("success"))
                    .andExpect(jsonPath("$.data.method").value("POST"))
                    .andExpect(jsonPath("$.data.receivedData").exists());
        }

        @Test
        @DisplayName("GET /api/v1/health should return health status")
        void healthCheck_ShouldReturnHealthStatus() throws Exception {
            mockMvc.perform(get("/api/v1/health"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.status").value("UP"))
                    .andExpect(jsonPath("$.data.version").value("1.0.0"))
                    .andExpect(jsonPath("$.data.uptime").exists())
                    .andExpect(jsonPath("$.message").value("System is healthy"));
        }

        @Test
        @DisplayName("GET /api/v1/status should return API status")
        void getApiStatus_ShouldReturnStatus() throws Exception {
            mockMvc.perform(get("/api/v1/status"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.status").value("operational"))
                    .andExpect(jsonPath("$.data.apiVersion").value("1.0.0"))
                    .andExpect(jsonPath("$.data.endpoints").value("active"));
        }

        @ParameterizedTest
        @ValueSource(strings = {"GET", "POST", "PUT", "DELETE"})
        @DisplayName("Test endpoint should support multiple HTTP methods")
        void testEndpoint_ShouldSupportMultipleMethods(String method) throws Exception {
            switch (method) {
                case "GET":
                    mockMvc.perform(get("/api/v1/test"))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.data.method").value("GET"));
                    break;
                case "POST":
                    mockMvc.perform(post("/api/v1/test")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{}"))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.data.method").value("POST"));
                    break;
                case "PUT":
                    mockMvc.perform(put("/api/v1/test")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{}"))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.data.method").value("PUT"));
                    break;
                case "DELETE":
                    mockMvc.perform(delete("/api/v1/test"))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.data.method").value("DELETE"));
                    break;
            }
        }
    }

    @Nested
    @DisplayName("Dashboard Endpoints")
    class DashboardEndpoints {

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/dashboard should return dashboard data")
        void getDashboardData_ShouldReturnDashboardData() throws Exception {
            // Given
            DashboardResponse mockResponse = createMockDashboardResponse();
            given(securityService.getDashboardData()).willReturn(mockResponse);

            // When & Then
            mockMvc.perform(get("/api/v1/dashboard"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").exists())
                    .andExpect(jsonPath("$.data.securityMetrics").isArray())
                    .andExpect(jsonPath("$.data.testStatusItems").isArray());

            verify(securityService).getDashboardData();
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/user-profile should return user profile")
        void getUserProfile_ShouldReturnUserProfile() throws Exception {
            // Given
            UserProfile mockProfile = createMockUserProfile();
            given(userProfileService.getCurrentUserProfile()).willReturn(mockProfile);

            // When & Then
            mockMvc.perform(get("/api/v1/user-profile"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.displayName").value("John Doe"))
                    .andExpect(jsonPath("$.data.email").value("<EMAIL>"));

            verify(userProfileService).getCurrentUserProfile();
        }

        @Test
        @DisplayName("GET /api/v1/dashboard should handle service errors gracefully")
        void getDashboardData_ServiceError_ShouldReturnErrorResponse() throws Exception {
            // Given
            given(securityService.getDashboardData())
                    .willThrow(new RuntimeException("Service unavailable"));

            // When & Then
            mockMvc.perform(get("/api/v1/dashboard"))
                    .andDo(print())
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.error").value(containsString("Failed to retrieve dashboard data")));
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/security-metrics should return security metrics")
        void getSecurityMetrics_ShouldReturnMetrics() throws Exception {
            // Given
            List<SecurityMetrics> mockMetrics = createMockSecurityMetrics();
            given(securityService.getSecurityMetrics()).willReturn(mockMetrics);

            // When & Then
            mockMvc.perform(get("/api/v1/security-metrics"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data", hasSize(mockMetrics.size())));
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/test-status-items should return test status items")
        void getTestStatusItems_ShouldReturnTestStatusItems() throws Exception {
            // Given
            List<TestStatusItem> mockItems = createMockTestStatusItems();
            given(securityService.getTestStatusItems()).willReturn(mockItems);

            // When & Then
            mockMvc.perform(get("/api/v1/test-status-items"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data", hasSize(mockItems.size())));
        }
    }

    @Nested
    @DisplayName("Alert Endpoints")
    class AlertEndpoints {

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/alerts should return all alerts")
        void getAllAlerts_ShouldReturnAlerts() throws Exception {
            // Given
            List<Alert> mockAlerts = createMockAlerts();
            given(alertsService.getAllAlerts()).willReturn(mockAlerts);

            // When & Then
            mockMvc.perform(get("/api/v1/alerts"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data", hasSize(mockAlerts.size())));

            verify(alertsService).getAllAlerts();
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/alerts with status filter should return filtered alerts")
        void getAllAlerts_WithStatusFilter_ShouldReturnFilteredAlerts() throws Exception {
            // Given
            List<Alert> filteredAlerts = createMockAlerts().subList(0, 1);
            given(alertsService.getFilteredAlerts("open", null)).willReturn(filteredAlerts);

            // When & Then
            mockMvc.perform(get("/api/v1/alerts")
                    .param("status", "open"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data", hasSize(1)));

            verify(alertsService).getFilteredAlerts("open", null);
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/alerts/{id} should return specific alert")
        void getAlertById_ShouldReturnAlert() throws Exception {
            // Given
            String alertId = "alert-123";
            Alert mockAlert = createMockAlert(alertId);
            given(alertsService.getAlertById(alertId)).willReturn(mockAlert);

            // When & Then
            mockMvc.perform(get("/api/v1/alerts/{id}", alertId))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.id").value(alertId))
                    .andExpect(jsonPath("$.data.title").exists())
                    .andExpect(jsonPath("$.data.severity").exists());

            verify(alertsService).getAlertById(alertId);
        }

        @Test
        @WithMockUser
        @DisplayName("PUT /api/v1/alerts/{id}/status should update alert status")
        void updateAlertStatus_ShouldUpdateStatus() throws Exception {
            // Given
            String alertId = "alert-123";
            String newStatus = "resolved";
            Alert updatedAlert = createMockAlert(alertId, newStatus);
            given(alertsService.updateAlertStatus(alertId, newStatus)).willReturn(updatedAlert);

            Map<String, String> statusUpdate = Map.of("status", newStatus);

            // When & Then
            mockMvc.perform(put("/api/v1/alerts/{id}/status", alertId)
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(statusUpdate)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.id").value(alertId))
                    .andExpect(jsonPath("$.message").value("Alert status updated successfully"));

            verify(alertsService).updateAlertStatus(alertId, newStatus);
        }

        @Test
        @WithMockUser
        @DisplayName("PUT /api/v1/alerts/{id}/status with empty status should return bad request")
        void updateAlertStatus_EmptyStatus_ShouldReturnBadRequest() throws Exception {
            // Given
            String alertId = "alert-123";
            Map<String, String> statusUpdate = Map.of("status", "");

            // When & Then
            mockMvc.perform(put("/api/v1/alerts/{id}/status", alertId)
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(statusUpdate)))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.message").value("Status is required"));
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/alerts/{id} with non-existent ID should return not found")
        void getAlertById_NonExistentId_ShouldReturnNotFound() throws Exception {
            // Given
            String alertId = "non-existent";
            given(alertsService.getAlertById(alertId)).willReturn(null);

            // When & Then
            mockMvc.perform(get("/api/v1/alerts/{id}", alertId))
                    .andDo(print())
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.message").value(containsString("Alert not found")));
        }
    }

    @Nested
    @DisplayName("System Log Endpoints")
    class SystemLogEndpoints {

        @ParameterizedTest
        @ValueSource(ints = {1, 2, 5, 10, 25})
        @WithMockUser
        @DisplayName("GET /api/v1/systemlog should handle different page sizes")
        void getSystemLogs_DifferentPageSizes_ShouldReturnPagedResults(int pageSize) throws Exception {
            // Given
            SystemLogResponse mockResponse = createMockSystemLogResponse(pageSize);
            given(systemLogService.getSystemLogs(any(), any(), any(), any(), any(), any(), anyInt(), anyInt()))
                    .willReturn(mockResponse);

            // When & Then
            mockMvc.perform(get("/api/v1/systemlog")
                            .param("pageSize", String.valueOf(pageSize)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").exists());

            verify(systemLogService).getSystemLogs(null, null, null, null, null, null, 1, pageSize);
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/systemlog with search should filter results")
        void getSystemLogs_WithSearch_ShouldFilterResults() throws Exception {
            // Given
            String searchTerm = "error";
            SystemLogResponse mockResponse = createMockSystemLogResponse();
            given(systemLogService.getSystemLogs(eq(searchTerm), any(), any(), any(), any(), any(), anyInt(), anyInt()))
                    .willReturn(mockResponse);

            // When & Then
            mockMvc.perform(get("/api/v1/systemlog")
                            .param("search", searchTerm))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));

            verify(systemLogService).getSystemLogs(eq(searchTerm), any(), any(), any(), any(), any(), eq(1), eq(10));
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/systemlog with date filters should filter by date range")
        void getSystemLogs_WithDateFilters_ShouldFilterByDateRange() throws Exception {
            // Given
            String startDate = "2025-01-01";
            String endDate = "2025-01-31";
            SystemLogResponse mockResponse = createMockSystemLogResponse();
            given(systemLogService.getSystemLogs(any(), eq(startDate), eq(endDate), any(), any(), any(), anyInt(), anyInt()))
                    .willReturn(mockResponse);

            // When & Then
            mockMvc.perform(get("/api/v1/systemlog")
                            .param("startDate", startDate)
                            .param("endDate", endDate))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));

            verify(systemLogService).getSystemLogs(any(), eq(startDate), eq(endDate), any(), any(), any(), eq(1), eq(10));
        }
    }

    @Nested
    @DisplayName("Notification Endpoints")
    class NotificationEndpoints {

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/notifications should return paginated response")
        void getNotifications_ShouldReturnPaginatedResponse() throws Exception {
            // Given
            List<NotificationData> mockNotifications = createMockNotifications(20);
            given(securityService.getNotifications()).willReturn(mockNotifications);

            // When & Then
            mockMvc.perform(get("/api/v1/notifications")
                            .param("page", "0")
                            .param("size", "7"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.items").isArray())
                    .andExpect(jsonPath("$.data.items", hasSize(7)))
                    .andExpect(jsonPath("$.data.pagination.currentPage").value(0))
                    .andExpect(jsonPath("$.data.pagination.pageSize").value(7))
                    .andExpect(jsonPath("$.data.pagination.totalElements").value(20))
                    .andExpect(jsonPath("$.data.pagination.hasNext").value(true));
        }

        @Test
        @WithMockUser
        @DisplayName("GET /api/v1/notifications with invalid page should return empty results")
        void getNotifications_WithInvalidPage_ShouldReturnEmptyResults() throws Exception {
            // Given
            List<NotificationData> mockNotifications = createMockNotifications(5);
            given(securityService.getNotifications()).willReturn(mockNotifications);

            // When & Then
            mockMvc.perform(get("/api/v1/notifications")
                            .param("page", "10")
                            .param("size", "7"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data.items").isArray())
                    .andExpect(jsonPath("$.data.items", hasSize(0)));
        }
    }

    @Nested
    @DisplayName("Validation Tests")
    class ValidationTests {

        @Test
        @WithMockUser
        @DisplayName("Request with invalid page number should return bad request")
        void invalidPageNumber_ShouldReturnBadRequest() throws Exception {
            mockMvc.perform(get("/api/v1/notifications")
                            .param("page", "-1")) // Invalid: page should be >= 0
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.message").value(containsString("Invalid page number")));
        }

        @Test
        @WithMockUser
        @DisplayName("Request with invalid page size should return bad request")
        void invalidPageSize_ShouldReturnBadRequest() throws Exception {
            mockMvc.perform(get("/api/v1/notifications")
                            .param("page", "0")
                            .param("size", "200")) // Invalid: size should be <= 100
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.message").value(containsString("Invalid page size")));
        }

        @Test
        @WithMockUser
        @DisplayName("Request with zero page size should return bad request")
        void zeroPageSize_ShouldReturnBadRequest() throws Exception {
            mockMvc.perform(get("/api/v1/notifications")
                            .param("page", "0")
                            .param("size", "0"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());
        }

        @Test
        @WithMockUser
        @DisplayName("Request with invalid date format should return bad request")
        void invalidDateFormat_ShouldReturnBadRequest() throws Exception {
            mockMvc.perform(get("/api/v1/systemlog")
                            .param("startDate", "invalid-date"))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.message").value(containsString("Invalid date format")));
        }
    }

    @Nested
    @DisplayName("Security Tests")
    class SecurityTests {

        @Test
        @DisplayName("Public endpoints without authentication should be accessible")
        void publicEndpoints_WithoutAuth_ShouldBeAccessible() throws Exception {
            mockMvc.perform(get("/api/v1/test"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));

            mockMvc.perform(get("/api/v1/health"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));

            mockMvc.perform(get("/api/v1/status"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));
        }

        @Test
        @DisplayName("Protected endpoints without authentication should return unauthorized")
        void protectedEndpoints_WithoutAuth_ShouldReturnUnauthorized() throws Exception {
            // Note: This test depends on your security configuration
            // Adjust based on actual security requirements
            mockMvc.perform(get("/api/v1/dashboard"))
                    .andExpect(status().isUnauthorized());

            mockMvc.perform(get("/api/v1/alerts"))
                    .andExpect(status().isUnauthorized());

            mockMvc.perform(get("/api/v1/user-profile"))
                    .andExpect(status().isUnauthorized());
        }

        @Test
        @WithMockUser(roles = "USER")
        @DisplayName("Endpoints with user role should be accessible")
        void endpointsWithUserRole_ShouldBeAccessible() throws Exception {
            // Given
            given(securityService.getDashboardData()).willReturn(createMockDashboardResponse());

            // When & Then
            mockMvc.perform(get("/api/v1/dashboard"))
                    .andExpect(status().isOk());
        }

        @Test
        @WithMockUser(roles = "ADMIN")
        @DisplayName("Endpoints with admin role should be accessible")
        void endpointsWithAdminRole_ShouldBeAccessible() throws Exception {
            // Given
            given(alertsService.getAllAlerts()).willReturn(createMockAlerts());

            // When & Then
            mockMvc.perform(get("/api/v1/alerts"))
                    .andExpect(status().isOk());
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @WithMockUser
        @DisplayName("Service exceptions should return proper error responses")
        void serviceExceptions_ShouldReturnProperErrorResponses() throws Exception {
            // Given
            given(securityService.getDashboardData()).willThrow(new RuntimeException("Database connection failed"));

            // When & Then
            mockMvc.perform(get("/api/v1/dashboard"))
                    .andDo(print())
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.success").value(false))
                    .andExpect(jsonPath("$.error").exists())
                    .andExpect(jsonPath("$.timestamp").exists())
                    .andExpect(jsonPath("$.requestId").exists());
        }

        @Test
        @DisplayName("Invalid JSON should return bad request")
        void invalidJson_ShouldReturnBadRequest() throws Exception {
            String invalidJson = "{invalid json}";

            mockMvc.perform(post("/api/v1/test")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(invalidJson))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.success").value(false));
        }

        @Test
        @DisplayName("Unsupported media type should return unsupported media type")
        void unsupportedMediaType_ShouldReturnUnsupportedMediaType() throws Exception {
            mockMvc.perform(post("/api/v1/test")
                            .contentType(MediaType.TEXT_PLAIN)
                            .content("plain text"))
                    .andDo(print())
                    .andExpect(status().isUnsupportedMediaType());
        }

        @Test
        @DisplayName("Method not allowed should return method not allowed")
        void methodNotAllowed_ShouldReturnMethodNotAllowed() throws Exception {
            // Assuming PATCH is not supported on /api/v1/test
            mockMvc.perform(patch("/api/v1/test"))
                    .andDo(print())
                    .andExpect(status().isMethodNotAllowed());
        }
    }

    // ============================================================================
    // HELPER METHODS FOR MOCK DATA CREATION
    // ============================================================================

    private DashboardResponse createMockDashboardResponse() {
        return new DashboardResponse(
                null, // failedFindings
                Collections.emptyList(), // topRiskUsers
                createMockSecurityMetrics(), // securityMetrics
                Collections.emptyList(), // topRisks
                Collections.emptyList(), // riskOverTime
                Collections.emptyList(), // statusCards
                createMockTestStatusItems(), // testStatusItems
                Collections.emptyList(), // notifications
                null // nextScanInfo
        );
    }

    private UserProfile createMockUserProfile() {
        return new UserProfile("John Doe", "<EMAIL>", "Administrator");
    }

    private List<SecurityMetrics> createMockSecurityMetrics() {
        return Arrays.asList(
                new SecurityMetrics("Identity", "safe", 0, 50),
                new SecurityMetrics("Data", "warning", 5, 30),
                new SecurityMetrics("Network", "critical", 2, 80)
        );
    }

    private List<TestStatusItem> createMockTestStatusItems() {
        return Arrays.asList(
                new TestStatusItem("Ok", 100, "#00FF00", TestStatusItem.IconType.ok, "linear-gradient(135deg, #00FF00, #008000)", 1.0),
                new TestStatusItem("Warning", 5, "#FFA500", TestStatusItem.IconType.warning, "linear-gradient(135deg, #FFA500, #FF8C00)", 0.8),
                new TestStatusItem("Critical", 2, "#FF0000", TestStatusItem.IconType.critical, "linear-gradient(135deg, #FF0000, #8B0000)", 0.9)
        );
    }

    private List<Alert> createMockAlerts() {
        return Arrays.asList(
                createMockAlert("alert-1", "open"),
                createMockAlert("alert-2", "resolved"),
                createMockAlert("alert-3", "open")
        );
    }

    private Alert createMockAlert(String id) {
        return createMockAlert(id, "open");
    }

    private Alert createMockAlert(String id, String status) {
        Alert.AlertStatus alertStatus;
        try {
            alertStatus = Alert.AlertStatus.valueOf(status);
        } catch (IllegalArgumentException e) {
            alertStatus = Alert.AlertStatus.open;
        }

        return new Alert(
                id,
                "Test Alert " + id,
                "Test Description for " + id,
                Alert.AlertSeverity.high,
                alertStatus,
                Alert.AlertCategory.security,
                "2025-06-02T10:00:00Z",
                "Test Source",
                "Test Resource",
                "Test Type",
                null,
                Collections.emptyList(),
                // Enhanced baseline policy information
                "TEST.POLICY." + id,
                "Test Policy Title",
                "Test implementation guidance",
                "SHOULD",
                Collections.emptyList(),
                "Test report details"
        );
    }

    private List<NotificationData> createMockNotifications(int count) {
        return Collections.nCopies(count, new NotificationData(
                "notification-1",
                NotificationData.NotificationType.medium,
                new NotificationData.ServiceInfo("Test Service", "service-icon"),
                new NotificationData.ApplicationInfo("Test App", "app-icon"),
                "Test notification message"
        ));
    }

    private SystemLogResponse createMockSystemLogResponse() {
        return createMockSystemLogResponse(10);
    }

    private SystemLogResponse createMockSystemLogResponse(int size) {
        // Create mock system log response with specified size
        List<SystemLogEntry> entries = Collections.nCopies(size, createMockSystemLogEntry());
        return new SystemLogResponse(entries, size);
    }

    private SystemLogEntry createMockSystemLogEntry() {
        return new SystemLogEntry(
                "entry-1",
                "User Login",
                "Success",
                new SystemLogEntry.User("testuser", "<EMAIL>"),
                "192.168.1.100",
                SystemLogEntry.DateInfo.create("2025-06-02", "10:00:00Z"),
                "Test Browser"
        );
    }
}
