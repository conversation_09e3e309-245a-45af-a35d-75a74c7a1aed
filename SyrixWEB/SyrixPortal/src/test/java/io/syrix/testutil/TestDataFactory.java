package io.syrix.testutil;

import io.syrix.datamodel.Customer;
import io.syrix.model.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Comprehensive test data factory for creating mock objects
 * Enhanced for API Unit Testing Strategy Step 3
 */
public class TestDataFactory {

    // ============================================================================
    // COMPANY TEST DATA
    // ============================================================================
    
    public static Customer createTestClient() {
        return createTestClient("Test Client", "test-tenant");
    }

    public static Customer createTestClient(String name, String tenantId) {
        Customer customer = new Customer();
        customer.setId(UUID.randomUUID());
        customer.setName(name);
        customer.setDisplayName(name);
        customer.setMicrosoftTenantId(tenantId);
        customer.setContactEmail(name.toLowerCase().replace(" ", "") + "@example.com");
        customer.setContactPhone("******-0123");
        customer.setStatus("active");
        customer.setCreatedAt(LocalDateTime.now());
        customer.setUpdatedAt(LocalDateTime.now());
        return customer;
    }

    public static List<Customer> createTestCompanies(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> createTestClient("Client " + (i + 1), "tenant-" + (i + 1)))
                .collect(Collectors.toList());
    }

    // ============================================================================
    // ALERT TEST DATA
    // ============================================================================
    
    public static Alert createTestAlert() {
        return createTestAlert("TEST-001", Alert.AlertStatus.open);
    }

    public static Alert createTestAlert(String id, Alert.AlertStatus status) {
        return new Alert(
                id,
                "Test Alert " + id,
                "Test alert description for " + id,
                Alert.AlertSeverity.medium,
                status,
                Alert.AlertCategory.security,
                LocalDateTime.now().toString(),
                "Test Source",
                "Test Resource",
                "Test Type",
                null,
                Collections.emptyList(),
                // Enhanced baseline policy information
                "TEST.POLICY." + id,
                "Test Policy Title",
                "Test implementation guidance",
                "SHOULD",
                Collections.emptyList(),
                "Test report details"
        );
    }

    public static Alert createTestAlert(String id, Alert.AlertSeverity severity, Alert.AlertStatus status) {
        return new Alert(
                id,
                "Test Alert " + id,
                "Test alert description for " + id,
                severity,
                status,
                Alert.AlertCategory.security,
                LocalDateTime.now().toString(),
                "Test Source",
                "Test Resource",
                "Test Type",
                null,
                Collections.emptyList(),
                // Enhanced baseline policy information
                "TEST.POLICY." + id,
                "Test Policy Title",
                "Test implementation guidance",
                "SHOULD",
                Collections.emptyList(),
                "Test report details"
        );
    }

    public static List<Alert> createTestAlerts(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> createTestAlert("TEST-" + String.format("%03d", i), Alert.AlertStatus.open))
                .collect(Collectors.toList());
    }

    public static List<Alert> createTestAlertsWithMixedStatus(int count) {
        Alert.AlertStatus[] statuses = Alert.AlertStatus.values();
        return IntStream.range(0, count)
                .mapToObj(i -> createTestAlert(
                        "MIXED-" + String.format("%03d", i),
                        statuses[i % statuses.length]
                ))
                .collect(Collectors.toList());
    }

    // ============================================================================
    // USER PROFILE TEST DATA
    // ============================================================================
    
    public static UserProfile createTestUserProfile() {
        return new UserProfile(
                "Test User",
                "<EMAIL>",
                "Administrator"
        );
    }

    public static UserProfile createTestUserProfile(String name, String email, String role) {
        return new UserProfile(name, email, role);
    }

    public static List<UserProfile> createTestUserProfiles(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> createTestUserProfile(
                        "User " + (i + 1),
                        "user" + (i + 1) + "@example.com",
                        i % 2 == 0 ? "Administrator" : "User"
                ))
                .collect(Collectors.toList());
    }

    // ============================================================================
    // DASHBOARD RESPONSE TEST DATA
    // ============================================================================
    
    public static DashboardResponse createTestDashboardResponse() {
        return new DashboardResponse(
                null, // failedFindings
                Collections.emptyList(), // topRiskUsers
                createTestSecurityMetrics(), // securityMetrics
                Collections.emptyList(), // topRisks
                Collections.emptyList(), // riskOverTime
                createTestStatusCards(), // statusCards
                createTestStatusItems(), // testStatusItems
                createTestNotifications(5), // notifications
                null // nextScanInfo
        );
    }

    // ============================================================================
    // STATUS CARDS TEST DATA
    // ============================================================================
    
    public static List<io.syrix.model.StatusCardData> createTestStatusCards() {
        return Arrays.asList(
                new io.syrix.model.StatusCardData("Critical Issues", "critical", "Critical Status", 5, 100),
                new io.syrix.model.StatusCardData("Warning Issues", "warning", "Warning Status", 15, 100),
                new io.syrix.model.StatusCardData("Info Issues", "info", "Info Status", 25, 100),
                new io.syrix.model.StatusCardData("Success Issues", "success", "Success Status", 55, 100)
        );
    }

    // ============================================================================
    // SECURITY METRICS TEST DATA
    // ============================================================================
    
    public static List<SecurityMetrics> createTestSecurityMetrics() {
        return Arrays.asList(
                new SecurityMetrics("Identity", "safe", 0, 95),
                new SecurityMetrics("Data", "warning", 3, 78),
                new SecurityMetrics("Network", "critical", 7, 62),
                new SecurityMetrics("Applications", "safe", 1, 87)
        );
    }

    public static SecurityMetrics createTestSecurityMetric(String category, String status, int riskCount, int score) {
        return new SecurityMetrics(category, status, riskCount, score);
    }

    // ============================================================================
    // TEST STATUS ITEMS
    // ============================================================================
    
    public static List<TestStatusItem> createTestStatusItems() {
        return Arrays.asList(
                new TestStatusItem("Ok", 120, "#0F657F", TestStatusItem.IconType.ok, "linear-gradient(135deg, #0F657F, #08455a)", 1.0),
                new TestStatusItem("Warning", 15, "#FFA500", TestStatusItem.IconType.warning, "linear-gradient(135deg, #FFA500, #cc8400)", 0.8),
                new TestStatusItem("Critical", 5, "#FF0000", TestStatusItem.IconType.critical, "linear-gradient(135deg, #FF0000, #cc0000)", 0.9),
                new TestStatusItem("AutoFixed", 10, "#08B798", TestStatusItem.IconType.ok, "linear-gradient(135deg, #08B798, #069279)", 0.7)
        );
    }

    public static TestStatusItem createTestStatusItem(String label, int count, String color, TestStatusItem.IconType iconType) {
        return new TestStatusItem(label, count, color, iconType, "linear-gradient(135deg, " + color + ", #000000)", 1.0);
    }

    // ============================================================================
    // SYSTEM LOG ENTRY TEST DATA
    // ============================================================================
    
    public static SystemLogEntry createTestSystemLogEntry() {
        LocalDateTime now = LocalDateTime.now();
        return new SystemLogEntry(
                UUID.randomUUID().toString(),
                "User Login",
                "Success",
                new SystemLogEntry.User("testuser", "<EMAIL>"),
                "192.168.1.100",
                SystemLogEntry.DateInfo.create(now.toLocalDate().toString(), now.toLocalTime().toString()),
                "Test Browser/1.0"
        );
    }

    public static SystemLogEntry createTestSystemLogEntry(String activity, String status) {
        LocalDateTime now = LocalDateTime.now();
        return new SystemLogEntry(
                UUID.randomUUID().toString(),
                activity,
                status,
                new SystemLogEntry.User("testuser", "<EMAIL>"),
                "192.168.1.100",
                SystemLogEntry.DateInfo.create(now.toLocalDate().toString(), now.toLocalTime().toString()),
                "Test Browser/1.0"
        );
    }

    public static List<SystemLogEntry> createTestSystemLogEntries(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> createTestSystemLogEntry(
                        "Activity " + (i + 1),
                        i % 3 == 0 ? "Failed" : "Success"
                ))
                .collect(Collectors.toList());
    }

    public static SystemLogResponse createTestSystemLogResponse(int count) {
        List<SystemLogEntry> entries = createTestSystemLogEntries(count);
        return new SystemLogResponse(entries, count, 1, Math.min(count, 10), count > 10);
    }

    // ============================================================================
    // NOTIFICATION TEST DATA
    // ============================================================================
    
    public static List<NotificationData> createTestNotifications(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> new NotificationData(
                        "notification-" + (i + 1),
                        i % 3 == 0 ? NotificationData.NotificationType.high : NotificationData.NotificationType.medium,
                        new NotificationData.ServiceInfo("Service " + (i + 1), "service-icon"),
                        new NotificationData.ApplicationInfo("App " + (i + 1), "app-icon"),
                        "Test notification message " + (i + 1)
                ))
                .collect(Collectors.toList());
    }

    // ============================================================================
    // BUILDER PATTERNS FOR COMPLEX SCENARIOS
    // ============================================================================
    
    public static class AlertBuilder {
        private String id = "TEST-" + UUID.randomUUID().toString().substring(0, 8);
        private String title = "Test Alert";
        private String description = "Test description";
        private Alert.AlertSeverity severity = Alert.AlertSeverity.medium;
        private Alert.AlertStatus status = Alert.AlertStatus.open;
        private Alert.AlertCategory category = Alert.AlertCategory.security;
        private String timestamp = LocalDateTime.now().toString();
        private String source = "Test Source";
        private String affectedResource = "Test Resource";
        private String affectedResourceType = "Test Type";
        private String assignedTo = "<EMAIL>";
        private List<String> tags = Collections.emptyList();

        public AlertBuilder withId(String id) {
            this.id = id;
            return this;
        }

        public AlertBuilder withTitle(String title) {
            this.title = title;
            return this;
        }

        public AlertBuilder withStatus(Alert.AlertStatus status) {
            this.status = status;
            return this;
        }

        public AlertBuilder withSeverity(Alert.AlertSeverity severity) {
            this.severity = severity;
            return this;
        }

        public AlertBuilder withCategory(Alert.AlertCategory category) {
            this.category = category;
            return this;
        }

        public AlertBuilder withDescription(String description) {
            this.description = description;
            return this;
        }

        public Alert build() {
            return new Alert(id, title, description, severity, status, category, 
                           timestamp, source, affectedResource, affectedResourceType, assignedTo, tags,
                           // Enhanced baseline policy information - use defaults for builder
                           "TEST.POLICY." + id,
                           "Test Policy Title",
                           "Test implementation guidance",
                           "SHOULD",
                           Collections.emptyList(),
                           "Test report details");
        }
    }

    public static class ClientBuilder {
        private Customer customer = new Customer();

        public ClientBuilder withName(String name) {
            customer.setName(name);
            return this;
        }

        public ClientBuilder withMicrosoftTenantId(String tenantId) {
            customer.setMicrosoftTenantId(tenantId);
            return this;
        }

        public ClientBuilder withContactEmail(String contactEmail) {
            customer.setContactEmail(contactEmail);
            return this;
        }

        public ClientBuilder withDisplayName(String displayName) {
            customer.setDisplayName(displayName);
            return this;
        }

        public ClientBuilder withStatus(String status) {
            customer.setStatus(status);
            return this;
        }

        public Customer build() {
            if (customer.getId() == null) {
                customer.setId(UUID.randomUUID());
            }
            if (customer.getName() == null) {
                customer.setName("Test Client");
            }
            if (customer.getMicrosoftTenantId() == null) {
                customer.setMicrosoftTenantId("test-tenant-" + UUID.randomUUID().toString().substring(0, 8));
            }
            if (customer.getContactEmail() == null) {
                customer.setContactEmail(customer.getName().toLowerCase().replace(" ", "") + "@example.com");
            }
            if (customer.getDisplayName() == null) {
                customer.setDisplayName(customer.getName() + " Display");
            }
            if (customer.getContactPhone() == null) {
                customer.setContactPhone("******-0123");
            }
            if (customer.getCreatedAt() == null) {
                customer.setCreatedAt(LocalDateTime.now());
            }
            if (customer.getUpdatedAt() == null) {
                customer.setUpdatedAt(LocalDateTime.now());
            }
            // Client status is already set in constructor or builder
            return customer;
        }
    }

    public static class SecurityMetricsBuilder {
        private String category;
        private String status;
        private int riskCount;
        private int score;

        public SecurityMetricsBuilder withCategory(String category) {
            this.category = category;
            return this;
        }

        public SecurityMetricsBuilder withStatus(String status) {
            this.status = status;
            return this;
        }

        public SecurityMetricsBuilder withRiskCount(int riskCount) {
            this.riskCount = riskCount;
            return this;
        }

        public SecurityMetricsBuilder withScore(int score) {
            this.score = score;
            return this;
        }

        public SecurityMetrics build() {
            return new SecurityMetrics(
                    category != null ? category : "Test Category",
                    status != null ? status : "safe",
                    riskCount,
                    score > 0 ? score : 85
            );
        }
    }

    // ============================================================================
    // TEST DATA CLEANUP UTILITIES
    // ============================================================================
    
    public static void cleanupTestData(org.springframework.data.mongodb.core.MongoTemplate mongoTemplate) {
        mongoTemplate.getDb().drop();
    }

    public static void cleanupTestDataCollections(org.springframework.data.mongodb.core.MongoTemplate mongoTemplate, String... collections) {
        for (String collection : collections) {
            mongoTemplate.dropCollection(collection);
        }
    }

    // ============================================================================
    // VALIDATION UTILITIES
    // ============================================================================
    
    public static boolean isValidTestAlert(Alert alert) {
        return alert != null
                && alert.id() != null
                && !alert.id().isEmpty()
                && alert.title() != null
                && !alert.title().isEmpty()
                && alert.status() != null
                && alert.severity() != null;
    }

    public static boolean isValidTestClient(Customer customer) {
        return customer != null
               && customer.getId() != null
               && customer.getName() != null
               && !customer.getName().isEmpty()
               && customer.getMicrosoftTenantId() != null
               && !customer.getMicrosoftTenantId().isEmpty();
    }

    // ============================================================================
    // INNER CLASSES FOR TEST DATA
    // ============================================================================
    
    public static class TestNotification {
        private final String id;
        private final String title;
        private final String message;
        private final String type;
        private final String timestamp;

        public TestNotification(String id, String title, String message, String type, String timestamp) {
            this.id = id;
            this.title = title;
            this.message = message;
            this.type = type;
            this.timestamp = timestamp;
        }

        // Getters
        public String getId() { return id; }
        public String getTitle() { return title; }
        public String getMessage() { return message; }
        public String getType() { return type; }
        public String getTimestamp() { return timestamp; }
    }

    public static class StatusCardData {
        private final String label;
        private final int count;
        private final String color;

        public StatusCardData(String label, int count, String color) {
            this.label = label;
            this.count = count;
            this.color = color;
        }

        // Getters
        public String getLabel() { return label; }
        public int getCount() { return count; }
        public String getColor() { return color; }
    }

    public static class SystemLogResponse {
        private final List<SystemLogEntry> entries;
        private final int totalCount;
        private final int currentPage;
        private final int pageSize;
        private final boolean hasNext;

        public SystemLogResponse(List<SystemLogEntry> entries, int totalCount, int currentPage, int pageSize, boolean hasNext) {
            this.entries = entries;
            this.totalCount = totalCount;
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.hasNext = hasNext;
        }

        // Getters
        public List<SystemLogEntry> getEntries() { return entries; }
        public int getTotalCount() { return totalCount; }
        public int getCurrentPage() { return currentPage; }
        public int getPageSize() { return pageSize; }
        public boolean isHasNext() { return hasNext; }
    }
}
