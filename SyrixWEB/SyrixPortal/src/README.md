# Security Dashboard

## Prerequisites

- Node.js (v14 or later)
- Yarn package manager

## Installation

### Installing Yarn on macOS

#### Using Homebrew
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Yarn
brew install yarn
```

#### Using npm
1. Install Node.js from [nodejs.org](https://nodejs.org)
2. Install Yarn:
```bash
npm install -g yarn
```

### Project Setup
```bash
# Clone repository
git clone [repository-url]
cd security-dashboard

# Install dependencies
yarn install
```

## Development

```bash
# Start development server
yarn start
```

The application will be available at `http://localhost:3000`.

## Debugging

### Browser Tools
1. **React Developer Tools**
   - Install Chrome/Firefox extension
   - Inspect component hierarchy and state

2. **Redux DevTools**
   - Monitor Redux store and actions
   - Time-travel debugging

### Debug Logging
```javascript
// Enable all debugging
localStorage.setItem('debug', 'security-dashboard:*');

// Enable specific features
localStorage.setItem('debug', 'security-dashboard:api,security-dashboard:auth');
```

### VS Code Debugging
Add to `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Application",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    }
  ]
}
```

## Testing

```bash
# Run all tests
yarn test

# Watch mode
yarn test --watch

# Run specific tests
yarn test path/to/testfile.tsx
```

## Production Build

```bash
# Create production build
yarn build
```

### Serving Production Build Locally
```bash
# Install serve globally
npm install -g serve

# Serve build directory
serve -s build
```

Visit `http://localhost:5000` to view the production build.

## Environment Configuration

Create `.env` file in project root:
```env
# Required
REACT_APP_API_URL=https://api.example.com

# Optional
REACT_APP_ENV=development
REACT_APP_DEBUG=true
REACT_APP_API_TIMEOUT=5000
```

## Project Structure

```
.
├── api/
│   └── index.ts              # API configuration
├── app/
│   ├── hooks.ts              # Redux hooks
│   └── store.ts              # Redux store
├── components/
│   ├── Alerts.tsx           # Alert management
│   ├── Dashboard.tsx        # Main dashboard
│   ├── SecurityChecks.tsx   # Security monitoring
│   └── Settings.tsx         # App configuration
├── features/
│   ├── dashboard/           # Dashboard feature
│   │   ├── actions.ts
│   │   ├── reducer.ts
│   │   └── selectors.ts
│   └── theme/              # Theme management
│       ├── actions.ts
│       └── reducer.ts
├── styles/
│   ├── darkTheme.ts        # Dark theme config
│   ├── global.css          # Global styles
│   └── lightTheme.ts       # Light theme config
└── types/
    └── dashboard.ts        # TypeScript definitions
```

## Performance Optimization

1. React DevTools Profiler
   - Monitor component rendering
   - Analyze performance bottlenecks

2. Lighthouse Reports
   - Run audits in Chrome DevTools
   - Check performance metrics
   - Review best practices

## Tech Stack

- React 18
- TypeScript 4.x
- Redux (with Redux Toolkit)
- Tailwind CSS
- Jest & Testing Library
