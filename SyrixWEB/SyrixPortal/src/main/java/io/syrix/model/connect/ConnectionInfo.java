package io.syrix.model.connect;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Information about a connection to an external service.
 */
public class ConnectionInfo {
    private UUID id;
    private String service;
    private boolean connected;
    private String displayName;
    private LocalDateTime lastConnected;
    private String externalTenantId;
    private UUID companyId;
    private String domain;
    private Token token;

    public ConnectionInfo(String service, boolean connected, String displayName, LocalDateTime lastConnected, String externalTenantId, UUID companyId, String domain) {
        this.service = service;
        this.connected = connected;
        this.displayName = displayName;
        this.lastConnected = lastConnected;
        this.externalTenantId = externalTenantId;
        this.companyId = companyId;
        this.domain = domain;
    }
    public ConnectionInfo(UUID id, String service, boolean connected, String displayName, LocalDateTime lastConnected, String externalTenantId, UUID companyId, String domain) {
        this(service, connected, displayName, lastConnected, externalTenantId, companyId, domain);
        this.id = id;
    }

    // Getters and setters
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public String getService() {
        return service;
    }
    
    public void setService(String service) {
        this.service = service;
    }
    
    public boolean isConnected() {
        return connected;
    }
    
    public void setConnected(boolean connected) {
        this.connected = connected;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public LocalDateTime getLastConnected() {
        return lastConnected;
    }
    
    public void setLastConnected(LocalDateTime lastConnected) {
        this.lastConnected = lastConnected;
    }
    
    public String getExternalTenantId() {
        return externalTenantId;
    }
    
    public void setExternalTenantId(String externalTenantId) {
        this.externalTenantId = externalTenantId;
    }

    public UUID getCompanyId() {
        return companyId;
    }
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    /**
     * Gets the authentication token for the connection.
     * @return the authentication token
     */
    public Token getToken() {
        return token;
    }
    
    /**
     * Sets the authentication token for the connection.
     * @param token the authentication token
     */
    public void setToken(Token token) {
        this.token = token;
    }
}
