package io.syrix.model.connect;

import java.time.Instant;

/**
 * Authentication token information for connections.
 */
public class Token {
    private TokenType tokenType;
    private String token;
    private Instant tokenExpiresAt;
    private String scopes;
    private Instant createdAt;

    // Constructors
    public Token() {}

    public Token(TokenType tokenType, String token, Instant tokenExpiresAt, String scopes, Instant createdAt) {
        this.tokenType = tokenType;
        this.token = token;
        this.tokenExpiresAt = tokenExpiresAt;
        this.scopes = scopes;
        this.createdAt = createdAt;
    }

    public TokenType getTokenType() {
        return tokenType;
    }

    public void setTokenType(TokenType tokenType) {
        this.tokenType = tokenType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Instant getTokenExpiresAt() {
        return tokenExpiresAt;
    }

    public void setTokenExpiresAt(Instant tokenExpiresAt) {
        this.tokenExpiresAt = tokenExpiresAt;
    }

    public String getScopes() {
        return scopes;
    }

    public void setScopes(String scopes) {
        this.scopes = scopes;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
}
