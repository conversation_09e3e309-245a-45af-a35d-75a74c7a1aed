package io.syrix.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.UUID;

/**
 * Standardized API response wrapper for all API endpoints.
 * Provides consistent structure for success and error responses.
 * 
 * @param <T> The type of data being returned
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public record ApiResponse<T>(
        @JsonProperty("success")
        boolean success,
        
        @JsonProperty("data")
        T data,
        
        @JsonProperty("message")
        String message,
        
        @JsonProperty("error")
        String error,
        
        @JsonProperty("timestamp")
        String timestamp,
        
        @JsonProperty("requestId")
        String requestId,
        
        @JsonProperty("apiVersion")
        String apiVersion
) {
    
    private static final String API_VERSION = "1.0.0";
    
    /**
     * Create a successful response with data
     * @param data The response data
     * @return ApiResponse with success=true
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(
                true,
                data,
                null,
                null,
                Instant.now().toString(),
                UUID.randomUUID().toString(),
                API_VERSION
        );
    }
    
    /**
     * Create a successful response with data and custom message
     * @param data The response data
     * @param message Success message
     * @return ApiResponse with success=true
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(
                true,
                data,
                message,
                null,
                Instant.now().toString(),
                UUID.randomUUID().toString(),
                API_VERSION
        );
    }
    
    /**
     * Create a successful response with only a message (no data)
     * @param message Success message
     * @return ApiResponse with success=true
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(
                true,
                null,
                message,
                null,
                Instant.now().toString(),
                UUID.randomUUID().toString(),
                API_VERSION
        );
    }
    
    /**
     * Create an error response
     * @param error Error message
     * @return ApiResponse with success=false
     */
    public static <T> ApiResponse<T> error(String error) {
        return new ApiResponse<>(
                false,
                null,
                null,
                error,
                Instant.now().toString(),
                UUID.randomUUID().toString(),
                API_VERSION
        );
    }
    
    /**
     * Create an error response with custom request ID
     * @param error Error message
     * @param requestId Custom request ID for tracking
     * @return ApiResponse with success=false
     */
    public static <T> ApiResponse<T> error(String error, String requestId) {
        return new ApiResponse<>(
                false,
                null,
                null,
                error,
                Instant.now().toString(),
                requestId,
                API_VERSION
        );
    }
}
