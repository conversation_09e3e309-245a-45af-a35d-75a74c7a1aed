package io.syrix.model.auth;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Request model for user registration
 */
public class RegisterRequest {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("password")
    private String password;
    
    @JsonProperty("companyName")
    private String companyName;
    
    @JsonProperty("industry")
    private String industry;
    
    @JsonProperty("employees")
    private String employees;
    
    public RegisterRequest() {
    }
    
    public RegisterRequest(String name, String email, String password, String companyName) {
        this.name = name;
        this.email = email;
        this.password = password;
        this.companyName = companyName;
    }
    
    public RegisterRequest(String name, String email, String password, String companyName, String industry, String employees) {
        this.name = name;
        this.email = email;
        this.password = password;
        this.companyName = companyName;
        this.industry = industry;
        this.employees = employees;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getCompanyName() {
        return companyName;
    }
    
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    
    public String getIndustry() {
        return industry;
    }
    
    public void setIndustry(String industry) {
        this.industry = industry;
    }
    
    public String getEmployees() {
        return employees;
    }
    
    public void setEmployees(String employees) {
        this.employees = employees;
    }
    
    @Override
    public String toString() {
        return "RegisterRequest{" +
                "name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", password='[REDACTED]'" +
                ", companyName='" + companyName + '\'' +
                ", industry='" + industry + '\'' +
                ", employees='" + employees + '\'' +
                '}';
    }
}
