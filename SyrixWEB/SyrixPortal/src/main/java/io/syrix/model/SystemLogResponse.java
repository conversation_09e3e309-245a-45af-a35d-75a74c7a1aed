package io.syrix.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record SystemLogResponse(
    List<SystemLogEntry> logs,
    int totalCount
) {
    // Static factory method for easy creation
    public static SystemLogResponse create(List<SystemLogEntry> logs, int totalCount) {
        return new SystemLogResponse(logs, totalCount);
    }
}
