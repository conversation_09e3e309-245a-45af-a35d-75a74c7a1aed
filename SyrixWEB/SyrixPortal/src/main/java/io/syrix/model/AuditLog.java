package io.syrix.model;

public class AuditLog {
    private String id;
    private String app;
    private User user;
    private Status status;
    private Date date;
    private String description;
    private Category category;

    // Enum for status values
    public enum Status {
        Success, Failed, Partial
    }
    
    // Enum for category values
    public enum Category {
        AutoFixed, ManuallyFixed, Ignored
    }

    // Nested class for user info
    public static class User {
        private String name;
        private String email;

        public User() {
        }

        public User(String name, String email) {
            this.name = name;
            this.email = email;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    // Nested class for date info
    public static class Date {
        private String fullDate;
        private String time;

        public Date() {
        }

        public Date(String fullDate, String time) {
            this.fullDate = fullDate;
            this.time = time;
        }

        public String getFullDate() {
            return fullDate;
        }

        public void setFullDate(String fullDate) {
            this.fullDate = fullDate;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }
    }

    // Constructors
    public AuditLog() {
    }

    public AuditLog(String id, String app, User user, Status status, Date date, String description, Category category) {
        this.id = id;
        this.app = app;
        this.user = user;
        this.status = status;
        this.date = date;
        this.description = description;
        this.category = category;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    
    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }
}
