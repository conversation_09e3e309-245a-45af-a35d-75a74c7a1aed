package io.syrix.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Standardized paginated response for API endpoints that return lists.
 * Provides pagination metadata along with the data.
 * 
 * @param <T> The type of items in the list
 */
public record PagedResponse<T>(
        @JsonProperty("items")
        List<T> items,
        
        @JsonProperty("pagination")
        Pagination pagination
) {
    
    /**
     * Constructor that calculates pagination metadata
     * @param items The list of items for this page
     * @param currentPage Current page number (0-based)
     * @param pageSize Number of items per page
     * @param totalElements Total number of elements across all pages
     */
    public PagedResponse(List<T> items, int currentPage, int pageSize, long totalElements) {
        this(items, new Pagination(currentPage, pageSize, totalElements, items.size()));
    }
    
    /**
     * Pagination metadata
     */
    public record Pagination(
            @JsonProperty("currentPage")
            int currentPage,
            
            @JsonProperty("pageSize")
            int pageSize,
            
            @JsonProperty("totalElements")
            long totalElements,
            
            @JsonProperty("currentPageSize")
            int currentPageSize,
            
            @JsonProperty("totalPages")
            int totalPages,
            
            @JsonProperty("hasNext")
            boolean hasNext,
            
            @JsonProperty("hasPrevious")
            boolean hasPrevious,
            
            @JsonProperty("isFirst")
            boolean isFirst,
            
            @JsonProperty("isLast")
            boolean isLast
    ) {
        
        /**
         * Constructor that calculates all pagination flags
         */
        public Pagination(int currentPage, int pageSize, long totalElements, int currentPageSize) {
            this(
                    currentPage,
                    pageSize,
                    totalElements,
                    currentPageSize,
                    calculateTotalPages(totalElements, pageSize),
                    calculateHasNext(currentPage, totalElements, pageSize),
                    currentPage > 0,
                    currentPage == 0,
                    calculateIsLast(currentPage, totalElements, pageSize)
            );
        }
        
        private static int calculateTotalPages(long totalElements, int pageSize) {
            return pageSize > 0 ? (int) Math.ceil((double) totalElements / pageSize) : 0;
        }
        
        private static boolean calculateHasNext(int currentPage, long totalElements, int pageSize) {
            int totalPages = calculateTotalPages(totalElements, pageSize);
            return currentPage < totalPages - 1;
        }
        
        private static boolean calculateIsLast(int currentPage, long totalElements, int pageSize) {
            int totalPages = calculateTotalPages(totalElements, pageSize);
            return currentPage >= totalPages - 1;
        }
    }
}
