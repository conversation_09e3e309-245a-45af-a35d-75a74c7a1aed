package io.syrix.model.connect;

/**
 * Request object for initializing OAuth2 flow.
 */
public class OAuth2InitRequest {
    private String service;
    private String tenantId;
    private String appId;
    private String companyId;
    
    // Constructors
    public OAuth2InitRequest() {}
    
    public OAuth2InitRequest(String service, String tenantId, String appId, String companyId) {
        this.service = service;
        this.tenantId = tenantId;
        this.appId = appId;
        this.companyId = companyId;
    }
    
    // Getters and setters
    public String getService() {
        return service;
    }
    
    public void setService(String service) {
        this.service = service;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getCompanyId() {
        return companyId;
    }
    
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
}
