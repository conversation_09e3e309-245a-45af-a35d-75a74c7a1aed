package io.syrix.model.connect;

/**
 * Response object for OAuth2 flow initialization.
 */
public class OAuth2InitResponse {
    private String authUrl;
    private String state;
    
    // Constructors
    public OAuth2InitResponse() {}
    
    public OAuth2InitResponse(String authUrl, String state) {
        this.authUrl = authUrl;
        this.state = state;
    }
    
    // Getters and setters
    public String getAuthUrl() {
        return authUrl;
    }
    
    public void setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
    }
    
    public String getState() {
        return state;
    }
    
    public void setState(String state) {
        this.state = state;
    }
}
