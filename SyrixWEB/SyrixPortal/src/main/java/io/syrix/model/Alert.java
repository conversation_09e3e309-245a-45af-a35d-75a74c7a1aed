package io.syrix.model;

import java.util.List;

import io.syrix.utils.baseline.MitreAttackTtp;

public record Alert(
    String id,
    String title,
    String description,
    AlertSeverity severity,
    AlertStatus status,
    AlertCategory category,
    String timestamp,
    String source,
    String affectedResource,
    String affectedResourceType,
    String assignedTo,
    List<String> tags,
    // Enhanced baseline policy information
    String policyId,
    String policyTitle,
    String implementation,
    String criticality,
    List<MitreAttackTtp> mitreAttackTtps,
    String reportDetails
) {
    public enum AlertSeverity {
        critical, high, medium, low
    }
    
    public enum AlertStatus {
        open, in_progress, resolved, dismissed
    }
    
    public enum AlertCategory {
        security, compliance, configuration, access, data
    }
}
