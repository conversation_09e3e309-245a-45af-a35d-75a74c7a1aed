package io.syrix.model;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record SystemLogEntry(
    String id,
    String activity,
    String status,
    User user,
    String ipAddress,
    DateInfo date,
    String description
) {
    // Static factory method for easy creation
    public static SystemLogEntry create(
            String id,
            String activity,
            String status,
            User user,
            String ipAddress,
            DateInfo date,
            String description) {
        return new SystemLogEntry(id, activity, status, user, ipAddress, date, description);
    }
    
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record User(String name, String email) {
        // Static factory method for easy creation
        public static User create(String name, String email) {
            return new User(name, email);
        }
    }
    
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record DateInfo(String fullDate, String time) {
        // Static factory method for easy creation
        public static DateInfo create(String fullDate, String time) {
            return new DateInfo(fullDate, time);
        }
    }
}
