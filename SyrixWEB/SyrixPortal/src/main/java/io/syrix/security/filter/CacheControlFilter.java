package io.syrix.security.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Filter to set appropriate cache control headers for different types of resources
 */
@Component
@Order(1)
public class CacheControlFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String uri = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        
        // Remove context path from URI for proper matching
        if (contextPath != null && !contextPath.isEmpty() && uri.startsWith(contextPath)) {
            uri = uri.substring(contextPath.length());
        }
        
        // Set cache control headers based on resource type
        setCacheHeaders(uri, httpResponse);
        
        chain.doFilter(request, response);
    }
    
    private void setCacheHeaders(String uri, HttpServletResponse response) {
        // Skip API endpoints - they handle their own caching
        if (isApiPath(uri)) {
            return;
        }
        
        // HTML files - Never cache to ensure latest version with correct asset references
        if (uri.endsWith(".html") || uri.equals("/") || uri.equals("/index.html")) {
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            return;
        }
        
        // Static assets with content hash (CSS, JS) - Long-term cache
        if (uri.startsWith("/static/") && (uri.contains(".css") || uri.contains(".js"))) {
            response.setHeader("Cache-Control", "public, max-age=31536000, immutable");
            return;
        }
        
        // Images, fonts, and other media - Medium-term cache
        if (uri.matches(".*\\.(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$")) {
            response.setHeader("Cache-Control", "public, max-age=86400"); // 1 day
            return;
        }
        
        // Manifest and JSON files - Short-term cache
        if (uri.matches(".*\\.(json|txt)$")) {
            response.setHeader("Cache-Control", "public, max-age=3600"); // 1 hour
            return;
        }
        
        // Default for other resources - No cache
        response.setHeader("Cache-Control", "no-cache, must-revalidate");
    }
    
    private boolean isApiPath(String uri) {
        if (uri == null) {
            return false;
        }
        
        return uri.startsWith("/api/") ||
               uri.startsWith("/public-api/") ||
               uri.startsWith("/actuator/") ||
               uri.startsWith("/auth/") ||
               uri.startsWith("/test/") ||
               uri.startsWith("/admin/");
    }
}
