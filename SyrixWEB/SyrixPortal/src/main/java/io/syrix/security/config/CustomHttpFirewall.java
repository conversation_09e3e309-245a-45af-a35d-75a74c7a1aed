package io.syrix.security.config;

import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.security.web.firewall.FirewalledRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Custom HTTP firewall that logs security violations without exposing full stacktraces
 * and blocks dangerous HTTP methods
 */
public class CustomHttpFirewall extends StrictHttpFirewall {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomHttpFirewall.class);

    public CustomHttpFirewall() {
        super();
        // Block TRACE method at the firewall level
        setUnsafeAllowAnyHttpMethod(false);
    }

    @Override
    public FirewalledRequest getFirewalledRequest(HttpServletRequest request) throws RequestRejectedException {
        // Block TRACE method explicitly
        if ("TRACE".equalsIgnoreCase(request.getMethod())) {
            logger.warn("TRACE method blocked for security: {} {}", request.getMethod(), request.getRequestURI());
            throw new RequestRejectedException("TRACE method is not allowed");
        }
        
        try {
            return super.getFirewalledRequest(request);
        } catch (RequestRejectedException ex) {
            // Log only essential information without stacktrace
            logger.warn("Request rejected: {} {} - Reason: {}", 
                request.getMethod(), 
                request.getRequestURI(), 
                ex.getMessage());
            throw ex;
        }
    }
}