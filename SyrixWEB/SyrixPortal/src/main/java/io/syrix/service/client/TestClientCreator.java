package io.syrix.service.client;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.Customer;
import io.syrix.model.connect.ConnectionInfo;
import io.syrix.model.connect.Token;
import io.syrix.model.connect.TokenType;
import io.syrix.service.connect.ConnectionInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Utility class for creating a test company.
 * This can be run on application startup in dev/test environments.
 * Refactored to work with MongoDB DAO implementation.
 */
@Component
public class TestClientCreator {
    private static final Logger logger = LoggerFactory.getLogger(TestClientCreator.class);

    private final ClientService clientService;
    private final DaoFactory daoFactory;
    private final ConnectionInfoService connectionInfoService;
    
    @Value("${syrix.config.path}")
    private String configPath;

    @Autowired
    public TestClientCreator(ClientService clientService, DaoFactory daoFactory, ConnectionInfoService connectionInfoService) {
        this.clientService = clientService;
        this.daoFactory = daoFactory;
        this.connectionInfoService = connectionInfoService;
    }

    /**
     * Initialize the MongoDB collection for companies if it doesn't exist.
     * MongoDB creates collections automatically when documents are inserted,
     * so we don't need to explicitly create the collection.
     */
    public void initializeMongoCollection() {
        logger.info("Checking MongoDB collection for companies");
        
        try {
            // Check if the companies collection exists by counting documents
            long count = daoFactory.getCustomerDao().count();
            logger.info("Companies collection exists with {} documents", count);
        } catch (DataAccessException e) {
            logger.warn("Error checking companies collection: {}", e.getMessage());
            // The collection will be created automatically when we insert the first document
            logger.info("Companies collection will be created automatically");
        }
    }

    /**
     * Create a test company for Syrix Development.
     *
     * @return The created or existing test company
     */
    public Customer createTestClient() {
        logger.info("Creating test company for Syrix Development");
        
        // Ensure the collection exists
        initializeMongoCollection();
        
        // Check if the test company already exists
        String tenantId = "syrixdev.onmicrosoft.com";
        return clientService.getClientByTenantId(tenantId).orElseGet(() -> {
            logger.info("Test company not found, creating new one");
            
            Customer testCustomer = new Customer();
//            testCompany.setId(UUID.randomUUID());
            testCustomer.setName("Syrix Development");
            testCustomer.setDisplayName("Syrix Dev Environment");
            testCustomer.setMicrosoftTenantId(tenantId);
            testCustomer.setContactEmail("<EMAIL>");
            testCustomer.setStatus("active");
            testCustomer.setCreatedAt(LocalDateTime.now());
            testCustomer.setUpdatedAt(LocalDateTime.now());
            
            return clientService.createClient(testCustomer);
        });
    }

    /**
     * Bean that creates the test company on application startup in dev/test environments.
     * Disabled by setting a special profile.
     */
    @Configuration
    @Profile({"dev-with-test-data", "test-with-test-data"}) // Changed profiles so it won't run by default
    public static class TestDataInitializer {
        private final TestClientCreator testClientCreator;
        
        @Autowired
        public TestDataInitializer(TestClientCreator testClientCreator) {
            this.testClientCreator = testClientCreator;
        }
        
        @Bean
        public CommandLineRunner initTestData() {
            return args -> {
                try {
                    Customer testCustomer = testClientCreator.createTestClient();
                    logger.info("Test company initialized with ID: {}", testCustomer.getId());
                } catch (Exception e) {
                    logger.error("Failed to initialize test company: {}", e.getMessage(), e);
                }
            };
        }
    }

    /**
     * Create the test company programmatically.
     * This method can be called from controllers or services.
     *
     * @return The created test company
     */
    public Customer createSyrixDevClient() {
        Customer customer = new Customer();
        customer.setName("Syrix Development");
        customer.setDisplayName("Syrix Dev Environment");
        customer.setMicrosoftTenantId("syrixdev.onmicrosoft.com");
        customer.setContactEmail("<EMAIL>");
        customer.setStatus("active");
        customer.setCreatedAt(LocalDateTime.now());
        customer.setUpdatedAt(LocalDateTime.now());
        
        return clientService.createOrUpdateClient(customer);
    }

    /**
     * Creates and saves ConnectionInfo to database using ConnectionInfoService.
     * Reads token data from config.yml file similar to TestClientInitializer.
     * 
     * @param customer The client object for which to create connection info
     * @return The created ConnectionInfo object
     */
    public ConnectionInfo createSyrixDevConnectionInfo(Customer customer) {
        logger.info("Creating ConnectionInfo for client: {}", customer.getName());
        
        try {
            // Read token data from config file
            Map<String, Object> configData = readConfigFile();

            // Extract credentials from config
            @SuppressWarnings("unchecked")
            Map<String, Object> credentials = (Map<String, Object>) configData.get("credentials");
            
            if (credentials == null) {
                logger.warn("No credentials section found in config file");
                throw new RuntimeException("No credentials section found in config file");
            }
            
            // Create ConnectionInfo with token data from config
            ConnectionInfo connectionInfo = new ConnectionInfo(
                    "OFFICE365", // Service type
                true, // Active status
					"Microsoft 365 - " + customer.getDisplayName(),
                LocalDateTime.now(),
                customer.getMicrosoftTenantId(),
                customer.getId(),
                extractDomainFromTenantId(customer.getMicrosoftTenantId())
            );
            
            // Create and set token if refresh token is available
            String refreshToken = (String) credentials.get("refreshToken");
            if (refreshToken != null && !refreshToken.isEmpty()) {
                Token token = new Token();
                token.setTokenType(TokenType.REFRESH_TOKEN);
                token.setToken(refreshToken);
                token.setCreatedAt(Instant.now());
                
                connectionInfo.setToken(token);
                logger.info("Added Microsoft refresh token to ConnectionInfo (length: {})", refreshToken.length());
            } else {
                logger.warn("No refresh token found in config file");
            }
            
            // Save using ConnectionInfoService
            ConnectionInfo savedConnectionInfo = connectionInfoService.createOrUpdateConnectionInfo(connectionInfo);
            logger.info("Successfully created and saved ConnectionInfo with ID: {}", savedConnectionInfo.getId());
            
            return savedConnectionInfo;
        } catch (Exception e) {
            logger.error("Error creating ConnectionInfo for client {}: {}", customer.getName(), e.getMessage(), e);
            throw new RuntimeException("Failed to create ConnectionInfo", e);
        }
    }
    
    /**
     * Reads YAML configuration file and returns parsed data.
     * 
     * @return Map containing configuration data or null if error
     */
    private Map<String, Object> readConfigFile() {
        try {
            logger.info("Reading configuration from file: {}", configPath);
            
            Yaml yaml = new Yaml();
            try (InputStream inputStream = new FileInputStream(configPath)) {
                Map<String, Object> data = yaml.load(inputStream);

                logger.info("Successfully read configuration file");
                return data;
            }
            
        } catch (FileNotFoundException e) {
            logger.error("Config file not found: {}", configPath);
            throw new RuntimeException("Config file not found:" + configPath);
        } catch (Exception e) {
            logger.error("Error reading config file {}: {}", configPath, e.getMessage(), e);
            throw new RuntimeException("Config file not found:" + configPath, e);
        }

    }
    
    /**
     * Extracts domain name from Microsoft tenant ID.
     * 
     * @param tenantId The tenant ID (e.g., "syrixdev.onmicrosoft.com")
     * @return Domain name or the original tenant ID if extraction fails
     */
    private String extractDomainFromTenantId(String tenantId) {
        if (tenantId == null) {
            return null;
        }
        
        // If tenant ID looks like a domain, return it as is
        if (tenantId.contains(".")) {
            return tenantId;
        }
        
        // If it's just a GUID, we can't extract a meaningful domain
        return tenantId;
    }


}
