package io.syrix.service;

import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.utils.baseline.PolicyCriticality;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * Spring-managed service wrapper for the BaselinePolicyService singleton.
 * Provides Spring integration and automatic initialization at application startup.
 * Delegates all operations to the singleton BaselinePolicyService from utils package.
 */
@Service
public class BaselinePolicyService {

    private static final Logger logger = LoggerFactory.getLogger(BaselinePolicyService.class);

    private io.syrix.utils.service.BaselinePolicyService baselineService;

    public BaselinePolicyService() {
        // Get the singleton instance from utils package
        this.baselineService = io.syrix.utils.service.BaselinePolicyService.getInstance();
    }

    /**
     * Initialize the service by loading and parsing all baseline policy files.
     * Called automatically after Spring dependency injection.
     */
    @PostConstruct
    public void initialize() {
        logger.info("Initializing Spring BaselinePolicyService wrapper...");

        try {
            // Initialize the singleton service
            baselineService.initialize();

            logger.info("Spring BaselinePolicyService wrapper initialized successfully. " +
                       "Singleton service loaded {} policies from {} services",
                       baselineService.getAllPolicies().size(),
                       baselineService.getAvailableServices().size());

        } catch (Exception e) {
            logger.error("Failed to initialize BaselinePolicyService: {}", e.getMessage(), e);
            throw new RuntimeException("BaselinePolicyService initialization failed", e);
        }
    }

    /**
     * Get a policy by its ID (e.g., "MS.EXO.8.2v2").
     *
     * @param policyId The policy ID to look up
     * @return The PolicyBaseline if found
     * @throws IllegalArgumentException if policy not found
     */
    public PolicyBaseline findPolicyByNumber(String policyId) {
        if (policyId == null || policyId.trim().isEmpty()) {
            throw new IllegalArgumentException("Policy ID cannot be null or empty");
        }

        PolicyBaseline policy = baselineService.getPolicyById(policyId.trim());
        if (policy == null) {
            throw new IllegalArgumentException("Policy not found: " + policyId);
        }

        return policy;
    }

    /**
     * Get all loaded policies as a map.
     *
     * @return Map of policyId -> PolicyBaseline
     */
    public Map<String, PolicyBaseline> loadAllPolicies() {
        return baselineService.getAllPolicies();
    }

    /**
     * Get policies filtered by service type (e.g., "EXO", "AAD", "TEAMS").
     *
     * @param serviceType The service type identifier
     * @return List of policies for that service type
     */
    public List<PolicyBaseline> getPoliciesByServiceType(String serviceType) {
        return baselineService.getPoliciesByService(serviceType);
    }

    /**
     * Get policies filtered by criticality level.
     *
     * @param criticality The criticality level to filter by
     * @return List of policies with matching criticality
     */
    public List<PolicyBaseline> getPoliciesByCriticality(PolicyCriticality criticality) {
        return baselineService.getPoliciesByCriticality(criticality);
    }

    /**
     * Check if a policy exists.
     *
     * @param policyId The policy ID to check
     * @return true if policy exists, false otherwise
     */
    public boolean policyExists(String policyId) {
        if (policyId == null) {
            return false;
        }

        return baselineService.getPolicyById(policyId.trim()) != null;
    }

    /**
     * Get basic statistics about loaded policies.
     *
     * @return Map with statistics (total, by_service, by_criticality)
     */
    public Map<String, Object> getPolicyStatistics() {
        Map<String, Object> stats = baselineService.getStatistics();

        // Add Spring-specific information
        Map<String, Object> springStats = new HashMap<>(stats);
        springStats.put("initialized", baselineService.isInitialized());
        springStats.put("service_types", baselineService.getAvailableServices());

        return springStats;
    }

    /**
     * Get the initialization status of the service.
     *
     * @return true if service is properly initialized, false otherwise
     */
    public boolean isInitialized() {
        return baselineService.isInitialized();
    }

    /**
     * Get all available service types.
     *
     * @return Set of service type names
     */
    public Set<String> getAvailableServices() {
        return baselineService.getAvailableServices();
    }

    /**
     * Get the source file for a policy.
     *
     * @param policyId The policy ID
     * @return The source file name or null if not found
     */
    public String getPolicySourceFile(String policyId) {
        return baselineService.getPolicySourceFile(policyId);
    }

    /**
     * Force reload of all baseline policies.
     *
     * @throws IOException if baseline files cannot be read
     */
    public void reload() throws IOException {
        baselineService.reload();
        logger.info("BaselinePolicyService reloaded successfully");
    }
}
