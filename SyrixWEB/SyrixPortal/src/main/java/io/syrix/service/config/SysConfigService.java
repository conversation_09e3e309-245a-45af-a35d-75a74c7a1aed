package io.syrix.service.config;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.SysConfigDao;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service class for managing SysConfig entities.
 * Provides business logic for system configuration operations including
 * Microsoft 365 security policy management.
 */
@Service
public class SysConfigService {

    private static final Logger logger = LoggerFactory.getLogger(SysConfigService.class);

    private final SysConfigDao sysConfigDao;

    @Autowired
    public SysConfigService(DaoFactory daoFactory) {
        this.sysConfigDao = daoFactory.getSysConfigDao();
    }

    /**
     * Save a system configuration to database.
     * 
     * @param sysConfig The configuration to save
     * @return The saved configuration with updated timestamps
     * @throws DataAccessException If database operation fails
     */
    public SysConfig saveConfiguration(SysConfig sysConfig) throws DataAccessException {
        logger.info("Saving system configuration: {}", sysConfig.getId());
        return sysConfigDao.save(sysConfig);
    }

    /**
     * Find a system configuration by ID.
     * 
     * @param id The configuration ID
     * @return Optional containing the configuration if found
     * @throws DataAccessException If database operation fails
     */
    public Optional<SysConfig> findById(UUID id) throws DataAccessException {
        logger.debug("Finding system configuration by ID: {}", id);
        return sysConfigDao.findById(id);
    }

    /**
     * Find the most recent system configuration.
     * 
     * @return Optional containing the latest configuration if found
     * @throws DataAccessException If database operation fails
     */
    public Optional<SysConfig> findLatest() throws DataAccessException {
        logger.debug("Finding latest system configuration");
        return sysConfigDao.findLatest();
    }

    /**
     * Get all system configurations.
     * 
     * @return List of all configurations ordered by creation date
     * @throws DataAccessException If database operation fails
     */
    public List<SysConfig> getAllConfigurations() throws DataAccessException {
        logger.debug("Retrieving all system configurations");
        return sysConfigDao.findAll();
    }

    /**
     * Find configurations by Microsoft service section.
     * 
     * @param sectionName The service section name (e.g., "aad", "defender", "exo")
     * @return List of configurations containing policies for the specified section
     * @throws DataAccessException If database operation fails
     */
    public List<SysConfig> findBySection(String sectionName) throws DataAccessException {
        logger.debug("Finding configurations by section: {}", sectionName);
        return sysConfigDao.findBySectionName(sectionName);
    }

    /**
     * Delete a configuration by ID.
     * 
     * @param id The configuration ID to delete
     * @throws DataAccessException If database operation fails
     */
    public void deleteConfiguration(UUID id) throws DataAccessException {
        logger.info("Deleting system configuration: {}", id);
        sysConfigDao.deleteById(id);
    }

    /**
     * Get the total number of unique policy IDs available across all configurations.
     * This method aggregates all policy IDs from all Microsoft service sections
     * in all configurations and returns the count of unique identifiers.
     * 
     * @return The total count of unique policy IDs in the system
     * @throws DataAccessException If database operation fails
     */
    public int getAvailablePolicyCount() {
        logger.debug("Retrieving count of available policy IDs");
        int count = sysConfigDao.countAvailablePolicyIds();
        logger.info("Found {} unique policy IDs across all configurations", count);
        return count;
    }

}
