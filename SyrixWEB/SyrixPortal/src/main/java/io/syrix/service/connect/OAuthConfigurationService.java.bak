package io.syrix.service.connect;

import io.syrix.exception.OAuth2FlowException;
import io.syrix.dao.OAuthConfigurationDao;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.OAuthConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service to retrieve OAuth2 configurations from the database.
 * This provides the single source of truth for OAuth2 credentials.
 */
@Service
public class OAuthConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(OAuthConfigurationService.class);
    
    private final OAuthConfigurationDao oauthConfigurationDao;
    
    // In-memory cache: service_type -> config object
    private final ConcurrentHashMap<ServiceType, OAuthConfig> configCache = new ConcurrentHashMap<>();

    public OAuthConfigurationService(OAuthConfigurationDao oauthConfigurationDao) {
        logger.info("Initializing OAuth2ConfigurationService with DAO");
        this.oauthConfigurationDao = oauthConfigurationDao;
    }

    /**
     * Load all configs from DB into cache (called on first access)
     */
    private synchronized void loadAllConfigsIfNeeded() {
        if (!configCache.isEmpty()) return;
        
        logger.info("Loading all OAuth2 configurations from database into memory");
        
        // Get all configurations from DAO
        List<OAuthConfig> allConfigs = oauthConfigurationDao.getAllConfigurations();
        
        // Handle case where no configurations are returned
        if (allConfigs.isEmpty()) {
            logger.warn("No OAuth2 configurations found in database. Creating a default fallback configuration.");
            throw new OAuth2FlowException("Failed to get OAuth2 configuration from database");
        }
        
        // Process each configuration
        allConfigs.forEach(config -> {
            ServiceType serviceType = config.getServiceType();
            logger.info("Loaded OAuth2 configuration for: {}, clientId: {}", serviceType, config.getClientId());
            configCache.put(serviceType, config);
        });

        logger.info("Successfully loaded {} OAuth2 configuration(s) into memory", configCache.size());
    }

    /**
     * Get OAuth2 configuration for a specific service.
     * 
     * @param service The OAuth2 service (e.g., "office365", "google")
     * @return OAuthConfig object for the requested service
     * @throws IllegalStateException if configuration cannot be found
     */
    public OAuthConfig getOAuthConfiguration(ServiceType service) {
        // First try to get from cache
        if (configCache.isEmpty()) {
            loadAllConfigsIfNeeded();
        }
        
        // Try to get configuration for the requested service from cache
        OAuthConfig cachedConfig = configCache.get(service);
        if (cachedConfig != null) {
            return cachedConfig;
        }
        
        // If not in cache, try to get directly from DAO
        Optional<OAuthConfig> dbConfig = oauthConfigurationDao.getConfigurationByService(service);
        if (dbConfig.isPresent()) {
            // Add to cache for future use
            configCache.put(service, dbConfig.get());
            return dbConfig.get();
        }
        
        // No configuration found
        String errorMsg = "No OAuth2 configuration found for service: " + service;
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }

    
    /**
     * Check if a configuration exists for a specific service.
     * 
     * @param service The OAuth2 service to check
     * @return true if configuration exists, false otherwise
     */
    public boolean hasConfiguration(ServiceType service) {
        try {
            // First check cache
            if (!configCache.isEmpty() && configCache.containsKey(service)) {
                return true;
            }
            
            // Then check database via DAO
            return oauthConfigurationDao.hasConfiguration(service);
        } catch (Exception e) {
            logger.warn("Error checking for OAuth2 configuration: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Clear the configuration cache and force reload from database.
     * Useful for testing or after configuration updates.
     */
    public synchronized void clearConfigCache() {
        logger.info("Clearing OAuth2 configuration cache");
        configCache.clear();
    }
}
