package io.syrix.service;

import io.syrix.model.AuditLog;
import io.syrix.model.AuditLog.Status;
import io.syrix.model.AuditLog.Category;
import io.syrix.model.AuditLog.User;
import io.syrix.model.AuditLog.Date;
import io.syrix.model.AuditLogs;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class AuditLogService {
    
    private final Random random = new Random();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MMM d, yyyy");
    private final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm '(UTC)'");
    
    private final String[] appNames = {
        "EntraID", 
        "Exchange", 
        "Sharepoint / Onedrive", 
        "Teams", 
        "Office 365"
    };
    
    private final String[] userNames = {
        "<PERSON>", 
        "<PERSON>", 
        "<PERSON>", 
        "<PERSON>", 
        "<PERSON>"
    };
    
    private final String[] emailDomains = {
        "gmail.com", 
        "outlook.com", 
        "company.com", 
        "example.org", 
        "syrix.com"
    };
    
    private final String[] descriptionTemplates = {
        "Title of issue. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        "Security alert detected. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        "User access review completed. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        "Configuration change detected. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        "Permission update. Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    };

    /**
     * Generate a list of random audit log entries
     * @param count The number of audit log entries to generate
     * @return A list of audit log entries
     */
    public AuditLogs getAuditLogs(int count) {
        List<AuditLog> logs = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            logs.add(generateRandomAuditLog());
        }
        
        return new AuditLogs(logs);
    }
    
    /**
     * Generate a random audit log entry
     * @return A random audit log entry
     */
    private AuditLog generateRandomAuditLog() {
        // Generate a random date-time within the last 30 days
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime randomDateTime = now.minusDays(random.nextInt(30)).minusHours(random.nextInt(24));
        
        String fullDate = randomDateTime.format(dateFormatter);
        String time = randomDateTime.format(timeFormatter);
        
        String id = UUID.randomUUID().toString();
        String app = appNames[random.nextInt(appNames.length)];
        
        String userName = userNames[random.nextInt(userNames.length)];
        String domain = emailDomains[random.nextInt(emailDomains.length)];
        String email = userName.toLowerCase().replace(" ", ".") + "@" + domain;
        
        User user = new User(userName, email);
        
        Status status = Status.values()[random.nextInt(Status.values().length)];
        Date date = new Date(fullDate, time);
        
        String description = descriptionTemplates[random.nextInt(descriptionTemplates.length)];
        
        Category category = Category.values()[random.nextInt(Category.values().length)];
        
        return new AuditLog(id, app, user, status, date, description, category);
    }
}
