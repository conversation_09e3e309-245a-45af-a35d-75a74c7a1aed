package io.syrix.service.connect;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.aad.msal4j.AuthorizationCodeParameters;
import com.microsoft.aad.msal4j.AuthorizationRequestUrlParameters;
import com.microsoft.aad.msal4j.ClientCredentialFactory;
import com.microsoft.aad.msal4j.ConfidentialClientApplication;
import com.microsoft.aad.msal4j.IAccount;
import com.microsoft.aad.msal4j.IAuthenticationResult;
import com.microsoft.aad.msal4j.IClientCredential;
import com.microsoft.aad.msal4j.IConfidentialClientApplication;
import com.microsoft.aad.msal4j.ITenantProfile;
import com.microsoft.aad.msal4j.PublicClientApplication;
import com.microsoft.aad.msal4j.ResponseMode;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.MSALOAuthConfig;
import io.syrix.datamodel.oauth.OAuthConfig;
import io.syrix.exception.AuthorizationUrlGenerationException;
import io.syrix.exception.TokenExchangeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * Microsoft Authentication Library (MSAL) provider for Office 365 authentication.
 * This class handles OAuth 2.0 flows using MSAL4J and REQUIRES database configuration.
 */
@Component
public class MSALAuthProvider {
	private static final int LOG_MAX_LENGTH = 50;
	private static final Logger logger = LoggerFactory.getLogger(MSALAuthProvider.class);

	// Keep tenant and authority configuration from properties
	@Value("${app.oauth2.tenant:common}")
	private String tenant;

	@Value("${app.oauth2.authority:https://login.microsoftonline.com/}")
	private String authority;

	private final OAuth2ConfigurationRetrievalService configService;

	@Autowired
	public MSALAuthProvider(OAuth2ConfigurationRetrievalService configService) {
		logger.info("Initializing MSALAuthProvider with required OAuth2ConfigurationService");
		if (configService == null) {
			throw new IllegalArgumentException("OAuth2ConfigurationService cannot be null - database access required");
		}
		this.configService = configService;
	}

	/**
	 * Generate the authorization URL for Microsoft login.
	 *
	 * @param state The state parameter for CSRF protection
	 * @return The authorization URL
	 * @throws IllegalStateException if database configuration cannot be found
	 */
	public String generateAuthorizationUrl(String state) {
		logger.info("Generating MSAL authorization URL with state: {}", state);

		try {
			// Load required configuration from database
			MSALOAuthConfig config = getRequiredMSALConfig();

			// Get client credentials
			String clientId = config.getClientId();
			String redirectUri = config.getRedirectUri();
			String scopesStr = config.getScopes();

			logInfo(clientId, redirectUri);
			logger.info("Using specific tenant authority URL for requesting access: {}", tenant);

			// Create the public client application for auth URL generation
			String tenantAuthority = this.authority + tenant;

			// Make sure authority URL ends with a slash if needed
			if (!tenantAuthority.endsWith("/")) {
				tenantAuthority = tenantAuthority + "/";
			}

			logger.info("Adjusted authority URL to ensure proper format: {}", tenantAuthority);

			PublicClientApplication app = PublicClientApplication.builder(clientId)
					.authority(tenantAuthority)
					.build();

			// Set up the auth code parameters
			Set<String> scopes = new HashSet<>(Arrays.asList(scopesStr.split(" ")));

			// Ensure we include offline_access scope for refresh tokens
			scopes.add("offline_access");
			logger.info("Added offline_access scope to request refresh tokens");

			AuthorizationRequestUrlParameters parameters = AuthorizationRequestUrlParameters
					.builder(redirectUri, scopes)
					.state(state)
					.responseMode(ResponseMode.QUERY)
					.build();

			// Generate the authorization URL
			String authorizationUrl = app.getAuthorizationRequestUrl(parameters).toString();
			if (logger.isInfoEnabled()) {
				logger.info("Generated MSAL authorization URL (partial): {}...",
						authorizationUrl.substring(0, Math.min(LOG_MAX_LENGTH, authorizationUrl.length())));
			}

			return authorizationUrl;
		} catch (IllegalStateException e) {
			// Re-throw database configuration errors
			throw e;
		} catch (Exception e) {
			throw new AuthorizationUrlGenerationException("Failed to generate authorization URL: " + e.getMessage(), e);
		}
	}

	private static void logInfo(String clientId, String redirectUri) {
		logger.info("Using client ID from database: {}", clientId);
		logger.info("Using redirect URI from database: {}", redirectUri);
	}

	/**
	 * Exchange authorization code for tokens using MSAL.
	 *
	 * @param authCode The authorization code from the OAuth callback
	 * @return A Map containing the token response
	 * @throws IllegalStateException if database configuration cannot be found
	 */
	public Map<String, Object> exchangeAuthCodeForTokens(String authCode) {
		logger.info("Exchanging authorization code for tokens using MSAL");

		try {
			MSALOAuthConfig config = getRequiredMSALConfig();
			String clientId = config.getClientId();
			String clientSecret = config.getClientSecret();
			String redirectUri = config.getRedirectUri();

			logInfo(clientId, redirectUri);
			logger.info("Using client secret from database (length: {})", clientSecret.length());

			String tenantAuthority = this.authority + tenant;
			logger.info("Using authority URL for token exchange: {}", tenantAuthority);

			if (!tenantAuthority.endsWith("/")) {
				tenantAuthority = tenantAuthority + "/";
				logger.info("Adjusted authority URL to ensure proper format: {}", tenantAuthority);
			}

			IConfidentialClientApplication app = getConfidentialClientApplication(clientId, clientSecret, tenantAuthority);

			Set<String> scopes = new HashSet<>();
			scopes.add("offline_access");

			AuthorizationCodeParameters parameters = AuthorizationCodeParameters.builder(
							authCode, URI.create(redirectUri))
					.scopes(scopes)
					.build();

			logger.info("Requesting tokens with offline_access scope for refresh token");

			CompletableFuture<IAuthenticationResult> future = app.acquireToken(parameters);
			IAuthenticationResult result = future.get();

			Map<String, Object> tokenResponse = buildTokenResponse(result);

			logger.info("Successfully acquired tokens using MSAL");

			return tokenResponse;
		} catch (IllegalStateException e) {
			throw e;
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new TokenExchangeException("Token exchange interrupted: " + e.getMessage(), e);
		} catch (MalformedURLException | ExecutionException e) {
			throw new TokenExchangeException("Failed to exchange code for tokens: " + e.getMessage(), e);
		}
	}

	private Map<String, Object> buildTokenResponse(IAuthenticationResult result) {
		Map<String, Object> tokenResponse = new HashMap<>();
		tokenResponse.put("access_token", result.accessToken());
		tokenResponse.put("id_token", result.idToken());

		addRefreshToken(result, tokenResponse);
		addTenantAndAccountInfo(result, tokenResponse);
		addExpiresIn(result, tokenResponse);

		tokenResponse.put("token_type", "Bearer");
		return tokenResponse;
	}

	@SuppressWarnings("java:S3011")
	private void addRefreshToken(IAuthenticationResult result, Map<String, Object> tokenResponse) {
		try {
			java.lang.reflect.Field refreshTokenField = result.getClass().getDeclaredField("refreshToken");
			refreshTokenField.setAccessible(true);
			String refreshToken = (String) refreshTokenField.get(result);

			if (refreshToken != null && !refreshToken.isEmpty()) {
				tokenResponse.put("refresh_token", refreshToken);
			}
		} catch (Exception e) {
			logger.warn("Could not extract refresh token: {}", e.getMessage());
		}
	}

	private void addTenantAndAccountInfo(IAuthenticationResult result, Map<String, Object> tokenResponse) {
		String tenantIdFromToken = extractTenantIdFromIdToken(result.idToken());
		IAccount account = result.account();
		if (account != null) {
			Map<String, ITenantProfile> profiles = account.getTenantProfiles();
			if (profiles != null && !profiles.isEmpty()) {
				tokenResponse.put("tid", profiles.keySet().iterator().next());
			} else if (tenantIdFromToken != null) {
				tokenResponse.put("tid", tenantIdFromToken);
			} else {
				tokenResponse.put("tid", tenant);
			}

			String homeAccountId = account.homeAccountId();
			if (homeAccountId != null) {
				tokenResponse.put("home_account_id", homeAccountId);
			}
		} else {
			tokenResponse.put("tid", tenant);
		}
	}

	private void addExpiresIn(IAuthenticationResult result, Map<String, Object> tokenResponse) {
		if (result.expiresOnDate() != null) {
			long expiresIn = (result.expiresOnDate().getTime() - System.currentTimeMillis()) / 1000;
			tokenResponse.put("expires_in", expiresIn);
		}
	}

	/**
	 * Get the required MSAL configuration - throws exception if not found or wrong type
	 *
	 * @return The MSALOAuth2Config object
	 * @throws IllegalStateException if configuration cannot be found or is wrong type
	 */
	private MSALOAuthConfig getRequiredMSALConfig() {
		logger.info("Loading required OAuth2 configuration from database for 'office365'");
		OAuthConfig config = configService.getOAuthConfiguration(ServiceType.OFFICE365);

		if (!(config instanceof MSALOAuthConfig)) {
			String errorMsg = "Retrieved configuration is not a MSAL configuration";
			logger.error(errorMsg);
			throw new IllegalStateException(errorMsg);
		}

		return (MSALOAuthConfig) config;
	}

	private IConfidentialClientApplication getConfidentialClientApplication(
			String clientId, String clientSecret, String authorityUrl) throws MalformedURLException {
		IClientCredential credential = ClientCredentialFactory.createFromSecret(clientSecret);
		return ConfidentialClientApplication.builder(clientId, credential)
				.authority(authorityUrl)
				.build();
	}

	private String extractTenantIdFromIdToken(String idToken) {
		if (idToken == null) return null;
		String[] parts = idToken.split("\\.");
		if (parts.length <= 1) return null;
		String payloadJson = new String(java.util.Base64.getUrlDecoder().decode(parts[1]));
		try {
			ObjectMapper mapper = new ObjectMapper();
			Map<String, Object> payload = mapper.readValue(payloadJson, new TypeReference<Map<String, Object>>() {
			});
			return (String) payload.get("tid");
		} catch (Exception e) {
			logger.warn("Failed to parse id_token for tenant id: {}", e.getMessage());
			return null;
		}
	}
}
