package io.syrix.service;

import io.syrix.model.UserProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Service for handling user profile related functionality.
 * This service provides user profile data that will later be replaced with real user management.
 */
@Service
public class UserProfileService {
    private static final Logger logger = LoggerFactory.getLogger(UserProfileService.class);

    /**
     * Retrieves the current user's profile information.
     * This is currently mockup data but will be replaced with real user data in the future.
     *
     * @return UserProfile object containing the user's information
     */
    public UserProfile getCurrentUserProfile() {
        logger.info("Retrieving current user profile");
        
        // This mock data matches what was previously hardcoded in the frontend
        return new UserProfile(
            "<PERSON>",
            "https://randomuser.me/api/portraits/women/44.jpg",
            "Account Settings"
        );
    }
}
