package io.syrix.service;

import io.syrix.model.Alerts;
import io.syrix.model.DashboardResponse;
import io.syrix.model.FailedFindings;
import io.syrix.model.NextScanInfo;
import io.syrix.model.NotificationData;
import io.syrix.model.Risk;
import io.syrix.model.RiskOverTime;
import io.syrix.model.SecurityMetrics;
import io.syrix.model.StatusCardData;
import io.syrix.model.TestStatusItem;
import io.syrix.model.TopRiskUser;
import io.syrix.service.config.SysConfigService;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import io.syrix.service.test.TestStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class SecurityService {
    private static final Logger logger = LoggerFactory.getLogger(SecurityService.class);
    public static final String MICROSOFT_365 = "Microsoft 365";
    public static final String ONE_DRIVE = "OneDrive";

    private final TestStatusService testStatusService;
    private final ScanService scanService;
    private final SysConfigService sysConfigService;
    
    @Autowired
    public SecurityService(TestStatusService testStatusService, ScanService scanService, SysConfigService sysConfigService) {
        this.testStatusService = testStatusService;
        this.scanService = scanService;
        this.sysConfigService = sysConfigService;
    }

    public List<SecurityMetrics> getSecurityMetrics() {
        return Arrays.asList(
                new SecurityMetrics("Misconfiguration", "safe", 0, 34),
                new SecurityMetrics("Identity", "warning", 12, 34),
                new SecurityMetrics("OAUTH", "safe", 0, 126),
                new SecurityMetrics("Domain", "critical", 26, 1263),
                new SecurityMetrics("Shadow IT", "safe", 0, 12),
                new SecurityMetrics("Compliance", "critical", 5, 2654)
        );
    }

    public FailedFindings getFailedFindings() {
        Alerts alerts = new Alerts(457, 203, 1104);
        return new FailedFindings(2345, 1467, 1467, alerts);
    }

    public List<Risk> getRisks() {
        return Arrays.asList(
                new Risk("Microsoft Mail", MICROSOFT_365, 65, "1.23K"),
                new Risk("Shared Docs", "Salesforce", 43, "1.02K"),
                new Risk("Sharepoint Docs", "Sharepoint", 23, "905K"),
                new Risk("Microsoft Word", MICROSOFT_365, 18, "460K")
        );
    }

    public List<TopRiskUser> getTopRiskUsers() {
        return Arrays.asList(
                new TopRiskUser("<EMAIL>", "3.49K", "1.23K"),
                new TopRiskUser("<EMAIL>", "549", "53"),
                new TopRiskUser("<EMAIL>", "1012K", "Safe")
        );
    }

    public List<RiskOverTime> getRiskOverTime() {
        logger.info("Generating last 12 months risk over time data");
        
        List<RiskOverTime> result = new ArrayList<>();
        LocalDate today = LocalDate.now();
        
        // Generate data for the last 12 months
        for (int i = 11; i >= 0; i--) {
            LocalDate date = today.minusMonths(i);
            String monthName = date.format(DateTimeFormatter.ofPattern("MMM"));
            
            // Generate a risk value that varies over time with some randomness but also patterns
            double baseRisk = 100.0;
            // Add seasonal pattern (higher in Q1 and Q4)
            int month = date.getMonthValue();
            if (month <= 3 || month >= 10) {
                baseRisk += 5.0;
            }
            
            // Add some randomness
            double randomFactor = Math.random() * 5.0 - 2.5; // Between -2.5 and 2.5
            double risk = Math.max(0, baseRisk + randomFactor);
            
            // Round to one decimal place
            risk = Math.round(risk * 10) / 10.0;
            
            result.add(new RiskOverTime(monthName, risk));
        }
        
        return result;
    }
    
    public List<RiskOverTime> getRiskOverTimeWeekly() {
        logger.info("Generating last 7 days risk over time data ending at current date");
        
        List<RiskOverTime> result = new ArrayList<>();
        LocalDate today = LocalDate.now();
        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("EEE dd"); // Day name + day number
        
        // Generate data for the last 7 days, ending at the current date
        for (int i = 6; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String dayName = date.format(dayFormatter);
            
            // Generate a realistic risk pattern with weekday variation
            double baseRisk = 12.0;
            int dayOfWeek = date.getDayOfWeek().getValue(); // 1 (Monday) to 7 (Sunday)
            
            // Weekends have lower risk
            if (dayOfWeek >= 6) { // Saturday and Sunday
                baseRisk -= 4.0;
            }
            // Midweek has highest risk
            else if (dayOfWeek == 3 || dayOfWeek == 4) { // Wednesday and Thursday
                baseRisk += 3.0;
            }
            
            // Add some randomness
            double randomFactor = Math.random() * 3.0 - 1.5; // Between -1.5 and 1.5
            double risk = Math.max(0, baseRisk + randomFactor);
            
            // Round to one decimal place
            risk = Math.round(risk * 10) / 10.0;
            
            result.add(new RiskOverTime(dayName, risk));
        }
        
        return result;
    }
    
    public List<RiskOverTime> getRiskOverTimeMonthly() {
        logger.info("Generating last month's daily risk over time data ending at current date");
        
        List<RiskOverTime> result = new ArrayList<>();
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM-dd"); // Month-Day format (e.g., Apr-08)
        
        // Generate data for the last 30 days, ending at the current date
        for (int i = 29; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String dayLabel = date.format(formatter);
            
            // Create pattern with realistic variations
            double baseRisk;
            int dayOfMonth = date.getDayOfMonth();
            
            // Pattern: Higher at beginning of month, drops in middle, rises slightly at end
            if (dayOfMonth <= 10) {
                baseRisk = 14.0 - (dayOfMonth - 1) * 0.3;
            } else if (dayOfMonth >= 20) {
                baseRisk = 8.0 + (dayOfMonth - 20) * 0.2;
            } else {
                baseRisk = 9.0 - (dayOfMonth % 3) * 0.3;
            }
            
            // Add weekly patterns (weekends lower)
            int dayOfWeek = date.getDayOfWeek().getValue();
            if (dayOfWeek >= 6) { // Weekend
                baseRisk -= 2.0;
            }
            
            // Add some randomness
            double randomFactor = Math.random() * 2.0 - 1.0; // Between -1.0 and 1.0
            double risk = Math.max(0, baseRisk + randomFactor);
            
            // Round to one decimal place
            risk = Math.round(risk * 10) / 10.0;
            
            result.add(new RiskOverTime(dayLabel, risk));
        }
        
        return result;
    }

    public List<StatusCardData> getStatusCards() {
        logger.info("Generating status cards data from API");

        int configPolicyCount = sysConfigService.getAvailablePolicyCount();
        return Arrays.asList(
                new StatusCardData("Configuration", "safe", "100% safe", 0, configPolicyCount),
                new StatusCardData("Users", "warning", "Check your safety", 12, 34),
                new StatusCardData("Data", "danger", "Critical problem", 26, 1263),
                new StatusCardData("Applications", "safe", "100% safe", 0, 126)
        );
    }

    // Get test status items from the dedicated service
    public List<TestStatusItem> getTestStatusItems() {
        logger.info("Getting test status items from TestStatusService");
        return testStatusService.getTestStatusItems();
    }


    public List<NotificationData> getNotifications() {
        logger.info("SecurityService.getNotifications() - returning empty list (real notifications come from AlertsService)");
        // Real notifications now come from AlertsService via frontend conversion
        // This method is deprecated and should return empty to avoid conflicts
        return new ArrayList<>();
    }

    public DashboardResponse getDashboardData() {
        logger.info("Generating complete dashboard data");
        
        // Get all the required data components
        FailedFindings failedFindings = getFailedFindings();
        List<TopRiskUser> topRiskUsers = getTopRiskUsers();
        List<SecurityMetrics> securityMetrics = getSecurityMetrics();
        List<Risk> topRisks = getRisks();
        List<RiskOverTime> riskOverTime = getRiskOverTime();
        List<StatusCardData> statusCards = getStatusCards();
        List<TestStatusItem> testStatusItems = getTestStatusItems();
        List<NotificationData> notifications = getNotifications();
        NextScanInfo nextScanInfo = scanService.getNextScanInfo();
        
        // Log the number of items in each collection to help with debugging
        logger.info("Dashboard data summary: {} security metrics, {} top risks, {} risk over time points, "
                + "{} status cards, {} test status items, {} notifications, next scan info included",
                securityMetrics.size(), topRisks.size(), riskOverTime.size(),
                statusCards.size(), testStatusItems.size(), notifications.size());
        
        // Create and return the complete response
        return new DashboardResponse(
                failedFindings,
                topRiskUsers,
                securityMetrics,
                topRisks,
                riskOverTime,
                statusCards,
                testStatusItems,
                notifications,
                nextScanInfo
        );
    }

    public double calculateSecurityScore() {
        List<SecurityMetrics> metrics = getSecurityMetrics();
        return metrics.stream()
                .mapToDouble(metric ->
                        ((double) (metric.total() - metric.current()) / metric.total()) * 100)
                .average()
                .orElse(0.0);
    }
}