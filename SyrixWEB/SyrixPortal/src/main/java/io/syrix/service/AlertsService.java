package io.syrix.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import io.syrix.dao.CustomerReportDao;
import io.syrix.dao.DaoFactory;
import io.syrix.utils.baseline.AlertSeverity;
import io.syrix.utils.baseline.PolicyBaseline;
import io.syrix.datamodel.report.CustomerReport;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.datamodel.report.PolicyResult;
import io.syrix.model.Alert;
import io.syrix.model.Alert.AlertCategory;
import io.syrix.model.Alert.AlertStatus;

/**
 * Service for generating and managing security alerts based on policy compliance failures.
 * Matches PolicyResults from ClientReports with baseline policies to create actionable alerts.
 */
@Service
public class AlertsService {

    private static final Logger logger = LoggerFactory.getLogger(AlertsService.class);

    // Cached alerts to avoid re-processing on window switches
    private final Map<UUID, List<Alert>> alertsCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes cache TTL

    private final BaselinePolicyService baselinePolicyService;
    private final DaoFactory daoFactory;

    @Autowired
    public AlertsService(BaselinePolicyService baselinePolicyService, DaoFactory daoFactory) {
        this.baselinePolicyService = baselinePolicyService;
        this.daoFactory = daoFactory;
    }

    /**
     * Get all alerts, generating them from policy compliance data on first call.
     * Subsequent calls return cached results until TTL expires.
     *
     * @param clientId The client ID to get alerts for
     * @return List of all alerts from policy compliance failures
     */
    public List<Alert> getAllAlerts(UUID clientId) {
        if (clientId == null) {
            logger.warn("No client ID provided, using fallback demo client ID");
            clientId = UUID.fromString("00000000-0000-0000-0000-000000000001");
        }

        logger.info("Getting alerts for client: {}", clientId);
        return getAlertsForClient(clientId);
    }

    /**
     * Get all alerts using demo client ID (for backward compatibility).
     *
     * @return List of all alerts from policy compliance failures
     * @deprecated Use getAllAlerts(UUID clientId) instead
     */
    @Deprecated
    public List<Alert> getAllAlerts() {
        logger.warn("Using deprecated getAllAlerts() method without client ID");
        UUID demoClientId = UUID.fromString("00000000-0000-0000-0000-000000000001");
        return getAlertsForClient(demoClientId);
    }

    /**
     * Get filtered alerts based on status and category.
     *
     * @param clientId The client ID to get alerts for
     * @param status Optional status filter
     * @param category Optional category filter
     * @return Filtered list of alerts
     */
    public List<Alert> getFilteredAlerts(UUID clientId, String status, String category) {
        List<Alert> allAlerts = getAllAlerts(clientId);

        List<Alert> filteredAlerts = new ArrayList<>(allAlerts);

        if (status != null && !status.isEmpty()) {
            try {
                AlertStatus alertStatus = AlertStatus.valueOf(status);
                filteredAlerts = filteredAlerts.stream()
                    .filter(alert -> alert.status() == alertStatus)
                    .collect(Collectors.toList());
            } catch (IllegalArgumentException e) {
                throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST, "Invalid status value: " + status);
            }
        }

        if (category != null && !category.isEmpty()) {
            try {
                AlertCategory alertCategory = AlertCategory.valueOf(category);
                filteredAlerts = filteredAlerts.stream()
                    .filter(alert -> alert.category() == alertCategory)
                    .collect(Collectors.toList());
            } catch (IllegalArgumentException e) {
                throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST, "Invalid category value: " + category);
            }
        }

        logger.info("Filtered {} alerts to {} for client {} with status={}, category={}",
                   allAlerts.size(), filteredAlerts.size(), clientId, status, category);

        return filteredAlerts;
    }

    /**
     * Get filtered alerts based on status and category (backward compatibility).
     *
     * @param status Optional status filter
     * @param category Optional category filter
     * @return Filtered list of alerts
     * @deprecated Use getFilteredAlerts(UUID clientId, String status, String category) instead
     */
    @Deprecated
    public List<Alert> getFilteredAlerts(String status, String category) {
        logger.warn("Using deprecated getFilteredAlerts() method without client ID");
        UUID demoClientId = UUID.fromString("00000000-0000-0000-0000-000000000001");
        return getFilteredAlerts(demoClientId, status, category);
    }

    /**
     * Get an alert by its ID.
     * 
     * @param id The alert ID
     * @return The alert if found
     * @throws ResponseStatusException if alert not found
     */
    public Alert getAlertById(String id) {
        return getAllAlerts().stream()
                .filter(alert -> alert.id().equals(id))
                .findFirst()
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND, "Alert not found with id: " + id));
    }

    /**
     * Update the status of an alert.
     * 
     * @param id Alert ID
     * @param status New status
     * @return Updated alert
     */
    public Alert updateAlertStatus(String id, String status) {
        // For now, we'll handle this in memory
        // In production, you might want to persist status changes
        Alert existingAlert = getAlertById(id);
        
        try {
            AlertStatus newStatus = AlertStatus.valueOf(status);
            
            Alert updatedAlert = new Alert(
                existingAlert.id(),
                existingAlert.title(),
                existingAlert.description(),
                existingAlert.severity(),
                newStatus,
                existingAlert.category(),
                existingAlert.timestamp(),
                existingAlert.source(),
                existingAlert.affectedResource(),
                existingAlert.affectedResourceType(),
                existingAlert.assignedTo(),
                existingAlert.tags(),
                // Enhanced baseline policy information
                existingAlert.policyId(),
                existingAlert.policyTitle(),
                existingAlert.implementation(),
                existingAlert.criticality(),
                existingAlert.mitreAttackTtps(),
                existingAlert.reportDetails()
            );
            
            // Update in cache
            updateAlertInCache(updatedAlert);
            
            return updatedAlert;
        } catch (IllegalArgumentException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST, "Invalid status value: " + status);
        }
    }


    /**
     * Get alerts for a specific client, with caching.
     * 
     * @param clientId The client ID
     * @return List of alerts for the client
     */
    private List<Alert> getAlertsForClient(UUID clientId) {
        long currentTime = System.currentTimeMillis();
        
        // Check cache first
        if (alertsCache.containsKey(clientId) && 
            (currentTime - lastCacheUpdate) < CACHE_TTL_MS) {
            logger.debug("Returning cached alerts for client: {}", clientId);
            return new ArrayList<>(alertsCache.get(clientId));
        }
        
        logger.info("Generating fresh alerts for client: {}", clientId);
        
        try {
            // Generate fresh alerts from policy compliance data
            List<Alert> alerts = generateAlertsFromPolicyCompliance(clientId);
            
            // Update cache
            alertsCache.put(clientId, alerts);
            lastCacheUpdate = currentTime;
            
            logger.info("Generated {} alerts for client {}", alerts.size(), clientId);
            return new ArrayList<>(alerts);
            
        } catch (Exception e) {
            logger.error("Error generating alerts for client {}: {}", clientId, e.getMessage(), e);
            
            // Fallback to empty list rather than failing completely
            return Collections.emptyList();
        }
    }

    /**
     * Generate alerts from policy compliance failures.
     * 
     * @param clientId The client ID
     * @return List of generated alerts
     */
    private List<Alert> generateAlertsFromPolicyCompliance(UUID clientId) {
        List<Alert> alerts = new ArrayList<>();
        
        try {
            // Get latest client report
            CustomerReportDao clientReportDao = daoFactory.getCustomerReportDao();
            Optional<CustomerReport> reportOpt = clientReportDao.findLatestByCustomerId(clientId);
            
            if (reportOpt.isEmpty()) {
                logger.warn("No client report found for client: {}", clientId);
                return Collections.emptyList();
            }
            
            CustomerReport report = reportOpt.get();
            Map<ConfigurationServiceType, List<PolicyResult>> results = report.getResults();
            
            if (results == null || results.isEmpty()) {
                logger.warn("No policy results found in client report for: {}", clientId);
                return Collections.emptyList();
            }
            
            // Process each service type
            for (Map.Entry<ConfigurationServiceType, List<PolicyResult>> entry : results.entrySet()) {
                ConfigurationServiceType serviceType = entry.getKey();
                List<PolicyResult> policyResults = entry.getValue();
                
                if (policyResults == null) continue;
                
                // Generate alerts for failed policies
                for (PolicyResult policyResult : policyResults) {
                    if (!policyResult.isRequirementMet()) {
                        Alert alert = createAlertFromFailedPolicy(policyResult, serviceType, report);
                        if (alert != null) {
                            alerts.add(alert);
                        }
                    }
                }
            }
            
            logger.info("Generated {} compliance alerts from {} service types", 
                       alerts.size(), results.size());
            
        } catch (Exception e) {
            logger.error("Error generating alerts from policy compliance: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
        
        return alerts.isEmpty() ? Collections.emptyList() : alerts;
    }

    /**
     * Create an Alert from a failed PolicyResult using baseline policy data.
     * 
     * @param policyResult The failed policy result
     * @param serviceType The service type
     * @param report The client report (for timestamp)
     * @return Generated Alert or null if baseline not found
     */
    private Alert createAlertFromFailedPolicy(PolicyResult policyResult, 
                                              ConfigurationServiceType serviceType, 
                                              CustomerReport report) {
        try {
            String policyId = policyResult.getPolicyId();
            if (policyId == null || policyId.trim().isEmpty()) {
                logger.warn("PolicyResult has null or empty policyId");
                return null;
            }
            
            // Get baseline policy details
            PolicyBaseline baseline = baselinePolicyService.findPolicyByNumber(policyId);
            
            // Generate alert title
            String title = generateAlertTitle(baseline, serviceType);
            
            // Use baseline description if available and different from title
            String baselineDescription = baseline.getDescription();
            String description;
            
            if (baselineDescription != null && 
                !baselineDescription.trim().isEmpty() && 
                !baselineDescription.trim().equals(baseline.getTitle())) {
                description = baselineDescription;
            } else {
                // If description is missing, empty, or same as title, use a generic description
                description = String.format("Policy compliance failure for %s. Please review the security configuration to ensure it meets baseline requirements.", baseline.getTitle());
            }
            
            // Map criticality to severity
            AlertSeverity baselineSeverity = baseline.getAlertSeverity();
            Alert.AlertSeverity alertSeverity = mapToAlertSeverity(baselineSeverity);
            
            // Determine category based on policy content
            AlertCategory category = determineAlertCategory(baseline);
            
            // Generate tags
            List<String> tags = generateAlertTags(baseline, serviceType, alertSeverity);
            
            // Use report timestamp or current time
            String timestamp = (report != null && report.getCreatedAt() != null)
                ? report.getCreatedAt().toString()
                : Instant.now().toString();


            return new Alert(
                generateAlertId(policyId, serviceType),
                title,
                description,
                alertSeverity,
                AlertStatus.open, // New compliance failures start as open
                category,
                timestamp,
                serviceType.getFullName(),
                serviceType.getFullName(),
                serviceType.getResultName().toUpperCase(),
                null, // Not assigned initially
                tags,
                // Enhanced baseline policy information
                baseline.getPolicyId(),
                baseline.getTitle(),
                baseline.getImplementation(),
                baseline.getCriticality() != null ? baseline.getCriticality().getCode() : null,
                baseline.getMitreAttackTtps(),
                policyResult.getReportDetails()
            );
            
        } catch (Exception e) {
            logger.error("Error creating alert from policy {}: {}", 
                        policyResult.getPolicyId(), e.getMessage());
            return null;
        }
    }

    /**
     * Generate a meaningful alert title from baseline policy.
     */
    private String generateAlertTitle(PolicyBaseline baseline, ConfigurationServiceType serviceType) {
        String title = baseline.getTitle();
        if (title == null || title.trim().isEmpty()) {
            title = "Policy Compliance Failure";
        }
        
        // Return just the policy title without service name or policy ID
        // The frontend will format this as [Policy ID] - Title
        return title;
    }

    /**
     * Generate detailed alert description with baseline context.
     */
    private String generateAlertDescription(PolicyBaseline baseline, PolicyResult policyResult) {
        StringBuilder description = new StringBuilder();
        
        description.append("Policy compliance failure detected for: ")
                   .append(baseline.getPolicyId())
                   .append("\n\n");
        
        if (baseline.getDescription() != null && !baseline.getDescription().trim().isEmpty()) {
            description.append("Rationale: ")
                       .append(baseline.getDescription())
                       .append("\n\n");
        }
        
        if (policyResult.getReportDetails() != null && !policyResult.getReportDetails().trim().isEmpty()) {
            description.append("Details: ")
                       .append(policyResult.getReportDetails())
                       .append("\n\n");
        }
        
        if (baseline.getImplementation() != null && !baseline.getImplementation().trim().isEmpty()) {
            description.append("Implementation: ")
                       .append(baseline.getImplementation());
        }
        
        return description.toString().trim();
    }

    /**
     * Map baseline AlertSeverity to Alert.AlertSeverity.
     */
    private Alert.AlertSeverity mapToAlertSeverity(AlertSeverity baselineSeverity) {
        return switch (baselineSeverity) {
            case CRITICAL -> Alert.AlertSeverity.critical;
            case HIGH -> Alert.AlertSeverity.high;
            case MEDIUM -> Alert.AlertSeverity.medium;
            case LOW -> Alert.AlertSeverity.low;
        };
    }

    /**
     * Determine alert category based on baseline policy content.
     */
    private AlertCategory determineAlertCategory(PolicyBaseline baseline) {
        String title = baseline.getTitle() != null ? baseline.getTitle().toLowerCase() : "";
        String rationale = baseline.getDescription() != null ? baseline.getDescription().toLowerCase() : "";
        
        if (title.contains("access") || title.contains("permission") || title.contains("authentication") ||
            rationale.contains("access") || rationale.contains("permission")) {
            return AlertCategory.access;
        }
        
        if (title.contains("data") || title.contains("information") || title.contains("sharing") ||
            rationale.contains("data") || rationale.contains("sharing")) {
            return AlertCategory.data;
        }
        
        if (title.contains("configuration") || title.contains("setting") ||
            rationale.contains("configuration") || rationale.contains("setting")) {
            return AlertCategory.configuration;
        }
        
        if (title.contains("compliance") || title.contains("policy") ||
            rationale.contains("compliance") || rationale.contains("policy")) {
            return AlertCategory.compliance;
        }
        
        // Default to security for unclassified policies
        return AlertCategory.security;
    }

    /**
     * Generate relevant tags for the alert.
     */
    private List<String> generateAlertTags(PolicyBaseline baseline, 
                                          ConfigurationServiceType serviceType, 
                                          Alert.AlertSeverity severity) {
        List<String> tags = new ArrayList<>();
        
        tags.add("policy-compliance");
        tags.add(serviceType.getResultName());
        tags.add(severity.name());
        
        if (baseline.getCriticality() != null) {
            tags.add(baseline.getCriticality().getCode().toLowerCase());
        }
        
        if (baseline.getMitreAttackTtps() != null && !baseline.getMitreAttackTtps().isEmpty()) {
            tags.add("mitre-attack");
        }
        
        return tags;
    }

    /**
     * Generate a unique alert ID.
     */
    private String generateAlertId(String policyId, ConfigurationServiceType serviceType) {
        return String.format("policy-%s-%s-%d", 
                           serviceType.getResultName(), 
                           policyId.replace(".", "-"), 
                           System.currentTimeMillis() % 10000);
    }

    /**
     * Update an alert in the cache.
     */
    private void updateAlertInCache(Alert updatedAlert) {
        for (List<Alert> clientAlerts : alertsCache.values()) {
            for (int i = 0; i < clientAlerts.size(); i++) {
                if (clientAlerts.get(i).id().equals(updatedAlert.id())) {
                    clientAlerts.set(i, updatedAlert);
                    return;
                }
            }
        }
    }
}
