package io.syrix.service.client;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.Customer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service class for managing companies, updated to use the syrixdao component.
 */
@Service
public class ClientService {
    private static final Logger logger = LoggerFactory.getLogger(ClientService.class);

    private final DaoFactory daoFactory;

    @Autowired
    public ClientService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
    }

    /**
     * Create a new client.
     *
     * @param customer The client to create
     * @return The created client with ID
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional
    public Customer createClient(Customer customer) {
        logger.info("Creating new client: {}", customer.getName());
        try {
            return daoFactory.getCustomerDao().save(customer);
        } catch (DataAccessException e) {
            logger.error("Error creating client", e);
            throw new RuntimeException("Failed to create client", e);
        }
    }

    /**
     * Get a client by ID.
     *
     * @param id The client ID
     * @return An optional containing the client, or empty if not found
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getClientById(UUID id) {
        try {
            return daoFactory.getCustomerDao().findById(id);
        } catch (DataAccessException e) {
            logger.error("Error getting client by ID: {}", id, e);
            throw new RuntimeException("Failed to get client by ID", e);
        }
    }

    /**
     * Get a client by name.
     *
     * @param name The client name
     * @return An optional containing the client, or empty if not found
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getClientByName(String name) {
        try {
            return daoFactory.getCustomerDao().findByName(name);
        } catch (DataAccessException e) {
            logger.error("Error getting client by name: {}", name, e);
            throw new RuntimeException("Failed to get client by name", e);
        }
    }

    /**
     * Get a client by Microsoft tenant ID.
     *
     * @param tenantId The Microsoft tenant ID
     * @return An optional containing the client, or empty if not found
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public Optional<Customer> getClientByTenantId(String tenantId) {
        try {
            return daoFactory.getCustomerDao().findByMicrosoftTenantId(tenantId);
        } catch (DataAccessException e) {
            logger.error("Error getting client by tenant ID: {}", tenantId, e);
            throw new RuntimeException("Failed to get client by tenant ID", e);
        }
    }

    /**
     * Get all companies.
     *
     * @return A list of all companies
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public List<Customer> getAllClients() {
        try {
            return daoFactory.getCustomerDao().findAll();
        } catch (DataAccessException e) {
            logger.error("Error getting all companies", e);
            throw new RuntimeException("Failed to get all companies", e);
        }
    }

    /**
     * Get all active companies.
     *
     * @return A list of all active companies
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public List<Customer> getAllActiveClients() {
        try {
            return daoFactory.getCustomerDao().findByStatus("active");
        } catch (DataAccessException e) {
            logger.error("Error getting active companies", e);
            throw new RuntimeException("Failed to get active companies", e);
        }
    }

    /**
     * Update a client.
     *
     * @param customer The client to update
     * @return The updated client
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional
    public Customer updateClient(Customer customer) {
        logger.info("Updating client: {}", customer.getId());
        try {
            return daoFactory.getCustomerDao().save(customer);
        } catch (DataAccessException e) {
            logger.error("Error updating client: {}", customer.getId(), e);
            throw new RuntimeException("Failed to update client", e);
        }
    }

    /**
     * Delete a client by ID.
     *
     * @param id The client ID to delete
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional
    public void deleteClient(UUID id) {
        logger.info("Deleting client: {}", id);
        try {
            daoFactory.getCustomerDao().deleteById(id);
        } catch (DataAccessException e) {
            logger.error("Error deleting client: {}", id, e);
            throw new RuntimeException("Failed to delete client", e);
        }
    }

    /**
     * Check if a client exists by Microsoft tenant ID.
     *
     * @param tenantId The Microsoft tenant ID
     * @return true if exists, false otherwise
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional(readOnly = true)
    public boolean existsByTenantId(String tenantId) {
        try {
            return getClientByTenantId(tenantId).isPresent();
        } catch (DataAccessException e) {
            logger.error("Error checking if client exists by tenant ID: {}", tenantId, e);
            throw new RuntimeException("Failed to check if client exists by tenant ID", e);
        }
    }

    /**
     * Create or update a client with the given Microsoft tenant ID.
     * If a client with the tenant ID already exists, it will be updated.
     * Otherwise, a new client will be created.
     *
     * @param customer The client data to create or update
     * @return The created or updated client
     * @throws RuntimeException if a data access error occurs
     */
    @Transactional
    public Customer createOrUpdateClient(Customer customer) {
        try {
            Optional<Customer> existingClient = getClientByTenantId(customer.getMicrosoftTenantId());
            
            if (existingClient.isPresent()) {
                Customer existing = existingClient.get();
                existing.setName(customer.getName());
                existing.setDisplayName(customer.getDisplayName());
                existing.setContactEmail(customer.getContactEmail());
                existing.setContactPhone(customer.getContactPhone());
                existing.setStatus(customer.getStatus());
                
                logger.info("Updating existing client: {}", existing.getId());
                return daoFactory.getCustomerDao().save(existing);
            } else {
                logger.info("Creating new client with tenant ID: {}", customer.getMicrosoftTenantId());
                return daoFactory.getCustomerDao().save(customer);
            }
        } catch (DataAccessException e) {
            logger.error("Error creating or updating client", e);
            throw new RuntimeException("Failed to create or update client", e);
        }
    }
}
