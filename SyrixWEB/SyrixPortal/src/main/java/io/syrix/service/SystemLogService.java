package io.syrix.service;

import io.syrix.model.SystemLogEntry;
import io.syrix.model.SystemLogResponse;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class SystemLogService {

    private static final String[] ACTIVITIES = {"Login", "Logout", "System Settings", "Approval"};
    private static final String[] STATUSES = {"Success", "Failed", "Partial"};
    private static final String[] USER_NAMES = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"};
    private static final String[] EMAIL_DOMAINS = {"gmail.com", "outlook.com", "company.com", "example.org"};
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM d, yyyy");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm '(UTC)'");
    
    private final Random random = new Random();
    private List<SystemLogEntry> cachedLogs;
    
    // Generate a fixed set of random logs when the service starts
    public SystemLogService() {
        this.cachedLogs = generateRandomLogs(50);
    }

    public List<SystemLogEntry> getAllLogs() {
        return new ArrayList<>(cachedLogs);
    }

    public SystemLogResponse getSystemLogs(
            String search,
            String startDate,
            String endDate,
            List<String> activities,
            List<String> statuses,
            List<String> users,
            int page,
            int pageSize
    ) {
        List<SystemLogEntry> filteredLogs = new ArrayList<>(cachedLogs);
        
        // Apply search term if provided
        if (search != null && !search.isEmpty()) {
            String searchLower = search.toLowerCase();
            filteredLogs = filteredLogs.stream()
                    .filter(log -> 
                        log.description().toLowerCase().contains(searchLower) ||
                        log.user().name().toLowerCase().contains(searchLower) ||
                        log.user().email().toLowerCase().contains(searchLower) ||
                        log.activity().toLowerCase().contains(searchLower)
                    )
                    .collect(Collectors.toList());
        }
        
        // Apply activity filter
        if (activities != null && !activities.isEmpty()) {
            filteredLogs = filteredLogs.stream()
                    .filter(log -> activities.contains(log.activity()))
                    .collect(Collectors.toList());
        }
        
        // Apply status filter
        if (statuses != null && !statuses.isEmpty()) {
            filteredLogs = filteredLogs.stream()
                    .filter(log -> statuses.contains(log.status()))
                    .collect(Collectors.toList());
        }
        
        // Apply user filter
        if (users != null && !users.isEmpty()) {
            filteredLogs = filteredLogs.stream()
                    .filter(log -> users.contains(log.user().email()))
                    .collect(Collectors.toList());
        }
        
        // Apply date range filter
        if ((startDate != null && !startDate.isEmpty()) || (endDate != null && !endDate.isEmpty())) {
            filteredLogs = filteredLogs.stream()
                    .filter(log -> {
                        LocalDate logDate = LocalDate.parse(log.date().fullDate(), DATE_FORMATTER);
                        boolean isInRange = true;
                        
                        if (startDate != null && !startDate.isEmpty()) {
                            LocalDate start = LocalDate.parse(startDate, DATE_FORMATTER);
                            isInRange = isInRange && !logDate.isBefore(start);
                        }
                        
                        if (endDate != null && !endDate.isEmpty()) {
                            LocalDate end = LocalDate.parse(endDate, DATE_FORMATTER);
                            isInRange = isInRange && !logDate.isAfter(end);
                        }
                        
                        return isInRange;
                    })
                    .collect(Collectors.toList());
        }
        
        // Apply pagination
        int total = filteredLogs.size();
        int startIndex = (page - 1) * pageSize;
        
        List<SystemLogEntry> paginatedLogs;
        if (startIndex < total) {
            int endIndex = Math.min(startIndex + pageSize, total);
            paginatedLogs = filteredLogs.subList(startIndex, endIndex);
        } else {
            paginatedLogs = Collections.emptyList();
        }
        
        return new SystemLogResponse(paginatedLogs, total);
    }
    
    public SystemLogEntry getLogById(String id) {
        return cachedLogs.stream()
                .filter(log -> log.id().equals(id))
                .findFirst()
                .orElse(null);
    }
    
    public List<String> getUniqueActivities() {
        return Arrays.asList(ACTIVITIES);
    }
    
    public List<String> getUniqueStatuses() {
        return Arrays.asList(STATUSES);
    }
    
    public List<SystemLogEntry.User> getUniqueUsers() {
        return cachedLogs.stream()
                .map(SystemLogEntry::user)
                .distinct()
                .collect(Collectors.toList());
    }
    
    public String exportLogsToCSV(List<SystemLogEntry> logs) {
        StringBuilder csv = new StringBuilder();
        
        // Add headers
        csv.append("ID,Activity,Status,User Name,User Email,IP Address,Date,Time,Description\n");
        
        // Add rows
        for (SystemLogEntry log : logs) {
            csv.append(escapeCSV(log.id())).append(",");
            csv.append(escapeCSV(log.activity())).append(",");
            csv.append(escapeCSV(log.status())).append(",");
            csv.append(escapeCSV(log.user().name())).append(",");
            csv.append(escapeCSV(log.user().email())).append(",");
            csv.append(escapeCSV(log.ipAddress())).append(",");
            csv.append(escapeCSV(log.date().fullDate())).append(",");
            csv.append(escapeCSV(log.date().time())).append(",");
            csv.append(escapeCSV(log.description())).append("\n");
        }
        
        return csv.toString();
    }
    
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }
    
    private List<SystemLogEntry> generateRandomLogs(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> {
                    String id = UUID.randomUUID().toString();
                    
                    // Random activity
                    String activity = ACTIVITIES[random.nextInt(ACTIVITIES.length)];
                    
                    // Random status
                    String status = STATUSES[random.nextInt(STATUSES.length)];
                    
                    // Random user
                    String userName = USER_NAMES[random.nextInt(USER_NAMES.length)];
                    String email = userName.toLowerCase().replace(" ", ".") + 
                            "@" + EMAIL_DOMAINS[random.nextInt(EMAIL_DOMAINS.length)];
                    SystemLogEntry.User user = new SystemLogEntry.User(userName, email);
                    
                    // Random IP address
                    String ipAddress = generateRandomIp();
                    
                    // Random date within last 30 days
                    LocalDateTime dateTime = LocalDateTime.now().minusDays(random.nextInt(30));
                    String fullDate = dateTime.format(DATE_FORMATTER);
                    String time = dateTime.format(TIME_FORMATTER);
                    SystemLogEntry.DateInfo date = new SystemLogEntry.DateInfo(fullDate, time);
                    
                    // Random description
                    String description = "System log entry " + (i + 1) + ". " + 
                            activity + " " + (status.equals("Success") ? "completed successfully" : 
                            status.equals("Failed") ? "failed" : "partially completed") + ".";
                    
                    return new SystemLogEntry(id, activity, status, user, ipAddress, date, description);
                })
                .collect(Collectors.toList());
    }
    
    private String generateRandomIp() {
        return random.nextInt(256) + "." + 
                random.nextInt(256) + "." + 
                random.nextInt(256) + "." + 
                random.nextInt(256);
    }
}
