package io.syrix.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import io.syrix.dao.CustomerDao;
import io.syrix.dao.CustomerReportDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.report.CustomerReport;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.datamodel.report.PolicyResult;
import io.syrix.model.NotificationData;
import io.syrix.model.NotificationData.ApplicationInfo;
import io.syrix.model.NotificationData.NotificationType;
import io.syrix.model.NotificationData.ServiceInfo;
import io.syrix.utils.baseline.PolicyBaseline;
import jakarta.annotation.PostConstruct;

@Service
public class NotificationsService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationsService.class);
    
    private final CustomerReportDao customerReportDao;
    private final Environment environment;
    private final BaselinePolicyService baselinePolicyService;
    
    // Mock data for development - only used when development mode is enabled
    private final List<NotificationData> mockNotifications = new ArrayList<>();
    
    @Value("${syrix.notifications.use-mock-data:false}")
    private boolean useMockData;

    @Autowired
    public NotificationsService(CustomerDao clientDao, CustomerReportDao clientReportDao,
                                Environment environment, BaselinePolicyService baselinePolicyService) {
        this.customerReportDao = clientReportDao;
        this.environment = environment;
        this.baselinePolicyService = baselinePolicyService;
    }
    
    /**
     * Backward compatibility method - deprecated
     * @deprecated Use getNotifications(UUID companyId, int page, int pageSize) instead
     */
    @Deprecated
    public Map<String, Object> getNotifications(int page, int pageSize) {
        logger.warn("Using deprecated getNotifications method without companyId - returning empty notifications");
        return createEmptyResponse(page);
    }
    
    @PostConstruct
    public void init() {
        // Check if we should use mock data (development mode)
        boolean isDevelopmentMode = Arrays.asList(environment.getActiveProfiles()).contains("development") || useMockData;
        
        if (isDevelopmentMode) {
            logger.info("Development mode detected - initializing mock notification data");
            generateMockNotifications();
        } else {
            logger.info("Production mode - using database for notifications");
        }
    }

    /**
     * Retrieves notifications for a specific company from the latest client report
     * 
     * @param companyId UUID of the company to get notifications for
     * @param page page number (starting from 1)
     * @param pageSize page size
     * @return Map with notifications and pagination information
     */
    public Map<String, Object> getNotifications(UUID companyId, int page, int pageSize) {
        try {
            List<NotificationData> notifications;
            
            // Use mock data in development mode, otherwise use database
            boolean isDevelopmentMode = Arrays.asList(environment.getActiveProfiles()).contains("development") || useMockData;
            
            if (isDevelopmentMode) {
                logger.debug("Using mock notification data for development");
                notifications = new ArrayList<>(mockNotifications);
            } else {
                logger.debug("Using database notification data for production with companyId: {}", companyId);
                notifications = getNotificationsFromDatabase(companyId);
            }
            
            int totalCount = notifications.size();
            int totalPages = totalCount > 0 ? (int) Math.ceil((double) totalCount / pageSize) : 1;
            
            // Ensure page is within bounds
            page = Math.max(1, Math.min(page, totalPages));
            
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, totalCount);
            
            List<NotificationData> paginatedNotifications = 
                startIndex < totalCount ? 
                notifications.subList(startIndex, endIndex) : 
                new ArrayList<>();
            
            Map<String, Object> response = new HashMap<>();
            response.put("notifications", paginatedNotifications);
            response.put("totalCount", totalCount);
            response.put("currentPage", page);
            response.put("totalPages", totalPages);
            
            return response;
            
        } catch (Exception e) {
            logger.error("Error retrieving notifications: {}", e.getMessage(), e);
            return createEmptyResponse(page);
        }
    }

    /**
     * Retrieves notifications from database based on the latest company report
     * 
     * @param companyId UUID of the company to get notifications for
     * @return list of notifications
     * @throws DataAccessException if data access error occurs
     */
    private List<NotificationData> getNotificationsFromDatabase(UUID companyId) throws DataAccessException {
        if (companyId == null) {
            logger.warn("No companyId provided for notifications");
            return new ArrayList<>();
        }
        
        logger.debug("Getting notifications for company ID: {}", companyId);
        
        // Get latest report for the company
        Optional<CustomerReport> reportOpt = customerReportDao.findLatestByCustomerId(companyId);
        
        if (reportOpt.isEmpty()) {
            logger.warn("Report for company ID '{}' not found", companyId);
            return new ArrayList<>();
        }
        
        CustomerReport report = reportOpt.get();
        logger.debug("Found report for company ID '{}' from {}", companyId, report.getCreatedAt());
        
        // Convert report results to notifications
        return convertReportToNotifications(report);
    }
    
    /**
     * Converts report results to a list of notifications
     * 
     * @param report company report
     * @return list of notifications
     */
    private List<NotificationData> convertReportToNotifications(CustomerReport report) {
        List<NotificationData> notifications = new ArrayList<>();
        
        if (report.getResults() == null) {
            return notifications;
        }
        
        int notificationCounter = 0;
        
        for (Map.Entry<ConfigurationServiceType, List<PolicyResult>> entry : report.getResults().entrySet()) {
            ConfigurationServiceType serviceType = entry.getKey();
            List<PolicyResult> policyResults = entry.getValue();
            
            if (policyResults == null) {
                continue;
            }
            
            // Create notifications only for policies that are not met
            for (PolicyResult policyResult : policyResults) {
                if (policyResult != null && !policyResult.isRequirementMet()) {
                    NotificationData notification = createNotificationFromPolicyResult(
                        notificationCounter++, 
                        serviceType, 
                        policyResult
                    );
                    notifications.add(notification);
                }
            }
        }
        
        logger.debug("Created {} notifications from report", notifications.size());
        return notifications;
    }
    
    /**
     * Creates notification based on policy result with baseline policy context
     * 
     * @param id notification identifier
     * @param serviceType service type
     * @param policyResult policy result
     * @return notification object
     */
    private NotificationData createNotificationFromPolicyResult(int id, ConfigurationServiceType serviceType, PolicyResult policyResult) {
        // Try to get baseline policy for richer information
        PolicyBaseline baseline = null;
        try {
            if (policyResult.getPolicyId() != null && baselinePolicyService.isInitialized()) {
                baseline = baselinePolicyService.findPolicyByNumber(policyResult.getPolicyId());
            }
        } catch (Exception e) {
            logger.debug("Baseline policy not found for {}: {}", policyResult.getPolicyId(), e.getMessage());
        }
        
        // Determine notification type based on baseline criticality or fallback to policy criticality
        NotificationType notificationType;
        if (baseline != null && baseline.getCriticality() != null) {
            notificationType = mapBaselineCriticalityToNotificationType(baseline.getCriticality().getCode());
        } else {
            notificationType = mapCriticalityToNotificationType(policyResult.getCriticality());
        }
        
        // Create service information
        ServiceInfo serviceInfo = createServiceInfo(serviceType);
        
        // Application information (always Microsoft 365)
        ApplicationInfo applicationInfo = new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg");
        
        // Enhanced description using baseline policy data
        String description = createEnhancedDescription(policyResult, baseline);
        
        return new NotificationData(
            "notification-" + id,
            notificationType,
            serviceInfo,
            applicationInfo,
            description
        );
    }
    
    /**
     * Creates enhanced description using baseline policy information
     * 
     * @param policyResult the policy result
     * @param baseline the baseline policy (may be null)
     * @return enhanced description
     */
    private String createEnhancedDescription(PolicyResult policyResult, PolicyBaseline baseline) {
        StringBuilder description = new StringBuilder();
        
        // Start with policy ID
        description.append("[").append(policyResult.getPolicyId()).append("] ");
        
        // Use baseline title if available, otherwise use a generic title
        if (baseline != null && baseline.getTitle() != null && !baseline.getTitle().trim().isEmpty()) {
            description.append(baseline.getTitle());
        } else {
            description.append("Security policy compliance failure");
        }
        
        // Add brief rationale if available and description isn't too long
        if (baseline != null && baseline.getDescription() != null && !baseline.getDescription().trim().isEmpty()) {
            String rationale = baseline.getDescription().trim();
            // Keep it brief for notifications - truncate if too long
            if (rationale.length() > 100) {
                rationale = rationale.substring(0, 97) + "...";
            }
            description.append(" - ").append(rationale);
        }
        
        return description.toString();
    }
    
    /**
     * Maps baseline policy criticality to notification type
     * 
     * @param criticality baseline criticality code
     * @return notification type
     */
    private NotificationType mapBaselineCriticalityToNotificationType(String criticality) {
        if (criticality == null) {
            return NotificationType.low;
        }
        
        return switch (criticality.toUpperCase()) {
            case "SHALL" -> NotificationType.high;
            case "SHOULD" -> NotificationType.medium;
            case "MAY" -> NotificationType.low;
            default -> NotificationType.low;
        };
    }
    
    /**
     * Maps policy criticality to notification type
     * 
     * @param criticality criticality level
     * @return notification type
     */
    private NotificationType mapCriticalityToNotificationType(String criticality) {
        if (criticality == null) {
            return NotificationType.low;
        }
        
        return switch (criticality.toLowerCase()) {
            case "high", "critical" -> NotificationType.high;
            case "medium", "moderate" -> NotificationType.medium;
            default -> NotificationType.low;
        };
    }
    
    /**
     * Creates service information based on configuration type
     * 
     * @param serviceType service type
     * @return service information
     */
    private ServiceInfo createServiceInfo(ConfigurationServiceType serviceType) {
        return switch (serviceType) {
            case ENTRA -> new ServiceInfo("EntraID", "/icons/entraid.svg");
            case EXCHANGE_ONLINE -> new ServiceInfo("Exchange", "/icons/exchange.svg");
            case SHAREPOINT -> new ServiceInfo("Sharepoint / Onedrive", "/icons/sharepoint.svg");
            case DEFENDER -> new ServiceInfo("Defender", "/icons/defender.svg");
            case TEAMS -> new ServiceInfo("Microsoft Teams", "/icons/teams.svg");
            case POWER_PLATFORM -> new ServiceInfo("Power Platform", "/icons/powerplatform.svg");
            default -> new ServiceInfo("Microsoft 365", "/icons/microsoft365.svg");
        };
    }
    
    /**
     * Creates empty response in case of error
     * 
     * @param page page number
     * @return empty response with pagination
     */
    private Map<String, Object> createEmptyResponse(int page) {
        Map<String, Object> response = new HashMap<>();
        response.put("notifications", new ArrayList<NotificationData>());
        response.put("totalCount", 0);
        response.put("currentPage", page);
        response.put("totalPages", 1);
        return response;
    }
    
    /**
     * Generates mock notification data for development purposes
     * This method is only called when running in development mode
     */
    private void generateMockNotifications() {
        logger.debug("Generating mock notification data for development");
        
        mockNotifications.clear();
        
        mockNotifications.add(new NotificationData(
            "notification-0",
            NotificationType.high,
            new ServiceInfo("EntraID", "/icons/entraid.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue"
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-1",
            NotificationType.medium,
            new ServiceInfo("Exchange", "/icons/exchange.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-2",
            NotificationType.low,
            new ServiceInfo("Sharepoint / Onedrive", "/icons/sharepoint.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-3",
            NotificationType.high,
            new ServiceInfo("Defender", "/icons/defender.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-4",
            NotificationType.medium,
            new ServiceInfo("Microsoft Groups", "/icons/groups.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-5",
            NotificationType.low,
            new ServiceInfo("EntraID", "/icons/entraid.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-6",
            NotificationType.high,
            new ServiceInfo("Exchange", "/icons/exchange.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-7",
            NotificationType.medium,
            new ServiceInfo("Sharepoint / Onedrive", "/icons/sharepoint.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-8",
            NotificationType.low,
            new ServiceInfo("Defender", "/icons/defender.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        mockNotifications.add(new NotificationData(
            "notification-9",
            NotificationType.high,
            new ServiceInfo("Microsoft Groups", "/icons/groups.svg"),
            new ApplicationInfo("Microsoft 365", "/icons/microsoft365.svg"),
            "Title of issue. Lorem ipsum dolor sit amet, consectetur..."
        ));
        
        logger.info("Generated {} mock notifications for development", mockNotifications.size());
    }
}
