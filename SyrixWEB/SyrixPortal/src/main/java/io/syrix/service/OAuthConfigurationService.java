package io.syrix.service;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.MSALOAuthConfig;
import io.syrix.datamodel.oauth.OAuthConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing OAuth2 configurations using the syrixdao component.
 */
@Service
public class OAuthConfigurationService {
    private static final Logger logger = LoggerFactory.getLogger(OAuthConfigurationService.class);

    private final DaoFactory daoFactory;

    @Autowired
    public OAuthConfigurationService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
    }

    /**
     * Get all OAuth2 configurations.
     *
     * @return List of all OAuth2 configurations
     */
    @Transactional(readOnly = true)
    public List<OAuthConfig> getAllConfigurations() {
        try {
            return daoFactory.getOAuthConfigurationDao().getAllConfigurations();
        } catch (DataAccessException e) {
            logger.error("Error getting all OAuth2 configurations", e);
            throw new RuntimeException("Failed to get OAuth2 configurations", e);
        }
    }

    /**
     * Get active OAuth2 configurations.
     *
     * @return List of active OAuth2 configurations
     */
    @Transactional(readOnly = true)
    public List<OAuthConfig> getActiveConfigurations() {
        try {
            return daoFactory.getOAuthConfigurationDao().findByStatus(ConnectionStatus.ACTIVE);
        } catch (DataAccessException e) {
            logger.error("Error getting active OAuth2 configurations", e);
            throw new RuntimeException("Failed to get active OAuth2 configurations", e);
        }
    }

    /**
     * Get OAuth2 configuration by ID.
     *
     * @param id Configuration ID
     * @return The configuration if found
     */
    @Transactional(readOnly = true)
    public Optional<OAuthConfig> getConfigurationById(UUID id) {
        try {
            return daoFactory.getOAuthConfigurationDao().findById(id);
        } catch (DataAccessException e) {
            logger.error("Error getting OAuth2 configuration by ID: {}", id, e);
            throw new RuntimeException("Failed to get OAuth2 configuration by ID", e);
        }
    }

    /**
     * Get OAuth2 configuration by provider name.
     *
     * @param providerName Provider name
     * @return The configuration if found
     */
    @Transactional(readOnly = true)
    public Optional<OAuthConfig> getConfigurationByProviderName(ServiceType service) {
        try {
            return daoFactory.getOAuthConfigurationDao().findByService(service);
        } catch (DataAccessException e) {
            logger.error("Error getting OAuth2 configuration by provider name: {}", service, e);
            throw new RuntimeException("Failed to get OAuth2 configuration by provider name", e);
        }
    }

    /**
     * Get Microsoft (MSAL) OAuth2 configuration.
     *
     * @return The MSAL configuration if found
     */
    @Transactional(readOnly = true)
    public Optional<MSALOAuthConfig> getMSALConfiguration() {
        try {
            // For the new structure, we're directly getting the Microsoft OAuth configuration
            // from the SyrixConfig collection
            return daoFactory.getOAuthConfigurationDao()
                    .getConfigurationByService(ServiceType.OFFICE365)
                    .map(config -> {
                        if (config instanceof MSALOAuthConfig) {
                            return (MSALOAuthConfig) config;
                        } else {
                            // If it's not a MSALOAuth2Config, convert it
                            return MSALOAuthConfig.builder()
                                    .serviceType(config.getServiceType())
                                    .clientId(config.getClientId())
                                    .clientSecret(config.getClientSecret())
                                    .redirectUri(config.getRedirectUri())
                                    .scopes(config.getScopes())
                                    .authUrl(config.getAuthUrl())
                                    .tokenUrl(config.getTokenUrl())
                                    .build();
                        }
                    });
        } catch (Exception e) {
            logger.error("Error getting MSAL OAuth2 configuration", e);
            throw new RuntimeException("Failed to get MSAL OAuth2 configuration", e);
        }
    }

    /**
     * Create or update an OAuth2 configuration.
     *
     * @param config The configuration to save
     * @return The saved configuration
     */
    @Transactional
    public OAuthConfig saveConfiguration(OAuthConfig config) {
        try {
            return daoFactory.getOAuthConfigurationDao().save(config);
        } catch (DataAccessException e) {
            logger.error("Error saving OAuth2 configuration", e);
            throw new RuntimeException("Failed to save OAuth2 configuration", e);
        }
    }

    /**
     * Delete an OAuth2 configuration by ID.
     *
     * @param id Configuration ID
     */
    @Transactional
    public void deleteConfiguration(UUID id) {
        try {
            daoFactory.getOAuthConfigurationDao().deleteById(id);
        } catch (DataAccessException e) {
            logger.error("Error deleting OAuth2 configuration: {}", id, e);
            throw new RuntimeException("Failed to delete OAuth2 configuration", e);
        }
    }
}
