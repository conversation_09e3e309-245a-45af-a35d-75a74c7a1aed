package io.syrix.service;

import io.syrix.exception.connection.ConnectionCreationException;
import io.syrix.exception.connection.ConnectionDeletionException;
import io.syrix.exception.connection.ConnectionRetrievalException;
import io.syrix.exception.connection.ConnectionUpdateException;
import io.syrix.dao.DaoFactory;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static io.syrix.datamodel.ConnectionStatus.ACTIVE;

/**
 * Service for managing connection information using the syrixdao component.
 */
@org.springframework.stereotype.Service
public class ConnectionService {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionService.class);

    private final DaoFactory daoFactory;

    @org.springframework.beans.factory.annotation.Autowired
    public ConnectionService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
        logger.info("ConnectionService initialized with daoFactory: {}", daoFactory.getClass().getName());
    }

    /**
     * Create a new connection.
     *
     * @param connectionInfo The connection info to create
     * @return The created connection info
     */
    public ConnectionInfo createConnection(ConnectionInfo connectionInfo) {
        try {
            // Set creation time if not already set
            if (connectionInfo.getCreatedAt() == null) {
                connectionInfo.setCreatedAt(LocalDateTime.now());
            }
            connectionInfo.setUpdatedAt(LocalDateTime.now());
            
            logger.info("Creating new connection for company: {}, service: {}", 
                connectionInfo.getCustomerId(), connectionInfo.getServiceType().name());
            
            return daoFactory.getConnectionInfoDao().save(connectionInfo);
        } catch (DataAccessException e) {
            logger.error("Error creating connection", e);
            throw new ConnectionCreationException("Failed to create connection", e);
        }
    }

    /**
     * Get a connection by ID.
     *
     * @param id The connection ID
     * @return An optional containing the connection, or empty if not found
     */
    public Optional<ConnectionInfo> getConnectionById(UUID id) {
        try {
            return daoFactory.getConnectionInfoDao().findById(id);
        } catch (DataAccessException e) {
            logger.error("Error getting connection by ID: {}", id, e);
            throw new ConnectionRetrievalException("Failed to get connection by ID: " + id, e);
        }
    }

    /**
     * Get all connections.
     *
     * @return A list of all connections
     */

    public List<ConnectionInfo> getAllConnections() {
        try {
            return daoFactory.getConnectionInfoDao().findAll();
        } catch (DataAccessException e) {
            logger.error("Error getting all connections", e);
            throw new ConnectionRetrievalException("Failed to get all connections", e);
        }
    }

    /**
     * Get connections by company ID.
     *
     * @param companyId The company ID
     * @return A list of connections for the company
     */

    public List<ConnectionInfo> getConnectionsByCompany(UUID companyId) {
        try {
            return daoFactory.getConnectionInfoDao().findByCustomerId(companyId.toString());
        } catch (DataAccessException e) {
            logger.error("Error getting connections for company: {}", companyId, e);
            throw new ConnectionRetrievalException("Failed to get connections for company: " + companyId, e);
        }
    }

    /**
     * Get connections by service type.
     *
     * @param serviceType The service type
     * @return A list of connections for the service type
     */

    public List<ConnectionInfo> getConnectionsByServiceType(ServiceType serviceType) {
        try {
            return daoFactory.getConnectionInfoDao().findByService(serviceType);
        } catch (DataAccessException e) {
            logger.error("Error getting connections for service type: {}", serviceType, e);
            throw new ConnectionRetrievalException("Failed to get connections for service type: " + serviceType, e);
        }
    }

    /**
     * Update a connection.
     *
     * @param connectionInfo The connection info to update
     * @return The updated connection info
     */
    public ConnectionInfo updateConnection(ConnectionInfo connectionInfo) {
        try {
            connectionInfo.setUpdatedAt(LocalDateTime.now());
            
            logger.info("Updating connection: {}", connectionInfo.getId());
            
            return daoFactory.getConnectionInfoDao().save(connectionInfo);
        } catch (DataAccessException e) {
            logger.error("Error updating connection: {}", connectionInfo.getId(), e);
            throw new ConnectionUpdateException("Failed to update connection: " + connectionInfo.getId(), e);
        }
    }

    /**
     * Delete a connection by ID.
     *
     * @param id The connection ID to delete
     */
    public void deleteConnection(UUID id) {
        try {
            logger.info("Deleting connection: {}", id);
            daoFactory.getConnectionInfoDao().deleteById(id);
        } catch (DataAccessException e) {
            logger.error("Error deleting connection: {}", id, e);
            throw new ConnectionDeletionException("Failed to delete connection: " + id, e);
        }
    }

    /**
     * Get active connections by company ID.
     *
     * @param companyId The company ID
     * @return A list of active connections for the company
     */
    public List<ConnectionInfo> getActiveConnectionsByCompany(UUID companyId) {
        try {
            List<ConnectionInfo> allConnections = getConnectionsByCompany(companyId);
            List<ConnectionInfo> activeConnections = new ArrayList<>();
            
            for (ConnectionInfo connection : allConnections) {
                if (ACTIVE == connection.getStatus()) {
                    activeConnections.add(connection);
                }
            }
            
            return activeConnections;
        } catch (Exception e) {
            logger.error("Error getting active connections for company: {}", companyId, e);
            throw new ConnectionRetrievalException("Failed to get active connections for company: " + companyId, e);
        }
    }
}
