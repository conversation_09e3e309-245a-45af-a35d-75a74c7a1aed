package io.syrix.service;

import io.syrix.model.NextScanInfo;
import io.syrix.model.ScanStatus;
import io.syrix.model.ScanStatusResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Service dedicated to handling scan-related functionality.
 * This contains mockup data for now, but will be replaced with real business logic later.
 */
@Service
public class ScanService {
    private static final Logger logger = LoggerFactory.getLogger(ScanService.class);
    private static final Random random = new Random();
    
    /**
     * Get information about the next scheduled scan.
     * Currently returns mockup data that will be replaced with real data later.
     *
     * @return NextScanInfo object with details about the upcoming scan
     */
    public NextScanInfo getNextScanInfo() {
        logger.info("Generating next scan information from API");
        
        // Generate a random future time between 1 and 48 hours from now
        int hoursInFuture = random.nextInt(47) + 1;
        LocalDateTime scheduledTime = LocalDateTime.now().plusHours(hoursInFuture).truncatedTo(ChronoUnit.MINUTES);
        
        // Generate random scan type
        String[] scanTypes = {"Full System Scan", "Security Compliance Check", "Vulnerability Assessment", "Configuration Audit"};
        String scanType = scanTypes[random.nextInt(scanTypes.length)];
        
        // Generate status
        String status = "Scheduled";
        
        // Generate target system
        String[] targetSystems = {"Microsoft 365", "Azure AD", "SharePoint Online", "Exchange Online", "All Systems"};
        String targetSystem = targetSystems[random.nextInt(targetSystems.length)];
        
        // Generate estimated duration (between 30 and 240 minutes)
        long estimatedDuration = random.nextInt(211) + 30;
        
        return new NextScanInfo(scheduledTime, scanType, status, targetSystem, estimatedDuration);
    }
    
    /**
     * Get scan status information for different services.
     * Currently returns mockup data that will be replaced with real data later.
     *
     * @return ScanStatusResponse with a list of scan statuses for different services
     */
    public ScanStatusResponse getScanStatuses() {
        logger.info("Generating scan status information from API");
        
        List<ScanStatus> scanStatuses = new ArrayList<>();
        
        // Add EntraID scan status
        scanStatuses.add(new ScanStatus(
            "EntraID",
            "2 hours ago",
            "2 hours",
            "Scanned"
        ));
        
        // Add Exchange scan status
        scanStatuses.add(new ScanStatus(
            "Exchange",
            "2 hours ago",
            "2 hours",
            "Scanned"
        ));
        
        // Add Sharepoint/Onedrive scan status
        scanStatuses.add(new ScanStatus(
            "Sharepoint / Onedrive",
            "2 hours ago",
            "2 hours",
            "Scanned"
        ));
        
        // Add Defender scan status
        scanStatuses.add(new ScanStatus(
            "Defender",
            "2 hours ago",
            "2 hours",
            "Scanned"
        ));
        
        // Add another EntraID scan status
        scanStatuses.add(new ScanStatus(
            "EntraID",
            "2 hours ago",
            "2 hours",
            "Scanned"
        ));
        
        return new ScanStatusResponse(scanStatuses);
    }
    
    /**
     * Get current scan status information.
     * Alias for getScanStatuses to maintain consistency with endpoint naming.
     *
     * @return ScanStatusResponse with a list of scan statuses
     */
    public ScanStatusResponse getCurrentScanStatus() {
        return getScanStatuses();
    }
}