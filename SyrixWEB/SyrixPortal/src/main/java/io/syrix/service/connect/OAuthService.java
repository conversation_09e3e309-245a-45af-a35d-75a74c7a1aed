package io.syrix.service.connect;

import io.syrix.exception.OAuth2FlowException;
import io.syrix.model.connect.OAuth2InitRequest;
import io.syrix.model.connect.OAuth2InitResponse;

import io.syrix.dao.OAuth2TokenDao;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.OAuthConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for handling OAuth2 flows for various services.
 * Uses database configuration through OAuth2ConfigurationService.
 */
@Service
public class OAuthService {

    private static final Logger logger = LoggerFactory.getLogger(OAuthService.class);
    // New constant for the "email" literal
    private static final String CLAIM_EMAIL = "email";
    // Added constant to avoid duplication of literal "expires_in"
    private static final String EXPIRES_IN = "expires_in";

    // Using MSALAuthProvider for secure OAuth handling
    private final MSALAuthProvider msalAuthProvider;

    // Configuration service for OAuth2
    private final OAuth2ConfigurationRetrievalService configService;
    // DAO for OAuth2 tokens
    private final OAuth2TokenDao oauth2TokenDao;
    
    private final RestTemplate restTemplate;

    // In-memory storage for connections - used as a backup
    private final List<ConnectionInfo> backupConnections = new ArrayList<>();

    public OAuthService(MSALAuthProvider msalAuthProvider, OAuth2ConfigurationRetrievalService configService,
                       @Qualifier("postgresOAuth2TokenDao") OAuth2TokenDao oauth2TokenDao) {
        this.msalAuthProvider = msalAuthProvider;
        this.configService = configService;
        this.oauth2TokenDao = oauth2TokenDao;
        this.restTemplate = new RestTemplate();
        
        // Removed deprecated overridden handleError method.
        this.restTemplate.setErrorHandler(new DefaultResponseErrorHandler());
        
        logger.info("OAuth2Service initialized with OAuth2ConfigurationService and OAuth2TokenDao");
    }

    /**
     * Initialize the OAuth2 flow for a service.
     * 
     * @param request Contains service, tenantId and appId
     * @return Response with authorization URL
     */
    public OAuth2InitResponse initializeMSOAuth2Flow(OAuth2InitRequest request) {
        String clientId;
        // If appId is provided in the request, use that, otherwise get from database

        OAuthConfig config = configService.getOAuthConfiguration(ServiceType.OFFICE365);
        clientId = config.getClientId();
        verifyAzureAppRegistration(clientId);

        String state = UUID.randomUUID().toString();
        logger.info("Generated state parameter: {}", state);
        // Store company ID in state
        logger.info("Stored company ID in state: {}", request.getCompanyId());
        // Build the authorization URL based on the service
        logger.info("Building Microsoft authorization URL for tenant: {}", request.getTenantId());
        // Use MSALAuthProvider for Microsoft OAuth
        String authUrl = msalAuthProvider.generateAuthorizationUrl(state);
        if (authUrl != null) {
            if (logger.isInfoEnabled()) {
                String shortAuthUrl = authUrl.substring(0, Math.min(50, authUrl.length()));
                logger.info("Authorization URL generated (partial): {}...", shortAuthUrl);
            }
        } else {
            throw new OAuth2FlowException("Failed to generate authorization URL");
        }
        return new OAuth2InitResponse(authUrl, state);
    }

    /**
     * Complete the OAuth2 flow after receiving the authorization code.
     * 
     * @param code The authorization code from the OAuth provider
     * @return Connection information
     */
    public ConnectionInfo completeMSOAuth2Flow(String code, String clientId) {
        logger.info("Completing OAuth2 flow for clientId: {}", clientId);
        try {
            logger.info("Exchanging code for tokens using MSALAuthProvider");
            Map<String, Object> tokenResponse = msalAuthProvider.exchangeAuthCodeForTokens(code);
            logger.info("Received token response keys: {}", tokenResponse.keySet());

            TokenInfo tokenInfo = processTokenResponse(tokenResponse);
            logger.info("Extracted tenant ID: {}", tokenInfo.tenantId);
            if (logger.isInfoEnabled()) {
                String shortAccessToken = tokenInfo.accessToken.substring(0, Math.min(10, tokenInfo.accessToken.length()));
                logger.info("Access token obtained (partial): {}...", shortAccessToken);
            }
            
            String domain = resolveDomain(tokenResponse, tokenInfo.tenantId);
            
            // Get scopes from configuration
            OAuthConfig config = configService.getOAuthConfiguration(ServiceType.OFFICE365);
            String scopes = config.getScopes();
            
            // Save tokens to database
            boolean saved = oauth2TokenDao.saveTokens(clientId, ServiceType.OFFICE365,
                    tokenInfo.accessToken, tokenInfo.refreshToken, tokenInfo.tenantId,
                    Timestamp.valueOf(tokenInfo.expirationTime), scopes, domain);
            if (!saved) {
                throw new OAuth2FlowException("Failed to save tokens to database");
            }

            UUID id = UUID.randomUUID();
            logger.info("Creating connection info with tenantId={}, clientId={}, domain={}", tokenInfo.tenantId, clientId, domain);
            ConnectionInfo connectionInfo = new ConnectionInfo(id, ServiceType.OFFICE365, ConnectionStatus.ACTIVE, "",
                    LocalDateTime.now(), tokenInfo.tenantId, UUID.fromString(clientId), domain);
            backupConnections.add(connectionInfo);
            logger.info("Added connection to backup store, id: {}", id);
            return connectionInfo;
        } catch (Exception e) {
            logger.error("Error completing OAuth2 flow for state {}: {}", clientId, e.getMessage(), e);
            throw new OAuth2FlowException("Error completing OAuth2 flow for client " + clientId + ": " + e.getMessage(), e);
        }
    }

    // New helper class to hold token details.
    private static class TokenInfo {
        String tenantId;
        String accessToken;
        String refreshToken;
        LocalDateTime expirationTime;
        TokenInfo(String tenantId, String accessToken, String refreshToken, LocalDateTime expirationTime) {
            this.tenantId = tenantId;
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.expirationTime = expirationTime;
        }
    }

    // New helper method to process the token response.
    private TokenInfo processTokenResponse(Map<String, Object> tokenResponse) {
        if (!tokenResponse.containsKey("access_token")) {
            logger.error("Token response did not contain an access token: {}", tokenResponse);
            throw new IllegalStateException("Failed to obtain access token from Microsoft");
        }
        String tenantId = (String) tokenResponse.get("tid");
        String accessToken = (String) tokenResponse.get("access_token");
        String refreshToken = (String) tokenResponse.get("refresh_token");
        if (refreshToken == null || refreshToken.isEmpty()) {
            logger.warn("No refresh token received. Keys: {}", tokenResponse.keySet());
        } else {
            logger.info("Received refresh token (length: {})", refreshToken.length());
        }
        Object expiresInObj = tokenResponse.get(EXPIRES_IN);
        int expiresIn;
        if (expiresInObj instanceof Integer integer) {
            expiresIn = integer;
        } else {
            expiresIn = Integer.parseInt(expiresInObj.toString());
        }
        LocalDateTime expirationTime = LocalDateTime.now().plusSeconds(expiresIn);
        return new TokenInfo(tenantId, accessToken, refreshToken, expirationTime);
    }

    // New helper method to resolve the domain.
    private String resolveDomain(Map<String, Object> tokenResponse, String tenantId) {
        String domain;
        if (tokenResponse.containsKey("id_token")) {
            domain = extractDomainFromIdToken((String) tokenResponse.get("id_token"));
            if (domain != null && !domain.isEmpty()) {
                logger.info("Extracted domain from id_token: {}", domain);
                return domain;
            }
        }
        if (tokenResponse.containsKey(CLAIM_EMAIL)) {
            String email = (String) tokenResponse.get(CLAIM_EMAIL);
            if (email != null && email.contains("@")) {
                domain = email.substring(email.indexOf("@") + 1);
                logger.info("Extracted domain from email: {}", domain);
                return domain;
            }
        }
        domain = (tenantId != null && !tenantId.isEmpty()) ? tenantId + ".onmicrosoft.com" : "";
        logger.info("Using placeholder domain based on tenantId: {}", domain);
        return domain;
    }

    // --- Helper to extract domain from id_token ---
    private String extractDomainFromIdToken(String idToken) {
        if (idToken == null) return null;
        String[] parts = idToken.split("\\.");
        if (parts.length < 2) return null;
        String payloadJson = new String(java.util.Base64.getUrlDecoder().decode(parts[1]));
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            Map<String, Object> payload = mapper.readValue(payloadJson,
					new com.fasterxml.jackson.core.type.TypeReference<>() {});
            String email = (String) payload.getOrDefault("preferred_username", payload.get("upn"));
            if (email == null) email = (String) payload.get(CLAIM_EMAIL); // modified
            if (email != null && email.contains("@")) {
                return email.substring(email.indexOf("@") + 1);
            }
        } catch (Exception e) {
            logger.warn("Failed to parse id_token for domain: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Get all connections from the oauth2_tokens table.
     * 
     * @return List of connections
     */
    public List<ConnectionInfo> getConnections() {
        logger.info("Getting all connections from database");
        
        // Use the DAO to get connections
        List<io.syrix.datamodel.ConnectionInfo> connections = oauth2TokenDao.getAllConnections();
        
        // If no connections from DAO, use backup connections
        if (connections.isEmpty() && !backupConnections.isEmpty()) {
            logger.info("No connections from database, using backup connections");
            return new ArrayList<>(backupConnections);
        }
        
        logger.info("Retrieved {} connections", connections.size());
        return connections;
    }

    /**
     * Delete a connection by ID.
     * 
     * @param id Connection ID
     */
    public void deleteConnection(UUID id) {
        logger.info("Deleting connection with ID: {}", id);
        
        // Use the DAO to delete the connection
        boolean deleted = oauth2TokenDao.deleteConnection(id);
        
        if (deleted) {
            logger.info("Successfully deleted connection with ID: {}", id);
        } else {
            logger.warn("No connection found with ID: {} in database", id);
        }
        
        // Also remove from backup list if present
        backupConnections.removeIf(connection -> connection.getId().equals(id));
    }

    /**
     * Verify that the Azure AD app registration exists and is properly configured
     * This method attempts to get the OpenID configuration which will indicate if
     * the app ID is valid
     * 
     * @param appId The client ID / app ID to verify
     */
    private void verifyAzureAppRegistration(String appId) {
        if (appId == null || appId.isEmpty()) {
            logger.warn("Cannot verify app registration: No app ID provided");
            return;
        }
        try {
            String wellKnownUrl = "https://login.microsoftonline.com/common/.well-known/openid-configuration";
            logger.info("Checking Azure app registration by accessing OpenID configuration");

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    wellKnownUrl,
                    HttpMethod.GET,
                    null,
					new ParameterizedTypeReference<>() {}
            );
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                logger.info("Successfully accessed OpenID configuration, app registration endpoint exists");
            } else {
                logger.warn("Unable to access OpenID configuration, app registration may not exist");
            }

            // Extracted method call to verify app metadata
            checkAppMetadata(appId);
        } catch (Exception e) {
            // Updated: log full exception context.
            logger.error("Error checking Azure app registration: {}", e.getMessage(), e);
        }
    }
    
    // New helper method to check app metadata using the appId.
    private void checkAppMetadata(String appId) {
        try {
            String appMetadataUrl = "https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration?appid=" + appId;
            // Changed code: use exchange with ParameterizedTypeReference instead of raw type Map
            ResponseEntity<Map<String, Object>> appResponse = restTemplate.exchange(
                    appMetadataUrl,
                    HttpMethod.GET,
                    null,
					new ParameterizedTypeReference<>() {}
            );
            if (appResponse.getStatusCode().is2xxSuccessful() && appResponse.getBody() != null) {
                logger.info("App ID {} appears to be registered in Azure AD", appId);
            }
        } catch (Exception e) {
            // Updated: log full exception context.
            logger.warn("Could not verify specific app ID {}: {}", appId, e.getMessage(), e);
        }
    }
}
