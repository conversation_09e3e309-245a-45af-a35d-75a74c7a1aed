package io.syrix.service.test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;

import io.syrix.model.TestStatusItem;

/**
 * Service dedicated to handling test status related functionality.
 * This now retrieves data from the /api/test-status endpoint instead of using hardcoded mock data.
 */
@Service
public class TestStatusService {
    private static final Logger logger = LoggerFactory.getLogger(TestStatusService.class);
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public TestStatusService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Returns test status items from the /api/test-status endpoint.
     * Falls back to mock data if the API call fails.
     *
     * @return List of TestStatusItem objects representing different test status categories
     */
    @SuppressWarnings("unchecked")
    public List<TestStatusItem> getTestStatusItems() {
        logger.info("Retrieving test status items from /api/v1/test-status endpoint");
        
        try {
            // Call the internal API endpoint
            String url = "http://localhost:8080/api/v1/test-status";
            logger.info("Making API call to: {}", url);
            
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            
            if (response != null && response.containsKey("items")) {
                List<Map<String, Object>> itemMaps = (List<Map<String, Object>>) response.get("items");
                
                List<TestStatusItem> items = itemMaps.stream()
                    .map(this::mapToTestStatusItem)
                    .toList();
                
                logger.info("Successfully retrieved {} test status items from API", items.size());
                return items;
            } else {
                logger.warn("API response does not contain 'items' field, falling back to mock data");
                return getFallbackTestStatusItems();
            }
            
        } catch (RestClientException e) {
            logger.error("Failed to retrieve test status items from API: {}", e.getMessage());
            logger.info("Falling back to mock data");
            return getFallbackTestStatusItems();
        } catch (Exception e) {
            logger.error("Unexpected error while retrieving test status items: {}", e.getMessage(), e);
            logger.info("Falling back to mock data");
            return getFallbackTestStatusItems();
        }
    }
    
    /**
     * Maps a Map object from the API response to a TestStatusItem.
     */
    private TestStatusItem mapToTestStatusItem(Map<String, Object> itemMap) {
        String label = (String) itemMap.get("label");
        Integer value = (Integer) itemMap.get("value");
        String color = (String) itemMap.get("color");
        String iconTypeStr = (String) itemMap.get("iconType");
        String listGradient = (String) itemMap.get("listGradient");
        Double listOpacity = (Double) itemMap.get("listOpacity");
        
        // Parse iconType enum
        TestStatusItem.IconType iconType;
        try {
            iconType = TestStatusItem.IconType.valueOf(iconTypeStr);
        } catch (Exception e) {
            logger.warn("Invalid iconType '{}', defaulting to 'ok'", iconTypeStr);
            iconType = TestStatusItem.IconType.ok;
        }
        
        return new TestStatusItem(label, value, color, iconType, listGradient, listOpacity);
    }
    
    /**
     * Provides fallback mock data when the API is unavailable.
     */
    private List<TestStatusItem> getFallbackTestStatusItems() {
        logger.info("Using fallback mock data for test status items");
        
        return Arrays.asList(
                new TestStatusItem("One click to fix", 10, "#E14B4B", 
                        TestStatusItem.IconType.critical, null, null),
                new TestStatusItem("Can't fix no licence", 5, "#F6C244", 
                        TestStatusItem.IconType.warning, null, null),
                new TestStatusItem("Ignored", 10, "#8B949E", 
                        TestStatusItem.IconType.warning, null, null),
                new TestStatusItem("Ok", 120, "#0F657F", 
                        TestStatusItem.IconType.ok, null, null),
                new TestStatusItem("AutoFixed", 100, "#08B798", 
                        TestStatusItem.IconType.ok, null, null)
        );
    }
    
    /**
     * Calculates the total number of tests across all status categories.
     *
     * @return The total number of tests
     */
    public int calculateTotalTests() {
        List<TestStatusItem> items = getTestStatusItems();
        return items.stream().mapToInt(TestStatusItem::value).sum();
    }
}