package io.syrix.service.connect;

import io.syrix.dao.DaoFactory;
import io.syrix.dao.exception.DataAccessException;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.model.connect.ConnectionInfo;
import io.syrix.model.connect.Token;
import io.syrix.model.connect.TokenType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing connection information with external services.
 * Provides operations for creating and retrieving connection data.
 */
@Service
public class ConnectionInfoService {
	private static final Logger logger = LoggerFactory.getLogger(ConnectionInfoService.class);

	private final DaoFactory daoFactory;

	@Autowired
	public ConnectionInfoService(DaoFactory daoFactory) {
		this.daoFactory = daoFactory;
	}

	/**
	 * Create new connection information or update existing one if it already exists for the same service type and client ID.
	 *
	 * @param connectionInfo The connection information to create or update
	 * @return The created or updated connection information
	 */
	@Transactional
	public ConnectionInfo createOrUpdateConnectionInfo(ConnectionInfo connectionInfo) {
		try {
			ServiceType serviceType = ServiceType.valueOf(connectionInfo.getService().toUpperCase());
			UUID clientId = connectionInfo.getCompanyId();

			// Check if connection already exists for this service type and client ID
			Optional<io.syrix.datamodel.ConnectionInfo> existingConnection = daoFactory.getConnectionInfoDao().findByServiceAndCustomerId(serviceType, clientId);

			io.syrix.datamodel.ConnectionInfo dataModelConnection;

			if (existingConnection.isPresent()) {
				// Update existing connection
				dataModelConnection = existingConnection.get();
				updateConnectionFields(dataModelConnection, connectionInfo);
				logger.info("Updating existing connection info for service: {} and clientId: {}", serviceType, clientId);
			} else {
				// Create new connection
				dataModelConnection = convertToDataModel(connectionInfo);
				logger.info("Creating new connection info for service: {} and clientId: {}", serviceType, clientId);
			}

			io.syrix.datamodel.ConnectionInfo savedConnection = daoFactory.getConnectionInfoDao().save(dataModelConnection);

			return convertFromDataModel(savedConnection);
		} catch (DataAccessException e) {
			logger.error("Error creating or updating connection info for service: {}", connectionInfo.getService(), e);
			throw new RuntimeException("Failed to create or update connection information", e);
		}
	}


	/**
	 * Update existing connection fields with new values from web model.
	 */
	private void updateConnectionFields(io.syrix.datamodel.ConnectionInfo existing, ConnectionInfo webModel) {
		// Update fields with new values
		if (webModel.getDisplayName() != null) {
			existing.setDisplayName(webModel.getDisplayName());
		}
		if (webModel.getLastConnected() != null) {
			existing.setLastConnected(webModel.getLastConnected());
		}
		if (webModel.getExternalTenantId() != null) {
			existing.setExternalTenantId(webModel.getExternalTenantId());
		}
		if (webModel.getDomain() != null) {
			existing.setDomain(webModel.getDomain());
		}
		if (webModel.getToken() != null) {
			existing.setToken(convertTokenToDataModel(webModel.getToken()));
		}
		// Update connection status based on connected flag
		existing.setStatus(webModel.isConnected() ? ConnectionStatus.ACTIVE : ConnectionStatus.INACTIVE);
	}

	/**
	 * Convert from web model to data model.
	 */
	private io.syrix.datamodel.ConnectionInfo convertToDataModel(ConnectionInfo webModel) {
		io.syrix.datamodel.ConnectionInfo dataModel = new io.syrix.datamodel.ConnectionInfo(
				webModel.getId(),
				ServiceType.valueOf(webModel.getService().toUpperCase()),
				ConnectionStatus.ACTIVE,
				webModel.getDisplayName(),
				webModel.getLastConnected(),
				webModel.getExternalTenantId(),
				webModel.getCompanyId(),
				webModel.getDomain()
		);

		// Map token if present
		if (webModel.getToken() != null) {
			dataModel.setToken(convertTokenToDataModel(webModel.getToken()));
		}

		return dataModel;
	}

	/**
	 * Convert from data model to web model.
	 */
	private ConnectionInfo convertFromDataModel(io.syrix.datamodel.ConnectionInfo dataModel) {
		ConnectionInfo webModel = new ConnectionInfo(
				dataModel.getId(),
				dataModel.getService().name(),
				dataModel.getStatus() == ConnectionStatus.ACTIVE,
				dataModel.getDisplayName(),
				dataModel.getLastConnected(),
				dataModel.getExternalTenantId(),
				dataModel.getCustomerId(),
				dataModel.getDomain()
		);

		// Map token if present
		if (dataModel.getToken() != null) {
			webModel.setToken(convertTokenFromDataModel(dataModel.getToken()));
		}

		return webModel;
	}

	/**
	 * Convert web model token to data model token.
	 */
	private io.syrix.datamodel.token.Token convertTokenToDataModel(Token webToken) {
		io.syrix.datamodel.token.Token dataToken = new io.syrix.datamodel.token.Token();

		if (webToken.getTokenType() != null) {
			dataToken.setTokenType(io.syrix.datamodel.token.TokenType.valueOf(webToken.getTokenType().name()));
		}
		dataToken.setToken(webToken.getToken());
		dataToken.setTokenExpiresAt(webToken.getTokenExpiresAt());
		dataToken.setScopes(webToken.getScopes());
		dataToken.setCreatedAt(webToken.getCreatedAt());

		return dataToken;
	}

	/**
	 * Convert data model token to web model token.
	 */
	private Token convertTokenFromDataModel(io.syrix.datamodel.token.Token dataToken) {
		Token webToken = new Token();

		if (dataToken.getTokenType() != null) {
			webToken.setTokenType(TokenType.valueOf(dataToken.getTokenType().name()));
		}
		webToken.setToken(dataToken.getToken());
		webToken.setTokenExpiresAt(dataToken.getTokenExpiresAt());
		webToken.setScopes(dataToken.getScopes());
		webToken.setCreatedAt(dataToken.getCreatedAt());

		return webToken;
	}

	/**
	 * Find connection by client ID and service type.
	 *
	 * @param clientId    The client ID to search for
	 * @param serviceType The service type to search for
	 * @return Optional connection information for the given client ID and service type
	 */
	public Optional<ConnectionInfo> findByClientIdAndServiceType(UUID clientId, ServiceType serviceType) {
		Optional<io.syrix.datamodel.ConnectionInfo> dataModelConnection = daoFactory.getConnectionInfoDao().findByServiceAndCustomerId(serviceType, clientId);

		if (dataModelConnection.isPresent()) {
			ConnectionInfo webModelConnection = convertFromDataModel(dataModelConnection.get());
			return Optional.of(webModelConnection);
		} else {
			return Optional.empty();
		}
	}
}
