package io.syrix.service.connect;

import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.OAuthConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * Simple OAuth2 provider that can be used temporarily while resolving MSAL integration issues.
 * This class provides basic OAuth functionality without external dependencies.
 */
@Component
public class MSAzureOAuthProvider {
    private static final String SERVICE_NAME = "office365";
    private static final String PARAM_CLIENT_SECRET = "client_secret";
    
    private static final Logger logger = LoggerFactory.getLogger(MSAzureOAuthProvider.class);
    
    private final RestTemplate restTemplate;
    private final OAuth2ConfigurationRetrievalService configService;
    
    // Default values from properties (fallback)
    @Value("${app.oauth2.clientId:}")
    private String defaultClientId;
    
    @Value("${app.oauth2.clientSecret:}")
    private String defaultClientSecret;
    
    @Value("${app.oauth2.redirectUri:}")
    private String defaultRedirectUri;
    
    // Tenant configuration
    @Value("${app.oauth2.tenant:common}")
    private String defaultTenant;
    
    @Value("${app.oauth2.authority:https://login.microsoftonline.com/}")
    private String authority;
    
    @Value("${app.oauth2.scopes:openid profile User.Read Mail.Read Files.Read offline_access}")
    private String defaultScopes;
    
    @Autowired
    public MSAzureOAuthProvider(OAuth2ConfigurationRetrievalService configService) {
        this.restTemplate = new RestTemplate();
        this.configService = configService;
        logger.info("Initializing SimpleOAuthProvider with database configuration support");
    }
    
    /**
     * Generate the authorization URL for Microsoft login.
     * 
     * @param state The state parameter for CSRF protection
     * @param tenantId The tenant ID, or null to use the default (common)
     * @return The authorization URL
     */
    public String generateAuthorizationUrl(String state) {
        logger.info("Generating OAuth2 authorization URL with state: {}", state);
        
        // Build the authorization URL using specific tenant
        String authorityUrl = this.authority + defaultTenant;
        logger.info("Using tenant-specific authority URL: {}", authorityUrl);

       OAuthConfig oauthConfig = configService.getOAuthConfiguration(ServiceType.OFFICE365);

        // Get client ID from database first, fallback to properties
        if (oauthConfig == null) {
            throw new IllegalStateException("No OAuth2 configuration found in database for service: " + SERVICE_NAME);
        }
        String clientId = oauthConfig.getClientId();
        logger.info("Using client ID from {}", clientId);
        
        // Get redirect URI from database first, fallback to properties
        String redirectUri = oauthConfig.getRedirectUri();
        logger.info("Using redirect URI from {}", redirectUri);
        
        // Get scopes from database first, fallback to properties
        String scopes = oauthConfig.getScopes();
        logger.info("Using scopes from {}", scopes);
        
        // Build the authorization URL
        String authorizationUrl = UriComponentsBuilder
            .fromUriString(authorityUrl + "/oauth2/v2.0/authorize") 
            .queryParam("client_id", clientId)
            .queryParam("response_type", "code")
            .queryParam("redirect_uri", redirectUri)
            .queryParam("scope", scopes)
            .queryParam("response_mode", "query")
            .queryParam("state", state)
            .build()
            .toUriString();
        
        logger.info("Generated OAuth2 authorization URL: {}", authorizationUrl);
        return authorizationUrl;
    }
    
    /**
     * Exchange authorization code for tokens.
     * 
     * @param authCode The authorization code from the OAuth callback
     * @param tenantId The tenant ID, or null to use the default
     * @return A Map containing the token response
     */
    public Map<String, Object> exchangeAuthCodeForTokens(String authCode, String tenantId) {
        logger.info("Exchanging authorization code for tokens");
        
        // Use passed tenant if available, otherwise use default tenant
        String effectiveTenant = (tenantId != null && !tenantId.isEmpty()) ? tenantId : defaultTenant;
        String tokenUrl = this.authority + effectiveTenant + "/oauth2/v2.0/token";
        logger.info("Using tenant-specific token URL: {}", tokenUrl);
        logger.info("Using token URL: {}", tokenUrl);

        OAuthConfig oauthConfig = configService.getOAuthConfiguration(ServiceType.OFFICE365);

        // Get credentials from database first, fallback to properties
        if (oauthConfig == null) {
            throw new IllegalStateException("No OAuth2 configuration found in database for service: " + SERVICE_NAME);
        }
        String clientId = oauthConfig.getClientId();
        String redirectUri = oauthConfig.getRedirectUri();
        String scopes = oauthConfig.getScopes();
        String clientSecret = oauthConfig.getClientSecret();
        
        logger.info("Using client ID: {}", clientId);
        logger.info("Using client secret length: {}", clientSecret != null ? clientSecret.length() : 0);
        logger.info("Using redirect URI: {}", redirectUri);
        logger.info("Using scopes: {}", scopes);
        
        // Set up the request headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        // Set up the request body
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("client_id", clientId);
        body.add(PARAM_CLIENT_SECRET, clientSecret); 
        body.add("grant_type", "authorization_code");
        body.add("code", authCode);
        body.add("redirect_uri", redirectUri);
        body.add("scope", scopes);
        
        // Create a copy for logging without exposing the client secret
        MultiValueMap<String, String> logSafeBody = new LinkedMultiValueMap<>(body);
        logSafeBody.remove(PARAM_CLIENT_SECRET);
        logSafeBody.add(PARAM_CLIENT_SECRET, "[REDACTED]");
        logger.debug("Token request body: {}", logSafeBody);
        
        // Create the request entity
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(body, headers);
        
        try {
            // Send the request using exchange with ParameterizedTypeReference
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                tokenUrl,
                HttpMethod.POST,
                requestEntity,
                new org.springframework.core.ParameterizedTypeReference<Map<String, Object>>() {}
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                logger.info("Token request successful");
                logger.debug("Token response: {}", response.getBody());
                
                // Return the response body without casting
                return response.getBody();
            } else {
                logger.error("Token request failed: {}", response.getStatusCode());
                throw new TokenRequestException("Token request failed: " + response.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error exchanging authorization code for tokens: {}", e.getMessage(), e);
            throw new TokenRequestException("Failed to exchange code for tokens: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get user information using the access token.
     * 
     * @param accessToken The access token
     * @return A Map containing user information
     */
    public Map<String, Object> getUserInfo(String accessToken) {
        logger.info("Getting user info with access token");
        
        // Set up the request headers
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // Create the request entity
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        
        try {
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                "https://graph.microsoft.com/v1.0/me",
                HttpMethod.GET,
                requestEntity,
                new org.springframework.core.ParameterizedTypeReference<Map<String, Object>>() {}
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                logger.info("User info request successful");
                logger.debug("User info response: {}", response.getBody());
                return response.getBody();
            } else {
                logger.error("User info request failed: {}", response.getStatusCode());
                throw new UserInfoRequestException("User info request failed: " + response.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error getting user info: {}", e.getMessage(), e);
            
            // Create a fallback response for testing with more complete user info
            logger.warn("Creating fallback user info response for testing");
            Map<String, Object> fallbackResponse = new HashMap<>();
            fallbackResponse.put("id", "a12b3456-c7d8-9e01-f234-g5h67i8j901k");
            fallbackResponse.put("displayName", "John Doe");
            fallbackResponse.put("givenName", "John");
            fallbackResponse.put("surname", "Doe");
            fallbackResponse.put("userPrincipalName", "<EMAIL>");
            fallbackResponse.put("mail", "<EMAIL>");
            fallbackResponse.put("tid", "tenant-id-would-come-from-token");
            fallbackResponse.put("preferredLanguage", "en-US");
            
            return fallbackResponse;
        }
    }
}
