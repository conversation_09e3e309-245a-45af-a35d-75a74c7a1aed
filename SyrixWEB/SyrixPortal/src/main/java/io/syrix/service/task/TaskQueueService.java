package io.syrix.service.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.syrix.datamodel.Customer;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.task.RetrieveTask;
import io.syrix.datamodel.task.TaskStatus;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.dao.DaoFactory;
import io.syrix.dao.TaskDao;
import io.syrix.datamodel.task.TaskType;
import io.syrix.messaging.TaskMessage;


import io.syrix.model.connect.ConnectionInfo;
import io.syrix.service.client.ClientService;
import io.syrix.service.connect.ConnectionInfoService;
import org.apache.activemq.artemis.api.core.TransportConfiguration;
import org.apache.activemq.artemis.api.core.client.*;
import org.apache.activemq.artemis.core.remoting.impl.netty.NettyConnectorFactory;
import org.apache.activemq.artemis.core.remoting.impl.netty.TransportConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Service for managing task queue operations.
 * Handles creation and submission of tasks to ActiveMQ Artemis queue.
 */
@Service
public class TaskQueueService {
    private static final Logger logger = LoggerFactory.getLogger(TaskQueueService.class);
    
    private static final String BROKER_HOST = "localhost";
    private static final int BROKER_PORT = 61616;
    private static final String BROKER_USERNAME = "admin";
    private static final String BROKER_PASSWORD = "admin123";
    private static final String QUEUE_NAME = "syrix-tasks";
    
    private final DaoFactory daoFactory;
    private final ObjectMapper objectMapper;


    private final ConnectionInfoService connectionInfoService;
    private final ClientService clientService;
    
    @Autowired
    public TaskQueueService(DaoFactory daoFactory, ConnectionInfoService connectionInfoService, ClientService clientService) {
        this.daoFactory = daoFactory;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        this.connectionInfoService = connectionInfoService;
        this.clientService = clientService;
    }

    public void submitTestRetrieveTask() {
        logger.info("Submitting test retrieve task");

        TaskDao taskDao = daoFactory.getTaskDao();
        
        try {
            // Find client through DAO
            Optional<Customer> clientOpt = clientService.getClientByName("Syrix Development");
            if (clientOpt.isEmpty()) {
                throw new RuntimeException("Client 'Syrix Development' not found in database");
            }

            Customer customer = clientOpt.get();
            logger.info("Found client: {} with ID: {}", customer.getName(), customer.getId());

            Optional<ConnectionInfo> connectionOpt = connectionInfoService.findByClientIdAndServiceType(customer.getId(), ServiceType.OFFICE365);
            
            if (connectionOpt.isEmpty()) {
                throw new RuntimeException("No Office365 connection found for client: " + customer.getName());
            }
            
            ConnectionInfo connection = connectionOpt.get();
            logger.info("Found connection: {} for service: {}", connection.getId(), connection.getService());

            // Create RetrieveTask with all available service types
            RetrieveTask retrieveTask = new RetrieveTask();
            retrieveTask.setCustomerId(customer.getId());
            retrieveTask.setConnectionId(connection.getId());
            retrieveTask.setExternalTenantId(connection.getExternalTenantId());
            retrieveTask.setStatus(TaskStatus.QUEUED);
            
            // Set all available service types
            List<ConfigurationServiceType> serviceTypes = Arrays.asList(
                ConfigurationServiceType.ENTRA,
                ConfigurationServiceType.TEAMS,
                ConfigurationServiceType.SHAREPOINT,
                ConfigurationServiceType.DEFENDER,
                ConfigurationServiceType.POWER_PLATFORM,
                ConfigurationServiceType.EXCHANGE_ONLINE
            );
            retrieveTask.setServiceTypeList(serviceTypes);
            
            // Save RetrieveTask to database
            RetrieveTask savedTask = taskDao.saveTaskSubclass(retrieveTask);
            logger.info("Saved RetrieveTask with ID: {} and connectionId: {}", 
                savedTask.getId(), savedTask.getConnectionId());
            
            // Create TaskMessage using saved task data
            TaskMessage taskMessage = createTaskMessage(
                savedTask.getId(),
                savedTask.getCustomerId(),
                TaskType.RETRIEVE_CONFIGURATION
            );
            
            // Send message to queue
            sendTaskMessage(taskMessage);
            
            logger.info("Successfully submitted retrieve task: {} with connection: {}", savedTask.getId(), savedTask.getConnectionId());

            
        } catch (Exception e) {
            logger.error("Failed to submit retrieve task for client", e);
            throw new RuntimeException("Failed to submit retrieve task: " + e.getMessage(), e);
        }
    }
    
    /**
     * Create a task message with specified parameters.
     */
    private TaskMessage createTaskMessage(UUID taskId, UUID clientId, TaskType taskType) {
        logger.debug("Creating TaskMessage: {}, {}, {}", taskId, taskType, clientId);
        TaskMessage taskMessage = new TaskMessage();
        taskMessage.setTaskId(taskId);
        taskMessage.setTaskType(taskType);
        taskMessage.setCustomerId(clientId);
        
        return taskMessage;
    }
    
    /**
     * Send task message to the ActiveMQ Artemis queue.
     */
    private void sendTaskMessage(TaskMessage taskMessage) throws Exception {
        ServerLocator serverLocator = null;
        ClientSessionFactory sessionFactory = null;
        ClientSession session = null;
        ClientProducer producer = null;
        
        try {
            logger.info("Setting up ActiveMQ Artemis producer for message sending");
            
            // Create transport configuration
            Map<String, Object> connectionParams = new HashMap<>();
            connectionParams.put(TransportConstants.HOST_PROP_NAME, BROKER_HOST);
            connectionParams.put(TransportConstants.PORT_PROP_NAME, BROKER_PORT);
            
            TransportConfiguration transportConfiguration = new TransportConfiguration(
                NettyConnectorFactory.class.getName(), 
                connectionParams
            );
            
            // Create server locator and session factory
            serverLocator = ActiveMQClient.createServerLocatorWithoutHA(transportConfiguration);
            sessionFactory = serverLocator.createSessionFactory();
            session = sessionFactory.createSession(BROKER_USERNAME, BROKER_PASSWORD, false, true, true, false, 0);
            
            // Create queue if it doesn't exist
            try {
//                QueueConfiguration queueConfig = QueueConfiguration.of(QUEUE_NAME)
//                    .setAddress(QUEUE_NAME)
//                    .setRoutingType(RoutingType.ANYCAST)
//                    .setDurable(true);
//                session.createQueue(queueConfig);
                logger.debug("Queue '{}' created or already exists", QUEUE_NAME);
            } catch (Exception e) {
                logger.debug("Queue might already exist: {}", e.getMessage());
            }
            
            // Create producer
            producer = session.createProducer(QUEUE_NAME);
            
            // Serialize message to JSON
            String messageJson = objectMapper.writeValueAsString(taskMessage);
            logger.debug("Message JSON: {}", messageJson);
            
            // Create and send message
            ClientMessage message = session.createMessage(true);
            message.getBodyBuffer().writeString(messageJson);
            producer.send(message);
            
            logger.info("Sent message to queue: TaskId={}, Type={}, Client={}", 
                taskMessage.getTaskId(), taskMessage.getTaskType(), taskMessage.getCustomerId());
                
        } finally {
            // Cleanup resources
            if (producer != null) {
                try {
                    producer.close();
                } catch (Exception e) {
                    logger.warn("Error closing producer: {}", e.getMessage());
                }
            }
            if (session != null) {
                try {
                    session.close();
                } catch (Exception e) {
                    logger.warn("Error closing session: {}", e.getMessage());
                }
            }
            if (sessionFactory != null) {
                try {
                    sessionFactory.close();
                } catch (Exception e) {
                    logger.warn("Error closing session factory: {}", e.getMessage());
                }
            }
            if (serverLocator != null) {
                try {
                    serverLocator.close();
                } catch (Exception e) {
                    logger.warn("Error closing server locator: {}", e.getMessage());
                }
            }
        }
    }
}
