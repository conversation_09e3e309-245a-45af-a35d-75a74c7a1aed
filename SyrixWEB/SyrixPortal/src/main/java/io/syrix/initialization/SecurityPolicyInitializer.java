package io.syrix.initialization;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.datamodel.SysConfig;
import io.syrix.service.config.SysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * Component for initializing security policy analysis on application startup
 */
@Component
public class SecurityPolicyInitializer implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(SecurityPolicyInitializer.class);

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final SysConfigService sysConfigService;

    private final boolean updatePolicyIdsEnabled;
    private final String opaApiUrl;

    @Autowired
    public SecurityPolicyInitializer(RestTemplate restTemplate, SysConfigService sysConfigService, Environment env) {
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
        this.sysConfigService = sysConfigService;
        this.updatePolicyIdsEnabled = env.getProperty("syrix.initializaton.udpatePolicyIds", Boolean.class, false);
        this.opaApiUrl = env.getProperty("syrix.opa.url", String.class, "http://localhost:8181") +"/v1/policies";
    }

    @Override
    public void run(String... args) throws Exception {
        if (!updatePolicyIdsEnabled) {
            logger.info("Security policy analysis initialization is disabled (syrix.initializaton.udpatePolicyIds=false)");
            return;
        }
        
        logger.info("Starting security policy analysis initialization");
        
        try {
            updateSyrixPolicyIds();
        } catch (Exception e) {
            logger.error("Error during policy analysis initialization: {}", e.getMessage(), e);
        }
    }

    private void updateSyrixPolicyIds() {
        try {
            Map<String, List<String>> policies = getAllPolicies();
            if (policies.isEmpty()) {
                logger.warn("No security policies found");
                return;
            }

            Optional<SysConfig> lastConfig = sysConfigService.findLatest();
            SysConfig sysConfig = lastConfig.orElseGet(SysConfig::new);
			sysConfig.setPolicyIdsBySection(policies);
			sysConfigService.saveConfiguration(sysConfig);
		} catch (Exception e) {
            logger.error("Error updating companies: {}", e.getMessage(), e);
        }
    }

    /**
     * Retrieves Microsoft 365 security configuration data from API and extracts PolicyId grouped by service sections.
     * Each section represents a Microsoft service (aad, defender, exo, powerplatform, sharepoint, teams).
     *
     * @return map where key is service section name and value is list of PolicyId for that section
     */
    public Map<String, List<String>> getAllPolicies() {
        try {
            logger.info("Retrieving Microsoft 365 security policy data from API: {}", opaApiUrl);

            String response = restTemplate.getForObject(opaApiUrl, String.class);

            if (response == null || response.isEmpty()) {
                logger.warn("Received empty response from security policy API");
                return new HashMap<>();
            }

            logger.debug("Successfully received security policy response from API");

            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode resultNode = rootNode.get("result");

            if (resultNode == null) {
                logger.warn("Missing 'result' node in security policy response");
                return new HashMap<>();
            }

            Map<String, List<String>> policyIdsBySection = extractPolicyIdsBySection(resultNode);

            int totalPolicyIds = policyIdsBySection.values().stream()
                    .mapToInt(List::size)
                    .sum();
            logger.info("Successfully extracted {} security policies across {} Microsoft 365 services", totalPolicyIds, policyIdsBySection.size());
            return policyIdsBySection;

        } catch (Exception e) {
            logger.error("Failed to retrieve security policy data from API: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Extracts PolicyId from Microsoft 365 service sections and groups them by service name.
     * Processes sections like AAD (Azure Active Directory), Defender, Exchange Online, etc.
     *
     * @param resultNode result node containing all Microsoft 365 service sections
     * @return map where key is Microsoft service name, value is list of PolicyId for that service
     */
    private Map<String, List<String>> extractPolicyIdsBySection(JsonNode resultNode) {
        Map<String, List<String>> policyIdsBySection = new HashMap<>();
        if (resultNode != null && resultNode.isArray()) {
            for (JsonNode item : resultNode) {
                List<String> policies = new ArrayList<>();
                String name = extractGroupName(item);
                if (name == null) {
                    continue;
                }
                findAllPolicyIdStructures(item, policies);
                if (policies.isEmpty()) {
                    continue;
                }
                policyIdsBySection.put(name, policies);
            }
        }

        return policyIdsBySection;
    }

    private String extractGroupName(JsonNode node) {
        JsonNode astNode = node.get("ast");
        if (!astNode.isObject()) {
            return null;
        }

        JsonNode packageNode = astNode.get("package");
        if (!packageNode.isObject()) {
            return null;
        }

        JsonNode pathNode = packageNode.get("path");
        if (!pathNode.isArray()) {
            return null;
        }

        return pathNode.get(1).get("value").asText();

    }

    private void findAllPolicyIdStructures(JsonNode node, List<String> foundResults) {
        if (node == null) {
            return;
        }

        if (node.isArray()) {
            String policyValue = extractPolicyIdValue(node);
            if (policyValue != null) {
                foundResults.add(policyValue);
                return;
            }

            for (int i = 0; i < node.size(); i++) {
                findAllPolicyIdStructures(node.get(i), foundResults);
            }

        } else if (node.isObject()) {
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                JsonNode fieldValue = field.getValue();
                findAllPolicyIdStructures(fieldValue, foundResults);
            }
        }
    }

    private String extractPolicyIdValue(JsonNode arrayNode) {
        if (!arrayNode.isArray() || arrayNode.size() != 2) {
            return null;
        }

        JsonNode firstElement = arrayNode.get(0);
        JsonNode secondElement = arrayNode.get(1);

        // {"type":"string","value":"PolicyId"}
        if (!isPolicyIdFlag(firstElement)) {
            return null;
        }

        // {"type":"string","value":"MS.POWERPLATFORM.5.1v1"}
        if (!isPolicyIdName(secondElement)) {
            return null;
        }

        return secondElement.get("value").asText();
    }

    private boolean isPolicyIdFlag(JsonNode node) {
        if (!node.isObject()) {
            return false;
        }

        JsonNode typeNode = node.get("type");
        JsonNode valueNode = node.get("value");

        if (typeNode == null || valueNode == null) {
            return false;
        }

        return "string".equals(typeNode.asText()) && "PolicyId".equals(valueNode.asText());
    }

    private boolean isPolicyIdName(JsonNode node) {
        if (!node.isObject()) {
            return false;
        }

        JsonNode typeNode = node.get("type");
        JsonNode valueNode = node.get("value");

        if (typeNode == null || valueNode == null) {
            return false;
        }

        return "string".equals(typeNode.asText()) && valueNode.isTextual();
    }


}
