/**
 * Package containing application startup initialization components.
 * 
 * <p>This package includes components that execute automatically during
 * Spring Boot application startup and perform preliminary initialization
 * of system data and configurations.
 * 
 * <h3>Initialization Components:</h3>
 * <ul>
 *   <li>{@link io.syrix.initialization.SecurityPolicyInitializer} - 
 *       Initializes Microsoft 365 security policies by loading them from OPA API 
 *       and saving to system configuration</li>
 *   <li>{@link io.syrix.initialization.TestClientInitializer} -
 *       Creates test company data for development and testing environments</li>
 * </ul>
 * 
 * <h3>Working Principle:</h3>
 * <p>All initializers implement the {@link org.springframework.boot.CommandLineRunner} interface,
 * which ensures their automatic execution after complete Spring context initialization.
 * Each initializer can be enabled or disabled through corresponding settings
 * in the application configuration file.
 * 
 * <h3>Configuration:</h3>
 * <ul>
 *   <li>{@code syrix.initialization.updatePolicyIds} - enables/disables security policy initialization</li>
 *   <li>{@code syrix.initialization.createTestCompany} - enables/disables test company creation</li>
 * </ul>
 * 
 * <p><strong>Important:</strong> All initializers are disabled by default to prevent
 * unwanted execution in production environment. Enabling is done explicitly through
 * configuration files.
 * 
 * @since 1.0
 * <AUTHOR> Development Team
 */
package io.syrix.initialization;
