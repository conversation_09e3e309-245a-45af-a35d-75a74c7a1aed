package io.syrix.health;

import io.syrix.config.ServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * Custom health indicator for Undertow server monitoring
 */
@Component
public class UndertowHealthIndicator implements HealthIndicator {
    
    @Autowired
    private ServerConfig.ServerPortConfiguration serverConfig;
    
    @Override
    public Health health() {
        try {
            Health.Builder builder = Health.up()
                .withDetail("server", "Undertow")
                .withDetail("http.port", serverConfig.httpPort())
                .withDetail("https.port", serverConfig.httpsPort())
                .withDetail("io.threads", serverConfig.ioThreads())
                .withDetail("worker.threads", serverConfig.workerThreads())
                .withDetail("buffer.size", serverConfig.bufferSize())
                .withDetail("dual.port.enabled", serverConfig.isDualPortEnabled())
                .withDetail("server.info", serverConfig.getServerInfo())
                .withDetail("performance.info", serverConfig.getPerformanceInfo());
                
            return builder.build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .withDetail("server", "Undertow")
                .build();
        }
    }
}
