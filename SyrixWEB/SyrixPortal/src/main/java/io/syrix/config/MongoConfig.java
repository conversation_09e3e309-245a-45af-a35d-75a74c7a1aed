package io.syrix.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import io.syrix.config.mongodb.MongoDatabaseProvider;
import io.syrix.dao.CustomerDao;
import io.syrix.dao.CustomerReportDao;
import io.syrix.dao.DaoFactory;
import io.syrix.dao.impl.mongodb.MongoDaoFactory;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

/**
 * Configuration class for MongoDB and DAO factory setup.
 */
@Configuration
public class MongoConfig {
    // Add beans for DAOs to avoid dependency injection issues
    
    private static final Logger logger = LoggerFactory.getLogger(MongoConfig.class);
    
    @Value("${spring.data.mongodb.host}")
    private String host;
    
    @Value("${spring.data.mongodb.port}")
    private int port;
    
    @Value("${spring.data.mongodb.database}")
    private String database;
    
    @Value("${spring.data.mongodb.username:}")
    private String username;
    
    @Value("${spring.data.mongodb.password:}")
    private String password;
    
    @Value("${spring.data.mongodb.authentication-database:admin}")
    private String authDatabase;
    
    /**
     * Create a MongoDB client with connection settings.
     * 
     * @return MongoDB client
     */
    @Bean
    public MongoClient mongoClient() {
        logger.info("Initializing MongoDB client, connecting to {}:{}/{}", host, port, database);
        
        // Create codec registry for POJO mapping
        CodecRegistry pojoCodecRegistry = fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                fromProviders(PojoCodecProvider.builder().automatic(true).build())
        );
        
        // Build client settings
        MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                .applyToClusterSettings(builder -> 
                        builder.hosts(List.of(new ServerAddress(host, port))))
                .codecRegistry(pojoCodecRegistry);
        
        // Add credentials if provided
        if (username != null && !username.isEmpty() && password != null && !password.isEmpty()) {
            logger.info("Using authentication for MongoDB with username: {}", username);
            MongoCredential credential = MongoCredential.createCredential(
                    username, authDatabase, password.toCharArray());
            settingsBuilder.credential(credential);
        } else {
            logger.info("No authentication credentials provided for MongoDB, connecting without authentication");
        }
        
        MongoClientSettings settings = settingsBuilder.build();
        
        // Create the client
        return MongoClients.create(settings);
    }
    
    /**
     * Create a MongoDB database instance.
     * 
     * @param mongoClient MongoDB client
     * @return MongoDB database
     */
    @Bean
    public MongoDatabase mongoDatabase(MongoClient mongoClient) {
        logger.info("Creating MongoDB database: {}", database);
        return mongoClient.getDatabase(database);
    }
    
    /**
     * Create a MongoDB database provider.
     * 
     * @param mongoClient MongoDB client
     * @param mongoDatabase MongoDB database
     * @return MongoDB database provider
     */
    @Bean
    public MongoDatabaseProvider mongoDatabaseProvider(MongoClient mongoClient, MongoDatabase mongoDatabase) {
        return new MongoDatabaseProvider() {
            @Override
            public MongoDatabase getDatabase() {
                return mongoDatabase;
            }
            
            @Override
            public MongoClient getClient() {
                return mongoClient;
            }
            
            @Override
            public void close() {
                mongoClient.close();
            }
        };
    }
    
    /**
     * Create a MongoDB DAO factory.
     * 
     * @param mongoDatabaseProvider Our MongoDB database provider
     * @return DAO factory
     */
    @Bean
    @Primary
    public DaoFactory daoFactory(MongoDatabaseProvider mongoDatabaseProvider) {
        logger.info("Creating MongoDB DAO factory using MongoDaoFactory");
        
        // Create an adapter from our io.syrix.config.mongodb.MongoDatabaseProvider to io.syrix.dao.mongodb.MongoDatabaseProvider
        io.syrix.dao.mongodb.MongoDatabaseProvider adapter = new io.syrix.dao.mongodb.MongoDatabaseProvider() {
            @Override
            public MongoDatabase getDatabase() {
                return mongoDatabaseProvider.getDatabase();
            }
            
            @Override
            public MongoClient getClient() {
                return mongoDatabaseProvider.getClient();
            }
            
            @Override
            public void close() {
                mongoDatabaseProvider.close();
            }
        };
        
        return new MongoDaoFactory(adapter);
    }
    
    /**
     * Expose OAuthConfigurationDao as a bean.
     *
     * @param daoFactory DAO factory
     * @return OAuthConfigurationDao
     */
    @Bean
    public io.syrix.dao.OAuthConfigurationDao oauthConfigurationDao(DaoFactory daoFactory) {
        return daoFactory.getOAuthConfigurationDao();
    }
    
    /**
     * Provide a custom implementation of OAuth2TokenDao as a bean.
     *
     * @param daoFactory DAO factory
     * @return OAuth2TokenDao
     */
    @Bean
    @org.springframework.beans.factory.annotation.Qualifier("postgresOAuth2TokenDao")
    public io.syrix.dao.OAuth2TokenDao oauth2TokenDao(DaoFactory daoFactory) {
        logger.info("Getting MongoDB OAuth2TokenDao from DAO factory");
        return daoFactory.getOAuth2TokenDao();
    }
    
    /**
     * Expose ConnectionInfoDao as a bean.
     *
     * @param daoFactory DAO factory
     * @return ConnectionInfoDao
     */
    @Bean
    public io.syrix.dao.ConnectionInfoDao connectionInfoDao(DaoFactory daoFactory) {
        return daoFactory.getConnectionInfoDao();
    }
    
    /**
     * Expose clientDao as a bean.
     *
     * @param daoFactory DAO factory
     * @return clientDao
     */
    @Bean
    public CustomerDao clientDao(DaoFactory daoFactory) {
        return daoFactory.getCustomerDao();
    }

    /**
     * Expose clientReportDao as a bean.
     *
     * @param daoFactory DAO factory
     * @return clientReportDao
     */
    @Bean
    public CustomerReportDao clientReportDao(DaoFactory daoFactory) {
        return daoFactory.getCustomerReportDao();
    }
}
