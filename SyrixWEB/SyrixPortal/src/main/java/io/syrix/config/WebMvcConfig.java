package io.syrix.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;
import org.springframework.web.servlet.resource.ResourceResolverChain;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 1. React build static assets (CSS, JS, media) - Long-term caching
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/static/")
                .setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS).cachePublic().mustRevalidate())
                .resourceChain(true);
                
        // 2. Root level assets (favicon, manifest, etc.) - Short-term caching
        registry.addResourceHandler("/favicon.ico", "/logo*.png", "/manifest.json", "/asset-manifest.json", "/robots.txt")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.maxAge(1, TimeUnit.HOURS))
                .resourceChain(true);
        
        // 3. API and authentication routes - NO static handling
        // These are handled by controllers
        
        // 4. SPA fallback - Return index.html for all other routes (NON-API only)
        // This must be the last handler as it catches everything except API routes
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.noCache().mustRevalidate())
                .resourceChain(false)
                .addResolver(new SpaPathResourceResolver());
                
        // Debug logging for resource handling
        logger.info("WebMvcConfig: Configured resource handlers with SPA fallback");
    }
    
    /**
     * Custom resolver for Single Page Application routing
     * Returns index.html for non-API routes while preserving API endpoints
     */
    private static class SpaPathResourceResolver extends PathResourceResolver {
        
        @Override
        public Resource resolveResource(HttpServletRequest request, String requestPath, 
                                      List<? extends Resource> locations, ResourceResolverChain chain) {
            
            // Skip API paths and let controllers handle them
            if (isApiPath(requestPath)) {
                return null;
            }
            
            // Try to resolve the actual resource first
            Resource requestedResource = super.resolveResource(request, requestPath, locations, chain);
            
            // If the requested resource exists and is readable, return it
            if (requestedResource != null && requestedResource.exists() && requestedResource.isReadable()) {
                return requestedResource;
            }
            
            // For all other requests (SPA routes), return index.html
            try {
                Resource indexHtml = new ClassPathResource("/static/index.html");
                if (indexHtml.exists() && indexHtml.isReadable()) {
                    return indexHtml;
                }
            } catch (Exception e) {
                // Log error but don't fail
                System.err.println("Error loading index.html: " + e.getMessage());
            }
            
            return null;
        }
        
        @Override
        protected Resource getResource(String resourcePath, Resource location) throws IOException {
            // Skip API paths
            if (isApiPath(resourcePath)) {
                return null;
            }
            
            // Try to get the requested resource
            Resource requestedResource = location.createRelative(resourcePath);
            
            // If the requested resource exists and is readable, return it
            if (requestedResource.exists() && requestedResource.isReadable()) {
                return requestedResource;
            }
            
            // For all other paths, return index.html
            try {
                return new ClassPathResource("/static/index.html");
            } catch (Exception e) {
                return null;
            }
        }
        
        /**
         * Check if the path should be handled by a controller instead of static resources
         * Returns TRUE for paths that should go to controllers (API endpoints)
         * Returns FALSE for paths that should be handled as static resources or SPA routes
         */
        private boolean isApiPath(String path) {
            if (path == null) {
                return false;
            }
            
            // Keep original path with leading slash for accurate matching
            String normalizedPath = path.startsWith("/") ? path : "/" + path;
            
            // API endpoints that should go to controllers
            return normalizedPath.startsWith("/api/") ||
                   normalizedPath.startsWith("/actuator/") ||
                   normalizedPath.startsWith("/auth/") ||
                   normalizedPath.equals("/ping") ||
                   normalizedPath.equals("/health.html") ||
                   normalizedPath.startsWith("/test/") ||
                   normalizedPath.startsWith("/admin/");
        }
    }
}
