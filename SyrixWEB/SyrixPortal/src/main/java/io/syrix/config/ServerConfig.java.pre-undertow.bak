package io.syrix.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.apache.catalina.connector.Connector;

/**
 * Generic server configuration that works with any servlet container.
 * This configuration supports dual port setup (HTTP and HTTPS) without
 * being tied to specific container implementation like Tomcat.
 * 
 * The implementation uses Spring Boot's generic interfaces but includes
 * Tomcat-specific customization as fallback for current setup.
 * This allows easy migration to other containers (like Undertow) in the future.
 */
@Configuration
public class ServerConfig {

    @Value("${server.http.port:8080}")
    private int httpPort;

    @Value("${server.port:8443}")
    private int httpsPort;

    /**
     * Generic web server factory customizer that works with any servlet container.
     * Uses Spring Boot's generic interfaces for maximum compatibility.
     */
    @Bean
    public WebServerFactoryCustomizer<ConfigurableWebServerFactory> webServerFactoryCustomizer() {
        return factory -> {
            // Generic configuration that works with any container
            if (factory instanceof ConfigurableServletWebServerFactory servletFactory) {
                // Configure basic servlet container settings
                servletFactory.setPort(httpsPort); // Primary HTTPS port
                
                // Add container-specific dual port configuration
                configureAdditionalConnectors(servletFactory);
            }
        };
    }

    /**
     * Configure additional HTTP connector for dual port setup.
     * This method uses container-specific logic but is isolated for easy replacement.
     */
    private void configureAdditionalConnectors(ConfigurableServletWebServerFactory factory) {
        // Check if this is Tomcat (current container)
        if (factory instanceof TomcatServletWebServerFactory tomcatFactory) {
            // Tomcat-specific connector for backward compatibility
            tomcatFactory.addConnectorCustomizers(createTomcatHttpConnectorCustomizer());
        }
        // Future container support can be added here:
        // else if (factory instanceof UndertowServletWebServerFactory undertowFactory) {
        //     configureUndertowHttpListener(undertowFactory);
        // }
        // else if (factory instanceof JettyServletWebServerFactory jettyFactory) {
        //     configureJettyHttpConnector(jettyFactory);
        // }
    }

    /**
     * Tomcat-specific connector customizer for HTTP port.
     * This method is isolated to make container migration easier.
     */
    private TomcatConnectorCustomizer createTomcatHttpConnectorCustomizer() {
        return connector -> {
            // Configure additional HTTP connector
            Connector httpConnector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
            httpConnector.setPort(httpPort);
            httpConnector.setScheme("http");
            httpConnector.setSecure(false);
            httpConnector.setRedirectPort(httpsPort);
            
            // Add the HTTP connector to Tomcat
            connector.getService().addConnector(httpConnector);
        };
    }

    /**
     * Future method for Undertow HTTP listener configuration.
     * Uncomment and implement when migrating to Undertow.
     */
    /*
    private void configureUndertowHttpListener(UndertowServletWebServerFactory factory) {
        factory.addBuilderCustomizers(builder -> {
            builder.addHttpListener(httpPort, "0.0.0.0");
        });
    }
    */

    /**
     * Configuration data for server ports.
     * Useful for monitoring and health checks.
     */
    @Bean
    public ServerPortConfiguration serverPortConfiguration() {
        return new ServerPortConfiguration(httpPort, httpsPort);
    }

    /**
     * Configuration data record for server ports
     */
    public record ServerPortConfiguration(int httpPort, int httpsPort) {
        public boolean isDualPortEnabled() {
            return httpPort != httpsPort;
        }
        
        public String getHttpUrl() {
            return "http://localhost:" + httpPort;
        }
        
        public String getHttpsUrl() {
            return "https://localhost:" + httpsPort;
        }
    }
}
