package io.syrix.config;

import io.syrix.security.filter.CacheControlFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * Configuration for HTTP filters implementing cache invalidation and security best practices
 */
@Configuration
public class FilterConfig {

    /**
     * Register the cache control filter with high priority
     * This filter implements content hashing cache invalidation strategy
     */
    @Bean
    public FilterRegistrationBean<CacheControlFilter> cacheControlFilter() {
        FilterRegistrationBean<CacheControlFilter> registrationBean = new FilterRegistrationBean<>();
        
        registrationBean.setFilter(new CacheControlFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        registrationBean.setName("cacheControlFilter");
        
        return registrationBean;
    }
}
