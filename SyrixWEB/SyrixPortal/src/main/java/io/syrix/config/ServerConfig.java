package io.syrix.config;

import io.undertow.Undertow;
import io.undertow.UndertowOptions;
import io.undertow.server.HttpHandler;
import io.undertow.server.HttpServerExchange;
import io.undertow.servlet.api.DeploymentInfo;
import io.undertow.util.HttpString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.undertow.UndertowBuilderCustomizer;
import org.springframework.boot.web.embedded.undertow.UndertowDeploymentInfoCustomizer;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Enhanced server configuration with optimized Undertow setup
 */
@Configuration
public class ServerConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ServerConfig.class);
    
    @Value("${server.http.port:8080}")
    private int httpPort;

    @Value("${server.port:8443}")
    private int httpsPort;

    @Value("${server.undertow.max-http-post-size:10MB}")
    private String maxHttpPostSize;

    @Value("${server.undertow.buffer-size:16384}")
    private int bufferSize;

    @Value("${server.undertow.io-threads:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}")
    private int ioThreads;

    @Value("${server.undertow.worker-threads:#{T(java.lang.Runtime).getRuntime().availableProcessors() * 8}}")
    private int workerThreads;

    /**
     * Generic web server factory customizer with Undertow optimization
     */
    @Bean
    public WebServerFactoryCustomizer<ConfigurableWebServerFactory> webServerFactoryCustomizer() {
        return factory -> {
            logger.info("Configuring web server: {}", factory.getClass().getSimpleName());
            
            if (factory instanceof ConfigurableServletWebServerFactory servletFactory) {
                servletFactory.setPort(httpsPort);
                
                // Apply Undertow-specific optimizations if available
                if (factory instanceof UndertowServletWebServerFactory undertowFactory) {
                    logger.info("Applying Undertow-specific optimizations");
                    configureUndertowOptimizations(undertowFactory);
                }
                
                configureAdditionalConnectors(servletFactory);
            }
        };
    }

    /**
     * Undertow-specific performance optimizations
     */
    private void configureUndertowOptimizations(UndertowServletWebServerFactory factory) {
        // Buffer size optimization
        factory.setBufferSize(bufferSize);
        
        // Thread configuration
        factory.setIoThreads(ioThreads);
        factory.setWorkerThreads(workerThreads);
        
        // Add builder customizations
        factory.addBuilderCustomizers(undertowBuilderCustomizer());
        
        // Add deployment customizations
        factory.addDeploymentInfoCustomizers(undertowDeploymentCustomizer());
        
        logger.info("Undertow configured - IO Threads: {}, Worker Threads: {}, Buffer Size: {}", 
                   ioThreads, workerThreads, bufferSize);
    }

    /**
     * Undertow builder customizations for performance
     */
    @Bean
    public UndertowBuilderCustomizer undertowBuilderCustomizer() {
        return builder -> {
            // Add HTTP listener for dual-port support
            builder.addHttpListener(httpPort, "0.0.0.0");
            
            // Performance optimizations
            builder.setServerOption(UndertowOptions.ENABLE_HTTP2, true);
            builder.setServerOption(UndertowOptions.HTTP2_SETTINGS_ENABLE_PUSH, true);
            builder.setServerOption(UndertowOptions.NO_REQUEST_TIMEOUT, 60000);
            
            // Security options
            builder.setServerOption(UndertowOptions.DECODE_URL, true);
            builder.setServerOption(UndertowOptions.URL_CHARSET, "UTF-8");
            
            // Connection management
            builder.setServerOption(UndertowOptions.ALWAYS_SET_KEEP_ALIVE, false);
            builder.setServerOption(UndertowOptions.MAX_ENTITY_SIZE, parseSize(maxHttpPostSize));
            
            logger.info("Undertow builder configured with HTTP/2 support and optimizations");
        };
    }

    /**
     * Undertow deployment customizations
     */
    @Bean
    public UndertowDeploymentInfoCustomizer undertowDeploymentCustomizer() {
        return deploymentInfo -> {
            // Session management
            deploymentInfo.setDefaultSessionTimeout(30 * 60); // 30 minutes
            
            // Error page handling
            deploymentInfo.setDefaultEncoding("UTF-8");
            
            // Security headers (can be overridden by Spring Security)
            deploymentInfo.addInitialHandlerChainWrapper(handler -> {
                return new SecurityHeadersHandler(handler);
            });
            
            logger.info("Undertow deployment configured with security and session settings");
        };
    }

    /**
     * Configure additional HTTP connector for dual-port setup
     */
    private void configureAdditionalConnectors(ConfigurableServletWebServerFactory factory) {
        if (factory instanceof UndertowServletWebServerFactory undertowFactory) {
            logger.info("Configuring Undertow dual-port setup: HTTP={}, HTTPS={}", httpPort, httpsPort);
            // HTTP listener already added in builder customizer
        } else {
            logger.warn("Non-Undertow container detected: {}", factory.getClass().getSimpleName());
        }
    }

    /**
     * Custom handler for security headers
     */
    private static class SecurityHeadersHandler implements HttpHandler {
        private final HttpHandler next;

        public SecurityHeadersHandler(HttpHandler next) {
            this.next = next;
        }

        @Override
        public void handleRequest(HttpServerExchange exchange) throws Exception {
            // Add security headers if not already present
            if (!exchange.getResponseHeaders().contains(HttpString.tryFromString("X-Content-Type-Options"))) {
                exchange.getResponseHeaders().put(HttpString.tryFromString("X-Content-Type-Options"), "nosniff");
            }
            if (!exchange.getResponseHeaders().contains(HttpString.tryFromString("X-Frame-Options"))) {
                exchange.getResponseHeaders().put(HttpString.tryFromString("X-Frame-Options"), "DENY");
            }
            if (!exchange.getResponseHeaders().contains(HttpString.tryFromString("X-XSS-Protection"))) {
                exchange.getResponseHeaders().put(HttpString.tryFromString("X-XSS-Protection"), "1; mode=block");
            }
            
            next.handleRequest(exchange);
        }
    }

    /**
     * Parse size string (like "10MB") to bytes
     */
    private long parseSize(String size) {
        if (size == null || size.trim().isEmpty()) {
            return 10 * 1024 * 1024; // Default 10MB
        }
        
        String trimmed = size.trim().toUpperCase();
        try {
            if (trimmed.endsWith("KB")) {
                return Long.parseLong(trimmed.substring(0, trimmed.length() - 2)) * 1024;
            } else if (trimmed.endsWith("MB")) {
                return Long.parseLong(trimmed.substring(0, trimmed.length() - 2)) * 1024 * 1024;
            } else if (trimmed.endsWith("GB")) {
                return Long.parseLong(trimmed.substring(0, trimmed.length() - 2)) * 1024 * 1024 * 1024;
            } else {
                return Long.parseLong(trimmed);
            }
        } catch (NumberFormatException e) {
            logger.warn("Could not parse size '{}', using default 10MB", size);
            return 10 * 1024 * 1024;
        }
    }

    /**
     * Server configuration information
     */
    @Bean
    public ServerPortConfiguration serverPortConfiguration() {
        return new ServerPortConfiguration(httpPort, httpsPort, ioThreads, workerThreads, bufferSize);
    }

    /**
     * Enhanced configuration record with Undertow-specific info
     */
    public record ServerPortConfiguration(
            int httpPort, 
            int httpsPort, 
            int ioThreads, 
            int workerThreads, 
            int bufferSize) {
        
        public boolean isDualPortEnabled() {
            return httpPort != httpsPort;
        }
        
        public String getServerInfo() {
            return String.format(
                "Undertow - HTTP: %d, HTTPS: %d, IO Threads: %d, Worker Threads: %d, Buffer: %d bytes", 
                httpPort, httpsPort, ioThreads, workerThreads, bufferSize);
        }
        
        public String getPerformanceInfo() {
            return String.format(
                "Performance Config - CPU Cores: %d, IO Threads: %d, Worker Threads: %d, Buffer: %dKB",
                Runtime.getRuntime().availableProcessors(), ioThreads, workerThreads, bufferSize / 1024);
        }
    }
}
