package io.syrix.config.mongodb;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;

/**
 * Interface for providing MongoDB database instances.
 * This matches the io.syrix.dao.mongodb.MongoDatabaseProvider interface.
 */
public interface MongoDatabaseProvider {
    
    /**
     * Get a MongoDB database instance.
     *
     * @return The MongoDB database
     */
    MongoDatabase getDatabase();
    
    /**
     * Get the MongoDB client.
     *
     * @return The MongoDB client
     */
    MongoClient getClient();
    
    /**
     * Close the MongoDB client.
     */
    void close();
}