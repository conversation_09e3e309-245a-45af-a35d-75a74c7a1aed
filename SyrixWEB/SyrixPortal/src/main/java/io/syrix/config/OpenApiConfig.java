package io.syrix.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI 3.0 configuration for Syrix API documentation.
 * Provides comprehensive API documentation with security schemes and server information.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8443}")
    private int httpsPort;

    @Value("${server.http.port:8080}")
    private int httpPort;

    @Bean
    public OpenAPI syrixOpenAPI() {
        return new OpenAPI()
                .info(createApiInfo())
                .servers(createServers())
                .components(createComponents())
                .addSecurityItem(new SecurityRequirement().addList("JWT Authentication"));
    }

    private Info createApiInfo() {
        return new Info()
                .title("Syrix Security Dashboard API")
                .description("Comprehensive API for Syrix security dashboard operations, " +
                           "including security metrics, alerts, audit logs, and system monitoring.")
                .version("1.0.0")
                .contact(new Contact()
                        .name("Syrix Development Team")
                        .email("<EMAIL>")
                        .url("https://syrix.io"))
                .license(new License()
                        .name("Proprietary")
                        .url("https://syrix.io/license"));
    }

    private List<Server> createServers() {
        return List.of(
                new Server()
                        .url("https://localhost:" + httpsPort)
                        .description("HTTPS Server (Production)"),
                new Server()
                        .url("http://localhost:" + httpPort)
                        .description("HTTP Server (Development)"),
                new Server()
                        .url("https://api.syrix.io")
                        .description("Production Server")
        );
    }

    private Components createComponents() {
        return new Components()
                .addSecuritySchemes("JWT Authentication",
                        new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT token for API authentication"))
                .addSecuritySchemes("OAuth2",
                        new SecurityScheme()
                                .type(SecurityScheme.Type.OAUTH2)
                                .description("OAuth2 authentication for Microsoft 365 integration"));
    }
}
