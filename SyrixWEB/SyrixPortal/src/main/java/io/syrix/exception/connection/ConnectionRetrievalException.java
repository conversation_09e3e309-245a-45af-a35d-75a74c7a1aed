package io.syrix.exception.connection;

/**
 * Exception thrown when a connection retrieval operation fails.
 */
public class ConnectionRetrievalException extends ConnectionServiceException {

    /**
     * Constructs a new ConnectionRetrievalException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionRetrievalException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionRetrievalException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionRetrievalException(String message, Throwable cause) {
        super(message, cause);
    }
}
