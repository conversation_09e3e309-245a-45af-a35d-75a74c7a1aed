package io.syrix.exception.connection;

/**
 * Exception thrown when a connection cannot be found.
 */
public class ConnectionNotFoundException extends ConnectionServiceException {

    /**
     * Constructs a new ConnectionNotFoundException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionNotFoundException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionNotFoundException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
