package io.syrix.exception.connection;

/**
 * Exception thrown when a connection cannot be created.
 */
public class ConnectionCreationException extends ConnectionServiceException {

    /**
     * Constructs a new ConnectionCreationException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionCreationException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionCreationException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionCreationException(String message, Throwable cause) {
        super(message, cause);
    }
}
