package io.syrix.exception.connection;

/**
 * Base exception for all connection service related exceptions.
 */
public class ConnectionServiceException extends RuntimeException {

    /**
     * Constructs a new ConnectionServiceException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionServiceException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionServiceException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new ConnectionServiceException with the specified cause.
     *
     * @param cause the cause
     */
    public ConnectionServiceException(Throwable cause) {
        super(cause);
    }
}
