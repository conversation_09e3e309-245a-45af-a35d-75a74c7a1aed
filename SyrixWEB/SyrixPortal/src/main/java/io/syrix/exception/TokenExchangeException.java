package io.syrix.exception;

/**
 * Custom exception class to handle errors during the token exchange process.
 */
public class TokenExchangeException extends RuntimeException {
    
    /**
     * Constructor with message.
     * 
     * @param message The error message
     */
    public TokenExchangeException(String message) {
        super(message);
    }

    /**
     * Constructor with message and cause.
     * 
     * @param message The error message
     * @param cause The cause of the error
     */
    public TokenExchangeException(String message, Throwable cause) {
        super(message, cause);
    }
}