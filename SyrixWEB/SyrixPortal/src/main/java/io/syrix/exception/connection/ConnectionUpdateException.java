package io.syrix.exception.connection;

/**
 * Exception thrown when a connection cannot be updated.
 */
public class ConnectionUpdateException extends ConnectionServiceException {

    /**
     * Constructs a new ConnectionUpdateException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionUpdateException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionUpdateException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}
