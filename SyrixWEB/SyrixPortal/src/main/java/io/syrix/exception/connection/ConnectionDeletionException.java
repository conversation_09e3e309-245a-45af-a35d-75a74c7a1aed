package io.syrix.exception.connection;

/**
 * Exception thrown when a connection cannot be deleted.
 */
public class ConnectionDeletionException extends ConnectionServiceException {

    /**
     * Constructs a new ConnectionDeletionException with the specified message.
     *
     * @param message the detail message
     */
    public ConnectionDeletionException(String message) {
        super(message);
    }

    /**
     * Constructs a new ConnectionDeletionException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public ConnectionDeletionException(String message, Throwable cause) {
        super(message, cause);
    }
}
