package io.syrix.controller.admin;

import io.syrix.datamodel.Customer;
import io.syrix.service.client.ClientService;
import io.syrix.service.task.TaskQueueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Admin REST controller for company operations using the syrixdao component.
 * This provides admin-specific endpoints for company management.
 */
@RestController
@RequestMapping("/api/admin/companies")
public class AdminClientController {
    private static final Logger logger = LoggerFactory.getLogger(AdminClientController.class);

    private final ClientService clientService;
    private final TaskQueueService taskQueueService;

    @Autowired
    public AdminClientController(ClientService clientService, TaskQueueService taskQueueService) {
        this.clientService = clientService;
        this.taskQueueService = taskQueueService;
    }

    /**
     * Admin endpoint to get all companies.
     *
     * @return List of all companies
     */
    @GetMapping
    public ResponseEntity<List<Customer>> getAllClients() {
        try {
            logger.info("Admin: Getting all companies");
            return ResponseEntity.ok(clientService.getAllClients());
        } catch (Exception e) {
            logger.error("Admin: Error getting all companies", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve companies", e);
        }
    }

    /**
     * Admin endpoint to get all active companies.
     *
     * @return List of active companies
     */
    @GetMapping("/active")
    public ResponseEntity<List<Customer>> getAllActiveClients() {
        try {
            logger.info("Admin: Getting active companies");
            return ResponseEntity.ok(clientService.getAllActiveClients());
        } catch (Exception e) {
            logger.error("Admin: Error getting active companies", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve active companies", e);
        }
    }

    /**
     * Admin endpoint to get a company by ID.
     *
     * @param id Company ID
     * @return The company if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<Customer> getClientById(@PathVariable("id") UUID id) {
        try {
            logger.info("Admin: Getting company by ID: {}", id);
            return clientService.getClientById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Admin: Error getting company by ID: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve company", e);
        }
    }

    /**
     * Admin endpoint to get a company by Microsoft tenant ID.
     *
     * @param tenantId Microsoft tenant ID
     * @return The company if found
     */
    @GetMapping("/tenant/{tenantId}")
    public ResponseEntity<Customer> getClientByTenantId(@PathVariable("tenantId") String tenantId) {
        try {
            logger.info("Admin: Getting company by tenant ID: {}", tenantId);
            return clientService.getClientByTenantId(tenantId)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found for this tenant ID"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Admin: Error getting company by tenant ID: {}", tenantId, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve company by tenant ID", e);
        }
    }

    /**
     * Admin endpoint to create a new company.
     *
     * @param customer Company to create
     * @return Created company
     */
    @PostMapping
    public ResponseEntity<Customer> createClient(@RequestBody Customer customer) {
        try {
            logger.info("Admin: Creating new company: {}", customer.getName());
            
            // Set creation time and ensure status is set
            customer.setCreatedAt(LocalDateTime.now());
            customer.setUpdatedAt(LocalDateTime.now());
            if (customer.getStatus() == null) {
                customer.setStatus("active");
            }
            
            Customer createdCustomer = clientService.createClient(customer);
            logger.info("Admin: Successfully created company with ID: {}", createdCustomer.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCustomer);
        } catch (Exception e) {
            logger.error("Admin: Error creating company", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create company", e);
        }
    }

    /**
     * Admin endpoint to update an existing company.
     *
     * @param id Company ID
     * @param customer Updated company data
     * @return Updated company
     */
    @PutMapping("/{id}")
    public ResponseEntity<Customer> updatedClient(@PathVariable("id") UUID id, @RequestBody Customer customer) {
        try {
            logger.info("Admin: Updating company: {}", id);
            
            // Check if company exists
            if (clientService.getClientById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
            }
            
            // Set ID and update time
            customer.setId(id);
            customer.setUpdatedAt(LocalDateTime.now());
            
            Customer updatedCustomer = clientService.updateClient(customer);
            logger.info("Admin: Successfully updated company: {}", id);
            return ResponseEntity.ok(updatedCustomer);
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Admin: Error updating company: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update company", e);
        }
    }

    /**
     * Admin endpoint to delete a company.
     *
     * @param id Company ID
     * @return No content response
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteClient(@PathVariable("id") UUID id) {
        try {
            logger.info("Admin: Deleting company: {}", id);
            
            // Check if company exists
            if (clientService.getClientById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
            }
            
            clientService.deleteClient(id);
            logger.info("Admin: Successfully deleted company: {}", id);
            return ResponseEntity.noContent().build();
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Admin: Error deleting company: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete company", e);
        }
    }

    @PostMapping("/send-message")
    public ResponseEntity<Boolean> sendMessage() {
        try {
            logger.info("Admin: Send message to ActiveMQ");
            taskQueueService.submitTestRetrieveTask();
            return ResponseEntity.status(HttpStatus.OK).body(true);
        } catch (Exception e) {
            logger.error("Admin: Error creating company", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create company", e);
        }
    }
}
