package io.syrix.controller;

import io.syrix.dao.DaoFactory;
import io.syrix.datamodel.ConnectionStatus;
import io.syrix.datamodel.ServiceType;
import io.syrix.datamodel.oauth.MSALOAuthConfig;
import io.syrix.datamodel.oauth.OAuthConfig;
import io.syrix.service.connect.OAuth2ConfigurationRetrievalService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for OAuth2 configuration operations using the syrixdao component.
 */
@RestController
@RequestMapping("/api/oauth2/configurations")
public class OAuth2ConfigurationController {
    private static final Logger logger = LoggerFactory.getLogger(OAuth2ConfigurationController.class);

    private final OAuth2ConfigurationRetrievalService oauthConfigurationService;
    private final DaoFactory daoFactory;

    @Autowired
    public OAuth2ConfigurationController(OAuth2ConfigurationRetrievalService oauthConfigurationService, DaoFactory daoFactory) {
        this.oauthConfigurationService = oauthConfigurationService;
        this.daoFactory = daoFactory;
    }

    /**
     * Get all OAuth2 configurations.
     *
     * @return List of all OAuth2 configurations
     */
    @GetMapping
    public ResponseEntity<List<OAuthConfig>> getAllConfigurations() {
        try {
            return ResponseEntity.ok(daoFactory.getOAuthConfigurationDao().getAllConfigurations());
        } catch (Exception e) {
            logger.error("Error getting all OAuth2 configurations", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve OAuth2 configurations", e);
        }
    }

    /**
     * Get active OAuth2 configurations.
     *
     * @return List of active OAuth2 configurations
     */
    @GetMapping("/active")
    public ResponseEntity<List<OAuthConfig>> getActiveConfigurations() {
        try {
            return ResponseEntity.ok(daoFactory.getOAuthConfigurationDao().findByStatus(ConnectionStatus.ACTIVE));
        } catch (Exception e) {
            logger.error("Error getting active OAuth2 configurations", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve active OAuth2 configurations", e);
        }
    }

    /**
     * Get OAuth2 configuration by ID.
     *
     * @param id Configuration ID
     * @return The configuration if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<OAuthConfig> getConfigurationById(@PathVariable("id") UUID id) {
        try {
            return daoFactory.getOAuthConfigurationDao().findById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "OAuth2 configuration not found"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getting OAuth2 configuration by ID: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve OAuth2 configuration", e);
        }
    }

    /**
     * Get Microsoft (MSAL) OAuth2 configuration.
     *
     * @return The MSAL configuration if found
     */
    @GetMapping("/microsoft")
    public ResponseEntity<MSALOAuthConfig> getMSALConfiguration() {
        try {
            // Get the configuration directly from the OAuth service
            Optional<OAuthConfig> config = daoFactory.getOAuthConfigurationDao().getConfigurationByService(ServiceType.OFFICE365);
            
            return config
                .filter(c -> c instanceof MSALOAuthConfig)
                .map(c -> (MSALOAuthConfig) c)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Microsoft OAuth2 configuration not found"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getting Microsoft OAuth2 configuration", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve Microsoft OAuth2 configuration", e);
        }
    }

    /**
     * Create a new OAuth2 configuration.
     *
     * @param config Configuration to create
     * @return Created configuration
     */
    @PostMapping
    public ResponseEntity<OAuthConfig> createConfiguration(@RequestBody OAuthConfig config) {
        try {
            OAuthConfig createdConfig = daoFactory.getOAuthConfigurationDao().save(config);
            // Clear the configuration cache to make sure the service picks up the new configuration
            oauthConfigurationService.clearConfigCache();
            return ResponseEntity.status(HttpStatus.CREATED).body(createdConfig);
        } catch (Exception e) {
            logger.error("Error creating OAuth2 configuration", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create OAuth2 configuration", e);
        }
    }

    /**
     * Update an existing OAuth2 configuration.
     *
     * @param id Configuration ID
     * @param config Updated configuration data
     * @return Updated configuration
     */
    @PutMapping("/{id}")
    public ResponseEntity<OAuthConfig> updateConfiguration(@PathVariable("id") UUID id, @RequestBody OAuthConfig config) {
        try {
            // Check if configuration exists
            if (!daoFactory.getOAuthConfigurationDao().findById(id).isPresent()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "OAuth2 configuration not found");
            }
            
            // Set ID
            config.setId(id.toString());
            
            OAuthConfig updatedConfig = daoFactory.getOAuthConfigurationDao().save(config);
            // Clear the configuration cache to ensure the service picks up the changes
            oauthConfigurationService.clearConfigCache();
            return ResponseEntity.ok(updatedConfig);
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating OAuth2 configuration: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update OAuth2 configuration", e);
        }
    }

    /**
     * Delete an OAuth2 configuration.
     *
     * @param id Configuration ID
     * @return No content response
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteConfiguration(@PathVariable("id") UUID id) {
        try {
            // Check if configuration exists
            if (!daoFactory.getOAuthConfigurationDao().findById(id).isPresent()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "OAuth2 configuration not found");
            }
            
            daoFactory.getOAuthConfigurationDao().deleteById(id);
            // Clear the configuration cache to ensure the service is aware of the deletion
            oauthConfigurationService.clearConfigCache();
            return ResponseEntity.noContent().build();
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting OAuth2 configuration: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete OAuth2 configuration", e);
        }
    }
}
