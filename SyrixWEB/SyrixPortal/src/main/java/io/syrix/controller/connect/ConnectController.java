package io.syrix.controller.connect;

import io.syrix.model.connect.OAuth2InitRequest;
import io.syrix.model.connect.OAuth2InitResponse;
import io.syrix.service.connect.OAuthService;

import io.syrix.datamodel.ConnectionInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Controller for handling connection-related functionality,
 * including OAuth2 flows for Office 365 and other services.
 */
@RestController
@RequestMapping("/api/connect")
public class ConnectController {

    private static final Logger logger = LoggerFactory.getLogger(ConnectController.class);

    private final OAuthService oauthService;

    @Autowired
    public ConnectController(OAuthService oauthService) {
        this.oauthService = oauthService;
    }

    /**
     * Initialize the OAuth2 flow for a service.
     * 
     * @param request Contains service name, tenantId, and appId
     * @return Response containing authUrl for browser redirect
     */
    @PostMapping("/oauth2/init")
    public ResponseEntity<OAuth2InitResponse> initiateOAuth2Flow(@RequestBody OAuth2InitRequest request) {
        logger.info("=== CONNECT CONTROLLER: /api/connect/oauth2/init ENDPOINT CALLED ===");
        logger.info("Initializing OAuth2 flow for service: {}, companyId: {}", 
            request.getService(), request.getCompanyId());
        
        OAuth2InitResponse response = oauthService.initializeMSOAuth2Flow(request);

        String authUrl = response.getAuthUrl(); // added conditional assignment
        logger.info("Generated authorization URL (partial): {}...", 
            authUrl != null ? authUrl.substring(0, Math.min(50, authUrl.length())) : "null");
        logger.info("State parameter: {}", response.getState());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Handle the OAuth2 callback from the provider.
     * 
     * @param params Map containing code and state parameters
     * @return Connection information after successful authentication
     */
    @PostMapping("/oauth2/callback")
    public ResponseEntity<ConnectionInfo> handleOAuth2Callback(
            @RequestBody Map<String, String> params,
            HttpServletRequest request) {
        
        logger.info("=== CONNECT CONTROLLER: /api/connect/oauth2/callback ENDPOINT CALLED ===");
        logger.info("Full request URL: {}", request.getRequestURL());
        logger.info("Request method: {}", request.getMethod());
        
        // Log all headers for debugging
        logger.info("Request headers:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (logger.isInfoEnabled()) {
                String headerValue = request.getHeader(headerName);
                logger.info("  {}: {}", headerName, headerValue);
            }
        }
        
        logger.info("Request body parameters:");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String value = entry.getKey().equals("code") 
                ? entry.getKey() + " (truncated): " + entry.getValue().substring(0, Math.min(20, entry.getValue().length())) + "..."
                : entry.getKey() + ": " + entry.getValue();
            
            logger.info("  {}", value);
        }
        
        String code = params.get("code");
        String state = params.get("state");
        
        if (code == null || state == null) {
            logger.error("Missing required parameters: code={}, state={}", code != null, state != null);
            throw new IllegalArgumentException("Code and state parameters are required");
        }
        
        logger.info("Received OAuth2 callback with state: {}", state);
        // Invoke method(s) only conditionally.
        if (logger.isInfoEnabled()) {
            logger.info("Received authorization code (partial): {}...", code.substring(0, Math.min(10, code.length())));
        }
        
        try {
            ConnectionInfo connectionInfo = oauthService.completeMSOAuth2Flow(code, state);
            logger.info("OAuth2 flow completed successfully");
            return ResponseEntity.ok(connectionInfo);
        } catch (Exception e) {
            logger.error("Error completing OAuth2 flow: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(new ConnectionInfo());
        }
    }

    /**
     * Get all connections for the current tenant.
     * 
     * @return List of connections
     */
    @GetMapping("/connections")
    public ResponseEntity<List<ConnectionInfo>> getConnections() {
        logger.info("Getting all connections");
        List<ConnectionInfo> connections = oauthService.getConnections();
        logger.info("Retrieved {} connections", connections.size());
        return ResponseEntity.ok(connections);
    }

    /**
     * Delete a connection by ID.
     * 
     * @param id Connection ID to delete
     * @return No content response on success
     */
    @DeleteMapping("/connections/{id}")
    public ResponseEntity<Void> deleteConnection(@PathVariable UUID id) {
        logger.info("Deleting connection with ID: {}", id);
        oauthService.deleteConnection(id);
        logger.info("Connection deleted successfully");
        return ResponseEntity.noContent().build();
    }
}
