package io.syrix.controller.base;

import io.syrix.model.response.ApiResponse;
import io.syrix.model.response.PagedResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Base controller providing common functionality for all API controllers.
 * Includes standardized response handling, logging, and error management.
 */
@RestController
public abstract class BaseApiController {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    /**
     * Create a successful API response with data
     * @param data The response data
     * @return ResponseEntity with ApiResponse wrapper
     */
    protected <T> ResponseEntity<ApiResponse<T>> success(T data) {
        return ResponseEntity.ok(ApiResponse.success(data));
    }
    
    /**
     * Create a successful API response with data and custom message
     * @param data The response data
     * @param message Custom success message
     * @return ResponseEntity with ApiResponse wrapper
     */
    protected <T> ResponseEntity<ApiResponse<T>> success(T data, String message) {
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }
    
    /**
     * Create a paginated successful response
     * @param items The list of items
     * @param page Current page number
     * @param size Page size
     * @param totalElements Total number of elements
     * @return ResponseEntity with paginated ApiResponse
     */
    protected <T> ResponseEntity<ApiResponse<PagedResponse<T>>> successPaged(
            List<T> items, int page, int size, long totalElements) {
        PagedResponse<T> pagedResponse = new PagedResponse<>(items, page, size, totalElements);
        return ResponseEntity.ok(ApiResponse.success(pagedResponse));
    }
    
    /**
     * Create an error response with default status 400 (Bad Request)
     * @param message Error message
     * @return ResponseEntity with error ApiResponse
     */
    protected <T> ResponseEntity<ApiResponse<T>> error(String message) {
        return error(message, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * Create an error response with custom status
     * @param message Error message
     * @param status HTTP status
     * @return ResponseEntity with error ApiResponse
     */
    protected <T> ResponseEntity<ApiResponse<T>> error(String message, HttpStatus status) {
        return ResponseEntity.status(status)
                .body(ApiResponse.error(message));
    }
    
    /**
     * Create a not found error response
     * @param resource The resource that was not found
     * @return ResponseEntity with 404 error
     */
    protected <T> ResponseEntity<ApiResponse<T>> notFound(String resource) {
        return error(resource + " not found", HttpStatus.NOT_FOUND);
    }
    
    /**
     * Create an unauthorized error response
     * @param message Error message
     * @return ResponseEntity with 401 error
     */
    protected <T> ResponseEntity<ApiResponse<T>> unauthorized(String message) {
        return error(message, HttpStatus.UNAUTHORIZED);
    }
    
    /**
     * Create a forbidden error response
     * @param message Error message
     * @return ResponseEntity with 403 error
     */
    protected <T> ResponseEntity<ApiResponse<T>> forbidden(String message) {
        return error(message, HttpStatus.FORBIDDEN);
    }
    
    /**
     * Create an internal server error response
     * @param message Error message
     * @return ResponseEntity with 500 error
     */
    protected <T> ResponseEntity<ApiResponse<T>> internalError(String message) {
        return error(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * Log and create error response for exceptions
     * @param ex The exception
     * @param message Custom error message
     * @return ResponseEntity with error response
     */
    protected <T> ResponseEntity<ApiResponse<T>> handleException(Exception ex, String message) {
        logger.error("API error: {}", message, ex);
        return internalError(message);
    }
    
    /**
     * Generate a unique request ID for tracking
     * @return UUID string
     */
    protected String generateRequestId() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * Get current timestamp as string
     * @return ISO instant string
     */
    protected String getCurrentTimestamp() {
        return Instant.now().toString();
    }
}
