package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.SystemLogEntry;
import io.syrix.model.SystemLogResponse;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.SystemLogService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * System Log API Controller for version 1.0
 * Handles system logging, filtering, and export functionality
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class SystemLogV1Controller extends BaseApiController {

    private final SystemLogService systemLogService;

    public SystemLogV1Controller(SystemLogService systemLogService) {
        this.systemLogService = systemLogService;
    }

    @GetMapping("/systemlog")
    public ResponseEntity<ApiResponse<SystemLogResponse>> getSystemLogs(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) List<String> activities,
            @RequestParam(required = false) List<String> statuses,
            @RequestParam(required = false) List<String> users,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) @Max(100) int pageSize) {
        logger.info("System logs requested");
        
        try {
            SystemLogResponse response = systemLogService.getSystemLogs(
                    search, startDate, endDate, activities, statuses, users, page, pageSize);
            return success(response);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve system logs");
        }
    }

    @GetMapping("/systemlog/{id}")
    public ResponseEntity<ApiResponse<SystemLogEntry>> getLogById(@PathVariable String id) {
        logger.info("System log requested for id={}", id);
        
        try {
            SystemLogEntry log = systemLogService.getLogById(id);
            if (log == null) {
                return notFound("System log entry");
            }
            return success(log);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve system log entry");
        }
    }

    @GetMapping("/systemlog/activities")
    public ResponseEntity<ApiResponse<List<String>>> getUniqueActivities() {
        logger.info("Unique activities requested");
        
        try {
            List<String> activities = systemLogService.getUniqueActivities();
            return success(activities);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve unique activities");
        }
    }

    @GetMapping("/systemlog/statuses")
    public ResponseEntity<ApiResponse<List<String>>> getUniqueStatuses() {
        logger.info("Unique statuses requested");
        
        try {
            List<String> statuses = systemLogService.getUniqueStatuses();
            return success(statuses);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve unique statuses");
        }
    }

    @GetMapping("/systemlog/users")
    public ResponseEntity<ApiResponse<List<SystemLogEntry.User>>> getUniqueUsers() {
        logger.info("Unique users requested");
        
        try {
            List<SystemLogEntry.User> users = systemLogService.getUniqueUsers();
            return success(users);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve unique users");
        }
    }

    @GetMapping("/systemlog/export")
    public ResponseEntity<String> exportLogsToCSV() {
        logger.info("System log export requested");
        
        try {
            List<SystemLogEntry> logs = systemLogService.getAllLogs();
            String csvContent = systemLogService.exportLogsToCSV(logs);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=system_logs.csv");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            logger.error("Failed to export system logs", e);
            return ResponseEntity.internalServerError()
                    .body("Failed to export system logs: " + e.getMessage());
        }
    }
}
