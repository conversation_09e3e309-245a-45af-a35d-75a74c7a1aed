package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.AuditLogs;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.AuditLogService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Audit Log API Controller for version 1.0
 * Handles audit logging and compliance tracking
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class AuditV1Controller extends BaseApiController {

    private final AuditLogService auditLogService;

    public AuditV1Controller(AuditLogService auditLogService) {
        this.auditLogService = auditLogService;
    }

    @GetMapping("/audit/logs")
    public ResponseEntity<ApiResponse<AuditLogs>> getAuditLogs(
            @RequestParam(value = "count", defaultValue = "10") @Min(1) @Max(100) int count,
            @RequestParam(value = "page", defaultValue = "1") @Min(1) int page) {
        logger.info("Audit logs requested with count={} and page={}", count, page);
        
        try {
            AuditLogs auditLogs = auditLogService.getAuditLogs(count);
            return success(auditLogs);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve audit logs");
        }
    }
}
