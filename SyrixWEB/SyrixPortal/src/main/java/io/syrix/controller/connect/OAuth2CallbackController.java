package io.syrix.controller.connect;

import io.syrix.datamodel.Customer;
import io.syrix.service.connect.OAuthService;
import io.syrix.service.client.ClientService;

import io.syrix.datamodel.ConnectionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.view.RedirectView;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Enumeration;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Controller for handling the OAuth2 callback from Microsoft.
 * This controller handles the browser redirect callback from the OAuth2 provider.
 */
@Controller
public class OAuth2CallbackController {
    
    private static final Logger logger = LoggerFactory.getLogger(OAuth2CallbackController.class);
    
    private final OAuthService oauthService;
    private final ClientService clientService;
    
    @Autowired
    public OAuth2CallbackController(OAuthService oauthService, ClientService clientService) {
        this.oauthService = oauthService;
        this.clientService = clientService;
    }
    
    /**
     * Handles the OAuth2 callback from the provider.
     * 
     * @param code The authorization code from the OAuth2 provider
     * @param state The state parameter for CSRF protection
     * @return Redirect to the main application page
     */
    @GetMapping("/auth/callback")
    public RedirectView handleCallback(
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request) {
        
        logger.info("Full request URL: {}", request.getRequestURL().toString() + (request.getQueryString() != null ? "?" + request.getQueryString() : ""));
        
        // Log all headers for debugging
        logger.info("Request headers:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            logger.info("  {}: {}", headerName, request.getHeader(headerName));
        }
        
        logger.info("Request parameters:");
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = paramName.equals("code") 
                ? (paramName + " (truncated): " + code.substring(0, Math.min(20, code.length())) + "...")
                : paramName + ": " + request.getParameter(paramName);
            logger.info("  {}", paramValue);
        }
        
        logger.info("Received OAuth2 callback with state: {}", state);
        logger.info("Received authorization code (partial): {}...", code.substring(0, Math.min(10, code.length())));
        
        try {
            // Process the authorization code
            logger.info("Initiating OAuth2 code exchange");
            ConnectionInfo connectionInfo = oauthService.completeMSOAuth2Flow(code, state);
            logger.info("Successfully connected to service: {}", connectionInfo.getService());
            logger.info("Connection info: displayName={}, externalTenantId={}", connectionInfo.getDisplayName(), connectionInfo.getExternalTenantId());
            
            // Extract and store company information from the connection
            String tenantId = connectionInfo.getExternalTenantId();
            if (tenantId != null && !tenantId.isEmpty()) {
                // Check if company with this tenant ID already exists
                if (!clientService.existsByTenantId(tenantId)) {
                    // Create a new company with the tenant ID
                    Customer customer = new Customer();
                    customer.setName(connectionInfo.getDisplayName() != null ?
                        connectionInfo.getDisplayName() : "Company from " + tenantId);
                    customer.setDisplayName(connectionInfo.getDisplayName());
                    customer.setMicrosoftTenantId(tenantId);
                    customer.setStatus("active");
                    
                    logger.info("Creating new company from OAuth2 callback with tenantId: {}", tenantId);
                    Customer createdCustomer = clientService.createClient(customer);
                    logger.info("Created new company from OAuth2 callback: {}", createdCustomer.getId());
                } else {
                    logger.info("Company with tenant ID {} already exists", tenantId);
                }
            } else {
                logger.warn("No tenant ID found in OAuth2 callback connection");
            }
            
            // Redirect to the main application
            logger.info("OAuth2 flow completed successfully, redirecting to homepage");
            return new RedirectView("/?oauth2Success=true");
        } catch (Exception e) {
            logger.error("Error processing OAuth2 callback: {}", e.getMessage(), e);
            
            // Provide more specific error information for better troubleshooting
            String errorType = "auth_failed";
            String errorDescription = e.getMessage();
            
            // Handle MSAL-specific errors
            if (e.getCause() != null && e.getCause().getMessage() != null) {
                String causeMessage = e.getCause().getMessage().toLowerCase();
                
                if (causeMessage.contains("invalid_client")) {
                    errorType = "invalid_client";
                    errorDescription = "Client ID or secret may be incorrect";
                } else if (causeMessage.contains("invalid_grant")) {
                    errorType = "invalid_grant";
                    errorDescription = "Authorization code is invalid or expired";
                } else if (causeMessage.contains("unauthorized_client")) {
                    errorType = "unauthorized_client";
                    errorDescription = "The client is not authorized to request tokens";
                } else if (causeMessage.contains("invalid_request")) {
                    errorType = "invalid_request";
                    errorDescription = "The request is missing a required parameter";
                }
            } else if (e instanceof IllegalArgumentException) {
                errorType = "invalid_state";
                errorDescription = "State parameter mismatch or missing state data";
            } else if (e instanceof IllegalStateException) {
                if (e.getMessage() != null && e.getMessage().contains("token")) {
                    errorType = "token_error";
                    errorDescription = "Error obtaining access token";
                } else if (e.getMessage() != null && e.getMessage().contains("user info")) {
                    errorType = "user_info_error";
                    errorDescription = "Error obtaining user information";
                }
            } else if (e.getMessage() != null && e.getMessage().contains("database")) {
                errorType = "database_error";
                errorDescription = "Database operation failed";
            }
            
            // Redirect to an error page with the specific error type
            return new RedirectView("/?error=" + errorType + "&error_description=" + errorDescription);
        }
    }
}
