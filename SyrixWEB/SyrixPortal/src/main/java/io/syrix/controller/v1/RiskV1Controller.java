package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.Risk;
import io.syrix.model.RiskOverTime;
import io.syrix.model.TopRiskUser;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.SecurityService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Risk API Controller for version 1.0
 * Handles risk analysis and trending
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class RiskV1Controller extends BaseApiController {

    private final SecurityService securityService;

    public RiskV1Controller(SecurityService securityService) {
        this.securityService = securityService;
    }

    @GetMapping("/top-risks")
    public ResponseEntity<ApiResponse<List<Risk>>> getTopRisks() {
        logger.info("Top risks requested");
        try {
            List<Risk> risks = securityService.getRisks();
            return success(risks);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve top risks");
        }
    }

    @GetMapping("/top-risk-users")
    public ResponseEntity<ApiResponse<List<TopRiskUser>>> getTopRiskUsers() {
        logger.info("Top risk users requested");
        try {
            List<TopRiskUser> users = securityService.getTopRiskUsers();
            return success(users);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve top risk users");
        }
    }

    @GetMapping("/risk-over-time")
    public ResponseEntity<ApiResponse<List<RiskOverTime>>> getRiskOverTime(
            @RequestParam(required = false, defaultValue = "year") String timeRange) {
        logger.info("Risk over time requested with timeRange={}", timeRange);
        
        try {
            List<RiskOverTime> riskData = switch (timeRange.toLowerCase()) {
                case "week" -> securityService.getRiskOverTimeWeekly();
                case "month" -> securityService.getRiskOverTimeMonthly();
                case "year" -> securityService.getRiskOverTime();
                default -> securityService.getRiskOverTime();
            };
            return success(riskData);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve risk over time data");
        }
    }
}
