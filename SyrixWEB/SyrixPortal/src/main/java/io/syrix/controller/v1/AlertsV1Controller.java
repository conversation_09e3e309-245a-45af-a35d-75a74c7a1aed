package io.syrix.controller.v1;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import jakarta.servlet.http.HttpSession;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.Alert;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.AlertsService;

/**
 * Alerts API Controller for version 1.0
 * Handles alert management and CRUD operations
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class AlertsV1Controller extends BaseApiController {

    private final AlertsService alertsService;

    public AlertsV1Controller(AlertsService alertsService) {
        this.alertsService = alertsService;
    }

    @GetMapping("/alerts")
    public ResponseEntity<ApiResponse<List<Alert>>> getAllAlerts(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String category,
            HttpSession session) {
        logger.info("Alerts requested with status={}, category={}", status, category);
        System.out.println("🔥 CONTROLLER HIT: " + Thread.currentThread().getName());
        try {
            // Get company ID from session
            UUID clientId = (UUID) session.getAttribute("companyId");
            if (clientId == null) {
                logger.error("No company ID found in session - authentication required");
                return error("Authentication required - no company ID in session", HttpStatus.UNAUTHORIZED);
            }
            
            logger.info("Using company ID from session: {}", clientId);

            List<Alert> alerts;
            if (status != null || category != null) {
                alerts = alertsService.getFilteredAlerts(clientId, status, category);
            } else {
                alerts = alertsService.getAllAlerts(clientId);
            }

            logger.info("Returning {} alerts for company {}", alerts.size(), clientId);
            return success(alerts);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve alerts");
        }
    }

    @GetMapping("/alerts/{id}")
    public ResponseEntity<ApiResponse<Alert>> getAlertById(@PathVariable String id) {
        logger.info("Alert requested for id={}", id);
        try {
            Alert alert = alertsService.getAlertById(id);
            return success(alert);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve alert");
        }
    }



    @PutMapping("/alerts/{id}/status")
    public ResponseEntity<ApiResponse<Alert>> updateAlertStatus(
            @PathVariable String id,
            @RequestBody Map<String, String> statusUpdate) {
        String status = statusUpdate.get("status");
        logger.info("Alert status update requested for id={}, status={}", id, status);
        
        try {
            Alert updatedAlert = alertsService.updateAlertStatus(id, status);
            return success(updatedAlert);
        } catch (Exception e) {
            return handleException(e, "Failed to update alert status");
        }
    }
}
