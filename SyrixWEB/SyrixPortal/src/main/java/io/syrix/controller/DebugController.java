package io.syrix.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Debug controller to help troubleshoot static resource issues
 */
@RestController
@RequestMapping("/api/debug")
public class DebugController {

    @GetMapping("/static-resources")
    public ResponseEntity<Map<String, Object>> checkStaticResources() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Check if index.html exists and get its content preview
            ClassPathResource indexResource = new ClassPathResource("/static/index.html");
            result.put("indexHtmlExists", indexResource.exists());
            result.put("indexHtmlReadable", indexResource.isReadable());
            
            if (indexResource.exists() && indexResource.isReadable()) {
                String content = new String(indexResource.getInputStream().readAllBytes());
                // Extract CSS and JS references from the HTML
                String[] lines = content.split("\n");
                for (String line : lines) {
                    if (line.contains(".css") || line.contains(".js")) {
                        result.put("htmlLine", line.trim());
                        break;
                    }
                }
                result.put("indexHtmlLength", content.length());
            }
            
            // Check if static directory exists
            ClassPathResource staticDir = new ClassPathResource("/static/static/");
            result.put("staticDirExists", staticDir.exists());
            
            // List CSS files
            try {
                ClassPathResource cssDir = new ClassPathResource("/static/static/css/");
                if (cssDir.exists()) {
                    Path cssPath = Paths.get(cssDir.getURI());
                    if (Files.exists(cssPath)) {
                        result.put("cssFiles", Files.list(cssPath)
                                .map(p -> p.getFileName().toString())
                                .collect(Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                result.put("cssFilesError", e.getMessage());
            }
            
            // List JS files
            try {
                ClassPathResource jsDir = new ClassPathResource("/static/static/js/");
                if (jsDir.exists()) {
                    Path jsPath = Paths.get(jsDir.getURI());
                    if (Files.exists(jsPath)) {
                        result.put("jsFiles", Files.list(jsPath)
                                .map(p -> p.getFileName().toString())
                                .collect(Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                result.put("jsFilesError", e.getMessage());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    @GetMapping("/app-info")
    public ResponseEntity<Map<String, Object>> getAppInfo() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("timestamp", System.currentTimeMillis());
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("springProfilesActive", System.getProperty("spring.profiles.active"));
        info.put("workingDirectory", System.getProperty("user.dir"));
        info.put("classPath", System.getProperty("java.class.path"));
        
        return ResponseEntity.ok(info);
    }
}
