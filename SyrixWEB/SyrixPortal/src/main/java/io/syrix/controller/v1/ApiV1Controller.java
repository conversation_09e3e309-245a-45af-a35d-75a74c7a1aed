package io.syrix.controller.v1;

/**
 * DEPRECATED: Legacy Unified API Controller for version 1.0
 * 
 * This controller has been refactored into domain-specific controllers:
 * - DashboardV1Controller (/api/v1/dashboard, /api/v1/user-profile, etc.)
 * - AlertsV1Controller (/api/v1/alerts, /api/v1/alerts/{id}, etc.) 
 * - SystemLogV1Controller (/api/v1/systemlog, etc.)
 * - AuditV1Controller (/api/v1/audit/logs)
 * - RiskV1Controller (/api/v1/top-risks, /api/v1/risk-over-time, etc.)
 * - ScanV1Controller (/api/v1/next-scan, /api/v1/scan-status, etc.)
 * - NotificationsV1Controller (/api/v1/notifications, etc.)
 * - SystemV1Controller (/api/v1/health, /api/v1/status, /api/v1/test)
 * 
 * This class has been completely removed to prevent endpoint conflicts.
 * All functionality has been moved to the domain-specific controllers listed above.
 * 
 * Original endpoints (28 total) now distributed across 8 focused controllers:
 * 
 * MOVED TO DashboardV1Controller:
 * - GET /api/v1/dashboard
 * - GET /api/v1/user-profile  
 * - GET /api/v1/security-metrics
 * - GET /api/v1/status-cards
 * - GET /api/v1/test-status-items
 * - GET /api/v1/failed-findings
 * 
 * MOVED TO AlertsV1Controller:
 * - GET /api/v1/alerts
 * - GET /api/v1/alerts/{id}
 * - PUT /api/v1/alerts/{id}/status
 * 
 * MOVED TO SystemLogV1Controller:
 * - GET /api/v1/systemlog
 * - GET /api/v1/systemlog/{id}
 * - GET /api/v1/systemlog/activities
 * - GET /api/v1/systemlog/statuses
 * - GET /api/v1/systemlog/users
 * - GET /api/v1/systemlog/export
 * 
 * MOVED TO AuditV1Controller:
 * - GET /api/v1/audit/logs
 * 
 * MOVED TO RiskV1Controller:
 * - GET /api/v1/top-risks
 * - GET /api/v1/top-risk-users
 * - GET /api/v1/risk-over-time
 * 
 * MOVED TO ScanV1Controller:
 * - GET /api/v1/next-scan-info
 * - GET /api/v1/next-scan
 * - GET /api/v1/scan-status
 * 
 * MOVED TO NotificationsV1Controller:
 * - GET /api/v1/notifications
 * - GET /api/v1/notifications-service
 * 
 * MOVED TO SystemV1Controller:
 * - GET /api/v1/health
 * - GET /api/v1/status
 * - GET /api/v1/test
 * - POST /api/v1/test
 */
@Deprecated
public class ApiV1Controller {
    // This class is deprecated and empty.
    // All endpoints have been moved to domain-specific controllers.
    // This file can be safely deleted once the new controllers are verified working.
}
