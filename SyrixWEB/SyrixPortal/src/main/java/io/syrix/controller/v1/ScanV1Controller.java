package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.NextScanInfo;
import io.syrix.model.ScanStatusResponse;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.ScanService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Scan API Controller for version 1.0
 * Handles security scanning operations and status
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class ScanV1Controller extends BaseApiController {

    private final ScanService scanService;

    public ScanV1Controller(ScanService scanService) {
        this.scanService = scanService;
    }

    @GetMapping("/next-scan-info")
    public ResponseEntity<ApiResponse<NextScanInfo>> getNextScanInfo() {
        logger.info("Next scan info requested");
        try {
            NextScanInfo scanInfo = scanService.getNextScanInfo();
            return success(scanInfo);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve next scan information");
        }
    }
    
    @GetMapping("/next-scan")
    public ResponseEntity<ApiResponse<NextScanInfo>> getNextScan() {
        logger.info("Next scan requested (alias for next-scan-info)");
        try {
            NextScanInfo scanInfo = scanService.getNextScanInfo();
            return success(scanInfo);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve next scan information");
        }
    }

    @GetMapping("/scan-status")
    public ResponseEntity<ApiResponse<ScanStatusResponse>> getScanStatus() {
        logger.info("Scan status requested");
        try {
            ScanStatusResponse status = scanService.getCurrentScanStatus();
            return success(status);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve scan status");
        }
    }
}
