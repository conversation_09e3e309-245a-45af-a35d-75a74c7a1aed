package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.response.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * System API Controller for version 1.0
 * Handles core system operations and health checks
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class SystemV1Controller extends BaseApiController {

    // Store application start time for uptime calculation
    private static final long startTime = System.currentTimeMillis();

    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        logger.info("Health check requested");
        
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "UP");
        healthData.put("timestamp", getCurrentTimestamp());
        healthData.put("uptime", System.currentTimeMillis() - startTime);
        healthData.put("version", "1.0.0");
        
        return success(healthData);
    }

    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getApiStatus() {
        logger.info("API status requested");
        
        Map<String, Object> status = new HashMap<>();
        status.put("status", "operational");
        status.put("apiVersion", "1.0.0");
        status.put("timestamp", System.currentTimeMillis());
        status.put("endpoints", "active");
        
        return success(status);
    }

    @GetMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testEndpointGet() {
        logger.info("GET test endpoint called");
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("status", "success");
        testData.put("method", "GET");
        testData.put("message", "API test endpoint is working");
        testData.put("timestamp", System.currentTimeMillis());
        
        return success(testData);
    }

    @PostMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testEndpointPost(
            @RequestBody(required = false) Map<String, Object> requestBody) {
        logger.info("POST test endpoint called");
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("status", "success");
        testData.put("method", "POST");
        testData.put("message", "API test endpoint is working");
        testData.put("timestamp", System.currentTimeMillis());
        if (requestBody != null) {
            testData.put("receivedData", requestBody);
        }
        
        return success(testData);
    }

    /**
     * Health check endpoint (migrated from HealthCheckController)
     * Simple health check for deployment verification
     */
    @GetMapping("/health-check")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheckDeploy() {
        logger.info("Health check deployment endpoint called");
        
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "OK");
        healthData.put("message", "Syrix backend is running");
        healthData.put("timestamp", System.currentTimeMillis());
        healthData.put("version", "1.0.0");
        
        return success(healthData);
    }

    /**
     * Deployment test endpoint (migrated from HealthCheckController)
     * Comprehensive deployment verification
     */
    @GetMapping("/deployment-test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deploymentTest() {
        logger.info("Deployment test endpoint called");
        
        Map<String, Object> deploymentData = new HashMap<>();
        deploymentData.put("backend", "operational");
        deploymentData.put("staticResources", "should be served by WebMvcConfig");
        deploymentData.put("authentication", "bypassed for testing");
        deploymentData.put("database", "configured for MongoDB");
        deploymentData.put("instructions", "If you see this, the backend is working. Check browser console for frontend errors.");
        deploymentData.put("timestamp", System.currentTimeMillis());
        
        return success(deploymentData);
    }
}
