package io.syrix.controller;

import io.syrix.exception.connection.ConnectionCreationException;
import io.syrix.exception.connection.ConnectionDeletionException;
import io.syrix.exception.connection.ConnectionNotFoundException;
import io.syrix.exception.connection.ConnectionRetrievalException;
import io.syrix.exception.connection.ConnectionServiceException;
import io.syrix.exception.connection.ConnectionUpdateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class ApiExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(ApiExceptionHandler.class);

    @ExceptionHandler(ConnectionNotFoundException.class)
    public ResponseEntity<Map<String, String>> handleConnectionNotFoundException(ConnectionNotFoundException e) {
        logger.error("Connection not found", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Connection not found");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }
    
    @ExceptionHandler(ConnectionCreationException.class)
    public ResponseEntity<Map<String, String>> handleConnectionCreationException(ConnectionCreationException e) {
        logger.error("Failed to create connection", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to create connection");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }
    
    @ExceptionHandler(ConnectionUpdateException.class)
    public ResponseEntity<Map<String, String>> handleConnectionUpdateException(ConnectionUpdateException e) {
        logger.error("Failed to update connection", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to update connection");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }
    
    @ExceptionHandler(ConnectionDeletionException.class)
    public ResponseEntity<Map<String, String>> handleConnectionDeletionException(ConnectionDeletionException e) {
        logger.error("Failed to delete connection", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to delete connection");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }
    
    @ExceptionHandler(ConnectionRetrievalException.class)
    public ResponseEntity<Map<String, String>> handleConnectionRetrievalException(ConnectionRetrievalException e) {
        logger.error("Failed to retrieve connection(s)", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to retrieve connection(s)");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    @ExceptionHandler(ConnectionServiceException.class)
    public ResponseEntity<Map<String, String>> handleConnectionServiceException(ConnectionServiceException e) {
        logger.error("Connection service error", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Connection service error");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleException(Exception e) {
        logger.error("Unhandled exception occurred", e);
        
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "An unexpected error occurred");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
