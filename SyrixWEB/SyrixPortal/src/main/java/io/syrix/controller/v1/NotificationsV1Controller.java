package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.NotificationData;
import io.syrix.model.response.ApiResponse;
import io.syrix.model.response.PagedResponse;
import io.syrix.service.NotificationsService;
import io.syrix.service.SecurityService;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Notifications API Controller for version 1.0
 * Handles notification management and delivery
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class NotificationsV1Controller extends BaseApiController {

    private final SecurityService securityService;
    private final NotificationsService notificationsService;

    public NotificationsV1Controller(SecurityService securityService, NotificationsService notificationsService) {
        this.securityService = securityService;
        this.notificationsService = notificationsService;
    }

    @GetMapping("/notifications")
    public ResponseEntity<ApiResponse<PagedResponse<NotificationData>>> getDashboardNotifications(
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "7") @Min(1) @Max(100) int size) {
        logger.info("Notifications requested with page={} and size={}", page, size);
        
        try {
            List<NotificationData> allNotifications = securityService.getNotifications();
            long totalElements = allNotifications.size();
            
            // Apply pagination
            int fromIndex = page * size;
            int toIndex = Math.min(fromIndex + size, allNotifications.size());
            
            List<NotificationData> pagedNotifications = fromIndex < allNotifications.size() 
                ? allNotifications.subList(fromIndex, toIndex) 
                : List.of();
            
            return successPaged(pagedNotifications, page, size, totalElements);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve notifications");
        }
    }

    @GetMapping("/notifications-service")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getNotificationsService(
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "5") @Min(1) @Max(100) int pageSize,
            HttpSession session) {
        logger.info("Notifications service requested with page={} and pageSize={}", page, pageSize);

        try {
            // Get company ID from session
            UUID companyId = (UUID) session.getAttribute("companyId");
            if (companyId == null) {
                logger.error("No company ID found in session - authentication required");
                return error("Authentication required - no company ID in session", HttpStatus.UNAUTHORIZED);
            }
            
            logger.info("Using company ID from session: {}", companyId);
            Map<String, Object> notifications = notificationsService.getNotifications(companyId, page, pageSize);
            return success(notifications);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve notifications from service");
        }
    }
}
