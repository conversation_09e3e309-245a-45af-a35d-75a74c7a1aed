package io.syrix.controller;

import io.syrix.service.ConnectionService;
import io.syrix.datamodel.ConnectionInfo;
import io.syrix.datamodel.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static io.syrix.datamodel.ConnectionStatus.ACTIVE;

/**
 * REST controller for connection operations using the syrixdao component.
 */
@RestController
@RequestMapping("/api/connections")
public class ConnectionController {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionController.class);

    private final ConnectionService connectionService;

    @Autowired
    public ConnectionController(ConnectionService connectionService) {
        this.connectionService = connectionService;
    }

    /**
     * Get all connections.
     *
     * @return List of all connections
     */
    @GetMapping
    public ResponseEntity<List<ConnectionInfo>> getAllConnections() {
        try {
            return ResponseEntity.ok(connectionService.getAllConnections());
        } catch (Exception e) {
            logger.error("Error getting all connections", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve connections", e);
        }
    }

    /**
     * Get a connection by ID.
     *
     * @param id Connection ID
     * @return The connection if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<ConnectionInfo> getConnectionById(@PathVariable("id") UUID id) {
        try {
            return connectionService.getConnectionById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Connection not found"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getting connection by ID: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve connection", e);
        }
    }

    /**
     * Get connections by service type.
     *
     * @param serviceType Service type
     * @return List of connections for the service type
     */
    @GetMapping("/service/{serviceType}")
    public ResponseEntity<List<ConnectionInfo>> getConnectionsByServiceType(@PathVariable("serviceType") String serviceType) {
        try {
            ServiceType type = ServiceType.valueOf(serviceType.toUpperCase());
            return ResponseEntity.ok(connectionService.getConnectionsByServiceType(type));
        } catch (IllegalArgumentException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid service type: " + serviceType);
        } catch (Exception e) {
            logger.error("Error getting connections by service type: {}", serviceType, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve connections by service type", e);
        }
    }

    /**
     * Get connections by company ID.
     *
     * @param companyId Company ID
     * @return List of connections for the company
     */
    @GetMapping("/company/{companyId}")
    public ResponseEntity<List<ConnectionInfo>> getConnectionsByCompany(@PathVariable("companyId") UUID companyId) {
        try {
            return ResponseEntity.ok(connectionService.getConnectionsByCompany(companyId));
        } catch (Exception e) {
            logger.error("Error getting connections by company ID: {}", companyId, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve connections by company", e);
        }
    }

    /**
     * Get active connections by company ID.
     *
     * @param companyId Company ID
     * @return List of active connections for the company
     */
    @GetMapping("/company/{companyId}/active")
    public ResponseEntity<List<ConnectionInfo>> getActiveConnectionsByCompany(@PathVariable("companyId") UUID companyId) {
        try {
            return ResponseEntity.ok(connectionService.getActiveConnectionsByCompany(companyId));
        } catch (Exception e) {
            logger.error("Error getting active connections by company ID: {}", companyId, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve active connections by company", e);
        }
    }

    /**
     * Create a new connection.
     *
     * @param connectionInfo Connection to create
     * @return Created connection
     */
    @PostMapping
    public ResponseEntity<ConnectionInfo> createConnection(@RequestBody ConnectionInfo connectionInfo) {
        try {
            // Set creation time and update time
            connectionInfo.setCreatedAt(LocalDateTime.now());
            connectionInfo.setUpdatedAt(LocalDateTime.now());
            if (connectionInfo.getStatus() == null) {
                connectionInfo.setStatus(ACTIVE);
            }
            
            ConnectionInfo createdConnection = connectionService.createConnection(connectionInfo);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdConnection);
        } catch (Exception e) {
            logger.error("Error creating connection", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create connection", e);
        }
    }

    /**
     * Update an existing connection.
     *
     * @param id Connection ID
     * @param connectionInfo Updated connection data
     * @return Updated connection
     */
    @PutMapping("/{id}")
    public ResponseEntity<ConnectionInfo> updateConnection(@PathVariable("id") UUID id, @RequestBody ConnectionInfo connectionInfo) {
        try {
            // Check if connection exists
            if (connectionService.getConnectionById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Connection not found");
            }
            
            // Set ID and update time
            connectionInfo.setId(id);
            connectionInfo.setUpdatedAt(LocalDateTime.now());
            
            ConnectionInfo updatedConnection = connectionService.updateConnection(connectionInfo);
            return ResponseEntity.ok(updatedConnection);
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating connection: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update connection", e);
        }
    }

    /**
     * Delete a connection.
     *
     * @param id Connection ID
     * @return No content response
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteConnection(@PathVariable("id") UUID id) {
        try {
            // Check if connection exists
            if (connectionService.getConnectionById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Connection not found");
            }
            
            connectionService.deleteConnection(id);
            return ResponseEntity.noContent().build();
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting connection: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete connection", e);
        }
    }
}
