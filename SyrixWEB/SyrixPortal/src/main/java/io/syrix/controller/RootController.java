package io.syrix.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;

import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@CrossOrigin(origins = "*")
public class RootController {
    private static final Logger logger = LoggerFactory.getLogger(RootController.class);

    // Change from "/" to "/api/status" to avoid conflicts with the root path
    @GetMapping("/api/root")
    public ResponseEntity<Map<String, String>> root() {
        logger.info("Root endpoint accessed");
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Syrix API is running");
        response.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/ping")
    public ResponseEntity<String> ping() {
        logger.info("Ping endpoint accessed");
        return ResponseEntity.ok("pong");
    }
}
