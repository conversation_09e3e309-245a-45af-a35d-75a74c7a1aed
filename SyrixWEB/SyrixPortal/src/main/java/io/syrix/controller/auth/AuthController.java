package io.syrix.controller.auth;

import io.syrix.model.auth.AuthResponse;
import io.syrix.model.auth.RegisterRequest;

import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.UUID;

/**
 * Authentication controller for user registration and login operations
 * This is a stub implementation that logs registration data without persisting it
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    /**
     * Register a new user account
     * This is a stub implementation that only logs the registration data
     */
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@RequestBody RegisterRequest request) {
        logger.info("=== NEW USER REGISTRATION ATTEMPT ===");
        logger.info("Name: {}", request.getName());
        logger.info("Email: {}", request.getEmail());
        logger.info("Company: {}", request.getCompanyName());
        logger.info("Industry: {}", request.getIndustry());
        logger.info("Employees: {}", request.getEmployees());
        logger.info("Password: [REDACTED - {} characters]", 
                   request.getPassword() != null ? request.getPassword().length() : 0);
        logger.info("=====================================");
        
        try {
            // Validate required fields
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                logger.warn("Registration failed: Name is required");
                return ResponseEntity.badRequest()
                    .body(new AuthResponse(false, "Name is required"));
            }
            
            if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
                logger.warn("Registration failed: Email is required");
                return ResponseEntity.badRequest()
                    .body(new AuthResponse(false, "Email is required"));
            }
            
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                logger.warn("Registration failed: Password is required");
                return ResponseEntity.badRequest()
                    .body(new AuthResponse(false, "Password is required"));
            }
            
            if (request.getCompanyName() == null || request.getCompanyName().trim().isEmpty()) {
                logger.warn("Registration failed: Company name is required");
                return ResponseEntity.badRequest()
                    .body(new AuthResponse(false, "Company name is required"));
            }
            
            // Simulate successful registration
            String mockUserId = UUID.randomUUID().toString();
            logger.info("Registration successful! Generated user ID: {}", mockUserId);
            
            // Return success response
            AuthResponse response = new AuthResponse(
                true, 
                "Registration successful! Please check your email to verify your account.",
                mockUserId,
                null // No token for registration, will be provided after email verification
            );
            
            logger.info("Returning success response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Registration failed with exception: ", e);
            return ResponseEntity.internalServerError()
                .body(new AuthResponse(false, "Registration failed due to server error"));
        }
    }
    
    /**
     * Create session with company ID (temporary for development)
     */
    @PostMapping("/session")
    public ResponseEntity<Map<String, Object>> createSession(
            @RequestBody Map<String, String> request,
            HttpSession session) {
        
        logger.info("Session creation request received");
        
        try {
            String companyId = request.get("companyId");
            if (companyId == null || companyId.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("success", false, "message", "Company ID is required"));
            }
            
            UUID companyUuid = UUID.fromString(companyId);
            session.setAttribute("companyId", companyUuid);
            
            logger.info("Session created successfully for company: {}", companyUuid);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Session created"
            ));
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid companyId format: {}", request.get("companyId"));
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", "Invalid company ID format"));
        } catch (Exception e) {
            logger.error("Session creation failed", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("success", false, "message", "Session creation failed"));
        }
    }
    
    /**
     * Health check endpoint for the auth service
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        logger.info("Auth service health check requested");
        return ResponseEntity.ok("Auth service is running");
    }
}
