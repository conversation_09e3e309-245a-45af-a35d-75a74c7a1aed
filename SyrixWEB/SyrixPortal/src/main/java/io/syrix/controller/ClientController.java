package io.syrix.controller;

import io.syrix.datamodel.Customer;
import io.syrix.service.client.ClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * REST controller for client operations using the syrixdao component.
 */
@RestController
@RequestMapping("/api/companies")
public class ClientController {
    private static final Logger logger = LoggerFactory.getLogger(ClientController.class);

    private final ClientService clientService;

    @Autowired
    public ClientController(ClientService clientService) {
        this.clientService = clientService;
    }

    /**
     * Get all companies.
     *
     * @return List of all companies
     */
    @GetMapping
    public ResponseEntity<List<Customer>> getAllCompanies() {
        try {
            return ResponseEntity.ok(clientService.getAllClients());
        } catch (Exception e) {
            logger.error("Error getting all companies", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve companies", e);
        }
    }

    /**
     * Get all active companies.
     *
     * @return List of active companies
     */
    @GetMapping("/active")
    public ResponseEntity<List<Customer>> getActiveCompanies() {
        try {
            return ResponseEntity.ok(clientService.getAllActiveClients());
        } catch (Exception e) {
            logger.error("Error getting active companies", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve active companies", e);
        }
    }

    /**
     * Get a client by ID.
     *
     * @param id client ID
     * @return The client if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<Customer> getClientById(@PathVariable("id") UUID id) {
        try {
            return clientService.getClientById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "client not found"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getting client by ID: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve client", e);
        }
    }

    /**
     * Get a client by Microsoft tenant ID.
     *
     * @param tenantId Microsoft tenant ID
     * @return The client if found
     */
    @GetMapping("/tenant/{tenantId}")
    public ResponseEntity<Customer> getClientByTenantId(@PathVariable("tenantId") String tenantId) {
        try {
            return clientService.getClientByTenantId(tenantId)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "client not found for this tenant ID"));
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getting client by tenant ID: {}", tenantId, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve client by tenant ID", e);
        }
    }

    /**
     * Create a new client.
     *
     * @param customer client to create
     * @return Created client
     */
    @PostMapping
    public ResponseEntity<Customer> createClient(@RequestBody Customer customer) {
        try {
            // Set creation time and ensure status is set
            customer.setCreatedAt(LocalDateTime.now());
            customer.setUpdatedAt(LocalDateTime.now());
            if (customer.getStatus() == null) {
                customer.setStatus("active");
            }
            
            Customer createdCustomer = clientService.createClient(customer);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCustomer);
        } catch (Exception e) {
            logger.error("Error creating client", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create client", e);
        }
    }

    /**
     * Update an existing client.
     *
     * @param id client ID
     * @param customer Updated client data
     * @return Updated client
     */
    @PutMapping("/{id}")
    public ResponseEntity<Customer> updateClient(@PathVariable("id") UUID id, @RequestBody Customer customer) {
        try {
            // Check if client exists
            if (clientService.getClientById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "client not found");
            }
            
            // Set ID and update time
            customer.setId(id);
            customer.setUpdatedAt(LocalDateTime.now());
            
            Customer updatedCustomer = clientService.updateClient(customer);
            return ResponseEntity.ok(updatedCustomer);
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating client: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update client", e);
        }
    }

    /**
     * Delete a client.
     *
     * @param id client ID
     * @return No content response
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteClient(@PathVariable("id") UUID id) {
        try {
            // Check if client exists
            if (clientService.getClientById(id).isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "client not found");
            }
            
            clientService.deleteClient(id);
            return ResponseEntity.noContent().build();
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting client: {}", id, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete client", e);
        }
    }
}
