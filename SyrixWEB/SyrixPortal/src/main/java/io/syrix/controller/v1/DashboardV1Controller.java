package io.syrix.controller.v1;

import io.syrix.controller.base.BaseApiController;
import io.syrix.model.DashboardResponse;
import io.syrix.model.FailedFindings;
import io.syrix.model.ScanStatusData;
import io.syrix.model.SecurityMetrics;
import io.syrix.model.StatusCardData;
import io.syrix.model.TestStatusItem;
import io.syrix.model.UserProfile;
import io.syrix.model.response.ApiResponse;
import io.syrix.service.SecurityService;
import io.syrix.service.UserProfileService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Dashboard API Controller for version 1.0
 * Handles dashboard data aggregation, user profiles, and security metrics
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class DashboardV1Controller extends BaseApiController {

    private final SecurityService securityService;
    private final UserProfileService userProfileService;

    public DashboardV1Controller(SecurityService securityService, UserProfileService userProfileService) {
        this.securityService = securityService;
        this.userProfileService = userProfileService;
    }

    @GetMapping("/dashboard")
    public ResponseEntity<ApiResponse<DashboardResponse>> getDashboardData() {
        logger.info("Dashboard data requested");
        try {
            DashboardResponse response = securityService.getDashboardData();
            logger.info("Successfully processed dashboard data request");
            return success(response);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve dashboard data");
        }
    }

    @GetMapping("/user-profile")
    public ResponseEntity<ApiResponse<UserProfile>> getUserProfile() {
        logger.info("User profile requested");
        try {
            UserProfile profile = userProfileService.getCurrentUserProfile();
            return success(profile);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve user profile");
        }
    }

    @GetMapping("/security-metrics")
    public ResponseEntity<ApiResponse<List<SecurityMetrics>>> getSecurityMetrics() {
        logger.info("Security metrics requested");
        try {
            List<SecurityMetrics> metrics = securityService.getSecurityMetrics();
            return success(metrics);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve security metrics");
        }
    }

    @GetMapping("/status-cards")
    public ResponseEntity<ApiResponse<List<StatusCardData>>> getStatusCards() {
        logger.info("Status cards requested");
        try {
            List<StatusCardData> cards = securityService.getStatusCards();
            return success(cards);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve status cards");
        }
    }

    @GetMapping("/test-status-items")
    public ResponseEntity<ApiResponse<List<TestStatusItem>>> getTestStatusItems() {
        logger.info("Test status items requested");
        try {
            List<TestStatusItem> items = securityService.getTestStatusItems();
            return success(items);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve test status items");
        }
    }

    @GetMapping("/failed-findings")
    public ResponseEntity<ApiResponse<FailedFindings>> getFailedFindings() {
        logger.info("Failed findings requested");
        try {
            FailedFindings findings = securityService.getFailedFindings();
            return success(findings);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve failed findings");
        }
    }

    /**
     * Get test status data (migrated from ScanStatusController)
     * Returns scan status data with category counts
     */
    @GetMapping("/test-status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTestStatus() {
        logger.info("=== API CALL RECEIVED: /api/v1/test-status ====");
        
        try {
            // API returns only numbers - colors and icons are defined in TypeScript
            List<ScanStatusData> data = List.of(
                new ScanStatusData("critical", 100),
                new ScanStatusData("warning", 0),
                new ScanStatusData("ignored", 10),
                new ScanStatusData("ok", 110),
                new ScanStatusData("autoFixed", 100)
            );
            
            // Total should be Ok + Ignored + Warning + Critical (excluding AutoFixed)
            int total = data.stream()
                .filter(item -> !"autoFixed".equals(item.category()))
                .mapToInt(ScanStatusData::count)
                .sum();
            
            Map<String, Object> response = Map.of(
                "data", data,
                "total", total
            );
            
            logger.info("=== API RESPONSE: Returning {} categories, total={} (excluding AutoFixed) ====", data.size(), total);
            return success(response);
        } catch (Exception e) {
            return handleException(e, "Failed to retrieve test status data");
        }
    }
}
