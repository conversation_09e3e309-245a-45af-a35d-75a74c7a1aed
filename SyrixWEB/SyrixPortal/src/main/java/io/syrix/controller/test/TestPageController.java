package io.syrix.controller.test;

import io.syrix.datamodel.Customer;
import io.syrix.model.connect.ConnectionInfo;
import io.syrix.service.client.ClientService;
import io.syrix.service.client.TestClientCreator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

/**
 * Controller for test pages.
 * Provides access to test functionality via browser UI.
 */
@Controller
@RequestMapping("/test")
public class TestPageController {

    private final ClientService clientService;
    private final TestClientCreator testClientCreator;

    @Autowired
    public TestPageController(ClientService clientService, TestClientCreator testClientCreator) {
        this.clientService = clientService;
        this.testClientCreator = testClientCreator;
    }

    /**
     * Display the test page with company management form.
     */
    @GetMapping
    public String testPage(Model model) {
        // Add a new company form bean
        model.addAttribute("newCompany", new Customer());
        
        // Add all existing companies
        List<Customer> companies = clientService.getAllClients();
        model.addAttribute("companies", companies);
        
        return "test/index";
    }

    /**
     * Handle the creation of a new company.
     */
    @PostMapping("/company/create")
    public String createCompany(@ModelAttribute("newCompany") Customer customer,
                                RedirectAttributes redirectAttributes) {
        try {
            Customer created = clientService.createClient(customer);
            redirectAttributes.addFlashAttribute("message", 
                    "Company created successfully with ID: " + created.getId());
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", 
                    "Error creating company: " + e.getMessage());
        }
        
        return "redirect:/test";
    }

    /**
     * Create the test company specifically for Syrix Dev.
     */
    @PostMapping("/client/create-test")
    public String createTestClient(RedirectAttributes redirectAttributes) {
        try {
            Customer created = testClientCreator.createSyrixDevClient();
            ConnectionInfo connectionInfo = testClientCreator.createSyrixDevConnectionInfo(created);

            redirectAttributes.addFlashAttribute("message", "Test company created successfully with ID: " + created.getId());
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error creating test company: " + e.getMessage());
        }
        
        return "redirect:/test";
    }
}
