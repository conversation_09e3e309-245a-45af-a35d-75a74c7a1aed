# Development Profile Configuration
# This file is loaded when spring.profiles.active includes 'development'

# Enable mock notification data for development
syrix.notifications.use-mock-data=true

# Development-specific logging
logging.level.io.syrix.service.NotificationsService=DEBUG
logging.level.io.syrix=DEBUG

# Disable some production features for development
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Development database settings (if different from production)
# You can override MongoDB settings here if needed for development
