# Application Configuration
spring.application.name=syrix
server.servlet.context-path=/
server.port=8443

# OAuth2 Callback Configuration
app.oauth2.callbackUrl=http://localhost:8080/auth/callback

# Allow bean definition overriding (if needed)
spring.main.allow-bean-definition-overriding=true

# HTTP Port (Additional port for non-SSL access)
server.http.port=8080

# SSL Configuration
server.ssl.enabled=true
server.ssl.key-store=classpath:ddns.p12
server.ssl.key-store-password=15101997
server.ssl.key-store-type=PKCS12
server.ssl.enabled-protocols=TLSv1.2,TLSv1.3

# Security Configuration
jwt.secret=c2354deb4029d0f166ce5901fe5e9d9e4cf75012acbf67a636a76d1fa459c3eb
jwt.expiration=86400000
# Removed spring.security.filter.order to prevent conflicts
server.redirect.to.https.port=8443

# CORS Configuration (Enhanced Security - now handled by SecurityConfig bean)
# Removed wildcard origins for better security
# Only safe HTTP methods allowed (GET, POST, OPTIONS)
# PUT and DELETE disabled for security

# Static Resources Configuration with Content Hashing
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.chain.enabled=true
spring.web.resources.chain.cache=true
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
spring.web.resources.chain.strategy.fixed.enabled=false
spring.mvc.static-path-pattern=/**
spring.mvc.view.prefix=/
spring.mvc.view.suffix=.html
# Ensure resources are found when app is packaged
spring.web.resources.add-mappings=true

# Cache Control Settings for Static Resources
spring.web.resources.cache.cachecontrol.max-age=31536000
spring.web.resources.cache.cachecontrol.cache-public=true
# HTML files should not be cached
spring.web.resources.cache.cachecontrol.no-cache=false

# Logging Configuration - Enhanced for debugging
logging.level.root=INFO
logging.level.org.springframework.boot.devtools=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.servlet.resource=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.syrix=DEBUG
logging.level.com.syrix.service.connect=TRACE
logging.level.io.syrix.controller.connect=TRACE
logging.level.com.syrix.config.WebMvcConfig=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
# Suppress stacktraces for security exceptions 
logging.level.org.springframework.security.web.firewall=WARN

# Actuator Configuration (if needed)
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# Server Compression with Security Headers
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css

# Security Headers (additional to SecurityConfig)
server.error.include-exception=false
# Disable dangerous HTTP methods at Tomcat level
server.tomcat.additional-tld-skip-patterns=
server.tomcat.remoteip.protocol-header=x-forwarded-proto

# Error Handling
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=never

# Enable devtools for hot reloading
spring.devtools.restart.exclude=.DS_Store,.git/**,static/**,public/**
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s

# Microsoft Authentication Library (MSAL) Configuration
# REMOVED: spring.config.import=classpath:oauth2.properties
# Using database configuration for OAuth2 credentials instead of properties file
# More info: https://learn.microsoft.com/en-us/entra/identity-platform/msal-overview
# Set MSAL debug logging
logging.level.com.microsoft.aad.msal4j=DEBUG

# OAuth2 Default Configuration
# These values are only used as fallbacks if database configuration is unavailable
app.oauth2.tenant=common
app.oauth2.authority=https://login.microsoftonline.com/

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Database Configuration - MongoDB
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=Syrix
# MongoDB authentication configuration
spring.data.mongodb.username=admin
spring.data.mongodb.password=password123
spring.data.mongodb.authentication-database=admin
spring.data.mongodb.auto-index-creation=true

# Database Type Configuration
# This property is used to explicitly set the database type if auto-detection fails
syrix.database.type=MONGODB

# Set active profile
spring.profiles.active=production,mongodb

syrix.initializaton.udpatePolicyIds=true
syrix.opa.url=http://localhost:8181
