# Application Configuration
spring.application.name=syrix
server.servlet.context-path=/
server.port=8443

# OAuth2 Callback Configuration
#app.oauth2.callbackUrl=http://localhost:8080/auth/callback

# Allow bean definition overriding (if needed)
spring.main.allow-bean-definition-overriding=true

# HTTP Port (Additional port for non-SSL access)
server.http.port=8080

# SSL Configuration (works with Undertow)
#server.ssl.enabled=true
#server.ssl.key-store=classpath:ddns.p12
#server.ssl.key-store-password=15101997
#server.ssl.key-store-type=PKCS12
#server.ssl.enabled-protocols=TLSv1.2,TLSv1.3

# === UNDERTOW CONFIGURATION ===

# Basic Undertow settings
server.undertow.buffer-size=16384
server.undertow.io-threads=${UNDERTOW_IO_THREADS:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}
server.undertow.worker-threads=${UNDERTOW_WORKER_THREADS:#{T(java.lang.Runtime).getRuntime().availableProcessors() * 8}}
server.undertow.direct-buffers=true

# HTTP/2 and connection settings
server.undertow.max-http-post-size=10MB
server.undertow.max-parameters=1000
server.undertow.max-headers=200
server.undertow.max-cookies=200

# Session configuration
server.servlet.session.timeout=30m
server.servlet.session.cookie.max-age=1800
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true

# Request handling
server.undertow.url-charset=UTF-8
server.undertow.decode-url=true

# Performance tuning
server.undertow.no-request-timeout=60000
#server.undertow.request-parse-timeout=60000
#server.undertow.idle-timeout=60000

# Access logging (optional)
server.undertow.accesslog.enabled=false
server.undertow.accesslog.dir=logs
server.undertow.accesslog.pattern=common

# === REMOVED OLD TOMCAT PROPERTIES ===
# server.tomcat.* properties are no longer needed

# Security Configuration
#jwt.secret=c2354deb4029d0f166ce5901fe5e9d9e4cf75012acbf67a636a76d1fa459c3eb
#jwt.expiration=86400000
#server.redirect.to.https.port=8443

# Static Resources Configuration with Content Hashing
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.chain.enabled=true
spring.web.resources.chain.cache=true
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
spring.web.resources.chain.strategy.fixed.enabled=false
spring.mvc.static-path-pattern=/**
spring.mvc.view.prefix=/
spring.mvc.view.suffix=.html
spring.web.resources.add-mappings=true

# Cache Control Settings for Static Resources
spring.web.resources.cache.cachecontrol.max-age=31536000
spring.web.resources.cache.cachecontrol.cache-public=true
spring.web.resources.cache.cachecontrol.no-cache=false

# Logging Configuration - Enhanced for debugging
logging.level.root=INFO
logging.level.org.springframework.boot.devtools=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.servlet.resource=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.syrix=DEBUG
logging.level.com.syrix.service.connect=TRACE
logging.level.io.syrix.controller.connect=TRACE
logging.level.com.syrix.config.WebMvcConfig=DEBUG
logging.level.io.undertow=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.level.org.springframework.security.web.firewall=WARN

# Actuator Configuration with Undertow metrics
management.endpoints.web.exposure.include=health,info,metrics,undertow
management.endpoint.health.show-details=always
management.endpoints.jmx.exposure.include=health,info,metrics,undertow

# Undertow-specific metrics
#management.metrics.web.server.request.autotime.enabled=true
#management.metrics.web.server.request.autotime.percentiles=0.50,0.95,0.99
#management.endpoint.metrics.enabled=true

# Security and compression (generic)
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
server.error.include-exception=false
server.error.include-stacktrace=never

# Error Handling
server.error.include-message=always
server.error.include-binding-errors=always

# Enable devtools for hot reloading
spring.devtools.restart.exclude=.DS_Store,.git/**,static/**,public/**
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s

# Microsoft Authentication Library (MSAL) Configuration
logging.level.com.microsoft.aad.msal4j=DEBUG

# OAuth2 Default Configuration
app.oauth2.tenant=common
app.oauth2.authority=https://login.microsoftonline.com/

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Database Configuration - MongoDB
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=Syrix
spring.data.mongodb.username=admin
spring.data.mongodb.password=password123
spring.data.mongodb.authentication-database=admin
spring.data.mongodb.auto-index-creation=true

# Database Type Configuration
syrix.database.type=MONGODB

# Set active profile
spring.profiles.active=production,mongodb

#
syrix.initializaton.udpatePolicyIds=true
syrix.initialization.createTestClient=true
syrix.opa.url=http://localhost:8181

# Config file path for refresh token
syrix.config.path=/Users/<USER>/Syrix/src/365Security/SyrixBackend/config/config.yml

# Development Configuration
# Set to true to use mock notification data instead of database (for development only)
syrix.notifications.use-mock-data=false
