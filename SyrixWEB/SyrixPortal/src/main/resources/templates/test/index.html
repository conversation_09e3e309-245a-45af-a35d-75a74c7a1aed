<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syrix Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
        }
        .alert {
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        form {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .companies-list {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Syrix Test Page</h1>
        
        <!-- Display success message -->
        <div th:if="${message}" class="alert alert-success" th:text="${message}"></div>
        
        <!-- Display error message -->
        <div th:if="${error}" class="alert alert-danger" th:text="${error}"></div>
        
        <h2>Create New Company</h2>
        <form th:action="@{/test/client/create}" th:object="${newClient}" method="post">
            <div class="form-group">
                <label for="name">Company Name:</label>
                <input type="text" id="name" th:field="*{name}" required>
            </div>
            
            <div class="form-group">
                <label for="displayName">Display Name:</label>
                <input type="text" id="displayName" th:field="*{displayName}">
            </div>
            
            <div class="form-group">
                <label for="microsoftTenantId">Microsoft Tenant ID:</label>
                <input type="text" id="microsoftTenantId" th:field="*{microsoftTenantId}">
            </div>
            
            <div class="form-group">
                <label for="contactEmail">Contact Email:</label>
                <input type="email" id="contactEmail" th:field="*{contactEmail}">
            </div>
            
            <div class="form-group">
                <label for="contactPhone">Contact Phone:</label>
                <input type="text" id="contactPhone" th:field="*{contactPhone}">
            </div>
            
            <button type="submit">Create Company</button>
        </form>
        
        <h2>Create Test Company (syrixdev.onmicrosoft.com)</h2>
        <form th:action="@{/test/client/create-test}" method="post">
            <p>Click the button below to create a pre-configured test client for Syrix Development with tenant ID "syrixdev.onmicrosoft.com".</p>
            <button type="submit">Create Test Company</button>
        </form>
        
        <h2>Existing Companies</h2>
        <div class="companies-list">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Display Name</th>
                        <th>Microsoft Tenant ID</th>
                        <th>Contact Email</th>
                        <th>Status</th>
                        <th>Created At</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="client : ${companies}">
                        <td th:text="${client.id}"></td>
                        <td th:text="${client.name}"></td>
                        <td th:text="${client.displayName}"></td>
                        <td th:text="${client.microsoftTenantId}"></td>
                        <td th:text="${client.contactEmail}"></td>
                        <td th:text="${client.status}"></td>
                        <td th:text="${#temporals.format(client.createdAt, 'yyyy-MM-dd HH:mm')}"></td>
                    </tr>
                    <tr th:if="${companies.empty}">
                        <td colspan="7">No companies found</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>Microsoft OAuth2 Integration</h2>
        <div>
            <p>After creating a client, you can initiate the OAuth2 flow by clicking the button below:</p>
            <a href="/connect" class="btn btn-primary">
                <button>Go to Connect Services</button>
            </a>
        </div>
    </div>
</body>
</html>
