# Microsoft Authentication Library (MSAL) Configuration
# This file provides default values that are used as fallbacks if database configuration is unavailable
# In production, prefer using database configuration through the oauth2_configurations table

# Client ID (Application ID)
app.oauth2.clientId=8d241555-ab7a-4d2b-b6fe-8a1076cabe69

# Client secret
app.oauth2.clientSecret=****************************************

# Redirect URL - must match the one registered in Azure AD
app.oauth2.redirectUri=http://localhost:8080/auth/callback

# OAuth scopes
app.oauth2.scopes=openid profile User.Read Mail.Read Files.Read offline_access

# Microsoft tenant - common for multi-tenant apps
app.oauth2.tenant=common

# Authority URL
app.oauth2.authority=https://login.microsoftonline.com/