#!/usr/bin/env node

/**
 * Service Worker Generator for Syrix Portal
 * 
 * This script generates a service worker with proper asset hashing for optimal caching.
 * It reads the React build output and creates a service worker that caches all the
 * correctly hashed static assets.
 */

const fs = require('fs');
const path = require('path');

// Paths
const FRONTEND_DIR = path.join(__dirname, '../frontend');
const BUILD_DIR = path.join(FRONTEND_DIR, 'build');
const STATIC_DIR = path.join(BUILD_DIR, 'static');
const TEMPLATE_PATH = path.join(__dirname, '../src/main/resources/static/sw-template.js');
const OUTPUT_PATH = path.join(BUILD_DIR, 'sw.js');
const MANIFEST_PATH = path.join(BUILD_DIR, 'asset-manifest.json');

console.log('🔧 Generating Service Worker with hashed assets...');

function generateCacheVersion() {
    const now = new Date();
    return `v${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}.${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
}

function getBuildTimestamp() {
    return new Date().toISOString();
}

function getStaticAssets() {
    const assets = [];
    
    try {
        // Read asset manifest if it exists
        if (fs.existsSync(MANIFEST_PATH)) {
            const manifest = JSON.parse(fs.readFileSync(MANIFEST_PATH, 'utf8'));
            console.log('📋 Found asset manifest, extracting assets...');
            
            // Extract main assets from manifest
            if (manifest.files) {
                Object.entries(manifest.files).forEach(([key, value]) => {
                    if (key.endsWith('.js') || key.endsWith('.css')) {
                        assets.push(value);
                        console.log(`   - ${value}`);
                    }
                });
            }
            
            // Add entrypoints
            if (manifest.entrypoints) {
                manifest.entrypoints.forEach(entrypoint => {
                    if (!assets.includes(entrypoint)) {
                        assets.push(entrypoint);
                        console.log(`   - ${entrypoint} (entrypoint)`);
                    }
                });
            }
        } else {
            console.log('📋 No asset manifest found, scanning build directory...');
            
            // Fallback: scan the static directory for JS and CSS files
            if (fs.existsSync(STATIC_DIR)) {
                const scanDirectory = (dir, prefix = '') => {
                    const items = fs.readdirSync(dir);
                    items.forEach(item => {
                        const fullPath = path.join(dir, item);
                        const relativePath = path.join(prefix, item);
                        
                        if (fs.statSync(fullPath).isDirectory()) {
                            scanDirectory(fullPath, relativePath);
                        } else if (item.endsWith('.js') || item.endsWith('.css')) {
                            const assetPath = `/static/${relativePath.replace(/\\/g, '/')}`;
                            assets.push(assetPath);
                            console.log(`   - ${assetPath}`);
                        }
                    });
                };
                
                scanDirectory(STATIC_DIR);
            }
        }
        
        // Always include core assets
        const coreAssets = [
            '/',
            '/manifest.json',
            '/favicon.ico'
        ];
        
        coreAssets.forEach(asset => {
            if (!assets.includes(asset)) {
                assets.push(asset);
                console.log(`   - ${asset} (core)`);
            }
        });
        
    } catch (error) {
        console.error('❌ Error reading build assets:', error.message);
        // Return minimal assets as fallback
        return [
            '/',
            '/static/js/bundle.js',
            '/static/css/main.css',
            '/manifest.json',
            '/favicon.ico'
        ];
    }
    
    return assets;
}

function generateServiceWorker() {
    try {
        // Check if template exists
        if (!fs.existsSync(TEMPLATE_PATH)) {
            console.error(`❌ Template not found at: ${TEMPLATE_PATH}`);
            console.log('📝 Creating basic service worker template...');
            createBasicTemplate();
        }
        
        // Read template
        const template = fs.readFileSync(TEMPLATE_PATH, 'utf8');
        
        // Get build information
        const cacheVersion = generateCacheVersion();
        const buildTimestamp = getBuildTimestamp();
        const staticAssets = getStaticAssets();
        
        console.log(`📦 Cache version: ${cacheVersion}`);
        console.log(`📅 Build timestamp: ${buildTimestamp}`);
        console.log(`🎯 Total assets to cache: ${staticAssets.length}`);
        
        // Replace placeholders in template
        let serviceWorker = template
            .replace(/\{\{CACHE_VERSION\}\}/g, cacheVersion)
            .replace(/\{\{BUILD_TIMESTAMP\}\}/g, buildTimestamp)
            .replace(/\{\{TOTAL_ASSETS\}\}/g, staticAssets.length)
            .replace(/\{\{ENTRYPOINTS_COUNT\}\}/g, staticAssets.filter(asset => asset.includes('main.')).length);
        
        // Replace the STATIC_RESOURCES array
        const staticResourcesArray = JSON.stringify(staticAssets, null, 2);
        serviceWorker = serviceWorker.replace(
            /const STATIC_RESOURCES = \[[\s\S]*?\];/,
            `const STATIC_RESOURCES = ${staticResourcesArray};`
        );
        
        // Add asset manifest as comment for debugging
        const assetManifest = `// Asset Manifest (${staticAssets.length} assets):\n// ${staticAssets.map(asset => `//   - ${asset}`).join('\n// ')}`;
        serviceWorker = serviceWorker.replace(
            /\/\/ \{\{ASSET_MANIFEST\}\}/,
            assetManifest
        );
        
        // Write the generated service worker
        fs.writeFileSync(OUTPUT_PATH, serviceWorker, 'utf8');
        
        console.log(`✅ Service Worker generated successfully!`);
        console.log(`📁 Output: ${OUTPUT_PATH}`);
        console.log(`📊 Cached ${staticAssets.length} assets with version ${cacheVersion}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ Error generating service worker:', error.message);
        console.error(error.stack);
        return false;
    }
}

function createBasicTemplate() {
    // Create a basic template if none exists
    const basicTemplate = `// Syrix Service Worker for Advanced Performance Caching
// Auto-generated from template on {{BUILD_TIMESTAMP}}
// Cache Version: {{CACHE_VERSION}}

const CACHE_VERSION = '{{CACHE_VERSION}}';
const CACHE_NAME = \`syrix-app-\${CACHE_VERSION}\`;
const API_CACHE_NAME = \`syrix-api-\${CACHE_VERSION}\`;
const STATIC_CACHE_NAME = \`syrix-static-\${CACHE_VERSION}\`;

// Build information
const BUILD_INFO = {
  timestamp: '{{BUILD_TIMESTAMP}}',
  version: '{{CACHE_VERSION}}',
  totalAssets: {{TOTAL_ASSETS}},
  entrypointsCount: {{ENTRYPOINTS_COUNT}}
};

// Resources to cache immediately on install (will be replaced with actual hashed filenames)
const STATIC_RESOURCES = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico'
];

// Install event - cache static resources with hashed filenames
self.addEventListener('install', (event) => {
  console.log(\`🔧 Syrix Service Worker installing (\${CACHE_VERSION})...\`);
  console.log(\`📊 Build info:\`, BUILD_INFO);
  console.log(\`🎯 Precaching \${STATIC_RESOURCES.length} critical assets\`);
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('📦 Caching static resources with hashed filenames');
        STATIC_RESOURCES.forEach(resource => {
          console.log(\`   - \${resource}\`);
        });
        return cache.addAll(STATIC_RESOURCES);
      }),
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log(\`✅ Syrix Service Worker activated (\${CACHE_VERSION})\`);
  
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== API_CACHE_NAME && 
                cacheName !== STATIC_CACHE_NAME) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim()
    ])
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  if (request.method !== 'GET') {
    return;
  }

  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
  } else if (url.pathname.startsWith('/static/')) {
    event.respondWith(handleStaticRequest(request));
  } else if (url.pathname === '/' || url.pathname.includes('.html')) {
    event.respondWith(handlePageRequest(request));
  } else {
    event.respondWith(handleDefaultRequest(request));
  }
});

async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE_NAME);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    return new Response('API not available', { status: 503 });
  }
}

async function handleStaticRequest(request) {
  const cache = await caches.open(STATIC_CACHE_NAME);
  
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    return new Response('Resource not available', { status: 404 });
  }
}

async function handlePageRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  
  const cachedResponse = await cache.match(request);
  
  const networkResponsePromise = fetch(request).then((response) => {
    if (response.ok) {
      cache.put(request, response.clone());
    }
    return response;
  }).catch(() => null);
  
  if (cachedResponse) {
    networkResponsePromise;
    return cachedResponse;
  }
  
  return networkResponsePromise || new Response('Page not available', { status: 404 });
}

async function handleDefaultRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    return new Response('Request failed', { status: 503 });
  }
}

console.log(\`🚀 Syrix Service Worker loaded successfully (\${CACHE_VERSION})\`);
console.log(\`📅 Build timestamp: \${BUILD_INFO.timestamp}\`);
console.log(\`🎯 Ready to cache \${STATIC_RESOURCES.length} critical assets with proper hashing\`);

// Asset manifest (for debugging)
// {{ASSET_MANIFEST}}
`;

    // Ensure the directory exists
    const templateDir = path.dirname(TEMPLATE_PATH);
    if (!fs.existsSync(templateDir)) {
        fs.mkdirSync(templateDir, { recursive: true });
    }
    
    fs.writeFileSync(TEMPLATE_PATH, basicTemplate, 'utf8');
    console.log(`📝 Created basic template at: ${TEMPLATE_PATH}`);
}

// Main execution
if (require.main === module) {
    const success = generateServiceWorker();
    process.exit(success ? 0 : 1);
}

module.exports = { generateServiceWorker };
