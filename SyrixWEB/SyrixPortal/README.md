# Syrix Security Dashboard 🛡️

**Modern Security Monitoring & Management Platform**

![Version](https://img.shields.io/badge/version-1.0.0-blue)
![Java](https://img.shields.io/badge/Java-21-orange)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.2-green)
![React](https://img.shields.io/badge/React-19-61dafb)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![MongoDB](https://img.shields.io/badge/MongoDB-7.0-green)

## 🎯 **Overview**

Syrix is a comprehensive security dashboard application providing real-time security monitoring, alerts management, audit logging, and system configuration through a modern, scalable web architecture.

### **Key Features**
- 🔍 **Real-time Security Monitoring** - Dashboard with live metrics
- 🚨 **Alerts Management** - Comprehensive alert processing and notifications  
- 📊 **Audit Logging** - Complete audit trail and compliance tracking
- 🔧 **System Configuration** - Centralized configuration management
- 👥 **User Management** - Role-based access control
- 🔒 **OAuth2 Integration** - Microsoft Entra ID and Google Workspace

---

## 🏗️ **Architecture Overview**

### **Technology Stack**

**Backend:**
- **Java 21** with **Spring Boot 3.4.2**
- **Spring Security 6** for authentication  
- **MongoDB** for document storage
- **Maven** for dependency management

**Frontend:**
- **React 19** with **TypeScript**
- **Redux Toolkit** for state management
- **Tailwind CSS** with **shadcn/ui** components
- **Axios** for API integration

### **Clean V1 API Architecture**

```mermaid
graph TB
    subgraph \"Frontend Layer\"
        UI[React TypeScript App<br/>Port: 3030]
        Redux[Redux Toolkit<br/>State Management]
        UI --> Redux
    end
    
    subgraph \"API Layer - V1 Only\"
        Gateway[Spring Boot API<br/>Port: 8080/8443<br/>/api/v1/*]
    end
    
    subgraph \"Domain Controllers\"
        SystemV1[SystemV1Controller<br/>Health, Status, Test]
        DashboardV1[DashboardV1Controller<br/>Dashboard, Metrics]
        AlertsV1[AlertsV1Controller<br/>Alert Management]
        LogsV1[SystemLogV1Controller<br/>System Logging]
        AuditV1[AuditV1Controller<br/>Audit Compliance]
        RiskV1[RiskV1Controller<br/>Risk Analysis] 
        ScanV1[ScanV1Controller<br/>Security Scanning]
        NotifyV1[NotificationsV1Controller<br/>Notifications]
    end
    
    subgraph \"Service Layer\"
        Services[Security Services<br/>Alert Services<br/>Notification Services<br/>OAuth2 Services]
    end
    
    subgraph \"Data Layer\"
        MongoDB[(MongoDB<br/>Document Store)]
    end
    
    Redux --> Gateway
    Gateway --> SystemV1
    Gateway --> DashboardV1
    Gateway --> AlertsV1
    Gateway --> LogsV1
    Gateway --> AuditV1
    Gateway --> RiskV1
    Gateway --> ScanV1
    Gateway --> NotifyV1
    
    SystemV1 --> Services
    DashboardV1 --> Services
    AlertsV1 --> Services
    LogsV1 --> Services
    AuditV1 --> Services
    RiskV1 --> Services
    ScanV1 --> Services
    NotifyV1 --> Services
    
    Services --> MongoDB
```

---

## 🚀 **API Endpoints - V1 Clean Architecture**

### **✅ All Endpoints Use `/api/v1/*` Pattern**

| **Domain** | **Controller** | **Endpoints** | **Description** |
|------------|----------------|---------------|-----------------|
| **System** | `SystemV1Controller` | `GET /api/v1/health`<br/>`GET /api/v1/status`<br/>`GET /api/v1/test`<br/>`POST /api/v1/test`<br/>`GET /api/v1/health-check`<br/>`GET /api/v1/deployment-test` | System health monitoring<br/>Status verification<br/>Endpoint testing<br/>Deployment validation |
| **Dashboard** | `DashboardV1Controller` | `GET /api/v1/dashboard`<br/>`GET /api/v1/user-profile`<br/>`GET /api/v1/security-metrics`<br/>`GET /api/v1/status-cards`<br/>`GET /api/v1/test-status-items`<br/>`GET /api/v1/failed-findings`<br/>`GET /api/v1/test-status` | Dashboard data<br/>User profiles<br/>Security metrics<br/>Status information |
| **Alerts** | `AlertsV1Controller` | `GET /api/v1/alerts`<br/>`GET /api/v1/alerts/{id}`<br/>`PUT /api/v1/alerts/{id}/status` | Alert management<br/>CRUD operations<br/>Status updates |
| **System Logs** | `SystemLogV1Controller` | `GET /api/v1/systemlog`<br/>`GET /api/v1/systemlog/{id}`<br/>`GET /api/v1/systemlog/activities`<br/>`GET /api/v1/systemlog/statuses`<br/>`GET /api/v1/systemlog/users`<br/>`GET /api/v1/systemlog/export` | System logging<br/>Log filtering<br/>Export functionality |
| **Audit** | `AuditV1Controller` | `GET /api/v1/audit/logs` | Compliance logging<br/>Audit trail tracking |
| **Risk** | `RiskV1Controller` | `GET /api/v1/top-risks`<br/>`GET /api/v1/top-risk-users`<br/>`GET /api/v1/risk-over-time` | Risk analysis<br/>Risk trending<br/>User risk assessment |
| **Security Scanning** | `ScanV1Controller` | `GET /api/v1/next-scan-info`<br/>`GET /api/v1/next-scan`<br/>`GET /api/v1/scan-status` | Security scanning<br/>Scan scheduling<br/>Status monitoring |
| **Notifications** | `NotificationsV1Controller` | `GET /api/v1/notifications`<br/>`GET /api/v1/notifications-service` | Notification management<br/>Service notifications |

### **🔄 Standardized Response Format**

All V1 endpoints return consistent `ApiResponse<T>` wrapper:

```json
{
  \"success\": true,
  \"data\": { /* actual response data */ },
  \"message\": null,
  \"error\": null,
  \"timestamp\": \"2025-06-03T10:53:02.776Z\",
  \"requestId\": \"uuid-request-id\",
  \"apiVersion\": \"1.0.0\"
}
```

---

## 🛠️ **Development Setup**

### **Prerequisites**

- **Java 21+** (OpenJDK or Oracle JDK)
- **Node.js 18+** and **Yarn**
- **MongoDB 6.0+**
- **Git**

### **Quick Start**

#### 1. **Clone Repository**
```bash
git clone <repository-url>
cd SyrixWEB/SyrixPortal
```

#### 2. **Backend Setup**
```bash
# Build backend using Maven Wrapper
./mvnw clean compile

# Run tests
./mvnw test

# Start backend server
./mvnw spring-boot:run
```

#### 3. **Frontend Setup**
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
yarn install

# Start development server
yarn start
```

#### 4. **Access Application**
- **Frontend**: http://localhost:3030
- **Backend API**: http://localhost:8080/api/v1
- **Health Check**: http://localhost:8080/api/v1/health

---

## 🧪 **Testing & Validation**

### **Comprehensive API Testing**

We provide a complete validation script to test all endpoints:

```bash
# Make validation script executable
chmod +x validate-api-migration.sh

# Run comprehensive API tests
./validate-api-migration.sh
```

**The validation script tests:**
- ✅ All 28+ V1 endpoints functionality
- ✅ ApiResponse<T> format validation  
- ✅ Error handling verification
- ✅ Frontend compatibility
- ✅ Request/response consistency

### **Manual Testing**

Test individual endpoints:

```bash
# Test system health
curl http://localhost:8080/api/v1/health

# Test dashboard data  
curl http://localhost:8080/api/v1/dashboard

# Test alerts
curl http://localhost:8080/api/v1/alerts

# Test with parameters
curl \"http://localhost:8080/api/v1/alerts?status=open&category=security\"
```

---

## 🏭 **Production Deployment**

### **Build for Production**

#### 1. **Backend Production Build**
```bash
# Create production JAR
./mvnw clean package -DskipTests

# JAR location: target/SyrixPortal-1.0-SNAPSHOT.jar
```

#### 2. **Frontend Production Build**  
```bash
cd frontend

# Create optimized build
yarn build

# Static files: frontend/build/
```

### **Deployment Configuration**

#### **Environment Variables**
```bash
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/syrix_production
MONGODB_DATABASE=syrix_production

# Server Configuration  
SERVER_PORT=8080
SERVER_SSL_PORT=8443

# OAuth2 Configuration
OAUTH2_TENANT_ID=your-tenant-id
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret

# Security Configuration
JWT_SECRET=your-jwt-secret
CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

#### **Production Startup**
```bash
# Start with production profile
java -jar target/SyrixPortal-1.0-SNAPSHOT.jar \
  --spring.profiles.active=production \
  --server.port=8080
```

### **Docker Deployment (Optional)**

```dockerfile
FROM openjdk:21-jre-slim

WORKDIR /app
COPY target/SyrixPortal-1.0-SNAPSHOT.jar app.jar

EXPOSE 8080 8443

ENTRYPOINT [\"java\", \"-jar\", \"app.jar\"]
```

```bash
# Build Docker image
docker build -t syrix-portal:latest .

# Run container
docker run -p 8080:8080 -p 8443:8443 syrix-portal:latest
```

---

## 🔧 **Configuration**

### **Database Configuration**

```properties
# MongoDB Configuration
spring.data.mongodb.uri=mongodb://localhost:27017/syrix_db
spring.data.mongodb.database=syrix_db

# Connection Pool Settings
spring.data.mongodb.options.max-pool-size=50
spring.data.mongodb.options.min-pool-size=5
```

### **Security Configuration**

```properties
# OAuth2 Settings
oauth2.tenant=your-tenant-id
oauth2.authority=https://login.microsoftonline.com/your-tenant-id
oauth2.redirect-uri=http://localhost:3030/auth/callback

# CORS Configuration
cors.allowed-origins=http://localhost:3030,https://yourdomain.com
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
```

### **Logging Configuration**

```properties
# Logging Levels
logging.level.io.syrix=INFO
logging.level.org.springframework.security=DEBUG

# Log File Configuration
logging.file.name=logs/syrix-application.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
```

---

## 📁 **Project Structure**

```
SyrixPortal/
├── src/main/java/io/syrix/
│   ├── controller/
│   │   ├── v1/                          # V1 API Controllers
│   │   │   ├── SystemV1Controller.java  
│   │   │   ├── DashboardV1Controller.java
│   │   │   ├── AlertsV1Controller.java
│   │   │   ├── SystemLogV1Controller.java
│   │   │   ├── AuditV1Controller.java
│   │   │   ├── RiskV1Controller.java
│   │   │   ├── ScanV1Controller.java
│   │   │   └── NotificationsV1Controller.java
│   │   ├── base/
│   │   │   └── BaseApiController.java   # Common controller logic
│   │   └── [other controllers]
│   ├── service/                         # Business logic layer
│   ├── model/                          # Data models
│   │   └── response/
│   │       └── ApiResponse.java        # Standardized response wrapper
│   └── config/                         # Configuration classes
├── frontend/
│   ├── src/
│   │   ├── components/                 # React components
│   │   ├── features/                   # Feature-specific components
│   │   ├── services/                   # API services
│   │   ├── store/                      # Redux store configuration
│   │   └── types/                      # TypeScript definitions
│   └── public/                         # Static assets
├── validate-api-migration.sh           # API validation script
└── README.md                           # This file
```

---

## 🔍 **Debugging**

### **Backend Debugging**

#### **VSCode Debug Configuration**
```json
{
  \"type\": \"java\",
  \"name\": \"Debug Syrix Backend\",
  \"request\": \"launch\",
  \"mainClass\": \"io.syrix.SyrixPortalApplication\",
  \"args\": \"--spring.profiles.active=dev\",
  \"vmArgs\": \"-Dspring.devtools.restart.enabled=true\"
}
```

#### **Remote Debugging**
```bash
# Start with remote debugging
./mvnw spring-boot:run -Dspring-boot.run.jvmArguments=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005\"
```

### **Frontend Debugging**

```bash
# Start with debugging enabled
cd frontend
yarn start

# Browser DevTools
# - React DevTools extension
# - Redux DevTools extension  
# - Network tab for API requests
```

### **Database Debugging**

```bash
# MongoDB query profiling
mongosh --eval \"db.setProfilingLevel(2)\"

# View query performance
mongosh --eval \"db.system.profile.find().sort({ts: -1}).limit(5)\"
```

---

## 📚 **API Documentation**

### **Interactive API Documentation**

When the application is running, access:
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI Spec**: http://localhost:8080/v3/api-docs

### **Example API Calls**

#### **Get Dashboard Data**
```bash
curl -X GET \"http://localhost:8080/api/v1/dashboard\" \
  -H \"Content-Type: application/json\"
```

#### **Get Alerts with Filter**
```bash
curl -X GET \"http://localhost:8080/api/v1/alerts?status=open&category=security\" \
  -H \"Content-Type: application/json\"
```

#### **Update Alert Status**
```bash
curl -X PUT \"http://localhost:8080/api/v1/alerts/alert-123/status\" \
  -H \"Content-Type: application/json\" \
  -d '{\"status\": \"resolved\"}'
```

---

## 🤝 **Contributing**

### **Development Workflow**

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation

3. **Test Changes**
   ```bash
   # Run backend tests
   ./mvnw test
   
   # Run frontend tests  
   cd frontend && yarn test
   
   # Run API validation
   ./validate-api-migration.sh
   ```

4. **Submit Pull Request**
   - Ensure all tests pass
   - Include clear description
   - Reference related issues

### **Code Style**

- **Backend**: Follow Google Java Style Guide
- **Frontend**: Use Prettier and ESLint configurations
- **Commits**: Use conventional commit format

---

## 🛡️ **Security**

### **Security Features**

- ✅ **OAuth2 Authentication** with Microsoft Entra ID
- ✅ **JWT Token Management** with refresh capabilities  
- ✅ **CORS Configuration** with domain restrictions
- ✅ **Request Rate Limiting** to prevent abuse
- ✅ **Input Validation** on all API endpoints
- ✅ **SQL Injection Prevention** with parameterized queries
- ✅ **XSS Protection** with proper output encoding

### **Security Headers**

The application automatically sets security headers:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

---

## 📄 **License**

This project is proprietary software. All rights reserved.

---

## 📞 **Support**

For technical support or questions:

- **Documentation**: Check this README and inline code comments
- **API Testing**: Use the provided validation script
- **Issues**: Review logs in `logs/syrix-application.log`
- **Database**: Verify MongoDB connection and collections

---

## 🎉 **Features & Highlights**

### **✅ Clean V1-Only Architecture**
- **Single API Version**: All endpoints use `/api/v1/*` pattern
- **Standardized Responses**: Consistent `ApiResponse<T>` wrapper
- **Domain-Specific Controllers**: Focused, maintainable code structure
- **Zero Legacy Complexity**: Clean codebase without deprecated endpoints

### **✅ Modern Development Stack**
- **Java 21**: Latest LTS version with modern language features
- **Spring Boot 3.4.2**: Latest framework with native compilation support
- **React 19**: Cutting-edge frontend with concurrent features
- **TypeScript**: Type-safe development with excellent IDE support

### **✅ Production-Ready Features**
- **Comprehensive Testing**: Automated validation for all endpoints
- **Health Monitoring**: Built-in health checks and metrics
- **Security Hardening**: OAuth2, CORS, rate limiting, input validation
- **Scalable Architecture**: Microservices-ready design

### **✅ Developer Experience**
- **Hot Reload**: Both frontend and backend support live reloading
- **Comprehensive Logging**: Detailed logging for debugging
- **API Documentation**: Auto-generated Swagger documentation
- **Development Tools**: VSCode configs, debugging setups

---

**🚀 Ready for development and production deployment!**
