# Syrix Security Platform 🛡️

**Enterprise Microsoft 365 Security Configuration & Remediation Platform**

![Java](https://img.shields.io/badge/Java-21-orange)
![Quarkus](https://img.shields.io/badge/Quarkus-3.23.0-blue)
![React](https://img.shields.io/badge/React-19-61dafb)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![MongoDB](https://img.shields.io/badge/MongoDB-7.0-green)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.2-green)

## 🎯 **Overview**

Syrix is a comprehensive Microsoft 365 security configuration retrieval, analysis, and remediation platform designed for enterprise environments. The platform provides real-time security monitoring, automated compliance checking, and AI-powered remediation recommendations across all Microsoft 365 services.

### **Key Capabilities**
- 🔍 **Microsoft 365 Security Analysis** - Comprehensive configuration retrieval and analysis
- 🤖 **AI-Powered Remediation** - Intelligent security policy remediation with LangChain4j
- 📊 **Real-time Security Monitoring** - Live dashboard with risk analysis and metrics
- 🚨 **Alert Management** - Comprehensive alert processing and notifications
- 📋 **Compliance Reporting** - Automated CISA baseline compliance checking
- 🔐 **Enterprise Authentication** - Multi-cloud OAuth2 with certificate-based security

---

## 🏗️ **Architecture Overview**

### **Multi-Module Architecture**

```
Syrix Platform
├── SyrixBackend/           # Core Security Engine (Quarkus Java 21)
├── SyrixCommon/            # Shared Foundation Module
│   ├── SyrixDM/           # Pure POJO Data Models
│   ├── SyrixDAO/          # Database Access Layer
│   └── SyrixMessaging/    # Task Queue System
├── SyrixWEB/              # Frontend Applications
│   ├── SyrixPortal/       # Main Security Dashboard
│   └── SyrixMSP/          # MSP Interface
└── SyrixCodeBuilder/      # Additional Tooling
```

### **Technology Stack**

**Backend (SyrixBackend)**:
- **Framework**: Quarkus 3.23.0 with CDI and Java 21
- **Microsoft Integration**: Graph API, PowerShell, MSAL4J authentication
- **AI Integration**: LangChain4j for intelligent remediation
- **Security**: OPA (Open Policy Agent) with Rego policies
- **Storage**: Pluggable abstraction (local files, AWS S3)
- **Messaging**: ActiveMQ Artemis for distributed processing
- **Port**: 8989 with CORS enabled

**Frontend (SyrixWEB)**:
- **Portal**: React 19 + TypeScript (Port: 3030)
- **MSP**: React + TypeScript (Port: 3000)
- **State Management**: Redux Toolkit
- **Styling**: Tailwind CSS with shadcn/ui components
- **API Integration**: Axios with comprehensive error handling

**Shared (SyrixCommon)**:
- **Data Models**: Pure POJOs with zero external dependencies
- **Database**: MongoDB 7.0 with transaction support
- **OAuth2**: MSAL integration with multi-cloud support

---

## 🚀 **Getting Started**

### **Prerequisites**

- **Java 21+** (OpenJDK or Oracle JDK)
- **Node.js 18+** and **Yarn**
- **MongoDB 7.0+**
- **Maven 3.9+**
- **Git**

### **Quick Start**

#### 1. **Clone Repository**
```bash
git clone <repository-url>
cd syrix
```

#### 2. **Build Shared Components**
```bash
# Build SyrixCommon modules
cd SyrixCommon
mvn clean install

# SyrixDM - Data Models
cd SyrixDM && mvn clean install && cd ..

# SyrixDAO - Database Access
cd SyrixCommonServices && mvn clean install && cd ..
```

#### 3. **Backend Setup (SyrixBackend)**
```bash
cd SyrixBackend

# Build backend
mvn clean compile

# Run tests
mvn test

# Start backend server
mvn quarkus:dev
```

#### 4. **Frontend Setup (SyrixPortal)**
```bash
cd SyrixWEB/SyrixPortal/frontend

# Install dependencies
yarn install

# Start development server
yarn start
```

#### 5. **Frontend Setup (SyrixMSP)**
```bash
cd SyrixWEB/SyrixMSP

# Install dependencies
yarn install

# Start MSP portal
yarn start
```

#### 6. **Access Applications**
- **SyrixBackend API**: http://localhost:8989
- **SyrixPortal Dashboard**: http://localhost:3030
- **SyrixMSP Portal**: http://localhost:3000
- **Portal Backend API**: http://localhost:8080/api/v1

---

## 🔐 **OAuth Authentication & Microsoft Integration**

### **Complete OAuth2 Authentication System**

The Syrix platform implements enterprise-grade OAuth2 authentication for Microsoft 365 integration with support for multiple authentication flows and cloud environments.

### **Dual Authentication Flow Architecture**

The Syrix system supports **two primary authentication flows**:

#### **A. Certificate-Based Authentication (Application Flow)**
- **Primary Implementation**: `MSCertBasedTokenGenerator` family
- **Grant Type**: `client_credentials`
- **Use Case**: Application-level authentication for service-to-service calls
- **Token Generators**:
  - `MSCertBasedGraphTokenGenerator` - Microsoft Graph API
  - `MSCertBasedOutlookTokenGenerator` - Exchange Online/Outlook
  - `MSCertBasedManagmentTokenGenerator` - Azure Management APIs
  - `PowerShellCertBasedSPTokenGenerator` - SharePoint PowerShell
  - `PowerShellCertBasedTeamsTokenGenerator` - Teams PowerShell

#### **B. Refresh Token-Based Authentication (Delegated Flow)**
- **Primary Implementation**: `MSRefreshTokenBasedTokenGenerator` family
- **Grant Type**: `refresh_token`
- **Use Case**: User-delegated authentication for user-context operations
- **Token Generators**:
  - `MSRefreshBasedGraphTokenGenerator` - Microsoft Graph API
  - `MSRefreshBasedOutlookTokenGenerator` - Exchange Online/Outlook
  - `MSRefreshTokenBasedManagementTokenGenerator` - Azure Management APIs

### **MSTokenGeneratorFactory Pattern**

```java
public class MSTokenGeneratorFactory {
    // Certificate-based authentication (Primary Enterprise Flow)
    public static IMSTokenGenerator getCertificateBasedTokenGenerator(
        String tenantId, String clientId, String certificatePath)
    
    // Refresh token authentication (Secondary/Interactive Flow)  
    public static IMSTokenGenerator getRefreshTokenBasedTokenGenerator(
        String tenantId, String clientId, String refreshToken)
    
    // Graph API client with authentication
    public static GraphServiceClient getGraphServiceClient(
        String tenantId, String clientId, String certificatePath)
    
    // Factory Pattern Implementation
    // Certificate-based (Application flow)
    ITokenGenerator graphTokenGenerator = MSTokenGeneratorFactory.getInstance()
        .getMGraphTokenGenerator(true, params); // true = certificate-based
    
    // Refresh token-based (Delegated flow)  
    ITokenGenerator graphTokenGenerator = MSTokenGeneratorFactory.getInstance()
        .getMGraphTokenGenerator(false, params); // false = refresh token-based
}
```

### **Microsoft Cloud Environment Support**

**Government Cloud Compatibility**:
- **Commercial Cloud**: login.microsoftonline.com endpoints
- **GCC (Government Community Cloud)**: Specialized government endpoints
- **GCC High**: High-security government cloud with enhanced compliance
- **DOD (Department of Defense)**: Maximum security classification environments

**Multi-Environment Support via MSEnvironment enum**:
- **COMMERCIAL**: Standard Microsoft 365 commercial cloud
- **GCC**: Government Community Cloud
- **GCC_HIGH**: Government Community Cloud High
- **DOD**: Department of Defense cloud

Each environment has different endpoints for authentication URLs, Graph API endpoints, Outlook/Exchange endpoints, Power Platform endpoints, and Security & Compliance endpoints.

### **Service-Specific Authentication Scopes**

**Defined Scopes by Microsoft Service**:
```java
// From Constants.java
public static final String MS_GRAPH_SCOPE_URL = "https://graph.microsoft.com/.default";
public static final String MS_MANAGMENT_SCOPE_URL = "https://management.azure.com/.default";
public static final String MS_OUTLOOK_SCOPE_URL = "https://outlook.office365.com/.default";
```

**Microsoft Graph API Integration**:
```java
// Graph API authentication with comprehensive scopes
GraphServiceClient graphClient = MSTokenGeneratorFactory
    .getGraphServiceClient(tenantId, clientId, certPath);

// Service-specific scope management
String[] requiredScopes = {
    "https://graph.microsoft.com/Directory.Read.All",
    "https://graph.microsoft.com/SecurityEvents.Read.All", 
    "https://graph.microsoft.com/Policy.Read.All"
};
```

**PowerShell Integration**:
```java
// PowerShell authentication for Exchange Online, Teams, SharePoint
IMSTokenGenerator psTokenGen = MSTokenGeneratorFactory
    .getCertificateBasedTokenGenerator(tenantId, clientId, certPath);
String accessToken = psTokenGen.getAccessToken("https://outlook.office365.com/");
```

### **Advanced Token Management**

**Token Caching Strategy**:
- **In-Memory Caching**: Tokens are cached in memory with expiration tracking
- **Automatic Refresh**: Tokens are refreshed automatically before expiration
- **5-minute Safety Buffer**: Tokens are considered expired 5 minutes before actual expiration

**Token Storage**:
- **Volatile Storage**: Current implementation uses in-memory storage
- **No Persistence**: Tokens are not persisted across application restarts
- **Per-Service Tokens**: Separate token generators for each Microsoft service

### **Permission Model Architecture**

**Application Permissions (App-only)**:
```java
public class ApplicationPermission extends Permission {
    private String permissionType;
    private String appRoleId;
    private String appRoleDisplayName;
    private String appRoleDescription;
    private String appRoleValue;
}
```

**Delegated Permissions (User-delegated)**:
```java
public class DelegatedPermission extends Permission {
    private String scope;
}
```

**Base Permission Fields**:
```java
public abstract class Permission {
    protected String resourceId;
    protected String resourceDisplayName;
    protected String consentType;
}
```

### **OAuth Configuration Models**

**MSALOAuthConfig Implementation**:
```java
public class MSALOAuthConfig implements OAuthConfig {
    private final String clientId;
    private final String clientSecret;
    private final String redirectUri;
    private final String scopes;
    private final String authUrl;
    private final String tokenUrl;
    private final ServiceType serviceType;
    
    // Default Configuration Values
    private String redirectUri = "http://localhost:8080/oauth2/callback";
    private String scopes = "openid profile User.Read offline_access";
    private String authUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
    private String tokenUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
}
```

### **Implementation Patterns**

**Token Generator Usage**:
```java
// Certificate-based authentication
ITokenGenerator tokenGenerator = MSTokenGeneratorFactory.getInstance()
    .getMGraphTokenGenerator(true, params);

// Refresh token-based authentication
ITokenGenerator tokenGenerator = MSTokenGeneratorFactory.getInstance()
    .getMGraphTokenGenerator(false, params);
```

**Token Acquisition Pattern**:
```java
CompletableFuture<String> tokenFuture = tokenGenerator.getAccessToken();
String accessToken = tokenFuture.join();
```

**HTTP Request Authentication**:
```java
HttpRequest request = HttpRequest.newBuilder()
    .uri(uri)
    .header("Authorization", "Bearer " + accessToken)
    .header("Content-Type", "application/json")
    .build();
```

**Builder Pattern for Token Parameters**:
```java
MSTokenGenerator.Params params = MSTokenGenerator.getParams()
    .clientId(clientId)
    .clientSecret(clientSecret)
    .refreshToken(refreshToken)
    .environment(environment)
    .appId(appId)
    .certPath(certPath)
    .certPassword(certPassword)
    .identifier(tenantId);
```

### **Current Limitations and Enhancement Recommendations**

**Current Limitations**:
- Limited delegated flow implementation for user-context operations
- In-memory token storage only (tokens lost on application restart)
- Static selection of certificate vs refresh token authentication
- Minimal user context tracking in delegated flows

**Enhancement Recommendations**:
- Implement comprehensive delegated flow support with user session management
- Add dynamic flow selection based on operation context
- Enhance token management with persistent storage and encryption
- Improve permission handling with dynamic scope management

---

## 📊 **Data Architecture & Database Layer**

### **SyrixDM - Clean Data Model Design**

**Design Philosophy**:
- **Clean POJOs**: Plain Java objects with no database-specific annotations
- **Separation of Concerns**: Data model separate from database access logic
- **Standard Java Types**: Uses `LocalDateTime` instead of DB-specific types
- **No External Dependencies**: No runtime dependencies on DAOs or ORM frameworks

**Core Business Models**:
- **Alert**: Security alert representation with status, category, severity
- **AuditLog**: Audit log entry for system operations and compliance tracking
- **Company**: Client/tenant in the Syrix platform with metadata
- **ConnectionInfo**: External service connection information and credentials
- **NotificationData**: Notification data with service information
- **Risk**: Risk assessment for applications or services with scoring
- **ScanStatus**: Security scan status representation and progress tracking
- **SecurityMetrics**: Security status and compliance metrics aggregation
- **SystemLogEntry**: Detailed system operation logs with categorization
- **UserProfile**: User profile information and preferences

**OAuth & Authentication Models**:
- **OAuthConfig**: Base interface for OAuth2 configurations
- **MSALOAuth2Config**: Microsoft-specific OAuth2 configuration implementation

**ServiceType Enum**: Available service types for Microsoft 365 integration

**Design Pattern**: Default constructors, required field constructors, full constructors with all fields, standard getters/setters, no runtime dependencies on DAOs or ORM frameworks.

### **SyrixDAO - Database Access Layer**

**Interface-Based Design Philosophy**:
- **Generic DAO Pattern**: Framework-independent database access
- **Factory Pattern**: `DAOFactory` for implementation selection
- **Transaction Support**: Built-in transaction management
- **Type Safety**: Generics-based compile-time type checking

**Core DAO Architecture**:
```java
// Generic DAO interface
public interface DAO<T> {
    void save(T entity);
    void update(T entity);
    void delete(T entity);
    T findById(String id);
    List<T> findAll();
}

// Specialized DAOs with domain-specific methods
public interface CompanyDAO extends DAO<Company> {
    Company findByName(String name);
    List<Company> findByStatus(String status);
}

public interface ConnectionInfoDAO extends DAO<ConnectionInfo> {
    List<ConnectionInfo> findByServiceType(ServiceType serviceType);
    ConnectionInfo findByCompanyAndService(String companyId, ServiceType serviceType);
}
```

**Factory Pattern Implementation**: DAOFactory provides implementation selection and abstracts concrete database technology choices.

### **MongoDB Implementation**

**Framework-Independent MongoDB Access**:
- **Pure MongoDB Integration**: Direct driver usage without Spring/JPA dependencies
- **Transaction Support**: Multi-document transactions with automatic rollback
- **Connection Management**: Built-in pooling and lifecycle management
- **Error Handling**: Comprehensive exception handling

**Transaction Management**:
```java
try (ClientSession session = mongoClient.startSession()) {
    session.withTransaction(() -> {
        companyDAO.save(session, company);
        connectionInfoDAO.save(session, connectionInfo);
        auditLogDAO.save(session, auditEntry);
        return null;
    });
} catch (MongoException ex) {
    logger.error("Transaction failed: {}", ex.getMessage());
}
```

**Advanced Features**:
- **Index Management**: Automated index creation and optimization
- **Aggregation Pipeline**: Support for complex data aggregation operations
- **Change Streams**: Real-time data change notifications
- **Bulk Operations**: Efficient batch operations for large datasets
- **Query Performance**: Optimization strategies and performance monitoring
- **Connection Pooling**: Efficient connection management and resource utilization
- **Error Recovery**: Automatic retry mechanisms and connection recovery

---

## 🌐 **Frontend Applications**

### **SyrixPortal - Comprehensive Security Dashboard**

**Modern Security Monitoring Platform**:
- **Version**: 1.0.0 with production-ready architecture
- **Framework**: React 19 + TypeScript frontend, Java 21 + Spring Boot 3.4.2 backend
- **Database**: MongoDB 7.0 with comprehensive document modeling
- **Architecture**: Clean V1-only API with standardized responses

**Ports & Architecture**:
```
Frontend (React 19) → API Gateway (Spring Boot) → Controllers → Services → MongoDB
Port: 3030              Port: 8080/8443        /api/v1/*
```

**Core Dashboard Features**:
- **Real-time Security Monitoring**: Live dashboard with metrics and status tracking
- **Alerts Management**: Comprehensive alert processing, filtering, and notifications
- **Audit Logging**: Complete audit trail with date filtering and export functionality
- **System Configuration**: Centralized management and validation
- **User Management**: Role-based access control
- **OAuth2 Integration**: Microsoft Entra ID and Google Workspace support

**Domain Controllers (28+ Endpoints)**:
- **SystemV1Controller**: Health checks, status verification, deployment validation
- **DashboardV1Controller**: Dashboard data, metrics, user profiles
- **AlertsV1Controller**: Alert management and status updates
- **SystemLogV1Controller**: System logging, filtering, export capabilities
- **AuditV1Controller**: Compliance logging and audit trails
- **RiskV1Controller**: Risk analysis and trending
- **ScanV1Controller**: Security scanning and scheduling
- **NotificationsV1Controller**: Notification management

**Standardized API Response Format**:
```json
{
  "success": true,
  "data": { /* actual response data */ },
  "message": null,
  "error": null,
  "timestamp": "2025-06-03T10:53:02.776Z",
  "requestId": "uuid-request-id",
  "apiVersion": "1.0.0"
}
```

**Frontend Development Structure**:
```
frontend/
├── src/
│   ├── features/           # Feature modules
│   │   ├── alerts/         # Alerts feature with status management
│   │   ├── auditlog/       # Audit log with filtering/export
│   │   │   ├── __tests__/  # Comprehensive testing
│   │   │   ├── redux/      # Redux slice for audit log
│   │   │   └── AuditLogPage.tsx
│   │   ├── dashboard/      # Dashboard with real-time metrics
│   │   ├── auth/           # Authentication flow
│   │   ├── connect/        # Service integrations
│   │   └── notifications/  # Notification system
│   ├── components/
│   │   ├── ui/             # shadcn/ui components
│   │   ├── shared/         # Reusable UI components
│   │   └── layout/         # Layout components
│   ├── app/                # Redux store and hooks
│   ├── api/                # API service layer
│   └── assets/             # Images, icons, Figma exports
```

**Audit Log Feature Implementation**:
- **Components**: AuditLogPage, DatePicker, AuditLogDetailModal
- **Redux State**: logs array, filters, searchTerm, activeTab, sortConfig
- **Capabilities**: Date filtering, application/status/user filtering, search, sort, detailed modal, CSV export

**Frontend Features**:
- **Feature-Based Structure**: Business functionality organization
- **Redux Toolkit**: Comprehensive state management with typed hooks
- **TypeScript Integration**: Strong typing throughout application
- **Audit Log System**: Advanced filtering, search, sorting, CSV export capabilities
- **Responsive Design**: Desktop-optimized with Tailwind CSS

**Development Practices**:
- **TypeScript**: Strong typing for all components and functions
- **Redux**: Follow Redux Toolkit patterns for state management
- **Testing**: Jest/React Testing Library for comprehensive coverage
- **Component Structure**: Single responsibility principle
- **Styling**: Tailwind CSS utility classes with custom Syrix design system

### **SyrixMSP - MSP Portal Interface**

**Simplified MSP Portal**:
- **Target**: Managed Service Providers with streamlined workflows
- **Technology**: React + TypeScript + Redux
- **Philosophy**: Simplified interface for MSP operational efficiency

**MSP-Specific Features**:
- **Client Management**: Multi-tenant overview and management
- **Service Monitoring**: Simplified health tracking across clients
- **Alert Summarization**: Condensed views for quick assessment
- **Streamlined Navigation**: MSP operational efficiency focused

**Recent Enhancements**:
- **Syrix Branding**: Updated visual identity and design system
- **Enhanced UI Components**: Improved user experience and interaction patterns
- **Performance Optimizations**: Faster loading and responsive design improvements

---

## 🔧 **Development & Testing**

### **Backend Development**

**SyrixBackend (Quarkus)**:
```bash
# Development mode with hot reload
mvn quarkus:dev

# Run tests
mvn test

# Build for production
mvn clean package
```

**SyrixPortal Backend (Spring Boot)**:
```bash
# Development server
./mvnw spring-boot:run

# Run tests
./mvnw test

# Build production JAR
./mvnw clean package -DskipTests
```

### **Frontend Development**

**SyrixPortal Frontend**:
```bash
cd SyrixWEB/SyrixPortal/frontend

# Development server
yarn start

# Run tests
yarn test

# Production build
yarn build
```

**SyrixMSP**:
```bash
cd SyrixWEB/SyrixMSP

# Development server
yarn start

# Production build
yarn build
```

### **Testing & Validation**

**Comprehensive API Testing**:
```bash
# SyrixPortal API validation
cd SyrixWEB/SyrixPortal
chmod +x validate-api-migration.sh
./validate-api-migration.sh
```

**Testing Coverage**:
- ✅ All 28+ V1 endpoints functionality
- ✅ ApiResponse<T> format validation
- ✅ Error handling verification
- ✅ Frontend compatibility
- ✅ Request/response consistency

---

## 🏭 **Production Deployment**

### **Environment Configuration**

```bash
# Backend Configuration
MONGODB_URI=mongodb://localhost:27017/syrix_production
MONGODB_DATABASE=syrix_production

# Server Configuration
SERVER_PORT=8080
BACKEND_PORT=8989

# OAuth2 Configuration
OAUTH2_TENANT_ID=your-tenant-id
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret

# Security Configuration
JWT_SECRET=your-jwt-secret
CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

### **Production Startup**

**Backend Services**:
```bash
# SyrixBackend (Quarkus)
java -jar target/syrix-backend-runner.jar

# SyrixPortal Backend (Spring Boot)
java -jar target/SyrixPortal-1.0-SNAPSHOT.jar \
  --spring.profiles.active=production
```

**Frontend Deployment**:
```bash
# Build optimized frontend
cd SyrixWEB/SyrixPortal/frontend && yarn build
cd SyrixWEB/SyrixMSP && yarn build

# Serve static files with nginx/apache
```

---

## 🔒 **Security Features**

### **Enterprise Security Standards**

- ✅ **OAuth2 Authentication** with Microsoft Entra ID
- ✅ **Certificate-Based Authentication** for automated systems
- ✅ **JWT Token Management** with refresh capabilities
- ✅ **CORS Configuration** with domain restrictions
- ✅ **Request Rate Limiting** to prevent abuse
- ✅ **Input Validation** on all API endpoints
- ✅ **Audit Logging** with comprehensive trail
- ✅ **Multi-Tenant Isolation** with secure data separation

### **Security Headers**

Automatic security header configuration:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

---

## 📁 **Project Structure**

```
/Users/<USER>/Documents/Development/private/syrix/
├── SyrixBackend/                    # Core Security Engine
│   ├── src/main/java/io/syrix/     # Main application code
│   │   ├── main/                   # Application context
│   │   ├── products.microsoft/     # Microsoft 365 integrations
│   │   ├── protocols/              # API clients
│   │   ├── common/                 # Shared utilities
│   │   ├── worker/                 # Task processing
│   │   ├── reports/                # OPA policy evaluation
│   │   └── service/                # Service orchestration
│   └── docs/                       # Backend documentation
│
├── SyrixCommon/                     # Shared Foundation
│   ├── SyrixDM/                    # Data Models (POJOs)
│   ├── SyrixCommonServices/        # DAO Implementation
│   │   ├── src/main/java/          # MongoDB DAO implementation
│   │   └── README-MONGODB.md       # MongoDB documentation
│   └── SyrixMessaging/             # Task Queue System
│
├── SyrixWEB/                       # Frontend Applications
│   ├── SyrixPortal/                # Main Security Dashboard
│   │   ├── src/main/java/io/syrix/ # Spring Boot backend
│   │   ├── frontend/               # React 19 + TypeScript
│   │   │   ├── src/features/       # Feature organization
│   │   │   ├── src/components/     # UI components
│   │   │   └── src/app/            # Redux store
│   │   ├── validate-api-migration.sh # API testing script
│   │   └── README.md               # Portal documentation
│   │
│   └── SyrixMSP/                   # MSP Interface
│       ├── src/assets/             # Syrix branding assets
│       ├── src/components/         # React components
│       ├── src/redux/              # MSP state management
│       └── README.md               # MSP documentation
│
└── README.md                       # This file
```

---

## 🤝 **Contributing**

### **Development Workflow**

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation

3. **Test Changes**
   ```bash
   # Backend tests
   mvn test
   
   # Frontend tests
   yarn test
   
   # API validation
   ./validate-api-migration.sh
   ```

4. **Submit Pull Request**
   - Ensure all tests pass
   - Include clear description
   - Reference related issues

### **Code Style**

- **Backend**: Follow Google Java Style Guide
- **Frontend**: Use Prettier and ESLint configurations
- **Commits**: Use conventional commit format
- **Package Naming**: All Java code uses `io.syrix` package structure

---

## 📚 **Documentation**

### **Module-Specific Documentation**

- **SyrixBackend**: `/SyrixBackend/docs/syrix-oauth-authentication-analysis.md`
- **SyrixPortal**: `/SyrixWEB/SyrixPortal/README.md`
- **SyrixMSP**: `/SyrixWEB/SyrixMSP/README.md`
- **Data Models**: `/SyrixCommon/SyrixDM/README.md`
- **Database Layer**: `/SyrixCommon/SyrixCommonServices/README.md`
- **MongoDB Implementation**: `/SyrixCommon/SyrixCommonServices/README-MONGODB.md`

### **API Documentation**

When applications are running:
- **SyrixBackend API**: http://localhost:8989/docs
- **SyrixPortal Swagger**: http://localhost:8080/swagger-ui.html
- **OpenAPI Spec**: http://localhost:8080/v3/api-docs

---

## 📄 **License**

This project is proprietary software. All rights reserved.

---

## 📞 **Support**

For technical support or questions:

- **Documentation**: Check module-specific README files
- **API Testing**: Use provided validation scripts
- **Issues**: Review application logs
- **Database**: Verify MongoDB connection and collections

---

## 🎉 **Features & Highlights**

### **✅ Enterprise-Ready Architecture**
- **Multi-Module Design**: Clean separation of concerns
- **Microservices Ready**: Scalable architecture design
- **Production Deployment**: Complete deployment documentation
- **Security Hardening**: Enterprise-grade security features

### **✅ Modern Development Stack**
- **Java 21**: Latest LTS with modern language features
- **Quarkus 3.23.0**: Cloud-native Java framework
- **React 19**: Cutting-edge frontend with concurrent features
- **TypeScript**: Type-safe development with excellent IDE support

### **✅ Microsoft 365 Integration**
- **Comprehensive API Coverage**: All major Microsoft 365 services
- **Multi-Cloud Support**: Commercial and government cloud environments
- **Certificate Authentication**: Enterprise-grade security
- **Real-time Configuration**: Live security configuration analysis

### **✅ Developer Experience**
- **Hot Reload**: Both frontend and backend live reloading
- **Comprehensive Testing**: Automated validation for all components
- **API Documentation**: Auto-generated documentation
- **Development Tools**: Complete development environment setup

---

**🚀 Ready for enterprise Microsoft 365 security management!**