# Syrix Project Knowledge & Memory System

Full-stack Microsoft 365 security platform with comprehensive codebase memory.

## Core Modules

**SyrixBackend** (Quarkus Java 21, Port 8989): Microsoft 365 security retrieval/analysis with MSAL4J auth, OPA compliance, LangChain4j AI remediation, async processing, pluggable storage (local/S3), ActiveMQ Artemis messaging.

**SyrixCommon Structure**:
- **SyrixDM**: Pure POJO data models (Alert, Company, ConnectionInfo, Risk, SecurityMetrics, etc.)
- **SyrixDAO**: Generic DAO pattern with MongoDB implementation and transactions  
- **SyrixMessaging**: Distributed task queue system

**SyrixWEB**: React 19 + TypeScript dashboards (SyrixPortal: main security, SyrixMSP: simplified MSP).

### Data Architecture

#### **SyrixDM - Clean Data Model Design**

**Philosophy**: Pure POJOs with no database-specific annotations, separation of concerns, standard Java types (LocalDateTime vs DB-specific), zero external dependencies.

**Core Business Models**:
- **Alert**: Security alert representation with status, category, severity
- **AuditLog**: Audit log entry for system operations and compliance tracking
- **Company**: Client/tenant in the Syrix platform with metadata
- **ConnectionInfo**: External service connection information and credentials
- **NotificationData**: Notification data with service information
- **Risk**: Risk assessment for applications or services with scoring
- **ScanStatus**: Security scan status representation and progress tracking
- **SecurityMetrics**: Security status and compliance metrics aggregation
- **SystemLogEntry**: Detailed system operation logs with categorization
- **UserProfile**: User profile information and preferences

**OAuth & Authentication Models**:
- **OAuthConfig**: Base interface for OAuth2 configurations
- **MSALOAuth2Config**: Microsoft-specific OAuth2 configuration implementation

**ServiceType Enum**: Available service types for Microsoft 365 integration

**Design Pattern**: Default constructors, required field constructors, full constructors with all fields, standard getters/setters, no runtime dependencies on DAOs or ORM frameworks.

#### **SyrixDAO - Database Access Layer**

**Interface-Based Design Philosophy**: Framework-independent database access with generic DAO pattern, DAOFactory for implementation selection, transaction support, type safety through generics.

**Core DAO Architecture**:
```java
// Generic DAO interface
public interface DAO<T> {
    void save(T entity);
    void update(T entity);
    void delete(T entity);
    T findById(String id);
    List<T> findAll();
}

// Specialized DAOs with domain-specific methods
public interface CompanyDAO extends DAO<Company> {
    Company findByName(String name);
    List<Company> findByStatus(String status);
}

public interface ConnectionInfoDAO extends DAO<ConnectionInfo> {
    List<ConnectionInfo> findByServiceType(ServiceType serviceType);
    ConnectionInfo findByCompanyAndService(String companyId, ServiceType serviceType);
}
```

**Factory Pattern Implementation**: DAOFactory provides implementation selection and abstracts concrete database technology choices.

#### **MongoDB Implementation**

**Framework-Independent MongoDB Access**: Pure MongoDB driver usage without Spring/JPA dependencies, multi-document transaction support with automatic rollback, connection management with built-in pooling and lifecycle management, comprehensive exception handling.

**Transaction Management**:
```java
try (ClientSession session = mongoClient.startSession()) {
    session.withTransaction(() -> {
        companyDAO.save(session, company);
        connectionInfoDAO.save(session, connectionInfo);
        auditLogDAO.save(session, auditEntry);
        return null;
    });
} catch (MongoException ex) {
    logger.error("Transaction failed: {}", ex.getMessage());
}
```

**Features**: Index management, aggregation pipeline support, change streams for real-time data, bulk operations, query performance optimization, connection pooling, error recovery mechanisms.

### Frontend Architecture

#### **SyrixPortal - Comprehensive Security Dashboard**

**Version**: 1.0.0 with production-ready architecture
**Framework**: React 19 + TypeScript frontend, Java 21 + Spring Boot 3.4.2 backend
**Database**: MongoDB 7.0 with comprehensive document modeling
**API**: Clean V1-only architecture with standardized responses

**Ports & Architecture**:
```
Frontend (React 19) → API Gateway (Spring Boot) → Controllers → Services → MongoDB
Port: 3030              Port: 8080/8443        /api/v1/*
```

**Core Dashboard Features**:
- **Real-time Security Monitoring**: Live dashboard with metrics and status tracking
- **Alerts Management**: Comprehensive alert processing, filtering, and notifications
- **Audit Logging**: Complete audit trail with date filtering and export functionality
- **System Configuration**: Centralized management and validation
- **User Management**: Role-based access control
- **OAuth2 Integration**: Microsoft Entra ID and Google Workspace support

**Domain Controllers (28+ Endpoints)**:
- **SystemV1Controller**: Health checks, status verification, deployment validation
- **DashboardV1Controller**: Dashboard data, metrics, user profiles
- **AlertsV1Controller**: Alert management and status updates
- **SystemLogV1Controller**: System logging, filtering, export capabilities
- **AuditV1Controller**: Compliance logging and audit trails
- **RiskV1Controller**: Risk analysis and trending
- **ScanV1Controller**: Security scanning and scheduling
- **NotificationsV1Controller**: Notification management

**Standardized API Response Format**:
```json
{
  "success": true,
  "data": { /* actual response data */ },
  "message": null,
  "error": null,
  "timestamp": "2025-06-03T10:53:02.776Z",
  "requestId": "uuid-request-id",
  "apiVersion": "1.0.0"
}
```

**Frontend Features**:
- **Feature-Based Structure**: Business functionality organization
- **Redux Toolkit**: Comprehensive state management with typed hooks
- **TypeScript Integration**: Strong typing throughout application
- **Audit Log System**: Advanced filtering, search, sorting, CSV export capabilities
- **Responsive Design**: Desktop-optimized with Tailwind CSS

#### **SyrixMSP - MSP Portal Interface**

**Target**: Managed Service Providers with streamlined workflows
**Technology**: React + TypeScript + Redux
**Philosophy**: Simplified interface for MSP operational efficiency

**MSP-Specific Features**:
- **Client Management**: Multi-tenant overview and management
- **Service Monitoring**: Simplified health tracking across clients
- **Alert Summarization**: Condensed views for quick assessment
- **Streamlined Navigation**: MSP operational efficiency focused

#### **Frontend Development Structure**

**Feature-Based Organization**:
```
frontend/
├── src/
│   ├── features/           # Feature modules
│   │   ├── alerts/         # Alerts feature with status management
│   │   ├── auditlog/       # Audit log with filtering/export
│   │   │   ├── __tests__/  # Comprehensive testing
│   │   │   ├── redux/      # Redux slice for audit log
│   │   │   └── AuditLogPage.tsx
│   │   ├── dashboard/      # Dashboard with real-time metrics
│   │   ├── auth/           # Authentication flow
│   │   ├── connect/        # Service integrations
│   │   └── notifications/  # Notification system
│   ├── components/
│   │   ├── ui/             # shadcn/ui components
│   │   ├── shared/         # Reusable UI components
│   │   └── layout/         # Layout components
│   ├── app/                # Redux store and hooks
│   ├── api/                # API service layer
│   └── assets/             # Images, icons, Figma exports
```

**Audit Log Feature Implementation**:
- **Components**: AuditLogPage, DatePicker, AuditLogDetailModal
- **Redux State**: logs array, filters, searchTerm, activeTab, sortConfig
- **Capabilities**: Date filtering, application/status/user filtering, search, sort, detailed modal, CSV export

**Development Practices**:
- **TypeScript**: Strong typing for all components and functions
- **Redux**: Follow Redux Toolkit patterns for state management  
- **Testing**: Jest/React Testing Library for comprehensive coverage
- **Component Structure**: Single responsibility principle
- **Styling**: Tailwind CSS utility classes with custom Syrix design system

**Stack**: Redux Toolkit, TypeScript, Tailwind CSS, shadcn/ui, React Router v7, Axios, Recharts/Chart.js, Playwright testing.

**Production Features**: Maven JAR packaging, optimized React build, Docker support, health monitoring, automated API testing, comprehensive validation scripts.


### Tech Stack

**Backend**: Quarkus 3.23.0, Java 21, MSAL4J, Graph API + PowerShell, OPA/Rego compliance, LangChain4j AI, Jackson JSON/YAML, ActiveMQ Artemis, pluggable storage (local/S3), SLF4J logging.

**Frontend**: React 19, TypeScript, Redux Toolkit, Tailwind + shadcn/ui, React Router v7, Axios, Recharts/Chart.js, Playwright testing.

**Shared**: MongoDB 5.5.0, pure POJO models, generic DAO pattern, MSAL OAuth2.

## OAuth & Microsoft Integration

### **Comprehensive OAuth2 Authentication System**

**MSTokenGeneratorFactory Pattern**: Dual authentication flows with Factory Pattern implementation:

**Certificate-Based (Application Flow)**:
- **Implementation**: `MSCertBasedTokenGenerator` family (Graph, Outlook, Management, SharePoint PowerShell, Teams PowerShell)
- **Grant Type**: `client_credentials`
- **Use Case**: Enterprise service-to-service authentication
- **Pattern**: `MSTokenGeneratorFactory.getInstance().getMGraphTokenGenerator(true, params)` // true = certificate-based

**Refresh Token-Based (Delegated Flow)**:
- **Implementation**: `MSRefreshTokenBasedTokenGenerator` family (Graph, Outlook, Management)
- **Grant Type**: `refresh_token` 
- **Use Case**: User-delegated authentication for user-context operations
- **Pattern**: `MSTokenGeneratorFactory.getInstance().getMGraphTokenGenerator(false, params)` // false = refresh token-based

**Multi-Cloud Support**: Commercial, GCC, GCC High, DOD environments with different endpoints for authentication URLs, Graph API, Outlook/Exchange, Power Platform, Security & Compliance.

**Service-Specific Scopes**:
```java
MS_GRAPH_SCOPE_URL = "https://graph.microsoft.com/.default"
MS_MANAGMENT_SCOPE_URL = "https://management.azure.com/.default" 
MS_OUTLOOK_SCOPE_URL = "https://outlook.office365.com/.default"
```

**Token Management**: In-memory caching with 5-minute expiration buffer, automatic refresh, per-service tokens, volatile storage (tokens lost on restart).

**Permission Model**: ApplicationPermission (app-only) with appRoleId/appRoleDisplayName/appRoleValue, DelegatedPermission (user-delegated) with scope, base Permission with resourceId/resourceDisplayName/consentType.

**Configuration**: MSALOAuth2Config with clientId/clientSecret/redirectUri/scopes/authUrl/tokenUrl/serviceType, default values for localhost:8080 callback and standard scopes.

**Implementation Patterns**: CompletableFuture<String> tokenFuture, Bearer token HTTP headers, MSTokenGenerator.Params builder pattern.

**Current Limitations**: Limited delegated flow integration, no persistent token storage, static flow selection, minimal user context tracking. **Recommendations**: Comprehensive delegated flow support, dynamic flow selection, persistent token storage, enhanced permission handling.

**Security**: X.509 certificates, encrypted tokens, hardware security module support, role-based access, compliance tracking, automatic token rotation.

## Memory System

**Initialization**: Start with "Remembering...", load knowledge graph with recent changes, active services/components, full-stack knowledge.

### **🚨 CRITICAL: Always Check Before Answering & Focus on Current Requests**

**MANDATORY RULE 1**: **NEVER answer user questions about what has been done, what exists, or current state WITHOUT first checking files, searching code, or using appropriate tools to validate the actual current state.**

**Examples of questions requiring checks BEFORE answering**:
- "Did you add X?"  →  **MUST check files/search first**
- "Is Y already implemented?"  →  **MUST search codebase first**  
- "What's the current status of Z?"  →  **MUST check actual files first**
- "Does the code contain W?"  →  **MUST search/grep first**

**MANDATORY RULE 2**: **ALWAYS focus on the user's CURRENT request and ask for clarification when needed. NEVER assume or continue previous tasks unless explicitly requested.**

**Critical Guidelines**:
1. **Focus on current request** - Address exactly what the user is asking for NOW
2. **Ask for clarification** - When user requests are unclear or ambiguous, ask specific questions  
3. **Don't assume context** - Never assume the user wants previous conversation tasks completed
4. **Validate understanding** - Confirm what the user wants before proceeding with work

**Example of WRONG approach**:
- User asks: "Add design documentation before code creation stage"
- WRONG: Continue previous OAuth/README integration task from conversation summary
- CORRECT: Focus on design documentation request and ask for clarification about specifics

**WHY THIS IS CRITICAL**:
- Prevents duplicate work from incorrect assumptions
- Avoids wasting user time with wrong information  
- Maintains accuracy and trust in responses
- Prevents confusion and inefficient workflows
- Ensures work aligns with actual user intent

**PROCESS**:
1. **Read current request carefully** - What is the user asking for NOW?
2. **Use tools FIRST** - If asking about current state, check/search before answering
3. **Ask for clarification** - If request is ambiguous or unclear
4. **Focus on current task** - Don't assume continuation of previous work
5. **Provide accurate response** - Based on actual findings and current request

**CodeEntity Types**: CLASS, SERVICE, CLIENT, MODEL, COMPONENT, DESIGN_DOCUMENT, USER_REQUEST, BEHAVIORAL_PATTERN, etc. 

**Properties**: name, path, lastModified, complexity, microsoftServices, apiEndpoints, usedComponents, darkModeSupport, designApprovalStatus, implementationReadiness, requestContext, clarificationNeeded, assumptionsMade.

**Behavioral Entity Tracking**: Track USER_REQUEST entities with properties for request clarity, assumed context, clarification status, and alignment with actual user intent. Track BEHAVIORAL_PATTERN entities for response patterns that should be avoided (continuing previous tasks without confirmation, answering without checking, making assumptions about user intent).

**Patterns**: 
- **Frontend**: shadcn/ui in `/src/components/ui/`, `cn()` utility, dark/light mode, React.FC typing, Redux hooks, Syrix colors
- **Backend**: `io.syrix.products.microsoft.*`, CompletableFuture async, specialized exceptions, Jackson JSON, OPA compliance
- **Cross-Stack**: API integration patterns, token management, error propagation

### **🚨 CRITICAL: Syrix Development Methodology - NO @ApplicationScoped**

**MANDATORY RULE**: **NEVER use @ApplicationScoped annotation in Syrix development**

**Correct Syrix Pattern**:
```java
@PolicyRemediator("MS.{SERVICE}.{SECTION}.{POLICY}v{VERSION}")
public class ExampleRemediator extends RemediatorBase {
    // CORRECT: Only @PolicyRemediator annotation
    // WRONG: @ApplicationScoped should NEVER be used
}
```

**Why @ApplicationScoped is forbidden**:
- Not part of Syrix development infrastructure  
- User has explicitly stated this multiple times
- Conflicts with Syrix methodology and architecture patterns
- Use constructor injection and @PolicyRemediator pattern instead

**Ignore existing codebase examples that use @ApplicationScoped** - they are incorrect and should not be followed for new code generation.

**Organization**: Frontend (`/src/features/`, `/src/components/`), Backend (`/io/syrix/products/`, `/io/syrix/common/`), track migrations and integrations.

### **🛠️ Command Development Lessons (Critical for Future Sessions)**

#### **MCP Tool Reliability Patterns**
- **NEVER assume MCP tools work**: Always implement execution barriers and validation checkpoints
- **Tool Syntax Consistency**: ALL MCP tools must use `mcp__server__tool` format (NO colon syntax like `server:tool`)
- **Hard Validation Gates**: Implement checkpoints that cannot be bypassed with soft validation
- **Real Data Verification**: Multiple layers of authenticity checks for external data retrieval

#### **Common MCP Tool Syntax Issues**
- ❌ **WRONG**: `desktop-commander:search_files`, `memento:create_entities`
- ✅ **CORRECT**: `mcp__desktop-commander__search_files`, `mcp__memento__create_entities`
- **Standard Pattern**: `mcp__[server-name]__[tool-name]`

#### **Anti-Hallucination Strategies**
- **Execution Barriers**: Mandatory MCP tool calls with validation checkpoints
- **Data Extraction**: Force explicit field extraction from real tool responses
- **User Confirmation**: Cannot proceed without explicit user approval of retrieved data
- **Specific Detection**: Include known hallucination patterns with hard stops

#### **Dynamic Research Implementation**
- **AVOID**: Hardcoded technology assumptions (e.g., always Microsoft Graph API)
- **IMPLEMENT**: Content-based domain identification from actual requirements
- **USE**: Sequential thinking for technical domain discovery
- **RESEARCH**: Only relevant technologies mentioned in real data

#### **Version Management Protocol**
- **ALWAYS update version** after significant changes
- **BACKUP original** before major modifications (`.bck` extension)
- **DOCUMENT changes** in version comments with specific improvements
- **TRACK reliability** improvements and syntax fixes

## Workflow & Testing

**Paths**: Frontend (`SyrixWEB/SyrixPortal`), Backend (`SyrixBackend`), Shared (`SyrixCommon`).

**Development Process**: 
1. **Design Phase**: Create comprehensive design documents before code generation (Phase 4.5 in JIRA-to-Code workflow)
2. **Frontend**: shadcn/ui migration, TypeScript components, dark/light mode implementation  
3. **Backend**: Java services, Microsoft API integration, async operations with CompletableFuture
4. **Integration**: API contracts, error handling, cross-stack compatibility

**Design Documentation**: Comprehensive technical design covering architecture, UI/UX, API integration, security considerations, implementation strategy, and user approval workflow before code generation.

**Testing**: Playwright (frontend, localhost:3030, 1725x1115), JUnit 5/Mockito (backend), Mock Graph API responses.

**Purpose**: Full-stack development with React/TypeScript patterns, Java concurrency, Microsoft cloud integration, shadcn/ui, security analysis. Edit files in-place as needed.

## JIRA-to-Code Automation

**Status**: Production v3.8.2 at `/.claude/commands/syrix-jira-to-code.md` (**MAJOR RELIABILITY UPGRADE**)

**Features**: 10-phase workflow (JIRA analysis → PR creation), MCP integration (Atlassian, GitHub, Figma, Desktop Commander, Memento), 85+ quality standards, @PolicyRemediator patterns, CISA compliance, configuration-rego validation.

**Modes**: Auto, Interactive, Review. 8 parameters for customization.

**Safety**: Memory fallbacks, parameter validation, file operation safety, phase dependencies, enhanced error handling.

**Usage**: `/syrix-jira-to-code jira_item=SYRIX-123 project_path="/path/to/syrix" [mode=interactive] [figma_url="..."]`

### **🚀 v3.8.2 Major Reliability Improvements (2025-07-25)**

**100% EXECUTION RELIABILITY ACHIEVED** - Complete Phase 1 restructure eliminating hallucination risks:

#### **🔒 Bulletproof 5-Barrier Execution System**
- **BARRIER 1**: Mandatory Atlassian authentication with cloudId extraction
- **BARRIER 2**: Real JIRA issue retrieval with JSON validation  
- **BARRIER 3**: Data extraction with authenticity verification
- **BARRIER 4**: Mandatory real data display with content validation
- **BARRIER 5**: User confirmation gate (cannot be bypassed)

#### **🛠️ MCP Tool Syntax Standardization**
- **Fixed 73 instances**: `desktop-commander:` → `mcp__desktop-commander__`
- **Fixed 8 instances**: `memento:` → `mcp__memento__`
- **Result**: 100% consistent `mcp__server__tool` format (138 total tools)

#### **🧠 Dynamic Domain Knowledge Research** 
- **Removed**: Hardcoded Microsoft Graph API research assumptions
- **Added**: Real JIRA content analysis for domain identification
- **Enhanced**: Sequential thinking for technical domain discovery
- **Flexibility**: Adapts to any technology mentioned in JIRA requirements

#### **🔐 Enhanced Security Remediation Analysis**
- **Preserved**: All existing CISA policy ID determination logic
- **Enhanced**: Works with guaranteed real JIRA data (no hallucination)
- **Improved**: Dynamic baseline file selection based on actual service context
- **Complete**: Full @PolicyRemediator 4-component bundle analysis

#### **⚠️ CRITICAL RELIABILITY FIXES**
- **Execution Bypass Prevention**: Hard validation gates that cannot be skipped
- **Hallucination Detection**: Multiple validation checkpoints with specific error patterns
- **Tool Failure Handling**: Comprehensive error recovery strategies
- **Data Authenticity**: Mandatory verification of all retrieved JIRA data

**Backup**: Original v3.7.0 preserved as `syrix-jira-to-code.md.bck`

## Audit Logging & Security

**Architecture**: SLF4J + JBoss LogManager, `RemediatorBase` with logger, `LoggingAgent.java`/`LoggingInitializer.java` initialization.

**Levels**: INFO (audit trail), WARN (non-critical), ERROR (failures), DEBUG (troubleshooting).

**Policy Management**: `@PolicyRemediator("MS.{SERVICE}.{SECTION}.{POLICY}v{VERSION}")`, service mapping (AAD=Entra, EXO=Exchange, SPO=SharePoint, Teams=Teams), `getPolicyId()` extraction.

**4-Component Bundle**: @PolicyRemediator class, ConfigurationService method, Rego policy rule, CISA baseline docs. **Critical**: Configuration-Rego key alignment, policy ID consistency.

**Response Structure**: STATUS_FIELD (SUCCESS/FAILED/REQUIREMENT_MET/etc.), POLICY_ID_FIELD, MESSAGE_FIELD, ParameterChangeResult tracking.

**Storage**: Complete audit trail in local files/AWS S3, `storage.saveRemediationResult()`, historical data with timestamps.

## Project Structure

**Root**: `/Users/<USER>/Documents/Development/private/syrix/`
- **SyrixBackend/**: Quarkus Java 21 engine  
- **SyrixCommon/**: Shared (SyrixDM, SyrixDAO, SyrixMessaging)
- **SyrixWEB/**: Frontend (SyrixPortal, SyrixMSP)

**Backend Packages**: `io.syrix/` → main, products.microsoft/ (base, defender, entra, exo, sharepoint, teams, powerplatform, dynamics), protocols, common, worker, reports, service.

**Frontend Structure**: src/ → features/ (dashboard, alerts, auditlog, auth, connect, notifications), components/ (ui, shared, layout), app/ (Redux).

**Shared Structure**: SyrixDM/ (entities, oauth, enums), SyrixDAO/ (interfaces, mongodb, exceptions), SyrixMessaging/ (models).
