# Syrix JIRA-to-Code Command - How-To Guide with Usage Examples

## 📚 **Complete Guide: From Setup to Production**

This practical guide shows you exactly how to use the Syrix JIRA-to-Code unified command for different development scenarios with real-world examples.

---

## 🚀 **Quick Start Setup**

### **Step 1: Install the Command**
```bash
# Copy the unified command to Claude Code
cp syrix-jira-to-code-unified.md ~/.claude-code/commands/

# Verify installation
ls ~/.claude-code/commands/ | grep syrix
```

### **Step 2: Basic Syntax**
```bash
/syrix-jira-to-code jira_item=<TICKET> project_path="<PATH>" [parameters...]
```

### **Step 3: Your First Command**
```bash
# Most basic usage - fully automated workflow
/syrix-jira-to-code jira_item=SYRIX-123 project_path="/Users/<USER>/Documents/Development/private/syrix"
```

---

## 📋 **Common Usage Scenarios**

### **🔄 Scenario 1: Backend API Development**

**When to use**: Creating REST APIs, service layers, database entities

```bash
# Basic backend feature (auto mode)
/syrix-jira-to-code jira_item=SYRIX-456 project_path="/Users/<USER>/Documents/Development/private/syrix"

# Backend with maximum quality standards
/syrix-jira-to-code jira_item=SYRIX-456 project_path="/Users/<USER>/Documents/Development/private/syrix"

# Review backend code before PR creation
/syrix-jira-to-code jira_item=SYRIX-456 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=review
```

**What gets generated**:
- `SyrixCommon/src/main/java/com/syrix/common/entity/[Entity].java`
- `SyrixBackend/src/main/java/com/syrix/backend/service/[Service].java`
- `SyrixBackend/src/main/java/com/syrix/backend/resource/[Resource].java`
- Unit and integration tests
- API documentation

---

### **🎨 Scenario 2: UI Development with Figma**

**When to use**: Building web interfaces, forms, dashboards with existing designs

```bash
# UI development with Figma design
/syrix-jira-to-code jira_item=SYRIX-789 project_path="/Users/<USER>/Documents/Development/private/syrix" figma_url="https://figma.com/file/abc123/user-dashboard"

# Interactive UI development (step-by-step review)
/syrix-jira-to-code jira_item=SYRIX-789 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=interactive figma_url="https://figma.com/file/abc123/user-dashboard"

# UI with maximum quality standards and asset control
/syrix-jira-to-code jira_item=SYRIX-789 project_path="/Users/<USER>/Documents/Development/private/syrix" figma_url="https://figma.com/file/abc123/user-dashboard" download_assets=true
```

**What gets generated**:
- Spring Boot MVC controllers based on Figma screens
- Thymeleaf templates matching Figma layouts
- CSS stylesheets with extracted design system
- Form DTOs based on Figma form designs
- Downloaded Figma assets (icons, images)

---

### **🔄 Scenario 3: Full-Stack Feature Development**

**When to use**: Complete features requiring both backend APIs and frontend UI

```bash
# Full-stack with Figma (auto mode)
/syrix-jira-to-code jira_item=SYRIX-101 project_path="/Users/<USER>/Documents/Development/private/syrix" figma_url="https://figma.com/file/xyz789/booking-system"

# Full-stack interactive mode for complex features
/syrix-jira-to-code jira_item=SYRIX-101 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=interactive figma_url="https://figma.com/file/xyz789/booking-system"

# Full-stack with review mode (generate everything, manual PR)
/syrix-jira-to-code jira_item=SYRIX-101 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=review figma_url="https://figma.com/file/xyz789/booking-system" create_pr=false
```

**What gets generated**:
- Complete backend: entities, services, REST APIs
- Complete frontend: controllers, templates, styles
- Form handling and validation
- Comprehensive test suite
- API and feature documentation

---

### **🔍 Scenario 4: Code Review and Quality Control**

**When to use**: When you want to review generated code before committing

```bash
# Generate code for review (no PR creation)
/syrix-jira-to-code jira_item=SYRIX-202 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=review

# Maximum quality standards with interactive review
/syrix-jira-to-code jira_item=SYRIX-202 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=interactive

# Review with selective phase execution
/syrix-jira-to-code jira_item=SYRIX-202 project_path="/Users/<USER>/Documents/Development/private/syrix" mode=review skip_phases="2"
```

**Workflow**:
1. Command generates all code and documentation
2. You review the generated files manually
3. Make any necessary adjustments
4. Create PR manually when satisfied

---

### **⚡ Scenario 5: Quick Prototyping and Testing**

**When to use**: Rapid prototyping, POCs, testing new features

```bash
# Skip discovery phase for faster execution
/syrix-jira-to-code jira_item=SYRIX-303 project_path="/Users/<USER>/Documents/Development/private/syrix" skip_phases="2"

# Force UI development even if not detected from JIRA
/syrix-jira-to-code jira_item=SYRIX-303 project_path="/Users/<USER>/Documents/Development/private/syrix" force_ui=true

# Maximum quality rapid prototyping (skip test and quality phases)
/syrix-jira-to-code jira_item=SYRIX-303 project_path="/Users/<USER>/Documents/Development/private/syrix" skip_phases="6,7"
```

**Benefits**:
- Faster execution by skipping certain phases
- Lower quality bar for prototypes
- Force UI generation for testing interfaces

---

## 🎛️ **Parameter Deep Dive with Examples**

### **`mode` Parameter Examples**

#### **Auto Mode (Default)**
```bash
# Fully automated - no user interaction
/syrix-jira-to-code jira_item=SYRIX-404 project_path="/path/to/syrix"

# Auto mode with maximum quality standards
/syrix-jira-to-code jira_item=SYRIX-404 project_path="/path/to/syrix" mode=auto
```

#### **Interactive Mode**
```bash
# Step-by-step confirmations
/syrix-jira-to-code jira_item=SYRIX-505 project_path="/path/to/syrix" mode=interactive

# Interactive with Figma integration
/syrix-jira-to-code jira_item=SYRIX-505 project_path="/path/to/syrix" mode=interactive figma_url="https://figma.com/file/abc/design"
```

**Interactive Mode User Experience**:
```
📋 PHASE 1 ANALYSIS RESULTS - USER REVIEW REQUIRED

JIRA Ticket Summary:
- Ticket: SYRIX-505
- Summary: User Profile Management Dashboard
- Type: Story
- Priority: High

AI Analysis Results:
- Requirement Type: Full-Stack (Backend API + Frontend UI)
- UI Development Required: Yes
- Complexity Assessment: Medium

🔍 USER DECISION POINT: Please review the analysis above and confirm:
1. Does the analysis accurately capture the JIRA requirements?
2. Do you agree with the UI development assessment?
3. Should we proceed with the current configuration?

Options:
- Type "proceed" to continue with current settings
- Type "change mode to review" to switch execution mode
- Type "add figma [URL]" to enable Figma integration
```

#### **Review Mode**
```bash
# Generate code but don't create PR
/syrix-jira-to-code jira_item=SYRIX-606 project_path="/path/to/syrix" mode=review

# Review mode with maximum quality standards
/syrix-jira-to-code jira_item=SYRIX-606 project_path="/path/to/syrix" mode=review
```

### **`skip_phases` Parameter Examples**

```bash
# Skip existing implementation discovery (Phase 2)
/syrix-jira-to-code jira_item=SYRIX-707 project_path="/path/to/syrix" skip_phases="2"

# Skip test generation (Phase 6) and quality assessment (Phase 7)
/syrix-jira-to-code jira_item=SYRIX-707 project_path="/path/to/syrix" skip_phases="6,7"

# Skip documentation phases for rapid development
/syrix-jira-to-code jira_item=SYRIX-707 project_path="/path/to/syrix" skip_phases="8,9"
```

**Phase Reference**:
- Phase 1: JIRA Analysis
- Phase 2: Existing Implementation Discovery
- Phase 3: Project Structure Analysis
- Phase 4: Code Pattern Discovery
- Phase 5: Code Generation
- Phase 6: Test Generation
- Phase 7: Quality Assessment
- Phase 8: Documentation
- Phase 9: Database & Config Documentation
- Phase 10: Git & PR Creation

### **Maximum Quality Standards**

The system always generates code with enterprise-grade quality standards (targeting 85+ score):

```bash
# All features automatically get maximum quality
/syrix-jira-to-code jira_item=SYRIX-808 project_path="/path/to/syrix"

# For rapid prototyping, skip quality assessment phase instead
/syrix-jira-to-code jira_item=SYRIX-808 project_path="/path/to/syrix" skip_phases="7"

# Quality is never compromised - system always aims for best possible code
/syrix-jira-to-code jira_item=SYRIX-808 project_path="/path/to/syrix" mode=interactive
```

**Quality Features**:
- **Enterprise Standards**: Always targets 85+ quality score
- **Comprehensive Testing**: Full unit and integration test coverage
- **Security Review**: Automatic security best practices validation
- **Architecture Compliance**: Ensures consistency with project patterns
- **Documentation**: Complete API and feature documentation

---

## 🎨 **Figma Integration Examples**

### **Design-Driven Development Workflow**

#### **Step 1: Provide Figma URL**
```bash
# Direct Figma integration
/syrix-jira-to-code jira_item=SYRIX-909 project_path="/path/to/syrix" figma_url="https://figma.com/file/abc123/ecommerce-checkout"
```

#### **Step 2: Interactive Figma Review**
```bash
# Interactive mode for design review
/syrix-jira-to-code jira_item=SYRIX-909 project_path="/path/to/syrix" mode=interactive figma_url="https://figma.com/file/abc123/ecommerce-checkout"
```

**Interactive Figma Experience**:
```
🎨 FIGMA DESIGN ANALYSIS - USER REVIEW REQUIRED

Design Overview:
- Figma File: E-commerce Checkout Flow
- Total Frames: 5 screens
- Design System: Material Design with custom colors

UI Components Identified:
1. Pages/Screens: Cart Summary, Shipping Info, Payment, Confirmation
2. Components: Form inputs, buttons, progress indicators
3. Forms: Shipping form (6 fields), Payment form (4 fields)

Design Specifications:
- Color Scheme: Primary: #2563EB, Secondary: #10B981
- Typography: Inter font family, 14px-32px scale
- Layout: 12-column grid, 16px spacing system

🔍 USER DECISION POINT: Please review the Figma design analysis:
1. Are all required UI screens captured in the Figma design?
2. Should we download design assets (icons, images) for the project?
3. Does the integration plan match your UI framework preferences?

Options:
- Type "proceed" to continue with UI development
- Type "download assets" to download Figma assets to project
- Type "modify UI approach" to adjust integration strategy
```

#### **Step 3: Asset Management**
```bash
# Control asset downloads
/syrix-jira-to-code jira_item=SYRIX-909 project_path="/path/to/syrix" figma_url="https://figma.com/file/abc123/design" download_assets=true

# Skip asset downloads for faster execution
/syrix-jira-to-code jira_item=SYRIX-909 project_path="/path/to/syrix" figma_url="https://figma.com/file/abc123/design" download_assets=false
```

---

## 🛠️ **Advanced Usage Patterns**

### **Pattern 1: Team Development Workflow**

#### **For Senior Developers (Auto Mode)**
```bash
# Trusted automated workflow with maximum quality
/syrix-jira-to-code jira_item=SYRIX-1001 project_path="/path/to/syrix"
```

#### **For Junior Developers (Interactive Mode)**
```bash
# Learning-focused interactive workflow with maximum quality
/syrix-jira-to-code jira_item=SYRIX-1002 project_path="/path/to/syrix" mode=interactive
```

#### **For Code Reviews (Review Mode)**
```bash
# Generate for team review
/syrix-jira-to-code jira_item=SYRIX-1003 project_path="/path/to/syrix" mode=review create_pr=false
```

### **Pattern 2: Feature-Based Configuration**

#### **Data Management Features**
```bash
# Backend-heavy features with maximum quality
/syrix-jira-to-code jira_item=SYRIX-1101 project_path="/path/to/syrix" skip_phases="8"
```

#### **User Interface Features**
```bash
# UI-heavy features with design focus
/syrix-jira-to-code jira_item=SYRIX-1102 project_path="/path/to/syrix" force_ui=true figma_url="https://figma.com/file/ui-design"
```

#### **Integration Features**
```bash
# API integration features with maximum quality
/syrix-jira-to-code jira_item=SYRIX-1103 project_path="/path/to/syrix" skip_phases="2,6"
```

### **Pattern 3: Project Phase-Based Usage**

#### **Early Development (Prototyping)**
```bash
# Fast prototyping with maximum quality (skip some phases for speed)
/syrix-jira-to-code jira_item=SYRIX-1201 project_path="/path/to/syrix" skip_phases="7,8,9" create_pr=false
```

#### **Active Development (Standard)**
```bash
# Standard development workflow with maximum quality
/syrix-jira-to-code jira_item=SYRIX-1202 project_path="/path/to/syrix" mode=interactive
```

#### **Pre-Production (High Quality)**
```bash
# Production-ready with full validation and maximum quality
/syrix-jira-to-code jira_item=SYRIX-1203 project_path="/path/to/syrix" mode=review
```

---

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: Command Not Found**
```bash
# Check installation
ls ~/.claude-code/commands/ | grep syrix

# Reinstall if missing
cp syrix-jira-to-code-unified.md ~/.claude-code/commands/
```

### **Issue 2: JIRA Access Errors**
```bash
# Test with review mode first
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" mode=review

# Verify JIRA ticket exists and is accessible
```

### **Issue 3: Figma Integration Issues**
```bash
# Try without Figma first
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" mode=review

# Then add Figma incrementally
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" mode=interactive figma_url="[URL]"
```

### **Issue 4: Quality Assessment Taking Too Long**
```bash
# Skip quality assessment for rapid prototyping
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" skip_phases="7"

# Note: Quality is always maximum when assessment runs
# The system never compromises on code quality standards
```

### **Issue 5: Slow Execution**
```bash
# Skip discovery phases for faster execution
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" skip_phases="2,3,4"

# Minimal workflow
/syrix-jira-to-code jira_item=SYRIX-TEST project_path="/path/to/syrix" skip_phases="2,6,7,8,9" create_pr=false
```

---

## 📊 **Best Practices & Tips**

### **✅ Recommended Approaches**

#### **First-Time Usage**
```bash
# Start with review mode to understand output (maximum quality)
/syrix-jira-to-code jira_item=SYRIX-FIRST project_path="/path/to/syrix" mode=review
```

#### **Production Features**
```bash
# Use interactive mode for important features (maximum quality)
/syrix-jira-to-code jira_item=SYRIX-PROD project_path="/path/to/syrix" mode=interactive
```

#### **Rapid Prototyping**
```bash
# Optimize for speed (maximum quality in core phases)
/syrix-jira-to-code jira_item=SYRIX-PROTO project_path="/path/to/syrix" skip_phases="2,7,8,9"
```

### **🎯 Parameter Selection Guide**

| Scenario | Mode | Quality | Skip Phases | Create PR |
|----------|------|---------|-------------|-----------|
| **Learning** | `interactive` | `75` | None | `false` |
| **Production** | `interactive` | `85-90` | None | `true` |
| **Prototyping** | `auto` | `60-70` | `"2,7,8,9"` | `false` |
| **Code Review** | `review` | `80` | None | `false` |
| **Batch Processing** | `auto` | `75` | None | `true` |

### **🔄 Workflow Recommendations**

#### **Team Workflow Example**
```bash
# 1. Senior developer creates initial implementation
/syrix-jira-to-code jira_item=SYRIX-TEAM project_path="/path/to/syrix" mode=review

# 2. Team reviews generated code

# 3. Junior developer learns by running interactively
/syrix-jira-to-code jira_item=SYRIX-TEAM project_path="/path/to/syrix" mode=interactive

# 4. Final production run (maximum quality)
/syrix-jira-to-code jira_item=SYRIX-TEAM project_path="/path/to/syrix"
```

---

## 📈 **Success Metrics & Validation**

### **Verify Command Success**

#### **Check Generated Files**
```bash
# Backend files
ls SyrixBackend/src/main/java/com/syrix/backend/

# Frontend files (if UI generated)
ls SyrixWEB/src/main/java/com/syrix/web/
ls SyrixWEB/src/main/resources/templates/

# Documentation
ls docs/api/
ls docs/features/
```

#### **Validate Quality Score**
Look for quality assessment in the command output:
```
📊 Quality Assessment Completed
Quality Score: 87/85%
Quality Threshold: 85%
Threshold Met: Yes
```

#### **Check PR Creation**
```bash
# If create_pr=true, check GitHub for new PR
# PR title format: "SYRIX-123: [Descriptive title from JIRA]"
```

### **Performance Benchmarks**

| Workflow Type | Expected Duration | Quality Score | Files Generated |
|---------------|------------------|---------------|-----------------|
| **Backend Only** | 2-4 minutes | 75-85% | 5-8 files |
| **UI with Figma** | 4-7 minutes | 80-90% | 10-15 files |
| **Full-Stack** | 6-10 minutes | 75-90% | 12-20 files |
| **Review Mode** | 3-6 minutes | 70-95% | All (no PR) |

---

## 🎓 **Learning Path**

### **Beginner (Week 1)**
1. **Basic backend feature**: `mode=review` (maximum quality)
2. **Interactive learning**: `mode=interactive`
3. **Simple UI feature**: `force_ui=true`

### **Intermediate (Week 2-3)**
4. **Figma integration**: `figma_url="[design]" mode=interactive`
5. **Quality focus**: `mode=review` (enterprise-grade standards)
6. **Phase customization**: `skip_phases="2,8"`

### **Advanced (Week 4+)**
7. **Production workflows**: `mode=auto` (maximum quality)
8. **Team collaboration**: Various modes for different team members
9. **Custom parameter combinations**: Complex multi-parameter workflows

---

*This how-to guide provides practical, real-world examples for every aspect of the Syrix JIRA-to-Code unified command. Start with the basic examples and progressively use more advanced parameter combinations as you become comfortable with the system.*

**Quick Reference**: Bookmark this guide and refer to the scenario sections for your specific use cases!