#!/bin/bash

# This script extracts a jira_item from key=value arguments and executes a command.
# Usage: ./view_jira.sh jira_item=SYRIX-123 project_path=/path/to/project

# Check for input arguments
if [ "$#" -eq 0 ]; then
    echo "Usage: $0 jira_item=<value> project_path=<value> ..."
    exit 1
fi

# Parse all arguments to find jira_item
jira_item=""
for arg in "$@"; do
    if [[ "$arg" == jira_item=* ]]; then
        jira_item=$(echo "$arg" | sed 's/jira_item=//')
        break
    fi
done

# Check if jira_item was extracted
if [ -z "$jira_item" ]; then
    echo "Error: Could not find jira_item in the arguments."
    exit 1
fi

# Execute the command with the extracted jira_item
acli jira workitem view "$jira_item"
