version: '3.8'

name: Syrix-Local

services:
  mongodb:
    image: mongo:8.0
    container_name: syrix-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: Syrix
    volumes:
      - mongodb_data:/data/db
    networks:
      - syrix-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  activemq:
    image: apache/activemq-artemis:2.41.0
    container_name: syrix-activemq
    restart: unless-stopped
    ports:
      - "61616:61616"  # TCP port for clients
      - "8161:8161"    # Web console
      - "5672:5672"    # AMQP port
    environment:
      ARTEMIS_USER: admin
      ARTEMIS_PASSWORD: admin123
    volumes:
      - activemq_data:/var/lib/artemis-instance
    networks:
      - syrix-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8161/console/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  localstack:
    image: localstack/localstack:latest
    container_name: syrix-localstack
    restart: unless-stopped
    ports:
      - "4566:4566"    # LocalStack main port
    environment:
      - SERVICES=s3
      - DEBUG=1
      - PERSISTENCE=1
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - HOSTNAME_EXTERNAL=localhost
      - LOCALSTACK_WEB_UI=1
    volumes:
      - localstack_data:/var/lib/localstack
    networks:
      - syrix-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4566/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  mongodb_data:
    driver: local
  activemq_data:
    driver: local
  localstack_data:
    driver: local

networks:
  syrix-network:
    driver: bridge