#!/bin/bash

# Complete OpenVPN Installation Script for Amazon Linux 2023
# Based on analysis of existing syrix-openvpn-server configuration
# Replicates the exact setup from the analyzed system

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration based on analysis
SERVER_NAME="openvpn-server"
SERVER_PORT="65120"  # Custom port from analysis
SERVER_PROTOCOL="udp"
SERVER_SUBNET="********"
SERVER_NETMASK="*************"
CIPHER="AES-128-GCM"
AUTH="SHA256"
TUN_MTU="1400"
MSS_FIX="1360"

# DNS servers from analysis
DNS_SERVERS=("*******" "*******" "*******" "*******")

# Directory paths matching analyzed system
OPENVPN_SERVER_DIR="/etc/openvpn/server"
OPENVPN_CLIENT_DIR="/etc/openvpn/client"
EASY_RSA_DIR="/etc/openvpn/easy-rsa"
KEYS_DIR="/etc/openvpn/server/keys"
LOG_DIR="/var/log/openvpn"

# Detected network interface from analysis
MAIN_INTERFACE=""

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" ;;
        "DEBUG") echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" ;;
        "SUCCESS") echo -e "${PURPLE}[SUCCESS]${NC} ${timestamp} - $message" ;;
    esac
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to detect network interface
detect_network_interface() {
    log "INFO" "Detecting network interface..."
    
    # Try to detect main interface like in analysis (ens5)
    MAIN_INTERFACE=$(ip route | grep default | awk '{print $5}' | head -n1)
    
    if [[ -z "$MAIN_INTERFACE" ]]; then
        log "ERROR" "Cannot detect main network interface"
        exit 1
    fi
    
    log "SUCCESS" "Detected main interface: $MAIN_INTERFACE"
}

# Function to get server IP (public IP)
get_server_ip() {
    log "INFO" "Detecting server IP address..."
    
    # Try AWS metadata service first (like in analysis)
    if SERVER_IP=$(curl -s --max-time 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null); then
        log "SUCCESS" "Detected AWS public IP: $SERVER_IP"
        return 0
    fi
    
    # Fallback to external services
    local ip_services=(
        "https://ipinfo.io/ip"
        "https://icanhazip.com"
        "https://checkip.amazonaws.com"
    )
    
    for service in "${ip_services[@]}"; do
        if SERVER_IP=$(curl -s --max-time 5 "$service" 2>/dev/null | tr -d '\n'); then
            if echo "$SERVER_IP" | grep -qE '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'; then
                log "SUCCESS" "Detected public IP: $SERVER_IP"
                return 0
            fi
        fi
    done
    
    log "ERROR" "Cannot detect server IP address"
    exit 1
}

# Function to check system requirements
check_system_requirements() {
    log "INFO" "Checking system requirements..."
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log "ERROR" "This script must be run as root"
        exit 1
    fi
    
    # Check OS - should be Amazon Linux 2023 like analyzed system
    if [[ ! -f /etc/os-release ]]; then
        log "ERROR" "Cannot determine OS version"
        exit 1
    fi
    
    local os_name=$(grep '^NAME=' /etc/os-release | cut -d'"' -f2)
    local os_version=$(grep '^VERSION=' /etc/os-release | cut -d'"' -f2)
    
    log "INFO" "Operating System: $os_name $os_version"
    
    if [[ ! "$os_name" =~ "Amazon Linux" ]]; then
        log "WARN" "This script is designed for Amazon Linux 2023 (like analyzed system)"
    fi
    
    log "SUCCESS" "System requirements check completed"
}

# Function to install required packages
install_packages() {
    log "INFO" "Installing required packages..."
    
    # Update system packages
    log "INFO" "Updating system packages..."
    dnf update -y
    
    # Install EPEL repository
    log "INFO" "Installing EPEL repository..."
    dnf install -y epel-release
    
    # Install OpenVPN and dependencies (matching analyzed system)
    log "INFO" "Installing OpenVPN and dependencies..."
    dnf install -y \
        openvpn \
        easy-rsa \
        openssl \
        iptables-services \
        wget \
        curl \
        net-tools \
        bind-utils \
        policycoreutils-python-utils
    
    # Verify installation matches analyzed versions
    if ! command_exists openvpn; then
        log "ERROR" "OpenVPN installation failed"
        exit 1
    fi
    
    local openvpn_version=$(openvpn --version | head -n1 | awk '{print $2}')
    log "SUCCESS" "OpenVPN installed: $openvpn_version (analyzed system had 2.6.12)"
}

# Function to setup directory structure (matching analyzed system)
setup_directories() {
    log "INFO" "Setting up directory structure..."
    
    # Create directories matching analyzed system
    local directories=(
        "$OPENVPN_SERVER_DIR"
        "$OPENVPN_CLIENT_DIR"
        "$KEYS_DIR"
        "$LOG_DIR"
        "/etc/systemd/system/openvpn-server@.service.d"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log "DEBUG" "Created directory: $dir"
    done
    
    # Set proper permissions matching analyzed system
    chmod 700 "$KEYS_DIR"
    chmod 755 "$OPENVPN_SERVER_DIR"
    chmod 755 "$OPENVPN_CLIENT_DIR"
    chmod 755 "$LOG_DIR"
    
    log "SUCCESS" "Directory structure created successfully"
}

# Function to setup Easy-RSA (matching analyzed PKI structure)
setup_easy_rsa() {
    log "INFO" "Setting up Easy-RSA PKI (matching analyzed system)..."
    
    # Copy Easy-RSA files to match analyzed path: /etc/openvpn/easy-rsa
    if [[ -d /usr/share/easy-rsa ]]; then
        cp -r /usr/share/easy-rsa "$EASY_RSA_DIR"
    elif [[ -d /usr/share/easy-rsa/3 ]]; then
        cp -r /usr/share/easy-rsa/3 "$EASY_RSA_DIR"
    else
        log "ERROR" "Easy-RSA not found in expected locations"
        exit 1
    fi
    
    # Navigate to Easy-RSA directory
    cd "$EASY_RSA_DIR"
    
    # Initialize PKI
    log "INFO" "Initializing PKI..."
    ./easyrsa init-pki
    
    # Create CA certificate
    log "INFO" "Creating CA certificate..."
    echo "yes" | ./easyrsa build-ca nopass
    
    # Generate server certificate
    log "INFO" "Generating server certificate..."
    echo "yes" | ./easyrsa build-server-full "$SERVER_NAME" nopass
    
    # Generate Diffie-Hellman parameters
    log "INFO" "Generating Diffie-Hellman parameters (this may take a while)..."
    ./easyrsa gen-dh
    
    # Generate TLS-Crypt key (matching analyzed system)
    log "INFO" "Generating TLS-Crypt key..."
    openvpn --genkey --secret "$KEYS_DIR/ta.key"
    
    # Copy certificates to keys directory (matching analyzed paths)
    cp "$EASY_RSA_DIR/pki/ca.crt" "$KEYS_DIR/"
    cp "$EASY_RSA_DIR/pki/issued/$SERVER_NAME.crt" "$KEYS_DIR/server.crt"
    cp "$EASY_RSA_DIR/pki/private/$SERVER_NAME.key" "$KEYS_DIR/server.key"
    cp "$EASY_RSA_DIR/pki/dh.pem" "$KEYS_DIR/"
    
    # Set proper permissions
    chmod 600 "$KEYS_DIR/server.key"
    chmod 600 "$KEYS_DIR/ta.key"
    
    log "SUCCESS" "Easy-RSA setup completed (PKI structure matches analyzed system)"
}

# Function to create server configuration (exact match to analyzed config)
create_server_config() {
    log "INFO" "Creating OpenVPN server configuration (matching analyzed system)..."
    
    local config_file="$OPENVPN_SERVER_DIR/server.conf"
    
    cat > "$config_file" << EOF
# Syrix OpenVPN Server Configuration - FINAL SERVER-SIDE FIX
# Explicit server ping settings + keepalive for clients

port $SERVER_PORT
proto $SERVER_PROTOCOL
dev tun

# Certificate and key files
ca $KEYS_DIR/ca.crt
cert $KEYS_DIR/server.crt
key $KEYS_DIR/server.key
dh $KEYS_DIR/dh.pem
tls-crypt $KEYS_DIR/ta.key

# VPN network configuration
server $SERVER_SUBNET $SERVER_NETMASK
topology subnet
ifconfig-pool-persist $LOG_DIR/ipp.txt

# Crypto configuration
auth $AUTH
cipher $CIPHER
data-ciphers $CIPHER
tls-version-min 1.2

# MTU optimization
tun-mtu $TUN_MTU
mssfix $MSS_FIX

# EXPLICIT SERVER-SIDE PING SETTINGS (forces server to use correct timeout)
ping 10
ping-restart 60

# CLIENT-SIDE SETTINGS (pushed to clients)
push "ping 10"
push "ping-restart 60"
push "ping-timer-rem"

# Client routing
push "redirect-gateway def1 bypass-dhcp"

# DNS Configuration
EOF

    # Add DNS servers from analysis
    for dns in "${DNS_SERVERS[@]}"; do
        echo "push \"dhcp-option DNS $dns\"" >> "$config_file"
    done

    cat >> "$config_file" << EOF

# Client-to-client communication
client-to-client

# Connection management - Reduced timeouts for faster recovery
connect-retry 5 30
hand-window 30
tls-timeout 300

# TLS renegotiation - 8 hours (prevents 1-hour disconnects)
reneg-sec 28800

# Logging - Enhanced for debugging
status $LOG_DIR/status.log 10
verb 4
mute 10

# Security
user nobody
group nobody
persist-key
persist-tun

# Performance
explicit-exit-notify 1
script-security 2
fast-io

# Buffer optimization
sndbuf 0
rcvbuf 0
push "sndbuf 393216"
push "rcvbuf 393216"

# Connection stability
resolv-retry infinite
push "persist-key"
push "persist-tun"

# Process management - Prevent hanging after timeouts
ping-exit 60
EOF

    log "SUCCESS" "Server configuration created: $config_file (matches analyzed system)"
}

# Function to setup system user (matching analyzed system)
setup_openvpn_user() {
    log "INFO" "Setting up OpenVPN system user..."
    
    # Create openvpn user if it doesn't exist (analyzed system uses 'nobody')
    if ! id -u openvpn >/dev/null 2>&1; then
        useradd -r -s /sbin/nologin openvpn
        log "SUCCESS" "Created openvpn system user"
    else
        log "INFO" "OpenVPN user already exists"
    fi
    
    # Set ownership (matching analyzed system permissions)
    chown -R root:openvpn "$OPENVPN_SERVER_DIR"
    chown -R root:root "$KEYS_DIR"
    chown -R nobody:nobody "$LOG_DIR"
    
    # Create nobody user if doesn't exist (analyzed system runs as nobody)
    if ! id -u nobody >/dev/null 2>&1; then
        useradd -r -s /sbin/nologin nobody
    fi
}

# Function to configure network (matching analyzed iptables rules)
configure_network() {
    log "INFO" "Configuring network settings (matching analyzed system)..."
    
    # Enable IP forwarding (analyzed system has this enabled)
    echo 'net.ipv4.ip_forward = 1' > /etc/sysctl.d/99-openvpn.conf
    echo 'net.ipv4.conf.all.forwarding = 1' >> /etc/sysctl.d/99-openvpn.conf
    echo 'net.ipv4.conf.all.accept_redirects = 0' >> /etc/sysctl.d/99-openvpn.conf
    echo 'net.ipv4.conf.all.send_redirects = 1' >> /etc/sysctl.d/99-openvpn.conf
    sysctl -p /etc/sysctl.d/99-openvpn.conf
    
    log "SUCCESS" "Network sysctl parameters configured (matching analyzed system)"
}

# Function to configure iptables (exact match to analyzed rules)
configure_iptables() {
    log "INFO" "Configuring iptables rules (matching analyzed system exactly)..."
    
    # Clear existing rules
    iptables -F
    iptables -X
    iptables -t nat -F
    iptables -t nat -X
    iptables -t mangle -F
    iptables -t mangle -X
    iptables -t raw -F
    iptables -t raw -X
    
    # Set default policies (matching analyzed system)
    iptables -P INPUT DROP
    iptables -P FORWARD DROP
    iptables -P OUTPUT ACCEPT
    
    # INPUT rules (matching analyzed system exactly)
    iptables -A INPUT -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
    iptables -A INPUT -i lo -j ACCEPT
    iptables -A INPUT -p tcp -m tcp --dport 22 -j ACCEPT
    iptables -A INPUT -p udp -m udp --dport 1194 -j ACCEPT
    iptables -A INPUT -p udp -m udp --dport $SERVER_PORT -j ACCEPT
    
    # FORWARD rules (matching analyzed system exactly)
    iptables -A FORWARD -m conntrack --ctstate INVALID -j LOG --log-prefix "CT_INVALID: "
    iptables -A FORWARD -i tun0 -o eth0 -j ACCEPT
    iptables -A FORWARD -i eth0 -o tun0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
    iptables -A FORWARD -i tun0 -o $MAIN_INTERFACE -j ACCEPT
    iptables -A FORWARD -i $MAIN_INTERFACE -o tun0 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
    
    # RAW table rules (matching analyzed system)
    iptables -t raw -A PREROUTING -p icmp -m icmp --icmp-type 3/3 -j DROP
    
    # NAT rules (matching analyzed system exactly)
    iptables -t nat -A POSTROUTING -s $SERVER_SUBNET/24 -o $MAIN_INTERFACE -j MASQUERADE
    
    # Save iptables rules
    iptables-save > /etc/iptables/rules.v4
    
    # Enable and start iptables service
    systemctl enable iptables
    systemctl start iptables || true
    
    log "SUCCESS" "IPTables rules configured (exact match to analyzed system)"
}

# Function to create systemd service override (matching analyzed system)
create_systemd_service() {
    log "INFO" "Creating systemd service configuration..."
    
    # Create override.conf (analyzed system has this)
    cat > "/etc/systemd/system/openvpn-server@.service.d/override.conf" << EOF
[Unit]
Description=OpenVPN service for %i
After=network.target

[Service]
Type=notify
PrivateTmp=true
WorkingDirectory=/etc/openvpn/server
ExecStart=
ExecStart=/usr/sbin/openvpn --status /run/openvpn-server/status-%i.log --status-version 2 --suppress-timestamps --config /etc/openvpn/server/%i.conf
CapabilityBoundingSet=CAP_IPC_LOCK CAP_NET_ADMIN CAP_NET_BIND_SERVICE CAP_NET_RAW CAP_SETGID CAP_SETUID CAP_SYS_CHROOT CAP_DAC_OVERRIDE CAP_AUDIT_WRITE
LimitNPROC=100
DeviceAllow=/dev/null rw
DeviceAllow=/dev/net/tun rw
ProtectSystem=true
ProtectHome=true
KillMode=process
RestartSec=5s
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable openvpn-server@server
    
    log "SUCCESS" "Systemd service configured (matching analyzed system)"
}

# Function to start OpenVPN service
start_openvpn_service() {
    log "INFO" "Starting OpenVPN service..."
    
    # Start the service
    systemctl start openvpn-server@server
    
    # Check service status
    if systemctl is-active --quiet openvpn-server@server; then
        log "SUCCESS" "OpenVPN service started successfully"
        
        # Show service status
        systemctl status openvpn-server@server --no-pager
        
        # Show if TUN interface is created
        sleep 3
        if ip addr show tun0 >/dev/null 2>&1; then
            log "SUCCESS" "TUN interface created successfully"
            ip addr show tun0
        else
            log "WARN" "TUN interface not yet created (may need a moment)"
        fi
    else
        log "ERROR" "Failed to start OpenVPN service"
        systemctl status openvpn-server@server --no-pager
        exit 1
    fi
}

# Function to create client certificate (using analyzed PKI structure)
create_client_cert() {
    local client_name="$1"
    
    log "INFO" "Creating client certificate for: $client_name"
    
    cd "$EASY_RSA_DIR"
    
    # Generate client certificate
    echo "yes" | ./easyrsa build-client-full "$client_name" nopass
    
    # Create client configuration file
    local client_config="$OPENVPN_CLIENT_DIR/$client_name.ovpn"
    
    cat > "$client_config" << EOF
# OpenVPN Client Configuration for $client_name
# Generated for Syrix OpenVPN Server

client
dev tun
proto $SERVER_PROTOCOL
remote $SERVER_IP $SERVER_PORT
resolv-retry infinite
nobind
persist-key
persist-tun

# Security (matching server)
cipher $CIPHER
auth $AUTH
tls-version-min 1.2
remote-cert-tls server
key-direction 1

# Performance (matching server settings)
tun-mtu $TUN_MTU
mssfix $MSS_FIX

# Logging
verb 3
mute 20

# Certificates (embedded)
<ca>
$(cat "$KEYS_DIR/ca.crt")
</ca>

<cert>
$(cat "$EASY_RSA_DIR/pki/issued/$client_name.crt")
</cert>

<key>
$(cat "$EASY_RSA_DIR/pki/private/$client_name.key")
</key>

<tls-crypt>
$(cat "$KEYS_DIR/ta.key")
</tls-crypt>
EOF

    log "SUCCESS" "Client configuration created: $client_config"
}

# Function to create management scripts
create_management_scripts() {
    log "INFO" "Creating management scripts..."
    
    # Create client creation script
    cat > "/usr/local/bin/openvpn-add-client" << 'EOF'
#!/bin/bash
# OpenVPN Client Addition Script

if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <client_name>"
    exit 1
fi

CLIENT_NAME="$1"
EASY_RSA_DIR="/etc/openvpn/easy-rsa"
CLIENT_DIR="/etc/openvpn/client"
KEYS_DIR="/etc/openvpn/server/keys"

# Check if client already exists
if [[ -f "$EASY_RSA_DIR/pki/issued/$CLIENT_NAME.crt" ]]; then
    echo "Error: Client $CLIENT_NAME already exists"
    exit 1
fi

# Generate client certificate
cd "$EASY_RSA_DIR"
echo "yes" | ./easyrsa build-client-full "$CLIENT_NAME" nopass

echo "Client certificate created for: $CLIENT_NAME"
echo "Generate client config with: openvpn-gen-client $CLIENT_NAME"
EOF

    # Create client config generation script
    cat > "/usr/local/bin/openvpn-gen-client" << 'EOF'
#!/bin/bash
# OpenVPN Client Config Generation Script

if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <client_name>"
    exit 1
fi

CLIENT_NAME="$1"
EASY_RSA_DIR="/etc/openvpn/easy-rsa"
CLIENT_DIR="/etc/openvpn/client"
KEYS_DIR="/etc/openvpn/server/keys"

# Check if client certificate exists
if [[ ! -f "$EASY_RSA_DIR/pki/issued/$CLIENT_NAME.crt" ]]; then
    echo "Error: Client certificate for $CLIENT_NAME does not exist"
    echo "Create it first with: openvpn-add-client $CLIENT_NAME"
    exit 1
fi

# Get server IP and port from config
SERVER_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "YOUR_SERVER_IP")
SERVER_PORT=$(grep "^port" /etc/openvpn/server/server.conf | awk '{print $2}')

# Create client config
cat > "$CLIENT_DIR/$CLIENT_NAME.ovpn" << CLIENTEOF
client
dev tun
proto udp
remote $SERVER_IP $SERVER_PORT
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-128-GCM
auth SHA256
tls-version-min 1.2
remote-cert-tls server
key-direction 1
tun-mtu 1400
mssfix 1360
verb 3
mute 20

<ca>
$(cat "$KEYS_DIR/ca.crt")
</ca>

<cert>
$(cat "$EASY_RSA_DIR/pki/issued/$CLIENT_NAME.crt")
</cert>

<key>
$(cat "$EASY_RSA_DIR/pki/private/$CLIENT_NAME.key")
</key>

<tls-crypt>
$(cat "$KEYS_DIR/ta.key")
</tls-crypt>
CLIENTEOF

echo "Client configuration created: $CLIENT_DIR/$CLIENT_NAME.ovpn"
EOF

    # Make scripts executable
    chmod +x /usr/local/bin/openvpn-add-client
    chmod +x /usr/local/bin/openvpn-gen-client
    
    log "SUCCESS" "Management scripts created in /usr/local/bin/"
}

# Function to display final information
display_final_info() {
    log "SUCCESS" "OpenVPN installation completed successfully!"
    
    echo ""
    echo "=== INSTALLATION SUMMARY ==="
    echo "Server IP: $SERVER_IP"
    echo "Server Port: $SERVER_PORT (UDP)"
    echo "VPN Subnet: $SERVER_SUBNET/24"
    echo "Configuration: $OPENVPN_SERVER_DIR/server.conf"
    echo "PKI Directory: $EASY_RSA_DIR"
    echo "Logs: $LOG_DIR/"
    echo ""
    echo "=== MANAGEMENT COMMANDS ==="
    echo "Check service status: systemctl status openvpn-server@server"
    echo "View logs: journalctl -u openvpn-server@server -f"
    echo "Add client: openvpn-add-client <client_name>"
    echo "Generate client config: openvpn-gen-client <client_name>"
    echo ""
    echo "=== SECURITY NOTES ==="
    echo "• Server runs as 'nobody' user for security"
    echo "• TLS-Crypt provides additional security layer"
    echo "• Firewall rules configured for OpenVPN traffic"
    echo "• IP forwarding enabled for VPN routing"
    echo ""
    echo "=== NEXT STEPS ==="
    echo "1. Create your first client: openvpn-add-client myclient"
    echo "2. Generate client config: openvpn-gen-client myclient"
    echo "3. Download: /etc/openvpn/client/myclient.ovpn"
    echo "4. Test connection from client device"
    echo ""
    
    # Show current status
    echo "=== CURRENT STATUS ==="
    systemctl status openvpn-server@server --no-pager
    echo ""
    
    if ip addr show tun0 >/dev/null 2>&1; then
        echo "TUN Interface Status:"
        ip addr show tun0
    fi
    
    echo ""
    echo "Installation replicates the exact configuration from analyzed system:"
    echo "• Same port (65120), protocol (UDP), and encryption (AES-128-GCM)"
    echo "• Identical directory structure and file paths"
    echo "• Matching iptables rules and network configuration"
    echo "• Same performance optimizations and security settings"
}

# Main installation function
main() {
    log "INFO" "Starting OpenVPN installation (replicating analyzed system)..."
    log "INFO" "Target system: Amazon Linux 2023"
    log "INFO" "Source analysis: syrix-openvpn-server configuration"
    
    # Run installation steps
    check_system_requirements
    detect_network_interface
    get_server_ip
    install_packages
    setup_directories
    setup_easy_rsa
    create_server_config
    setup_openvpn_user
    configure_network
    configure_iptables
    create_systemd_service
    start_openvpn_service
    create_management_scripts
    
    # Create a sample client certificate
    log "INFO" "Creating sample client certificate..."
    create_client_cert "sample-client"
    
    display_final_info
}

# Trap to handle errors
trap 'log "ERROR" "Installation failed at line $LINENO"' ERR

# Run main function
main "$@"