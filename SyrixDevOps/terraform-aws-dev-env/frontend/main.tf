resource "aws_instance" "reverse_proxy" {
  ami                         = "ami-0c94855ba95c71c99"  # Replace with your region's Ubuntu AMI
  instance_type               = "t2.micro"
  subnet_id                   = var.public_subnet_id
  associate_public_ip_address = true

  tags = {
    Name = "nginx"
  }

  user_data = <<-EOF
              #!/bin/bash
              apt-get update
              apt-get install -y nginx
              cat <<EOT > /etc/nginx/sites-enabled/default
              server {
                  listen 443 ssl;
                  server_name nginx.dev.internal;

                  location / {
                      proxy_pass https://backend.dev.internal;
                      proxy_set_header Host $host;
                      proxy_set_header X-Real-IP $remote_addr;
                      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                      proxy_set_header X-Forwarded-Proto $scheme;
                  }
              }
              EOT
              systemctl restart nginx
              EOF
}

output "nginx_private_ip" {
  value = aws_instance.reverse_proxy.private_ip
}

variable "public_subnet_id" {}
