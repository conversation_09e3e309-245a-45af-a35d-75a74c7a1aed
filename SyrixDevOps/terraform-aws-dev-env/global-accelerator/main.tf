resource "aws_globalaccelerator_accelerator" "nginx_accelerator" {
  name               = "nginx-web-accelerator"
  ip_address_type    = "IPV4"
  enabled            = true
}

resource "aws_globalaccelerator_listener" "nginx_listener" {
  accelerator_arn = aws_globalaccelerator_accelerator.nginx_accelerator.id
  protocol        = "TCP"

  port_ranges {
    from_port = 443
    to_port   = 443
  }
}

resource "aws_globalaccelerator_endpoint_group" "nginx_endpoint_group" {
  listener_arn = aws_globalaccelerator_listener.nginx_listener.id
  region       = var.aws_region

  endpoint_configuration {
    endpoint_id = aws_instance.reverse_proxy.id
    weight      = 100
  }

  traffic_dial_percentage = 100
}
