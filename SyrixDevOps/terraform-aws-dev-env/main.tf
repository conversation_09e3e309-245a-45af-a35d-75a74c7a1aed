module "vpc" {
  source = "./vpc"
}

module "nat" {
  source             = "./nat-gateway"
  vpc_id             = module.vpc.vpc_id
  public_subnet_id   = module.vpc.public_subnet_id
  private_subnet_ids = module.vpc.private_subnet_ids
}

module "frontend" {
  source           = "./frontend"
  public_subnet_id = module.vpc.public_subnet_id
}

module "backend" {
  source             = "./backend"
  private_subnet_id  = module.vpc.private_subnet_ids[0]
  security_group_id  = module.sg.backend_sg_id
}

module "sg" {
  source = "./security-groups"
  vpc_id = module.vpc.vpc_id
}

module "route53" {
  source             = "./route53"
  vpc_id             = module.vpc.vpc_id
  nginx_private_ip   = module.frontend.nginx_private_ip
  backend_private_ip = module.backend.backend_private_ip
}
