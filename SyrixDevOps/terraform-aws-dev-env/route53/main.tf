resource "aws_route53_zone" "internal" {
  name = "dev.internal"
  vpc {
    vpc_id = var.vpc_id
  }
}

resource "aws_route53_record" "nginx" {
  zone_id = aws_route53_zone.internal.zone_id
  name    = "nginx.dev.internal"
  type    = "A"
  ttl     = 60
  records = [var.nginx_private_ip]
}

resource "aws_route53_record" "backend" {
  zone_id = aws_route53_zone.internal.zone_id
  name    = "backend.dev.internal"
  type    = "A"
  ttl     = 60
  records = [var.backend_private_ip]
}

variable "vpc_id" {}
variable "nginx_private_ip" {}
variable "backend_private_ip" {}
