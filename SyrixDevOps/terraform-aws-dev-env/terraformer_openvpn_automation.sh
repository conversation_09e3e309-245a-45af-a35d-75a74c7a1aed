#!/bin/bash

# =============================================================================
# Automated Infrastructure and Configuration Capture using Terraformer
# This script captures both infrastructure and application configurations
# =============================================================================

set -e

# Configuration
AWS_REGION="${AWS_REGION:-us-east-1}"
PROJECT_NAME="${PROJECT_NAME:-captured-infrastructure}"
INSTANCE_TAG_FILTER="${INSTANCE_TAG_FILTER:-Name=Environment;Value=Production}"
OUTPUT_DIR="${OUTPUT_DIR:-./infrastructure-capture}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if terraformer is installed
    if ! command -v terraformer &> /dev/null; then
        error "Terraformer is not installed. Install it first:"
        echo "curl -LO \"https://github.com/GoogleCloudPlatform/terraformer/releases/download/\$(curl -s https://api.github.com/repos/GoogleCloudPlatform/terraformer/releases/latest | grep tag_name | cut -d '\"' -f 4)/terraformer-all-linux-amd64\""
        echo "chmod +x terraformer-all-linux-amd64 && sudo mv terraformer-all-linux-amd64 /usr/local/bin/terraformer"
        exit 1
    fi
    
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS CLI is not configured. Configure it first with: aws configure"
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        error "Terraform is not installed."
        exit 1
    fi
    
    success "All prerequisites are met"
}

# Step 1: Use Terraformer to capture infrastructure
capture_infrastructure() {
    log "Capturing infrastructure with Terraformer..."
    
    mkdir -p "$OUTPUT_DIR"
    cd "$OUTPUT_DIR"
    
    # Capture EC2 instances and related resources
    log "Importing EC2 instances and related resources..."
    terraformer import aws \
        --resources=ec2_instance,ebs,sg,vpc,subnet,igw,route_table,key_pair \
        --filter="$INSTANCE_TAG_FILTER" \
        --regions="$AWS_REGION" \
        --compact=true \
        --path-pattern="{output}/" || {
        warning "Some resources might not have been imported. Continuing..."
    }
    
    success "Infrastructure capture completed"
}

# Step 2: Identify instances with OpenVPN or other services
identify_service_instances() {
    log "Identifying instances running OpenVPN and other services..."
    
    # Get instance IDs from the generated terraform files
    local instance_ids=$(grep -r "aws_instance" generated/ | grep -o 'i-[a-f0-9]\{17\}' | sort -u)
    
    echo "# Service Discovery Results" > service_discovery.md
    echo "Generated on: $(date)" >> service_discovery.md
    echo "" >> service_discovery.md
    
    for instance_id in $instance_ids; do
        log "Analyzing instance: $instance_id"
        
        # Get instance details
        local instance_info=$(aws ec2 describe-instances \
            --instance-ids "$instance_id" \
            --region "$AWS_REGION" \
            --query 'Reservations[0].Instances[0]' 2>/dev/null)
        
        if [ "$?" -eq 0 ] && [ "$instance_info" != "null" ]; then
            local public_ip=$(echo "$instance_info" | jq -r '.PublicIpAddress // "N/A"')
            local private_ip=$(echo "$instance_info" | jq -r '.PrivateIpAddress // "N/A"')
            local instance_type=$(echo "$instance_info" | jq -r '.InstanceType')
            local key_name=$(echo "$instance_info" | jq -r '.KeyName // "N/A"')
            
            echo "## Instance: $instance_id" >> service_discovery.md
            echo "- **Public IP**: $public_ip" >> service_discovery.md
            echo "- **Private IP**: $private_ip" >> service_discovery.md
            echo "- **Instance Type**: $instance_type" >> service_discovery.md
            echo "- **Key Name**: $key_name" >> service_discovery.md
            echo "" >> service_discovery.md
            
            # Store for configuration capture
            echo "$instance_id,$public_ip,$private_ip,$key_name" >> instances_for_config_capture.csv
        fi
    done
    
    success "Service discovery completed. Check service_discovery.md for details"
}

# Step 3: Generate configuration capture scripts
generate_config_capture_terraform() {
    log "Generating Terraform configuration for capturing application configs..."
    
    cat > config_capture.tf << 'EOF'
# =============================================================================
# Configuration Capture for Existing Infrastructure
# This configuration captures application-level configs from existing instances
# =============================================================================

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

variable "instances_to_capture" {
  description = "Map of instances to capture configurations from"
  type = map(object({
    public_ip    = string
    private_ip   = string
    key_name     = string
    ssh_user     = string
  }))
  default = {}
}

variable "ssh_private_key_path" {
  description = "Path to SSH private key"
  type        = string
  default     = "~/.ssh/id_rsa"
}

# Data source to get key pair information
data "aws_key_pair" "existing_keys" {
  for_each = toset([for instance in var.instances_to_capture : instance.key_name])
  key_name = each.key
}

# Null resource for configuration capture
resource "null_resource" "config_capture" {
  for_each = var.instances_to_capture

  connection {
    type        = "ssh"
    user        = each.value.ssh_user
    private_key = file(var.ssh_private_key_path)
    host        = each.value.public_ip != "N/A" ? each.value.public_ip : each.value.private_ip
    port        = 22
    timeout     = "5m"
  }

  # Create directory for captured configs
  provisioner "local-exec" {
    command = "mkdir -p captured_configs/${each.key}"
  }

  # Capture system information
  provisioner "remote-exec" {
    inline = [
      "# Create temporary directory for config collection",
      "sudo mkdir -p /tmp/config_capture",
      "sudo chmod 755 /tmp/config_capture",
      "",
      "# System information",
      "echo '=== SYSTEM INFO ===' > /tmp/config_capture/system_info.txt",
      "uname -a >> /tmp/config_capture/system_info.txt",
      "cat /etc/os-release >> /tmp/config_capture/system_info.txt",
      "df -h >> /tmp/config_capture/system_info.txt",
      "free -h >> /tmp/config_capture/system_info.txt",
      "",
      "# Network configuration",
      "echo '=== NETWORK CONFIG ===' > /tmp/config_capture/network_config.txt",
      "ip addr show >> /tmp/config_capture/network_config.txt",
      "ip route show >> /tmp/config_capture/network_config.txt",
      "cat /etc/resolv.conf >> /tmp/config_capture/network_config.txt",
      "",
      "# Process list",
      "echo '=== PROCESSES ===' > /tmp/config_capture/processes.txt",
      "ps aux >> /tmp/config_capture/processes.txt",
      "",
      "# Service status",
      "echo '=== SERVICES ===' > /tmp/config_capture/services.txt",
      "systemctl list-units --type=service --state=active >> /tmp/config_capture/services.txt",
      "",
      "# OpenVPN specific checks",
      "echo '=== OPENVPN CONFIG ===' > /tmp/config_capture/openvpn_info.txt",
      "if systemctl is-active --quiet openvpn; then",
      "  echo 'OpenVPN service is running' >> /tmp/config_capture/openvpn_info.txt",
      "  systemctl status openvpn >> /tmp/config_capture/openvpn_info.txt",
      "else",
      "  echo 'OpenVPN service not found or not running' >> /tmp/config_capture/openvpn_info.txt",
      "fi",
      "",
      "# Check for OpenVPN configurations",
      "find /etc/openvpn* -name '*.conf' -o -name '*.ovpn' 2>/dev/null > /tmp/config_capture/openvpn_files.txt || true",
      "find /etc -name '*openvpn*' -type d 2>/dev/null >> /tmp/config_capture/openvpn_files.txt || true",
      "",
      "# Look for common VPN-related processes",
      "ps aux | grep -i 'openvpn\\|vpn\\|wireguard' | grep -v grep >> /tmp/config_capture/vpn_processes.txt || true",
      "",
      "# Check listening ports",
      "echo '=== LISTENING PORTS ===' > /tmp/config_capture/listening_ports.txt",
      "ss -tulpn >> /tmp/config_capture/listening_ports.txt",
      "",
      "# Iptables rules",
      "echo '=== FIREWALL RULES ===' > /tmp/config_capture/firewall_rules.txt",
      "sudo iptables -L -n >> /tmp/config_capture/firewall_rules.txt || true",
      "sudo iptables -t nat -L -n >> /tmp/config_capture/firewall_rules.txt || true",
      "",
      "# Check for custom startup scripts",
      "echo '=