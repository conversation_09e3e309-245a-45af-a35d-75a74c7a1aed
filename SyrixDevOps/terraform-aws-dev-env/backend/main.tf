resource "aws_instance" "backend_web" {
  ami                    = "ami-0c94855ba95c71c99"  # Replace as needed
  instance_type          = "t2.micro"
  subnet_id              = var.private_subnet_id
  vpc_security_group_ids = [var.security_group_id]

  tags = {
    Name = "backend"
  }

  user_data = <<-EOF
              #!/bin/bash
              apt-get update
              apt-get install -y apache2 openssl
              a2enmod ssl
              a2ensite default-ssl
              systemctl restart apache2
              EOF
}

output "backend_private_ip" {
  value = aws_instance.backend_web.private_ip
}

variable "private_subnet_id" {}
variable "security_group_id" {}
